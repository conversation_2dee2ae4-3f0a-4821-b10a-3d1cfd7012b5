package androidx.biometric;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.biometric.BiometricPrompt;
import androidx.core.hardware.fingerprint.FingerprintManagerCompat;
import androidx.core.os.CancellationSignal;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import java.util.concurrent.Executor;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\FingerprintHelperFragment.smali */
public class FingerprintHelperFragment extends Fragment {
    private static final String TAG = "FingerprintHelperFrag";
    static final int USER_CANCELED_FROM_NEGATIVE_BUTTON = 2;
    static final int USER_CANCELED_FROM_NONE = 0;
    static final int USER_CANCELED_FROM_USER = 1;
    final FingerprintManagerCompat.AuthenticationCallback mAuthenticationCallback = new FingerprintManagerCompat.AuthenticationCallback() { // from class: androidx.biometric.FingerprintHelperFragment.1
        /* JADX INFO: Access modifiers changed from: private */
        public void dismissAndForwardResult(final int errMsgId, final CharSequence errString) {
            FingerprintHelperFragment.this.mMessageRouter.sendMessage(3);
            if (!Utils.isConfirmingDeviceCredential()) {
                FingerprintHelperFragment.this.mExecutor.execute(new Runnable() { // from class: androidx.biometric.FingerprintHelperFragment.1.1
                    @Override // java.lang.Runnable
                    public void run() {
                        FingerprintHelperFragment.this.mClientAuthenticationCallback.onAuthenticationError(errMsgId, errString);
                    }
                });
            }
        }

        @Override // androidx.core.hardware.fingerprint.FingerprintManagerCompat.AuthenticationCallback
        public void onAuthenticationError(int errMsgId, CharSequence errString) {
            final CharSequence dialogErrString;
            if (errMsgId == 5) {
                if (FingerprintHelperFragment.this.mCanceledFrom == 0) {
                    dismissAndForwardResult(errMsgId, errString);
                }
                FingerprintHelperFragment.this.cleanup();
            } else {
                if (errMsgId == 7 || errMsgId == 9) {
                    dismissAndForwardResult(errMsgId, errString);
                    FingerprintHelperFragment.this.cleanup();
                    return;
                }
                if (errString != null) {
                    dialogErrString = errString;
                } else {
                    Log.e(FingerprintHelperFragment.TAG, "Got null string for error message: " + errMsgId);
                    dialogErrString = FingerprintHelperFragment.this.mContext.getResources().getString(R.string.default_error_msg);
                }
                final int dialogErrMsgId = Utils.isUnknownError(errMsgId) ? 8 : errMsgId;
                FingerprintHelperFragment.this.mMessageRouter.sendMessage(2, dialogErrMsgId, 0, dialogErrString);
                FingerprintHelperFragment.this.mHandler.postDelayed(new Runnable() { // from class: androidx.biometric.FingerprintHelperFragment.1.2
                    @Override // java.lang.Runnable
                    public void run() {
                        dismissAndForwardResult(dialogErrMsgId, dialogErrString);
                        FingerprintHelperFragment.this.cleanup();
                    }
                }, FingerprintDialogFragment.getHideDialogDelay(FingerprintHelperFragment.this.getContext()));
            }
        }

        @Override // androidx.core.hardware.fingerprint.FingerprintManagerCompat.AuthenticationCallback
        public void onAuthenticationHelp(int helpMsgId, CharSequence helpString) {
            FingerprintHelperFragment.this.mMessageRouter.sendMessage(1, helpString);
        }

        @Override // androidx.core.hardware.fingerprint.FingerprintManagerCompat.AuthenticationCallback
        public void onAuthenticationSucceeded(FingerprintManagerCompat.AuthenticationResult result) {
            FingerprintHelperFragment.this.mMessageRouter.sendMessage(5);
            final BiometricPrompt.AuthenticationResult promptResult = result != null ? new BiometricPrompt.AuthenticationResult(FingerprintHelperFragment.unwrapCryptoObject(result.getCryptoObject())) : new BiometricPrompt.AuthenticationResult(null);
            FingerprintHelperFragment.this.mExecutor.execute(new Runnable() { // from class: androidx.biometric.FingerprintHelperFragment.1.3
                @Override // java.lang.Runnable
                public void run() {
                    FingerprintHelperFragment.this.mClientAuthenticationCallback.onAuthenticationSucceeded(promptResult);
                }
            });
            FingerprintHelperFragment.this.cleanup();
        }

        @Override // androidx.core.hardware.fingerprint.FingerprintManagerCompat.AuthenticationCallback
        public void onAuthenticationFailed() {
            FingerprintHelperFragment.this.mMessageRouter.sendMessage(1, FingerprintHelperFragment.this.mContext.getResources().getString(R.string.fingerprint_not_recognized));
            FingerprintHelperFragment.this.mExecutor.execute(new Runnable() { // from class: androidx.biometric.FingerprintHelperFragment.1.4
                @Override // java.lang.Runnable
                public void run() {
                    FingerprintHelperFragment.this.mClientAuthenticationCallback.onAuthenticationFailed();
                }
            });
        }
    };
    private int mCanceledFrom;
    private CancellationSignal mCancellationSignal;
    BiometricPrompt.AuthenticationCallback mClientAuthenticationCallback;
    private Context mContext;
    private BiometricPrompt.CryptoObject mCryptoObject;
    Executor mExecutor;
    private Handler mHandler;
    private MessageRouter mMessageRouter;
    private boolean mShowing;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\FingerprintHelperFragment$MessageRouter.smali */
    static class MessageRouter {
        private final Handler mHandler;

        MessageRouter(Handler handler) {
            this.mHandler = handler;
        }

        void sendMessage(int what) {
            this.mHandler.obtainMessage(what).sendToTarget();
        }

        void sendMessage(int what, Object obj) {
            this.mHandler.obtainMessage(what, obj).sendToTarget();
        }

        void sendMessage(int what, int arg1, int arg2, Object obj) {
            this.mHandler.obtainMessage(what, arg1, arg2, obj).sendToTarget();
        }
    }

    static FingerprintHelperFragment newInstance() {
        return new FingerprintHelperFragment();
    }

    void setCryptoObject(BiometricPrompt.CryptoObject crypto) {
        this.mCryptoObject = crypto;
    }

    @Override // androidx.fragment.app.Fragment
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRetainInstance(true);
        this.mContext = getContext();
    }

    @Override // androidx.fragment.app.Fragment
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (!this.mShowing) {
            this.mCancellationSignal = new CancellationSignal();
            this.mCanceledFrom = 0;
            FingerprintManagerCompat fingerprintManagerCompat = FingerprintManagerCompat.from(this.mContext);
            if (handlePreAuthenticationErrors(fingerprintManagerCompat)) {
                this.mMessageRouter.sendMessage(3);
                cleanup();
            } else {
                fingerprintManagerCompat.authenticate(wrapCryptoObject(this.mCryptoObject), 0, this.mCancellationSignal, this.mAuthenticationCallback, null);
                this.mShowing = true;
            }
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    void setCallback(Executor executor, BiometricPrompt.AuthenticationCallback callback) {
        this.mExecutor = executor;
        this.mClientAuthenticationCallback = callback;
    }

    void setHandler(Handler handler) {
        this.mHandler = handler;
        this.mMessageRouter = new MessageRouter(this.mHandler);
    }

    void setMessageRouter(MessageRouter messageRouter) {
        this.mMessageRouter = messageRouter;
    }

    void cancel(int canceledFrom) {
        this.mCanceledFrom = canceledFrom;
        if (canceledFrom == 1) {
            sendErrorToClient(10);
        }
        CancellationSignal cancellationSignal = this.mCancellationSignal;
        if (cancellationSignal != null) {
            cancellationSignal.cancel();
        }
        cleanup();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void cleanup() {
        this.mShowing = false;
        FragmentActivity activity = getActivity();
        if (getFragmentManager() != null) {
            getFragmentManager().beginTransaction().detach(this).commitAllowingStateLoss();
        }
        if (!Utils.isConfirmingDeviceCredential()) {
            Utils.maybeFinishHandler(activity);
        }
    }

    private boolean handlePreAuthenticationErrors(FingerprintManagerCompat fingerprintManager) {
        if (!fingerprintManager.isHardwareDetected()) {
            sendErrorToClient(12);
            return true;
        }
        if (!fingerprintManager.hasEnrolledFingerprints()) {
            sendErrorToClient(11);
            return true;
        }
        return false;
    }

    private void sendErrorToClient(int error) {
        if (!Utils.isConfirmingDeviceCredential()) {
            this.mClientAuthenticationCallback.onAuthenticationError(error, getErrorString(this.mContext, error));
        }
    }

    private String getErrorString(Context context, int errorCode) {
        switch (errorCode) {
            case 1:
                return context.getString(R.string.fingerprint_error_hw_not_available);
            case 10:
                return context.getString(R.string.fingerprint_error_user_canceled);
            case 11:
                return context.getString(R.string.fingerprint_error_no_fingerprints);
            case 12:
                return context.getString(R.string.fingerprint_error_hw_not_present);
            default:
                Log.e(TAG, "Unknown error code: " + errorCode);
                return context.getString(R.string.default_error_msg);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BiometricPrompt.CryptoObject unwrapCryptoObject(FingerprintManagerCompat.CryptoObject cryptoObject) {
        if (cryptoObject == null) {
            return null;
        }
        if (cryptoObject.getCipher() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getCipher());
        }
        if (cryptoObject.getSignature() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getSignature());
        }
        if (cryptoObject.getMac() == null) {
            return null;
        }
        return new BiometricPrompt.CryptoObject(cryptoObject.getMac());
    }

    private static FingerprintManagerCompat.CryptoObject wrapCryptoObject(BiometricPrompt.CryptoObject cryptoObject) {
        if (cryptoObject == null) {
            return null;
        }
        if (cryptoObject.getCipher() != null) {
            return new FingerprintManagerCompat.CryptoObject(cryptoObject.getCipher());
        }
        if (cryptoObject.getSignature() != null) {
            return new FingerprintManagerCompat.CryptoObject(cryptoObject.getSignature());
        }
        if (cryptoObject.getMac() == null) {
            return null;
        }
        return new FingerprintManagerCompat.CryptoObject(cryptoObject.getMac());
    }
}
