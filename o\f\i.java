package o.f;

import java.util.Date;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\i.smali */
public abstract class i extends e {
    private static int d = 0;
    private static int e = 1;
    private final d c;

    i(e.d dVar, Date date, d dVar2) {
        super(dVar, date);
        this.c = dVar2;
    }

    final d l() {
        int i = d;
        int i2 = i + 91;
        e = i2 % 128;
        int i3 = i2 % 2;
        d dVar = this.c;
        int i4 = ((i | 89) << 1) - (i ^ 89);
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return dVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }
}
