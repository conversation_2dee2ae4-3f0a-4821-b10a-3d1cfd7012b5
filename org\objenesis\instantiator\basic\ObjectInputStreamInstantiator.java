package org.objenesis.instantiator.basic;

import com.esotericsoftware.asm.Opcodes;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.NotSerializableException;
import java.io.ObjectInputStream;
import java.io.ObjectStreamClass;
import java.io.Serializable;
import org.objenesis.ObjenesisException;
import org.objenesis.instantiator.ObjectInstantiator;
import org.objenesis.instantiator.annotations.Instantiator;
import org.objenesis.instantiator.annotations.Typology;

@Instantiator(Typology.SERIALIZATION)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\objenesis\instantiator\basic\ObjectInputStreamInstantiator.smali */
public class ObjectInputStreamInstantiator<T> implements ObjectInstantiator<T> {
    private final ObjectInputStream inputStream;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\objenesis\instantiator\basic\ObjectInputStreamInstantiator$MockStream.smali */
    private static class MockStream extends InputStream {
        private static byte[] HEADER;
        private static final int[] NEXT = {1, 2, 2};
        private static byte[] REPEATING_DATA;
        private final byte[][] buffers;
        private int pointer = 0;
        private int sequence = 0;
        private byte[] data = HEADER;

        static {
            initialize();
        }

        private static void initialize() {
            try {
                ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
                DataOutputStream dout = new DataOutputStream(byteOut);
                dout.writeShort(-21267);
                dout.writeShort(5);
                HEADER = byteOut.toByteArray();
                ByteArrayOutputStream byteOut2 = new ByteArrayOutputStream();
                DataOutputStream dout2 = new DataOutputStream(byteOut2);
                dout2.writeByte(Opcodes.DREM);
                dout2.writeByte(Opcodes.LREM);
                dout2.writeInt(8257536);
                REPEATING_DATA = byteOut2.toByteArray();
            } catch (IOException e) {
                throw new Error("IOException: " + e.getMessage());
            }
        }

        public MockStream(Class<?> clazz) {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            DataOutputStream dout = new DataOutputStream(byteOut);
            try {
                dout.writeByte(Opcodes.DREM);
                dout.writeByte(114);
                dout.writeUTF(clazz.getName());
                dout.writeLong(ObjectStreamClass.lookup(clazz).getSerialVersionUID());
                dout.writeByte(2);
                dout.writeShort(0);
                dout.writeByte(Opcodes.ISHL);
                dout.writeByte(Opcodes.IREM);
                byte[] firstData = byteOut.toByteArray();
                this.buffers = new byte[][]{HEADER, firstData, REPEATING_DATA};
            } catch (IOException e) {
                throw new Error("IOException: " + e.getMessage());
            }
        }

        private void advanceBuffer() {
            this.pointer = 0;
            int i = NEXT[this.sequence];
            this.sequence = i;
            this.data = this.buffers[i];
        }

        @Override // java.io.InputStream
        public int read() {
            byte[] bArr = this.data;
            int i = this.pointer;
            int i2 = i + 1;
            this.pointer = i2;
            int result = bArr[i];
            if (i2 >= bArr.length) {
                advanceBuffer();
            }
            return result;
        }

        @Override // java.io.InputStream
        public int available() {
            return Integer.MAX_VALUE;
        }

        @Override // java.io.InputStream
        public int read(byte[] b, int off, int len) {
            int left = len;
            int remaining = this.data.length - this.pointer;
            while (remaining <= left) {
                System.arraycopy(this.data, this.pointer, b, off, remaining);
                off += remaining;
                left -= remaining;
                advanceBuffer();
                remaining = this.data.length - this.pointer;
            }
            if (left > 0) {
                System.arraycopy(this.data, this.pointer, b, off, left);
                this.pointer += left;
            }
            return len;
        }
    }

    public ObjectInputStreamInstantiator(Class<T> clazz) {
        if (Serializable.class.isAssignableFrom(clazz)) {
            try {
                this.inputStream = new ObjectInputStream(new MockStream(clazz));
                return;
            } catch (IOException e) {
                throw new Error("IOException: " + e.getMessage());
            }
        }
        throw new ObjenesisException(new NotSerializableException(clazz + " not serializable"));
    }

    @Override // org.objenesis.instantiator.ObjectInstantiator
    public T newInstance() {
        try {
            return (T) this.inputStream.readObject();
        } catch (ClassNotFoundException e) {
            throw new Error("ClassNotFoundException: " + e.getMessage());
        } catch (Exception e2) {
            throw new ObjenesisException(e2);
        }
    }
}
