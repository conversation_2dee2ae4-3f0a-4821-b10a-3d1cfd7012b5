package o.y;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.bv.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\b.smali */
public abstract class b<CB> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static int[] g;
    private static int i;
    private final g a = new g();
    private final CB b;
    private a<?> c;
    private Context d;
    private final o.ei.c e;
    private o.bb.d h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        g = new int[]{-48786032, -820928320, -914435677, 898427741, 1306396051, 1464201126, -29196255, -1362138784, -1052034262, 877870400, -1772181558, 144343073, -994406287, -1593863485, 414765983, 2111174658, 353150410, 804603508};
    }

    static void init$0() {
        $$a = new byte[]{17, -116, 103, 33};
        $$b = Opcodes.IRETURN;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(short r5, short r6, int r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 2
            int r5 = r5 + 4
            int r6 = r6 + 115
            byte[] r0 = o.y.b.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = -1
            int r7 = r7 + r2
            if (r0 != 0) goto L19
            r6 = r5
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L35
        L19:
            r4 = r6
            r6 = r5
            r5 = r4
        L1c:
            int r2 = r2 + 1
            byte r3 = (byte) r5
            r1[r2] = r3
            if (r2 != r7) goto L2c
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2c:
            r3 = r0[r6]
            r4 = r8
            r8 = r7
            r7 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r4
        L35:
            int r7 = -r7
            int r5 = r5 + r7
            int r6 = r6 + 1
            r7 = r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.b.u(short, short, int, java.lang.Object[]):void");
    }

    public abstract String a();

    public abstract a<?> b();

    public b(Context context, CB cb, o.ei.c cVar, o.bb.e eVar) {
        this.d = context;
        this.b = cb;
        this.e = cVar;
        this.h = new o.bb.d(eVar);
    }

    public final void c() {
        o.ee.g.c();
        String a = a();
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        s(new int[]{68057861, 947527371, 768108651, -492788794, -1670198852, -861205977, -1898909215, 839447963, -234326636, -324870122, -1890797968, 1784588860, 1622307177, -554413412}, MotionEvent.axisFromString("") + 29, objArr);
        o.ee.g.d(a, sb.append(((String) objArr[0]).intern()).append(this.e).toString());
        a<?> b = b();
        switch (b != null ? 'M' : 'A') {
            case 'M':
                int i2 = f + 11;
                i = i2 % 128;
                int i3 = i2 % 2;
                this.c = b;
                b.execute(new Void[0]);
                break;
        }
        int i4 = f + Opcodes.DNEG;
        i = i4 % 128;
        int i5 = i4 % 2;
    }

    public final o.bb.d d() {
        int i2 = f + Opcodes.DMUL;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return this.h;
            default:
                throw null;
        }
    }

    public final void c(o.bb.d dVar) {
        int i2 = f + 49;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        this.h = dVar;
        int i5 = i3 + Opcodes.DNEG;
        f = i5 % 128;
        int i6 = i5 % 2;
    }

    public final Context e() {
        int i2 = f;
        int i3 = i2 + 33;
        i = i3 % 128;
        int i4 = i3 % 2;
        Context context = this.d;
        int i5 = i2 + Opcodes.LMUL;
        i = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                int i6 = 51 / 0;
                return context;
            default:
                return context;
        }
    }

    public final void a(Context context) {
        int i2 = i + 73;
        f = i2 % 128;
        char c = i2 % 2 == 0 ? '\f' : '.';
        this.d = context;
        switch (c) {
            case '\f':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final CB j() {
        int i2 = i;
        int i3 = i2 + 43;
        f = i3 % 128;
        int i4 = i3 % 2;
        CB cb = this.b;
        int i5 = i2 + 69;
        f = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 26 : (char) 5) {
            case 5:
                return cb;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final a<?> i() {
        int i2 = f + 37;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return this.c;
            default:
                int i3 = 2 / 0;
                return this.c;
        }
    }

    public final o.ei.c g() {
        int i2 = i;
        int i3 = i2 + 3;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 24 : (char) 22) {
            case 22:
                o.ei.c cVar = this.e;
                int i4 = i2 + 89;
                f = i4 % 128;
                int i5 = i4 % 2;
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final g h() {
        int i2 = f + 1;
        int i3 = i2 % 128;
        i = i3;
        char c = i2 % 2 != 0 ? 'K' : (char) 1;
        g gVar = this.a;
        switch (c) {
            case 1:
                break;
            default:
                int i4 = 70 / 0;
                break;
        }
        int i5 = i3 + 81;
        f = i5 % 128;
        int i6 = i5 % 2;
        return gVar;
    }

    public void f() {
        String a;
        Object obj;
        int i2 = f + 25;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 28 : Typography.quote) {
            case '\"':
                o.ee.g.c();
                a = a();
                Object[] objArr = new Object[1];
                s(new int[]{-584227918, 344606563, -46681078, -1070962122, 810943256, -1013843956}, 10 - ((Process.getThreadPriority(0) + 20) >> 6), objArr);
                obj = objArr[0];
                break;
            default:
                o.ee.g.c();
                a = a();
                Object[] objArr2 = new Object[1];
                s(new int[]{-584227918, 344606563, -46681078, -1070962122, 810943256, -1013843956}, 81 << ((Process.getThreadPriority(0) << 31) + Opcodes.IAND), objArr2);
                obj = objArr2[0];
                break;
        }
        o.ee.g.d(a, ((String) obj).intern());
        int i3 = f + 31;
        i = i3 % 128;
        switch (i3 % 2 != 0 ? 'W' : (char) 20) {
            case Opcodes.POP /* 87 */:
                throw null;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    private static void s(int[] iArr, int i2, Object[] objArr) {
        int[] iArr2;
        o.a.g gVar = new o.a.g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = g;
        int i3 = -1667374059;
        int i4 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i5 = 0;
            while (true) {
                switch (i5 < length) {
                    case true:
                        try {
                            Object[] objArr2 = {Integer.valueOf(iArr3[i5])};
                            Object obj = o.e.a.s.get(Integer.valueOf(i3));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getScrollBarSize() >> 8) + 10, (char) (8856 - TextUtils.getOffsetAfter("", 0)), (ViewConfiguration.getTapTimeout() >> 16) + 324);
                                byte b = (byte) 0;
                                byte b2 = (byte) (b + 1);
                                Object[] objArr3 = new Object[1];
                                u(b, b2, (byte) (b2 - 1), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj);
                            }
                            iArr4[i5] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                            i5++;
                            i3 = -1667374059;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        int i6 = $11 + 83;
                        $10 = i6 % 128;
                        int i7 = i6 % 2;
                        iArr3 = iArr4;
                        break;
                }
            }
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = g;
        if (iArr6 != null) {
            int length3 = iArr6.length;
            int[] iArr7 = new int[length3];
            int i8 = 0;
            while (true) {
                switch (i8 < length3 ? i4 : 19) {
                    case 0:
                        try {
                            Object[] objArr4 = new Object[1];
                            objArr4[i4] = Integer.valueOf(iArr6[i8]);
                            Object obj2 = o.e.a.s.get(-1667374059);
                            if (obj2 != null) {
                                iArr2 = iArr6;
                            } else {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getPressedStateDuration() >> 16) + 10, (char) (8856 - ((Process.getThreadPriority(i4) + 20) >> 6)), 325 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)));
                                byte b3 = (byte) i4;
                                byte b4 = (byte) (b3 + 1);
                                iArr2 = iArr6;
                                Object[] objArr5 = new Object[1];
                                u(b3, b4, (byte) (b4 - 1), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj2);
                            }
                            iArr7[i8] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                            i8++;
                            iArr6 = iArr2;
                            i4 = 0;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        iArr6 = iArr7;
                        break;
                }
            }
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr5);
            int i9 = 0;
            for (int i10 = 16; i9 < i10; i10 = 16) {
                int i11 = $10 + 83;
                $11 = i11 % 128;
                int i12 = i11 % 2;
                gVar.e ^= iArr5[i9];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c(12 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (char) Drawable.resolveOpacity(0, 0), 572 - View.MeasureSpec.makeMeasureSpec(0, 0))).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                    i9++;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i13 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i13;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i14 = gVar.e;
            int i15 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            o.a.g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 12, (char) (55183 - (KeyEvent.getMaxKeyCode() >> 16)), (Process.myPid() >> 22) + 515);
                    byte b5 = (byte) 0;
                    byte b6 = b5;
                    Object[] objArr8 = new Object[1];
                    u(b5, b6, b6, objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i2);
    }
}
