package com.google.android.gms.dynamic;

import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.os.RemoteException;

/* compiled from: com.google.android.gms:play-services-basement@@18.3.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\dynamic\zza.smali */
public final class zza extends com.google.android.gms.internal.common.zza implements IFragmentWrapper {
    zza(IBinder iBinder) {
        super(iBinder, "com.google.android.gms.dynamic.IFragmentWrapper");
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzA() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final int zzb() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final int zzc() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final Bundle zzd() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final IFragmentWrapper zze() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final IFragmentWrapper zzf() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final IObjectWrapper zzg() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final IObjectWrapper zzh() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final IObjectWrapper zzi() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final String zzj() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzk(IObjectWrapper iObjectWrapper) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzl(boolean z) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzm(boolean z) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzn(boolean z) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzo(boolean z) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzp(Intent intent) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzq(Intent intent, int i) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final void zzr(IObjectWrapper iObjectWrapper) throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzs() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzt() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzu() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzv() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzw() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzx() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzy() throws RemoteException {
        throw null;
    }

    @Override // com.google.android.gms.dynamic.IFragmentWrapper
    public final boolean zzz() throws RemoteException {
        throw null;
    }
}
