package o.cy;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import kotlin.io.encoding.Base64;
import o.a.f;
import o.ee.g;
import o.ei.i;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cy\b.smali */
public final class b implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int b;
    private static int c;
    private static int d;
    private static byte[] e;
    private static short[] g;
    private static int h;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        i = 1;
        a();
        Process.getElapsedCpuTime();
        Process.getGidForName("");
        ViewConfiguration.getScrollDefaultDelay();
        SystemClock.uptimeMillis();
        ExpandableListView.getPackedPositionType(0L);
        int i2 = i + 43;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 21 : '1') {
            case '1':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void a() {
        a = new int[]{852785266, -1763542420, 1816993503, 704875534, 1099240724, 1433411618, -1530766108, -90712258, 2031212366, -466412657, -176948690, -940595146, -1171484584, -942695746, -1597816427, -2102785571, 1726826297, -222091892};
        e = new byte[]{117, 105, 107, -71, 122, 99, -102, 105, -115, -98, 114, -103, -111, 115, 98, 114, -66, PSSSigner.TRAILER_IMPLICIT, 110, -85, 77, 70, UtilitiesSDKConstants.SRP_LABEL_MAC, 78, -70, 70, -77, 126, -92, -88, -86, 85, -70, 77, 86, -81, 92, -72, -85, 71, -84, -92, 70, -73, 70, -85, UtilitiesSDKConstants.SRP_LABEL_MAC, 124, -89, -88, -66, 77, 22, -22, 75, 1, -29, -19, 122, 38, -38, 124, 63, 50, -63, -47, 49, -112, 123, -61, -126, 108, -59, -63, -64, -51, 62, -125, 76, 80, 95, -87, -89, 85, 114, -26, 20, 24, -18, -26, 4, 21, 30, -7, 18, -8, 99, 83, -81, 14, 64, 66, UtilitiesSDKConstants.SRP_LABEL_ENC, -31, 7, 66, -67, -76, -74, 72, 76, -32, 70, -72, 14, 64, 66, -112, 90, 90, -65, 72, 73, -69, 70, 112, -109, -121, 125, -101, 111, 107, -104, -109, 120, 78, 41, 33, -45, 43, -33, 40, -41, 78, 6, 11, -8, -12, 22, -24, 10, 96, -53, 52, 38, -40, 52, 102, -121, -54, 51, -64, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 55, -37, 48, 56, -38, -53, 97, -121, -62, Base64.padSymbol, 52, 54, -56, -52};
        c = 909053626;
        b = 906631914;
        d = -1821384675;
    }

    static void init$0() {
        $$a = new byte[]{69, 29, 47, 95};
        $$b = Opcodes.FMUL;
    }

    private static void k(int i2, byte b2, short s, Object[] objArr) {
        int i3 = 116 - b2;
        int i4 = i2 + 4;
        byte[] bArr = $$a;
        int i5 = (s * 3) + 1;
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            int i8 = i4 + i7;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i3 = i8;
            i4 = i4;
        }
        while (true) {
            int i9 = i4 + 1;
            int i10 = i6 + 1;
            bArr2[i10] = (byte) i3;
            if (i10 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i9];
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i10;
            i3 = b3 + i3;
            i4 = i9;
        }
    }

    @Override // o.cy.e
    public final o.dr.a e(String str, String str2) throws i {
        o.dr.a aVar = new o.dr.a();
        try {
            o.eg.b bVar = new o.eg.b(str2);
            aVar.l(str);
            Object[] objArr = new Object[1];
            f(new int[]{-1895254745, -365198809, -1661839369, 1075064265}, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 7, objArr);
            aVar.n(bVar.r(((String) objArr[0]).intern()));
            String c2 = o.dr.b.c(str, aVar.w());
            aVar.d(c2);
            aVar.q(c2);
            Object[] objArr2 = new Object[1];
            f(new int[]{541397230, -53784022, 194927933, 409675563, -953935115, -669139686}, View.resolveSize(0, 0) + 12, objArr2);
            aVar.a(o.ej.e.c(bVar.r(((String) objArr2[0]).intern())));
            Object[] objArr3 = new Object[1];
            j((byte) (TextUtils.getOffsetBefore("", 0) + 12), 1522471283 - TextUtils.indexOf("", "", 0), (short) (ViewConfiguration.getKeyRepeatDelay() >> 16), (-43) - (ViewConfiguration.getDoubleTapTimeout() >> 16), MotionEvent.axisFromString("") - 2429957, objArr3);
            aVar.d(a(bVar.r(((String) objArr3[0]).intern())));
            Object[] objArr4 = new Object[1];
            f(new int[]{-1526889767, -1483189138, -1152971341, -1134219372, 540896714, -1750806940, -401053579, 1011226559, -191178877, -451549051}, (ViewConfiguration.getLongPressTimeout() >> 16) + 19, objArr4);
            aVar.a(d(bVar.r(((String) objArr4[0]).intern())));
            Object[] objArr5 = new Object[1];
            f(new int[]{-1913865151, 233545655, 581573720, 1843236361, -708239854, 58043011, -328897514, -1496628255, -458077895, -648357681}, 20 - View.getDefaultSize(0, 0), objArr5);
            aVar.d(e(bVar.r(((String) objArr5[0]).intern())));
            Object[] objArr6 = new Object[1];
            f(new int[]{1489444734, -2019848684, 903034931, -749857425}, 6 - View.MeasureSpec.getSize(0), objArr6);
            switch (bVar.b(((String) objArr6[0]).intern())) {
                case false:
                    break;
                default:
                    int i2 = h + 95;
                    i = i2 % 128;
                    int i3 = i2 % 2;
                    Object[] objArr7 = new Object[1];
                    f(new int[]{1489444734, -2019848684, 903034931, -749857425}, Drawable.resolveOpacity(0, 0) + 6, objArr7);
                    aVar.d(BigDecimal.valueOf(bVar.f(((String) objArr7[0]).intern()).doubleValue()).abs());
                    break;
            }
            Object[] objArr8 = new Object[1];
            f(new int[]{299082673, -1013888881, -898119696, 351933126, 1140249675, 309872099}, 11 - TextUtils.indexOf((CharSequence) "", '0'), objArr8);
            aVar.a(bVar.q(((String) objArr8[0]).intern()));
            Object[] objArr9 = new Object[1];
            f(new int[]{299082673, -1013888881, -898119696, 351933126, -1672730924, 2142821280, -1890486925, -534421436, -1748348897, -767705207}, TextUtils.getOffsetBefore("", 0) + 18, objArr9);
            aVar.o(bVar.q(((String) objArr9[0]).intern()));
            Object[] objArr10 = new Object[1];
            j((byte) (Color.green(0) - 37), 1522471298 - Color.red(0), (short) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (-44) - TextUtils.indexOf((CharSequence) "", '0', 0), Color.rgb(0, 0, 0) + 14347251, objArr10);
            aVar.s(bVar.q(((String) objArr10[0]).intern()));
            Object[] objArr11 = new Object[1];
            f(new int[]{-1913865151, 233545655, 581573720, 1843236361, 1429557504, 1425150225, 41015826, -1082782771, 1910109053, -146234404, 1441240825, -1375392839}, 21 - (ViewConfiguration.getScrollBarSize() >> 8), objArr11);
            aVar.r(bVar.q(((String) objArr11[0]).intern()));
            int i4 = h + 47;
            i = i4 % 128;
            int i5 = i4 % 2;
            return aVar;
        } catch (o.eg.d e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr12 = new Object[1];
            f(new int[]{1216640180, -1403933979, -659907281, -487457472, 1954035568, 790597886, -1988842563, 14106378, 156276148, 687643128, 1079383416, 18069835, -700531532, -1192909639, -2114920764, -1970995641, -978171551, 1950349300, 411968661, -1825844046, -1773609569, 1294190504, -326332020, -1490669151, 750905652, 2093642754, 1511346802, 739016585}, View.resolveSize(0, 0) + 55, objArr12);
            throw new i(sb.append(((String) objArr12[0]).intern()).append(e2.getMessage()).toString());
        }
    }

    private static Date e(String str) throws i {
        int i2 = h + 61;
        i = i2 % 128;
        Object obj = null;
        try {
            switch (i2 % 2 == 0 ? '\'' : '2') {
                case '\'':
                    o.eb.d.d(str);
                    throw null;
                default:
                    Date d2 = o.eb.d.d(str);
                    int i3 = h + 7;
                    i = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            return d2;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        } catch (IllegalArgumentException e2) {
            g.c();
            Object[] objArr = new Object[1];
            j((byte) ('i' - AndroidCharacter.getMirror('0')), 1522471310 - (ViewConfiguration.getScrollBarSize() >> 8), (short) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (-16777259) - Color.rgb(0, 0, 0), (-2429997) - Gravity.getAbsoluteGravity(0, 0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            j((byte) ((ViewConfiguration.getTouchSlop() >> 8) + 96), 1522471334 + (ViewConfiguration.getScrollDefaultDelay() >> 16), (short) ((-1) - MotionEvent.axisFromString("")), (-44) - TextUtils.indexOf((CharSequence) "", '0'), (-2430006) + (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            j((byte) (View.MeasureSpec.getMode(0) + 80), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1522471340, (short) TextUtils.getOffsetAfter("", 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 44, (-2430042) - Color.alpha(0), objArr3);
            g.a(intern, append.append(((String) objArr3[0]).intern()).toString(), e2);
            Object[] objArr4 = new Object[1];
            f(new int[]{1063017000, -1151167355, -2036623369, -2038082059, 1193374242, -1427022111, 1080067001, -1536063296, -87059407, 2039433302}, 20 - Gravity.getAbsoluteGravity(0, 0), objArr4);
            throw new i(((String) objArr4[0]).intern());
        }
    }

    private static o.dr.d a(String str) throws i {
        char c2 = 65535;
        switch (str.hashCode()) {
            case -1881484424:
                Object[] objArr = new Object[1];
                j((byte) (Color.rgb(0, 0, 0) + 16777270), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1522471360, (short) (ViewConfiguration.getTapTimeout() >> 16), (-44) - TextUtils.indexOf((CharSequence) "", '0', 0), (-2429992) - (ViewConfiguration.getTouchSlop() >> 8), objArr);
                switch (!str.equals(((String) objArr[0]).intern())) {
                    case true:
                        break;
                    default:
                        int i2 = i + 67;
                        h = i2 % 128;
                        switch (i2 % 2 == 0 ? '2' : '\b') {
                            case '\b':
                                c2 = 1;
                                break;
                            default:
                                c2 = 1;
                                break;
                        }
                }
            case -1769016063:
                Object[] objArr2 = new Object[1];
                f(new int[]{1363383756, -546184176, -1535518656, -1894397404}, 9 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr2);
                if (str.equals(((String) objArr2[0]).intern())) {
                    c2 = 0;
                    break;
                }
                break;
            case -1564332615:
                Object[] objArr3 = new Object[1];
                f(new int[]{-1599014860, 1783837736, -945596966, 2105449377, -839290518, 735344647}, 11 - Color.argb(0, 0, 0, 0), objArr3);
                if (str.equals(((String) objArr3[0]).intern())) {
                    c2 = 4;
                    break;
                }
                break;
            case -912227312:
                Object[] objArr4 = new Object[1];
                j((byte) (123 - TextUtils.getCapsMode("", 0, 0)), 1522471368 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), 5 - AndroidCharacter.getMirror('0'), (-2430009) - TextUtils.indexOf("", "", 0, 0), objArr4);
                switch (!str.equals(((String) objArr4[0]).intern())) {
                    case false:
                        c2 = 5;
                        break;
                }
            case -379334758:
                Object[] objArr5 = new Object[1];
                f(new int[]{-1599014860, 1783837736, 413626855, -1291193351, 239713649, 1459819346, 603289588, -1559039672}, 14 - (Process.myPid() >> 22), objArr5);
                switch (str.equals(((String) objArr5[0]).intern())) {
                    case true:
                        c2 = 2;
                        break;
                }
            case 2089988781:
                Object[] objArr6 = new Object[1];
                f(new int[]{715771588, -120586661, -1889448446, -547517321, 638783975, 2123926998, -590392944, -1352693169, -503647397, -53977216}, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 17, objArr6);
                if (str.equals(((String) objArr6[0]).intern())) {
                    int i3 = h + 43;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                    c2 = 3;
                    break;
                }
                break;
        }
        switch (c2) {
            case 0:
                o.dr.d dVar = o.dr.d.c;
                int i5 = h + 39;
                i = i5 % 128;
                switch (i5 % 2 == 0) {
                    case false:
                        return dVar;
                    default:
                        throw null;
                }
            case 1:
                return o.dr.d.d;
            case 2:
                return o.dr.d.a;
            case 3:
                return o.dr.d.b;
            case 4:
                return o.dr.d.a;
            case 5:
                return o.dr.d.a;
            default:
                g.c();
                Object[] objArr7 = new Object[1];
                j((byte) (57 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 1522471310 - (ViewConfiguration.getPressedStateDuration() >> 16), (short) TextUtils.indexOf("", "", 0, 0), (-44) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (-2429997) - Color.green(0), objArr7);
                String intern = ((String) objArr7[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr8 = new Object[1];
                j((byte) (37 - View.combineMeasuredStates(0, 0)), 1522471379 - TextUtils.getOffsetBefore("", 0), (short) (ViewConfiguration.getDoubleTapTimeout() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 42, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 2429961, objArr8);
                g.e(intern, sb.append(((String) objArr8[0]).intern()).append(str).toString());
                Object[] objArr9 = new Object[1];
                f(new int[]{443905940, 1467399252, -1332639632, 405427321, -1913865151, 233545655, 581573720, 1843236361, -1022278920, 187699849, -2047090133, -1826312394}, KeyEvent.normalizeMetaState(0) + 24, objArr9);
                throw new i(((String) objArr9[0]).intern());
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:33:0x0120  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.dr.c d(java.lang.String r14) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 562
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.b.d(java.lang.String):o.dr.c");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1030
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.b.f(int[], int, java.lang.Object[]):void");
    }

    private static void j(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        char c2;
        int i5;
        boolean z2;
        int i6;
        int length;
        byte[] bArr;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(c)};
            Object obj = o.e.a.s.get(-2120899312);
            int i7 = -1;
            if (obj == null) {
                Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 11, (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), KeyEvent.normalizeMetaState(0) + 65);
                byte b3 = (byte) (-1);
                Object[] objArr3 = new Object[1];
                k(b3, (byte) (b3 & 8), (byte) 0, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            if (intValue == -1) {
                int i8 = $10 + 13;
                $11 = i8 % 128;
                int i9 = i8 % 2;
                z = true;
            } else {
                z = false;
            }
            switch (z) {
                case false:
                    c2 = 11;
                    break;
                default:
                    byte[] bArr2 = e;
                    switch (bArr2 != null) {
                        default:
                            int i10 = $10 + 27;
                            $11 = i10 % 128;
                            if (i10 % 2 == 0) {
                                length = bArr2.length;
                                bArr = new byte[length];
                            } else {
                                length = bArr2.length;
                                bArr = new byte[length];
                            }
                            int i11 = 0;
                            while (i11 < length) {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr2[i11])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 19, (char) (16425 - Color.green(0)), TextUtils.getTrimmedLength("") + Opcodes.FCMPG);
                                        byte b4 = (byte) i7;
                                        Object[] objArr5 = new Object[1];
                                        k(b4, (byte) (b4 & 6), (byte) 0, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr[i11] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i11++;
                                    i7 = -1;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            }
                            bArr2 = bArr;
                        case false:
                            if (bArr2 != null) {
                                int i12 = $11 + 31;
                                $10 = i12 % 128;
                                switch (i12 % 2 == 0) {
                                    case false:
                                        byte[] bArr3 = e;
                                        try {
                                            Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(d)};
                                            Object obj3 = o.e.a.s.get(-2120899312);
                                            if (obj3 != null) {
                                                c2 = 11;
                                            } else {
                                                c2 = 11;
                                                Class cls3 = (Class) o.e.a.c(View.getDefaultSize(0, 0) + 11, (char) Color.alpha(0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 65);
                                                byte b5 = (byte) (-1);
                                                Object[] objArr7 = new Object[1];
                                                k(b5, (byte) (b5 & 8), (byte) 0, objArr7);
                                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                                o.e.a.s.put(-2120899312, obj3);
                                            }
                                            i6 = ((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] - (-5810760824076169584L))) >>> ((int) (c & (-5810760824076169584L)));
                                            break;
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    default:
                                        c2 = 11;
                                        byte[] bArr4 = e;
                                        try {
                                            Object[] objArr8 = {Integer.valueOf(i2), Integer.valueOf(d)};
                                            Object obj4 = o.e.a.s.get(-2120899312);
                                            if (obj4 == null) {
                                                Class cls4 = (Class) o.e.a.c(AndroidCharacter.getMirror('0') - '%', (char) TextUtils.indexOf("", "", 0, 0), 65 - Color.blue(0));
                                                byte b6 = (byte) (-1);
                                                Object[] objArr9 = new Object[1];
                                                k(b6, (byte) (b6 & 8), (byte) 0, objArr9);
                                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                                o.e.a.s.put(-2120899312, obj4);
                                            }
                                            i6 = ((byte) (bArr4[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L)));
                                            break;
                                        } catch (Throwable th3) {
                                            Throwable cause3 = th3.getCause();
                                            if (cause3 == null) {
                                                throw th3;
                                            }
                                            throw cause3;
                                        }
                                }
                                intValue = (byte) i6;
                                int i13 = $11 + 69;
                                $10 = i13 % 128;
                                int i14 = i13 % 2;
                                break;
                            } else {
                                c2 = 11;
                                intValue = (short) (((short) (g[i2 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                                break;
                            }
                    }
            }
            if (intValue > 0) {
                int i15 = ((i2 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                if (z) {
                    i5 = 1;
                } else {
                    int i16 = $11 + 49;
                    $10 = i16 % 128;
                    int i17 = i16 % 2;
                    i5 = 0;
                }
                fVar.d = i15 + i5;
                try {
                    Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(b), sb};
                    Object obj5 = o.e.a.s.get(160906762);
                    if (obj5 == null) {
                        obj5 = ((Class) o.e.a.c(ImageFormat.getBitsPerPixel(0) + 12, (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 603 - KeyEvent.normalizeMetaState(0))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj5);
                    }
                    ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr5 = e;
                    if (bArr5 != null) {
                        int length2 = bArr5.length;
                        byte[] bArr6 = new byte[length2];
                        for (int i18 = 0; i18 < length2; i18++) {
                            bArr6[i18] = (byte) (bArr5[i18] ^ (-5810760824076169584L));
                        }
                        bArr5 = bArr6;
                    }
                    switch (bArr5 != null ? c2 : 'P') {
                        case 11:
                            z2 = true;
                            break;
                        default:
                            z2 = false;
                            break;
                    }
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        if (z2) {
                            byte[] bArr7 = e;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                        } else {
                            short[] sArr = g;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
