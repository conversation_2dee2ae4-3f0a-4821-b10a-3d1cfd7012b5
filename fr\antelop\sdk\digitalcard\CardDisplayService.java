package fr.antelop.sdk.digitalcard;

import fr.antelop.sdk.exception.WalletValidationException;
import o.er.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\CardDisplayService.smali */
public final class CardDisplayService {
    private final g innerCardDisplayService;

    public CardDisplayService(g gVar) {
        this.innerCardDisplayService = gVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerCardDisplayService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final SecureCardDisplay getSecureCardDisplay() throws WalletValidationException {
        return new SecureCardDisplay(this.innerCardDisplayService.e());
    }
}
