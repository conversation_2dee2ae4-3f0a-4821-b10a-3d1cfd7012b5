package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d1.smali */
public class d1 extends j0 {
    d1(int i, int i2, int i3, h hVar) {
        super(i, i2, i3, hVar);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        b0 aSN1Primitive = this.L.toASN1Primitive();
        boolean k = k();
        int a = aSN1Primitive.a(k);
        if (k) {
            a += 3;
        }
        return a + (z ? z.b(this.C) : 0);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.j0
    e0 c(b0 b0Var) {
        return new z0(b0Var);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return k() || this.L.toASN1Primitive().e();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        b0 aSN1Primitive = this.L.toASN1Primitive();
        boolean k = k();
        if (z) {
            int i = this.x;
            if (k || aSN1Primitive.e()) {
                i |= 32;
            }
            zVar.a(true, i, this.C);
        }
        if (k) {
            zVar.c(128);
            aSN1Primitive.a(zVar, true);
            zVar.c(0);
            zVar.c(0);
            return;
        }
        aSN1Primitive.a(zVar, false);
    }
}
