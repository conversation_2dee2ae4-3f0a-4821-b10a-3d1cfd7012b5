package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfr.smali */
final class zzfr {
    zzfr() {
    }

    public static final Object zza(Object obj, Object obj2) {
        zzfq zzfqVar = (zzfq) obj;
        zzfq zzfqVar2 = (zzfq) obj2;
        if (!zzfqVar2.isEmpty()) {
            if (!zzfqVar.zze()) {
                zzfqVar = zzfqVar.zzb();
            }
            zzfqVar.zzd(zzfqVar2);
        }
        return zzfqVar;
    }
}
