package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.security.keystore.UserNotAuthenticatedException;
import androidx.biometric.BiometricPrompt;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingBiometricAuthenticationCallback;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import javax.crypto.Mac;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\r\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000f\u001a\u00020\u000e¢\u0006\u0004\b\u0010\u0010\u0011J\u0018\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0004H\u0016J\b\u0010\b\u001a\u00020\u0006H\u0016J\u0010\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\tH\u0016¨\u0006\u0012"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/a;", "Landroidx/biometric/BiometricPrompt$AuthenticationCallback;", "", "errorCode", "", "errString", "", "onAuthenticationError", "onAuthenticationFailed", "Landroidx/biometric/BiometricPrompt$AuthenticationResult;", "result", "onAuthenticationSucceeded", "", "salt", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;", "deviceBindingCallback", "<init>", "(Ljava/lang/String;Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\a.smali */
public final class a extends BiometricPrompt.AuthenticationCallback {
    private final String a;
    private final DeviceBindingBiometricAuthenticationCallback b;

    public a(String salt, DeviceBindingBiometricAuthenticationCallback deviceBindingCallback) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        Intrinsics.checkNotNullParameter(deviceBindingCallback, "deviceBindingCallback");
        this.a = salt;
        this.b = deviceBindingCallback;
    }

    @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
    public void onAuthenticationError(int errorCode, CharSequence errString) {
        Intrinsics.checkNotNullParameter(errString, "errString");
        super.onAuthenticationError(errorCode, errString);
        if (errorCode == 13) {
            this.b.onAuthenticationFailed(new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.AUTHENTICATION_CANCELLED, new Throwable(errString.toString(), new Throwable())));
        } else {
            this.b.onAuthenticationFailed(new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, new Throwable(errString.toString(), new Throwable())));
        }
    }

    @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
    public void onAuthenticationFailed() {
        super.onAuthenticationFailed();
        this.b.onAuthenticationFailed(new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, null, 2, null));
    }

    @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
    public void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult result) {
        byte[] bArr;
        Mac mac;
        Intrinsics.checkNotNullParameter(result, "result");
        super.onAuthenticationSucceeded(result);
        try {
            BiometricPrompt.CryptoObject cryptoObject = result.getCryptoObject();
            if (cryptoObject == null || (mac = cryptoObject.getMac()) == null) {
                bArr = null;
            } else {
                byte[] bytes = this.a.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
                bArr = mac.doFinal(bytes);
            }
            this.b.onAuthenticationSucceeded(p.a(bArr));
        } catch (UserNotAuthenticatedException e) {
            this.b.onAuthenticationFailed(new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.USER_NOT_AUTHENTICATED, new Throwable(e)));
        } catch (Exception e2) {
            this.b.onAuthenticationFailed(new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, new Throwable(e2)));
        }
    }
}
