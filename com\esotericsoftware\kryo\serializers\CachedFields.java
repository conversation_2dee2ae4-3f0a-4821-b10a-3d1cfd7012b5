package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.serializers.AsmField;
import com.esotericsoftware.kryo.serializers.CollectionSerializer;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.serializers.MapSerializer;
import com.esotericsoftware.kryo.serializers.ReflectField;
import com.esotericsoftware.kryo.serializers.UnsafeField;
import com.esotericsoftware.kryo.util.Generics;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import com.esotericsoftware.reflectasm.FieldAccess;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.security.AccessControlException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\CachedFields.smali */
class CachedFields implements Comparator<FieldSerializer.CachedField> {
    static final FieldSerializer.CachedField[] emptyCachedFields = new FieldSerializer.CachedField[0];
    private Object access;
    private final FieldSerializer serializer;
    FieldSerializer.CachedField[] fields = new FieldSerializer.CachedField[0];
    FieldSerializer.CachedField[] copyFields = new FieldSerializer.CachedField[0];
    private final ArrayList<Field> removedFields = new ArrayList<>();

    public CachedFields(FieldSerializer serializer) {
        this.serializer = serializer;
    }

    public void rebuild() {
        if (this.serializer.type.isInterface()) {
            FieldSerializer.CachedField[] cachedFieldArr = emptyCachedFields;
            this.fields = cachedFieldArr;
            this.copyFields = cachedFieldArr;
            this.serializer.initializeCachedFields();
            return;
        }
        ArrayList<FieldSerializer.CachedField> newFields = new ArrayList<>();
        ArrayList<FieldSerializer.CachedField> newCopyFields = new ArrayList<>();
        boolean asm = (Util.unsafe || Util.isAndroid || !Modifier.isPublic(this.serializer.type.getModifiers())) ? false : true;
        for (Class nextClass = this.serializer.type; nextClass != Object.class; nextClass = nextClass.getSuperclass()) {
            for (Field field : nextClass.getDeclaredFields()) {
                addField(field, asm, newFields, newCopyFields);
            }
        }
        if (this.fields.length != newFields.size()) {
            this.fields = new FieldSerializer.CachedField[newFields.size()];
        }
        newFields.toArray(this.fields);
        Arrays.sort(this.fields, this);
        if (this.copyFields.length != newCopyFields.size()) {
            this.copyFields = new FieldSerializer.CachedField[newCopyFields.size()];
        }
        newCopyFields.toArray(this.copyFields);
        Arrays.sort(this.copyFields, this);
        this.serializer.initializeCachedFields();
    }

    private void addField(Field field, boolean asm, ArrayList<FieldSerializer.CachedField> fields, ArrayList<FieldSerializer.CachedField> copyFields) {
        FieldSerializer.CachedField cachedField;
        int modifiers = field.getModifiers();
        if (Modifier.isStatic(modifiers)) {
            return;
        }
        FieldSerializer.FieldSerializerConfig config = this.serializer.config;
        if (field.isSynthetic() && config.ignoreSyntheticFields) {
            return;
        }
        if (!field.isAccessible()) {
            if (!config.setFieldsAsAccessible) {
                return;
            }
            try {
                field.setAccessible(true);
            } catch (AccessControlException e) {
                if (Log.DEBUG) {
                    Log.debug("kryo", "Unable to set field as accessible: " + field);
                    return;
                }
                return;
            }
        }
        FieldSerializer.Optional optional = (FieldSerializer.Optional) field.getAnnotation(FieldSerializer.Optional.class);
        if ((optional == null || this.serializer.kryo.getContext().containsKey(optional.value())) && !this.removedFields.contains(field)) {
            boolean isTransient = Modifier.isTransient(modifiers);
            if (!isTransient || config.serializeTransient || config.copyTransient) {
                Class declaringClass = field.getDeclaringClass();
                Generics.GenericType genericType = new Generics.GenericType(declaringClass, this.serializer.type, field.getGenericType());
                Class fieldClass = genericType.getType() instanceof Class ? (Class) genericType.getType() : field.getType();
                int accessIndex = -1;
                if (asm && !Modifier.isFinal(modifiers) && Modifier.isPublic(modifiers) && Modifier.isPublic(fieldClass.getModifiers())) {
                    try {
                        if (this.access == null) {
                            this.access = FieldAccess.get(this.serializer.type);
                        }
                        accessIndex = ((FieldAccess) this.access).getIndex(field);
                    } catch (LinkageError | RuntimeException ex) {
                        if (Log.DEBUG) {
                            Log.debug("kryo", "Unable to use ReflectASM.", ex);
                        }
                    }
                }
                if (Util.unsafe) {
                    cachedField = newUnsafeField(field, fieldClass, genericType);
                } else if (accessIndex != -1) {
                    cachedField = newAsmField(field, fieldClass, genericType);
                    cachedField.access = (FieldAccess) this.access;
                    cachedField.accessIndex = accessIndex;
                } else {
                    cachedField = newReflectField(field, fieldClass, genericType);
                }
                cachedField.varEncoding = config.varEncoding;
                if (config.extendedFieldNames) {
                    cachedField.name = declaringClass.getSimpleName() + "." + field.getName();
                } else {
                    cachedField.name = field.getName();
                }
                if (cachedField instanceof ReflectField) {
                    cachedField.canBeNull = config.fieldsCanBeNull && !field.isAnnotationPresent(FieldSerializer.NotNull.class);
                    if (this.serializer.kryo.isFinal(fieldClass) || config.fixedFieldTypes) {
                        cachedField.valueClass = fieldClass;
                    }
                    if (Log.TRACE) {
                        Log.trace("kryo", "Cached " + fieldClass.getSimpleName() + " field: " + field.getName() + " (" + Util.className(declaringClass) + ")");
                    }
                } else {
                    cachedField.canBeNull = fieldClass == String.class && config.fieldsCanBeNull;
                    cachedField.valueClass = fieldClass;
                    if (Log.TRACE) {
                        Log.trace("kryo", "Cached " + fieldClass.getSimpleName() + " field: " + field.getName() + " (" + Util.className(declaringClass) + ")");
                    }
                }
                applyAnnotations(cachedField);
                if (isTransient) {
                    if (config.serializeTransient) {
                        fields.add(cachedField);
                    }
                    if (config.copyTransient) {
                        copyFields.add(cachedField);
                        return;
                    }
                    return;
                }
                fields.add(cachedField);
                copyFields.add(cachedField);
            }
        }
    }

    private FieldSerializer.CachedField newUnsafeField(Field field, Class fieldClass, Generics.GenericType genericType) {
        if (fieldClass.isPrimitive()) {
            if (fieldClass == Integer.TYPE) {
                return new UnsafeField.IntUnsafeField(field);
            }
            if (fieldClass == Float.TYPE) {
                return new UnsafeField.FloatUnsafeField(field);
            }
            if (fieldClass == Boolean.TYPE) {
                return new UnsafeField.BooleanUnsafeField(field);
            }
            if (fieldClass == Long.TYPE) {
                return new UnsafeField.LongUnsafeField(field);
            }
            if (fieldClass == Double.TYPE) {
                return new UnsafeField.DoubleUnsafeField(field);
            }
            if (fieldClass == Short.TYPE) {
                return new UnsafeField.ShortUnsafeField(field);
            }
            if (fieldClass == Character.TYPE) {
                return new UnsafeField.CharUnsafeField(field);
            }
            if (fieldClass == Byte.TYPE) {
                return new UnsafeField.ByteUnsafeField(field);
            }
        }
        if (fieldClass == String.class && (!this.serializer.kryo.getReferences() || !this.serializer.kryo.getReferenceResolver().useReferences(String.class))) {
            return new UnsafeField.StringUnsafeField(field);
        }
        return new UnsafeField(field, this.serializer, genericType);
    }

    private FieldSerializer.CachedField newAsmField(Field field, Class fieldClass, Generics.GenericType genericType) {
        if (fieldClass.isPrimitive()) {
            if (fieldClass == Integer.TYPE) {
                return new AsmField.IntAsmField(field);
            }
            if (fieldClass == Float.TYPE) {
                return new AsmField.FloatAsmField(field);
            }
            if (fieldClass == Boolean.TYPE) {
                return new AsmField.BooleanAsmField(field);
            }
            if (fieldClass == Long.TYPE) {
                return new AsmField.LongAsmField(field);
            }
            if (fieldClass == Double.TYPE) {
                return new AsmField.DoubleAsmField(field);
            }
            if (fieldClass == Short.TYPE) {
                return new AsmField.ShortAsmField(field);
            }
            if (fieldClass == Character.TYPE) {
                return new AsmField.CharAsmField(field);
            }
            if (fieldClass == Byte.TYPE) {
                return new AsmField.ByteAsmField(field);
            }
        }
        if (fieldClass == String.class && (!this.serializer.kryo.getReferences() || !this.serializer.kryo.getReferenceResolver().useReferences(String.class))) {
            return new AsmField.StringAsmField(field);
        }
        return new AsmField(field, this.serializer, genericType);
    }

    private FieldSerializer.CachedField newReflectField(Field field, Class fieldClass, Generics.GenericType genericType) {
        if (fieldClass.isPrimitive()) {
            if (fieldClass == Integer.TYPE) {
                return new ReflectField.IntReflectField(field);
            }
            if (fieldClass == Float.TYPE) {
                return new ReflectField.FloatReflectField(field);
            }
            if (fieldClass == Boolean.TYPE) {
                return new ReflectField.BooleanReflectField(field);
            }
            if (fieldClass == Long.TYPE) {
                return new ReflectField.LongReflectField(field);
            }
            if (fieldClass == Double.TYPE) {
                return new ReflectField.DoubleReflectField(field);
            }
            if (fieldClass == Short.TYPE) {
                return new ReflectField.ShortReflectField(field);
            }
            if (fieldClass == Character.TYPE) {
                return new ReflectField.CharReflectField(field);
            }
            if (fieldClass == Byte.TYPE) {
                return new ReflectField.ByteReflectField(field);
            }
        }
        return new ReflectField(field, this.serializer, genericType);
    }

    @Override // java.util.Comparator
    public int compare(FieldSerializer.CachedField o1, FieldSerializer.CachedField o2) {
        return o1.name.compareTo(o2.name);
    }

    public void removeField(String fieldName) {
        boolean found = false;
        int i = 0;
        while (true) {
            FieldSerializer.CachedField[] cachedFieldArr = this.fields;
            if (i >= cachedFieldArr.length) {
                break;
            }
            FieldSerializer.CachedField cachedField = cachedFieldArr[i];
            if (!cachedField.name.equals(fieldName)) {
                i++;
            } else {
                FieldSerializer.CachedField[] newFields = new FieldSerializer.CachedField[r3.length - 1];
                System.arraycopy(this.fields, 0, newFields, 0, i);
                System.arraycopy(this.fields, i + 1, newFields, i, newFields.length - i);
                this.fields = newFields;
                this.removedFields.add(cachedField.field);
                found = true;
                break;
            }
        }
        int i2 = 0;
        while (true) {
            FieldSerializer.CachedField[] cachedFieldArr2 = this.copyFields;
            if (i2 >= cachedFieldArr2.length) {
                break;
            }
            FieldSerializer.CachedField cachedField2 = cachedFieldArr2[i2];
            if (!cachedField2.name.equals(fieldName)) {
                i2++;
            } else {
                FieldSerializer.CachedField[] newFields2 = new FieldSerializer.CachedField[r3.length - 1];
                System.arraycopy(this.copyFields, 0, newFields2, 0, i2);
                System.arraycopy(this.copyFields, i2 + 1, newFields2, i2, newFields2.length - i2);
                this.copyFields = newFields2;
                this.removedFields.add(cachedField2.field);
                found = true;
                break;
            }
        }
        if (!found) {
            throw new IllegalArgumentException("Field \"" + fieldName + "\" not found on class: " + this.serializer.type.getName());
        }
    }

    public void removeField(FieldSerializer.CachedField removeField) {
        boolean found = false;
        int i = 0;
        while (true) {
            FieldSerializer.CachedField[] cachedFieldArr = this.fields;
            if (i >= cachedFieldArr.length) {
                break;
            }
            FieldSerializer.CachedField cachedField = cachedFieldArr[i];
            if (cachedField != removeField) {
                i++;
            } else {
                FieldSerializer.CachedField[] newFields = new FieldSerializer.CachedField[cachedFieldArr.length - 1];
                System.arraycopy(cachedFieldArr, 0, newFields, 0, i);
                System.arraycopy(this.fields, i + 1, newFields, i, newFields.length - i);
                this.fields = newFields;
                this.removedFields.add(cachedField.field);
                found = true;
                break;
            }
        }
        int i2 = 0;
        while (true) {
            FieldSerializer.CachedField[] cachedFieldArr2 = this.copyFields;
            if (i2 >= cachedFieldArr2.length) {
                break;
            }
            FieldSerializer.CachedField cachedField2 = cachedFieldArr2[i2];
            if (cachedField2 != removeField) {
                i2++;
            } else {
                FieldSerializer.CachedField[] newFields2 = new FieldSerializer.CachedField[cachedFieldArr2.length - 1];
                System.arraycopy(cachedFieldArr2, 0, newFields2, 0, i2);
                System.arraycopy(this.copyFields, i2 + 1, newFields2, i2, newFields2.length - i2);
                this.copyFields = newFields2;
                this.removedFields.add(cachedField2.field);
                found = true;
                break;
            }
        }
        if (!found) {
            throw new IllegalArgumentException("Field \"" + removeField + "\" not found on class: " + this.serializer.type.getName());
        }
    }

    private void applyAnnotations(FieldSerializer.CachedField cachedField) {
        Field field = cachedField.field;
        if (field.isAnnotationPresent(FieldSerializer.Bind.class)) {
            if (cachedField.serializer != null) {
                throw new KryoException("@Bind applied to a field that already has a serializer: " + cachedField.field.getDeclaringClass().getName() + "." + cachedField.field.getName());
            }
            FieldSerializer.Bind annotation = (FieldSerializer.Bind) field.getAnnotation(FieldSerializer.Bind.class);
            Class valueClass = annotation.valueClass();
            if (valueClass == Object.class) {
                valueClass = null;
            }
            if (valueClass != null) {
                cachedField.setValueClass(valueClass);
            }
            Serializer serializer = newSerializer(valueClass, annotation.serializer(), annotation.serializerFactory());
            if (serializer != null) {
                cachedField.setSerializer(serializer);
            }
            cachedField.setCanBeNull(annotation.canBeNull());
            cachedField.setVariableLengthEncoding(annotation.variableLengthEncoding());
            cachedField.setOptimizePositive(annotation.optimizePositive());
        }
        if (field.isAnnotationPresent(CollectionSerializer.BindCollection.class)) {
            if (cachedField.serializer != null) {
                throw new KryoException("@BindCollection applied to a field that already has a serializer: " + cachedField.field.getDeclaringClass().getName() + "." + cachedField.field.getName());
            }
            if (!Collection.class.isAssignableFrom(field.getType())) {
                throw new KryoException("@BindCollection can only be used with a field implementing Collection: " + Util.className(field.getType()));
            }
            CollectionSerializer.BindCollection annotation2 = (CollectionSerializer.BindCollection) field.getAnnotation(CollectionSerializer.BindCollection.class);
            Class elementClass = annotation2.elementClass();
            if (elementClass == Object.class) {
                elementClass = null;
            }
            Serializer elementSerializer = newSerializer(elementClass, annotation2.elementSerializer(), annotation2.elementSerializerFactory());
            CollectionSerializer serializer2 = new CollectionSerializer();
            serializer2.setElementsCanBeNull(annotation2.elementsCanBeNull());
            if (elementClass != null) {
                serializer2.setElementClass(elementClass);
            }
            if (elementSerializer != null) {
                serializer2.setElementSerializer(elementSerializer);
            }
            cachedField.setSerializer(serializer2);
        }
        if (field.isAnnotationPresent(MapSerializer.BindMap.class)) {
            if (cachedField.serializer != null) {
                throw new KryoException("@BindMap applied to a field that already has a serializer: " + cachedField.field.getDeclaringClass().getName() + "." + cachedField.field.getName());
            }
            if (!Map.class.isAssignableFrom(field.getType())) {
                throw new KryoException("@BindMap can only be used with a field implementing Map: " + Util.className(field.getType()));
            }
            MapSerializer.BindMap annotation3 = (MapSerializer.BindMap) field.getAnnotation(MapSerializer.BindMap.class);
            Class valueClass2 = annotation3.valueClass();
            if (valueClass2 == Object.class) {
                valueClass2 = null;
            }
            Serializer valueSerializer = newSerializer(valueClass2, annotation3.valueSerializer(), annotation3.valueSerializerFactory());
            Class keyClass = annotation3.keyClass();
            if (keyClass == Object.class) {
                keyClass = null;
            }
            Serializer keySerializer = newSerializer(keyClass, annotation3.keySerializer(), annotation3.keySerializerFactory());
            MapSerializer serializer3 = new MapSerializer();
            serializer3.setKeysCanBeNull(annotation3.keysCanBeNull());
            serializer3.setValuesCanBeNull(annotation3.valuesCanBeNull());
            if (keyClass != null) {
                serializer3.setKeyClass(keyClass);
            }
            if (keySerializer != null) {
                serializer3.setKeySerializer(keySerializer);
            }
            if (valueClass2 != null) {
                serializer3.setValueClass(valueClass2);
            }
            if (valueSerializer != null) {
                serializer3.setValueSerializer(valueSerializer);
            }
            cachedField.setSerializer(serializer3);
        }
    }

    private Serializer newSerializer(Class valueClass, Class serializerClass, Class factoryClass) {
        if (serializerClass == Serializer.class) {
            serializerClass = null;
        }
        if (factoryClass == SerializerFactory.class) {
            factoryClass = null;
        }
        if (factoryClass == null && serializerClass != null) {
            factoryClass = SerializerFactory.ReflectionSerializerFactory.class;
        }
        if (factoryClass == null) {
            return null;
        }
        return Util.newFactory(factoryClass, serializerClass).newSerializer(this.serializer.kryo, valueClass);
    }
}
