package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.util.Arrays;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\t6.smali */
class t6 {
    private u6 a;
    private boolean b;

    t6() {
    }

    public void a(boolean z, CipherParameters cipherParameters) {
        if (cipherParameters instanceof k6) {
            this.a = (u6) ((k6) cipherParameters).a();
        } else {
            this.a = (u6) cipherParameters;
        }
        this.b = z;
        int a = k1.a(this.a.b());
        u6 u6Var = this.a;
        t1.a(new w3("RSA", a, u6Var, a(u6Var.isPrivate(), z)));
    }

    public int b() {
        return this.b ? (this.a.b().bitLength() + 7) / 8 : ((r0 + 7) / 8) - 1;
    }

    public BigInteger b(BigInteger bigInteger) {
        v6 v6Var;
        BigInteger f;
        u6 u6Var = this.a;
        if ((u6Var instanceof v6) && (f = (v6Var = (v6) u6Var).f()) != null) {
            BigInteger e = v6Var.e();
            BigInteger g = v6Var.g();
            BigInteger c = v6Var.c();
            BigInteger d = v6Var.d();
            BigInteger h = v6Var.h();
            BigInteger modPow = bigInteger.remainder(e).modPow(c, e);
            BigInteger modPow2 = bigInteger.remainder(g).modPow(d, g);
            BigInteger add = modPow.subtract(modPow2).multiply(h).mod(e).multiply(g).add(modPow2);
            if (add.modPow(f, v6Var.b()).equals(bigInteger)) {
                return add;
            }
            throw new IllegalStateException("RSA engine faulty decryption/signing detected");
        }
        return bigInteger.modPow(this.a.a(), this.a.b());
    }

    public int a() {
        int bitLength = this.a.b().bitLength();
        if (this.b) {
            return ((bitLength + 7) / 8) - 1;
        }
        return (bitLength + 7) / 8;
    }

    public BigInteger a(byte[] bArr, int i, int i2) {
        if (i2 <= a() + 1) {
            if (i2 == a() + 1 && !this.b) {
                throw new DataLengthException("input too large for RSA cipher.");
            }
            if (i != 0 || i2 != bArr.length) {
                byte[] bArr2 = new byte[i2];
                System.arraycopy(bArr, i, bArr2, 0, i2);
                bArr = bArr2;
            }
            BigInteger bigInteger = new BigInteger(1, bArr);
            if (bigInteger.compareTo(this.a.b()) < 0) {
                return bigInteger;
            }
            throw new DataLengthException("input too large for RSA cipher.");
        }
        throw new DataLengthException("input too large for RSA cipher.");
    }

    public byte[] a(BigInteger bigInteger) {
        byte[] bArr;
        byte[] byteArray = bigInteger.toByteArray();
        if (this.b) {
            if (byteArray[0] == 0 && byteArray.length > b()) {
                int length = byteArray.length - 1;
                byte[] bArr2 = new byte[length];
                System.arraycopy(byteArray, 1, bArr2, 0, length);
                return bArr2;
            }
            if (byteArray.length >= b()) {
                return byteArray;
            }
            int b = b();
            byte[] bArr3 = new byte[b];
            System.arraycopy(byteArray, 0, bArr3, b - byteArray.length, byteArray.length);
            return bArr3;
        }
        if (byteArray[0] == 0) {
            int length2 = byteArray.length - 1;
            bArr = new byte[length2];
            System.arraycopy(byteArray, 1, bArr, 0, length2);
        } else {
            int length3 = byteArray.length;
            bArr = new byte[length3];
            System.arraycopy(byteArray, 0, bArr, 0, length3);
        }
        Arrays.fill(byteArray, (byte) 0);
        return bArr;
    }

    private q1 a(boolean z, boolean z2) {
        boolean z3 = z && z2;
        boolean z4 = !z && z2;
        boolean z5 = (z || z2) ? false : true;
        if (z3) {
            return q1.SIGNING;
        }
        if (z4) {
            return q1.ENCRYPTION;
        }
        if (z5) {
            return q1.VERIFYING;
        }
        return q1.DECRYPTION;
    }
}
