package fr.antelop.sdk.authentication;

import androidx.webkit.ProxyConfig;
import o.f.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationIssuerPasscode.smali */
public final class CustomerAuthenticationIssuerPasscode extends CustomerAuthenticationCredentials {
    private static final int MAX_CRYPTOGRAM_EXTRA_DATA_LENGTH = 1024;
    private static final int MAX_CRYPTOGRAM_LENGTH = 8192;
    private static final int MAX_ISSUER_PASSCODE_LENGTH = 8;
    private static final int MIN_CRYPTOGRAM_EXTRA_DATA_LENGTH = 0;
    private static final int MIN_CRYPTOGRAM_LENGTH = 0;
    private static final int MIN_ISSUER_PASSCODE_LENGTH = 4;
    private final d cryptogram;
    private final byte[] cryptogramExtraData;
    private final d passcode;

    public CustomerAuthenticationIssuerPasscode(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        this.passcode = new d(bArr);
        this.cryptogram = new d(bArr2);
        this.cryptogramExtraData = bArr3;
    }

    public final d getPasscode() {
        return this.passcode;
    }

    public final d getCryptogram() {
        return this.cryptogram;
    }

    public final byte[] getCryptogramExtraData() {
        return this.cryptogramExtraData;
    }

    public final boolean isValid() {
        int d = this.passcode.d();
        int d2 = this.cryptogram.d();
        boolean z = this.passcode.c() && d >= 4 && d <= 8;
        boolean z2 = d2 > 0 && d2 <= 8192;
        byte[] bArr = this.cryptogramExtraData;
        return z && z2 && (bArr == null || (bArr.length > 0 && bArr.length <= 1024));
    }

    public final String toString() {
        String str = "";
        for (int i = 0; i < getPasscode().d(); i++) {
            str = new StringBuilder().append(str).append(ProxyConfig.MATCH_ALL_SCHEMES).toString();
        }
        return new StringBuilder("CustomerAuthenticationIssuerPasscode{passcode='").append(str).append(", cryptogram='").append(getCryptogram()).append("', cryptogramExtraData='").append(getCryptogramExtraData()).append("'}").toString();
    }
}
