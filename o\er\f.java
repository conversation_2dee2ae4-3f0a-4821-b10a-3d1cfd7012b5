package o.er;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\f.smali */
public final class f extends a {
    private final String e;
    private static int c = 0;
    private static int a = 1;

    public f(boolean z, String str) {
        super(z);
        this.e = str;
    }

    public final String c() {
        String str;
        int i = a;
        int i2 = (i + 82) - 1;
        c = i2 % 128;
        switch (i2 % 2 != 0 ? 'Y' : 'M') {
            case Opcodes.DUP /* 89 */:
                str = this.e;
                int i3 = 18 / 0;
                break;
            default:
                str = this.e;
                break;
        }
        int i4 = i + 83;
        c = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }
}
