package o.bn;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\c.smali */
public final class c extends Exception {
    private final e b;
    private static int d = 0;
    private static int c = 1;

    public c(e eVar) {
        this.b = eVar;
    }

    public final e b() {
        int i = d;
        int i2 = (i ^ 73) + ((i & 73) << 1);
        c = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 5 : '-') {
            case '-':
                return this.b;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\c$e.smali */
    public static final class e {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final e a;
        private static int b;
        public static final e c;
        private static final /* synthetic */ e[] d;
        public static final e e;
        private static int f;
        private static int g;
        private static short[] h;
        private static int i;
        private static byte[] j;
        private static int k;

        static void c() {
            j = new byte[]{-66, -115, -88, -63, -61, -61, -18, -108, -41, -87, -55, -42, -83, -48, -53, -81, -15, 31, 33, 27, 47, 29, 0, 63, 44, -28, 50, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 26, 7, 13, 11, 41, -24, -17, -1, -23, 19, -27, -28, 22, 30, -64, Tnaf.POW_2_WIDTH, -20, -28, 28, -26, -1, 22, 29, 76, 88, 126, 74, 120, 69, 66, 120, 72, -110, 34, 114, 110, -112, -112, -112, -112};
            i = 909053666;
            g = 1755051583;
            b = 97264406;
        }

        static void init$0() {
            $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
            $$b = 104;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x003a). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void m(int r6, int r7, int r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.bn.c.e.$$a
                int r6 = r6 + 4
                int r8 = r8 * 2
                int r8 = 110 - r8
                int r7 = r7 * 4
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r8 = r6
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r7
                goto L3a
            L1a:
                r3 = r2
                r5 = r7
                r7 = r6
                r6 = r8
                r8 = r5
            L1f:
                int r7 = r7 + 1
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r8) goto L2e
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2e:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L3a:
                int r7 = -r7
                int r6 = r6 + r7
                r7 = r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1f
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bn.c.e.m(int, int, int, java.lang.Object[]):void");
        }

        private e(String str, int i2) {
        }

        private static /* synthetic */ e[] b() {
            int i2 = k + 53;
            f = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return new e[]{e, a, c};
                default:
                    e[] eVarArr = new e[2];
                    eVarArr[1] = e;
                    eVarArr[1] = a;
                    eVarArr[3] = c;
                    return eVarArr;
            }
        }

        public static e valueOf(String str) {
            int i2 = k + 49;
            f = i2 % 128;
            int i3 = i2 % 2;
            e eVar = (e) Enum.valueOf(e.class, str);
            int i4 = k + 23;
            f = i4 % 128;
            int i5 = i4 % 2;
            return eVar;
        }

        public static e[] values() {
            int i2 = k + 51;
            f = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return (e[]) d.clone();
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            k = 1;
            c();
            Object[] objArr = new Object[1];
            l((byte) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.getDefaultSize(0, 0) - 870526325, (short) (146 - AndroidCharacter.getMirror('0')), (-98) - ExpandableListView.getPackedPositionChild(0L), Color.alpha(0) - 1588918360, objArr);
            e = new e(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            l((byte) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (-870526309) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (short) ((-121) - TextUtils.getTrimmedLength("")), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 95, (-1588918370) - (ViewConfiguration.getPressedStateDuration() >> 16), objArr2);
            a = new e(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            l((byte) KeyEvent.getDeadChar(0, 0), (-870526291) + (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) (ImageFormat.getBitsPerPixel(0) + 36), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 101, (-1588918369) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr3);
            c = new e(((String) objArr3[0]).intern(), 2);
            d = b();
            int i2 = k + 83;
            f = i2 % 128;
            int i3 = i2 % 2;
        }

        public final o.bb.a d() {
            int i2 = k + 53;
            f = i2 % 128;
            int i3 = i2 % 2;
            switch (AnonymousClass5.c[ordinal()]) {
                case 1:
                    return o.bb.a.t;
                case 2:
                    o.bb.a aVar = o.bb.a.m;
                    int i4 = k + 57;
                    f = i4 % 128;
                    int i5 = i4 % 2;
                    return aVar;
                case 3:
                    return o.bb.a.w;
                default:
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr = new Object[1];
                    l((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), ((Process.getThreadPriority(0) + 20) >> 6) - 870526342, (short) ((-73) - TextUtils.lastIndexOf("", '0', 0)), (-96) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (-1588918362) + (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
                    throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:105:0x0224, code lost:
        
            r4 = 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:107:0x0222, code lost:
        
            if (r4 != false) goto L64;
         */
        /* JADX WARN: Code restructure failed: missing block: B:60:0x0210, code lost:
        
            if (r4 != false) goto L64;
         */
        /* JADX WARN: Code restructure failed: missing block: B:61:0x0226, code lost:
        
            r4 = 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:79:0x02da, code lost:
        
            r3 = r7;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 886
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bn.c.e.l(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.bn.c$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\c$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int b;
        static final /* synthetic */ int[] c;
        private static int d;

        static {
            d = 0;
            b = 1;
            int[] iArr = new int[e.values().length];
            c = iArr;
            try {
                iArr[e.e.ordinal()] = 1;
                int i = b;
                int i2 = ((i | 95) << 1) - (i ^ 95);
                d = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                c[e.a.ordinal()] = 2;
                int i4 = b;
                int i5 = (i4 & 97) + (i4 | 97);
                d = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[e.c.ordinal()] = 3;
                int i6 = (d + 40) - 1;
                b = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }
}
