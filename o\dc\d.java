package o.dc;

import android.graphics.Color;
import android.media.AudioTrack;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static char g;
    private static char[] h;
    private static int j;
    private final o.eg.b a;
    private final long b;
    private final Date c;
    private final Date d;
    private final h e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        h = new char[]{30514, 30521, 30580, 30500, 30530, 30560, 30569, 30571, 30587, 30582, 30526, 30522, 30527, 30574, 30539, 30559, 30583, 30589, 30591, 30520, 30566, 30561, 30525, 30501, 30588, 30524, 30568, 30572, 30499, 30563, 30523, 30570, 30511, 30562, 30586, 30529};
        g = (char) 17043;
    }

    static void init$0() {
        $$a = new byte[]{121, 45, -42, -114};
        $$b = 8;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = 73 - r8
            byte[] r0 = o.dc.d.$$a
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L34
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            int r6 = r6 + 1
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.d.k(int, short, byte, java.lang.Object[]):void");
    }

    public d(long j2, h hVar, Date date, Date date2, o.eg.b bVar) {
        this.b = j2;
        this.e = hVar;
        this.d = date;
        this.c = date2;
        this.a = bVar;
    }

    public final long d() {
        int i = j;
        int i2 = i + 19;
        f = i2 % 128;
        int i3 = i2 % 2;
        long j2 = this.b;
        int i4 = i + 35;
        f = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return j2;
            default:
                int i5 = 0 / 0;
                return j2;
        }
    }

    public final h a() {
        int i = f + Opcodes.LSUB;
        j = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 96 / 0;
                return this.e;
            default:
                return this.e;
        }
    }

    public final Date e() {
        int i = j;
        int i2 = i + Opcodes.DMUL;
        f = i2 % 128;
        int i3 = i2 % 2;
        Date date = this.d;
        int i4 = i + 25;
        f = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                throw null;
            default:
                return date;
        }
    }

    public final Date c() {
        int i = f + 23;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.c;
            default:
                int i2 = 8 / 0;
                return this.c;
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null) {
            int i = f + 33;
            j = i % 128;
            int i2 = i % 2;
            switch (getClass() == obj.getClass()) {
                case true:
                    d dVar = (d) obj;
                    if (this.b == dVar.b) {
                        int i3 = f + 39;
                        j = i3 % 128;
                        int i4 = i3 % 2;
                        switch (this.e.ordinal() == dVar.e.ordinal()) {
                            case false:
                                break;
                            default:
                                int i5 = f + Opcodes.LSHR;
                                j = i5 % 128;
                                int i6 = i5 % 2;
                                break;
                        }
                    }
                    break;
            }
            return true;
        }
        return false;
    }

    public final boolean b() {
        Date date = this.c;
        if (date != null) {
            switch (date.before(new Date())) {
                case true:
                    int i = f + 47;
                    j = i % 128;
                    if (i % 2 != 0) {
                    }
                    return true;
            }
        }
        int i2 = j + 35;
        f = i2 % 128;
        int i3 = i2 % 2;
        return false;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 26, "\u0010!㗶㗶\u0005\u000b\u000e\u001a\b\u0012\u0019\u000f\u000e\u001a\u0003\u0017\u0001\"㗩㗩\u000e\u0019 \u0001\u0013\b㖧", (byte) (TextUtils.lastIndexOf("", '0') + 1), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.b);
        Object[] objArr2 = new Object[1];
        i(8 - ((byte) KeyEvent.getModifierMetaStateMask()), "\u001a\"\u000b\u0017\u001a \u0015\u0016㖷", (byte) (16 - TextUtils.getOffsetBefore("", 0)), objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(this.e);
        Object[] objArr3 = new Object[1];
        i(View.resolveSize(0, 0) + 15, "\u001a\"\u001d\u000f\u0001\u0013\u000e\u001a\u0003\u0017\u000f\u000e\u0007 㖵", (byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 15), objArr3);
        StringBuilder append3 = append2.append(((String) objArr3[0]).intern()).append(this.d);
        Object[] objArr4 = new Object[1];
        i(AndroidCharacter.getMirror('0') - '\'', "\u001a\"\"\r\u0013\u0015\u000f\u000b㖽", (byte) (22 - Color.green(0)), objArr4);
        StringBuilder append4 = append3.append(((String) objArr4[0]).intern()).append(this.c);
        Object[] objArr5 = new Object[1];
        i((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 10, "\u001a\"\" 㘮㘮\u000e\u0019\u001e\u0001", (byte) (70 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), objArr5);
        String obj = append4.append(((String) objArr5[0]).intern()).append(this.a).append('}').toString();
        int i = f + 27;
        j = i % 128;
        switch (i % 2 != 0 ? '_' : '(') {
            case Opcodes.SWAP /* 95 */:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    public final o.eg.b g() {
        o.eg.b bVar;
        int i = f + Opcodes.DDIV;
        int i2 = i % 128;
        j = i2;
        switch (i % 2 != 0 ? 'J' : 'C') {
            case 'C':
                bVar = this.a;
                break;
            default:
                bVar = this.a;
                int i3 = 63 / 0;
                break;
        }
        int i4 = i2 + Opcodes.DMUL;
        f = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    public final int hashCode() {
        int i = f + 19;
        j = i % 128;
        switch (i % 2 != 0) {
            case false:
                return Objects.hash(Long.valueOf(this.b), this.e);
            default:
                Object[] objArr = new Object[3];
                objArr[0] = Long.valueOf(this.b);
                objArr[1] = this.e;
                return Objects.hash(objArr);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:50:0x018f, code lost:
    
        if (r2.e == r2.a) goto L61;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r25, java.lang.String r26, byte r27, java.lang.Object[] r28) {
        /*
            Method dump skipped, instructions count: 1084
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.d.i(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
