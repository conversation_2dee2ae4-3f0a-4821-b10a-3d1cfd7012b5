package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.DataLengthException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v3.smali */
public abstract class v3 implements o5 {
    protected v3() {
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o5
    public int getMultiBlockSize() {
        return getBlockSize();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o5
    public int processBlocks(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException, IllegalStateException {
        int multiBlockSize = getMultiBlockSize();
        int i4 = 0;
        for (int i5 = 0; i5 != i2; i5++) {
            i4 += processBlock(bArr, i, bArr2, i3 + i4);
            i += multiBlockSize;
        }
        return i4;
    }
}
