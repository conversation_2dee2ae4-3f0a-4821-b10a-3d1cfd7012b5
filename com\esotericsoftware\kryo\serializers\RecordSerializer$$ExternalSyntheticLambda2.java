package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.serializers.RecordSerializer;
import java.util.function.Function;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\RecordSerializer$$ExternalSyntheticLambda2.smali */
public final /* synthetic */ class RecordSerializer$$ExternalSyntheticLambda2 implements Function {
    @Override // java.util.function.Function
    public final Object apply(Object obj) {
        return ((RecordSerializer.RecordComponent) obj).name();
    }
}
