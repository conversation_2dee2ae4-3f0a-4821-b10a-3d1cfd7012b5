package o.ew;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.sql.Timestamp;
import o.ey.a;
import o.ey.d;
import o.ey.e;
import o.fc.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ew\a.smali */
public final class a extends d<o.fi.a, b> {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char[] c;
    private static int e;
    private static int f;
    private final String a;
    private final String d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        f = 1;
        b();
        ViewConfiguration.getMinimumFlingVelocity();
        TextUtils.lastIndexOf("", '0', 0, 0);
        int i = f + 41;
        e = i % 128;
        int i2 = i % 2;
    }

    static void b() {
        c = new char[]{30591, 30574, 30572, 30590, 30560, 30573, 30562, 30565, 30570, 30571, 30554, 30569, 30555, 30567, 30557, 30585, 30586, 30587, 30566, 30584, 30564, 30561, 30588, 30589, 30563};
        b = (char) 17040;
    }

    static void init$0() {
        $$g = new byte[]{108, 119, -51, 110};
        $$h = 102;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(byte r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r6 = 73 - r6
            int r7 = r7 * 3
            int r7 = r7 + 4
            byte[] r0 = o.ew.a.$$g
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ew.a.o(byte, int, int, java.lang.Object[]):void");
    }

    public a() {
        Object[] objArr = new Object[1];
        n(15 - ((byte) KeyEvent.getModifierMetaStateMask()), "\b\u0013\u0002\u0007\u0003\u0015\u0007\u000e\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (92 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), objArr);
        this.a = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n(KeyEvent.keyCodeFromString("") + 20, "\u0003\u0000\u0016\u0017\u0015\u000b\u0002\u000f\u0013\u0003\u0016\u000b\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) ((KeyEvent.getMaxKeyCode() >> 16) + 24), objArr2);
        this.d = ((String) objArr2[0]).intern();
    }

    @Override // o.ey.d
    public final /* synthetic */ o.eg.b b(o.fi.a aVar) throws o.eg.d {
        int i = e + 23;
        f = i % 128;
        int i2 = i % 2;
        o.eg.b c2 = c(aVar);
        int i3 = f + 81;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? ':' : '[') {
            case Opcodes.ASTORE /* 58 */:
                throw null;
            default:
                return c2;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ e b(o.eg.b bVar) throws o.eg.d {
        int i = f + 37;
        e = i % 128;
        switch (i % 2 != 0 ? 'D' : 'V') {
            case Opcodes.SASTORE /* 86 */:
                return c(bVar);
            default:
                c(bVar);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    @Override // o.ey.a
    public final /* synthetic */ o.fc.d b(c cVar, short s) {
        int i = e + 15;
        f = i % 128;
        switch (i % 2 == 0) {
        }
        o.fi.a e2 = e(false, cVar, s);
        int i2 = f + 95;
        e = i2 % 128;
        int i3 = i2 % 2;
        return e2;
    }

    @Override // o.ey.d
    public final /* synthetic */ o.fi.a d(o.eg.b bVar) throws o.eg.d {
        int i = f + 29;
        e = i % 128;
        int i2 = i % 2;
        o.fi.a f2 = f(bVar);
        int i3 = e + Opcodes.DNEG;
        f = i3 % 128;
        int i4 = i3 % 2;
        return f2;
    }

    @Override // o.ey.a
    public final /* synthetic */ o.eg.b e(e eVar) throws o.eg.d {
        int i = f + 19;
        e = i % 128;
        int i2 = i % 2;
        o.eg.b a = a((b) eVar);
        int i3 = f + Opcodes.LMUL;
        e = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.ey.d, o.ey.a
    public final /* synthetic */ o.eg.b e(o.fc.d dVar) throws o.eg.d {
        int i = e + Opcodes.LSHL;
        f = i % 128;
        int i2 = i % 2;
        o.eg.b c2 = c((o.fi.a) dVar);
        int i3 = f + Opcodes.LNEG;
        e = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return c2;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ e e(String str, String str2, boolean z) {
        int i = f + 23;
        e = i % 128;
        boolean z2 = i % 2 != 0;
        b a = a(str, str2, z);
        switch (z2) {
            case false:
                break;
            default:
                int i2 = 88 / 0;
                break;
        }
        int i3 = f + 25;
        e = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.ey.d, o.ey.a
    public final /* synthetic */ o.fc.d e(o.eg.b bVar) throws o.eg.d {
        int i = f + Opcodes.LMUL;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                o.fi.a f2 = f(bVar);
                int i2 = f + 99;
                e = i2 % 128;
                int i3 = i2 % 2;
                return f2;
            default:
                f(bVar);
                throw null;
        }
    }

    @Override // o.ey.a
    public final a.d d() {
        int i = e + Opcodes.DDIV;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                a.d dVar = a.d.c;
                int i2 = f + 9;
                e = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        int i3 = 37 / 0;
                        return dVar;
                    default:
                        return dVar;
                }
            default:
                a.d dVar2 = a.d.c;
                throw null;
        }
    }

    private o.eg.b a(b bVar) throws o.eg.d {
        int i = e + Opcodes.LUSHR;
        f = i % 128;
        int i2 = i % 2;
        o.eg.b e2 = super.e((a) bVar);
        Object[] objArr = new Object[1];
        n(20 - (ViewConfiguration.getWindowTouchSlop() >> 8), "\u0013\u0002\u0017\u0005\u0014\u000b\u0017\u0010\u0001\u0012\t\r\u0006\r\r\u0003\u0006\u0017\u0003\u0007", (byte) (77 - View.getDefaultSize(0, 0)), objArr);
        e2.d(((String) objArr[0]).intern(), bVar.r());
        int i3 = f + 43;
        e = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    private b c(o.eg.b bVar) throws o.eg.d {
        int i = f + 95;
        e = i % 128;
        int i2 = i % 2;
        b bVar2 = (b) super.b(bVar);
        Object[] objArr = new Object[1];
        n(21 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\u0013\u0002\u0017\u0005\u0014\u000b\u0017\u0010\u0001\u0012\t\r\u0006\r\r\u0003\u0006\u0017\u0003\u0007", (byte) (77 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), objArr);
        bVar2.d(bVar.r(((String) objArr[0]).intern()));
        int i3 = f + 65;
        e = i3 % 128;
        int i4 = i3 % 2;
        return bVar2;
    }

    private static b a(String str, String str2, boolean z) {
        b bVar = new b(str, str2, z);
        int i = f + 75;
        e = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    private static o.fi.a e(boolean z, c cVar, short s) {
        o.fi.a aVar = new o.fi.a(false, cVar, s);
        int i = f + 5;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                return aVar;
            default:
                int i2 = 59 / 0;
                return aVar;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0075. Please report as an issue. */
    private o.fi.a f(o.eg.b bVar) throws o.eg.d {
        int i = f + 83;
        e = i % 128;
        int i2 = i % 2;
        o.fi.a aVar = (o.fi.a) super.d(bVar);
        Object[] objArr = new Object[1];
        n((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 15, "\b\u0013\u0002\u0007\u0003\u0015\u0007\u000e\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (91 - ExpandableListView.getPackedPositionChild(0L)), objArr);
        if (bVar.b(((String) objArr[0]).intern())) {
            Object[] objArr2 = new Object[1];
            n(TextUtils.indexOf("", "", 0, 0) + 16, "\b\u0013\u0002\u0007\u0003\u0015\u0007\u000e\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (92 - Color.red(0)), objArr2);
            aVar.b(new Timestamp(bVar.m(((String) objArr2[0]).intern()).longValue()));
            int i3 = e + 97;
            f = i3 % 128;
            switch (i3 % 2 == 0 ? 'V' : '\b') {
            }
        }
        Object[] objArr3 = new Object[1];
        n(20 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "\u0003\u0000\u0016\u0017\u0015\u000b\u0002\u000f\u0013\u0003\u0016\u000b\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (TextUtils.getTrimmedLength("") + 24), objArr3);
        if (bVar.b(((String) objArr3[0]).intern())) {
            Object[] objArr4 = new Object[1];
            n(20 - TextUtils.indexOf("", "", 0, 0), "\u0003\u0000\u0016\u0017\u0015\u000b\u0002\u000f\u0013\u0003\u0016\u000b\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) ((Process.myTid() >> 22) + 24), objArr4);
            aVar.e(new Timestamp(bVar.m(((String) objArr4[0]).intern()).longValue()));
        }
        return aVar;
    }

    private o.eg.b c(o.fi.a aVar) throws o.eg.d {
        Object obj;
        int i = f + Opcodes.DNEG;
        e = i % 128;
        int i2 = i % 2;
        o.eg.b b2 = super.b((a) aVar);
        switch (aVar.i() != null ? 'I' : '(') {
            case 'I':
                Object[] objArr = new Object[1];
                n(16 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), "\b\u0013\u0002\u0007\u0003\u0015\u0007\u000e\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (92 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), objArr);
                b2.d(((String) objArr[0]).intern(), aVar.i().getTime());
                break;
        }
        if (aVar.n() != null) {
            int i3 = f + 13;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? 'G' : 'P') {
                case 'P':
                    Object[] objArr2 = new Object[1];
                    n(20 - TextUtils.getOffsetBefore("", 0), "\u0003\u0000\u0016\u0017\u0015\u000b\u0002\u000f\u0013\u0003\u0016\u000b\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (23 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), objArr2);
                    obj = objArr2[0];
                    break;
                default:
                    Object[] objArr3 = new Object[1];
                    n(90 - TextUtils.getOffsetBefore("", 0), "\u0003\u0000\u0016\u0017\u0015\u000b\u0002\u000f\u0013\u0003\u0016\u000b\u0010\b\u0007\u0017\u0010\u0002\u0005\u0001", (byte) (104 % TextUtils.indexOf((CharSequence) "", '-', 0, 0)), objArr3);
                    obj = objArr3[0];
                    break;
            }
            b2.d(((String) obj).intern(), aVar.n().getTime());
        }
        int i4 = e + 71;
        f = i4 % 128;
        int i5 = i4 % 2;
        return b2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:99:0x0164, code lost:
    
        if (r4.e == r4.a) goto L51;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(int r22, java.lang.String r23, byte r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1058
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ew.a.n(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
