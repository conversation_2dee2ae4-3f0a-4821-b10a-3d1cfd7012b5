package bc.org.bouncycastle.crypto.params;

import bc.org.bouncycastle.crypto.CipherParameters;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\params\AsymmetricKeyParameter.smali */
public class AsymmetricKeyParameter implements CipherParameters {
    boolean a;

    public AsymmetricKeyParameter(boolean z) {
        this.a = z;
    }

    public boolean isPrivate() {
        return this.a;
    }
}
