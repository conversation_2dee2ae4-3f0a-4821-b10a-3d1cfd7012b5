package o.by;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import o.ee.g;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\by\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static short[] f;
    private static byte[] g;
    private static int h;
    private static int i;
    private static int j;
    private static int k;
    private c b;
    b c;
    private final o.ei.c d;
    private final Context e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        k = 1;
        d();
        TextUtils.getCapsMode("", 0, 0);
        Color.argb(0, 0, 0, 0);
        Process.myPid();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        SystemClock.uptimeMillis();
        int i2 = k + 17;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void d() {
        g = new byte[]{-86, 89, -95, 84, -86, -77, 125, -85, 94, -94, 82, -89, -88, -75, 73, 86, -76, 90, 83, PSSSigner.TRAILER_IMPLICIT, -34, 49, 56, -49, 48, -61, 63, -59, -54, 98, -108, 109, 102, 103, -1, -13, 9, -12, 4, 12, -13, 82, UtilitiesSDKConstants.SRP_LABEL_MAC, -1, 6, -11, 17, -2, -9, 0, -1, 12, 67, -12, -59, -7, 5, -5, 0, -7, 3, 82, -84, 5, 1, 78, -83, 10, 73, -84, 15, -7, 0, 11, 10, -112, -112, -112, -112};
        j = 909053674;
        h = -141026594;
        a = 268260070;
    }

    static void init$0() {
        $$a = new byte[]{96, 104, -93, 9};
        $$b = Opcodes.FDIV;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r8 = r8 * 4
            int r8 = r8 + 4
            byte[] r0 = o.by.a.$$a
            int r6 = r6 * 2
            int r6 = 110 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L39
        L1c:
            r3 = r2
            r5 = r8
            r8 = r6
        L1f:
            r6 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r9
            r9 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L39:
            int r8 = r8 + r7
            int r7 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.by.a.m(int, int, byte, java.lang.Object[]):void");
    }

    public a(Context context, b bVar, o.ei.c cVar) {
        this.e = context;
        this.c = bVar;
        this.d = cVar;
    }

    public final void b() {
        g.c();
        Object[] objArr = new Object[1];
        l((byte) (55 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), (-970087542) - ((Process.getThreadPriority(0) + 20) >> 6), (short) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 101, 1044966407 + (ViewConfiguration.getScrollBarSize() >> 8), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 95), (-970087521) - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (short) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), (-113) - TextUtils.lastIndexOf("", '0', 0), 1044966422 - Color.green(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        switch (this.b == null) {
            case true:
                break;
            default:
                int i2 = i + 1;
                k = i2 % 128;
                int i3 = i2 % 2;
                break;
        }
        this.c = null;
        int i4 = i + Opcodes.LMUL;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x002f, code lost:
    
        r2 = new o.by.e(r20.e, new o.by.a.AnonymousClass1(r20), r20.d, r22, r21);
        r20.b = r2;
        r2.e(r23);
        r0 = o.by.a.i + com.esotericsoftware.asm.Opcodes.LREM;
        o.by.a.k = r0 % 128;
        r0 = r0 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0055, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0029, code lost:
    
        if (r20.d.q() != false) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001d, code lost:
    
        if (r20.d.q() != false) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0056, code lost:
    
        r3 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r14 = new java.lang.Object[1];
        l((byte) (android.graphics.Color.rgb(0, 0, 0) + 16777213), android.view.KeyEvent.keyCodeFromString("") - 970087513, (short) (android.view.ViewConfiguration.getFadingEdgeLength() >> 16), (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 116, 1044966408 - (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) == 0 ? 0 : -1)), r14);
        r6 = ((java.lang.String) r14[0]).intern();
        r4 = new java.lang.Object[1];
        l((byte) (android.graphics.Color.red(0) - 112), (-970087509) - android.text.TextUtils.lastIndexOf("", '0', 0), (short) ((android.os.SystemClock.elapsedRealtime() > 0 ? 1 : (android.os.SystemClock.elapsedRealtime() == 0 ? 0 : -1)) - 1), (-80) - android.view.KeyEvent.keyCodeFromString(""), (android.os.Process.myTid() >> 22) + 1044966409, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x00d8, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r3, r6, ((java.lang.String) r4[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void b(o.i.f r21, final boolean r22, o.h.d r23) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r20 = this;
            r1 = r20
            int r0 = o.by.a.i
            int r0 = r0 + 15
            int r2 = r0 % 128
            o.by.a.k = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L11
            r0 = 94
            goto L13
        L11:
            r0 = 51
        L13:
            r2 = 0
            switch(r0) {
                case 94: goto L20;
                default: goto L17;
            }
        L17:
            o.ei.c r0 = r1.d
            boolean r0 = r0.q()
            if (r0 == 0) goto L56
        L1f:
            goto L2f
        L20:
            o.ei.c r0 = r1.d
            boolean r0 = r0.q()
            r3 = 31
            int r3 = r3 / r2
            if (r0 == 0) goto L56
            goto L1f
        L2c:
            r0 = move-exception
            r2 = r0
            throw r2
        L2f:
            o.by.a$1 r5 = new o.by.a$1
            r0 = r22
            r5.<init>()
            o.by.e r2 = new o.by.e
            android.content.Context r4 = r1.e
            o.ei.c r6 = r1.d
            r3 = r2
            r7 = r22
            r8 = r21
            r3.<init>(r4, r5, r6, r7, r8)
            r1.b = r2
            r0 = r23
            r2.e(r0)
            int r0 = o.by.a.i
            int r0 = r0 + 113
            int r2 = r0 % 128
            o.by.a.k = r2
            int r0 = r0 % 2
            return
        L56:
            fr.antelop.sdk.exception.WalletValidationException r0 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r3 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            r4 = 16777213(0xfffffd, float:2.3509883E-38)
            int r5 = android.graphics.Color.rgb(r2, r2, r2)
            int r5 = r5 + r4
            byte r6 = (byte) r5
            r4 = -970087513(0xffffffffc62da3a7, float:-11112.913)
            java.lang.String r5 = ""
            int r7 = android.view.KeyEvent.keyCodeFromString(r5)
            int r7 = r7 + r4
            int r4 = android.view.ViewConfiguration.getFadingEdgeLength()
            int r4 = r4 >> 16
            short r8 = (short) r4
            r4 = 0
            float r9 = android.graphics.PointF.length(r4, r4)
            int r4 = (r9 > r4 ? 1 : (r9 == r4 ? 0 : -1))
            int r9 = r4 + (-116)
            long r10 = android.widget.ExpandableListView.getPackedPositionForChild(r2, r2)
            r12 = 0
            int r4 = (r10 > r12 ? 1 : (r10 == r12 ? 0 : -1))
            r10 = 1044966408(0x3e48ec08, float:0.19621289)
            int r10 = r10 - r4
            r4 = 1
            java.lang.Object[] r14 = new java.lang.Object[r4]
            r11 = r14
            l(r6, r7, r8, r9, r10, r11)
            r6 = r14[r2]
            java.lang.String r6 = (java.lang.String) r6
            java.lang.String r6 = r6.intern()
            int r7 = android.graphics.Color.red(r2)
            int r7 = r7 + (-112)
            byte r14 = (byte) r7
            r7 = 48
            int r7 = android.text.TextUtils.lastIndexOf(r5, r7, r2)
            r8 = -970087509(0xffffffffc62da3ab, float:-11112.917)
            int r15 = r8 - r7
            long r7 = android.os.SystemClock.elapsedRealtime()
            int r7 = (r7 > r12 ? 1 : (r7 == r12 ? 0 : -1))
            int r7 = r7 + (-1)
            short r7 = (short) r7
            int r5 = android.view.KeyEvent.keyCodeFromString(r5)
            int r17 = (-80) - r5
            int r5 = android.os.Process.myTid()
            int r5 = r5 >> 22
            r8 = 1044966409(0x3e48ec09, float:0.1962129)
            int r18 = r5 + r8
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r16 = r7
            r19 = r4
            l(r14, r15, r16, r17, r18, r19)
            r2 = r4[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            r0.<init>(r3, r6, r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.by.a.b(o.i.f, boolean, o.h.d):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:88:0x02b7, code lost:
    
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r22, int r23, short r24, int r25, int r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 938
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.by.a.l(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
