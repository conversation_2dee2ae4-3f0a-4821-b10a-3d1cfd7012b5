package com.vasco.digipass.sdk.utils.utilities.sc;

import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u7;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelGenerateResponse;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\sc\UtilitiesSDKSecureChannelMessageGenerator.smali */
public class UtilitiesSDKSecureChannelMessageGenerator {
    private UtilitiesSDKSecureChannelMessageGenerator() {
    }

    private static void a(String str, StringBuilder sb) throws UtilitiesSDKException {
        String substring = str.substring(0, 3);
        char charAt = substring.charAt(0);
        char charAt2 = substring.charAt(1);
        char charAt3 = substring.charAt(2);
        int indexOf = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ =&%".indexOf(charAt);
        int indexOf2 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ =&%".indexOf(charAt2);
        int indexOf3 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ =&%".indexOf(charAt3);
        if (indexOf == -1 || indexOf2 == -1 || indexOf3 == -1) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_INCORRECT_FORMAT);
        }
        StringBuilder sb2 = new StringBuilder(Integer.toHexString((indexOf * 40 * 40) + (indexOf2 * 40) + indexOf3));
        while (sb2.length() < 4) {
            sb2.insert(0, '0');
        }
        sb.append((CharSequence) sb2);
    }

    private static void b(String str, StringBuilder sb) throws UtilitiesSDKException {
        String substring = str.substring(3);
        if (!u7.b(substring)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_INCORRECT_FORMAT);
        }
        StringBuilder sb2 = new StringBuilder(Integer.toHexString(Integer.parseInt(substring)));
        while (sb2.length() < 6) {
            sb2.insert(0, '0');
        }
        sb.append((CharSequence) sb2);
    }

    private static void c(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        f(utilitiesSDKSecureChannelMessage, sb);
        e(utilitiesSDKSecureChannelMessage, sb);
        d(utilitiesSDKSecureChannelMessage, sb);
    }

    private static void d(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        String str = utilitiesSDKSecureChannelMessage.nonce;
        if (str == null) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NONCE_NULL);
        }
        if (str.length() != 16) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NONCE_INCORRECT_LENGTH);
        }
        if (!u7.c(str)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NONCE_INCORRECT_FORMAT);
        }
        sb.append(str);
    }

    private static void e(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        String str = utilitiesSDKSecureChannelMessage.serialNumber;
        if (str == null) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_NULL);
        }
        if (str.length() != 10) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_INCORRECT_LENGTH);
        }
        a(str, sb);
        b(str, sb);
    }

    private static void f(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        byte b = utilitiesSDKSecureChannelMessage.protocolVersion;
        if (b < 0 || b > 15) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_PROTOCOL_VERSION);
        }
        byte b2 = utilitiesSDKSecureChannelMessage.messageType;
        if (b2 < 0 || b2 > 63) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_MESSAGE_TYPE);
        }
        byte b3 = utilitiesSDKSecureChannelMessage.protectionType;
        if (b3 < 0 || b3 > 63) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_PROTECTION_TYPE);
        }
        int i = (b << 12) + (b2 << 6) + b3;
        sb.append(u7.a(new byte[]{(byte) ((i >> 8) & 255), (byte) (i & 255)}));
    }

    public static UtilitiesSDKSecureChannelGenerateResponse generateSecureChannelMessage(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) {
        try {
            if (utilitiesSDKSecureChannelMessage == null) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL);
            }
            StringBuilder sb = new StringBuilder();
            c(utilitiesSDKSecureChannelMessage, sb);
            a(utilitiesSDKSecureChannelMessage, sb);
            b(utilitiesSDKSecureChannelMessage, sb);
            return new UtilitiesSDKSecureChannelGenerateResponse(0, sb.toString().toUpperCase());
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKSecureChannelGenerateResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKSecureChannelGenerateResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static void b(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        byte b = utilitiesSDKSecureChannelMessage.protectionType;
        if (b == 1 || b == 17) {
            String str = utilitiesSDKSecureChannelMessage.authenticationTag;
            if (str != null) {
                if (str.length() == 16) {
                    if (u7.c(str)) {
                        sb.append(str);
                        return;
                    }
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_INCORRECT_FORMAT);
                }
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_INCORRECT_LENGTH);
            }
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_NULL);
        }
    }

    private static void a(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage, StringBuilder sb) throws UtilitiesSDKException {
        String str = utilitiesSDKSecureChannelMessage.body;
        if (str != null) {
            if (str.length() != 0 && str.length() <= 1024 && str.length() % 2 == 0) {
                if (u7.c(str)) {
                    sb.append(str);
                    return;
                }
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_FORMAT);
            }
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_LENGTH);
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_NULL);
    }
}
