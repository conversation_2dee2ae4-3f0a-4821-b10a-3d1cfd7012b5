package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import cz.muni.fi.xklinex.whiteboxAES.generator.GF2MatrixEx;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r5.smali */
public class r5 {
    public static void a(GF2MatrixEx gF2MatrixEx, byte b, int i, int i2) {
        for (int i3 = 0; i3 < 8; i3++) {
            gF2MatrixEx.set(i + i3, i2, (1 << i3) & b);
        }
    }

    public static byte a(GF2MatrixEx gF2MatrixEx, int i, int i2) {
        long j = 0;
        for (int i3 = 0; i3 < 8; i3++) {
            j |= gF2MatrixEx.get(i + i3, i2) == 0 ? 0L : 1 << i3;
        }
        return (byte) j;
    }
}
