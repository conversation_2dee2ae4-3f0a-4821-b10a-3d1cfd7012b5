package org.apache.cordova;

import android.net.Uri;
import androidx.webkit.ProxyConfig;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\AllowList.smali */
public class AllowList {
    public static final String TAG = "CordovaAllowList";
    private ArrayList<URLPattern> allowList = new ArrayList<>();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\AllowList$URLPattern.smali */
    private static class URLPattern {
        public Pattern host;
        public Pattern path;
        public Integer port;
        public Pattern scheme;

        private String regexFromPattern(String pattern, boolean allowWildcards) {
            StringBuilder regex = new StringBuilder();
            for (int i = 0; i < pattern.length(); i++) {
                char c = pattern.charAt(i);
                if (c == '*' && allowWildcards) {
                    regex.append(".");
                } else if ("\\.[]{}()^$?+|".indexOf(c) > -1) {
                    regex.append('\\');
                }
                regex.append(c);
            }
            return regex.toString();
        }

        /* JADX WARN: Removed duplicated region for block: B:13:0x0075 A[Catch: NumberFormatException -> 0x008e, TryCatch #0 {NumberFormatException -> 0x008e, blocks: (B:27:0x000a, B:30:0x0011, B:4:0x001e, B:6:0x0024, B:8:0x005d, B:11:0x0064, B:13:0x0075, B:16:0x007e, B:19:0x008a, B:21:0x0071, B:22:0x0027, B:24:0x002f, B:25:0x0051, B:3:0x001c), top: B:26:0x000a }] */
        /* JADX WARN: Removed duplicated region for block: B:22:0x0027 A[Catch: NumberFormatException -> 0x008e, TryCatch #0 {NumberFormatException -> 0x008e, blocks: (B:27:0x000a, B:30:0x0011, B:4:0x001e, B:6:0x0024, B:8:0x005d, B:11:0x0064, B:13:0x0075, B:16:0x007e, B:19:0x008a, B:21:0x0071, B:22:0x0027, B:24:0x002f, B:25:0x0051, B:3:0x001c), top: B:26:0x000a }] */
        /* JADX WARN: Removed duplicated region for block: B:6:0x0024 A[Catch: NumberFormatException -> 0x008e, TryCatch #0 {NumberFormatException -> 0x008e, blocks: (B:27:0x000a, B:30:0x0011, B:4:0x001e, B:6:0x0024, B:8:0x005d, B:11:0x0064, B:13:0x0075, B:16:0x007e, B:19:0x008a, B:21:0x0071, B:22:0x0027, B:24:0x002f, B:25:0x0051, B:3:0x001c), top: B:26:0x000a }] */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public URLPattern(java.lang.String r7, java.lang.String r8, java.lang.String r9, java.lang.String r10) throws java.net.MalformedURLException {
            /*
                r6 = this;
                r6.<init>()
                r0 = 0
                java.lang.String r1 = "*"
                r2 = 2
                r3 = 0
                if (r7 == 0) goto L1c
                boolean r4 = r1.equals(r7)     // Catch: java.lang.NumberFormatException -> L8e
                if (r4 == 0) goto L11
                goto L1c
            L11:
                java.lang.String r4 = r6.regexFromPattern(r7, r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.util.regex.Pattern r4 = java.util.regex.Pattern.compile(r4, r2)     // Catch: java.lang.NumberFormatException -> L8e
                r6.scheme = r4     // Catch: java.lang.NumberFormatException -> L8e
                goto L1e
            L1c:
                r6.scheme = r3     // Catch: java.lang.NumberFormatException -> L8e
            L1e:
                boolean r4 = r1.equals(r8)     // Catch: java.lang.NumberFormatException -> L8e
                if (r4 == 0) goto L27
                r6.host = r3     // Catch: java.lang.NumberFormatException -> L8e
                goto L5b
            L27:
                java.lang.String r4 = "*."
                boolean r4 = r8.startsWith(r4)     // Catch: java.lang.NumberFormatException -> L8e
                if (r4 == 0) goto L51
                java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch: java.lang.NumberFormatException -> L8e
                r4.<init>()     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.String r5 = "([a-z0-9.-]*\\.)?"
                java.lang.StringBuilder r4 = r4.append(r5)     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.String r5 = r8.substring(r2)     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.String r0 = r6.regexFromPattern(r5, r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.StringBuilder r0 = r4.append(r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.String r0 = r0.toString()     // Catch: java.lang.NumberFormatException -> L8e
                java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r2)     // Catch: java.lang.NumberFormatException -> L8e
                r6.host = r0     // Catch: java.lang.NumberFormatException -> L8e
                goto L5b
            L51:
                java.lang.String r0 = r6.regexFromPattern(r8, r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0, r2)     // Catch: java.lang.NumberFormatException -> L8e
                r6.host = r0     // Catch: java.lang.NumberFormatException -> L8e
            L5b:
                if (r9 == 0) goto L71
                boolean r0 = r1.equals(r9)     // Catch: java.lang.NumberFormatException -> L8e
                if (r0 == 0) goto L64
                goto L71
            L64:
                r0 = 10
                int r0 = java.lang.Integer.parseInt(r9, r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch: java.lang.NumberFormatException -> L8e
                r6.port = r0     // Catch: java.lang.NumberFormatException -> L8e
                goto L73
            L71:
                r6.port = r3     // Catch: java.lang.NumberFormatException -> L8e
            L73:
                if (r10 == 0) goto L8a
                java.lang.String r0 = "/*"
                boolean r0 = r0.equals(r10)     // Catch: java.lang.NumberFormatException -> L8e
                if (r0 == 0) goto L7e
                goto L8a
            L7e:
                r0 = 1
                java.lang.String r0 = r6.regexFromPattern(r10, r0)     // Catch: java.lang.NumberFormatException -> L8e
                java.util.regex.Pattern r0 = java.util.regex.Pattern.compile(r0)     // Catch: java.lang.NumberFormatException -> L8e
                r6.path = r0     // Catch: java.lang.NumberFormatException -> L8e
                goto L8c
            L8a:
                r6.path = r3     // Catch: java.lang.NumberFormatException -> L8e
            L8c:
                return
            L8e:
                r0 = move-exception
                java.net.MalformedURLException r1 = new java.net.MalformedURLException
                java.lang.String r2 = "Port must be a number"
                r1.<init>(r2)
                throw r1
            */
            throw new UnsupportedOperationException("Method not decompiled: org.apache.cordova.AllowList.URLPattern.<init>(java.lang.String, java.lang.String, java.lang.String, java.lang.String):void");
        }

        public boolean matches(Uri uri) {
            try {
                Pattern pattern = this.scheme;
                if (pattern != null && !pattern.matcher(uri.getScheme()).matches()) {
                    return false;
                }
                Pattern pattern2 = this.host;
                if (pattern2 != null && !pattern2.matcher(uri.getHost()).matches()) {
                    return false;
                }
                Integer num = this.port;
                if (num != null && !num.equals(Integer.valueOf(uri.getPort()))) {
                    return false;
                }
                Pattern pattern3 = this.path;
                if (pattern3 != null) {
                    if (!pattern3.matcher(uri.getPath()).matches()) {
                        return false;
                    }
                }
                return true;
            } catch (Exception e) {
                LOG.d(AllowList.TAG, e.toString());
                return false;
            }
        }
    }

    public void addAllowListEntry(String origin, boolean subdomains) {
        if (this.allowList != null) {
            try {
                if (origin.compareTo(ProxyConfig.MATCH_ALL_SCHEMES) == 0) {
                    LOG.d(TAG, "Unlimited access to network resources");
                    this.allowList = null;
                    return;
                }
                Pattern parts = Pattern.compile("^((\\*|[A-Za-z-]+):(//)?)?(\\*|((\\*\\.)?[^*/:]+))?(:(\\d+))?(/.*)?");
                Matcher m = parts.matcher(origin);
                if (m.matches()) {
                    String scheme = m.group(2);
                    String host = m.group(4);
                    if (("file".equals(scheme) || "content".equals(scheme)) && host == null) {
                        host = ProxyConfig.MATCH_ALL_SCHEMES;
                    }
                    String port = m.group(8);
                    String path = m.group(9);
                    if (scheme == null) {
                        this.allowList.add(new URLPattern("http", host, port, path));
                        this.allowList.add(new URLPattern("https", host, port, path));
                    } else {
                        this.allowList.add(new URLPattern(scheme, host, port, path));
                    }
                }
            } catch (Exception e) {
                LOG.d(TAG, "Failed to add origin %s", origin);
            }
        }
    }

    public boolean isUrlAllowListed(String uri) {
        if (this.allowList == null) {
            return true;
        }
        Uri parsedUri = Uri.parse(uri);
        Iterator<URLPattern> pit = this.allowList.iterator();
        while (pit.hasNext()) {
            URLPattern p = pit.next();
            if (p.matches(parsedUri)) {
                return true;
            }
        }
        return false;
    }
}
