package o.em;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import java.util.List;
import kotlin.text.Typography;
import o.af.c;
import o.ee.o;
import o.em.d;
import o.eo.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\c.smali */
public final class c extends d<f> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int h;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        j();
        Color.red(0);
        int i = h + 91;
        j = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$a = new byte[]{1, 25, 123, 58};
        $$b = 223;
    }

    static void j() {
        a = new int[]{-1798970651, -720997993, -82287256, 1891801548, 1975641799, 1027654424, -497101199, -30338966, -1756173030, 482415742, -2107313285, -1379420641, -226534027, 148686624, -50359793, 260883968, 1978664627, -2064821178};
        d = (char) 4954;
        b = (char) 38916;
        c = (char) 47419;
        e = (char) 19266;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r7 = 116 - r7
            int r6 = r6 * 3
            int r6 = 4 - r6
            byte[] r0 = o.em.c.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r7
            r4 = r2
            r7 = r6
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r3 = -r3
            int r6 = r6 + r3
            int r7 = r7 + 1
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.c.m(int, int, short, java.lang.Object[]):void");
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ long a() {
        int i = j + Opcodes.LSHR;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                return super.a();
            default:
                super.a();
                throw null;
        }
    }

    @Override // o.em.d
    public final /* synthetic */ void b() {
        int i = h + Opcodes.DSUB;
        j = i % 128;
        int i2 = i % 2;
        super.b();
        int i3 = h + 93;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ List<f> d() {
        int i = j + 99;
        h = i % 128;
        int i2 = i % 2;
        List<f> d2 = super.d();
        int i3 = j + 35;
        h = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    @Override // o.em.d
    protected final /* synthetic */ o.eg.b d(f fVar) throws o.eg.d {
        int i = h + 51;
        j = i % 128;
        boolean z = i % 2 != 0;
        o.eg.b b2 = b(fVar);
        switch (z) {
            default:
                int i2 = 88 / 0;
            case true:
                return b2;
        }
    }

    @Override // o.em.d
    protected final /* synthetic */ f e(o.eg.b bVar) throws o.eg.d {
        int i = h + Opcodes.DREM;
        j = i % 128;
        int i2 = i % 2;
        f a2 = a(bVar);
        int i3 = j + 77;
        h = i3 % 128;
        int i4 = i3 % 2;
        return a2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:20:0x01a4. Please report as an issue. */
    private static o.eg.b b(f fVar) throws o.eg.d {
        String e2;
        String str;
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        g(new int[]{-1797173099, -1216954330}, View.resolveSize(0, 0) + 3, objArr);
        String intern = ((String) objArr[0]).intern();
        String str2 = null;
        switch (fVar.a() == null) {
            case false:
                e2 = fVar.a().e();
                break;
            default:
                e2 = null;
                break;
        }
        bVar.d(intern, e2);
        Object[] objArr2 = new Object[1];
        g(new int[]{1007791304, -2064682793, 175772227, -2143275549, 39721719, -1772288547}, 9 - TextUtils.lastIndexOf("", '0', 0), objArr2);
        bVar.d(((String) objArr2[0]).intern(), fVar.b());
        Object[] objArr3 = new Object[1];
        l("铎㩝艗ꁑ\ud8b2듰좹ᆌꗩẮ蒠폧㉵蟃㾡ϡ\ue9aa燄瀃\ue687洊\uedc6", (ViewConfiguration.getScrollBarSize() >> 8) + 22, objArr3);
        bVar.d(((String) objArr3[0]).intern(), fVar.c());
        Object[] objArr4 = new Object[1];
        l("铎㩝篇ᓩ衐빪䎱괤ሞ獹", (ViewConfiguration.getLongPressTimeout() >> 16) + 9, objArr4);
        bVar.d(((String) objArr4[0]).intern(), fVar.d());
        Object[] objArr5 = new Object[1];
        l("铎㩝艗ꁑ\ud8b2듰좹ᆌ肴\ue824呿뻙뤁ꚇ㌟䬀諭ፈሞ獹", 19 - View.MeasureSpec.getSize(0), objArr5);
        bVar.d(((String) objArr5[0]).intern(), fVar.e());
        Object[] objArr6 = new Object[1];
        g(new int[]{2092206151, 282945478, -26163496, -1431846889, -70309978, -2096905310, -908021722, 641419502, 1540274784, -1633507424}, 18 - View.resolveSize(0, 0), objArr6);
        bVar.d(((String) objArr6[0]).intern(), fVar.h());
        Object[] objArr7 = new Object[1];
        l("㌟䬀泙ᒍﱂ禑鉤\udaf2숌\ue51c獅ꈌ截伫摴윯队駞\udcd4♍ﯵ蓢", 21 - TextUtils.indexOf("", "", 0), objArr7);
        bVar.d(((String) objArr7[0]).intern(), fVar.f());
        Object[] objArr8 = new Object[1];
        l("㌟䬀泙ᒍﱂ禑鉤\udaf2숌\ue51c獅ꈌ截伫뻘ⷲ࿏셏", 17 - TextUtils.indexOf((CharSequence) "", '0'), objArr8);
        bVar.d(((String) objArr8[0]).intern(), fVar.g() != null ? fVar.g().e() : null);
        Object[] objArr9 = new Object[1];
        g(new int[]{613981925, 819667536}, 4 - KeyEvent.normalizeMetaState(0), objArr9);
        String intern2 = ((String) objArr9[0]).intern();
        switch (fVar.j() != null ? '_' : 'C') {
            case 'C':
                str = null;
                break;
            default:
                str = fVar.j().e();
                break;
        }
        bVar.d(intern2, str);
        Object[] objArr10 = new Object[1];
        g(new int[]{1073186218, -1632487233, 1512792933, 1259301515}, 6 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr10);
        String intern3 = ((String) objArr10[0]).intern();
        if (fVar.n() != null) {
            int i = j + 37;
            h = i % 128;
            int i2 = i % 2;
            str2 = fVar.n().e();
            int i3 = j + 33;
            h = i3 % 128;
            switch (i3 % 2 == 0) {
            }
        } else {
            int i4 = h + 99;
            j = i4 % 128;
            if (i4 % 2 == 0) {
            }
        }
        bVar.d(intern3, str2);
        Object[] objArr11 = new Object[1];
        g(new int[]{997529349, 1537446578, -978623674, -838845590, 632337266, 2106894652, 1540274784, -1633507424}, Color.argb(0, 0, 0, 0) + 14, objArr11);
        bVar.d(((String) objArr11[0]).intern(), fVar.l());
        Object[] objArr12 = new Object[1];
        l("痰闑杖恪ǉ\uebdb\u0ffb찚\uf71a畲", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 10, objArr12);
        bVar.d(((String) objArr12[0]).intern(), fVar.o());
        Object[] objArr13 = new Object[1];
        g(new int[]{2092206151, 282945478, 967902074, 639584137}, 8 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr13);
        bVar.d(((String) objArr13[0]).intern(), fVar.k());
        return bVar;
    }

    private static f a(o.eg.b bVar) throws o.eg.d {
        f.a aVar;
        f.e eVar;
        f.d dVar;
        Object[] objArr = new Object[1];
        g(new int[]{-1797173099, -1216954330}, 3 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        f.b bVar2 = (f.b) bVar.e(f.b.class, ((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g(new int[]{1007791304, -2064682793, 175772227, -2143275549, 39721719, -1772288547}, 10 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        String r = bVar.r(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l("铎㩝艗ꁑ\ud8b2듰좹ᆌꗩẮ蒠폧㉵蟃㾡ϡ\ue9aa燄瀃\ue687洊\uedc6", 22 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr3);
        String q = bVar.q(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        l("铎㩝篇ᓩ衐빪䎱괤ሞ獹", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 9, objArr4);
        String q2 = bVar.q(((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        l("铎㩝艗ꁑ\ud8b2듰좹ᆌ肴\ue824呿뻙뤁ꚇ㌟䬀諭ፈሞ獹", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 18, objArr5);
        String r2 = bVar.r(((String) objArr5[0]).intern());
        Object[] objArr6 = new Object[1];
        g(new int[]{2092206151, 282945478, -26163496, -1431846889, -70309978, -2096905310, -908021722, 641419502, 1540274784, -1633507424}, ExpandableListView.getPackedPositionGroup(0L) + 18, objArr6);
        String q3 = bVar.q(((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        l("㌟䬀泙ᒍﱂ禑鉤\udaf2숌\ue51c獅ꈌ截伫摴윯队駞\udcd4♍ﯵ蓢", 21 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr7);
        String q4 = bVar.q(((String) objArr7[0]).intern());
        o.du.b bVar3 = q4 != null ? new o.du.b(q4) : null;
        Object[] objArr8 = new Object[1];
        l("㌟䬀泙ᒍﱂ禑鉤\udaf2숌\ue51c獅ꈌ截伫뻘ⷲ࿏셏", 18 - Color.alpha(0), objArr8);
        String q5 = bVar.q(((String) objArr8[0]).intern());
        switch (q5 != null ? Typography.dollar : (char) 22) {
            case '$':
                int i = j + 41;
                h = i % 128;
                int i2 = i % 2;
                aVar = (f.a) o.c(f.a.class, q5);
                if (aVar == null) {
                    Object[] objArr9 = new Object[1];
                    g(new int[]{763146321, -814638580, 951217725, 1769507721, 1088398089, 74051631, 1544421531, -1925412821, -1580520344, 1115612902, -1782337995, 91782216, 1737569704, 1255598665, -762654901, -1545476590, -765147172, -1638871983}, 34 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr9);
                    throw new o.eg.d(((String) objArr9[0]).intern());
                }
                break;
            default:
                aVar = null;
                break;
        }
        Object[] objArr10 = new Object[1];
        g(new int[]{613981925, 819667536}, (ViewConfiguration.getTouchSlop() >> 8) + 4, objArr10);
        String q6 = bVar.q(((String) objArr10[0]).intern());
        switch (q6 == null) {
            case false:
                eVar = (f.e) o.c(f.e.class, q6);
                if (eVar == null) {
                    Object[] objArr11 = new Object[1];
                    l("꿑✲⎥苟쫯䠭䎑♪鳿ǩ᭷吿됛\uee1f遲㷫餼㶺숌\ue51c", TextUtils.getOffsetBefore("", 0) + 20, objArr11);
                    throw new o.eg.d(((String) objArr11[0]).intern());
                }
                break;
            default:
                eVar = null;
                break;
        }
        Object[] objArr12 = new Object[1];
        g(new int[]{1073186218, -1632487233, 1512792933, 1259301515}, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 5, objArr12);
        String q7 = bVar.q(((String) objArr12[0]).intern());
        switch (q7 != null) {
            case true:
                int i3 = j + 13;
                h = i3 % 128;
                int i4 = i3 % 2;
                f.d dVar2 = (f.d) o.c(f.d.class, q7);
                if (dVar2 == null) {
                    Object[] objArr13 = new Object[1];
                    l("꿑✲⎥苟쫯䠭䎑♪㺛돋杭\ue5fe兣䲱瓲鎡遲㷫餼㶺숌\ue51c", TextUtils.getTrimmedLength("") + 22, objArr13);
                    throw new o.eg.d(((String) objArr13[0]).intern());
                }
                dVar = dVar2;
                break;
            default:
                dVar = null;
                break;
        }
        Object[] objArr14 = new Object[1];
        g(new int[]{997529349, 1537446578, -978623674, -838845590, 632337266, 2106894652, 1540274784, -1633507424}, TextUtils.getTrimmedLength("") + 14, objArr14);
        Long n = bVar.n(((String) objArr14[0]).intern());
        Object[] objArr15 = new Object[1];
        l("痰闑杖恪ǉ\uebdb\u0ffb찚\uf71a畲", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 9, objArr15);
        String q8 = bVar.q(((String) objArr15[0]).intern());
        Object[] objArr16 = new Object[1];
        g(new int[]{2092206151, 282945478, 967902074, 639584137}, (Process.myPid() >> 22) + 8, objArr16);
        return new f(bVar2, r, q, q2, r2, q3, bVar3, aVar, eVar, dVar, n, q8, bVar.r(((String) objArr16[0]).intern()));
    }

    public final void a(Context context, String str, final d.e<f> eVar) throws WalletValidationException {
        int i = h + 61;
        j = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        l("\ue765\ud7a6泙ᒍ\u0a58봈ꖉ\ue7e3ᨭ\udda3衐빪旅糵直撱\uf71a畲뀢ͪ", 20 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("佬ㄪ闥㼾뤁ꚇ틾볌", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 6, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.ei.c c2 = o.ei.c.c();
        if (!c2.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            g(new int[]{-1801619396, 10622210, 1129461950, 971505807}, 5 - Process.getGidForName(""), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l("䧙畆說䅱㰫ఐ\uda3a\uf0d9\udec8塇\uebbb톴努䃏㍱軙彌갾컘郯\udcbf\uf4b8喙\udf25ꜩ\u1ac2䃼蘰僪咛㾡ϡ茯櫹佬ㄪ呿뻙삯䧒\u0ab1ᖹ", MotionEvent.axisFromString("") + 43, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.af.c(context, new c.e() { // from class: o.em.c.1
            private static int d = 0;
            private static int e = 1;

            @Override // o.af.c.e
            public final void c(List<f> list) {
                c.this.a(list, new Date().getTime());
                eVar.e(list);
                int i3 = d + 11;
                e = i3 % 128;
                int i4 = i3 % 2;
            }

            @Override // o.af.c.e
            public final void a(o.bb.d dVar) {
                int i3 = e + 35;
                d = i3 % 128;
                int i4 = i3 % 2;
                eVar.e(o.bv.c.c(dVar).d());
                int i5 = d + 43;
                e = i5 % 128;
                switch (i5 % 2 == 0 ? 'a' : (char) 2) {
                    case 2:
                        return;
                    default:
                        int i6 = 38 / 0;
                        return;
                }
            }
        }, c2).c(str);
        int i3 = j + 23;
        h = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 882
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.c.g(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 556
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.c.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
