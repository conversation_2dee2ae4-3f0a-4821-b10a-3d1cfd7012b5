package fr.antelop.exposed;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import fr.antelop.sdk.firebase.AntelopFirebaseMessagingUtil;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\exposed\DefaultAntelopFirebaseMessagingService.smali */
public final class DefaultAntelopFirebaseMessagingService extends FirebaseMessagingService {
    @Override // com.google.firebase.messaging.FirebaseMessagingService
    public final void onNewToken(String str) {
        AntelopFirebaseMessagingUtil.onTokenRefresh(getBaseContext());
    }

    @Override // com.google.firebase.messaging.FirebaseMessagingService
    public final void onMessageReceived(RemoteMessage remoteMessage) {
        AntelopFirebaseMessagingUtil.onMessageReceived(getBaseContext(), remoteMessage);
    }
}
