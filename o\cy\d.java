package o.cy;

import android.os.SystemClock;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cy\d.smali */
final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        b = 1;
        c();
        ViewConfiguration.getFadingEdgeLength();
        SystemClock.currentThreadTimeMillis();
        int i = a + 5;
        b = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = 2928190290638190511L;
    }

    private static void f(int i, int i2, byte b2, Object[] objArr) {
        int i3 = 1 - (b2 * 2);
        byte[] bArr = $$a;
        int i4 = (i2 * 2) + Opcodes.IREM;
        int i5 = 4 - (i * 2);
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            i5++;
            i4 = i5 + i7;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
        }
        while (true) {
            int i8 = i6 + 1;
            bArr2[i8] = (byte) i4;
            if (i8 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i5];
            i5++;
            i4 += b3;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i8;
        }
    }

    static void init$0() {
        $$a = new byte[]{13, -73, -57, -113};
        $$b = 39;
    }

    d() {
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:26:0x007c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static o.cy.e c(java.lang.String r6) {
        /*
            int r0 = o.cy.d.a
            int r0 = r0 + 97
            int r1 = r0 % 128
            o.cy.d.b = r1
            int r0 = r0 % 2
            int r0 = r6.hashCode()
            r1 = 1
            r2 = 0
            switch(r0) {
                case 85333: goto L4c;
                case 2361477: goto L15;
                default: goto L13;
            }
        L13:
            goto L7c
        L15:
            java.lang.String r0 = ""
            r3 = 48
            int r0 = android.text.TextUtils.indexOf(r0, r3)
            int r0 = r0 + 29270
            java.lang.Object[] r3 = new java.lang.Object[r1]
            java.lang.String r4 = "煲̮闐➓"
            d(r4, r0, r3)
            r0 = r3[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L13
            int r0 = o.cy.d.a
            int r0 = r0 + 115
            int r3 = r0 % 128
            o.cy.d.b = r3
            int r0 = r0 % 2
            if (r0 != 0) goto L43
            r0 = 98
            goto L45
        L43:
            r0 = 66
        L45:
            switch(r0) {
                case 98: goto L4a;
                default: goto L48;
            }
        L48:
            r0 = r2
            goto L7d
        L4a:
            r0 = r2
            goto L7d
        L4c:
            int r0 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r0 = r0 >> 16
            int r0 = r0 + 28181
            java.lang.Object[] r3 = new java.lang.Object[r1]
            java.lang.String r4 = "煩\u1f7e굆"
            d(r4, r0, r3)
            r0 = r3[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L6b
            r0 = r1
            goto L6c
        L6b:
            r0 = r2
        L6c:
            switch(r0) {
                case 1: goto L70;
                default: goto L6f;
            }
        L6f:
            goto L7c
        L70:
            int r0 = o.cy.d.a
            int r0 = r0 + 19
            int r3 = r0 % 128
            o.cy.d.b = r3
            int r0 = r0 % 2
            r0 = r1
            goto L7d
        L7c:
            r0 = -1
        L7d:
            switch(r0) {
                case 0: goto Lb4;
                case 1: goto Lae;
                default: goto L80;
            }
        L80:
            java.lang.UnsupportedOperationException r0 = new java.lang.UnsupportedOperationException
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            int r4 = android.view.ViewConfiguration.getScrollDefaultDelay()
            int r4 = r4 >> 16
            int r4 = r4 + 5779
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r5 = "煪柂屼㋾⬃ƅ\uf62e\uef4e엂며邡褸羺吤䵀⏇ᠵໜ"
            d(r5, r4, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r1 = r3.append(r1)
            java.lang.StringBuilder r6 = r1.append(r6)
            java.lang.String r6 = r6.toString()
            r0.<init>(r6)
            throw r0
        Lae:
            o.cy.a r6 = new o.cy.a
            r6.<init>()
            return r6
        Lb4:
            o.cy.b r6 = new o.cy.b
            r6.<init>()
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.d.c(java.lang.String):o.cy.e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void d(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 602
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.d.d(java.lang.String, int, java.lang.Object[]):void");
    }
}
