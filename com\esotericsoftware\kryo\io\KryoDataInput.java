package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.KryoException;
import java.io.DataInput;
import java.io.EOFException;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\KryoDataInput.smali */
public class KryoDataInput implements DataInput, AutoCloseable {
    protected Input input;

    public KryoDataInput(Input input) {
        this.input = input;
    }

    public void setInput(Input input) {
        this.input = input;
    }

    @Override // java.io.DataInput
    public void readFully(byte[] b) throws IOException {
        readFully(b, 0, b.length);
    }

    @Override // java.io.DataInput
    public void readFully(byte[] b, int off, int len) throws IOException {
        try {
            this.input.readBytes(b, off, len);
        } catch (KryoException ex) {
            throw new EOFException(ex.getMessage());
        }
    }

    @Override // java.io.DataInput
    public int skipBytes(int n) throws IOException {
        return (int) this.input.skip(n);
    }

    @Override // java.io.DataInput
    public boolean readBoolean() throws IOException {
        return this.input.readBoolean();
    }

    @Override // java.io.DataInput
    public byte readByte() throws IOException {
        return this.input.readByte();
    }

    @Override // java.io.DataInput
    public int readUnsignedByte() throws IOException {
        return this.input.readByteUnsigned();
    }

    @Override // java.io.DataInput
    public short readShort() throws IOException {
        return this.input.readShort();
    }

    @Override // java.io.DataInput
    public int readUnsignedShort() throws IOException {
        return this.input.readShortUnsigned();
    }

    @Override // java.io.DataInput
    public char readChar() throws IOException {
        return this.input.readChar();
    }

    @Override // java.io.DataInput
    public int readInt() throws IOException {
        return this.input.readInt();
    }

    @Override // java.io.DataInput
    public long readLong() throws IOException {
        return this.input.readLong();
    }

    @Override // java.io.DataInput
    public float readFloat() throws IOException {
        return this.input.readFloat();
    }

    @Override // java.io.DataInput
    public double readDouble() throws IOException {
        return this.input.readDouble();
    }

    @Override // java.io.DataInput
    public String readLine() throws UnsupportedOperationException {
        throw new UnsupportedOperationException();
    }

    @Override // java.io.DataInput
    public String readUTF() throws IOException {
        return this.input.readString();
    }

    @Override // java.lang.AutoCloseable
    public void close() throws Exception {
        this.input.close();
    }
}
