package o.bo;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        d = 1;
        c();
        SystemClock.uptimeMillis();
        int i = a + 23;
        d = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = 4344524769789246113L;
        b = new char[]{50753, 50751, 50744, 51152, 51160, 51136, 51160, 51162, 51163, 51163, 51165, 51161, 51166, 51138, 51163, 51138, 51137, 51157, 51166, 51161, 51137, 51136, 51160, 51136, 51160, 51162, 50720, 50716, 50716, 50751, 51156, 50751, 50750, 51153, 51158, 51152, 51159, 51164, 51138, 51163, 50745, 50726, 51163, 51163, 51165, 51160, 51164, 51137, 51167, 51166, 50720, 50723, 51160, 51158, 50745, 50744, 51156, 51164, 50720, 50721, 51162, 51155, 51152, 51163, 51161, 51153, 50744, 50744, 51153, 51157, 51166, 50929, 50817, 50826, 50879, 50873, 50878, 50854, 50856, 50852, 50853, 50857, 50854, 50857, 50860, 50848, 50853, 50852, 50860, 50863, 50855, 50863, 50855, 50849, 50831, 50923, 50923, 50826, 50851, 50826, 50823, 50876, 50848, 50853, 50852, 50826, 50821, 50855, 50854, 50879, 50851, 50851, 50878, 50849, 50857, 50821, 50923, 50826, 50851, 50826, 50823, 50879, 50855, 50863, 50855, 50849, 50831, 50823, 50876, 50826, 50828, 50863, 50852, 50879, 50932, 50854, 50856, 50852, 50853, 50857, 50854, 50857, 50860, 50848, 50853, 50852, 50860, 50863, 50855, 50863, 50855, 50849, 50831, 50923, 50923, 50820, 50854, 50849, 50876, 50848, 50876, 50848, 50857, 50831, 50822, 50854, 50859, 50877, 50848, 50831, 50912, 50912, 50937, 50853, 50857, 50855, 50878, 50849, 50852, 50827, 50825, 50848, 50873, 50879, 50821, 50826, 50851, 50826, 50923, 50923, 50831, 50843, 50837, 50855, 50854, 50851, 50876, 50854, 50848, 50876, 50876, 50854, 50859, 50855, 50854, 50842, 50832, 50849, 50859, 50855, 50852, 50853, 50848, 50876, 50823, 50827, 50849, 50877, 50876, 50826, 50830, 50855, 50854, 50859, 50859, 50850, 50879, 50877, 50876, 50821, 50923, 50821, 50853, 50858, 50860, 50859, 50855, 50856, 50854, 50854, 50829, 50820, 50854, 50857, 50859, 50850, 50879, 50877, 50876, 50821, 50830, 50855};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r0 = o.bo.d.$$a
            int r7 = r7 + 4
            int r8 = r8 + 66
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2e
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r4 = -r4
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.d.h(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{Base64.padSymbol, 89, 45, -101};
        $$b = 24;
    }

    public static b b(Context context) throws g {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("㵁㴦\ue0c5鼵መ퓘ﻋ\udade\uebbd딢봠\u2437\ueedf퓔ꌅ㸥胂⋝\ud937", ViewConfiguration.getEdgeSlop() >> 16, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.bo.b c2 = new j().c(context);
        switch (c2 == null ? 'E' : 'X') {
            case Opcodes.POP2 /* 88 */:
                String a2 = c2.a(context);
                a aVar = new a(c2.e(), a2);
                c.a(context, aVar);
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", 1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                f("ꩲꨕ쯋灴䙴䏫헅\uf1d0Ӽ婣\ue94c灛秬ￚ䱄橉៱\u09d3㙶萎\u0dbfᎮᡒ빳㯙㷩Ʉꡨ퇇䟺\uf410쉰쿛出\ude4dﱫ\ue592篔쁠", ViewConfiguration.getMinimumFlingVelocity() >> 16, objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(aVar).toString());
                b bVar = new b(c2.a(), a2);
                int i = a + 85;
                d = i % 128;
                int i2 = i % 2;
                return bVar;
            default:
                int i3 = d + Opcodes.LNEG;
                a = i3 % 128;
                if (i3 % 2 != 0) {
                }
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", ViewConfiguration.getMaximumDrawingCacheSize() >> 24, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f("瘫癌응▃ǣ龲\ud95f\ufd4a儋ྔ껛㟌ꖵ\uf340ᦳⷞ쮨Չ掁쎙퇦ἴ䶹燐\ue7d3ㅬ垪\ueffaඓ䬤ꆪ藴᎐嵿议믾㦂眚闐凁俣褎\uffd0䞟嗲ꌀ짂綃箣딍폙᎘臢켸㷶ণ韟\ue139\u07b3㿩뷋זּᇨ햧쏊ന篦쮷\ue9cc⒔䔙\ue054＿㻐꽟陝Դ像뤂豟", ViewConfiguration.getMaximumDrawingCacheSize() >> 24, objArr6);
                o.ee.g.d(intern3, ((String) objArr6[0]).intern());
                return null;
        }
    }

    public static void a(Context context) throws g {
        Object[] objArr = new Object[1];
        f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", KeyEvent.getDeadChar(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        f("≰∂Ռ䟒㍊쯭ᭂ㽗㍈淈鱜պ\uf1efㅒ篁\u1f58鿍읁ǈ\uf15f藤\udd6c", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.bo.b c2 = new j().c(context);
        if (c2 == null) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            g("\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000", new int[]{0, 71, Opcodes.LOOKUPSWITCH, 8}, false, objArr3);
            o.ee.g.e(intern, ((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            f("䝇䜩᱔㥘嗄껊ɀ♅䶄ጘ龜掜铚⡎ԛ秗龜\ude44缉韂\ue0c0쐻儨귦훰\uea65䬹미㳲遹뵨퇥⋹虢霩\uefe0ࣦ같襔֑", (-1) - TextUtils.indexOf((CharSequence) "", '0'), objArr4);
            throw new g(((String) objArr4[0]).intern());
        }
        a c3 = c.c(context);
        switch (c3 == null ? (char) 1 : Typography.greater) {
            case 1:
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                g("\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000", new int[]{71, 63, 0, 5}, false, objArr5);
                o.ee.g.d(intern, ((String) objArr5[0]).intern());
                return;
            default:
                String a2 = c2.a(context);
                a aVar = new a(c2.e(), a2);
                c.a(context, aVar);
                o.ee.g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                g("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000", new int[]{Opcodes.I2F, 38, 0, 0}, false, objArr6);
                o.ee.g.d(intern, sb.append(((String) objArr6[0]).intern()).append(a2).toString());
                if (aVar.d(c3)) {
                    o.ee.g.c();
                    Object[] objArr7 = new Object[1];
                    f("䝮䜜鹿딾ﶳ껳\ue74e썛솤鼤劥쮃铱쵞褭톡䀘㭍\uf324㾦\ue0faⅠ\udd58ג횖ལ윟Ꮧ㳊畺\u3103禊⋈挽ᬃ䞏\u08cf䤋տ귺绶뜉潵믣撻鴖奼自䪢譑䌠\uefe9낡\uf165굍\uf5cfꚒ\udf2c靄쏒賞씡腇⦏\uf285㌼\ueb52㟕\ud897᪅햷ᰪ칢", View.MeasureSpec.getMode(0), objArr7);
                    o.ee.g.d(intern, ((String) objArr7[0]).intern());
                    int i = a + 77;
                    d = i % 128;
                    int i2 = i % 2;
                    return;
                }
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                f("伅佷ா阮툂Ꚙᖰㆥ赜폜純\ue432鲚㾠엕︐\uf2b8즳뿜ဗ\ue891펞醠⩣\udefdﶇ诧㰭㒰螅綰嘽⪥醂埶栱 뮻䦄艋盉䗶⎋鑒泐濯ᗜ긞䋃秬ྌ쁟룃ς\ue1a9\uda60껺ⶓ\udbaa\uec67蓶㟀춵ٰ\ufae9", Gravity.getAbsoluteGravity(0, 0), objArr8);
                o.ee.g.d(intern, ((String) objArr8[0]).intern());
                o.db.b.a().e(context, new o.de.d(o.av.a.d, f.B), false);
                int i3 = a + 1;
                d = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
        }
    }

    public static String e(Context context) throws g {
        String intern;
        Object obj;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", Color.green(0), objArr);
        String intern2 = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("䘗䙰벽ű୩꾆ꊾ蚦痹⭪ꑕ㵂閕袸㵧❖ﮑ纴䝴쥙\ue190撱楁", ViewConfiguration.getMinimumFlingVelocity() >> 16, objArr2);
        o.ee.g.d(intern2, ((String) objArr2[0]).intern());
        o.bo.b c2 = new j().c(context);
        switch (c2 == null ? '\t' : (char) 31) {
            case '\t':
                int i = d + 37;
                a = i % 128;
                if (i % 2 != 0) {
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", TextUtils.getCapsMode("", 1, 0), objArr3);
                    intern = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    g("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{Opcodes.IRETURN, 80, 0, 37}, false, objArr4);
                    obj = objArr4[0];
                } else {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", TextUtils.getCapsMode("", 0, 0), objArr5);
                    intern = ((String) objArr5[0]).intern();
                    Object[] objArr6 = new Object[1];
                    g("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{Opcodes.IRETURN, 80, 0, 37}, true, objArr6);
                    obj = objArr6[0];
                }
                o.ee.g.d(intern, ((String) obj).intern());
                return null;
            default:
                String d2 = c2.d(context);
                int i2 = a + 39;
                d = i2 % 128;
                switch (i2 % 2 == 0 ? ')' : '7') {
                    case ')':
                        throw null;
                    default:
                        return d2;
                }
        }
    }

    public static void d(Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f("䌟䍏Ϥ䤟羙ꪪ᷼㧯㶐挗킋䦊邎㟦甕厏ﺐ쇄༕붇\ue49e\udbd6℩螣", TextUtils.indexOf("", "", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("㒦㓔蓤稦牐\udd3b髺뻿ຼ倠\udd58䑺\ue723냢䘸广褫䛪㰫끛錡峈ሎ詥ꔭ狌ࠚ鱼伟\u08d2︕\uf642儜Ỗ퐎졳笂㒤쩲", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new j();
        j.d(context);
        int i = a + 81;
        d = i % 128;
        int i2 = i % 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\d$b.smali */
    public static final class b {
        private final String b;
        private final String c;
        private static int e = 0;
        private static int a = 1;

        public b(String str, String str2) {
            this.b = str;
            this.c = str2;
        }

        public final String c() {
            int i = a;
            int i2 = (i & Opcodes.DREM) + (i | Opcodes.DREM);
            e = i2 % 128;
            int i3 = i2 % 2;
            String str = this.b;
            int i4 = (i & 3) + (i | 3);
            e = i4 % 128;
            int i5 = i4 % 2;
            return str;
        }

        public final String a() {
            int i = (e + 2) - 1;
            int i2 = i % 128;
            a = i2;
            int i3 = i % 2;
            String str = this.c;
            int i4 = i2 + 57;
            e = i4 % 128;
            int i5 = i4 % 2;
            return str;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\d$a.smali */
    static final class a {
        private static int c = 0;
        private static int e = 1;
        final String b;
        final String d;

        a(String str, String str2) {
            this.b = str;
            this.d = str2;
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:25:0x009c. Please report as an issue. */
        /* JADX WARN: Removed duplicated region for block: B:22:0x008c  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        final boolean d(o.bo.d.a r5) {
            /*
                r4 = this;
                int r0 = o.bo.d.a.e
                int r1 = r0 + 117
                int r2 = r1 % 128
                o.bo.d.a.c = r2
                int r1 = r1 % 2
                if (r5 != 0) goto L10
                r1 = 51
                goto L12
            L10:
                r1 = 47
            L12:
                r2 = 0
                r3 = 1
                switch(r1) {
                    case 51: goto L22;
                    default: goto L17;
                }
            L17:
                java.lang.String r0 = r4.b
                java.lang.String r1 = r5.b
                boolean r0 = r0.equals(r1)
                if (r0 == 0) goto La2
                goto L4e
            L22:
                r5 = r0 & 9
                r1 = r0 | 9
                int r5 = r5 + r1
                int r1 = r5 % 128
                o.bo.d.a.c = r1
                int r5 = r5 % 2
                if (r5 == 0) goto L31
                r5 = r3
                goto L32
            L31:
                r5 = r2
            L32:
                switch(r5) {
                    case 1: goto L36;
                    default: goto L35;
                }
            L35:
                goto L37
            L36:
                r2 = r3
            L37:
                int r0 = r0 + 97
                int r5 = r0 % 128
                o.bo.d.a.c = r5
                int r0 = r0 % 2
                if (r0 == 0) goto L44
                r5 = 13
                goto L46
            L44:
                r5 = 39
            L46:
                switch(r5) {
                    case 13: goto L4a;
                    default: goto L49;
                }
            L49:
                return r2
            L4a:
                r5 = 0
                throw r5     // Catch: java.lang.Throwable -> L4c
            L4c:
                r5 = move-exception
                throw r5
            L4e:
                int r0 = o.bo.d.a.e
                r1 = r0 ^ 73
                r0 = r0 & 73
                int r0 = r0 << r3
                int r1 = r1 + r0
                int r0 = r1 % 128
                o.bo.d.a.c = r0
                int r1 = r1 % 2
                if (r1 == 0) goto L60
                r0 = r2
                goto L61
            L60:
                r0 = r3
            L61:
                switch(r0) {
                    case 1: goto L6d;
                    default: goto L64;
                }
            L64:
                java.lang.String r0 = r4.d
                java.lang.String r5 = r5.d
                boolean r5 = r0.equals(r5)
                goto L80
            L6d:
                java.lang.String r0 = r4.d
                java.lang.String r5 = r5.d
                boolean r5 = r0.equals(r5)
                if (r5 == 0) goto L7a
                r5 = 95
                goto L7c
            L7a:
                r5 = 33
            L7c:
                switch(r5) {
                    case 33: goto La2;
                    default: goto L7f;
                }
            L7f:
                goto L8c
            L80:
                r0 = 92
                int r0 = r0 / r2
                if (r5 == 0) goto L87
                r5 = r2
                goto L88
            L87:
                r5 = r3
            L88:
                switch(r5) {
                    case 1: goto La2;
                    default: goto L8b;
                }
            L8b:
                goto L7f
            L8c:
                int r5 = o.bo.d.a.c
                int r5 = r5 + 102
                int r5 = r5 - r3
                int r0 = r5 % 128
                o.bo.d.a.e = r0
                int r5 = r5 % 2
                if (r5 != 0) goto L9b
                r2 = r3
                goto L9c
            L9b:
            L9c:
                switch(r2) {
                    case 0: goto L9f;
                    default: goto L9f;
                }
            L9f:
                return r3
            La0:
                r5 = move-exception
                throw r5
            La2:
                int r5 = o.bo.d.a.c
                int r5 = r5 + 56
                int r5 = r5 - r3
                int r0 = r5 % 128
                o.bo.d.a.e = r0
                int r5 = r5 % 2
                return r2
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bo.d.a.d(o.bo.d$a):boolean");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\d$c.smali */
    static final class c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static a a;
        private static int b;
        private static long c;
        private static char[] d;
        private static int e;

        static void e() {
            d = new char[]{10407, 48981, 1880, 61297, 30590, 57091, 42770, 3893, 38681, 32710, 51162, 45026, 8716, 46571, 3574, 58847, 32223, 11417, 47948, 858, 60273, 29508, 56092, 41754, 2858, 37672, 31710, 50112, 44023, 13294, 39860, 25480, 52151, 21416, 14942, 33356, 27243, 11449, 47964, 859, 60266, 29536, 56074, 41757, 2829, 37670, 31698, 50124, 44023, 13225, 39892, 25545, 52115, 21434, 14934, 33351, 27228, 62065, 23066, 8716, 35369, 4669, 64208, 17094, 10999, 32354, 59782, 20938, 47541, 8618, 35264, 61889, 23032, 49643, 10500, 37194, 63797, 24874, 51520, 12609, 39288, 363, 26756, 53388, 14519, 41121, 2264, 28877, 55542, 16630, 43029, 4118, 30765, 57371, 18503, 45132, 6261, 32886, 60305, 21376, 48011, 9140, 35782, 62401, 23538, 50145, 11014, 37633, 64314, 25383, 52049, 13143, 11449, 47948, 858, 60273, 29511, 56086, 41757, 2864, 37679, 31696, 50122, 44024, 13309, 39824, 25478, 52151, 21401, 14923, 33350, 27247, 62048, 23069, 8716, 35371, 4711, 64218, 17116, 10987, 45819, 6812, 57991, 19117, 53917, 47446, 322, 59772, 29031, 11439, 47963, 861, 60274, 28941, 59122, 24313, 46814, 11977, 34490, 65208, 22174, 23279, 52509, 29980, 40200, 1325, 44362, 54619, 32113, 58747, 3468, 46477, 56764, 17820, 60887, 5571, 48637, 9702, 19544, 62533, 7288, 33794, 11339, 21575, 64630, 25677, 35968, 13451, 23741, 50360, 27852, 38081, 15607, 42214};
            c = 3223675615240960825L;
        }

        private static void g(byte b2, int i, byte b3, Object[] objArr) {
            byte[] bArr = $$a;
            int i2 = 3 - (b3 * 3);
            int i3 = i + 102;
            int i4 = 1 - (b2 * 2);
            byte[] bArr2 = new byte[i4];
            int i5 = -1;
            int i6 = i4 - 1;
            if (bArr == null) {
                int i7 = i6 + i2;
                i2 = i2;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = -1;
                i3 = i7;
                i6 = i6;
            }
            while (true) {
                int i8 = i5 + 1;
                bArr2[i8] = (byte) i3;
                if (i8 == i6) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                int i9 = i2 + 1;
                int i10 = i3;
                int i11 = i6;
                i2 = i9;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = i8;
                i3 = i10 + bArr[i9];
                i6 = i11;
            }
        }

        static void init$0() {
            $$a = new byte[]{116, -79, 3, -53};
            $$b = 69;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            b = 1;
            e();
            ViewConfiguration.getMaximumFlingVelocity();
            TextUtils.getOffsetBefore("", 0);
            TypedValue.complexToFraction(0, 0.0f, 0.0f);
            AudioTrack.getMaxVolume();
            TypedValue.complexToFloat(0);
            ViewConfiguration.getJumpTapTimeout();
            a = null;
            int i = b + 99;
            e = i % 128;
            int i2 = i % 2;
        }

        static void a(Context context, a aVar) {
            String str;
            a aVar2 = a;
            switch (aVar2 == null) {
                case false:
                    int i = e + 23;
                    b = i % 128;
                    int i2 = i % 2;
                    if (aVar2.d(aVar)) {
                        return;
                    }
                    break;
            }
            try {
                o.eg.b bVar = new o.eg.b();
                Object[] objArr = new Object[1];
                f((char) ((Process.myTid() >> 22) + 1054), MotionEvent.axisFromString("") + 1, View.MeasureSpec.makeMeasureSpec(0, 0) + 12, objArr);
                bVar.d(((String) objArr[0]).intern(), aVar.b);
                Object[] objArr2 = new Object[1];
                f((char) (View.getDefaultSize(0, 0) + 3763), 12 - View.combineMeasuredStates(0, 0), TextUtils.getTrimmedLength("") + 5, objArr2);
                bVar.d(((String) objArr2[0]).intern(), aVar.d);
                str = bVar.b();
                int i3 = b + 21;
                e = i3 % 128;
                int i4 = i3 % 2;
            } catch (o.eg.d e2) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f((char) ((Process.getThreadPriority(0) + 20) >> 6), KeyEvent.getDeadChar(0, 0) + 17, 20 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr3);
                String intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((char) (Process.myTid() >> 22), Color.alpha(0) + 37, (Process.myTid() >> 22) + 28, objArr4);
                o.ee.g.a(intern, ((String) objArr4[0]).intern(), e2);
                str = null;
            }
            Object[] objArr5 = new Object[1];
            f((char) (TextUtils.indexOf("", "") + 21197), (ViewConfiguration.getTouchSlop() >> 8) + 65, 48 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr5);
            SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr5[0]).intern(), 0).edit();
            Object[] objArr6 = new Object[1];
            f((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), 112 - TextUtils.getTrimmedLength(""), 36 - TextUtils.lastIndexOf("", '0', 0), objArr6);
            edit.putString(((String) objArr6[0]).intern(), str).commit();
            a = aVar;
        }

        static a c(Context context) {
            int i = b + 89;
            int i2 = i % 128;
            e = i2;
            Object obj = null;
            switch (i % 2 != 0 ? 'N' : '7') {
                case 'N':
                    obj.hashCode();
                    throw null;
                default:
                    a aVar = a;
                    switch (aVar == null) {
                        case true:
                            try {
                                Object[] objArr = new Object[1];
                                f((char) (21198 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 66 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), 47 - View.MeasureSpec.getMode(0), objArr);
                                SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
                                Object[] objArr2 = new Object[1];
                                f((char) Color.red(0), 112 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 37 - View.combineMeasuredStates(0, 0), objArr2);
                                String string = sharedPreferences.getString(((String) objArr2[0]).intern(), null);
                                if (string != null) {
                                    o.eg.b bVar = new o.eg.b(string);
                                    Object[] objArr3 = new Object[1];
                                    f((char) (Color.green(0) + 1054), ViewConfiguration.getPressedStateDuration() >> 16, 11 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
                                    String r = bVar.r(((String) objArr3[0]).intern());
                                    Object[] objArr4 = new Object[1];
                                    f((char) (3764 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 12 - Color.blue(0), Gravity.getAbsoluteGravity(0, 0) + 5, objArr4);
                                    a = new a(r, bVar.r(((String) objArr4[0]).intern()));
                                } else {
                                    Object[] objArr5 = new Object[1];
                                    f((char) (ViewConfiguration.getFadingEdgeLength() >> 16), 149 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 5 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr5);
                                    String string2 = sharedPreferences.getString(((String) objArr5[0]).intern(), null);
                                    SharedPreferences.Editor edit = sharedPreferences.edit();
                                    Object[] objArr6 = new Object[1];
                                    f((char) (Process.myPid() >> 22), MotionEvent.axisFromString("") + Opcodes.FCMPG, 3 - MotionEvent.axisFromString(""), objArr6);
                                    edit.remove(((String) objArr6[0]).intern()).apply();
                                    if (string2 != null) {
                                        Object[] objArr7 = new Object[1];
                                        f((char) (View.combineMeasuredStates(0, 0) + 23970), 153 - (ViewConfiguration.getJumpTapTimeout() >> 16), 8 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr7);
                                        a = new a(((String) objArr7[0]).intern(), string2);
                                    }
                                }
                            } catch (o.eg.d e2) {
                                o.ee.g.c();
                                Object[] objArr8 = new Object[1];
                                f((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), Process.getGidForName("") + 18, Process.getGidForName("") + 21, objArr8);
                                String intern = ((String) objArr8[0]).intern();
                                Object[] objArr9 = new Object[1];
                                f((char) (KeyEvent.getDeadChar(0, 0) + 30273), 161 - TextUtils.getTrimmedLength(""), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 33, objArr9);
                                o.ee.g.a(intern, ((String) objArr9[0]).intern(), e2);
                            }
                            return a;
                        default:
                            int i3 = i2 + 17;
                            b = i3 % 128;
                            switch (i3 % 2 != 0) {
                                case true:
                                    return aVar;
                                default:
                                    obj.hashCode();
                                    throw null;
                            }
                    }
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
            /*
                Method dump skipped, instructions count: 604
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bo.d.c.f(char, int, int, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v1 */
    /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
    private static void f(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.d.f(java.lang.String, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:27:0x00dd  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 838
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.d.g(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
