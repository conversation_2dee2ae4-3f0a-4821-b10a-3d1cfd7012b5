package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a3.smali */
public class a3 extends l {
    public a3(d3 d3Var) {
        super(d3Var);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l
    e0 h() {
        i iVar = new i(4);
        w wVar = this.b;
        if (wVar != null) {
            iVar.a(wVar);
        }
        r rVar = this.x;
        if (rVar != null) {
            iVar.a(rVar);
        }
        b0 b0Var = this.C;
        if (b0Var != null) {
            iVar.a(b0Var.g());
        }
        int i = this.L;
        iVar.a((h) new h3(i == 0, i, this.R));
        return new d3(iVar);
    }

    public a3(w wVar, r rVar, b0 b0Var, int i, b0 b0Var2) {
        super(wVar, rVar, b0Var, i, b0Var2);
    }
}
