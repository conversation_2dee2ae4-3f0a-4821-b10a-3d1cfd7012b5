package bc.org.bouncycastle.math.ec;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\b.smali */
class b {
    private final BigInteger a;
    private final int b;

    public b(BigInteger bigInteger, int i) {
        if (i < 0) {
            throw new IllegalArgumentException("scale may not be negative");
        }
        this.a = bigInteger;
        this.b = i;
    }

    private void b(b bVar) {
        if (this.b != bVar.b) {
            throw new IllegalArgumentException("Only SimpleBigDecimal of same scale allowed in arithmetic operations");
        }
    }

    public b a(int i) {
        if (i < 0) {
            throw new IllegalArgumentException("scale may not be negative");
        }
        int i2 = this.b;
        return i == i2 ? this : new b(this.a.shiftLeft(i - i2), i);
    }

    public b c() {
        return new b(this.a.negate(), this.b);
    }

    public BigInteger d() {
        return a(new b(ECConstants.ONE, 1).a(this.b)).a();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof b)) {
            return false;
        }
        b bVar = (b) obj;
        return this.a.equals(bVar.a) && this.b == bVar.b;
    }

    public int hashCode() {
        return this.a.hashCode() ^ this.b;
    }

    public String toString() {
        if (this.b == 0) {
            return this.a.toString();
        }
        BigInteger a = a();
        BigInteger subtract = this.a.subtract(a.shiftLeft(this.b));
        if (this.a.signum() == -1) {
            subtract = ECConstants.ONE.shiftLeft(this.b).subtract(subtract);
        }
        if (a.signum() == -1 && !subtract.equals(ECConstants.ZERO)) {
            a = a.add(ECConstants.ONE);
        }
        String bigInteger = a.toString();
        char[] cArr = new char[this.b];
        String bigInteger2 = subtract.toString(2);
        int length = bigInteger2.length();
        int i = this.b - length;
        for (int i2 = 0; i2 < i; i2++) {
            cArr[i2] = '0';
        }
        for (int i3 = 0; i3 < length; i3++) {
            cArr[i + i3] = bigInteger2.charAt(i3);
        }
        String str = new String(cArr);
        StringBuffer stringBuffer = new StringBuffer(bigInteger);
        stringBuffer.append(".");
        stringBuffer.append(str);
        return stringBuffer.toString();
    }

    public b c(b bVar) {
        return a(bVar.c());
    }

    public b b(BigInteger bigInteger) {
        return new b(this.a.subtract(bigInteger.shiftLeft(this.b)), this.b);
    }

    public int b() {
        return this.b;
    }

    public b a(b bVar) {
        b(bVar);
        return new b(this.a.add(bVar.a), this.b);
    }

    public int a(BigInteger bigInteger) {
        return this.a.compareTo(bigInteger.shiftLeft(this.b));
    }

    public BigInteger a() {
        return this.a.shiftRight(this.b);
    }
}
