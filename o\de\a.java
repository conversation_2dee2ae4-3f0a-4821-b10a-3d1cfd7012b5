package o.de;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.o;
import o.av.b;
import o.bv.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\a.smali */
public final class a implements o.b.a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int l;
    private static long m;
    private static char n;

    /* renamed from: o, reason: collision with root package name */
    private static long f53o;
    private static int p;
    HandlerThread a;
    final e b;
    final Context c;
    o.b.c d;
    private final o.av.a e;
    private o.av.b f;
    private boolean g;
    private final SharedPreferences h;
    private final f i;
    private final String j;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\a$e.smali */
    public interface e {
        void onJobEnd(o.bb.d dVar, a aVar, g gVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        p = 1;
        f();
        View.MeasureSpec.getMode(0);
        ExpandableListView.getPackedPositionGroup(0L);
        ViewConfiguration.getMinimumFlingVelocity();
        int i = l + 69;
        p = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 53 / 0;
                break;
        }
    }

    static void f() {
        f53o = -4733428448214858038L;
        n = (char) 17957;
        k = 1711241061;
        m = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{21, -84, -91, -118};
        $$b = Opcodes.IFNULL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void s(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = 114 - r7
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r6 = r6 * 3
            int r6 = r6 + 4
            byte[] r0 = o.de.a.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = -r6
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.a.s(byte, byte, int, java.lang.Object[]):void");
    }

    a(Context context, e eVar, o.av.a aVar, String str, f fVar) {
        this.c = context;
        this.b = eVar;
        this.e = aVar;
        this.j = str;
        this.i = fVar;
        Object[] objArr = new Object[1];
        q("<\ueb9f휚쌞껨骽虵爷嶍䥅㕒⃦ಠ\uf865\ue43d쾏뭅꜍鋬纬橳嘵䆉ⵉᤀӤ\uf0be\udc6e적뎒齀謒盈抨买㨀▖ᅛﴕ\ue8dd풧쁧갹鞉荍演嫋", ImageFormat.getBitsPerPixel(0) + 60344, objArr);
        this.h = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
    }

    final synchronized void c() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56392 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        q("?燍\ue3e1嗴잓㦅ꮩᶽ轅ĝ獦\ue570圌쥙㭥굻ể郡ʴ瓝\ue6d6", 29167 - TextUtils.getOffsetAfter("", 0), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.j).toString());
        if (this.d == null) {
            this.d = new o.b.c(this.c);
            int i = l + 49;
            p = i % 128;
            int i2 = i % 2;
        }
        Object[] objArr3 = new Object[1];
        q(";覍\u135c鴔⛒남㩼쑻䷾ힲ愆\ueacc璕﹆蠧ᇡ鮯", 35257 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
        HandlerThread handlerThread = new HandlerThread(((String) objArr3[0]).intern());
        this.a = handlerThread;
        handlerThread.start();
        new o.ee.b(this.a.getLooper()).post(new Runnable() { // from class: o.de.a$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                a.this.h();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Failed to find 'out' block for switch in B:20:0x0038. Please report as an issue. */
    public /* synthetic */ void h() {
        int i = l + Opcodes.DNEG;
        p = i % 128;
        try {
            switch (i % 2 == 0 ? (char) 0 : '^') {
                case Opcodes.DUP2_X2 /* 94 */:
                    Thread.sleep(1100L);
                    int i2 = p + 41;
                    l = i2 % 128;
                    switch (i2 % 2 != 0 ? Typography.greater : 'I') {
                    }
                default:
                    Thread.sleep(1100L);
                    throw null;
            }
        } catch (InterruptedException e2) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 56393, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            r(AndroidCharacter.getMirror('0') + 49297, "乌颹쥥㭋韛฿悤㯯旨ስꗤ懍腣勭嶘ɴ格\ue3a3恇䶀툡\udd9e︨\uf161䯾퐼\uf77d", (char) (TextUtils.indexOf((CharSequence) "", '0') + 36902), "셞嫀┮殐", "\u0000\u0000\u0000\u0000", objArr2);
            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
            o.bb.d dVar = new o.bb.d(o.bb.e.e);
            dVar.c(o.bb.a.ak);
            this.b.onJobEnd(dVar, this, new g());
            this.a.quitSafely();
        }
        this.d.c(this.c, this, new o.db.e(), null, null);
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - KeyEvent.keyCodeFromString(""), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        q("?⁕䃑慜至ꉽ싹\ue375΅\u2455䒖攨薬꙱욽\ue6cdݎ➝䠩梯西ꧽ쩀\uead1\u0b52", 8311 - View.getDefaultSize(0, 0), objArr4);
        o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(this.j).toString());
    }

    final synchronized void d() {
        int i = l + 29;
        p = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        boolean z = false;
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q("3ﲁ籠\uf620\uf2fc\uefa1\uec11\ue8d9\ue586\ue26e\udf26\udbfa\ud806픁퇓캣", 64693 - Color.argb(0, 0, 0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.av.b bVar = this.f;
        switch (bVar != null ? ' ' : 'T') {
            default:
                int i3 = p + Opcodes.LNEG;
                l = i3 % 128;
                switch (i3 % 2 != 0 ? ')' : 'C') {
                    case ')':
                        bVar.l();
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        bVar.l();
                }
            case Opcodes.BASTORE /* 84 */:
                b(false);
                o.bb.d dVar = new o.bb.d(o.bb.e.m);
                dVar.c(o.bb.a.ap);
                this.b.onJobEnd(dVar, this, new g());
                o.b.c cVar = this.d;
                if (cVar == null) {
                    z = true;
                }
                switch (z) {
                    case false:
                        cVar.a(this.c);
                        break;
                }
                this.a.quitSafely();
                break;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:19:0x00a6. Please report as an issue. */
    @Override // o.b.a
    public final void c(o.ei.c cVar, o.bb.d dVar, g gVar) {
        int i = l + 61;
        p = i % 128;
        switch (i % 2 == 0) {
            case false:
                switch (this.g) {
                    case true:
                        if (this.e == o.av.a.d) {
                            o.ee.g.c();
                            Object[] objArr = new Object[1];
                            q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 56392, objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            r((-390962962) - (ViewConfiguration.getJumpTapTimeout() >> 16), "臻馑婲ᛃ犊⁒⫈\u2d74ɘ谢㰻ⱪ쥯ꀅ葓둨쿱\ud94d챳␀\ue262늘\uedf5ᔺꐸ麐\uf681ꭑ➐脴䮰㎑\uf0c2曷睍碘ꃔ利\udbd4잁磳\ue816\u008aᳺ䮈㡜ꨖ퀡\uf185༪낱뮘쀄\ue5cdﳄ⾝ꨎ萺䴃헑볁\uf112ꗰﴘ瞮캼\ue7fdᒪ䪝ኇ\u0b7c体騐짨ᮾ녫Ꮣ㪧鼜悃됨厞രⰒ◿ዪ㣅ꚼḂ꽉玚\uea4d膄á濚䬹紅䪋ㄩ\ud8fd绗ᖲ뚛㦌킬婜榎᠁誔粶烀冞ꗶ갽咤嚒玲雈蹕瘬趔Ɐ⚡퉉㐹惴✾ꌵ驼ે天塓蕥ࠤ睑杳ᨨ\u0b7d嫳ﱑ툸寧\udf5c繛ᆓ", (char) (712 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), "\uee81뉠짨䴂", "\u0000\u0000\u0000\u0000", objArr2);
                            o.ee.g.d(intern, ((String) objArr2[0]).intern());
                            this.b.onJobEnd(dVar, this, gVar);
                            o.b.c cVar2 = this.d;
                            switch (cVar2 == null) {
                                case true:
                                    break;
                                default:
                                    int i2 = p + Opcodes.LNEG;
                                    l = i2 % 128;
                                    int i3 = i2 % 2;
                                    cVar2.a(this.c);
                                    int i4 = p + Opcodes.DREM;
                                    l = i4 % 128;
                                    switch (i4 % 2 == 0) {
                                    }
                            }
                            this.a.quitSafely();
                            return;
                        }
                        break;
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - (Process.myTid() >> 22), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                r(Drawable.resolveOpacity(0, 0), "䢓쇔蘛ￔ䯖\ue43e䏣ꦰ荃묫嚩۞ꁑ攒䊙ꃥ戚家슳郠鵺↑尞鐵ꠒ跳⪂ꑢ粹䊕㌸뾛\uda89輬┳啦䪽⍅䯠៦Ổ佻㭼쭿ᕫ꧳栬グ\udfd1\ued04䲴됭\ude36᱃薣닍䯵㊡ꌼⰻ⊏䷿툵䪻䛄ꖅ\ue8d7貛췩☒", (char) (8229 - TextUtils.indexOf((CharSequence) "", '0', 0)), "圵눞⛠쐠", "\u0000\u0000\u0000\u0000", objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(this.e).toString());
                b(true);
                b.InterfaceC0029b interfaceC0029b = new b.InterfaceC0029b() { // from class: o.de.a.4
                    public static final byte[] $$a = null;
                    public static final int $$b = 0;
                    private static int $10;
                    private static int $11;
                    private static char[] a;
                    private static int c;
                    private static int d;
                    private static long e;

                    static {
                        init$0();
                        $10 = 0;
                        $11 = 1;
                        c = 0;
                        d = 1;
                        a = new char[]{56793, 34236, 28019, 54576, 48374, 25772, 52342, 46137, 8176, 51103, 44922, 5943, 33729, 56202, 13127, 35585, 58057, 15042, 37463, 59922, 16838, 39299, 61782, 18695, 41090, 63617, 20547, 43022, 1998, 24448, 46915, 3841, 26313, 48834, 5647, 28226, 50637, 7564, 30049, 52490, 9415, 31873, 54345, 11319, 35794, 58246, 15171, 37654, 60103, 17057, 39501, 61967, 18898, 41358, 63815, 20758, 43207, 134, 11434, 29921, 39980, 9322, 19874, 38313, 15676, 17785, 61101, 14056, 24125, 58988, 4073, 22506, 65320, 1893, 43173, 61675, 6184, 41066, 51618, 4521, 47460, 49449, 27302, 45799, 55818, 25185, 35756, 54250, 31522, 33628, 9401, 19693, 37928, 15485, 17836, 60879, 13608, 23904, 59045, 3836, 22075, 65132};
                        e = -8705715296427608951L;
                    }

                    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
                    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
                    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x0030). Please report as a decompilation issue!!! */
                    /*
                        Code decompiled incorrectly, please refer to instructions dump.
                        To view partially-correct add '--show-bad-code' argument
                    */
                    private static void g(int r6, int r7, short r8, java.lang.Object[] r9) {
                        /*
                            int r6 = r6 + 102
                            int r7 = r7 + 4
                            byte[] r0 = o.de.a.AnonymousClass4.$$a
                            int r8 = r8 * 4
                            int r8 = r8 + 1
                            byte[] r1 = new byte[r8]
                            r2 = 0
                            if (r0 != 0) goto L15
                            r3 = r1
                            r4 = r2
                            r1 = r0
                            r0 = r9
                            r9 = r8
                            goto L30
                        L15:
                            r3 = r2
                        L16:
                            byte r4 = (byte) r6
                            r1[r3] = r4
                            int r3 = r3 + 1
                            if (r3 != r8) goto L25
                            java.lang.String r6 = new java.lang.String
                            r6.<init>(r1, r2)
                            r9[r2] = r6
                            return
                        L25:
                            int r7 = r7 + 1
                            r4 = r0[r7]
                            r5 = r9
                            r9 = r8
                            r8 = r4
                            r4 = r3
                            r3 = r1
                            r1 = r0
                            r0 = r5
                        L30:
                            int r6 = r6 + r8
                            r8 = r9
                            r9 = r0
                            r0 = r1
                            r1 = r3
                            r3 = r4
                            goto L16
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.de.a.AnonymousClass4.g(int, int, short, java.lang.Object[]):void");
                    }

                    static void init$0() {
                        $$a = new byte[]{71, -50, -52, -118};
                        $$b = 192;
                    }

                    @Override // o.av.b.InterfaceC0029b
                    public final void b(o.bb.d dVar2, g gVar2) {
                        int i5 = d + 87;
                        c = i5 % 128;
                        int i6 = i5 % 2;
                        o.ee.g.c();
                        Object[] objArr5 = new Object[1];
                        f((char) (61787 - ImageFormat.getBitsPerPixel(0)), KeyEvent.normalizeMetaState(0), TextUtils.lastIndexOf("", '0', 0) + 13, objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        f((char) (View.MeasureSpec.getMode(0) + 44907), 12 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 46 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr6);
                        o.ee.g.d(intern3, ((String) objArr6[0]).intern());
                        a.this.b(false);
                        a.this.b.onJobEnd(dVar2, a.this, gVar2);
                        switch (a.this.d != null) {
                            case false:
                                break;
                            default:
                                int i7 = c + 95;
                                d = i7 % 128;
                                int i8 = i7 % 2;
                                a.this.d.a(a.this.c);
                                break;
                        }
                        a.this.a.quitSafely();
                        int i9 = c + 81;
                        d = i9 % 128;
                        int i10 = i9 % 2;
                    }

                    @Override // o.av.b.InterfaceC0029b
                    public final void c(o.bb.d dVar2, g gVar2) {
                        o.ee.g.c();
                        Object[] objArr5 = new Object[1];
                        f((char) (61788 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), View.resolveSize(0, 0), 12 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        f((char) TextUtils.getCapsMode("", 0, 0), 58 - (ViewConfiguration.getPressedStateDuration() >> 16), View.MeasureSpec.getMode(0) + 44, objArr6);
                        o.ee.g.d(intern3, ((String) objArr6[0]).intern());
                        a.this.b(false);
                        a.this.b.onJobEnd(dVar2, a.this, gVar2);
                        switch (a.this.d != null ? 'R' : Typography.less) {
                            case Opcodes.DASTORE /* 82 */:
                                int i5 = c + Opcodes.LSHL;
                                d = i5 % 128;
                                int i6 = i5 % 2;
                                a.this.d.a(a.this.c);
                                int i7 = c + 73;
                                d = i7 % 128;
                                int i8 = i7 % 2;
                                break;
                        }
                        a.this.a.quitSafely();
                    }

                    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                        */
                    private static void f(char r19, int r20, int r21, java.lang.Object[] r22) {
                        /*
                            Method dump skipped, instructions count: 610
                            To view this dump add '--comments-level debug' option
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.de.a.AnonymousClass4.f(char, int, int, java.lang.Object[]):void");
                    }
                };
                if (this.e == o.av.a.c) {
                    this.f = new o.ay.c(this.c, interfaceC0029b, cVar, this.i);
                } else {
                    this.f = new o.ax.a(this.c, interfaceC0029b, cVar, this.i);
                }
                this.f.m();
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.b.a
    public final void d(o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        r(Process.myPid() >> 22, "㌥\ue448㧬┕٩\uf60a〱ﻁ㝬喐ῥ\uf4ee䚌줘쐲쓜펟\uebce䋴鍌ᤚ멟둵삲\uea49璃㲴术享糔义\ue840ꝼ걅\u2d29៊첽퀾", (char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), "⚿\uf2b3\uf5dd蚚", "\u0000\u0000\u0000\u0000", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        b(false);
        this.b.onJobEnd(dVar, this, new g());
        this.a.quitSafely();
        int i = l + Opcodes.DDIV;
        p = i % 128;
        switch (i % 2 == 0 ? '9' : (char) 21) {
            case 21:
                return;
            default:
                throw null;
        }
    }

    @Override // o.b.a
    public final void d(o.cb.a aVar, o.g.b bVar, o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56392 - TextUtils.lastIndexOf("", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        r(Color.green(0) - 1504896907, "獏䦟\ue1fd懎仔襥꧍齃뛬į郙㘃㔛썥Վㄴᴦ\ued56\udcca⟷澘ꗩ㗖鐛\uf3cb\uf13f\uebe7怐鰢▍魺쑸᯦풽ꭅ뷥ꇂઝڠ\uf416译룹䴦䑚瘣\ue591\ua8cd재꛴僼软䃮㽈传黂ꞇ毯‘괹艹읟䃔Ứ閮檞ཝ\ud8e6潜\uf869䟔\uecd7", (char) (Gravity.getAbsoluteGravity(0, 0) + 5036), "町䴘겦숓", "\u0000\u0000\u0000\u0000", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        b(false);
        this.b.onJobEnd(dVar, this, new g());
        this.a.quitSafely();
        int i = p + 73;
        l = i % 128;
        int i2 = i % 2;
    }

    @Override // o.b.a
    public final void e() {
        int i = l + 47;
        p = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q("5鋇◽룭䯢\ude80熄Ґ鞲⪆뵅假\ue352癨।鰪⼋숗唭\ue834竃\u0dc7ꃷ㏡웾妈\uec9a羞\u128fꖾ㡔쭞幌\uf168葨ᜊꨙ㴫퀇挱\uf5e5裞ᯭ껥䇹", 37619 - ((Process.getThreadPriority(0) + 20) >> 6), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.g = true;
        int i3 = p + Opcodes.DMUL;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.b.a
    public final void a() {
        int i = l + Opcodes.LSHL;
        p = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        q("\u0016\udc7a뢮铤焝䵎⦏\u05c9\ue277뺁髯眛", 56393 - View.combineMeasuredStates(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q("5疣\ueb35惱홲䯌솼㜔겒≂韝ോ茢\uf894湬\ue3ce奋켳䒥먈⿳ꕋ\u1aef邭ؔ箉\uf165曙\udcbb刀잋㵧닓⡛鸵ᎋ褏ﻦ瑞\uea14徬픲䫳쁮㗀ꮶ", 30103 - KeyEvent.keyCodeFromString(""), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.g = false;
        int i3 = p + 47;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 19 : '\b') {
            case 19:
                throw null;
            default:
                return;
        }
    }

    public final o.av.a b() {
        int i = p + 93;
        l = i % 128;
        switch (i % 2 != 0 ? 'E' : (char) 7) {
            case 7:
                return this.e;
            default:
                throw null;
        }
    }

    final void b(boolean z) {
        int i = p + 17;
        l = i % 128;
        int i2 = i % 2;
        SharedPreferences.Editor edit = this.h.edit();
        Object[] objArr = new Object[1];
        r((-1518058281) - TextUtils.getOffsetAfter("", 0), "왊雽⽐媺찫ꚕ㕔ე탿၏謌杒께岎\uea8c忥즺", (char) (22498 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), "\ud7a4葄\ue1a5癗", "\u0000\u0000\u0000\u0000", objArr);
        edit.putBoolean(((String) objArr[0]).intern(), z).apply();
        int i3 = l + 93;
        p = i3 % 128;
        int i4 = i3 % 2;
    }

    public static boolean e(Context context) {
        int i = p + 69;
        l = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        q("<\ueb9f휚쌞껨骽虵爷嶍䥅㕒⃦ಠ\uf865\ue43d쾏뭅꜍鋬纬橳嘵䆉ⵉᤀӤ\uf0be\udc6e적뎒齀謒盈抨买㨀▖ᅛﴕ\ue8dd풧쁧갹鞉荍演嫋", 60343 - View.combineMeasuredStates(0, 0), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        r((-1518058281) - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "왊雽⽐媺찫ꚕ㕔ე탿၏謌杒께岎\uea8c忥즺", (char) (22497 - View.MeasureSpec.makeMeasureSpec(0, 0)), "\ud7a4葄\ue1a5癗", "\u0000\u0000\u0000\u0000", objArr2);
        boolean z = sharedPreferences.getBoolean(((String) objArr2[0]).intern(), false);
        int i3 = l + 45;
        p = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return z;
            default:
                int i4 = 44 / 0;
                return z;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 550
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.a.q(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void r(int i, String str, char c, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] charArray;
        switch (str3 == null) {
            case true:
                cArr = str3;
                break;
            default:
                cArr = str3.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        int i2 = 2;
        if (str2 != null) {
            int i3 = $11 + 31;
            $10 = i3 % 128;
            int i4 = i3 % 2;
            cArr2 = str2.toCharArray();
        } else {
            cArr2 = str2;
        }
        char[] cArr4 = cArr2;
        switch (str != null ? 'D' : (char) 31) {
            case 'D':
                int i5 = $11 + 67;
                $10 = i5 % 128;
                int i6 = i5 % 2;
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i7 = $11 + 11;
            $10 = i7 % 128;
            int i8 = i7 % i2;
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(10 - TextUtils.getOffsetBefore("", 0), (char) (20954 - Gravity.getAbsoluteGravity(0, 0)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 344);
                    byte b = (byte) 0;
                    Object[] objArr3 = new Object[1];
                    s(b, (byte) (b | 15), b, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(View.resolveSizeAndState(0, 0, 0) + 10, (char) ExpandableListView.getPackedPositionGroup(0L), 207 - (ViewConfiguration.getPressedStateDuration() >> 16));
                        byte b2 = (byte) 0;
                        Object[] objArr5 = new Object[1];
                        s(b2, (byte) (b2 | 13), b2, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr5[oVar.e % 4] * 32718), Integer.valueOf(cArr6[intValue])};
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(11 - Drawable.resolveOpacity(0, 0), (char) Color.red(0), Process.getGidForName("") + 282);
                            byte b3 = (byte) 0;
                            Object[] objArr7 = new Object[1];
                            s(b3, (byte) (b3 | 11), b3, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(Gravity.getAbsoluteGravity(0, 0) + 19, (char) (14687 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), Process.getGidForName("") + Opcodes.LREM);
                                byte b4 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                s(b4, (byte) (b4 | 8), b4, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r3[oVar.e]) ^ (m ^ 6565854932352255525L)) ^ ((int) (k ^ 6565854932352255525L))) ^ ((char) (n ^ 6565854932352255525L)));
                            oVar.e++;
                            i2 = 2;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str4 = new String(cArr7);
        int i9 = $10 + Opcodes.LSUB;
        $11 = i9 % 128;
        switch (i9 % 2 == 0) {
            case true:
                Object obj5 = null;
                obj5.hashCode();
                throw null;
            default:
                objArr[0] = str4;
                return;
        }
    }
}
