package fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui;

import android.os.Bundle;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ImageView;
import androidx.activity.OnBackPressedCallback;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.material.button.MaterialButton;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity;
import fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels.ManageGooglePayMockViewModel;
import fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels.ManageSamsungPayMockViewModel;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\googlepay\ui\SetAsDefaultFragment.smali */
public class SetAsDefaultFragment extends Fragment {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10 = 0;
    private static int $11 = 0;
    private static final String WALLET_TYPE = "WALLET_TYPE";
    private static long a;
    private static int b;
    private static int d;
    private MaterialButton cancelButton;
    private MaterialButton confirmButton;
    private ImageView logo;
    private String mWalletType;
    private ManageDeviceWalletViewModel manageDeviceWalletMockViewModel;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        a = 356974678538678224L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void e(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 71 - r8
            byte[] r0 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.$$a
            int r7 = r7 * 4
            int r7 = r7 + 1
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r8
            r4 = r2
            r7 = r6
            goto L30
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r3 = -r3
            int r6 = r6 + r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.e(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{108, 119, -51, 110};
        $$b = 239;
    }

    public static SetAsDefaultFragment newInstance(String str) {
        SetAsDefaultFragment setAsDefaultFragment = new SetAsDefaultFragment();
        Bundle bundle = new Bundle();
        bundle.putString(WALLET_TYPE, str);
        setAsDefaultFragment.setArguments(bundle);
        int i = d + 39;
        b = i % 128;
        switch (i % 2 == 0 ? 'S' : '0') {
            case '0':
                return setAsDefaultFragment;
            default:
                int i2 = 28 / 0;
                return setAsDefaultFragment;
        }
    }

    @Override // androidx.fragment.app.Fragment
    public void onCreate(Bundle bundle) {
        int i = d + 71;
        b = i % 128;
        int i2 = i % 2;
        super.onCreate(bundle);
        switch (getArguments() != null ? 'M' : (char) 3) {
            case 3:
                break;
            default:
                String string = getArguments().getString(WALLET_TYPE);
                this.mWalletType = string;
                Object[] objArr = new Object[1];
                c("\uf2a8ვ숝\uf4aa\uf2ef㾕鱌秀付ﯛ\ud818붓誑뜋", 1 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr);
                if (string.equals(((String) objArr[0]).intern())) {
                    this.manageDeviceWalletMockViewModel = (ManageDeviceWalletViewModel) new ViewModelProvider(this).get(ManageGooglePayMockViewModel.class);
                }
                String str = this.mWalletType;
                Object[] objArr2 = new Object[1];
                c("聯껕ᗘ\udbef龍膛䮋嚑䗾䗐࿅鋙膿ओ쌗", 1 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr2);
                if (str.equals(((String) objArr2[0]).intern())) {
                    this.manageDeviceWalletMockViewModel = (ManageDeviceWalletViewModel) new ViewModelProvider(this).get(ManageSamsungPayMockViewModel.class);
                    break;
                }
                break;
        }
        int i3 = d + Opcodes.DNEG;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:19:0x0092 A[FALL_THROUGH, RETURN] */
    @Override // androidx.fragment.app.Fragment
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public android.view.View onCreateView(android.view.LayoutInflater r6, android.view.ViewGroup r7, android.os.Bundle r8) {
        /*
            r5 = this;
            int r8 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b
            int r8 = r8 + 83
            int r0 = r8 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d = r0
            int r8 = r8 % 2
            int r8 = fr.antelop.sdk.R.layout.fragment_set_as_default
            r0 = 0
            android.view.View r6 = r6.inflate(r8, r7, r0)
            int r7 = fr.antelop.sdk.R.id.set_as_default_confirm
            android.view.View r7 = r6.findViewById(r7)
            com.google.android.material.button.MaterialButton r7 = (com.google.android.material.button.MaterialButton) r7
            r5.confirmButton = r7
            int r7 = fr.antelop.sdk.R.id.set_as_default_cancel
            android.view.View r7 = r6.findViewById(r7)
            com.google.android.material.button.MaterialButton r7 = (com.google.android.material.button.MaterialButton) r7
            r5.cancelButton = r7
            int r7 = fr.antelop.sdk.R.id.set_as_default_logo
            android.view.View r7 = r6.findViewById(r7)
            android.widget.ImageView r7 = (android.widget.ImageView) r7
            r5.logo = r7
            java.lang.String r7 = r5.mWalletType
            int r8 = android.view.ViewConfiguration.getPressedStateDuration()
            int r8 = r8 >> 16
            r1 = 1
            int r8 = r8 + r1
            java.lang.Object[] r2 = new java.lang.Object[r1]
            java.lang.String r3 = "\uf2a8ვ숝\uf4aa\uf2ef㾕鱌秀付ﯛ\ud818붓誑뜋"
            c(r3, r8, r2)
            r8 = r2[r0]
            java.lang.String r8 = (java.lang.String) r8
            java.lang.String r8 = r8.intern()
            boolean r7 = r7.equals(r8)
            if (r7 != 0) goto L92
            java.lang.String r7 = r5.mWalletType
            int r8 = android.view.ViewConfiguration.getWindowTouchSlop()
            r2 = 8
            int r8 = r8 >> r2
            int r8 = 1 - r8
            java.lang.Object[] r3 = new java.lang.Object[r1]
            java.lang.String r4 = "聯껕ᗘ\udbef龍膛䮋嚑䗾䗐࿅鋙膿ओ쌗"
            c(r4, r8, r3)
            r8 = r3[r0]
            java.lang.String r8 = (java.lang.String) r8
            java.lang.String r8 = r8.intern()
            boolean r7 = r7.equals(r8)
            if (r7 == 0) goto L71
            r7 = 45
            goto L72
        L71:
            r7 = 4
        L72:
            switch(r7) {
                case 4: goto L92;
                default: goto L75;
            }
        L75:
            int r7 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d
            int r7 = r7 + 75
            int r8 = r7 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b = r8
            int r7 = r7 % 2
            if (r7 != 0) goto L83
            r1 = r0
            goto L84
        L83:
        L84:
            android.widget.ImageView r7 = r5.logo
            int r8 = fr.antelop.sdk.R.drawable.antelop_samsung_pay_logo
            r7.setImageResource(r8)
            switch(r1) {
                case 1: goto L8f;
                default: goto L8e;
            }
        L8e:
            int r2 = r2 / r0
        L8f:
            goto L92
        L90:
            r6 = move-exception
            throw r6
        L92:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* renamed from: lambda$onViewCreated$0$fr-antelop-sdk-digitalcard-devicewallet-googlepay-ui-SetAsDefaultFragment, reason: not valid java name */
    /* synthetic */ void m230x744057c0(java.lang.String r7, java.util.List r8) {
        /*
            r6 = this;
            int r0 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d
            int r0 = r0 + 89
            int r1 = r0 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b = r1
            int r0 = r0 % 2
            java.util.Iterator r8 = r8.iterator()
        Le:
            boolean r0 = r8.hasNext()
            if (r0 == 0) goto L17
            r0 = 50
            goto L19
        L17:
            r0 = 84
        L19:
            switch(r0) {
                case 84: goto L29;
                default: goto L1c;
            }
        L1c:
            int r0 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b
            int r0 = r0 + 109
            int r1 = r0 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L2a
            goto L2a
        L29:
            return
        L2a:
            java.lang.Object r0 = r8.next()
            o.ep.e r0 = (o.ep.e) r0
            java.lang.String r1 = r0.d()
            boolean r1 = r1.equals(r7)
            if (r1 == 0) goto L68
            android.content.Intent r1 = new android.content.Intent
            r1.<init>()
            long r2 = android.os.Process.getElapsedCpuTime()
            r4 = 0
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            r3 = 1
            java.lang.Object[] r3 = new java.lang.Object[r3]
            java.lang.String r4 = "ꐦ灳\udb3e酾ꑂ弙蕆ᰲᡯ魔섐𢀪힛ස钾郼Ꮿ䦅僻"
            c(r4, r2, r3)
            r2 = 0
            r2 = r3[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r0 = r0.c()
            android.content.Intent r0 = r1.putExtra(r2, r0)
            androidx.fragment.app.FragmentActivity r1 = r6.requireActivity()
            r2 = -1
            r1.setResult(r2, r0)
        L68:
            goto Le
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.m230x744057c0(java.lang.String, java.util.List):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
    
        if (r3 != null) goto L19;
     */
    /* renamed from: lambda$onViewCreated$1$fr-antelop-sdk-digitalcard-devicewallet-googlepay-ui-SetAsDefaultFragment, reason: not valid java name */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    /* synthetic */ void m231xa7ee8281(final java.lang.String r3, android.view.View r4) {
        /*
            r2 = this;
            int r4 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d
            int r0 = r4 + 19
            int r1 = r0 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 69
            goto L11
        Lf:
            r0 = 20
        L11:
            r1 = 0
            switch(r0) {
                case 20: goto L16;
                default: goto L15;
            }
        L15:
            goto L19
        L16:
            if (r3 == 0) goto L25
        L18:
            goto L2d
        L19:
            r0 = 72
            int r0 = r0 / r1
            if (r3 == 0) goto L20
            r0 = r1
            goto L21
        L20:
            r0 = 1
        L21:
            switch(r0) {
                case 1: goto L25;
                default: goto L24;
            }
        L24:
            goto L18
        L25:
            androidx.fragment.app.FragmentActivity r3 = r2.requireActivity()
            r3.setResult(r1)
            goto L47
        L2d:
            int r4 = r4 + 65
            int r0 = r4 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b = r0
            int r4 = r4 % 2
            fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel r4 = r2.manageDeviceWalletMockViewModel
            androidx.lifecycle.LiveData r4 = r4.getMockedTokens()
            androidx.lifecycle.LifecycleOwner r0 = r2.getViewLifecycleOwner()
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment$$ExternalSyntheticLambda0 r1 = new fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment$$ExternalSyntheticLambda0
            r1.<init>()
            r4.observe(r0, r1)
        L47:
            androidx.fragment.app.FragmentActivity r3 = r2.requireActivity()
            r3.finish()
            int r3 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.b
            int r3 = r3 + 61
            int r4 = r3 % 128
            fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.d = r4
            int r3 = r3 % 2
            return
        L59:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.m231xa7ee8281(java.lang.String, android.view.View):void");
    }

    /* renamed from: lambda$onViewCreated$2$fr-antelop-sdk-digitalcard-devicewallet-googlepay-ui-SetAsDefaultFragment, reason: not valid java name */
    /* synthetic */ void m232xdb9cad42(View view) {
        int i = b + 61;
        d = i % 128;
        int i2 = i % 2;
        requireActivity().setResult(0);
        requireActivity().finish();
        int i3 = b + 55;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // androidx.fragment.app.Fragment
    public void onViewCreated(View view, Bundle bundle) {
        super.onViewCreated(view, bundle);
        final String stringExtra = requireActivity().getIntent().getStringExtra(DeviceWalletMockActivity.LAST_DIGITS_BUNDLE_KEY);
        this.confirmButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                SetAsDefaultFragment.this.m231xa7ee8281(stringExtra, view2);
            }
        });
        this.cancelButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment$$ExternalSyntheticLambda2
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                SetAsDefaultFragment.this.m232xdb9cad42(view2);
            }
        });
        requireActivity().getOnBackPressedDispatcher().addCallback(getViewLifecycleOwner(), new OnBackPressedCallback(true) { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.1
            @Override // androidx.activity.OnBackPressedCallback
            public void handleOnBackPressed() {
                SetAsDefaultFragment.this.requireActivity().setResult(0);
                SetAsDefaultFragment.this.requireActivity().finish();
            }
        });
        int i = d + 85;
        b = i % 128;
        int i2 = i % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void c(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 360
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment.c(java.lang.String, int, java.lang.Object[]):void");
    }
}
