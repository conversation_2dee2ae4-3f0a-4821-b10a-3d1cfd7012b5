package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\Utils.smali */
public class Utils implements Serializable {
    private static final long serialVersionUID = 6444143955262330363L;

    public static long byte2long(byte[] bArr) {
        return (bArr[0] & 255) | ((bArr[1] & 255) << 8) | ((bArr[2] & 255) << 16) | ((255 & bArr[3]) << 24);
    }

    public static void long2byte(byte[] bArr, long j) {
        bArr[0] = (byte) (j & 255);
        bArr[1] = (byte) ((j >>> 8) & 255);
        bArr[2] = (byte) ((j >>> 16) & 255);
        bArr[3] = (byte) ((j >>> 24) & 255);
    }
}
