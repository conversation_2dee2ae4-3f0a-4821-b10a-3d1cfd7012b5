package o.bs;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.SystemClock;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;
import com.google.android.gms.security.ProviderInstaller;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\b.smali */
public final class b implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int c;
    private static char d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        c();
        View.resolveSize(0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        int i = c + 99;
        e = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        a = new char[]{30568, 30591, 30586, 29853, 29844, 30583, 29840, 30585, 30587, 30566, 30561, 30584, 29841, 30559, 29843, 30567, 30571, 30563, 30498, 29845, 29846, 30556, 30517, 30570, 30511, 30572, 29852, 30589, 30528, 30582, 30588, 30560, 30574, 29842, 30536, 29847};
        d = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r0 = o.bs.b.$$a
            int r9 = r9 + 69
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r9 = r8
            r3 = r9
            r5 = r2
            r8 = r7
            goto L2e
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            int r7 = r7 + 1
            if (r5 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2e:
            int r3 = -r3
            int r7 = r7 + r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.b.g(int, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{20, -38, -101, -62};
        $$b = 4;
    }

    @Override // o.bs.c
    public final i d(Context context) {
        o.ee.e.a();
        i b = b(o.ee.c.l(context));
        g.c();
        Object[] objArr = new Object[1];
        f(27 - View.MeasureSpec.getMode(0), "# \u001e\u0001\u0017\u001d\u000e\f#\u001a\u0016\u0012\u0019\t\u0007\u001b\u0012#\u0018\"\u0016\u0012\u0019\t\u0007\u001b㘯", (byte) ((-16777168) - Color.rgb(0, 0, 0)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 11, "\u0005\u0012\t\u0014\u000e\u0002\u000e\b\u0000\u001e\u0012\u001c", (byte) (16 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(b).toString());
        int i = e + 85;
        c = i % 128;
        switch (i % 2 != 0) {
            case true:
                return b;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.bs.c
    public final void c(Context context) {
        int i = c + 29;
        e = i % 128;
        try {
            switch (i % 2 == 0) {
                case true:
                    ProviderInstaller.installIfNeeded(context);
                    break;
                default:
                    ProviderInstaller.installIfNeeded(context);
                    int i2 = 40 / 0;
                    break;
            }
        } catch (GooglePlayServicesNotAvailableException | GooglePlayServicesRepairableException e2) {
            g.c();
            Object[] objArr = new Object[1];
            f(28 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "# \u001e\u0001\u0017\u001d\u000e\f#\u001a\u0016\u0012\u0019\t\u0007\u001b\u0012#\u0018\"\u0016\u0012\u0019\t\u0007\u001b㘯", (byte) (48 - (ViewConfiguration.getScrollBarSize() >> 8)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(58 - View.getDefaultSize(0, 0), "\u0003\u0019\u0013\u0005!\u001a\u0012\u001d\u0018\u001e\u001d\u000b\u001d\u0013\u0002\u0007\u0007!\u0006\u001c\t\u0011\u0016\u000b\u001a\u0000\u0004\r\u0002\u000e\n\u000b\u0006\u001e\u0011\u0016\b\n\u001d\u0013\u001e\u0000\u0013\u001d\u0003\u001a\n\t\u0018\u0019\u0003\u0019\u0001\r\n\u000f\u0015\u001d", (byte) (View.MeasureSpec.makeMeasureSpec(0, 0) + 54), objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e2);
        }
    }

    @Override // o.bs.c
    public final String b() {
        int i = c + 25;
        e = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f((ViewConfiguration.getWindowTouchSlop() >> 8) + 27, "# \u001e\u0001\u0017\u001d\u000e\f#\u001a\u0016\u0012\u0019\t\u0007\u001b\u0012#\u0018\"\u0016\u0012\u0019\t\u0007\u001b㘯", (byte) (ImageFormat.getBitsPerPixel(0) + 49), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = c + 65;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? '=' : Typography.quote) {
            case '\"':
                return intern;
            default:
                int i4 = 90 / 0;
                return intern;
        }
    }

    private static i b(int i) {
        int i2 = e + Opcodes.DREM;
        c = i2 % 128;
        int i3 = i2 % 2;
        switch (i) {
            case 0:
                return i.a;
            case 1:
                return i.h;
            case 2:
                return i.b;
            case 3:
                return i.d;
            case 9:
                i iVar = i.e;
                int i4 = c + 29;
                e = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        return iVar;
                    default:
                        int i5 = 36 / 0;
                        return iVar;
                }
            case 18:
                return i.c;
            default:
                return i.h;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r28, java.lang.String r29, byte r30, java.lang.Object[] r31) {
        /*
            Method dump skipped, instructions count: 1058
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.b.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
