package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.security.keystore.KeyGenParameterSpec;
import android.util.Base64;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.ItemTouchHelper;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.InvalidAlgorithmParameterException;
import java.security.Key;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\e.smali */
public abstract class e {
    public static final byte[] a = a(new int[]{Opcodes.FCMPG, 57, 215, Opcodes.FCMPG, 4, 204, 48, 32, 52, 252, 30, Opcodes.IF_ICMPNE, 0, 215, 220, Opcodes.DNEG});
    public static final byte[] b = a(new int[]{21, 104, 252, 30, Opcodes.I2F, 99, Opcodes.IF_ICMPLT, 5, 74, Opcodes.LUSHR, 84, 109, 37, Opcodes.ISHL, 215, Opcodes.LSHL});
    public static final byte[] c = a(new int[]{5, Opcodes.IINC, 236, 99, Opcodes.ARETURN, Opcodes.FCMPL, 251, 238, 127, 114, 21, 33, Opcodes.ANEWARRAY, 192, Opcodes.IFGT, 249});
    public static final byte[] d = a(new int[]{65, BERTags.FLAGS, 53, Opcodes.LSHR, 77, 8, 4, 213, Opcodes.D2I, 26, Opcodes.LOR, 127, 50, Opcodes.LSUB, Opcodes.MONITORENTER, Opcodes.DCMPG});
    public static final byte[] e = a(new int[]{Opcodes.LMUL, 2, 50, 77, 235, Opcodes.LMUL, 245, 88, 5, Opcodes.INEG, 43, 99, Opcodes.IF_ICMPGE, 48, Opcodes.RETURN, 11});
    public static final byte[] f = a(new int[]{Opcodes.INVOKEDYNAMIC, 65, 41, 223, Opcodes.MONITORENTER, Opcodes.D2I, 238, Opcodes.RETURN, 228, Opcodes.IF_ACMPEQ, Opcodes.ISHL, 235, 128, Opcodes.L2D, Opcodes.GETFIELD, 213});
    public static final byte[] g = a(new int[]{Opcodes.L2I, 16, Opcodes.PUTFIELD, Opcodes.DCMPG, 229, Opcodes.ARRAYLENGTH, 84, 91, Opcodes.PUTFIELD, 209, 35, Opcodes.DNEG, 245, 31, 207, 74});
    public static final byte[] h = a(new int[]{200, 245, Opcodes.IF_ICMPGE, 240, 241, 94, Opcodes.I2S, 46, Opcodes.FRETURN, 69, ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION, 188, 20, Opcodes.NEW, 210, 87});
    public static final String i = w.a();
    public static final String j;
    public static final String k;

    static {
        byte[] bArr = new byte[10];
        int[] iArr = {9, 205, 108, 204, 234, Opcodes.RET, 232, 39, Opcodes.GOTO, 231};
        for (int i2 = 0; i2 < 10; i2++) {
            int i3 = iArr[i2];
            bArr[i2] = (byte) (((((i3 & 255) >> 5) | (i3 << 3)) & 255) - i2);
        }
        j = new String(bArr);
        k = "OneSpan_SecureStorage_SE_";
    }

    public static final String a(byte[] data, byte[] secureEncryptionKey) {
        byte[] hmacKey;
        Intrinsics.checkNotNullParameter(data, "data");
        Intrinsics.checkNotNullParameter(secureEncryptionKey, "secureEncryptionKey");
        byte[] bArr = new byte[64];
        byte[] bArr2 = a;
        System.arraycopy(bArr2, 0, bArr, 0, bArr2.length);
        byte[] bArr3 = b;
        System.arraycopy(bArr3, 0, bArr, 16, bArr3.length);
        byte[] bArr4 = c;
        System.arraycopy(bArr4, 0, bArr, 32, bArr4.length);
        byte[] bArr5 = d;
        System.arraycopy(bArr5, 0, bArr, 48, bArr5.length);
        byte[] bArr6 = null;
        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byteArrayOutputStream.write(bArr);
            byteArrayOutputStream.write(secureEncryptionKey);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            try {
                UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, byteArray);
                if (hash.getReturnCode() != 0) {
                    v vVar = SecureStorageSDKException.Companion;
                    int returnCode = hash.getReturnCode();
                    vVar.getClass();
                    v.a(returnCode);
                    throw null;
                }
                hmacKey = hash.getOutputData();
                try {
                    Intrinsics.checkNotNullParameter(hmacKey, "hmacKey");
                    UtilitiesSDKCryptoResponse hmac = UtilitiesSDK.hmac((byte) 3, data, hmacKey);
                    if (hmac.getReturnCode() == 0) {
                        String a2 = y.a(hmac.getOutputData());
                        y.b(bArr);
                        y.b(hmacKey);
                        y.b(byteArray);
                        return a2;
                    }
                    v vVar2 = SecureStorageSDKException.Companion;
                    int returnCode2 = hmac.getReturnCode();
                    vVar2.getClass();
                    v.a(returnCode2);
                    throw null;
                } catch (Throwable th) {
                    th = th;
                    bArr6 = byteArray;
                    y.b(bArr);
                    y.b(hmacKey);
                    y.b(bArr6);
                    throw th;
                }
            } catch (Throwable th2) {
                th = th2;
                hmacKey = null;
            }
        } catch (Throwable th3) {
            th = th3;
            hmacKey = null;
        }
    }

    public static SecretKey b(String keyAlias) {
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        try {
            KeyStore keyStore = KeyStore.getInstance("AndroidKeyStore");
            keyStore.load(null);
            Key key = keyStore.getKey(keyAlias, null);
            if (key instanceof SecretKey) {
                return (SecretKey) key;
            }
            return null;
        } catch (SecureStorageSDKException e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        } catch (IOException e3) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_IO_KEY_STORE, e3);
        } catch (KeyStoreException e4) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_ANDROID_KEY_STORE, e4);
        } catch (NoSuchAlgorithmException e5) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_NO_SUCH_ALGORITHM_KEY_STORE, e5);
        } catch (UnrecoverableKeyException e6) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNRECOVERABLE_KEY, e6);
        } catch (CertificateException e7) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_LOADING_ANDROID_KEY_STORE, e7);
        }
    }

    public static final SecretKey c(String useKeyName) {
        Intrinsics.checkNotNullParameter(useKeyName, "useKeyName");
        try {
            KeyStore keyStore = KeyStore.getInstance(i);
            keyStore.load(null);
            Key key = keyStore.getKey(useKeyName, null);
            if (key != null) {
                return (SecretKey) key;
            }
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_KEY, null, 2, null);
        } catch (IOException e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_IO_KEY_STORE, e2);
        } catch (KeyStoreException e3) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_ANDROID_KEY_STORE, e3);
        } catch (NoSuchAlgorithmException e4) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_NO_SUCH_ALGORITHM_KEY_STORE, e4);
        } catch (UnrecoverableKeyException e5) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNRECOVERABLE_KEY, e5);
        } catch (CertificateException e6) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_LOADING_ANDROID_KEY_STORE, e6);
        }
    }

    public static final void a(String useKeyName) {
        Intrinsics.checkNotNullParameter(useKeyName, "useKeyName");
        try {
            KeyGenParameterSpec.Builder builder = new KeyGenParameterSpec.Builder(useKeyName, 4);
            builder.setKeySize(512);
            builder.setUserAuthenticationRequired(false);
            KeyGenParameterSpec build = builder.build();
            Intrinsics.checkNotNullExpressionValue(build, "keyProtectionBuilder.build()");
            KeyGenerator keyGenerator = KeyGenerator.getInstance("HmacSHA256", i);
            keyGenerator.init(build);
            keyGenerator.generateKey();
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        }
    }

    public static SecretKey a(String keyAlias, int i2, FragmentActivity context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("HmacSHA256", "AndroidKeyStore");
            KeyGenParameterSpec.Builder builder = new KeyGenParameterSpec.Builder(keyAlias, 4);
            if (context.getPackageManager().hasSystemFeature("android.hardware.strongbox_keystore")) {
                builder.setIsStrongBoxBacked(true);
            }
            builder.setUserAuthenticationRequired(true);
            builder.setUserAuthenticationParameters(i2, 3);
            keyGenerator.init(builder.build());
            SecretKey generateKey = keyGenerator.generateKey();
            Intrinsics.checkNotNullExpressionValue(generateKey, "keyGenerator.generateKey()");
            return generateKey;
        } catch (InvalidAlgorithmParameterException e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_INVALID_ALGORITHM_KEYSTORE, e2);
        } catch (NoSuchAlgorithmException e3) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_NO_SUCH_ALGORITHM_KEY_STORE, e3);
        } catch (NoSuchProviderException e4) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ERROR_NO_SUCH_PROVIDER_KEYSTORE, e4);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:4:0x002e, code lost:
    
        if (r4 == null) goto L6;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static final boolean a(java.lang.String r4, int r5, byte[] r6, boolean r7, byte[] r8, java.lang.String r9, java.lang.String r10) {
        /*
            java.lang.String r0 = "salt"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r6, r0)
            java.lang.String r1 = "derivedKey"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r8, r1)
            java.lang.String r2 = "useKeyName"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r9, r2)
            java.lang.String r3 = "packageName"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r10, r3)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r6, r0)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r8, r1)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r9, r2)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r10, r3)
            r10 = 0
            if (r4 == 0) goto L30
            java.nio.charset.Charset r0 = kotlin.text.Charsets.UTF_8
            byte[] r4 = r4.getBytes(r0)
            java.lang.String r0 = "this as java.lang.String).getBytes(charset)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r4, r0)
            if (r4 != 0) goto L32
        L30:
            byte[] r4 = new byte[r10]
        L32:
            r0 = 3
            r1 = 32
            com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse r5 = com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK.deriveKey(r0, r4, r6, r5, r1)
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.y.b(r4)
            int r4 = r5.getReturnCode()
            r6 = 0
            if (r4 != 0) goto Lb3
            byte[] r4 = r5.getOutputData()
            java.lang.System.arraycopy(r4, r10, r8, r10, r1)
            if (r7 == 0) goto Lb2
            byte[] r4 = new byte[r1]
            java.lang.String r5 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.i     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.security.KeyStore r7 = java.security.KeyStore.getInstance(r5)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            r7.load(r6)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            boolean r0 = r7.containsAlias(r9)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            if (r0 == 0) goto L99
            java.security.Key r7 = r7.getKey(r9, r6)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.lang.String r9 = "null cannot be cast to non-null type javax.crypto.SecretKey"
            kotlin.jvm.internal.Intrinsics.checkNotNull(r7, r9)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            javax.crypto.SecretKey r7 = (javax.crypto.SecretKey) r7     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.lang.String r9 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.j     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            javax.crypto.Mac r9 = javax.crypto.Mac.getInstance(r9)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            r9.init(r7)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            byte[] r9 = r9.doFinal(r8)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.lang.String r0 = "mac.doFinal(secureKey)"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r9, r0)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.lang.System.arraycopy(r9, r10, r4, r10, r1)     // Catch: java.lang.Exception -> L9f java.security.UnrecoverableKeyException -> La8
            java.lang.String r6 = r7.getAlgorithm()     // Catch: java.lang.Exception -> L96
            javax.crypto.SecretKeyFactory r5 = javax.crypto.SecretKeyFactory.getInstance(r6, r5)     // Catch: java.lang.Exception -> L96
            java.lang.Class<android.security.keystore.KeyInfo> r6 = android.security.keystore.KeyInfo.class
            java.security.spec.KeySpec r5 = r5.getKeySpec(r7, r6)     // Catch: java.lang.Exception -> L96
            java.lang.String r6 = "null cannot be cast to non-null type android.security.keystore.KeyInfo"
            kotlin.jvm.internal.Intrinsics.checkNotNull(r5, r6)     // Catch: java.lang.Exception -> L96
            android.security.keystore.KeyInfo r5 = (android.security.keystore.KeyInfo) r5     // Catch: java.lang.Exception -> L96
            r5.isInsideSecureHardware()     // Catch: java.lang.Exception -> L96
            goto L99
        L96:
            r5 = move-exception
            r5 = 1
            goto L9a
        L99:
            r5 = r10
        L9a:
            java.lang.System.arraycopy(r4, r10, r8, r10, r1)
            r10 = r5
            goto Lb2
        L9f:
            r4 = move-exception
            com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException r5 = new com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException
            r6 = -4300(0xffffffffffffef34, float:NaN)
            r5.<init>(r6, r4)
            throw r5
        La8:
            r4 = move-exception
            com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException r4 = new com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException
            r5 = -4317(0xffffffffffffef23, float:NaN)
            r7 = 2
            r4.<init>(r5, r6, r7, r6)
            throw r4
        Lb2:
            return r10
        Lb3:
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.v r4 = com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException.Companion
            int r5 = r5.getReturnCode()
            r4.getClass()
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.v.a(r5)
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.a(java.lang.String, int, byte[], boolean, byte[], java.lang.String, java.lang.String):boolean");
    }

    public static String a(byte[] salt, String prefix, String filename) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Intrinsics.checkNotNullParameter(filename, "filename");
        UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, salt);
        if (hash.getReturnCode() == 0) {
            StringBuilder append = new StringBuilder().append(prefix).append(filename).append('_');
            byte[] encode = Base64.encode(hash.getOutputData(), 0);
            Intrinsics.checkNotNullExpressionValue(encode, "encode(hash.outputData, Base64.DEFAULT)");
            return append.append(new String(encode, Charsets.UTF_8)).toString();
        }
        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
    }

    public static final byte[] a() {
        byte[] bArr = new byte[64];
        byte[] bArr2 = e;
        System.arraycopy(bArr2, 0, bArr, 0, bArr2.length);
        byte[] bArr3 = f;
        System.arraycopy(bArr3, 0, bArr, 16, bArr3.length);
        byte[] bArr4 = g;
        System.arraycopy(bArr4, 0, bArr, 32, bArr4.length);
        byte[] bArr5 = h;
        System.arraycopy(bArr5, 0, bArr, 48, bArr5.length);
        return bArr;
    }

    public static byte[] a(int[] iArr) {
        int length = iArr.length;
        byte[] bArr = new byte[length];
        for (int i2 = 0; i2 < length; i2++) {
            bArr[i2] = (byte) iArr[i2];
        }
        return bArr;
    }
}
