package bc.org.bouncycastle.crypto.digests;

import bc.org.bouncycastle.crypto.Digest;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\SHA256Digest.smali */
public class SHA256Digest extends a implements i7 {

    /* renamed from: o, reason: collision with root package name */
    static final int[] f5o = {1116352408, 1899447441, -1245643825, -373957723, 961987163, 1508970993, -1841331548, -1424204075, -670586216, 310598401, 607225278, 1426881987, 1925078388, -2132889090, -1680079193, -1046744716, -459576895, -272742522, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, -1740746414, -1473132947, -1341970488, -1084653625, -958395405, -710438585, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, -2117940946, -1838011259, -1564481375, -1474664885, -1035236496, -949202525, -778901479, -694614492, -200395387, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, -2067236844, -1933114872, -1866530822, -1538233109, -1090935817, -965641998};
    private int e;
    private int f;
    private int g;
    private int h;
    private int i;
    private int j;
    private int k;
    private int l;
    private int[] m;
    private int n;

    public SHA256Digest() {
        this(q1.ANY);
    }

    private static int a(int i) {
        return ((i << 10) | (i >>> 22)) ^ (((i >>> 2) | (i << 30)) ^ ((i >>> 13) | (i << 19)));
    }

    private static int a(int i, int i2, int i3) {
        return ((~i) & i3) ^ (i2 & i);
    }

    private void a(SHA256Digest sHA256Digest) {
        super.a((a) sHA256Digest);
        this.e = sHA256Digest.e;
        this.f = sHA256Digest.f;
        this.g = sHA256Digest.g;
        this.h = sHA256Digest.h;
        this.i = sHA256Digest.i;
        this.j = sHA256Digest.j;
        this.k = sHA256Digest.k;
        this.l = sHA256Digest.l;
        int[] iArr = sHA256Digest.m;
        System.arraycopy(iArr, 0, this.m, 0, iArr.length);
        this.n = sHA256Digest.n;
    }

    private static int b(int i) {
        return ((i << 7) | (i >>> 25)) ^ (((i >>> 6) | (i << 26)) ^ ((i >>> 11) | (i << 21)));
    }

    private static int b(int i, int i2, int i3) {
        return ((i ^ i2) & i3) | (i & i2);
    }

    private static int c(int i) {
        return (i >>> 3) ^ (((i >>> 7) | (i << 25)) ^ ((i >>> 18) | (i << 14)));
    }

    private static int d(int i) {
        return (i >>> 10) ^ (((i >>> 17) | (i << 15)) ^ ((i >>> 19) | (i << 13)));
    }

    public static i7 newInstance() {
        return new SHA256Digest();
    }

    protected p1 b() {
        return g.a(this, 256, this.a);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public m5 copy() {
        return new SHA256Digest(this);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i) {
        finish();
        j6.a(this.e, bArr, i);
        j6.a(this.f, bArr, i + 4);
        j6.a(this.g, bArr, i + 8);
        j6.a(this.h, bArr, i + 12);
        j6.a(this.i, bArr, i + 16);
        j6.a(this.j, bArr, i + 20);
        j6.a(this.k, bArr, i + 24);
        j6.a(this.l, bArr, i + 28);
        reset();
        return 32;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public String getAlgorithmName() {
        return "SHA-256";
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int getDigestSize() {
        return 32;
    }

    public byte[] getEncodedState() {
        int i = (this.n * 4) + 52 + 1;
        byte[] bArr = new byte[i];
        super.a(bArr);
        j6.a(this.e, bArr, 16);
        j6.a(this.f, bArr, 20);
        j6.a(this.g, bArr, 24);
        j6.a(this.h, bArr, 28);
        j6.a(this.i, bArr, 32);
        j6.a(this.j, bArr, 36);
        j6.a(this.k, bArr, 40);
        j6.a(this.l, bArr, 44);
        j6.a(this.n, bArr, 48);
        for (int i2 = 0; i2 != this.n; i2++) {
            j6.a(this.m[i2], bArr, (i2 * 4) + 52);
        }
        bArr[i - 1] = (byte) this.a.ordinal();
        return bArr;
    }

    @Override // bc.org.bouncycastle.crypto.digests.a, bc.org.bouncycastle.crypto.Digest
    public void reset() {
        super.reset();
        this.e = 1779033703;
        this.f = -1150833019;
        this.g = 1013904242;
        this.h = -1521486534;
        this.i = 1359893119;
        this.j = -1694144372;
        this.k = 528734635;
        this.l = 1541459225;
        this.n = 0;
        int i = 0;
        while (true) {
            int[] iArr = this.m;
            if (i == iArr.length) {
                return;
            }
            iArr[i] = 0;
            i++;
        }
    }

    public SHA256Digest(q1 q1Var) {
        super(q1Var);
        this.m = new int[64];
        t1.a(b());
        reset();
    }

    public static i7 newInstance(q1 q1Var) {
        return new SHA256Digest(q1Var);
    }

    public static i7 newInstance(Digest digest) {
        if (digest instanceof SHA256Digest) {
            return new SHA256Digest((SHA256Digest) digest);
        }
        throw new IllegalArgumentException("receiver digest not available for input type " + (digest != null ? digest.getClass().getName() : "null"));
    }

    public static i7 newInstance(byte[] bArr) {
        return new SHA256Digest(bArr);
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(byte[] bArr, int i) {
        this.m[this.n] = j6.a(bArr, i);
        int i2 = this.n + 1;
        this.n = i2;
        if (i2 == 16) {
            a();
        }
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(long j) {
        if (this.n > 14) {
            a();
        }
        int[] iArr = this.m;
        iArr[14] = (int) (j >>> 32);
        iArr[15] = (int) (j & (-1));
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public void reset(m5 m5Var) {
        a((SHA256Digest) m5Var);
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a() {
        for (int i = 16; i <= 63; i++) {
            int[] iArr = this.m;
            int d = d(iArr[i - 2]);
            int[] iArr2 = this.m;
            iArr[i] = d + iArr2[i - 7] + c(iArr2[i - 15]) + this.m[i - 16];
        }
        int i2 = this.e;
        int i3 = this.f;
        int i4 = this.g;
        int i5 = this.h;
        int i6 = this.i;
        int i7 = this.j;
        int i8 = this.k;
        int i9 = this.l;
        int i10 = 0;
        for (int i11 = 0; i11 < 8; i11++) {
            int b = b(i6) + a(i6, i7, i8);
            int[] iArr3 = f5o;
            int i12 = i9 + b + iArr3[i10] + this.m[i10];
            int i13 = i5 + i12;
            int a = i12 + a(i2) + b(i2, i3, i4);
            int i14 = i10 + 1;
            int b2 = i8 + b(i13) + a(i13, i6, i7) + iArr3[i14] + this.m[i14];
            int i15 = i4 + b2;
            int a2 = b2 + a(a) + b(a, i2, i3);
            int i16 = i14 + 1;
            int b3 = i7 + b(i15) + a(i15, i13, i6) + iArr3[i16] + this.m[i16];
            int i17 = i3 + b3;
            int a3 = b3 + a(a2) + b(a2, a, i2);
            int i18 = i16 + 1;
            int b4 = i6 + b(i17) + a(i17, i15, i13) + iArr3[i18] + this.m[i18];
            int i19 = i2 + b4;
            int a4 = b4 + a(a3) + b(a3, a2, a);
            int i20 = i18 + 1;
            int b5 = i13 + b(i19) + a(i19, i17, i15) + iArr3[i20] + this.m[i20];
            i9 = a + b5;
            i5 = b5 + a(a4) + b(a4, a3, a2);
            int i21 = i20 + 1;
            int b6 = i15 + b(i9) + a(i9, i19, i17) + iArr3[i21] + this.m[i21];
            i8 = a2 + b6;
            i4 = b6 + a(i5) + b(i5, a4, a3);
            int i22 = i21 + 1;
            int b7 = i17 + b(i8) + a(i8, i9, i19) + iArr3[i22] + this.m[i22];
            i7 = a3 + b7;
            i3 = b7 + a(i4) + b(i4, i5, a4);
            int i23 = i22 + 1;
            int b8 = i19 + b(i7) + a(i7, i8, i9) + iArr3[i23] + this.m[i23];
            i6 = a4 + b8;
            i2 = b8 + a(i3) + b(i3, i4, i5);
            i10 = i23 + 1;
        }
        this.e += i2;
        this.f += i3;
        this.g += i4;
        this.h += i5;
        this.i += i6;
        this.j += i7;
        this.k += i8;
        this.l += i9;
        this.n = 0;
        for (int i24 = 0; i24 < 16; i24++) {
            this.m[i24] = 0;
        }
    }

    public SHA256Digest(SHA256Digest sHA256Digest) {
        super(sHA256Digest);
        this.m = new int[64];
        a(sHA256Digest);
    }

    public SHA256Digest(byte[] bArr) {
        super(bArr);
        this.m = new int[64];
        this.e = j6.a(bArr, 16);
        this.f = j6.a(bArr, 20);
        this.g = j6.a(bArr, 24);
        this.h = j6.a(bArr, 28);
        this.i = j6.a(bArr, 32);
        this.j = j6.a(bArr, 36);
        this.k = j6.a(bArr, 40);
        this.l = j6.a(bArr, 44);
        this.n = j6.a(bArr, 48);
        for (int i = 0; i != this.n; i++) {
            this.m[i] = j6.a(bArr, (i * 4) + 52);
        }
    }
}
