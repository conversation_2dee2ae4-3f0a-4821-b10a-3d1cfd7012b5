package o.ey;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.ee.g;
import o.fc.d;
import o.fj.c;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\e.smali */
public abstract class e<T extends o.fc.d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long f;
    private static long g;
    private static int h;
    private static int i;
    private static char[] j;
    private c a;
    private boolean b;
    private final String c;
    private List<T> d;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        s();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        AndroidCharacter.getMirror('0');
        SystemClock.elapsedRealtime();
        ViewConfiguration.getGlobalActionKeyTimeout();
        ViewConfiguration.getPressedStateDuration();
        ViewConfiguration.getLongPressTimeout();
        KeyEvent.getDeadChar(0, 0);
        TextUtils.indexOf("", "", 0);
        ViewConfiguration.getKeyRepeatDelay();
        TextUtils.getTrimmedLength("");
        int i2 = i + 35;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? 'K' : 'T') {
            case 'K':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void A(short r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            int r7 = 105 - r7
            byte[] r0 = o.ey.e.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r7 = r9
            r3 = r1
            r4 = r2
            r9 = r8
            r1 = r0
            r0 = r10
            r10 = r7
            goto L35
        L17:
            r3 = r2
            r6 = r9
            r9 = r7
            r7 = r6
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            int r7 = r7 + 1
            r1[r3] = r5
            if (r4 != r8) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r8 = -r8
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.A(short, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{40, 24, -45, -26};
        $$b = 247;
    }

    static void s() {
        f = 437535866716506030L;
        char[] cArr = new char[520];
        ByteBuffer.wrap(",¦9\u001f\u0007üm\u008cz_@\u0010®é´±\u0081eïØõ\u009aÂ@(\u001d6ø\u001c¶iow\r]\u0098ªJ°\u0000\u009eËä½ñ|ß%%©2\u001c\u0018YfÄL\u009fY|§y\u008dÀ\u009b¹à\u0001ÎÕÔ\u0088!J\u000f0\u0015íc¨HfVß¼Ùm1x\u0088Fy,\u0017;Ò\u0001ªïkõ\"À×®H´\u000f\u0083Õi\u008awo]8(ó6þ\u001cKë\u008eñ\u0093ßH¥+°®\u009e\u0097dnsÖY\u0082'_\r\u001d\u0018çæºÌ\u007fÚ1¡\u0088\u008f\u000e,\u00ad9\u0018\u0007Êm\u0082zH@#®ý´\u0084\u0081qïÁõ\u0090ÂS(\f6õ\u001c\u0092idw0]¢ª\\°\u0015\u009e\u0089äüñ9ß$%ÿ2\\\u0018YfàL\u0099Ya§5\u008dè\u009bªà\u0010ÎÍÔ\u0088!F\u000f?\u0015¹cäHzV\u0091¼\u0091\u0089@\u0097\u001aý±Ë°Ðu>:\u0004Ñ\u0011R\u007f\u0004EÐSñ¸j\u0086$ìýù\u0011Ç\u001c-Ù;\u0099\u0000xn+täB\u00ad\u0000Ã\u0015v+¤AìV&lM\u0082\u0093\u0098ì\u00ad\bÃ±Ùäî:\u0004j\u001a\u009a0ÓE [UqÛ\u0086>\u009c|²¤ÈÞÝ\u0005óK\t\u0082\u001e;4\\Jª`þu,\u008bR¡\u009b·\u0087Ì2â÷øê\r1#R9×Oîd\u0017z¯\u0090û¥&»dÑ\u009eçÃü\u0006\u0012H(ñ=wS*i´,\u00ad9\u0018\u0007Êm\u0082zH@#®ý´\u0082\u0081fïßõ\u008aÂT(\u00046ô\u001c½iNw;]µªP°\u0012\u009eÊä°ñkß%%ì2U\u00182fÄL\u0090YB§<\u008dõ\u009béà\\Î\u0099Ô\u0082!E\u000f4\u0015øc¯H`Vß¼\u009e\u0089\u0001\u0097\u0007ýþË\u00adÐ!> \u0004\u009f\u0011P\u007f\u0015EÀS°¸u\u0086(ìúùTÇ\u001d-\u0081;\u0082\u0000tn t¡Bó¯Qµ\u009c\u0083\u0092è\tö7Üë*®7d\u001d\u0091k\u009cpL^\u001f¤±²\u0098\u009fqå9ó\u009dØP&\u0002\fÈ\u001a¥gpM.[ç \u0011\u008e\\\u0094ÒâÉÏ=Õy#ñ\t¨\u0016\b|ÔJ\u0084WG½%\u008b×\u0091´þdÄÓÒ\u009c?S\u0005I\u0013«yùF$¬:M\u000eX»fi\f!\u001bë!\u0080Ï^Õ)àÏ\u008ek\u0094\t£çI¾W\u0012}W\b\u0082\u0016\u008f<$Ë÷ÑâÿK\u0085\u0002\u0090Ê¾\u008eDCSñy»\u0007v-#8ÝÆ\u0094ì\u0002úO\u0081¡D¬Q\u001doÇ\u0005©\u0012J(3ÆáÜ«él\u0087÷\u009d\u0091ªU@\u0017^¼tù\u0001,\u001f%5\u008cÂDØ\u0000öÍ\u008c¿\u0099u·8MíZSp\u001a\u000e\u008c$Þ1<,é9\u0019\u0007Øm\u0092z\t@?®ö´á\u0081oïÃõ\u009cÂR(\u00016±\u001c²idw0]\u0082AÕT%jä\u0000®\u00175-\fÃËÙÝìP\u0082õ\u0098µ¯tE'[Èq\u0081\u0004\u001d\u001a\u001e0¨Ç|Ý}óæ\u0089\u0088\u009cQ,é9\u0019\u0007Øm\u0092z\t@?®ö´µ\u0081)ïÔõ\u0097ÂN(\u001c6ö\u001c±i!w/]\u0083ª\\°\u0012\u009eÁäññrß$%ð2B\u0018Yf\u008cLÉY\u007f§,\u008dì\u009b«à\u0014ÎËÔÁ!\u0013\u000fq\u0015¼c¥H)VÅ¼\u0091\u0089S\u0097\fýâË±Ðn>%\u0004\u0095\u0011\u0019\u007f[E\u0089Sô¸}ñCäùÚ °M§²\u009dÝs\u001di@\\\u00852>(x\u001f©õÏë\u0019ÁM´¢ªÑ\u0080qw¶méC69\u001c,Ù\u0002\u008cø\u0005ï¬Åä» \u0091m\u0084\u009fzÕP\u0018FM=ó\u0013:\t,üþÒ\u009c,ª9\u0003\u0007Üm\u0085zL@?®í´¨\u0081hïÝõ\u008aKâ^W".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 520);
        j = cArr;
        g = -35411323151435407L;
    }

    public void a(Context context) {
        int i2 = i + 23;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    public abstract boolean a(Context context, T t);

    public abstract o.eg.b b(o.ek.b bVar) throws o.eg.d;

    public abstract void b();

    public abstract o.ct.b<T> c();

    public abstract a e();

    public abstract o.fc.e k();

    public e(String str, String str2, boolean z) {
        this.c = str;
        this.e = str2;
        this.b = z;
    }

    public final String d() {
        int i2 = i + 3;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c;
        }
    }

    public final String a() {
        int i2 = i + 5;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return this.e;
            default:
                throw null;
        }
    }

    public final void c(List<T> list) {
        int i2 = h + Opcodes.LMUL;
        i = i2 % 128;
        boolean z = i2 % 2 == 0;
        this.d = list;
        switch (z) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void b(c cVar) {
        int i2 = i + 89;
        int i3 = i2 % 128;
        h = i3;
        char c = i2 % 2 == 0 ? 'A' : '/';
        this.a = cVar;
        switch (c) {
            case '/':
                break;
            default:
                int i4 = 66 / 0;
                break;
        }
        int i5 = i3 + Opcodes.DSUB;
        i = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return;
            default:
                int i6 = 25 / 0;
                return;
        }
    }

    protected final List<T> f() {
        int i2 = h;
        int i3 = i2 + 53;
        i = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                List<T> list = this.d;
                int i4 = i2 + Opcodes.DMUL;
                i = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return list;
                }
            default:
                throw null;
        }
    }

    public final c j() {
        int i2 = i;
        int i3 = i2 + Opcodes.DSUB;
        h = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0) {
            case false:
                c cVar = this.a;
                int i4 = i2 + 69;
                h = i4 % 128;
                switch (i4 % 2 == 0 ? 'V' : (char) 6) {
                    case Opcodes.SASTORE /* 86 */:
                        throw null;
                    default:
                        return cVar;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final boolean g() {
        int i2 = i + 27;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final o.eg.b h() throws o.eg.d {
        int i2 = i + 89;
        h = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                o.eg.b e = e().e((a) this);
                int i3 = i + 109;
                h = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 25 : '6') {
                    case Opcodes.ISTORE /* 54 */:
                        return e;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                e().e((a) this);
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.lang.String e(o.ek.b r7) {
        /*
            r6 = this;
            java.util.LinkedHashMap r7 = r7.c()
            java.lang.String r0 = r6.e
            java.lang.Object r7 = r7.get(r0)
            o.eo.e r7 = (o.eo.e) r7
            r0 = 0
            if (r7 == 0) goto Lc9
            int r1 = o.ey.e.i
            int r1 = r1 + 45
            int r2 = r1 % 128
            o.ey.e.h = r2
            int r1 = r1 % 2
            java.util.LinkedHashMap r1 = r7.A()
            if (r1 != 0) goto L23
            r1 = 85
            goto L25
        L23:
            r1 = 83
        L25:
            switch(r1) {
                case 83: goto L2a;
                default: goto L28;
            }
        L28:
            goto Lc9
        L2a:
            java.util.LinkedHashMap r7 = r7.A()
            java.util.Collection r7 = r7.values()
            java.util.Iterator r7 = r7.iterator()
            r1 = r0
        L38:
            boolean r2 = r7.hasNext()
            r3 = 1
            r4 = 0
            if (r2 == 0) goto L42
            r2 = r3
            goto L43
        L42:
            r2 = r4
        L43:
            switch(r2) {
                case 0: goto L53;
                default: goto L46;
            }
        L46:
            int r2 = o.ey.e.h
            int r2 = r2 + 69
            int r5 = r2 % 128
            o.ey.e.i = r5
            int r2 = r2 % 2
            if (r2 == 0) goto L6f
            goto L6d
        L53:
            if (r1 == 0) goto L6c
            int r7 = o.ey.e.h
            int r7 = r7 + 107
            int r2 = r7 % 128
            o.ey.e.i = r2
            int r7 = r7 % 2
            if (r7 != 0) goto L66
            java.lang.String r7 = r1.l()
            return r7
        L66:
            r1.l()
            throw r0     // Catch: java.lang.Throwable -> L6a
        L6a:
            r7 = move-exception
            throw r7
        L6c:
            return r0
        L6d:
            r3 = r4
            goto L70
        L6f:
        L70:
            switch(r3) {
                case 0: goto L82;
                default: goto L73;
            }
        L73:
            java.lang.Object r2 = r7.next()
            o.eo.d r2 = (o.eo.d) r2
            java.util.List r2 = r2.d()
            java.util.Iterator r2 = r2.iterator()
            goto L96
        L82:
            java.lang.Object r2 = r7.next()
            o.eo.d r2 = (o.eo.d) r2
            java.util.List r2 = r2.d()
            java.util.Iterator r2 = r2.iterator()
            r3 = 64
            int r3 = r3 / r4
            goto L96
        L94:
            r7 = move-exception
            throw r7
        L96:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto Lc6
            int r3 = o.ey.e.h
            int r3 = r3 + 81
            int r4 = r3 % 128
            o.ey.e.i = r4
            int r3 = r3 % 2
            java.lang.Object r3 = r2.next()
            o.et.c r3 = (o.et.c) r3
            if (r3 == 0) goto Lc5
            java.lang.String r4 = r3.n()
            java.lang.String r5 = r6.c
            boolean r4 = r4.equals(r5)
            if (r4 == 0) goto Lbe
            r4 = 88
            goto Lc0
        Lbe:
            r4 = 95
        Lc0:
            switch(r4) {
                case 88: goto Lc4;
                default: goto Lc3;
            }
        Lc3:
            goto Lc5
        Lc4:
            r1 = r3
        Lc5:
            goto L96
        Lc6:
            goto L38
        Lc9:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.e(o.ek.b):java.lang.String");
    }

    public final void i() {
        Iterator<T> it;
        g.c();
        Object[] objArr = new Object[1];
        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", 1 - View.getDefaultSize(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        z((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), ViewConfiguration.getScrollBarSize() >> 8, 42 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.c).toString());
        this.b = true;
        List<T> list = this.d;
        switch (list != null ? (char) 16 : Typography.amp) {
            case 16:
                int i2 = i + Opcodes.LSHR;
                h = i2 % 128;
                if (i2 % 2 == 0) {
                    it = list.iterator();
                    int i3 = 36 / 0;
                } else {
                    it = list.iterator();
                }
                while (it.hasNext()) {
                    int i4 = i + 7;
                    h = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case true:
                            it.next().e(this.c, null);
                            break;
                        default:
                            it.next().e(this.c, null);
                            int i5 = 5 / 0;
                            break;
                    }
                }
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void m() {
        /*
            r10 = this;
            o.ee.g.c()
            r0 = 0
            r1 = 0
            float r2 = android.util.TypedValue.complexToFraction(r0, r1, r1)
            int r2 = (r2 > r1 ? 1 : (r2 == r1 ? 0 : -1))
            r3 = 1
            int r2 = 1 - r2
            java.lang.Object[] r4 = new java.lang.Object[r3]
            java.lang.String r5 = "\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐"
            w(r5, r2, r4)
            r2 = r4[r0]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            int r5 = android.view.Gravity.getAbsoluteGravity(r0, r0)
            int r5 = 16791 - r5
            char r5 = (char) r5
            float r6 = android.graphics.PointF.length(r1, r1)
            int r1 = (r6 > r1 ? 1 : (r6 == r1 ? 0 : -1))
            int r1 = r1 + 43
            long r6 = android.view.ViewConfiguration.getGlobalActionKeyTimeout()
            r8 = 0
            int r6 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            int r6 = r6 + 34
            java.lang.Object[] r7 = new java.lang.Object[r3]
            z(r5, r1, r6, r7)
            r1 = r7[r0]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r1 = r4.append(r1)
            java.lang.String r4 = r10.c
            java.lang.StringBuilder r1 = r1.append(r4)
            java.lang.String r1 = r1.toString()
            o.ee.g.d(r2, r1)
            java.util.List<T extends o.fc.d> r1 = r10.d
            if (r1 == 0) goto L61
            r2 = 80
            goto L63
        L61:
            r2 = 76
        L63:
            switch(r2) {
                case 80: goto L67;
                default: goto L66;
            }
        L66:
            goto La8
        L67:
            int r2 = o.ey.e.i
            int r2 = r2 + 117
            int r4 = r2 % 128
            o.ey.e.h = r4
            int r2 = r2 % 2
            java.util.Iterator r1 = r1.iterator()
        L75:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L7d
            r2 = r3
            goto L7e
        L7d:
            r2 = r0
        L7e:
            switch(r2) {
                case 1: goto L82;
                default: goto L81;
            }
        L81:
            goto L66
        L82:
            int r2 = o.ey.e.i
            int r2 = r2 + r3
            int r4 = r2 % 128
            o.ey.e.h = r4
            int r2 = r2 % 2
            r4 = 0
            if (r2 == 0) goto L9a
            java.lang.Object r2 = r1.next()
            o.fc.d r2 = (o.fc.d) r2
            java.lang.String r5 = r10.c
            r2.e(r5, r4)
            goto L75
        L9a:
            java.lang.Object r0 = r1.next()
            o.fc.d r0 = (o.fc.d) r0
            java.lang.String r1 = r10.c
            r0.e(r1, r4)
            throw r4     // Catch: java.lang.Throwable -> La6
        La6:
            r0 = move-exception
            throw r0
        La8:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.m():void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x002d, code lost:
    
        if (r9.a != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x002f, code lost:
    
        r0 = r0 + 91;
        o.ey.e.h = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0037, code lost:
    
        if ((r0 % 2) != 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0039, code lost:
    
        r0 = '\\';
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x003e, code lost:
    
        switch(r0) {
            case 92: goto L24;
            default: goto L21;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0041, code lost:
    
        r0 = r9.a.d();
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0047, code lost:
    
        if (r0 == null) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x005f, code lost:
    
        if (r0.before(new java.util.Date()) == false) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0061, code lost:
    
        o.ee.g.c();
        r2 = new java.lang.Object[1];
        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1)), r2);
        r0 = ((java.lang.String) r2[0]).intern();
        r4 = new java.lang.Object[1];
        z((char) (android.os.Process.myTid() >> 22), (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1)) + 77, (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16) + 65, r4);
        o.ee.g.d(r0, java.lang.String.format(((java.lang.String) r4[0]).intern(), r9.c));
        r0 = r9.d.iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00b7, code lost:
    
        if (r0.hasNext() == false) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00b9, code lost:
    
        r0.next().e(r9.c, r10);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x004a, code lost:
    
        r9.a.d();
        r10 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0050, code lost:
    
        r10.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0053, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x003c, code lost:
    
        r0 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0027, code lost:
    
        if (r9.a != null) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void a(o.dd.e r10) {
        /*
            Method dump skipped, instructions count: 244
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.a(o.dd.e):void");
    }

    public final void n() {
        switch (this.d == null) {
            case true:
                break;
            default:
                int i2 = h + 61;
                i = i2 % 128;
                int i3 = i2 % 2;
                switch (this.a == null) {
                    case false:
                        g.c();
                        Object[] objArr = new Object[1];
                        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", 1 - TextUtils.getTrimmedLength(""), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        z((char) (KeyEvent.normalizeMetaState(0) + 11374), Process.getGidForName("") + Opcodes.D2F, (ViewConfiguration.getFadingEdgeLength() >> 16) + 53, objArr2);
                        g.d(intern, String.format(((String) objArr2[0]).intern(), this.c));
                        for (T t : this.d) {
                            switch (!t.d()) {
                                case false:
                                    break;
                                default:
                                    switch (t.b() == o.fc.c.b ? 'K' : '#') {
                                        case 'K':
                                            g.c();
                                            Object[] objArr3 = new Object[1];
                                            w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", ExpandableListView.getPackedPositionType(0L) + 1, objArr3);
                                            String intern2 = ((String) objArr3[0]).intern();
                                            Object[] objArr4 = new Object[1];
                                            z((char) ((-1) - TextUtils.lastIndexOf("", '0')), (ViewConfiguration.getJumpTapTimeout() >> 16) + 196, 113 - TextUtils.getOffsetBefore("", 0), objArr4);
                                            g.d(intern2, String.format(((String) objArr4[0]).intern(), Short.valueOf(t.c()), this.c, Short.valueOf(t.e())));
                                            t.c(o.fc.c.d);
                                            int i4 = h + 21;
                                            i = i4 % 128;
                                            int i5 = i4 % 2;
                                            break;
                                    }
                            }
                        }
                        break;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void c(o.dd.e r10) {
        /*
            r9 = this;
            java.util.List<T extends o.fc.d> r0 = r9.d
            if (r0 == 0) goto Lb6
            int r0 = o.ey.e.i
            int r0 = r0 + 49
            int r1 = r0 % 128
            o.ey.e.h = r1
            int r0 = r0 % 2
            o.fj.c r0 = r9.a
            r2 = 1
            r3 = 0
            if (r0 == 0) goto L17
            r0 = r3
            goto L18
        L17:
            r0 = r2
        L18:
            switch(r0) {
                case 1: goto Lb6;
                default: goto L1b;
            }
        L1b:
            int r1 = r1 + 83
            int r0 = r1 % 128
            o.ey.e.i = r0
            int r1 = r1 % 2
            o.ee.g.c()
            java.lang.String r0 = ""
            int r0 = android.text.TextUtils.getCapsMode(r0, r3, r3)
            int r0 = r0 + r2
            java.lang.Object[] r1 = new java.lang.Object[r2]
            java.lang.String r4 = "\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐"
            w(r4, r0, r1)
            r0 = r1[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r1 = android.view.ViewConfiguration.getJumpTapTimeout()
            int r1 = r1 >> 16
            int r1 = r1 + 24995
            char r1 = (char) r1
            int r4 = android.view.ViewConfiguration.getKeyRepeatDelay()
            int r4 = r4 >> 16
            int r4 = 309 - r4
            long r5 = android.os.SystemClock.elapsedRealtime()
            r7 = 0
            int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            int r5 = 35 - r5
            java.lang.Object[] r6 = new java.lang.Object[r2]
            z(r1, r4, r5, r6)
            r1 = r6[r3]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.String r4 = r9.c
            java.lang.Object[] r4 = new java.lang.Object[]{r4}
            java.lang.String r1 = java.lang.String.format(r1, r4)
            o.ee.g.d(r0, r1)
            java.util.List<T extends o.fc.d> r0 = r9.d
            java.util.Iterator r0 = r0.iterator()
        L77:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L7f
            r1 = r3
            goto L80
        L7f:
            r1 = r2
        L80:
            switch(r1) {
                case 1: goto Lb6;
                default: goto L83;
            }
        L83:
            int r1 = o.ey.e.h
            int r1 = r1 + 57
            int r4 = r1 % 128
            o.ey.e.i = r4
            int r1 = r1 % 2
            if (r1 == 0) goto L92
            r1 = 14
            goto L94
        L92:
            r1 = 51
        L94:
            switch(r1) {
                case 51: goto La3;
                default: goto L97;
            }
        L97:
            java.lang.Object r0 = r0.next()
            o.fc.d r0 = (o.fc.d) r0
            java.lang.String r1 = r9.c
            r0.e(r1, r10)
            goto Laf
        La3:
            java.lang.Object r1 = r0.next()
            o.fc.d r1 = (o.fc.d) r1
            java.lang.String r4 = r9.c
            r1.e(r4, r10)
            goto L77
        Laf:
            r10 = 0
            r10.hashCode()     // Catch: java.lang.Throwable -> Lb4
            throw r10     // Catch: java.lang.Throwable -> Lb4
        Lb4:
            r10 = move-exception
            throw r10
        Lb6:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.c(o.dd.e):void");
    }

    public final boolean c(int i2) {
        int i3 = h + Opcodes.DDIV;
        i = i3 % 128;
        int i4 = i3 % 2;
        Object[] objArr = new Object[1];
        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", TextUtils.getCapsMode("", 0, 0) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 26637), 343 - (ViewConfiguration.getWindowTouchSlop() >> 8), 30 - Color.green(0), objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        List<T> list = this.d;
        if (list != null) {
            int i5 = h + 73;
            i = i5 % 128;
            int i6 = i5 % 2;
            if (!list.isEmpty()) {
                c cVar = this.a;
                if (cVar == null) {
                    g.c();
                    StringBuilder append = new StringBuilder().append(intern2).append(this.c);
                    Object[] objArr3 = new Object[1];
                    w("舆舦䅆鼣䙟醠ꙻ끛鿢挄뵩鉌맥Ӄ\udbb3\uf780\udb3a⚃識즅\uf57b㢻ᐦ⭽ᚠ\uda69㈶യ・ﰢ", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1, objArr3);
                    g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
                    return false;
                }
                Date d = cVar.d();
                if (d != null && d.before(new Date())) {
                    g.c();
                    StringBuilder append2 = new StringBuilder().append(intern2).append(this.c);
                    Object[] objArr4 = new Object[1];
                    z((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 27964), ((Process.getThreadPriority(0) + 20) >> 6) + 391, 22 - Process.getGidForName(""), objArr4);
                    g.d(intern, append2.append(((String) objArr4[0]).intern()).toString());
                    return false;
                }
                short s = 0;
                for (T t : this.d) {
                    switch (t.a() ? ' ' : '\r') {
                        case ' ':
                            s = (short) (s + t.e());
                            break;
                    }
                    if (s > i2) {
                        g.c();
                        StringBuilder append3 = new StringBuilder().append(intern2).append(this.c);
                        Object[] objArr5 = new Object[1];
                        w("䍿䍟㜅⺭ご‧읰텂廈ᕁ\u0ce4\uf309碘狘横雃ᨓ傏䡻ꢊ㐜亽ꗵ䩸ퟎ걨菢汷\uf1ca詽\ue133\u0fee鍎\ue7fbｼ↭굿엿\udd05쌦䣿⌡㪕\ue52f檥Ĩᣞ\uf8e3Ѽ绥瘍髯♵峕吐벗", (-16777215) - Color.rgb(0, 0, 0), objArr5);
                        g.d(intern, String.format(append3.append(((String) objArr5[0]).intern()).toString(), Short.valueOf(s), Integer.valueOf(i2)));
                        int i7 = i + 69;
                        h = i7 % 128;
                        switch (i7 % 2 == 0 ? (char) 5 : '-') {
                            case '-':
                                return true;
                            default:
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                        }
                    }
                }
                g.c();
                StringBuilder append4 = new StringBuilder().append(intern2).append(this.c);
                Object[] objArr6 = new Object[1];
                z((char) (TextUtils.lastIndexOf("", '0', 0) + 1), 413 - ExpandableListView.getPackedPositionChild(0L), 55 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr6);
                g.d(intern, String.format(append4.append(((String) objArr6[0]).intern()).toString(), Short.valueOf(s), Integer.valueOf(i2)));
                return false;
            }
        }
        g.c();
        StringBuilder append5 = new StringBuilder().append(intern2).append(this.c);
        Object[] objArr7 = new Object[1];
        z((char) Color.argb(0, 0, 0, 0), 374 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 18 - KeyEvent.normalizeMetaState(0), objArr7);
        g.d(intern, append5.append(((String) objArr7[0]).intern()).toString());
        return false;
    }

    public final short o() {
        short s;
        List<T> list = this.d;
        switch (list != null ? 'U' : 'X') {
            case Opcodes.POP2 /* 88 */:
                break;
            default:
                int i2 = h + 27;
                i = i2 % 128;
                int i3 = i2 % 2;
                if (!list.isEmpty()) {
                    c cVar = this.a;
                    if (cVar == null) {
                        g.c();
                        Object[] objArr = new Object[1];
                        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", (ViewConfiguration.getFadingEdgeLength() >> 16) + 1, objArr);
                        String intern = ((String) objArr[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr2 = new Object[1];
                        z((char) (56813 - ExpandableListView.getPackedPositionGroup(0L)), TextUtils.getOffsetAfter("", 0) + 469, TextUtils.getCapsMode("", 0, 0) + 38, objArr2);
                        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(this.c);
                        Object[] objArr3 = new Object[1];
                        w("舆舦䅆鼣䙟醠ꙻ끛鿢挄뵩鉌맥Ӄ\udbb3\uf780\udb3a⚃識즅\uf57b㢻ᐦ⭽ᚠ\uda69㈶യ・ﰢ", 1 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr3);
                        g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
                        return (short) 0;
                    }
                    Date d = cVar.d();
                    if (d != null && d.before(new Date())) {
                        g.c();
                        Object[] objArr4 = new Object[1];
                        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", 1 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr4);
                        String intern2 = ((String) objArr4[0]).intern();
                        StringBuilder sb2 = new StringBuilder();
                        Object[] objArr5 = new Object[1];
                        z((char) (56861 - AndroidCharacter.getMirror('0')), 470 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (KeyEvent.getMaxKeyCode() >> 16) + 38, objArr5);
                        StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(this.c);
                        Object[] objArr6 = new Object[1];
                        z((char) (AndroidCharacter.getMirror('0') + 27916), TextUtils.indexOf((CharSequence) "", '0', 0) + 392, 23 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr6);
                        g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
                        return (short) 0;
                    }
                    List<T> list2 = this.d;
                    switch (list2 != null) {
                        case true:
                            int i4 = h + 77;
                            i = i4 % 128;
                            switch (i4 % 2 != 0) {
                                case true:
                                    list2.iterator();
                                    throw null;
                                default:
                                    Iterator<T> it = list2.iterator();
                                    s = 0;
                                    while (true) {
                                        switch (it.hasNext() ? (char) 14 : (char) 20) {
                                            case 20:
                                                break;
                                            default:
                                                int i5 = i + 45;
                                                h = i5 % 128;
                                                int i6 = i5 % 2;
                                                T next = it.next();
                                                switch (next.a()) {
                                                    case true:
                                                        s = (short) (s + next.e());
                                                        break;
                                                }
                                                break;
                                        }
                                    }
                            }
                        default:
                            s = 0;
                            break;
                    }
                    int i7 = i + 69;
                    h = i7 % 128;
                    if (i7 % 2 != 0) {
                        return s;
                    }
                    int i8 = 85 / 0;
                    return s;
                }
                break;
        }
        g.c();
        Object[] objArr7 = new Object[1];
        w("\u2dcf\u2d9f堼͋弬ැ뛩ꃗの穷ℂ芵ᘢᶬ䟹\ue75d瓭㿥斟\ud91f媱⇈衐", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr8 = new Object[1];
        z((char) ((Process.myPid() >> 22) + 56813), 469 - KeyEvent.getDeadChar(0, 0), (ViewConfiguration.getLongPressTimeout() >> 16) + 38, objArr8);
        StringBuilder append3 = sb3.append(((String) objArr8[0]).intern()).append(this.c);
        Object[] objArr9 = new Object[1];
        w("簊簪ꎵ倌꒬序\ue04e\uf66e懮臷牆푹䟩\ue630ᒜ놵┧쑧㛀辸୶\uda4c\udb1f流\ue8bb", 1 - TextUtils.indexOf("", "", 0, 0), objArr9);
        g.d(intern3, append3.append(((String) objArr9[0]).intern()).toString());
        return (short) 0;
    }

    public final void l() {
        int i2 = i + 31;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '(' : 'U') {
            case Opcodes.CASTORE /* 85 */:
                List<T> list = this.d;
                switch (list == null) {
                    case true:
                        break;
                    default:
                        list.clear();
                        int i3 = h + Opcodes.DNEG;
                        i = i3 % 128;
                        int i4 = i3 % 2;
                        break;
                }
                int i5 = h + 95;
                i = i5 % 128;
                int i6 = i5 % 2;
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0011. Please report as an issue. */
    public final void q() {
        int i2 = i + 97;
        int i3 = i2 % 128;
        h = i3;
        switch (i2 % 2 != 0) {
        }
        this.b = false;
        int i4 = i3 + 61;
        i = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void w(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 400
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.w(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void z(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1014
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.e.z(char, int, int, java.lang.Object[]):void");
    }
}
