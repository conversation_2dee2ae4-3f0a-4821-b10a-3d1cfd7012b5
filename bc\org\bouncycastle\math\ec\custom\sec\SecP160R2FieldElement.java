package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP160R2FieldElement.smali */
public class SecP160R2FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73"));
    protected int[] a;

    public SecP160R2FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP160R2FieldElement");
        }
        this.a = SecP160R2Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R2Field.add(this.a, ((SecP160R2FieldElement) eCFieldElement).a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = t5.a();
        SecP160R2Field.addOne(this.a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R2Field.inv(((SecP160R2FieldElement) eCFieldElement).a, a);
        SecP160R2Field.multiply(a, this.a, a);
        return new SecP160R2FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP160R2FieldElement) {
            return t5.a(this.a, ((SecP160R2FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP160R2Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 5);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = t5.a();
        SecP160R2Field.inv(this.a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return t5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return t5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R2Field.multiply(this.a, ((SecP160R2FieldElement) eCFieldElement).a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = t5.a();
        SecP160R2Field.negate(this.a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (t5.b(iArr) || t5.a(iArr)) {
            return this;
        }
        int[] a = t5.a();
        SecP160R2Field.square(iArr, a);
        SecP160R2Field.multiply(a, iArr, a);
        int[] a2 = t5.a();
        SecP160R2Field.square(a, a2);
        SecP160R2Field.multiply(a2, iArr, a2);
        int[] a3 = t5.a();
        SecP160R2Field.square(a2, a3);
        SecP160R2Field.multiply(a3, iArr, a3);
        int[] a4 = t5.a();
        SecP160R2Field.squareN(a3, 3, a4);
        SecP160R2Field.multiply(a4, a2, a4);
        SecP160R2Field.squareN(a4, 7, a3);
        SecP160R2Field.multiply(a3, a4, a3);
        SecP160R2Field.squareN(a3, 3, a4);
        SecP160R2Field.multiply(a4, a2, a4);
        int[] a5 = t5.a();
        SecP160R2Field.squareN(a4, 14, a5);
        SecP160R2Field.multiply(a5, a3, a5);
        SecP160R2Field.squareN(a5, 31, a3);
        SecP160R2Field.multiply(a3, a5, a3);
        SecP160R2Field.squareN(a3, 62, a5);
        SecP160R2Field.multiply(a5, a3, a5);
        SecP160R2Field.squareN(a5, 3, a3);
        SecP160R2Field.multiply(a3, a2, a3);
        SecP160R2Field.squareN(a3, 18, a3);
        SecP160R2Field.multiply(a3, a4, a3);
        SecP160R2Field.squareN(a3, 2, a3);
        SecP160R2Field.multiply(a3, iArr, a3);
        SecP160R2Field.squareN(a3, 3, a3);
        SecP160R2Field.multiply(a3, a, a3);
        SecP160R2Field.squareN(a3, 6, a3);
        SecP160R2Field.multiply(a3, a2, a3);
        SecP160R2Field.squareN(a3, 2, a3);
        SecP160R2Field.multiply(a3, iArr, a3);
        SecP160R2Field.square(a3, a);
        if (t5.a(iArr, a)) {
            return new SecP160R2FieldElement(a3);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = t5.a();
        SecP160R2Field.square(this.a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R2Field.subtract(this.a, ((SecP160R2FieldElement) eCFieldElement).a, a);
        return new SecP160R2FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return t5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return t5.c(this.a);
    }

    public SecP160R2FieldElement() {
        this.a = t5.a();
    }

    protected SecP160R2FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
