package fr.antelop.sdk.card;

import o.co.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\EmvApplicationActivationMethod.smali */
public final class EmvApplicationActivationMethod {
    private final a innerMethod;

    public EmvApplicationActivationMethod(a aVar) {
        this.innerMethod = aVar;
    }

    public final String getId() {
        return this.innerMethod.d();
    }

    public final EmvApplicationActivationMethodType getType() {
        return this.innerMethod.e();
    }

    public final String getData1() {
        return this.innerMethod.b();
    }

    public final String getData2() {
        return this.innerMethod.f();
    }

    public final String getDescription() {
        return this.innerMethod.g();
    }

    public final String toString() {
        return new StringBuilder("EmvApplicationActivationMethod{id=").append(getId()).append(", type=").append(getType()).append(", data1=").append(getData1() == null ? "" : getData1()).append(", data2=").append(getData2() == null ? "" : getData2()).append(", description=").append(getDescription() != null ? getDescription() : "").append('}').toString();
    }
}
