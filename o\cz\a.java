package o.cz;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import o.ee.g;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static char[] b;
    private static int c;
    private static long d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        d();
        Process.myPid();
        AudioTrack.getMaxVolume();
        ViewConfiguration.getLongPressTimeout();
        int i = c + 47;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        b = new char[]{24900, 3982, 48315, 11692, 56000, 19408, 63681, 27108, 5634, 34566, 13375, 42273, 21077, 50001, 28799, 57723, 44118, 49838, 29083, 57485, 6129, 34529, 13785, 42125, 56112, 18986, 63775, 26632, 40804, 3700, 48406, 11331, 17062, 61844, 24711, 38841, 1762, 46540, 9409, 23347, 51820, 30997, 59403, 8061, 36453, 11428, 16967, 61807, 24656, 38663, 1539, 46397, 9267, 23504, 51917, 31211, 59621, 8082, 36484, 15770, 44197, 49750, 29027, 57449, 5888, 35645, 58832, 22252, 51188, 12416, 41374, 4745, 33718, 64582, 27993, 56931, 20342, 47120, 10522, 39469, 2879, 26100, 55014, 18419, 45192, 8595, 37566, 942, 16615, 11793, 40231, 3133, 64379, 27225, 55679, 18551, 14208, 42644, 28934, 8176, 44230, 15836, 51851, 23486, 59527, 31163, 1643, 38781, 9281, 46410, 11437, 16979, 61814, 24680, 38689, 1539, 46371, 9234, 23508, 51933, 31226, 59640, 8105, 36483, 15798, 44205, 49750, 29027, 57459, 11395, 16981, 61816, 24682, 38741, 1575, 46379, 9251, 23508, 51934, 31211, 59621, 8082, 36484, 15867, 44205, 49751, 29045, 57448, 5889, 34315, 13606, 42022, 56258, 19140, 63994, 26799, 40843, 3717, 48563, 11431, 16989, 61705, 24694, 38661, 1547, 46390, 9255, 23488, 51923, 31224, 59616, 8088, 36556, 15793, 44195, 49750, 28993, 57453, 5893, 34375, 13678, 42085};
        d = -776966801924079066L;
        a = new int[]{-1951657982, -746624351, -1526519288, 1887312356, -1007014212, 1645676296, -788388285, 1271061806, 1968977058, -1724805692, 374825205, 54066058, -730839979, 466150835, 1424441138, 1981188315, 785806805, 1461535501};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 1 - r6
            int r7 = r7 * 2
            int r7 = 3 - r7
            int r8 = 116 - r8
            byte[] r0 = o.cz.a.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r7 = r7 + 1
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.a.h(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{40, 24, -45, -26};
        $$b = Opcodes.LSHL;
    }

    public static o.fn.d c(o.eg.b bVar) throws i {
        int i = c + 7;
        e = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) ((Process.myTid() >> 22) + 19905), (-1) - TextUtils.lastIndexOf("", '0', 0, 0), (ViewConfiguration.getLongPressTimeout() >> 16) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (33006 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 15 - ExpandableListView.getPackedPositionChild(0L), 29 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            Object[] objArr3 = new Object[1];
            f((char) ((Process.getThreadPriority(0) + 20) >> 6), Color.blue(0) + 45, AndroidCharacter.getMirror('0') - 28, objArr3);
            BigDecimal o2 = bVar.o(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            f((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 42905), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 66, TextUtils.lastIndexOf("", '0', 0, 0) + 24, objArr4);
            int intValue = bVar.e(((String) objArr4[0]).intern(), (Integer) 60).intValue();
            Object[] objArr5 = new Object[1];
            g(new int[]{1249154703, -1285049726, 1022899776, 1941374994, 251079666, 79736045, -855312518, 853295773, 725446364, 739516694, -919175750, -1511016832, -1668935571, 384340745, -1145718181, -1965450544}, Gravity.getAbsoluteGravity(0, 0) + 30, objArr5);
            int intValue2 = bVar.i(((String) objArr5[0]).intern()).intValue();
            if (intValue2 < 0) {
                Object[] objArr6 = new Object[1];
                g(new int[]{1249154703, -1285049726, 2097279932, -748885261, -1948726666, 824060548, -1576513214, 218939935, 519725329, -1674368333, 1030578779, 210976460, -1806375352, -1517258422, 132632611, 354862426, -854287479, -291593691, -168450144, 1524349273, -1379320151, -133705914, -1057413830, -1577549046, -1834072069, 1858794805, 322891192, 492349753, -927822927, -1084431509, 1641446677, -1501362119}, 61 - Gravity.getAbsoluteGravity(0, 0), objArr6);
                throw new i(((String) objArr6[0]).intern());
            }
            Object[] objArr7 = new Object[1];
            g(new int[]{1249154703, -1285049726, 1022899776, 1941374994, 251079666, 79736045, -855312518, 853295773, 725446364, 739516694, 856335138, -110671807, 1823155578, 944878302, 722392295, -672551307}, 31 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr7);
            int intValue3 = bVar.i(((String) objArr7[0]).intern()).intValue();
            if (intValue3 < 0) {
                Object[] objArr8 = new Object[1];
                g(new int[]{1249154703, -1285049726, 2097279932, -748885261, -1948726666, 824060548, -1576513214, 218939935, 519725329, -1674368333, 1030578779, 210976460, 453825290, 1812348038, -1336269204, 1552007907, 12184864, -1450532723, -168450144, 1524349273, -1379320151, -133705914, -1057413830, -1577549046, -1834072069, 1858794805, 322891192, 492349753, -927822927, -1084431509, 1641446677, -1501362119}, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 61, objArr8);
                throw new i(((String) objArr8[0]).intern());
            }
            Object[] objArr9 = new Object[1];
            f((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 27741), 88 - TextUtils.indexOf("", "", 0, 0), 10 - KeyEvent.keyCodeFromString(""), objArr9);
            int intValue4 = bVar.e(((String) objArr9[0]).intern(), (Integer) (-1)).intValue();
            Object[] objArr10 = new Object[1];
            f((char) (23998 - Process.getGidForName("")), 98 - (ViewConfiguration.getTouchSlop() >> 8), ExpandableListView.getPackedPositionChild(0L) + 13, objArr10);
            int intValue5 = bVar.e(((String) objArr10[0]).intern(), (Integer) (-1)).intValue();
            Object[] objArr11 = new Object[1];
            f((char) KeyEvent.normalizeMetaState(0), 109 - TextUtils.lastIndexOf("", '0', 0), View.resolveSizeAndState(0, 0, 0) + 19, objArr11);
            o.fn.d dVar = new o.fn.d(o2, intValue, intValue2, intValue3, intValue4, intValue5, bVar.e(((String) objArr11[0]).intern(), (Integer) (-1)).intValue());
            int i3 = e + 61;
            c = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return dVar;
                default:
                    throw null;
            }
        } catch (o.eg.d e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr12 = new Object[1];
            f((char) TextUtils.indexOf("", "", 0, 0), Color.blue(0) + Opcodes.LOR, Color.rgb(0, 0, 0) + 16777269, objArr12);
            throw new i(sb.append(((String) objArr12[0]).intern()).append(e2.getMessage()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.a.f(char, int, int, java.lang.Object[]):void");
    }

    private static void g(int[] iArr, int i, Object[] objArr) {
        int i2;
        int i3;
        long j;
        o.a.g gVar;
        int[] iArr2 = iArr;
        o.a.g gVar2 = new o.a.g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr2.length * 2];
        int[] iArr3 = a;
        float f = 0.0f;
        int i4 = -1667374059;
        int i5 = 1;
        int i6 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i7 = 0;
            while (i7 < length) {
                try {
                    Object[] objArr2 = {Integer.valueOf(iArr3[i7])};
                    Object obj = o.e.a.s.get(Integer.valueOf(i4));
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(11 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) ((TypedValue.complexToFraction(0, f, f) > f ? 1 : (TypedValue.complexToFraction(0, f, f) == f ? 0 : -1)) + 8856), 323 - ((byte) KeyEvent.getModifierMetaStateMask()));
                        byte b2 = (byte) 0;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        h(b2, b3, b3, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1667374059, obj);
                    }
                    iArr4[i7] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                    i7++;
                    f = 0.0f;
                    i4 = -1667374059;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            iArr3 = iArr4;
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = a;
        long j2 = 0;
        int i8 = 16;
        switch (iArr6 != null ? (char) 11 : (char) 27) {
            case 27:
                break;
            default:
                int i9 = $10 + 43;
                $11 = i9 % 128;
                int i10 = i9 % 2;
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i11 = 0;
                while (true) {
                    switch (i11 < length3 ? i6 : i5) {
                        case 1:
                            iArr6 = iArr7;
                            break;
                        default:
                            int i12 = $10 + 97;
                            $11 = i12 % 128;
                            if (i12 % 2 == 0) {
                                try {
                                    Object[] objArr4 = new Object[i5];
                                    objArr4[i6] = Integer.valueOf(iArr6[i11]);
                                    Object obj2 = o.e.a.s.get(-1667374059);
                                    if (obj2 != null) {
                                        gVar = gVar2;
                                        j = 0;
                                    } else {
                                        j = 0;
                                        Class cls2 = (Class) o.e.a.c((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 9, (char) (8856 - (ViewConfiguration.getWindowTouchSlop() >> 8)), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 324);
                                        byte b4 = (byte) i6;
                                        byte b5 = b4;
                                        gVar = gVar2;
                                        Object[] objArr5 = new Object[1];
                                        h(b4, b5, b5, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj2);
                                    }
                                    iArr7[i11] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                    i11 *= 1;
                                    iArr2 = iArr;
                                    j2 = j;
                                    gVar2 = gVar;
                                    i8 = 16;
                                    i5 = 1;
                                    i6 = 0;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                o.a.g gVar3 = gVar2;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(iArr6[i11])};
                                    Object obj3 = o.e.a.s.get(-1667374059);
                                    if (obj3 != null) {
                                        i2 = 1;
                                        i3 = 0;
                                    } else {
                                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 10, (char) (8904 - AndroidCharacter.getMirror('0')), Color.rgb(0, 0, 0) + 16777540);
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr7 = new Object[1];
                                        h(b6, b7, b7, objArr7);
                                        i2 = 1;
                                        i3 = 0;
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj3);
                                    }
                                    iArr7[i11] = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                                    i11++;
                                    iArr2 = iArr;
                                    i5 = i2;
                                    i6 = i3;
                                    j2 = 0;
                                    gVar2 = gVar3;
                                    i8 = 16;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                    }
                }
        }
        System.arraycopy(iArr6, i6, iArr5, i6, length2);
        gVar2.a = i6;
        while (gVar2.a < iArr2.length) {
            int i13 = $11 + Opcodes.DNEG;
            $10 = i13 % 128;
            int i14 = i13 % 2;
            cArr[i6] = (char) (iArr2[gVar2.a] >> i8);
            cArr[i5] = (char) iArr2[gVar2.a];
            cArr[2] = (char) (iArr2[gVar2.a + i5] >> i8);
            cArr[3] = (char) iArr2[gVar2.a + i5];
            gVar2.e = (cArr[i6] << i8) + cArr[i5];
            gVar2.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr5);
            int i15 = $11 + 35;
            $10 = i15 % 128;
            int i16 = i15 % 2;
            int i17 = i6;
            while (i17 < i8) {
                gVar2.e ^= iArr5[i17];
                int b8 = o.a.g.b(gVar2.e);
                try {
                    Object[] objArr8 = new Object[4];
                    objArr8[3] = gVar2;
                    objArr8[2] = gVar2;
                    objArr8[i5] = Integer.valueOf(b8);
                    objArr8[i6] = gVar2;
                    Object obj4 = o.e.a.s.get(-2036901605);
                    if (obj4 == null) {
                        Class cls4 = (Class) o.e.a.c((SystemClock.uptimeMillis() > j2 ? 1 : (SystemClock.uptimeMillis() == j2 ? 0 : -1)) + 10, (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 572 - KeyEvent.getDeadChar(i6, i6));
                        Class<?>[] clsArr = new Class[4];
                        clsArr[i6] = Object.class;
                        clsArr[i5] = Integer.TYPE;
                        clsArr[2] = Object.class;
                        clsArr[3] = Object.class;
                        obj4 = cls4.getMethod("q", clsArr);
                        o.e.a.s.put(-2036901605, obj4);
                    }
                    int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                    gVar2.e = gVar2.c;
                    gVar2.c = intValue;
                    i17++;
                    int i18 = $10 + i5;
                    $11 = i18 % 128;
                    int i19 = i18 % 2;
                    j2 = 0;
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            int i20 = gVar2.e;
            gVar2.e = gVar2.c;
            gVar2.c = i20;
            gVar2.c ^= iArr5[i8];
            gVar2.e ^= iArr5[17];
            int i21 = gVar2.e;
            int i22 = gVar2.c;
            cArr[i6] = (char) (gVar2.e >>> i8);
            cArr[i5] = (char) gVar2.e;
            cArr[2] = (char) (gVar2.c >>> i8);
            cArr[3] = (char) gVar2.c;
            o.a.g.d(iArr5);
            cArr2[gVar2.a * 2] = cArr[i6];
            cArr2[(gVar2.a * 2) + i5] = cArr[i5];
            cArr2[(gVar2.a * 2) + 2] = cArr[2];
            cArr2[(gVar2.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr9 = {gVar2, gVar2};
                Object obj5 = o.e.a.s.get(-331007466);
                if (obj5 == null) {
                    Class cls5 = (Class) o.e.a.c(View.combineMeasuredStates(i6, i6) + 12, (char) (ImageFormat.getBitsPerPixel(i6) + 55184), 515 - Color.green(i6));
                    byte b9 = (byte) i6;
                    byte b10 = b9;
                    Object[] objArr10 = new Object[i5];
                    h(b9, b10, (byte) (b10 + 1), objArr10);
                    String str = (String) objArr10[i6];
                    Class<?>[] clsArr2 = new Class[2];
                    clsArr2[i6] = Object.class;
                    clsArr2[i5] = Object.class;
                    obj5 = cls5.getMethod(str, clsArr2);
                    o.e.a.s.put(-331007466, obj5);
                }
                ((Method) obj5).invoke(null, objArr9);
                j2 = 0;
            } catch (Throwable th5) {
                Throwable cause5 = th5.getCause();
                if (cause5 == null) {
                    throw th5;
                }
                throw cause5;
            }
        }
        objArr[i6] = new String(cArr2, i6, i);
    }
}
