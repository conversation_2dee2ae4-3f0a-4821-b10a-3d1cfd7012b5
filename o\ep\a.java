package o.ep;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import o.ee.g;
import o.ee.h;
import o.ee.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\a.smali */
public interface a<Response> {
    public static final List<e> e = new ArrayList();

    /* renamed from: o.ep.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\a$a.smali */
    public interface InterfaceC0042a<T> {
        void e(T t);

        void e(o.bv.c cVar);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\a$c.smali */
    public interface c {
        void a(String str);

        void d(o.bv.c cVar);
    }

    @Deprecated
    void a(Activity activity, InterfaceC0042a<Object> interfaceC0042a, i iVar);

    void a(InterfaceC0042a<String> interfaceC0042a);

    void b(InterfaceC0042a<String> interfaceC0042a);

    void c(Activity activity);

    void d(InterfaceC0042a<List<e>> interfaceC0042a);

    void e(Activity activity, c cVar, i iVar, o.eo.e eVar, e eVar2, Response response, h hVar);

    void e(InterfaceC0042a<b> interfaceC0042a);

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\a$b.smali */
    public static final class b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final b a;
        public static final b b;
        public static final b c;
        private static long d;
        private static final /* synthetic */ b[] e;
        private static int h;
        private static int j;

        static void e() {
            d = -3378859620608635812L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0037). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void g(byte r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 4
                int r7 = r7 + 1
                int r6 = r6 * 4
                int r6 = 4 - r6
                int r8 = r8 * 3
                int r8 = 71 - r8
                byte[] r0 = o.ep.a.b.$$a
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1d
                r8 = r7
                r3 = r1
                r4 = r2
                r7 = r6
                r1 = r0
                r0 = r9
                r9 = r8
                goto L37
            L1d:
                r3 = r2
            L1e:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                r4 = r0[r6]
                int r3 = r3 + 1
                r5 = r7
                r7 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L37:
                int r6 = -r6
                int r8 = r8 + r6
                int r6 = r7 + 1
                r7 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1e
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ep.a.b.g(byte, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{81, -74, 18, 60};
            $$b = Opcodes.IXOR;
        }

        private b(String str, int i) {
        }

        private static /* synthetic */ b[] d() {
            int i = j + Opcodes.DNEG;
            int i2 = i % 128;
            h = i2;
            int i3 = i % 2;
            b[] bVarArr = {c, a, b};
            int i4 = i2 + 1;
            j = i4 % 128;
            int i5 = i4 % 2;
            return bVarArr;
        }

        public static b valueOf(String str) {
            int i = j + 109;
            h = i % 128;
            int i2 = i % 2;
            b bVar = (b) Enum.valueOf(b.class, str);
            int i3 = j + 23;
            h = i3 % 128;
            int i4 = i3 % 2;
            return bVar;
        }

        public static b[] values() {
            int i = h + 97;
            j = i % 128;
            switch (i % 2 == 0) {
                case true:
                    return (b[]) e.clone();
                default:
                    int i2 = 45 / 0;
                    return (b[]) e.clone();
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            h = 1;
            e();
            Object[] objArr = new Object[1];
            f("鈁㞷鉀筗➚缢ꁻ뻨䥝髂", 1 - View.resolveSizeAndState(0, 0, 0), objArr);
            c = new b(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            f("\ueb26\u0b46\ueb68䞪\ueefc휘\ud945瞎疧볺㋒┮轙뮨諢笖給", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr2);
            a = new b(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            f("衞棇蠐\u242bⳂᗿ먧떰ᘸ绒\uf025\ue7df\uec34\ud828䣋맠Ḹ訙髞语䀘簝퓥巹", (ViewConfiguration.getWindowTouchSlop() >> 8) + 1, objArr3);
            b = new b(((String) objArr3[0]).intern(), 2);
            e = d();
            int i = j + Opcodes.LNEG;
            h = i % 128;
            int i2 = i % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 358
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ep.a.b.f(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    default String a(e eVar) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        bVar.d("tokenIdentifier", eVar.c());
        bVar.d("addTimestamp", eVar.a());
        bVar.d("panLastFour", eVar.d());
        bVar.d("tokenServiceProvider", eVar.b());
        bVar.d("tokenState", eVar.e().toString());
        bVar.d("isDefaultToken", eVar.f());
        return bVar.b();
    }

    default e c(o.eg.b bVar) throws o.eg.d {
        d dVar;
        d valueOf;
        switch (bVar.b("tokenState") ? (char) 15 : 'J') {
            case 'J':
                valueOf = d.c;
                dVar = valueOf;
                break;
            default:
                try {
                    valueOf = d.valueOf(bVar.r("tokenState"));
                    dVar = valueOf;
                    break;
                } catch (IllegalArgumentException e2) {
                    d dVar2 = d.c;
                    g.c();
                    g.a("IDevicePayWallet", "init() - deserializeToken", e2);
                    dVar = dVar2;
                    break;
                }
        }
        return new e(bVar.r("tokenIdentifier"), bVar.m("addTimestamp"), bVar.r("panLastFour"), bVar.i("tokenServiceProvider"), dVar, bVar.g("isDefaultToken"));
    }

    default void a(Context context, List<e> list, String str) {
        context.getSharedPreferences("fr.antelop.antelophcelibrary_shared_preferences", 0).edit().remove(str).apply();
        list.clear();
    }

    default void d(Context context, String str) {
        Iterator<String> it = context.getSharedPreferences("fr.antelop.antelophcelibrary_shared_preferences", 0).getStringSet(str, new HashSet()).iterator();
        while (it.hasNext()) {
            try {
                e.add(c(new o.eg.b(it.next())));
            } catch (o.eg.d e2) {
                g.c();
                g.a("IDevicePayWallet", "init() - deserializeToken", e2);
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    default void e(android.content.Context r5, java.util.List<o.ep.e> r6, java.lang.String r7) {
        /*
            r4 = this;
            java.util.HashSet r0 = new java.util.HashSet
            r0.<init>()
            java.util.Iterator r6 = r6.iterator()
        La:
        Lb:
            boolean r1 = r6.hasNext()
            r2 = 0
            if (r1 == 0) goto L14
            r1 = 1
            goto L15
        L14:
            r1 = r2
        L15:
            switch(r1) {
                case 1: goto L2a;
                default: goto L18;
            }
        L18:
            java.lang.String r6 = "fr.antelop.antelophcelibrary_shared_preferences"
            android.content.SharedPreferences r5 = r5.getSharedPreferences(r6, r2)
            android.content.SharedPreferences$Editor r5 = r5.edit()
            android.content.SharedPreferences$Editor r5 = r5.putStringSet(r7, r0)
            r5.commit()
            goto L44
        L2a:
            java.lang.Object r1 = r6.next()
            o.ep.e r1 = (o.ep.e) r1
            java.lang.String r1 = r4.a(r1)     // Catch: o.eg.d -> L38
            r0.add(r1)     // Catch: o.eg.d -> L38
            goto La
        L38:
            r1 = move-exception
            o.ee.g.c()
            java.lang.String r2 = "IDevicePayWallet"
            java.lang.String r3 = "persist - Unable to serialize token"
            o.ee.g.a(r2, r3, r1)
            goto Lb
        L44:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ep.a.e(android.content.Context, java.util.List, java.lang.String):void");
    }

    default e a(Context context, String str, String str2, String str3, Integer num, d dVar) {
        e eVar = new e(str2, Long.valueOf(new Date().getTime()), str3, num, dVar);
        List<e> list = e;
        list.add(eVar);
        e(context, list, str);
        return eVar;
    }
}
