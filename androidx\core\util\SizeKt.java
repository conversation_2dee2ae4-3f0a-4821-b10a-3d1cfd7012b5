package androidx.core.util;

import android.util.Size;
import android.util.SizeF;
import kotlin.Metadata;

/* compiled from: Size.kt */
@Metadata(d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0087\n\u001a\r\u0010\u0000\u001a\u00020\u0003*\u00020\u0004H\u0087\n\u001a\r\u0010\u0000\u001a\u00020\u0003*\u00020\u0005H\u0086\n\u001a\r\u0010\u0006\u001a\u00020\u0001*\u00020\u0002H\u0087\n\u001a\r\u0010\u0006\u001a\u00020\u0003*\u00020\u0004H\u0087\n\u001a\r\u0010\u0006\u001a\u00020\u0003*\u00020\u0005H\u0086\n¨\u0006\u0007"}, d2 = {"component1", "", "Landroid/util/Size;", "", "Landroid/util/SizeF;", "Landroidx/core/util/SizeFCompat;", "component2", "core-ktx_release"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\util\SizeKt.smali */
public final class SizeKt {
    public static final int component1(Size $this$component1) {
        return $this$component1.getWidth();
    }

    public static final int component2(Size $this$component2) {
        return $this$component2.getHeight();
    }

    public static final float component1(SizeF $this$component1) {
        return $this$component1.getWidth();
    }

    public static final float component2(SizeF $this$component2) {
        return $this$component2.getHeight();
    }

    public static final float component1(SizeFCompat $this$component1) {
        return $this$component1.getWidth();
    }

    public static final float component2(SizeFCompat $this$component2) {
        return $this$component2.getHeight();
    }
}
