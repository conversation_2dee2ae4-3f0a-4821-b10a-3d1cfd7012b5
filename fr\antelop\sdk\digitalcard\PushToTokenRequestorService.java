package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import o.er.s;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\PushToTokenRequestorService.smali */
public final class PushToTokenRequestorService {
    private final s innerPushToTokenRequestorService;

    public PushToTokenRequestorService(s sVar) {
        this.innerPushToTokenRequestorService = sVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerPushToTokenRequestorService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final void getTokenRequestors(Context context, OperationCallback<List<TokenRequestor>> operationCallback) throws WalletValidationException {
        this.innerPushToTokenRequestorService.b(context, operationCallback);
    }
}
