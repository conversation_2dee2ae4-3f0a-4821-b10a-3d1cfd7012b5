package o.dj;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.transaction.TransactionDecision;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import fr.antelop.sdk.transaction.hce.HceTransactionDeclineReason;
import fr.antelop.sdk.transaction.hce.HceTransactionStatus;
import fr.antelop.sdk.transaction.hce.HceTransactionStep;
import fr.antelop.sdk.transaction.hce.HceTransactionUnknownReason;
import fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.dj.b;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\b.smali */
public abstract class b implements WalletHceTransactionCallback {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    static final Object LOCK;
    private static final String TAG;
    private static int a;
    private static byte[] b;
    private static short[] c;
    private static int d;
    private static int e;
    private static char f;
    private static int g;
    private static long h;
    private static int i;
    private static int j;
    private final C0037b delayedEventDispatcher = new C0037b();
    final e transactionFlowLock = e.a();
    private boolean isPreviousTransactionProperlyFinished = false;

    static void d() {
        b = new byte[]{-127, -99, 103, -103, 97, -111, -102, -115, 78, 111, -106, 101, -127, -110, 126, -107, -99, ByteCompanionObject.MAX_VALUE, -114, ByteCompanionObject.MAX_VALUE, -110, -117, 70, -99, 103, -112, -75, -92, 105, -107, 102, -112, -98, -110, 100, 109, -110, -64, 41, 105, -107, 105, -112, -111, 107, -111, -61, 100, 88, 111, 97, -110, -109, -99, -111, -47, 34, -103, -112, -109, -46, Base64.padSymbol, -126, 105, -40, 34, 111, -106, 101, -127, -110, 126, -107, -99, ByteCompanionObject.MAX_VALUE, -114, -92, 99, -99, Base64.padSymbol, -112, -98, 99, -101, 104, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 114, 111, -65, 105, -107, 102, -112, -98, -110, 100, 109, -110, -64, 41, 105, -107, 101, -110, -127, 125, -111, -61, 100, 85, 105, -107, 101, -110, -127, 125, -111, -61, Base64.padSymbol, -102, -39, 34, 111, -106, 101, -127, -110, 126, -107, -99, ByteCompanionObject.MAX_VALUE, -114, -92, 99, -99, Base64.padSymbol, -112, -98, 99, -101, 104, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 114, 111, -119, -85, 118, 87, -97, 101, -105, 100, -97, -120, 79, 111, -106, 101, -125, 110, 106, 101, -106, -103, 109, 100, 111, -92, 77, 111, 126, -104, -109, -112, -101, -47, -112, -112, -112, 103, -125, 118, 92, 111, 108, -126, 108, -125, 125, 103, -107, 109, -103, 110, -111, -44, 99, -99, 103, -111, 83, 103, -107, 109, -103, 110, -79, 70, 111};
        e = 909053594;
        a = -1883239009;
        d = 1654837372;
        f = (char) 27250;
        j = 161105445;
        h = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{106, -103, -121, 51};
        $$b = 33;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dj.b.$$a
            int r9 = r9 * 4
            int r9 = 1 - r9
            int r7 = 110 - r7
            int r8 = r8 * 3
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = r8 + 1
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.b.m(short, byte, byte, java.lang.Object[]):void");
    }

    public abstract void onCredentialsRequired(Context context, List<CustomerAuthenticationMethod> list, HceTransaction hceTransaction);

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onSuccess(Context context, HceTransactionStatus hceTransactionStatus, HceTransaction hceTransaction) {
        int i2 = g + 97;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    public abstract void onTransactionDone(Context context, HceTransaction hceTransaction);

    public abstract void onTransactionError(Context context, AntelopError antelopError);

    public void onTransactionProgress(Context context, HceTransactionStep hceTransactionStep) {
        int i2 = g + 49;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public abstract void onTransactionStart(Context context);

    public void onTransactionsUpdated(Context context, Map<String, HceTransaction> map) {
        int i2 = i + 59;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onUnknownResult(Context context, HceTransactionUnknownReason hceTransactionUnknownReason, HceTransaction hceTransaction) {
        int i2 = g + 99;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        d();
        Object[] objArr = new Object[1];
        k((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), TextUtils.getTrimmedLength("") - 1418583788, (short) ExpandableListView.getPackedPositionGroup(0L), '%' - AndroidCharacter.getMirror('0'), 1175518522 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        TAG = ((String) objArr[0]).intern();
        LOCK = new Object();
        int i2 = i + 55;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onProgress(Context context, HceTransactionStep hceTransactionStep) {
        Object[] objArr = new Object[1];
        k((byte) TextUtils.getCapsMode("", 0, 0), View.combineMeasuredStates(0, 0) - 1418583788, (short) Color.red(0), (-11) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 1175518522 + (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        if (hceTransactionStep.getIndex() == 0) {
            g.c();
            Object[] objArr2 = new Object[1];
            l((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, "ꦜᒘ缼厫\ue0da蚏帒Ꞁ鞺ﲴ繏\udda9\uddca寅ঀ웧揣鵥砃踠茤现ⶹ︴歹ꃑ㸬徔蒯슔燗\udddb뵩脯聘㫃", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), "䖴긁聐백", "\u0000\u0000\u0000\u0000", objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            if (!this.transactionFlowLock.b()) {
                g.c();
                Object[] objArr3 = new Object[1];
                k((byte) TextUtils.indexOf("", "", 0), (-1418583761) + (ViewConfiguration.getPressedStateDuration() >> 16), (short) Color.blue(0), (-11) - View.resolveSize(0, 0), 1175518559 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
                g.d(intern, ((String) objArr3[0]).intern());
                return;
            }
        }
        synchronized (LOCK) {
            if (hceTransactionStep.getIndex() == 0) {
                g.c();
                Object[] objArr4 = new Object[1];
                k((byte) View.combineMeasuredStates(0, 0), (-1418583699) + TextUtils.indexOf("", "", 0, 0), (short) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 12, 1175518559 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr4);
                g.d(intern, ((String) objArr4[0]).intern());
                this.isPreviousTransactionProperlyFinished = false;
                if (this.delayedEventDispatcher.d()) {
                    g.c();
                    Object[] objArr5 = new Object[1];
                    l(132798640 - (ViewConfiguration.getEdgeSlop() >> 16), "\ued7f鉏葼릺触悥\udc9f涢ퟷ\uea8d咠櫋᪐谍ᯂ㊩ᱎ\ue017정ჩ㽦坿ഓᐳ\ue54e㏹䮪讕嫝牏떿鹓㷿❴脖\uf4a8ؼ⁅࠭薋㱖㜈駩低㷼\uf2f6敏잶끝㷽甚", (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), "끏\uea58\uf407ᵜ", "\u0000\u0000\u0000\u0000", objArr5);
                    g.d(intern, ((String) objArr5[0]).intern());
                    onTransactionStart(context);
                }
            }
            if (hceTransactionStep.getIndex() == 5) {
                g.c();
                Object[] objArr6 = new Object[1];
                l((-1) - ImageFormat.getBitsPerPixel(0), "N놭\ue379韹⻓\udb1c흙笔뺼䳮贋ॷ堗⎕ꩢ\ue3c1⋂\uef60絫\uf149訤\ue7d1↞ꈝ参荵얇滑", (char) (2550 - Color.green(0)), "ධꦮ\uf6ad\uda09", "\u0000\u0000\u0000\u0000", objArr6);
                g.d(intern, ((String) objArr6[0]).intern());
                this.transactionFlowLock.e();
                if (!this.isPreviousTransactionProperlyFinished) {
                    g.c();
                    Object[] objArr7 = new Object[1];
                    l((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 1011995710, "鳟წ\uf652堨ᮋ貇Ǡ௫詊\uee09㟀쐍⑼姘싰펴餇䢍撟峻\ua958扳\uf336筘릷돛沽⠮浩\uefbdꠐ卢ᷗ⪯滆떲啄ह\ueb80֩ㆲ襬혳̘ㅔἮ\u1f47\ue041骤㗪㴣\ue081얖", (char) (View.combineMeasuredStates(0, 0) + 53750), "㽪凔\uf63cố", "\u0000\u0000\u0000\u0000", objArr7);
                    g.d(intern, ((String) objArr7[0]).intern());
                    this.delayedEventDispatcher.c(context);
                }
            }
            onTransactionProgress(context, hceTransactionStep);
        }
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onCustomerCredentialsRequired(Context context, List<CustomerAuthenticationMethod> list, HceTransaction hceTransaction) {
        synchronized (LOCK) {
            g.c();
            Object[] objArr = new Object[1];
            k((byte) Color.alpha(0), (-1418583788) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (short) (Process.myPid() >> 22), (-11) - Color.green(0), 1175518523 + TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            l(KeyEvent.normalizeMetaState(0) + 915646170, "퉜뷲䇋㔍趺\uec6e옕\uda97熻ꓫŊ\uf2fc⬊䓏脙鵩๐⮗인Ċ煳뵵\ueae2씲\udb69鄄᬴㸣\uf1b2秕䱑\uea96嘂稗ᨮ㺳钪\ueb7c륡䝋걵퀮담힙孚", (char) (6821 - (ViewConfiguration.getScrollBarSize() >> 8)), "\uda8e鎦ꔶ㴚", "\u0000\u0000\u0000\u0000", objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(hceTransaction);
            Object[] objArr3 = new Object[1];
            k((byte) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (-1418583643) + (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (short) TextUtils.getCapsMode("", 0, 0), View.MeasureSpec.getMode(0) - 11, 1192295697 + Color.rgb(0, 0, 0), objArr3);
            StringBuilder append2 = append.append(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            l((-1640401183) - TextUtils.getOffsetAfter("", 0), "࣠ȗ䤸", (char) (TextUtils.getOffsetBefore("", 0) + 27008), "\ue1a0㥶肞镩", "\u0000\u0000\u0000\u0000", objArr4);
            StringBuilder append3 = append2.append(o.d(((String) objArr4[0]).intern(), (Iterator) list.iterator()));
            Object[] objArr5 = new Object[1];
            k((byte) (ViewConfiguration.getKeyRepeatTimeout() >> 16), (-1418583607) - View.resolveSize(0, 0), (short) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (-11) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 1175518542 + TextUtils.indexOf("", ""), objArr5);
            g.d(intern, append3.append(((String) objArr5[0]).intern()).toString());
            this.isPreviousTransactionProperlyFinished = true;
            this.transactionFlowLock.c(true);
            this.delayedEventDispatcher.b();
            onCredentialsRequired(context, list, hceTransaction);
        }
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onPending(Context context, HceTransaction hceTransaction) {
        synchronized (LOCK) {
            g.c();
            Object[] objArr = new Object[1];
            k((byte) Color.green(0), (-1418583788) - KeyEvent.normalizeMetaState(0), (short) TextUtils.getTrimmedLength(""), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 11, 1175518522 + View.MeasureSpec.getSize(0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            l(Color.argb(0, 0, 0, 0) - 2020643423, "砇\ue011펛兩쀁鄀蒅ﺣԤ秚觻亠痖뽝ꈿퟫ邪鸇듖\ue295⪣\ue762족ꨟ뗉熺\ud8a3", (char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 46796), "ꄅ轭첇ྶ", "\u0000\u0000\u0000\u0000", objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(hceTransaction).toString());
            this.isPreviousTransactionProperlyFinished = true;
            onTransactionDone(context, hceTransaction);
        }
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final void onDecline(Context context, HceTransactionDeclineReason hceTransactionDeclineReason, AntelopError antelopError, HceTransaction hceTransaction) {
        synchronized (LOCK) {
            g.c();
            Object[] objArr = new Object[1];
            k((byte) KeyEvent.keyCodeFromString(""), (-1418583788) - (KeyEvent.getMaxKeyCode() >> 16), (short) ((-1) - TextUtils.lastIndexOf("", '0')), Color.red(0) - 11, 1175518522 - KeyEvent.normalizeMetaState(0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((byte) View.combineMeasuredStates(0, 0), View.combineMeasuredStates(0, 0) - 1418583606, (short) (MotionEvent.axisFromString("") + 1), (-11) - ExpandableListView.getPackedPositionGroup(0L), ExpandableListView.getPackedPositionChild(0L) + 1175518561, objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(hceTransactionDeclineReason.name());
            Object[] objArr3 = new Object[1];
            l(1704780825 - View.resolveSizeAndState(0, 0, 0), "企㐷㨕㺘糿\uf18a컅은蟔斷쪽", (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "᧟鳤ɥ\udd3d", "\u0000\u0000\u0000\u0000", objArr3);
            StringBuilder append2 = append.append(((String) objArr3[0]).intern()).append(antelopError.toString());
            Object[] objArr4 = new Object[1];
            l((-972869059) - TextUtils.getCapsMode("", 0, 0), "\udc14\ue0f1ѿ⨲\udc02陊菴ॕ媢型ಞ䂜覵왾髻ꆠ엤", (char) (47352 - ExpandableListView.getPackedPositionGroup(0L)), "㷍̲\uf8c6햸", "\u0000\u0000\u0000\u0000", objArr4);
            g.d(intern, append2.append(((String) objArr4[0]).intern()).append(hceTransaction).toString());
            if (antelopError.getCode() == AntelopErrorCode.NfcDisconnection) {
                this.isPreviousTransactionProperlyFinished = false;
            } else {
                this.isPreviousTransactionProperlyFinished = true;
                onTransactionError(context, antelopError);
            }
        }
    }

    @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
    public final TransactionDecision onFinalization(Context context, CustomerAuthenticationMethod customerAuthenticationMethod, Date date, HceTransaction hceTransaction) {
        int i2 = i + 15;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                onTransactionFinalization(context, customerAuthenticationMethod, date, hceTransaction);
                throw null;
            default:
                return onTransactionFinalization(context, customerAuthenticationMethod, date, hceTransaction);
        }
    }

    /* renamed from: o.dj.b$b, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\b$b.smali */
    final class C0037b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int[] b;
        private static int f;
        private static int i;
        private final Handler a;
        private Runnable c;
        private Runnable e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            f = 1;
            b = new int[]{701030787, -2114192408, 1303068532, -444677981, -594221514, -637469722, 1048675487, -1211337016, -686768463, -538948281, -1294068886, -47054325, -344680422, -130162562, -1663206615, 499830678, 2088581714, -436223357};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(byte r7, short r8, int r9, java.lang.Object[] r10) {
            /*
                int r8 = r8 * 2
                int r8 = r8 + 4
                int r9 = 116 - r9
                int r7 = r7 * 3
                int r7 = r7 + 1
                byte[] r0 = o.dj.b.C0037b.$$a
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L16
                r3 = r9
                r4 = r2
                r9 = r8
                r8 = r7
                goto L2c
            L16:
                r3 = r2
            L17:
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                if (r4 != r7) goto L26
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L26:
                r3 = r0[r8]
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L2c:
                int r9 = r9 + 1
                int r7 = r7 + r3
                r3 = r4
                r6 = r9
                r9 = r7
                r7 = r8
                r8 = r6
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.dj.b.C0037b.h(byte, short, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{26, 103, -21, 32};
            $$b = 71;
        }

        C0037b() {
            Object[] objArr = new Object[1];
            g(new int[]{-1460618445, 1853997002, 38542125, -1775584084, -367788474, -318143655, -1693466520, 1033861789, -1686433196, 1052337887, -1357845204, -346338068, 1835429913, -338382873, 915370347, 1406071832, -341889571, -1827434074}, 36 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
            HandlerThread handlerThread = new HandlerThread(((String) objArr[0]).intern());
            handlerThread.start();
            this.a = new o.ee.b(handlerThread.getLooper());
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void d(Context context) {
            synchronized (b.LOCK) {
                g.c();
                Object[] objArr = new Object[1];
                g(new int[]{-367788474, -318143655, -1693466520, 1033861789, -1686433196, 1052337887, -1357845204, -346338068, 1835429913, -338382873, 915370347, 1406071832, -341889571, -1827434074}, View.combineMeasuredStates(0, 0) + 27, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                g(new int[]{824806927, 1617081418, 1876789362, -428753354, -1704263537, 1197932899, -388945519, -1787204285, 663532314, 391567729, -1657634596, -1968964712, 880297287, -225861056, -1014247237, -643550125, -70795072, -94897522, -2142193756, 1379984804, -258755347, 204168082, 1480694170, -1620397456, -620713323, -895355418, -1736400306, -280505939, 915090377, -1006020214}, Color.red(0) + 58, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.e = null;
                b.this.onTransactionError(context, new o.bv.c(AntelopErrorCode.NfcDisconnection).d());
            }
        }

        final void c(final Context context) {
            int i2 = i + Opcodes.LSUB;
            f = i2 % 128;
            int i3 = i2 % 2;
            Runnable runnable = new Runnable() { // from class: o.dj.b$b$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() {
                    b.C0037b.this.d(context);
                }
            };
            switch (this.a.postDelayed(runnable, 300L) ? (char) 31 : 'L') {
                case Base64.mimeLineLength /* 76 */:
                    break;
                default:
                    this.e = runnable;
                    int i4 = i + Opcodes.DNEG;
                    f = i4 % 128;
                    if (i4 % 2 != 0) {
                        break;
                    } else {
                        break;
                    }
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void a() {
            synchronized (b.LOCK) {
                g.c();
                Object[] objArr = new Object[1];
                g(new int[]{-367788474, -318143655, -1693466520, 1033861789, -1686433196, 1052337887, -1357845204, -346338068, 1835429913, -338382873, 915370347, 1406071832, -341889571, -1827434074}, 26 - TextUtils.lastIndexOf("", '0'), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                g(new int[]{824806927, 1617081418, 1876789362, -428753354, -1704263537, 1197932899, -388945519, -1787204285, 663532314, 391567729, -1657634596, -1968964712, 880297287, -225861056, -1014247237, -643550125, -2013862397, -498354571, -446716300, 1092299135, -437395893, 1555645783, 721926515, 177500564, 474795150, 1060703595, 1978939048, 1609235081, -1520215704, 1557311686, -1388026906, -1469033807, -1555834557, -236557150, -907989563, -1031429050, 1498211431, -1645688421}, 76 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                b.this.transactionFlowLock.c(false);
            }
        }

        final void b() {
            int i2 = f + Opcodes.DNEG;
            i = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    this.a.postDelayed(new Runnable() { // from class: o.dj.b$b$$ExternalSyntheticLambda1
                        @Override // java.lang.Runnable
                        public final void run() {
                            b.C0037b.this.a();
                        }
                    }, 3000L);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    Runnable runnable = new Runnable() { // from class: o.dj.b$b$$ExternalSyntheticLambda1
                        @Override // java.lang.Runnable
                        public final void run() {
                            b.C0037b.this.a();
                        }
                    };
                    switch (this.a.postDelayed(runnable, 3000L) ? '?' : 'T') {
                        case Opcodes.BASTORE /* 84 */:
                            return;
                        default:
                            this.c = runnable;
                            int i3 = f + Opcodes.DSUB;
                            i = i3 % 128;
                            int i4 = i3 % 2;
                            return;
                    }
            }
        }

        final boolean d() {
            g.c();
            Object[] objArr = new Object[1];
            g(new int[]{-367788474, -318143655, -1693466520, 1033861789, -1686433196, 1052337887, -1357845204, -346338068, 1835429913, -338382873, 915370347, 1406071832, -341889571, -1827434074}, 'K' - AndroidCharacter.getMirror('0'), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g(new int[]{824806927, 1617081418, 1876789362, -428753354, -1704263537, 1197932899, -388945519, -1787204285, 663532314, 391567729, -1657634596, -1968964712, 34575864, 1183889079, 229768989, 305811508, -1558338188, -733585362, 72696020, 1132308727, 239031000, -872983397, -727897428, 1455279304, -1098842752, 1083708026, 1460034889, 671062912, -362391668, 1846494711}, 56 - ImageFormat.getBitsPerPixel(0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            Runnable runnable = this.c;
            switch (runnable != null) {
                case false:
                    break;
                default:
                    int i2 = i + 3;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                    this.a.removeCallbacks(runnable);
                    this.c = null;
                    break;
            }
            Runnable runnable2 = this.e;
            if (runnable2 == null) {
                return true;
            }
            int i4 = f + 23;
            i = i4 % 128;
            int i5 = i4 % 2;
            this.a.removeCallbacks(runnable2);
            this.e = null;
            int i6 = f + 73;
            i = i6 % 128;
            switch (i6 % 2 != 0 ? 'E' : (char) 11) {
                case 11:
                    return false;
                default:
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void g(int[] r24, int r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 994
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.dj.b.C0037b.g(int[], int, java.lang.Object[]):void");
        }
    }

    public TransactionDecision onTransactionFinalization(Context context, CustomerAuthenticationMethod customerAuthenticationMethod, Date date, HceTransaction hceTransaction) {
        int i2 = i + 19;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        int i5 = i3 + 83;
        i = i5 % 128;
        switch (i5 % 2 == 0 ? 'Q' : Typography.less) {
            case '<':
                return null;
            default:
                int i6 = 72 / 0;
                return null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:119:0x009e, code lost:
    
        r4 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x02b3, code lost:
    
        r3 = o.dj.b.$10 + 63;
        o.dj.b.$11 = r3 % 128;
        r3 = r3 % 2;
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 920
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.b.k(byte, int, short, int, int, java.lang.Object[]):void");
    }

    private static void l(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] charArray;
        char[] cArr;
        int i3;
        char[] charArray2 = str3 != null ? str3.toCharArray() : str3;
        Object obj = null;
        int i4 = 2;
        switch (str2 != null ? (char) 19 : 'K') {
            case 19:
                int i5 = $10 + 91;
                $11 = i5 % 128;
                switch (i5 % 2 == 0 ? 'T' : (char) 25) {
                    case 25:
                        charArray = str2.toCharArray();
                        break;
                    default:
                        str2.toCharArray();
                        obj.hashCode();
                        throw null;
                }
            default:
                charArray = str2;
                break;
        }
        char[] cArr2 = charArray;
        if (str != null) {
            int i6 = $10 + Opcodes.LNEG;
            $11 = i6 % 128;
            int i7 = i6 % 2;
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        o.a.o oVar = new o.a.o();
        int length = cArr2.length;
        char[] cArr3 = new char[length];
        int length2 = charArray2.length;
        char[] cArr4 = new char[length2];
        int i8 = 0;
        System.arraycopy(cArr2, 0, cArr3, 0, length);
        System.arraycopy(charArray2, 0, cArr4, 0, length2);
        cArr3[0] = (char) (cArr3[0] ^ c2);
        cArr4[2] = (char) (cArr4[2] + ((char) i2));
        int length3 = cArr.length;
        char[] cArr5 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i9 = $10 + Opcodes.DDIV;
            $11 = i9 % 128;
            int i10 = i9 % i4;
            try {
                Object[] objArr2 = {oVar};
                Object obj2 = o.e.a.s.get(-429442487);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c(10 - View.combineMeasuredStates(i8, i8), (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 20953), 344 - (ViewConfiguration.getJumpTapTimeout() >> 16));
                    byte b2 = (byte) i8;
                    Object[] objArr3 = new Object[1];
                    m((byte) 11, b2, b2, objArr3);
                    String str4 = (String) objArr3[i8];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i8] = Object.class;
                    obj2 = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj2);
                }
                int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj3 = o.e.a.s.get(-515165572);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(i8, i8) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(i8, i8) == 0L ? 0 : -1)) + 11, (char) ((-1) - Process.getGidForName("")), 206 - TextUtils.lastIndexOf("", '0', i8, i8));
                        byte b3 = (byte) i8;
                        Object[] objArr5 = new Object[1];
                        m((byte) 9, b3, b3, objArr5);
                        String str5 = (String) objArr5[i8];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i8] = Object.class;
                        obj3 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj3);
                    }
                    int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                    int i11 = cArr3[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr4[intValue]);
                        objArr6[1] = Integer.valueOf(i11);
                        objArr6[i8] = oVar;
                        Object obj4 = o.e.a.s.get(-1614232674);
                        if (obj4 == null) {
                            Class cls3 = (Class) o.e.a.c(11 - View.MeasureSpec.makeMeasureSpec(i8, i8), (char) (ViewConfiguration.getJumpTapTimeout() >> 16), 281 - View.resolveSize(i8, i8));
                            byte b4 = (byte) i8;
                            Object[] objArr7 = new Object[1];
                            m((byte) 7, b4, b4, objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr3[intValue2] * 32718), Integer.valueOf(cArr4[intValue])};
                            Object obj5 = o.e.a.s.get(406147795);
                            if (obj5 != null) {
                                i3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 18, (char) (14688 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.DDIV);
                                byte length4 = (byte) $$a.length;
                                byte b5 = (byte) (length4 - 4);
                                Object[] objArr9 = new Object[1];
                                m(length4, b5, b5, objArr9);
                                i3 = 2;
                                obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj5);
                            }
                            cArr4[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                            cArr3[intValue2] = oVar.d;
                            cArr5[oVar.e] = (char) ((((cArr3[intValue2] ^ r5[oVar.e]) ^ (h ^ 6565854932352255525L)) ^ ((int) (j ^ 6565854932352255525L))) ^ ((char) (f ^ 6565854932352255525L)));
                            oVar.e++;
                            i4 = i3;
                            i8 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr5);
    }
}
