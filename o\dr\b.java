package o.dr;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        b = 5114153665284333344L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x003b). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void d(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r8 = r8 * 2
            int r8 = 114 - r8
            int r6 = r6 * 3
            int r6 = r6 + 1
            byte[] r0 = o.dr.b.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1d
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L3b
        L1d:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L21:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L3b:
            int r6 = -r6
            int r6 = r6 + r8
            int r8 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L21
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.b.d(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{31, 57, -118, -60};
        $$b = 93;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0035. Please report as an issue. */
    public static java.lang.String c(java.lang.String r7, java.lang.String r8) {
        /*
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.StringBuilder r7 = r1.append(r7)
            java.lang.StringBuilder r7 = r7.append(r8)
            java.lang.String r7 = r7.toString()
            java.nio.charset.Charset r8 = o.ee.j.c()
            byte[] r7 = r7.getBytes(r8)
            byte[] r7 = o.ec.e.e(r7)
            int r8 = r7.length
            int r1 = o.dr.b.e
            int r1 = r1 + 41
            int r2 = r1 % 128
            o.dr.b.c = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L33
            r1 = 76
            goto L35
        L33:
            r1 = 18
        L35:
            switch(r1) {
                case 18: goto L39;
                default: goto L38;
            }
        L38:
            goto L3a
        L39:
        L3a:
            r1 = 0
            r2 = r1
        L3c:
            if (r2 >= r8) goto L41
            r3 = 63
            goto L43
        L41:
            r3 = 55
        L43:
            switch(r3) {
                case 55: goto L53;
                default: goto L46;
            }
        L46:
            int r3 = o.dr.b.c
            int r3 = r3 + 31
            int r4 = r3 % 128
            o.dr.b.e = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L61
            goto L58
        L53:
            java.lang.String r7 = r0.toString()
            return r7
        L58:
            r3 = r7[r2]
            r4 = r3 | 32376(0x7e78, float:4.5368E-41)
            r5 = 15
            if (r4 >= r5) goto L8f
            goto L69
        L61:
            r3 = r7[r2]
            r4 = r3 & 255(0xff, float:3.57E-43)
            r5 = 16
            if (r4 >= r5) goto L8f
        L69:
            r4 = 37847(0x93d7, float:5.3035E-41)
            int r5 = android.view.View.getDefaultSize(r1, r1)
            int r5 = r5 + r4
            r4 = 1
            java.lang.Object[] r4 = new java.lang.Object[r4]
            java.lang.String r6 = "ᦀ"
            a(r6, r5, r4)
            r4 = r4[r1]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r4 = r0.append(r4)
            r3 = r3 & 255(0xff, float:3.57E-43)
            java.lang.String r3 = java.lang.Integer.toHexString(r3)
            r4.append(r3)
            goto L98
        L8f:
            r3 = r3 & 255(0xff, float:3.57E-43)
            java.lang.String r3 = java.lang.Integer.toHexString(r3)
            r0.append(r3)
        L98:
            int r2 = r2 + 1
            goto L3c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.b.c(java.lang.String, java.lang.String):java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void a(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 504
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.b.a(java.lang.String, int, java.lang.Object[]):void");
    }
}
