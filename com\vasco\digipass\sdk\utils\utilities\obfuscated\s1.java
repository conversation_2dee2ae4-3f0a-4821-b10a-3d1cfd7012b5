package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.security.Permission;
import java.util.HashSet;
import java.util.Set;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s1.smali */
public class s1 extends Permission {
    private final Set<String> b;

    public s1(String str) {
        super(str);
        HashSet hashSet = new HashSet();
        this.b = hashSet;
        hashSet.add(str);
    }

    public boolean equals(Object obj) {
        return (obj instanceof s1) && this.b.equals(((s1) obj).b);
    }

    @Override // java.security.Permission
    public String getActions() {
        return this.b.toString();
    }

    public int hashCode() {
        return this.b.hashCode();
    }

    @Override // java.security.Permission
    public boolean implies(Permission permission) {
        if (!(permission instanceof s1)) {
            return false;
        }
        s1 s1Var = (s1) permission;
        return getName().equals(s1Var.getName()) || this.b.containsAll(s1Var.b);
    }
}
