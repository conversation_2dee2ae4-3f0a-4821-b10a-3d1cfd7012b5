package fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Process;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import o.ee.g;
import o.eg.d;
import o.ep.e;
import o.eq.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\viewmodels\ManageDeviceWalletViewModel.smali */
public abstract class ManageDeviceWalletViewModel extends AndroidViewModel {
    protected final MutableLiveData<List<e>> mockedTokens;
    public final Set<String> persistedTokensSet;
    public final SharedPreferences sharedPreferences;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int a = 0;
    private static int f = 1;
    private static char d = 2549;
    private static char e = 62415;
    private static char c = 53180;
    private static char b = 59342;

    protected abstract String getSharedPrefDeviceWalletKey();

    protected abstract String getTag();

    public ManageDeviceWalletViewModel(Application application) {
        super(application);
        this.mockedTokens = new MutableLiveData<>();
        Context applicationContext = application.getApplicationContext();
        Object[] objArr = new Object[1];
        g("䱡븓\ueb68낚矉橂\uf2d4澩㥞峛\ueb68낚矉橂\uf2d4澩㥞峛䂎\uea62\uf2d4澩⅛ꍯᕖ\ueb0eം\ue6a9ᦀ圄ꃜ鰉卒樾돯辄캤\ue036䁍蹊揀㒁ᾫ㷀ᣀ\uf197鉢왷", 47 - (Process.myTid() >> 22), objArr);
        SharedPreferences sharedPreferences = applicationContext.getSharedPreferences(((String) objArr[0]).intern(), 0);
        this.sharedPreferences = sharedPreferences;
        this.persistedTokensSet = sharedPreferences.getStringSet(getSharedPrefDeviceWalletKey(), new HashSet());
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public androidx.lifecycle.LiveData<java.util.List<o.ep.e>> getMockedTokens() {
        /*
            r6 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.Set<java.lang.String> r1 = r6.persistedTokensSet
            java.util.Iterator r1 = r1.iterator()
        Lc:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L15
            r2 = 53
            goto L16
        L15:
            r2 = 3
        L16:
            switch(r2) {
                case 3: goto L26;
                default: goto L19;
            }
        L19:
            int r2 = fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.f
            int r2 = r2 + 95
            int r3 = r2 % 128
            fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.a = r3
            int r2 = r2 % 2
            if (r2 == 0) goto L38
            goto L38
        L26:
            androidx.lifecycle.MutableLiveData<java.util.List<o.ep.e>> r1 = r6.mockedTokens
            r1.setValue(r0)
            androidx.lifecycle.MutableLiveData<java.util.List<o.ep.e>> r0 = r6.mockedTokens
            int r1 = fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.f
            int r1 = r1 + 21
            int r2 = r1 % 128
            fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.a = r2
            int r1 = r1 % 2
            return r0
        L38:
            java.lang.Object r2 = r1.next()     // Catch: o.eg.d -> L46
            java.lang.String r2 = (java.lang.String) r2     // Catch: o.eg.d -> L46
            o.ep.e r2 = o.eq.b.a(r2)     // Catch: o.eg.d -> L46
            r0.add(r2)     // Catch: o.eg.d -> L46
            goto Lc
        L46:
            r2 = move-exception
            o.ee.g.c()
            java.lang.String r3 = r6.getTag()
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            java.lang.String r5 = "getMockedTokens - Unable to deserialize token: "
            r4.<init>(r5)
            java.lang.String r2 = r2.getMessage()
            java.lang.StringBuilder r2 = r4.append(r2)
            java.lang.String r2 = r2.toString()
            o.ee.g.d(r3, r2)
            goto Lc
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.getMockedTokens():androidx.lifecycle.LiveData");
    }

    public void deleteToken(e eVar) {
        String str;
        Iterator<String> it = this.persistedTokensSet.iterator();
        while (true) {
            switch (it.hasNext()) {
                case true:
                    try {
                        str = it.next();
                    } catch (d e2) {
                        g.c();
                        g.d(getTag(), new StringBuilder("deleteToken - Unable to deserialize token: ").append(e2.getMessage()).toString());
                    }
                    if (b.a(str).c().equals(eVar.c())) {
                        break;
                    }
                default:
                    str = null;
                    break;
            }
        }
        switch (str != null ? (char) 20 : 'V') {
            case Opcodes.SASTORE /* 86 */:
                break;
            default:
                int i = a + 53;
                f = i % 128;
                int i2 = i % 2;
                if (this.persistedTokensSet.remove(str)) {
                    this.sharedPreferences.edit().remove(getSharedPrefDeviceWalletKey()).apply();
                    this.sharedPreferences.edit().putStringSet(getSharedPrefDeviceWalletKey(), this.persistedTokensSet).apply();
                    ArrayList arrayList = new ArrayList();
                    Iterator<String> it2 = this.persistedTokensSet.iterator();
                    while (it2.hasNext()) {
                        int i3 = f + 25;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                        try {
                            arrayList.add(b.a(it2.next()));
                        } catch (d e3) {
                            g.c();
                            g.d(getTag(), new StringBuilder("deleteToken - Unable to deserialize token: ").append(e3.getMessage()).toString());
                        }
                    }
                    this.mockedTokens.setValue(arrayList);
                    break;
                }
                break;
        }
    }

    public void wipeDeviceWalletData() {
        this.sharedPreferences.edit().remove(getSharedPrefDeviceWalletKey()).apply();
        this.mockedTokens.setValue(new ArrayList());
        int i = f + 83;
        a = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x003e, code lost:
    
        if (r1.b >= r0.length) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0040, code lost:
    
        r6 = 7;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0044, code lost:
    
        switch(r6) {
            case 7: goto L24;
            default: goto L74;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0056, code lost:
    
        r5[r3] = r0[r1.b];
        r5[1] = r0[r1.b + 1];
        r6 = 58224;
        r8 = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x006b, code lost:
    
        if (r8 >= 16) goto L75;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x006d, code lost:
    
        r12 = fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.$11 + 97;
        fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.$10 = r12 % 128;
        r12 = r12 % r2;
        r12 = r5[1];
        r13 = r5[r3];
        r9 = (r13 + r6) ^ ((r13 << 4) + ((char) (fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.e ^ 8439748517800462901L)));
        r10 = r13 >>> 5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0092, code lost:
    
        r14 = new java.lang.Object[4];
        r14[3] = java.lang.Integer.valueOf(fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.c);
        r14[r2] = java.lang.Integer.valueOf(r10);
        r14[1] = java.lang.Integer.valueOf(r9);
        r14[r3] = java.lang.Integer.valueOf(r12);
        r9 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00be, code lost:
    
        if (r9 == null) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00c1, code lost:
    
        r9 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getDoubleTapTimeout() >> 16) + 11, (char) android.view.View.MeasureSpec.getSize(r3), 603 - android.view.KeyEvent.keyCodeFromString(""));
        r10 = new java.lang.Class[4];
        r10[r3] = java.lang.Integer.TYPE;
        r10[1] = java.lang.Integer.TYPE;
        r10[r2] = java.lang.Integer.TYPE;
        r10[3] = java.lang.Integer.TYPE;
        r9 = r9.getMethod("C", r10);
        o.e.a.s.put(-1512468642, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0101, code lost:
    
        r2 = ((java.lang.Character) ((java.lang.reflect.Method) r9).invoke(null, r14)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x010e, code lost:
    
        r5[1] = r2;
        r20 = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0126, code lost:
    
        r10 = new java.lang.Object[]{java.lang.Integer.valueOf(r5[r3]), java.lang.Integer.valueOf((r2 + r6) ^ ((r2 << 4) + ((char) (fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.b ^ 8439748517800462901L)))), java.lang.Integer.valueOf(r2 >>> 5), java.lang.Integer.valueOf(fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.d)};
        r2 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0150, code lost:
    
        if (r2 == null) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x01a6, code lost:
    
        r5[0] = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r10)).charValue();
        r6 = r6 - 40503;
        r8 = r8 + 1;
        r4 = r20;
        r2 = 2;
        r3 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0153, code lost:
    
        r2 = ((java.lang.Class) o.e.a.c(10 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0, 0), (char) ((-1) - (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) == 0 ? 0 : -1))), android.text.TextUtils.getOffsetBefore("", 0) + 603)).getMethod("C", java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(-1512468642, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x01b5, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x01b6, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x01ba, code lost:
    
        if (r1 != null) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x01bc, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x01bd, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x01be, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x01bf, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x01c3, code lost:
    
        if (r1 != null) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x01c5, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x01c6, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x01c7, code lost:
    
        r20 = r4;
        r20[r1.b] = r5[0];
        r20[r1.b + 1] = r5[1];
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x01d7, code lost:
    
        r2 = new java.lang.Object[]{r1, r1};
        r3 = o.e.a.s.get(2062727845);
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x01e8, code lost:
    
        if (r3 == null) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x01ea, code lost:
    
        r8 = 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0229, code lost:
    
        ((java.lang.reflect.Method) r3).invoke(null, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x022f, code lost:
    
        r2 = r8;
        r4 = r20;
        r3 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x01ec, code lost:
    
        r8 = 2;
        r3 = ((java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getJumpTapTimeout() >> 16), (char) ((android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 30725), 662 - android.text.AndroidCharacter.getMirror('0'))).getMethod("A", java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(2062727845, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0235, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0236, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x023a, code lost:
    
        if (r1 != null) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x023c, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x023d, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0047, code lost:
    
        r23[0] = new java.lang.String(r4, 0, r22);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x0055, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0042, code lost:
    
        r6 = 'K';
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x001e, code lost:
    
        r0 = r0 + 59;
        fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.$11 = r0 % 128;
        r0 = r0 % 2;
        r0 = r21.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x001c, code lost:
    
        if (r21 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
    
        if (r21 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x002a, code lost:
    
        r0 = r21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x002c, code lost:
    
        r0 = r0;
        r1 = new o.a.i();
        r4 = new char[r0.length];
        r1.b = 0;
        r5 = new char[2];
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 590
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
