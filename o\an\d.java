package o.an;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        a = 1;
        e();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        TextUtils.lastIndexOf("", '0', 0, 0);
        ExpandableListView.getPackedPositionForGroup(0);
        int i = a + Opcodes.LNEG;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        b = 874635333;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.an.d.$$a
            int r8 = r8 * 2
            int r8 = r8 + 107
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r8
            r4 = r2
            r8 = r7
            goto L2e
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            int r6 = r6 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2e:
            int r3 = -r3
            int r7 = r7 + r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.d.f(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{79, 74, -126, -127};
        $$b = 15;
    }

    public static o.eg.b a(o.ee.h hVar) throws o.eg.d {
        g.c();
        Object[] objArr = new Object[1];
        d(1 - TextUtils.indexOf((CharSequence) "", '0', 0), "\u0003ￜ\f\uffff\u0014\u0003\u0006\ufffb\u0003\f\uffff￭\r\r\uffff\f\ufffe\ufffeￛ\u0001\b\u0003\u0006\u0006", TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 25, 159 - MotionEvent.axisFromString(""), true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        d(AndroidCharacter.getMirror('0') - '*', "\u0004\ufff9\u0001\n�\u000b\u000b\u000b�\n￼￼\uffd9\n�\u0006\u0006￡�\u0012\u0001", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 21, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + Opcodes.IF_ICMPGT, true, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.eg.b bVar = new o.eg.b();
        Object[] objArr3 = new Object[1];
        d(4 - (ViewConfiguration.getDoubleTapTimeout() >> 16), "\u000b\u0010\u0007ￓ\u000e", 5 - View.getDefaultSize(0, 0), 152 - (ViewConfiguration.getPressedStateDuration() >> 16), false, objArr3);
        bVar.d(((String) objArr3[0]).intern(), hVar.d());
        Object[] objArr4 = new Object[1];
        d(2 - (Process.myPid() >> 22), "\u000b\u000eￔ\u0007\u0010", View.resolveSizeAndState(0, 0, 0) + 5, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + Opcodes.DCMPG, true, objArr4);
        bVar.d(((String) objArr4[0]).intern(), hVar.a());
        Object[] objArr5 = new Object[1];
        d(4 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), "\u000b\u0006\ufffb\ufff5", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 4, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.GOTO, true, objArr5);
        bVar.d(((String) objArr5[0]).intern(), hVar.c());
        Object[] objArr6 = new Object[1];
        d(View.resolveSize(0, 0) + 4, "\u0007\ufff4\u0007\u0006\ufff8", 5 - TextUtils.indexOf("", ""), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + Opcodes.IF_ACMPNE, true, objArr6);
        bVar.d(((String) objArr6[0]).intern(), hVar.b());
        Object[] objArr7 = new Object[1];
        d(5 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "\ufffe\u0004\u0002\t\ufff3\uffff\u0005", View.resolveSize(0, 0) + 7, 170 - KeyEvent.getDeadChar(0, 0), false, objArr7);
        bVar.d(((String) objArr7[0]).intern(), hVar.e());
        Object[] objArr8 = new Object[1];
        d(TextUtils.getCapsMode("", 0, 0) + 10, "\ufffe�\bￜ\u0005\ufffa\r\f\b\t", ExpandableListView.getPackedPositionChild(0L) + 11, 161 - Gravity.getAbsoluteGravity(0, 0), true, objArr8);
        bVar.d(((String) objArr8[0]).intern(), hVar.h());
        int i = e + 41;
        a = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    private static void d(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        int i4 = $11 + 61;
        $10 = i4 % 128;
        int i5 = i4 % 2;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        o.a.h hVar = new o.a.h();
        char[] cArr3 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            hVar.b = cArr2[hVar.a];
            cArr3[hVar.a] = (char) (i3 + hVar.b);
            int i6 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr3[i6]), Integer.valueOf(b)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(TextUtils.getTrimmedLength("") + 12, (char) TextUtils.indexOf("", "", 0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 459);
                    byte b2 = (byte) (-1);
                    byte b3 = (byte) (b2 + 1);
                    Object[] objArr3 = new Object[1];
                    f(b2, b3, b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr3[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 11, (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 313);
                        byte b4 = (byte) (-1);
                        byte b5 = (byte) (b4 + 1);
                        Object[] objArr5 = new Object[1];
                        f(b4, b5, (byte) (b5 + 1), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        switch (i > 0 ? '(' : (char) 19) {
            case '(':
                hVar.c = i;
                char[] cArr4 = new char[i2];
                System.arraycopy(cArr3, 0, cArr4, 0, i2);
                System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
                int i7 = $11 + Opcodes.DNEG;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                break;
        }
        if (z) {
            char[] cArr5 = new char[i2];
            hVar.a = 0;
            while (hVar.a < i2) {
                cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                try {
                    Object[] objArr6 = {hVar, hVar};
                    Object obj3 = o.e.a.s.get(-1412673904);
                    if (obj3 == null) {
                        Class cls3 = (Class) o.e.a.c((-16777205) - Color.rgb(0, 0, 0), (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 313 - ExpandableListView.getPackedPositionGroup(0L));
                        byte b6 = (byte) (-1);
                        byte b7 = (byte) (b6 + 1);
                        Object[] objArr7 = new Object[1];
                        f(b6, b7, (byte) (b7 + 1), objArr7);
                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj3);
                    }
                    ((Method) obj3).invoke(null, objArr6);
                    int i9 = $10 + Opcodes.LSUB;
                    $11 = i9 % 128;
                    int i10 = i9 % 2;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            cArr3 = cArr5;
        }
        objArr[0] = new String(cArr3);
    }
}
