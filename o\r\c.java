package o.r;

import android.app.KeyguardManager;
import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.hardware.biometrics.BiometricManager;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.security.keystore.KeyGenParameterSpec;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.ProviderException;
import java.security.cert.CertificateException;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\r\c.smali */
public final class c implements f {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] c;
    private static int f;
    private static long i;
    private static int j;
    private int a;
    private byte[] d;
    private int b = 0;
    private int e = 0;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        j = 1;
        b();
        Color.alpha(0);
        ViewConfiguration.getEdgeSlop();
        ViewConfiguration.getTouchSlop();
        TextUtils.getCapsMode("", 0, 0);
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        Process.getThreadPriority(0);
        TextUtils.getTrimmedLength("");
        TextUtils.indexOf("", "", 0, 0);
        Color.argb(0, 0, 0, 0);
        int i2 = j + 43;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? '[' : 'D') {
            case Opcodes.DUP_X2 /* 91 */:
                int i3 = 37 / 0;
                break;
        }
    }

    static void b() {
        char[] cArr = new char[1508];
        ByteBuffer.wrap("X\u0004HWxþi\r\u0019±\tË:\\*úÛ\u0013ËµûÆìk\u009c \u008d\u0013½¨\u00adÏ^sN\u008d\u007f?oM\u001fã\u0000l0\u0096!2ÑFÁîòpâ\u0082, <ß\f@\u001d\u0089m3}VNþ^m¯\u0094¿6\u008f^\u0098úèUùÅÉoÙ@*ð:\u0013\u000b¾\u001bÓkYtòD\u000eU¹¥üµu\u0086å\u0096\u000fç¢÷ÝÇmÐÒ \u00001¿\u0001\u0083\u0011hb\u0088r<B[S\u008a£;\u008cÄ\u009c\u001dì\u007fýÙÍ[Þª¾\u0012®y\u009eÊ\u008f1ÿ\u0098ïáÜUÌÞ, <ß\f@\u001d\u0089m3}VNþ^m¯\u0094¿6\u008f^\u0098úèUùÅÉoÙI*ü:\u0005\u000b´\u001bÃkLtòD\u0003U\u0097¥Ðµz\u0086ê\u0096\tç ÷ÊÇ?Ð\u009b \u001a1ì\u0001Í\u0011sb\u0091r<B\u0017S\u0090£!\u008c¢\u009c\u001aìrýÆÍM¸\u0000¨g\u0098ß\u0089'ù\u0083éùÚKÊÇ,\u009b<ÿ\fB,\u0088<Â\fg\u001d\u0094m2}YNó^A¯\u0084¿=\u008fh\u0098êè\u001aù\u009aÉ*,¢<Ü\f.\u001d\u0093m3}\\Nø^i¯\u008a,\u008c<ï\fA,\u0099<ç\f@\u001dµml}`Nö^n¯\u0085¿-\u008fU\u0098ù,\u008a<â\f>\u001d\u008dm-}\u001dNâ^d¯\u008d¿+\u008fX\u0098õK\n[mkÕz-\n\u0089\u001aó)A9ÍÈcØËè¹ÿO\u008f¢\u009e)®\u008e¾åMH]\u00adGïW\u0090g\rvÊ\u0006f\u0016\u0016%®5$ÄÚÔnä\u0010óñ\u0083\u0005\u0092\u0087, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾èXùÈÉ*ÙZ*ú:\u0019\u000b£\u001bÂkDtïD\t, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾èXùÈÉ$ÙG*à:\\\u000bº\u001bÅk\rtîD\u0012U¶¥Ý$ã4\u0087\u0004:\u0015±e`u\u000bF\u00adV]§É·w\u0087\u0000\u0090µà<ñÀÁVÑ>\"\u00852m\u0003Å\u0013©&\u009a6ô\u0006L\u0017¾g\u001cw'DÍTA¥¡µ\r\u0085v\u0092Èâbó\u0092Ã\u001fÓk È0 \u0001\u0098,®<É\fw\u001d¯m3}CNã^k¯\u008f¿'\u008f^, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾è]ù\u0089É:ÙV*ñ:\u0019\u000b½\u001bÂkDtãD\u0006U®¥Ôµp\u0086¢\u0096Nçè÷\u0098ÇkÐ\u0080 \u001c1©\u008ay\u009a\u0006ª\u009b»\\ËðÛ\u0080è8ø²\tL\u0019ø)\u0086>gN\u0081_\u0011oó\u007f\u0083\u008c#\u009cÀ\u00adz½\u001bÍ\u009dÒ6âÐó#\u0003\u000b\u0013¡ 30ÄAoQAaüv\u000b, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾èXùÈÉ:ÙQ*ü:\u000e\u000bó\u001bßk^t D\tUµ¥Åµ4\u0086ê\u0096\u001bç±÷ÐÇzÐ\u009c \u001d1¥\u0001À\u0011gb\u0089r5BS, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾è]ù\u0086É ÙV*¹:\u001d\u000b¦\u001bÂkEtåD\tU®¥Øµw\u0086ê\u0096\u001aç ÷ÜÇ6ÐÒ D1ì\u0001×\u0011tb\u0088r5,\u0080<Â\fu\u001d\u0087m1}YNó^*¯\u0094¿7\u008f^\u0098ìèUù\u0089É:ÙV*ñ:\u0019\u000b½\u001bÂkDtãD\u0006U®¥Øµ{\u0086å\u0096Nç³÷ÙÇsÐ\u009b \r1¥\u0001×\u0011\u007fbÝr4BBSØ£`\u008c\u0090\u009c2ìQýû\u001c<\fC<Þ-\u0019]µMÅ~}n÷\u009f\t\u008f½¿Ã¨\"ØÄÉTù\u0090éÑ\u001ak\n\u0093;;+X[ÄD\u007ft\u008fe)\u0095_\u0085¨¶g¦\u0080×6ÇT÷ñà\u0007\u0010\u0090\u0001$1^!èR\u0018BìrÎcD\u0093ï¼\u0017¬µÜ\u0082Í~ýüî\u0016\u001e°\u000e\u0085?f/\u008aX>H²xÔir\u0099\u0088\u008a*º\bªÜÛwË\u0080ô7äW\u0014á\u0005g5\u0095&\u007fVQFäw\u0015gë\u0090{\u0080\u001d°û¡\u0006Ñ·ÁÚòQâ³\u0013D\u0003å, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾èXùÈÉ=ÙG*è:\t\u000b¶\u001bÅkYtéD\tU½¥\u0091µ\u007f\u0086î\u0096\u0017ç¶÷ÌÇpÐ\u0080 \f1ì\u0001Â\u0011ab\u009cr9BYR*BUrÈc\u000f\u0013£\u0003Ó0k áÑ\u001fÁ«ñÕæ4\u0096Ò\u0087B·±§ÇT|DÖu4e]\u0015É\ns:Í+\"Û^Ëïøtè\u0081\u0099<\u0089F¹æ, <ß\fB\u001d\u0085m)}YNá^k¯\u0095¿!\u008f_\u0098¾èXùÈÉ\u0000ÙV*ñ:\u0019\u000b¡\u001b\u0096kFtéD\tU¾¥\u0091µ{\u0086í\u0096Nç ÷ÀÇ|Ð\u0097 \u00191¸\u0001Ê\u0011ib\u0093rpB\rS\u008a£b\u008c\u0088\u009c>ì_ýûÍaÞ\u0081.%>\u0019\u000f÷\u001f\u0016h¯x>HTYè©\bº´£&³Y\u0083Ä\u0092\u0003â¯òßÁgÑí \u00130§\u0000Ù\u00178gÞvNF\u00adVÁ¥sµ\u009f\u0084!\u0094YäÅûaËÁÚ7*R:ë\t-\u0019\u0098h\"xWHë_T¯\u008e¾9\u008e\u0005\u009eòí\u001eýµÍÞÜZ,â\u0003\u0010\u0013¤c\u0098r>B®Q\f¡¼±Ü\u0080\u007f\u0090\u0085ç$÷¢ÇÉÖo,¯<À\fv\u001d\u0095m5,¯<À\fv\u001d\u0095m5}\u0010Nº^*¯\u0084¿<\u008fX\u0098ûè\u0005ù\u009cÉ&ÙM*÷,§<É\ft\u001dÆm4}^Nç^\u007f¯\u0095¿d\u008f\u0001\u0098¾Pw@\u0013pºaN\u0011þ\u0001\u009a29\"\u0099ÓUÃîó\u0094ä0\u0094ë\u0085Sµá¥\u0099<L,(\u001cÈ\ri}×m¯^\u001dNÁ¿~¯À\u009fð\u0088\u0017øûé#ÙÀÉ¬:\u0011*å\u001bA\u000b-{²d\u000eTè,¨<Ù\fw\u001d\u008em8}^Nã^c¯\u0082¿%\u008fO\u0098÷è\u001aù\u0086É\u000bÙW*ë:\u001d\u000b§\u001bßkBtîD4U¿¥Òµ{\u0086å\u0096\nç¶÷\u0098Ç{Ð\u0097 \u00051\u00ad\u0001Ú\u0011&b¼r>BSSØ£n\u008c\u008d\u009c?ì\u001eý Í(ÞÂ.b>r\u000fÓ,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001b×kXtôD\u000fU¿¥ßµ`\u0086â\u0096\rç¤÷ÌÇvÐ\u009d \u00071\u0088\u0001Ö\u0011tb\u009cr$B^SÅ£o\u008c·\u009c>ì]ýúÍfÞ\u008b.1>\u0019\u000fø\u001f\u0016hºx,HYY§©;º¿\u008aÐ\u009aYëáû\fÄ¼Ô\u009f$'5©\u0005A\u0016ãfévVN`^\u0004n\u00ad\u007fY\u000fé\u001f\u008d,.<\u008eÍBÝùí\u0083ú'\u008aü\u009bD«ö»\u008eHtX\u009ci>y\u0010\t\u0085\u00164&\u008a72Ç\u000f×ùä/ôÐ\u0085(\u0095\u001b¥½²KB\u0084Sdc\u0016s¢\u0000C\u0010é \u00931\tÁ«î\tþÿ\u008e\u009d\u009fx¯®¼GLö\\\u0087m%}Ñ\ni\u001aå;´+Ð\u001by\n\u008dz=jYYúIZ¸\u0096¨-\u0098W\u008fóÿ(î\u0090Þ\"ÎZ= -H\u001cê\fÆ|ZcïS\u001fB¯²Á¢i\u0091²\u0081\u001cð¹àØÐ&Ç\u009f7\t&¥\u0016ß\u0099g\u0089\t¹\u008f¨EØùÈ\u009bû1,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001bÒkLtôD\u0006Uú¥Õµq\u0086è\u0096\u001cç¼÷ÈÇkÐ\u0097 \r1ì\u0001Ð\u0011sb\u009er3BRSÙ£r\u008c\u0082\u009c.ìRýùÍqÞÏ.x>\u0019DµTÑdxu\u008c\u0005<\u0015X&û6[Ç\u0097×,çVðò\u0080)\u0091\u0091¡#±[B¡RIcësË\u0003M\u001cû,\u001a=²ÍÝÝeîüþ\u0018\u008fý\u009fÃ¯k¸\u008bH\u0002Y§i\u009by$\nÅ\u0013~\u0003\u001a3³\"GR÷B\u0093q0a\u0090\u0090\\\u0080ç°\u009d§9×âÆZöèæ\u0090\u0015j\u0005\u00824 $\u0010T\u008dK6{Æj)\u009a\u000b\u008a´¹x©ÓØyÈ\u001føìï@\u001fÏ\u000ek>\u0018.°]@M÷}\u008dl\u001a\u009c³³C£íÓ\u0089,¼<ß\ff\u001d\u0094m}}^Nø^~¯Á¿%\u008fN\u0098êè\u001dù\u008dÉ!ÙV*ð:\u001f\u000b²\u001bÂkHtäCuS\u0011c¸rL\u0002ü\u0012\u0098!;1\u009bÀWÐìà\u0096÷2\u0087é\u0096Q¦ã¶\u009bEaU\u0089d+t\u000b\u0004\u008d\u001b;+Ú:rÊ\u001dÚ¥é<ùØ,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001bõkBtîD\u0014U®¥Ãµa\u0086è\u0096\u001açª÷ÊÇ?Ð\u0082 \u001b1£\u0001Ó\u0011tb\u0094r5BCSË£s\u008c\u009d\u009c{ì[ýçÍzÞ\u0080.0>\u0019\u000fë\u001f\u001bh³x#H\u0000Yá©\u001fº¥\u008a×\u009aCëçû\u000bÄ¿Ô\u009f$y5ì\u0005\u0015\u0016°fÒvrG\u0082W2 ê°Ê\u0080a\u0091\u0082á~ñ\u0018Â\u0088Òl#\u00833,\u0003Olö|VM×]`,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001bÄkHtñD\u0012U¿¥Âµ`\u0086â\u0096\u0000ç¢÷\u0098ÇtÐ\u0097 \u00101¿\u0001×\u0011ib\u008fr5B\u0017SË£f\u008c\u0085\u009c2ìP,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001bÂkBtïDGU·¥Ðµz\u0086ò\u0096Nç·÷ÝÇnÐ\u0087 \f1¿\u0001×\u0011u, <Á\fs\u001d\u0089m.}CNþ^h¯\u008d¿!\u008f\u001b\u0098êè\u001aùÈÉ+ÙG*ú:\u000e\u000bª\u001bÆkYt D\u000eU´¥Áµa\u0086ÿ\u0096Nç¡÷ÙÇkÐ\u0093 I,\u00ad<É\f`\u001d\u0094m$}@Nã^C¯\u008f¿4\u008fN\u0098êè1ù\u0089É;ÙC*¹:Q\u000bó\u001bùkYtèD\u0002U¨¥\u0091µ\u007f\u0086â\u0096\u0000ç¡÷\u0098ÇpÐ\u0094 I1©\u0001Û\u0011eb\u0098r BCSÃ£n\u008c\u008a\u009c{ì\u0004ýµÍkÞ\u0083.'>X\u000fò\u001f\u001ah¸x*H\u0000Yì©\u001fº¨\u008aÇ\u009a_ëáû\u0017Ä½,º<Ù\fh\u001d\u0096ms}[Nç^n".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1508);
        c = cArr;
        i = 6701220320267943084L;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Type inference failed for: r7v2, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = 105 - r8
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r6 = r6 * 2
            int r6 = 1 - r6
            byte[] r0 = o.r.c.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r7
            r4 = r2
            goto L26
        L14:
            r3 = r2
        L15:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r6) goto L24
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L24:
            r3 = r0[r7]
        L26:
            int r7 = r7 + 1
            int r8 = r8 + r3
            r3 = r4
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.c.h(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 12;
    }

    /* JADX WARN: Removed duplicated region for block: B:7:0x001f A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0020  */
    @Override // o.r.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(android.content.Context r4) {
        /*
            r3 = this;
            int r4 = o.r.c.j
            int r4 = r4 + 61
            int r0 = r4 % 128
            o.r.c.f = r0
            int r4 = r4 % 2
            r0 = 1
            r1 = 0
            if (r4 == 0) goto L1b
            int r4 = android.os.Build.VERSION.SDK_INT
            r2 = 64
            if (r4 < r2) goto L16
            r4 = r0
            goto L17
        L16:
            r4 = r1
        L17:
            switch(r4) {
                case 1: goto L1f;
                default: goto L1a;
            }
        L1a:
            goto L1e
        L1b:
            switch(r1) {
                case 0: goto L1f;
                default: goto L1e;
            }
        L1e:
            goto L20
        L1f:
            return r0
        L20:
            int r4 = o.r.c.j
            int r4 = r4 + 85
            int r0 = r4 % 128
            o.r.c.f = r0
            int r4 = r4 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.c.a(android.content.Context):boolean");
    }

    @Override // o.r.e
    public final boolean e(Context context) {
        switch (Build.VERSION.SDK_INT >= 30 ? '*' : 'a') {
            case '*':
                BiometricManager biometricManager = (BiometricManager) context.getSystemService(BiometricManager.class);
                if (biometricManager == null) {
                    g.c();
                    Object[] objArr = new Object[1];
                    g((char) (ImageFormat.getBitsPerPixel(0) + 29855), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 28 - KeyEvent.getDeadChar(0, 0), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    g((char) ExpandableListView.getPackedPositionGroup(0L), 27 - TextUtils.indexOf((CharSequence) "", '0'), 47 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr2);
                    g.e(intern, ((String) objArr2[0]).intern());
                    return false;
                }
                switch (biometricManager.canAuthenticate(32783) == 0 ? 'c' : (char) 14) {
                    case 14:
                        int i2 = j + 91;
                        f = i2 % 128;
                        if (i2 % 2 == 0) {
                            return false;
                        }
                        throw null;
                    default:
                        int i3 = f + 73;
                        j = i3 % 128;
                        return i3 % 2 != 0;
                }
            default:
                Object[] objArr3 = new Object[1];
                g((char) (37553 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 76 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 8, objArr3);
                KeyguardManager keyguardManager = (KeyguardManager) context.getSystemService(((String) objArr3[0]).intern());
                switch (keyguardManager != null) {
                    case true:
                        boolean isDeviceSecure = keyguardManager.isDeviceSecure();
                        int i4 = j + 73;
                        f = i4 % 128;
                        int i5 = i4 % 2;
                        return isDeviceSecure;
                    default:
                        g.c();
                        Object[] objArr4 = new Object[1];
                        g((char) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 29854), View.resolveSize(0, 0), 28 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr4);
                        String intern2 = ((String) objArr4[0]).intern();
                        Object[] objArr5 = new Object[1];
                        g((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 83 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), Gravity.getAbsoluteGravity(0, 0) + 46, objArr5);
                        g.e(intern2, ((String) objArr5[0]).intern());
                        return false;
                }
        }
    }

    @Override // o.r.e
    public final byte[] c(Context context) throws b {
        int i2 = f + 91;
        j = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((char) (29854 - KeyEvent.normalizeMetaState(0)), TextUtils.indexOf("", "", 0, 0), 28 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) (38055 - TextUtils.lastIndexOf("", '0', 0, 0)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + Opcodes.LOR, 7 - TextUtils.indexOf((CharSequence) "", '0'), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            Object[] objArr3 = new Object[1];
            g((char) (TextUtils.lastIndexOf("", '0') + 1), KeyEvent.keyCodeFromString("") + Opcodes.L2F, 3 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (KeyEvent.getMaxKeyCode() >> 16) + Opcodes.F2L, AndroidCharacter.getMirror('0') - '!', objArr4);
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(intern2, ((String) objArr4[0]).intern());
            if (Build.VERSION.SDK_INT >= 30) {
                Object[] objArr5 = new Object[1];
                g((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + Opcodes.IFLT, 9 - (ViewConfiguration.getScrollBarSize() >> 8), objArr5);
                KeyGenParameterSpec.Builder userAuthenticationRequired = new KeyGenParameterSpec.Builder(((String) objArr5[0]).intern(), 3).setUserAuthenticationRequired(true);
                Object[] objArr6 = new Object[1];
                g((char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) + Opcodes.IF_ICMPLE, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 3, objArr6);
                KeyGenParameterSpec.Builder blockModes = userAuthenticationRequired.setBlockModes(((String) objArr6[0]).intern());
                Object[] objArr7 = new Object[1];
                g((char) Color.alpha(0), 167 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 12, objArr7);
                keyPairGenerator.initialize(blockModes.setEncryptionPaddings(((String) objArr7[0]).intern()).setKeySize(2048).setUserAuthenticationParameters(this.a, 3).build());
            } else {
                Object[] objArr8 = new Object[1];
                g((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 155 - View.MeasureSpec.getMode(0), 9 - (ViewConfiguration.getEdgeSlop() >> 16), objArr8);
                KeyGenParameterSpec.Builder userAuthenticationRequired2 = new KeyGenParameterSpec.Builder(((String) objArr8[0]).intern(), 3).setUserAuthenticationRequired(true);
                Object[] objArr9 = new Object[1];
                g((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 164 - (ViewConfiguration.getPressedStateDuration() >> 16), (Process.myPid() >> 22) + 3, objArr9);
                KeyGenParameterSpec.Builder blockModes2 = userAuthenticationRequired2.setBlockModes(((String) objArr9[0]).intern());
                Object[] objArr10 = new Object[1];
                g((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 166 - ((byte) KeyEvent.getModifierMetaStateMask()), (ViewConfiguration.getEdgeSlop() >> 16) + 12, objArr10);
                keyPairGenerator.initialize(blockModes2.setEncryptionPaddings(((String) objArr10[0]).intern()).setKeySize(2048).setUserAuthenticationValidityDurationSeconds(this.a).build());
                int i4 = j + Opcodes.DREM;
                f = i4 % 128;
                int i5 = i4 % 2;
            }
            KeyPair generateKeyPair = keyPairGenerator.generateKeyPair();
            g.c();
            Object[] objArr11 = new Object[1];
            g((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 29854), Color.rgb(0, 0, 0) + 16777216, 27 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr11);
            String intern3 = ((String) objArr11[0]).intern();
            Object[] objArr12 = new Object[1];
            g((char) (Gravity.getAbsoluteGravity(0, 0) + 26530), 191 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 18 - View.getDefaultSize(0, 0), objArr12);
            g.d(intern3, ((String) objArr12[0]).intern());
            return generateKeyPair.getPublic().getEncoded();
        } catch (InvalidAlgorithmParameterException | NoSuchAlgorithmException | NoSuchProviderException | ProviderException e) {
            throw new b(e.getMessage(), e);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:56:0x0371, code lost:
    
        if (java.lang.Integer.valueOf(r3.getCause().getMessage()).intValue() > 17) goto L102;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:98:0x02c6. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:36:0x025d  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x02d6  */
    /* JADX WARN: Removed duplicated region for block: B:87:0x04ae  */
    /* JADX WARN: Removed duplicated region for block: B:95:0x0289  */
    @Override // o.r.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean c() {
        /*
            Method dump skipped, instructions count: 1238
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.c.c():boolean");
    }

    @Override // o.r.e
    public final void e() throws b {
        int i2 = f + 33;
        j = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((char) (29854 - (ViewConfiguration.getTouchSlop() >> 8)), 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 29, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) View.MeasureSpec.makeMeasureSpec(0, 0), (Process.myPid() >> 22) + 772, 5 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            Object[] objArr3 = new Object[1];
            g((char) ((-1) - TextUtils.lastIndexOf("", '0')), 140 - KeyEvent.keyCodeFromString(""), 15 - TextUtils.getOffsetBefore("", 0), objArr3);
            KeyStore keyStore = KeyStore.getInstance(((String) objArr3[0]).intern());
            keyStore.load(null);
            Object[] objArr4 = new Object[1];
            g((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), TextUtils.lastIndexOf("", '0', 0) + Opcodes.IFGE, ImageFormat.getBitsPerPixel(0) + 10, objArr4);
            keyStore.deleteEntry(((String) objArr4[0]).intern());
            int i4 = f + 73;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    int i5 = 9 / 0;
                    return;
                default:
                    return;
            }
        } catch (IOException | KeyStoreException | NoSuchAlgorithmException | CertificateException e) {
            g.c();
            Object[] objArr5 = new Object[1];
            g((char) (ExpandableListView.getPackedPositionChild(0L) + 29855), ViewConfiguration.getPressedStateDuration() >> 16, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 28, objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            g((char) (ViewConfiguration.getEdgeSlop() >> 16), ((byte) KeyEvent.getModifierMetaStateMask()) + UtilitiesSDKConstants.LENGTH_SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER, 17 - ExpandableListView.getPackedPositionGroup(0L), objArr6);
            g.a(intern2, ((String) objArr6[0]).intern(), e);
            throw new b(e.getMessage());
        }
    }

    @Override // o.r.e
    public final void c(byte[] bArr) {
        g.c();
        Object[] objArr = new Object[1];
        g((char) (29854 - View.getDefaultSize(0, 0)), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), KeyEvent.normalizeMetaState(0) + 28, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), 793 - ((byte) KeyEvent.getModifierMetaStateMask()), 12 - (Process.myPid() >> 22), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(o.dk.b.e(bArr)).toString());
        this.d = bArr;
        int i2 = f + 55;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x007f, code lost:
    
        if (android.os.Build.VERSION.SDK_INT == 93) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final byte[] d() throws o.r.b, o.r.d {
        /*
            Method dump skipped, instructions count: 1558
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.c.d():byte[]");
    }

    public final void a(int i2) {
        int i3 = f + 25;
        int i4 = i3 % 128;
        j = i4;
        int i5 = i3 % 2;
        this.a = i2;
        int i6 = i4 + 95;
        f = i6 % 128;
        int i7 = i6 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 594
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.c.g(char, int, int, java.lang.Object[]):void");
    }
}
