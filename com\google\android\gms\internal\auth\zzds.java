package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzds.smali */
final class zzds {
    public int zza;
    public long zzb;
    public Object zzc;
    public final zzek zzd;

    zzds() {
        int i = zzek.zzb;
        throw null;
    }

    zzds(zzek zzekVar) {
        if (zzekVar == null) {
            throw null;
        }
        this.zzd = zzekVar;
    }
}
