package kotlin.jvm.internal;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\internal\InlineMarker.smali */
public class InlineMarker {
    public static void mark(int i) {
    }

    public static void mark(String name) {
    }

    public static void beforeInlineCall() {
    }

    public static void afterInlineCall() {
    }

    public static void finallyStart(int finallyDepth) {
    }

    public static void finallyEnd(int finallyDepth) {
    }
}
