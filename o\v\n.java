package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Pair;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import androidx.work.WorkRequest;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import o.ak.e;
import o.em.e;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\n.smali */
public final class n extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static long l;

    /* renamed from: o, reason: collision with root package name */
    private static char[] f109o;
    private static int t;
    private final boolean h;
    final List<o.es.a<?>> i;
    private final o.er.p m;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        t = 1;
        a();
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getJumpTapTimeout();
        KeyEvent.getModifierMetaStateMask();
        int i = t + Opcodes.LUSHR;
        k = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        char[] cArr = new char[1693];
        ByteBuffer.wrap(",½\u008aÉ`Lßñµrl\u0092Ê\u0006¡£\u001f0ö¤¬Ó\nláþ_m6\u0081ì\u0015K\u0086!7,\u0080\u008aÕ`Cßúµsl Ê\u0000¡´\u001f,ö¹¬Ø\nkáø_d6\u009cì\u0013K\u0088!7\u0098\u008evÞ,S\u008b÷aPØ\u0087¶\u001dm\u008aË)¢ª\u0018åöQ\u00adô\u000biâúX\u001a7\u008eí+D¨\"Ü\u0098KwÔ-v\u0084åb\tÙ\u009d·>n¯Äå¢U\u0019Æ÷x®è\u0004\fã\u0092,»\u008aÎ`CßÏµsl\u009cÊ\u0006¡²\u001f*ö¸,»\u008aÎ`CßÏµsl\u009cÊ\u0006¡²\u001f*ö¸¬\u009d\n\u0002á±_q6\u0090ì\u0013K\u009b!2\u0098¨vÉ,D\u008b³aqØ\u0085¶\u0018m\u0085Ë.¢®\u0018ÒöW\u00adü\u000bhâçX[7\u008eí0D¯\"Ç\u0098Wwø-u\u0084øb]ÙÂ·qn\u008dÄÚ¢\u0007\u0019Ý÷i®ì\u0004\u0011ã\u0092Y20¦îÃDP#Ä\u0099sp¯.\u0012\u0085\u008cc;Ú³°;nTÅÁ£l\u001a¡ð^¯Û\u0005wü\u009dZ$0\u001dïÝEt<å\u009a\u0007q\u0082/\u001a\u0086³\u008d\u0003+vÁû~w\u0014ËÍ$k¾\u0000\n¾\u0092W\u0000\r%«º@\tþÉ\u0097(M«ê#\u0080\u008a9\u0010×q\u008dü*\u000bÀÉy=\u0017 Ì=j\u0096\u0003\u0016¹jWï\fDªÐC_ùã\u00966L\u0088å\u0017\u0083\u007f9ïÖ@\u008cÍ%@Ãåxz\u0016ÉÏ?el\u0003ë¸tV\u0083\u000f\\¥´Byø\u0085\u0091\bOcåí\u008238\u0088Ñ\t\u008fé$\u001fÂ\u0082{_\u0011\u0083Ïæds\u0002Õ»\\Q¸\u000e5z\u008bÜþ6s\u0089ÿãC:¬\u009c6÷\u0082I\u001a \u0088ú\u00ad\\2·\u0081\tA` º#\u001d«w\u0002Î\u0098 ùztÝ\u00837A\u008eµà(;µ\u009d\u001eô\u009eNâ gûÌ]X´×\u000eka¾»\u0000\u0012\u009ft÷Îg!È{EÒÈ4m\u008fòáA8¿\u0092äôdOí¡\u000bøÏR*µ¥\u000f\u0011f\u009c¸â\u0012\u007fuþÏ\r&Ïx Ó 5\u0011\u008c×æH8;\u0093\u00adõ\u001fL\u0081¦.ù¦SGªÄ\fEf-¹Û\u0013Nj\u0093Ì7'²y?Ð\u0099*\u0018\u008c|çù,»\u008aÎ`CßÏµsl\u009cÊ\u0006¡²\u001f*ö¸¬\u009d\n\u0002á±_q6\u0090ì\u0013K\u009b!2\u0098¨vÉ,D\u008b³aqØ\u0085¶\u0018m\u0085Ë.¢®\u0018ÒöW\u00adü\u000bhâçX[7\u008eí0D¯\"Ç\u0098Wwø-u\u0084øb]ÙÂ·qn\u008dÄÚ¢\u0007\u0019Û÷~®ë\u0004\rã\u0084Y 0\u00adî\u0097DM#Ä\u0099=pë.\u001e\u0085ÏcuÚµ°,nOÅØ£m\u001aïðS¯\u0086\u0005\"ü«Z90XïÁEe<£\u009a\u0001q\u0095/\b\u0086µ|>Ú^±ÂogÆì¼\u0018\u001b\u0097ñK¨¾\u0006 ü_[×1gèèFe=\u0088ÞÉx¼\u00921-½G\u0001\u009eî8tSÀíX\u0004Ê^ïøp\u0013Ã\u00ad\u0003Äâ\u001ea¹éÓ@jÚ\u0084»Þ6yÁ\u0093\u0003*÷Dj\u009f÷9\\PÜê \u0004%_\u008eù\u001a\u0010\u0095ª)Åü\u001fB¶ÝÐµj%\u0085\u008aß\u0007v\u008a\u0090/+°E\u0003\u009cõ6¨Puë\u0089\u0005\f\\\u0099ö\u007f\u0011ö«RÂß\u0084\u0001\"tÈùwu\u001dÉÄ&b¼\t\b·\u0090^\u0002\u0004'¢¸I\u000b÷Ë\u009e*D©ã!\u0089\u00880\u0012Þs\u0084þ#}ÉÍp,\u001e\u00adÅ\"c\u0086\n\u0016°\u007f^ð\u0005@£ÓJpð®\u009f9E\u0091ì\t\u008af0óß\r\u0085ô,PÊ«q9\u001f\u008eÆ\rlY\nü±\u007f_È\u0006S¬¤K/ñ\u0080\u0098\u0010FcìÆ\u008bi1ÄØP\u0086»--Ë\u0086r\u0012\u0018\u009dÆ¡mr\u000bÝ²XX¬\u0007/\u00ad\u0099T\nò\u009e\u0098éG5,½\u008aÉ`Lßñµrl\u0092Ê\u0006¡£\u001f0ö¤¬Ó\nláþ_m6\u0081ì\u0015K\u0086!7\u0098\u009evÚ,S\u008båalØ\u0094¶\u001c!6\u0087fmãÒG,\u009d\u008aÉ`Lßñµrl\u0092Ê\u0006¡£\u001f0ö¤¬Ó\n\u000fáò_l6\u009bì\u0013K\u009b!4\u0098¡v\u009f,B\u008büakØ\u0084¶\rm\u0099Ë<¢¦\u0018ßöW\u00adæ\u000b'â³X[7\u0084í9Dá\"ô\u0098@wø-u\u0084äb\u001eÙ\u008e·%nªÄÚ¢I\u0019\u0087÷Y®á\u0004\u0010ã\u0082Y80\u0084îÛDU#\u008b\u0099tpü.Q\u0085\u0097c'Ú²°,n\u0017Å\u008d£|\u001aîð\u0006¯\u008b\u0005#ü«Z\"0XïÜEF<ë\u009a\u001cq\u0093/\f\u0086·|$ÚL±Õo3Æê¼\u0005\u001bÙñ\u0018¨©\u0006.üE[Æ1BèïF`=\u008f\u009b\br³((\u0086@}ÑÛ7²ôh~Ç\u008e½\u001b\u0014¿òd¨A\u0007\u0087ýKTþ2-é\u0091G\u0014>¿\u0094)r\u0017)Ö\u0087Y~½Ôj³\u009ci\u0013À¡¾>þ\u009bXÏ²J\r÷gt¾\u0094\u0018\u0000s¥Í6$¢~ÕØ\t3ô\u008djä\u009d>\u0015\u0099\u009dó2J§¤\u0099þDYú³m\n\u0082d\u000b¿\u009f\u0019:p ÊÙ$Q\u007fàÙ!0µ\u008a]å\u0082??\u0096çðòJF¥þÿsVâ°\u0018\u000b\u0088e#¼¬\u0016ÜpOË\u0081%\\|çÖ\u001c1\u0095\u008b!â\u0082<Ý\u0096Sñ\u008dKr¢úüWW\u0091±!\b´b*¼\u0011\u0017\u008bqzÈè\"\u0000}\u008d×%.\u00ad\u0088$â^=Ú\u0097@îíH\u001a£\u0095ý\nT±®\"\bJcÓ½5\u0014ìn\u0003Éß#\u001ez¯Ô(.C\u0089ÀãD:é\u0094fï\u0089I\u000e µú.TF¯×\t1`òºx\u0015\u0088o\u001dÆ¹ bzGÕ\u0081/M\u0086øà+;\u0097\u0095\u0012ì¹F/ \u0011ûÐU_¬»\u0006la\u009a»\u0015\u0012§l8,\u009d\u008aÉ`Lßñµrl\u0092Ê\u0006¡£\u001f0ö¤¬Ó\n\u000fáò_l6\u009bì\u0013K\u009b!4\u0098¡v\u009f,B\u008büakØ\u0084¶\rm\u0099Ë<¢¦\u0018ßöW\u00adæ\u000b'â³X[7\u0084í9Dá\"þ\u0098@wå-z\u0084ãb\u001cÙ\u0081·%n\u0097ÄÌ¢W\u0019Ì÷5®Ï\u0004\u0013ã\u008eY00®îöDU#Ç\u0099=pæ.\u0002\u0085Ãc!Úµ°<n^Å\u0081£?\u001aÃð\u001f¯\u0084\u00054ü²Z'0TïÜEe<æ\u009a\u0011qª/\f\u0086©|.ÚW±Ào}Æñ¼#\u001b\u0080ñ\u001b¨¸\u0006<ü\u0011[Î1`èôF}=\u0095\u009bJr«(a\u0086Q}ÀÛ7²÷h~Ç\u0091½\u0003\u0014ñò,¨G\u0007\u0087ýLTö2}é\u008bG\u00183;\u0095o\u007fêÀWªÔs4Õ ¾\u0005\u0000\u0096é\u0002³u\u0015©þT@Ê)=óµT=>\u0092\u0087\u0007i93ä\u0094Z~ÍÇ\"©«r?Ô\u009a½\u0000\u0007yéñ²@\u0014\u0081ý\u0015Gý(\"ò\u009f[G=X\u0087æhC2Ü\u009bE}ºÆ'¨\u0083q1Ûj½ñ\u0006jè\u0093±j\u001bµü\"F\u0087/\u0017ñP[ó<a\u0086\u009bo@1¤\u009ae|\u0087Å\u0013¯\u009aqøÚ'¼\u0099\u0005eï¹°\"\u001a\u0092ã\u0014E\u0081/òðzZÃ#@\u0085·n\f0ª\u0099\u000fc\u0088Åñ®fpÛÙW£\u0085\u0004&î½·\u001e\u0019\u009aã·Dh.Æ÷RYÛ\"3\u0084ìm\r7Ç\u0099÷bfÄ\u0091\u00adQwØØ7¢¥\u000bWí\u008a·á\u0018!âêKP-Ûö-X¾ýÒ[\u0086±\u0003\u000e¾d=½Ý\u001bIpìÎ\u007f'ë}\u009cÛ@0½\u008e#çÔ=\\\u009aÔð{Iî§Ðý\rZ³°$\tËgB¼Ö\u001asséÉ\u0090'\u0018|©Úh3ü\u0089\u0014æË<v\u0095®óªI\u000f¦´ü9U§³[\bÔfg¿¢\u0015ªs\rÈ\u0094&=\u007f\u00adÕT2\u008e\u0088uáù?\u0096\u0095Qò\u0090Hr¡®ÿKTÀ²v\u000b¤a&¿\u0000\u0014\u008ar5Ë !\u001c~üÔ}-ú\u008bká\u0011>\u0089\u0094*íµK\u0014 üþOWù\u00adg\u000b*`\u0081¾2\u0017¯m\u0018ÊÛ Qyá×t-^\u008a\u0082à59¼\u0097fìÖJG£°ù`W\t¬\u0086\n4cö¹%\u0016Ál\u0000Åé#iy\u0016Ö\u0084,F\u0085µã18\u0090\u0096KïõE~£\u0010ø\u0093V\u0016¯ò\u0005\u0016bÛ¸@\u0011õokÄï\"\u0080x\u001bÑþ/\f\u0084ÐâE;û\u0091}îÐD\u009a¢\u0012û»Q?®Ò\u0004G]Ê»p\u0010¢n\u009fÄ\u001c\u001dü{\u001cÐÝ.Z\u0087ËÝq:é\u0090\u008aî\u0015Gô\u009d\túªPQ©Ð\u0007d\\Úº\u0094\u0010\u0018i½Ç%\u001c¬z]ÓÌ)zÅ©cý\u0089x6Å\\F\u0085¦#2H\u0097ö\u0004\u001f\u0090Eçã;\bÆ¶Xß¯\u0005'¢¯È\u0000q\u0095\u009f«ÅvbÈ\u0088_1°_9\u0084\u00ad\"\bK\u0092ñë\u001fcDÒâ\u0013\u000b\u0087±oÞ°\u0004\r\u00adÕËÑqt\u009eÏÄBmÜ\u008b 0¯^\u001c\u0087Ù-ÃK\u007fðò\u001eLGÒí\u001f\n½°\u0015Ù\u0094\u0007ð\u00adeÊðpE\u0099ßÇel¾\u008a\u00123\u009dYZ\u0087{,¹JEóÀ\u0019+F½ìO\u0015Í³\u000bÙa\u0006þ¬KÕ\u0097s\u0017\u0098¶Æ1o\u0080\u0095\u001a3bXá\u0086^/\u009fU\u0013ò¨\u0018-A\u0080ï\u0014\u0015a²·Ø@\u0001Ý¯YÔïr\u000f\u009b\u008eÁ\u0019oh\u0094ò2J[Ù\u0081F.çT\u000fý\u008c\u001b\u001aAdîÉ\u0014r½ÁÛ\\\u0000ë®8×\u0092}\u0002\u009bwÀ\u00adnq\u0097Æ=OZå\u00805)\u0084WSü\u0093\u001aú@uéÇ\u0017\u0015¼¦Ú\"\u0003Ã©\u001aÖ\u009a|å\u009aw3\u0084\u0095Ð\u007fUÀèªks\u008bÕ\u001f¾º\u0000)é½³Ê\u0015\u0016þë@u)\u0082ó\nT\u0082>-\u0087¸i\u00863[\u0094å~rÇ\u009d©\u0014r\u0080Ô%½¿\u0007ÆéN²ÿ\u0014>ýªGB(\u009dò [ø=ü\u0087Yhâ2o\u009bñ}\rÆ\u0082¨1qôÛí½R\u0006Õèp±à\u001b2ü\u0090F8/¹ñÝ[H<Ý\u0086hoò1H\u009a\u0093|?Å°¯wqVÚ\u0094¼h\u0005íï\u0006°\u0090\u001abãàE&/LðÓZf#º\u0085:n\u009b0\u001c\u0099\u00adc7ÅO®ÌpsÙ²£>\u0004\u0085î\u0000·\u00ad\u00199ãLD\u009a.m÷ðYt\"Â\u0084\"m£74\u0099EbßÄg\u00adôwkØÊ¢\"\u000b¡í7·I\u0018äâ_Kì-qöÆX\u0015!¿\u008b/mZ6\u0080\u0098\\aëËb¬Èv\u0018ß©¡~\n¾ì×¶X\u001fêá8J\u008b,\u000fõî_7 ·\u008aÈlZ,\u009d\u008aÉ`Lßñµrl\u0092Ê\u0006¡£\u001f0ö¤¬Ó\n\u000fáò_l6\u009bì\u0013K\u009b!4\u0098¡v\u009f,B\u008büakØ\u0084¶\rm\u0099Ë<¢¦\u0018ßöW\u00adæ\u000b'â³X[7\u0084í9Dá\"å\u0098@wû-v\u0084èb\u0014Ù\u009b·(níÄá¢N\u0019Ä÷~®×\u0004\u0010ã\u008fY60åîÞDJ#Å\u0099:pû.Q\u0085\u008dc Ú«°%n\u0017Å\u008d£k\u001aéð\u0016¯\u008b\u0005wü\u008fZ.0QïÀEr<ê\u009a\u0001q\u009e/G\u0086\u008b|(ÚM±Èo|Æá¼W\u001b\u0094ñ\u001e¨®\u0006;ü\u0011[Í1zèóF)=\u0099\u009b\brÿ(/\u0086F}ÉÛ{²¹hjÇ\u008e½O\u0014¦ò&¨Y\u0007Ëý\tTú2~éßG\u0004>º\u00941r_)Ü\u0087Y~½ÔY³\u0094i\u000fÀº¾$\u0015 óÏ©T\u0000±þCU\u009f3\nê´@2?\u009f\u0095Õs]*ô\u0080p\u007f\u009dÕ\b\u008c\u0085j?Áí¿Ð\u0015SÌ³ªS\u0001\u0092ÿ\u0015V\u0084\f>ë¦AÅ?Z\u0096»LF+å\u0081\u001ex\u009fÖ+\u008d\u0095kÛÁW¸ò\u0016jÍã«\u0012\u0002\u0083ø5".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1693);
        f109o = cArr;
        l = 5856886160197651131L;
    }

    static void init$0() {
        $$d = new byte[]{30, 126, 60, -105};
        $$e = 23;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(byte r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.v.n.$$d
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r8 = 105 - r8
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r6 = r6 + 1
            int r4 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = -r6
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.n.v(byte, short, byte, java.lang.Object[]):void");
    }

    static /* synthetic */ o.p.g a(n nVar) {
        int i = k + 33;
        t = i % 128;
        switch (i % 2 != 0) {
            case true:
                return nVar.l();
            default:
                nVar.l();
                throw null;
        }
    }

    static /* synthetic */ void b(n nVar) {
        int i = t + 27;
        k = i % 128;
        int i2 = i % 2;
        nVar.n();
        int i3 = t + Opcodes.LSUB;
        k = i3 % 128;
        switch (i3 % 2 != 0 ? '-' : '*') {
            case '-':
                throw null;
            default:
                return;
        }
    }

    static /* synthetic */ o.p.g c(n nVar) {
        int i = t + 53;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l2 = nVar.l();
        int i3 = t + 17;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return l2;
            default:
                throw null;
        }
    }

    static /* synthetic */ o.p.g d(n nVar) {
        int i = k + 67;
        t = i % 128;
        int i2 = i % 2;
        o.p.g l2 = nVar.l();
        int i3 = t + 71;
        k = i3 % 128;
        int i4 = i3 % 2;
        return l2;
    }

    static /* synthetic */ o.p.g e(n nVar) {
        int i = k + 85;
        t = i % 128;
        int i2 = i % 2;
        o.p.g l2 = nVar.l();
        int i3 = t + 37;
        k = i3 % 128;
        switch (i3 % 2 != 0 ? '9' : 'U') {
            case Opcodes.CASTORE /* 85 */:
                return l2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g i(n nVar) {
        int i = k + 49;
        t = i % 128;
        switch (i % 2 != 0) {
            case true:
                o.p.g l2 = nVar.l();
                int i2 = k + 23;
                t = i2 % 128;
                int i3 = i2 % 2;
                return l2;
            default:
                nVar.l();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g j(n nVar) {
        int i = t + 99;
        k = i % 128;
        switch (i % 2 != 0 ? (char) 11 : ']') {
            case 11:
                nVar.l();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return nVar.l();
        }
    }

    public n(String str, o.eo.e eVar, boolean z, o.er.p pVar, List<o.es.d<?>> list) {
        super(str, eVar);
        this.h = z;
        this.m = pVar;
        ArrayList arrayList = new ArrayList();
        for (o.es.d<?> dVar : list) {
            arrayList.add(new o.es.a(dVar.c(), dVar.d(), Boolean.TRUE, eVar));
        }
        this.i = arrayList;
    }

    @Override // o.p.h
    public final String d() {
        int i = t + Opcodes.LSUB;
        k = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        u((char) Color.argb(0, 0, 0, 0), 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 19 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = k + 79;
        t = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.p.h
    public final void a(final Context context, o.ei.c cVar, final o.h.d dVar) {
        boolean z;
        Object[] objArr = new Object[1];
        u((char) TextUtils.indexOf("", ""), 18 - (ViewConfiguration.getLongPressTimeout() >> 16), View.getDefaultSize(0, 0) + 53, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        u((char) TextUtils.getTrimmedLength(""), KeyEvent.normalizeMetaState(0) + 71, 9 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        final o.em.e j = o.ei.c.c().j();
        final o.ak.e eVar = new o.ak.e(context, new e.d() { // from class: o.v.n.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static long a;
            private static int b;
            private static int f;
            private static int i;
            private static char j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                i = 0;
                f = 1;
                j = (char) 17957;
                b = -2082836173;
                a = 6565854932352255525L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void h(int r7, short r8, byte r9, java.lang.Object[] r10) {
                /*
                    byte[] r0 = o.v.n.AnonymousClass2.$$a
                    int r9 = r9 * 2
                    int r9 = r9 + 1
                    int r8 = 106 - r8
                    int r7 = r7 + 4
                    byte[] r1 = new byte[r9]
                    int r9 = r9 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L18
                    r8 = r7
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    goto L35
                L18:
                    r3 = r2
                L19:
                    int r7 = r7 + 1
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    int r4 = r3 + 1
                    if (r3 != r9) goto L2a
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2a:
                    r3 = r0[r7]
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    r6 = r10
                    r10 = r9
                    r9 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r6
                L35:
                    int r7 = r7 + r9
                    r9 = r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    goto L19
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.n.AnonymousClass2.h(int, short, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{102, 46, -74, -23};
                $$b = Opcodes.ISHL;
            }

            @Override // o.ak.e.d
            public final void e(Map<o.es.b, Object> map, String str, Date date) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                g(TextUtils.getTrimmedLength("") - 350518140, "\ue3b1\ue1a9ᜢ훾囜ᾤ礣ᱵ㹲㬦\uf4ac턞臎鎰䥀뤎\u20ff䚔７蕁ࠛ斧\uf40eꮹ\ue3ca\ueb5e捿ࢰ\u0b7b釣껞㎕寿킳\ue83b箹ڝ㼁‶ﵪ두ꀵ徆\u187f鐎츪颁\u2fd8픢\ue30b荪▘슷", (char) (View.MeasureSpec.getSize(0) + 56022), "蓛ᮄ훫凚", "\u0000\u0000\u0000\u0000", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                g(ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), "캢鲵댇\uef8f슭楳\uddb7೪ੜⅠῤ⦙泳ດ⧝\ue2e2沈\uf791纨繺⦩剮ॽ\u1af7┮뫷歇ꅲญʄ甖❩퉯瓹쩰扸숶㈫ᣔ㤝ᗢ\uf504赦혁\udcb9⌚笲盇젾芳栋爛춢㿫\ud82cԷ䱗男\uee68ኘ羆\uf173\ue09f洞牒厹\udf6f뻸顇瘣ꆉꔿ雇邻\ue666㎛澺턫\ueb14覥캋㭺", (char) (Drawable.resolveOpacity(0, 0) + 9567), "⫻䦣徾툥", "\u0000\u0000\u0000\u0000", objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(str).toString());
                ArrayList arrayList = new ArrayList();
                for (o.es.b bVar : o.es.b.values()) {
                    arrayList.add(new o.es.a(map.get(bVar), bVar, Boolean.TRUE, ((d) n.this).n));
                }
                j.a(context, ((d) n.this).n.e(), arrayList, date);
                switch (n.c(n.this) != null ? '-' : 'W') {
                    case '-':
                        int i2 = i + 93;
                        f = i2 % 128;
                        switch (i2 % 2 == 0 ? (char) 7 : 'N') {
                            case 7:
                                n.e(n.this).onProcessSuccess();
                                int i3 = 78 / 0;
                                return;
                            default:
                                n.e(n.this).onProcessSuccess();
                                return;
                        }
                    default:
                        return;
                }
            }

            @Override // o.ak.e.d
            public final void c(o.bb.d dVar2) {
                o.bv.c c = o.bv.c.c(dVar2);
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                g((-350518141) - ExpandableListView.getPackedPositionChild(0L), "\ue3b1\ue1a9ᜢ훾囜ᾤ礣ᱵ㹲㬦\uf4ac턞臎鎰䥀뤎\u20ff䚔７蕁ࠛ斧\uf40eꮹ\ue3ca\ueb5e捿ࢰ\u0b7b釣껞㎕寿킳\ue83b箹ڝ㼁‶ﵪ두ꀵ徆\u187f鐎츪颁\u2fd8픢\ue30b荪▘슷", (char) (56022 - Color.alpha(0)), "蓛ᮄ훫凚", "\u0000\u0000\u0000\u0000", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                g((-705092123) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "귰嫡⼾꿠≽곧蔀䂷ɯ㾣ህ₤뺶幯쑥䭢躑\uf529飌㌾鸅핒빘ꐊﮆﰗ䅗適䈪롃꾮㵚ᯠ頌\uf8d8鮯굆媻\udc30껣⮅祐㑏푛飗\ud847耩뼌ᦲ剸\u2ef7\ue706ు釣凶⨵Ф", (char) (51587 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), "\ue5b3拉菕㓉", "\u0000\u0000\u0000\u0000", objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(c).toString());
                if (n.a(n.this) != null) {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    g((-350518140) - (ViewConfiguration.getLongPressTimeout() >> 16), "\ue3b1\ue1a9ᜢ훾囜ᾤ礣ᱵ㹲㬦\uf4ac턞臎鎰䥀뤎\u20ff䚔７蕁ࠛ斧\uf40eꮹ\ue3ca\ueb5e捿ࢰ\u0b7b釣껞㎕寿킳\ue83b箹ڝ㼁‶ﵪ두ꀵ徆\u187f鐎츪颁\u2fd8픢\ue30b荪▘슷", (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 56021), "蓛ᮄ훫凚", "\u0000\u0000\u0000\u0000", objArr5);
                    String intern3 = ((String) objArr5[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr6 = new Object[1];
                    g(TextUtils.lastIndexOf("", '0') + 1, "꩟㭳\ua87a澮鲮렄\ue262\u20c1\udafe雂薅쾀络徙輣㤿έ졙嶮遀蜒옖ᥔ괷ᴝ廏궚坜涏霁꿞苻薀鈲Ⱥ虝핒붔♔楐첰꧌⟵\ue4f8즆ﴃ厶蠎疑㛄匾疯뺅襟Ⅸ㿉沄웰鍀杊ﰺ䟕샸댯\ueac5㘤难⮵䢈׳", (char) (23553 - TextUtils.getCapsMode("", 0, 0)), "돥耏ǋ鹜", "\u0000\u0000\u0000\u0000", objArr6);
                    o.ee.g.e(intern3, sb2.append(((String) objArr6[0]).intern()).append(dVar2.d()).toString());
                    switch (dVar2.d() == o.bb.a.aA ? '1' : (char) 19) {
                        case '1':
                            int i2 = f + 81;
                            i = i2 % 128;
                            switch (i2 % 2 != 0 ? 'J' : '#') {
                                case '#':
                                    n.b(n.this);
                                    n.d(n.this).onAuthenticationDeclined();
                                    return;
                                default:
                                    n.b(n.this);
                                    n.d(n.this).onAuthenticationDeclined();
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                            }
                        default:
                            n.j(n.this).onError(c);
                            int i3 = i + Opcodes.LNEG;
                            f = i3 % 128;
                            int i4 = i3 % 2;
                            return;
                    }
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void g(int r21, java.lang.String r22, char r23, java.lang.String r24, java.lang.String r25, java.lang.Object[] r26) {
                /*
                    Method dump skipped, instructions count: 722
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.n.AnonymousClass2.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
            }
        }, cVar);
        List<o.es.a<?>> i = j.b().i(((d) this).n.e());
        switch (i.isEmpty() ? 'R' : (char) 6) {
            case 6:
                Long j2 = j.b().j(((d) this).n.e());
                switch (j2 != null) {
                    case false:
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        u((char) (41399 - Process.getGidForName("")), View.MeasureSpec.getMode(0) + Opcodes.IF_ICMPGT, KeyEvent.keyCodeFromString("") + 71, objArr3);
                        o.ee.g.d(intern, ((String) objArr3[0]).intern());
                        z = true;
                        break;
                    default:
                        switch (j2.longValue() < System.currentTimeMillis() - WorkRequest.MIN_BACKOFF_MILLIS ? 'N' : '8') {
                            case 'N':
                                o.ee.g.c();
                                Object[] objArr4 = new Object[1];
                                u((char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 22064), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 234, 84 - ExpandableListView.getPackedPositionChild(0L), objArr4);
                                o.ee.g.d(intern, ((String) objArr4[0]).intern());
                                z = true;
                                break;
                            default:
                                z = false;
                                break;
                        }
                }
            default:
                int i2 = t + Opcodes.LSHL;
                k = i2 % 128;
                if (i2 % 2 == 0) {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    u((char) KeyEvent.keyCodeFromString(""), TextUtils.lastIndexOf("", '0', 0, 0) + 82, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 82, objArr5);
                    o.ee.g.d(intern, ((String) objArr5[0]).intern());
                    z = true;
                    break;
                } else {
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    u((char) KeyEvent.keyCodeFromString(""), TextUtils.lastIndexOf("", (char) 24, 0, 1) + 45, 127 / (PointF.length(1.0f, 2.0f) > 2.0f ? 1 : (PointF.length(1.0f, 2.0f) == 2.0f ? 0 : -1)), objArr6);
                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                    z = false;
                    break;
                }
        }
        if (z) {
            o.ee.g.c();
            Object[] objArr7 = new Object[1];
            u((char) (62067 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), ExpandableListView.getPackedPositionChild(0L) + 418, 55 - (Process.myTid() >> 22), objArr7);
            o.ee.g.d(intern, ((String) objArr7[0]).intern());
            try {
                j.b(context, ((d) this).n.e(), new e.c<o.es.a<?>>() { // from class: o.v.n.3
                    public static final byte[] $$a = null;
                    public static final int $$b = 0;
                    private static int $10;
                    private static int $11;
                    private static int d;
                    private static int e;
                    private static int i;

                    static {
                        init$0();
                        $10 = 0;
                        $11 = 1;
                        d = 0;
                        i = 1;
                        e = 874635464;
                    }

                    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
                    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
                    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x003b). Please report as a decompilation issue!!! */
                    /*
                        Code decompiled incorrectly, please refer to instructions dump.
                        To view partially-correct add '--show-bad-code' argument
                    */
                    private static void g(short r6, int r7, short r8, java.lang.Object[] r9) {
                        /*
                            int r7 = r7 * 2
                            int r7 = r7 + 4
                            int r8 = r8 * 2
                            int r8 = 109 - r8
                            int r6 = r6 * 4
                            int r6 = 1 - r6
                            byte[] r0 = o.v.n.AnonymousClass3.$$a
                            byte[] r1 = new byte[r6]
                            int r6 = r6 + (-1)
                            r2 = 0
                            if (r0 != 0) goto L1d
                            r8 = r7
                            r3 = r1
                            r4 = r2
                            r7 = r6
                            r1 = r0
                            r0 = r9
                            r9 = r8
                            goto L3b
                        L1d:
                            r3 = r2
                            r5 = r8
                            r8 = r7
                            r7 = r5
                        L21:
                            byte r4 = (byte) r7
                            r1[r3] = r4
                            if (r3 != r6) goto L2e
                            java.lang.String r6 = new java.lang.String
                            r6.<init>(r1, r2)
                            r9[r2] = r6
                            return
                        L2e:
                            int r3 = r3 + 1
                            r4 = r0[r8]
                            r5 = r7
                            r7 = r6
                            r6 = r4
                            r4 = r3
                            r3 = r1
                            r1 = r0
                            r0 = r9
                            r9 = r8
                            r8 = r5
                        L3b:
                            int r6 = -r6
                            int r6 = r6 + r8
                            int r8 = r9 + 1
                            r9 = r0
                            r0 = r1
                            r1 = r3
                            r3 = r4
                            r5 = r7
                            r7 = r6
                            r6 = r5
                            goto L21
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.v.n.AnonymousClass3.g(short, int, short, java.lang.Object[]):void");
                    }

                    static void init$0() {
                        $$a = new byte[]{32, 0, 62, 110};
                        $$b = 252;
                    }

                    @Override // o.em.e.c
                    public final void a(List<o.es.a<?>> list) {
                        int i3 = i + 15;
                        d = i3 % 128;
                        int i4 = i3 % 2;
                        Map<o.es.b, Object> b = n.b(list, n.this.i);
                        Pair<Boolean, String> e2 = n.e(b);
                        switch (!((Boolean) e2.first).booleanValue()) {
                            case false:
                                int i5 = i + 93;
                                d = i5 % 128;
                                if (i5 % 2 != 0) {
                                    eVar.c(((d) n.this).n.e(), b, dVar, n.this.o());
                                    int i6 = 29 / 0;
                                } else {
                                    eVar.c(((d) n.this).n.e(), b, dVar, n.this.o());
                                }
                                int i7 = i + 7;
                                d = i7 % 128;
                                switch (i7 % 2 != 0) {
                                    case true:
                                        int i8 = 48 / 0;
                                        return;
                                    default:
                                        return;
                                }
                            default:
                                n.i(n.this).onError(new o.bv.c(AntelopErrorCode.OperationRefused, (String) e2.second));
                                return;
                        }
                    }

                    @Override // o.em.e.c
                    public final void d(AntelopError antelopError) {
                        o.ee.g.c();
                        Object[] objArr8 = new Object[1];
                        f(19 - View.MeasureSpec.getMode(0), "�\u000e\u0003\t\b\uffdd\t\b\u000e\f\t\u0006￪\f\t�\uffff\r\r￣\b\b\uffff\f￭\uffff�\u000f\f\uffff\uffde\u0003\u0001\u0003\u000e\ufffb\u0006\uffdd\ufffb\f\ufffe\uffef\n\ufffe\ufffb\u000e\uffff￮\f\ufffb\b\r\ufffb", MotionEvent.axisFromString("") + 54, 285 - Color.green(0), false, objArr8);
                        String intern2 = ((String) objArr8[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr9 = new Object[1];
                        f(((byte) KeyEvent.getModifierMetaStateMask()) + 19, "\u0007\r\f￡\r\f\u0012\u0010\r\nﾾ\u0003\u0010\u0010\r\u0010ﾾ\uffd8\u0010\u0013\f￮\u0010\r\u0001\u0003\u0011\u0011ﾾￋﾾ\u0010\u0003\u0012\u0010\u0007\u0003\u0014\u0003\ufff2\u0010\uffff\f\u0011\uffff\u0001\u0012", 47 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), ExpandableListView.getPackedPositionType(0L) + 281, false, objArr9);
                        o.ee.g.e(intern2, sb.append(((String) objArr9[0]).intern()).append(antelopError.getMessage()).toString());
                        int i3 = d + Opcodes.DMUL;
                        i = i3 % 128;
                        int i4 = i3 % 2;
                    }

                    private static void f(int i3, String str, int i4, int i5, boolean z2, Object[] objArr8) {
                        char[] cArr;
                        int i6 = $10 + 55;
                        int i7 = i6 % 128;
                        $11 = i7;
                        int i8 = i6 % 2;
                        switch (str != null ? (char) 24 : (char) 22) {
                            case 22:
                                cArr = str;
                                break;
                            default:
                                int i9 = i7 + 3;
                                $10 = i9 % 128;
                                if (i9 % 2 != 0) {
                                    str.toCharArray();
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                                }
                                cArr = str.toCharArray();
                                break;
                        }
                        char[] cArr2 = cArr;
                        o.a.h hVar = new o.a.h();
                        char[] cArr3 = new char[i4];
                        hVar.a = 0;
                        while (hVar.a < i4) {
                            hVar.b = cArr2[hVar.a];
                            cArr3[hVar.a] = (char) (i5 + hVar.b);
                            int i10 = hVar.a;
                            try {
                                Object[] objArr9 = {Integer.valueOf(cArr3[i10]), Integer.valueOf(e)};
                                Object obj2 = o.e.a.s.get(2038615114);
                                if (obj2 == null) {
                                    Class cls = (Class) o.e.a.c(13 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 460);
                                    byte b = $$a[1];
                                    byte b2 = b;
                                    Object[] objArr10 = new Object[1];
                                    g(b, b2, (byte) (b2 + 1), objArr10);
                                    obj2 = cls.getMethod((String) objArr10[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2038615114, obj2);
                                }
                                cArr3[i10] = ((Character) ((Method) obj2).invoke(null, objArr9)).charValue();
                                try {
                                    Object[] objArr11 = {hVar, hVar};
                                    Object obj3 = o.e.a.s.get(-1412673904);
                                    if (obj3 == null) {
                                        Class cls2 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 11, (char) KeyEvent.getDeadChar(0, 0), 314 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)));
                                        byte b3 = $$a[1];
                                        byte b4 = b3;
                                        Object[] objArr12 = new Object[1];
                                        g(b3, b4, b4, objArr12);
                                        obj3 = cls2.getMethod((String) objArr12[0], Object.class, Object.class);
                                        o.e.a.s.put(-1412673904, obj3);
                                    }
                                    ((Method) obj3).invoke(null, objArr11);
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                        switch (i3 > 0 ? '\t' : '/') {
                            case '/':
                                break;
                            default:
                                hVar.c = i3;
                                char[] cArr4 = new char[i4];
                                System.arraycopy(cArr3, 0, cArr4, 0, i4);
                                System.arraycopy(cArr4, 0, cArr3, i4 - hVar.c, hVar.c);
                                System.arraycopy(cArr4, hVar.c, cArr3, 0, i4 - hVar.c);
                                break;
                        }
                        switch (!z2) {
                            case false:
                                char[] cArr5 = new char[i4];
                                hVar.a = 0;
                                while (hVar.a < i4) {
                                    int i11 = $11 + 45;
                                    $10 = i11 % 128;
                                    int i12 = i11 % 2;
                                    cArr5[hVar.a] = cArr3[(i4 - hVar.a) - 1];
                                    try {
                                        Object[] objArr13 = {hVar, hVar};
                                        Object obj4 = o.e.a.s.get(-1412673904);
                                        if (obj4 == null) {
                                            Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 313 - TextUtils.getOffsetBefore("", 0));
                                            byte b5 = $$a[1];
                                            byte b6 = b5;
                                            Object[] objArr14 = new Object[1];
                                            g(b5, b6, b6, objArr14);
                                            obj4 = cls3.getMethod((String) objArr14[0], Object.class, Object.class);
                                            o.e.a.s.put(-1412673904, obj4);
                                        }
                                        ((Method) obj4).invoke(null, objArr13);
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                                cArr3 = cArr5;
                                break;
                        }
                        objArr8[0] = new String(cArr3);
                    }
                });
                return;
            } catch (WalletValidationException e) {
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                u((char) (43194 - TextUtils.getCapsMode("", 0, 0)), Color.green(0) + 472, Color.alpha(0) + 76, objArr8);
                o.ee.g.a(intern, ((String) objArr8[0]).intern(), e);
                return;
            }
        }
        int i3 = t + 79;
        k = i3 % 128;
        int i4 = i3 % 2;
        o.ee.g.c();
        Object[] objArr9 = new Object[1];
        u((char) View.resolveSizeAndState(0, 0, 0), 319 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) + 98, objArr9);
        o.ee.g.d(intern, ((String) objArr9[0]).intern());
        Map<o.es.b, Object> b = b(i, this.i);
        Pair<Boolean, String> e2 = e(b);
        if (!((Boolean) e2.first).booleanValue()) {
            l().onError(new o.bv.c(AntelopErrorCode.OperationRefused, (String) e2.second));
            return;
        }
        eVar.c(((d) this).n.e(), b, dVar, o());
        int i5 = t + 53;
        k = i5 % 128;
        switch (i5 % 2 != 0 ? '*' : '\'') {
            case '*':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.v.d
    final void b_() throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r9 = this;
            int r0 = o.v.n.k
            int r0 = r0 + 91
            int r1 = r0 % 128
            o.v.n.t = r1
            int r0 = r0 % 2
            boolean r0 = r9.h
            r1 = 0
            r3 = 1
            r4 = 0
            if (r0 == 0) goto L84
            java.util.List<o.es.a<?>> r0 = r9.i
            java.util.Iterator r0 = r0.iterator()
        L18:
            boolean r5 = r0.hasNext()
            if (r5 == 0) goto L20
            r5 = r4
            goto L21
        L20:
            r5 = r3
        L21:
            switch(r5) {
                case 1: goto L43;
                default: goto L24;
            }
        L24:
            java.lang.Object r5 = r0.next()
            o.es.a r5 = (o.es.a) r5
            o.er.p r6 = r9.m
            o.er.a[] r6 = r6.i()
            r6 = r6[r4]
            o.er.w r6 = (o.er.w) r6
            o.es.b r5 = r5.e()
            java.lang.Boolean r5 = r6.b(r5)
            boolean r5 = r5.booleanValue()
            if (r5 == 0) goto L59
            goto L44
        L43:
            return
        L44:
            int r5 = o.v.n.k
            int r5 = r5 + 97
            int r6 = r5 % 128
            o.v.n.t = r6
            int r5 = r5 % 2
            if (r5 != 0) goto L53
            r5 = 35
            goto L55
        L53:
            r5 = 98
        L55:
            switch(r5) {
                case 98: goto L58;
                default: goto L58;
            }
        L58:
            goto L18
        L59:
            fr.antelop.sdk.exception.WalletValidationException r0 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r5 = fr.antelop.sdk.exception.WalletValidationErrorCode.Unexpected
            int r6 = android.view.ViewConfiguration.getTouchSlop()
            int r6 = r6 >> 8
            char r6 = (char) r6
            long r7 = android.os.SystemClock.elapsedRealtimeNanos()
            int r1 = (r7 > r1 ? 1 : (r7 == r1 ? 0 : -1))
            int r1 = 1 - r1
            int r2 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r2 = (byte) r2
            int r2 = r2 + 19
            java.lang.Object[] r3 = new java.lang.Object[r3]
            u(r6, r1, r2, r3)
            r1 = r3[r4]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r0.<init>(r5, r1)
            throw r0
        L84:
            fr.antelop.sdk.exception.WalletValidationException r0 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r5 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            int r6 = android.graphics.Color.alpha(r4)
            char r6 = (char) r6
            long r7 = android.view.ViewConfiguration.getZoomControlsTimeout()
            int r1 = (r7 > r1 ? 1 : (r7 == r1 ? 0 : -1))
            int r1 = r1 + 547
            int r2 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r2 = r2 >> 16
            int r2 = 25 - r2
            java.lang.Object[] r3 = new java.lang.Object[r3]
            u(r6, r1, r2, r3)
            r1 = r3[r4]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r0.<init>(r5, r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.n.b_():void");
    }

    public final void b(Context context, o.p.g gVar) throws WalletValidationException {
        int i = k + 17;
        t = i % 128;
        int i2 = i % 2;
        if (!((d) this).n.y()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            u((char) (3515 - Process.getGidForName("")), 574 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 4 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (!this.m.b()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr2 = new Object[1];
            u((char) ExpandableListView.getPackedPositionType(0L), TextUtils.lastIndexOf("", '0', 0) + 549, 25 - TextUtils.getOffsetBefore("", 0), objArr2);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
        }
        d(context, gVar);
        int i3 = k + 67;
        t = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x004f  */
    /* JADX WARN: Removed duplicated region for block: B:4:0x0020  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static java.util.Map<o.es.b, java.lang.Object> b(java.util.List<o.es.a<?>> r5, java.util.List<o.es.a<?>> r6) {
        /*
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            java.util.ArrayList r1 = new java.util.ArrayList
            r1.<init>()
            java.util.Iterator r6 = r6.iterator()
            int r2 = o.v.n.t
            int r2 = r2 + 59
            int r3 = r2 % 128
            o.v.n.k = r3
            int r2 = r2 % 2
        L19:
            boolean r2 = r6.hasNext()
            if (r2 == 0) goto L45
            java.lang.Object r2 = r6.next()
            o.es.a r2 = (o.es.a) r2
            o.es.b r3 = r2.e()
            java.lang.Object r4 = r2.d()
            r0.put(r3, r4)
            o.es.b r2 = r2.e()
            r1.add(r2)
            int r2 = o.v.n.k
            int r2 = r2 + 23
            int r3 = r2 % 128
            o.v.n.t = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L19
            goto L19
        L45:
            java.util.Iterator r5 = r5.iterator()
        L49:
            boolean r6 = r5.hasNext()
            if (r6 == 0) goto L9f
            int r6 = o.v.n.t
            int r6 = r6 + 113
            int r2 = r6 % 128
            o.v.n.k = r2
            int r6 = r6 % 2
            if (r6 == 0) goto L5e
            r6 = 11
            goto L60
        L5e:
            r6 = 22
        L60:
            switch(r6) {
                case 11: goto L74;
                default: goto L63;
            }
        L63:
            java.lang.Object r6 = r5.next()
            o.es.a r6 = (o.es.a) r6
            o.es.b r2 = r6.e()
            boolean r2 = r1.contains(r2)
            if (r2 != 0) goto L9e
            goto L88
        L74:
            java.lang.Object r5 = r5.next()
            o.es.a r5 = (o.es.a) r5
            o.es.b r5 = r5.e()
            r1.contains(r5)
            r5 = 0
            r5.hashCode()     // Catch: java.lang.Throwable -> L86
            throw r5     // Catch: java.lang.Throwable -> L86
        L86:
            r5 = move-exception
            throw r5
        L88:
            o.es.b r2 = r6.e()
            java.lang.Object r6 = r6.d()
            r0.put(r2, r6)
            int r6 = o.v.n.t
            int r6 = r6 + 9
            int r2 = r6 % 128
            o.v.n.k = r2
            int r6 = r6 % 2
        L9e:
            goto L49
        L9f:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.n.b(java.util.List, java.util.List):java.util.Map");
    }

    /* renamed from: o.v.n$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\n$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a = 1;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            e = 0;
            int[] iArr = new int[o.es.b.values().length];
            d = iArr;
            try {
                iArr[o.es.b.p.ordinal()] = 1;
                int i = a;
                int i2 = (i & 63) + (i | 63);
                e = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.es.b.r.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[o.es.b.w.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[o.es.b.u.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                d[o.es.b.x.ordinal()] = 5;
                int i4 = (a + 20) - 1;
                e = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                d[o.es.b.v.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                d[o.es.b.A.ordinal()] = 7;
                int i5 = (a + 114) - 1;
                e = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e8) {
            }
            try {
                d[o.es.b.C.ordinal()] = 8;
            } catch (NoSuchFieldError e9) {
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:153:0x0009, code lost:
    
        continue;
     */
    /* JADX WARN: Code restructure failed: missing block: B:203:0x0009, code lost:
    
        continue;
     */
    /* JADX WARN: Removed duplicated region for block: B:169:0x02f3  */
    /* JADX WARN: Removed duplicated region for block: B:173:0x02fd  */
    /* JADX WARN: Removed duplicated region for block: B:178:0x0336 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:179:0x0337 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:185:0x0303 A[ADDED_TO_REGION, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:187:0x02f7 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static android.util.Pair<java.lang.Boolean, java.lang.String> e(java.util.Map<o.es.b, java.lang.Object> r11) {
        /*
            Method dump skipped, instructions count: 910
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.n.e(java.util.Map):android.util.Pair");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 730
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.n.u(char, int, int, java.lang.Object[]):void");
    }
}
