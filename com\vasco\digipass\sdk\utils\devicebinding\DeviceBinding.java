package com.vasco.digipass.sdk.utils.devicebinding;

import android.content.Context;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.f;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.g;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.h;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.j;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

@Metadata(d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\bf\u0018\u0000 \u00052\u00020\u0001:\u0002\u0005\u0006J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H&¨\u0006\u0007"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding;", "", "fingerprint", "", "salt", "Companion", "FingerprintType", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBinding.smali */
public interface DeviceBinding {

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = Companion.a;
    public static final int MINIMUM_SALT_LENGTH = 64;
    public static final String version = "5.3.0";

    @Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u0018\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T¢\u0006\u0002\n\u0000¨\u0006\r"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding$Companion;", "", "()V", "MINIMUM_SALT_LENGTH", "", "version", "", "createDeviceBinding", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding;", "context", "Landroid/content/Context;", "type", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding$FingerprintType;", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBinding$Companion.smali */
    public static final class Companion {
        public static final int MINIMUM_SALT_LENGTH = 64;
        static final /* synthetic */ Companion a = new Companion();
        public static final String version = "5.3.0";

        @Metadata(k = 3, mv = {1, 7, 1}, xi = 48)
        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBinding$Companion$a.smali */
        public /* synthetic */ class a {
            public static final /* synthetic */ int[] a;

            static {
                int[] iArr = new int[FingerprintType.values().length];
                iArr[FingerprintType.SERIAL.ordinal()] = 1;
                iArr[FingerprintType.ANDROID_ID.ordinal()] = 2;
                iArr[FingerprintType.SERIAL_WITH_ANDROID_ID.ordinal()] = 3;
                iArr[FingerprintType.HARDWARE.ordinal()] = 4;
                a = iArr;
            }
        }

        private Companion() {
        }

        @JvmStatic
        public final DeviceBinding createDeviceBinding(Context context, FingerprintType type) {
            Intrinsics.checkNotNullParameter(context, "context");
            Intrinsics.checkNotNullParameter(type, "type");
            switch (a.a[type.ordinal()]) {
                case 1:
                    return new j(context);
                case 2:
                    return new f(context);
                case 3:
                    return new g(context);
                case 4:
                    return new h(context);
                default:
                    throw new NoWhenBranchMatchedException();
            }
        }
    }

    @Metadata(bv = {}, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0001\u0018\u0000 \u00042\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0005B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t¨\u0006\n"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding$FingerprintType;", "", "<init>", "(Ljava/lang/String;I)V", "Companion", "a", "SERIAL", "ANDROID_ID", "SERIAL_WITH_ANDROID_ID", "HARDWARE", "lib_release"}, k = 1, mv = {1, 7, 1})
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBinding$FingerprintType.smali */
    public enum FingerprintType {
        SERIAL,
        ANDROID_ID,
        SERIAL_WITH_ANDROID_ID,
        HARDWARE;

        public static final String deprecationMsg = "The HARDWARE option should be used instead.";
        public static final String replaceWithExpr = "FingerprintType.HARDWARE";
    }

    @JvmStatic
    static DeviceBinding createDeviceBinding(Context context, FingerprintType fingerprintType) {
        return INSTANCE.createDeviceBinding(context, fingerprintType);
    }

    String fingerprint(String salt) throws DeviceBindingSDKException;
}
