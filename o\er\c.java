package o.er;

import com.esotericsoftware.asm.Opcodes;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\c.smali */
public final class c extends a {
    private static int a = 0;
    private static int b = 1;
    private final boolean c;
    private final String d;

    public c(boolean z, String str, boolean z2) {
        super(z);
        this.d = str;
        this.c = z2;
    }

    public final String e() {
        int i = b + 55;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        String str = this.d;
        int i4 = (i2 ^ 49) + ((i2 & 49) << 1);
        b = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final boolean d() {
        int i = b;
        int i2 = (i & Opcodes.LREM) + (i | Opcodes.LREM);
        int i3 = i2 % 128;
        a = i3;
        Object obj = null;
        switch (i2 % 2 != 0 ? 'F' : '?') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                obj.hashCode();
                throw null;
            default:
                boolean z = this.c;
                int i4 = (i3 + Opcodes.INEG) - 1;
                b = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return z;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }
}
