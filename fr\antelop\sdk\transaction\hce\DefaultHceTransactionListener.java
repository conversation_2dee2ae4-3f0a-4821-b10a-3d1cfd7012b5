package fr.antelop.sdk.transaction.hce;

import android.content.Context;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.transaction.TransactionDecision;
import java.util.Date;
import java.util.List;
import java.util.Map;
import o.dj.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\transaction\hce\DefaultHceTransactionListener.smali */
public class DefaultHceTransactionListener extends b {
    @Override // o.dj.b
    public void onTransactionStart(Context context) {
    }

    @Override // o.dj.b
    public void onTransactionDone(Context context, HceTransaction hceTransaction) {
    }

    @Override // o.dj.b
    public void onCredentialsRequired(Context context, List<CustomerAuthenticationMethod> list, HceTransaction hceTransaction) {
    }

    @Override // o.dj.b
    public void onTransactionError(Context context, AntelopError antelopError) {
    }

    @Override // o.dj.b
    public void onTransactionProgress(Context context, HceTransactionStep hceTransactionStep) {
    }

    @Override // o.dj.b
    public TransactionDecision onTransactionFinalization(Context context, CustomerAuthenticationMethod customerAuthenticationMethod, Date date, HceTransaction hceTransaction) {
        return null;
    }

    @Override // o.dj.b
    public void onTransactionsUpdated(Context context, Map<String, HceTransaction> map) {
    }
}
