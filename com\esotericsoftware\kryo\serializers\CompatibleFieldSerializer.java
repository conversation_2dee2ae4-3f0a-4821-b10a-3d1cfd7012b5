package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.InputChunked;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.io.OutputChunked;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.util.ObjectMap;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\CompatibleFieldSerializer.smali */
public class CompatibleFieldSerializer<T> extends FieldSerializer<T> {
    private static final int binarySearchThreshold = 32;
    private final CompatibleFieldSerializerConfig config;

    public CompatibleFieldSerializer(Kryo kryo, Class type) {
        this(kryo, type, new CompatibleFieldSerializerConfig());
    }

    public CompatibleFieldSerializer(Kryo kryo, Class type, CompatibleFieldSerializerConfig config) {
        super(kryo, type, config);
        this.config = config;
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        Output fieldOutput;
        OutputChunked outputChunked;
        int pop = pushTypeVariables();
        FieldSerializer.CachedField[] fields = this.cachedFields.fields;
        ObjectMap context = kryo.getGraphContext();
        if (!context.containsKey(this)) {
            if (Log.TRACE) {
                Log.trace("kryo", "Write fields for class: " + this.type.getName());
            }
            context.put(this, null);
            output.writeVarInt(fields.length, true);
            int n = fields.length;
            for (int i = 0; i < n; i++) {
                if (Log.TRACE) {
                    Log.trace("kryo", "Write field name: " + fields[i].name + Util.pos(output.position()));
                }
                output.writeString(fields[i].name);
            }
        }
        boolean chunked = this.config.chunked;
        boolean readUnknownTagData = this.config.readUnknownFieldData;
        if (chunked) {
            outputChunked = new OutputChunked(output, this.config.chunkSize);
            fieldOutput = outputChunked;
        } else {
            fieldOutput = output;
            outputChunked = null;
        }
        for (FieldSerializer.CachedField cachedField : fields) {
            if (Log.TRACE) {
                log("Write", cachedField, output.position());
            }
            if (readUnknownTagData) {
                Class valueClass = null;
                if (object != null) {
                    try {
                        Object value = cachedField.field.get(object);
                        if (value != null) {
                            valueClass = value.getClass();
                        }
                    } catch (IllegalAccessException e) {
                    }
                }
                kryo.writeClass(fieldOutput, valueClass);
                if (valueClass == null) {
                    if (chunked) {
                        outputChunked.endChunk();
                    }
                } else {
                    cachedField.setCanBeNull(false);
                    cachedField.setValueClass(valueClass);
                    cachedField.setReuseSerializer(false);
                }
            }
            cachedField.write(fieldOutput, object);
            if (chunked) {
                outputChunked.endChunk();
            }
        }
        popTypeVariables(pop);
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        Input fieldInput;
        InputChunked inputChunked;
        boolean readUnknownTagData;
        int n;
        Kryo kryo2 = kryo;
        int pop = pushTypeVariables();
        T object = create(kryo, input, type);
        kryo2.reference(object);
        FieldSerializer.CachedField[] fields = (FieldSerializer.CachedField[]) kryo.getGraphContext().get(this);
        if (fields == null) {
            fields = readFields(kryo, input);
        }
        FieldSerializer.CachedField[] fields2 = fields;
        boolean chunked = this.config.chunked;
        boolean readUnknownTagData2 = this.config.readUnknownFieldData;
        if (!chunked) {
            fieldInput = input;
            inputChunked = null;
        } else {
            inputChunked = new InputChunked(input, this.config.chunkSize);
            fieldInput = inputChunked;
        }
        int i = 0;
        for (int n2 = fields2.length; i < n2; n2 = n) {
            FieldSerializer.CachedField cachedField = fields2[i];
            FieldSerializer.CachedField[] fields3 = fields2;
            if (readUnknownTagData2) {
                try {
                    Registration registration = kryo2.readClass(fieldInput);
                    if (registration == null) {
                        if (chunked) {
                            inputChunked.nextChunk();
                        }
                        readUnknownTagData = readUnknownTagData2;
                        n = n2;
                    } else {
                        readUnknownTagData = readUnknownTagData2;
                        Class valueClass = registration.getType();
                        if (cachedField == null) {
                            if (Log.TRACE) {
                                n = n2;
                                Log.trace("kryo", "Read unknown data, type: " + Util.className(valueClass) + Util.pos(input.position()));
                            } else {
                                n = n2;
                            }
                            try {
                                kryo2.readObject(fieldInput, valueClass);
                            } catch (KryoException ex) {
                                String message = "Unable to read unknown data, type: " + Util.className(valueClass) + " (" + getType().getName() + "#" + cachedField + ")";
                                if (!chunked) {
                                    throw new KryoException(message, ex);
                                }
                                if (Log.DEBUG) {
                                    Log.debug("kryo", message, ex);
                                }
                            }
                            if (chunked) {
                                inputChunked.nextChunk();
                            }
                        } else {
                            n = n2;
                            if (cachedField.valueClass != null && !Util.isAssignableTo(valueClass, cachedField.field.getType())) {
                                String message2 = "Read type is incompatible with the field type: " + Util.className(valueClass) + " -> " + Util.className(cachedField.valueClass) + " (" + getType().getName() + "#" + cachedField + ")";
                                if (!chunked) {
                                    throw new KryoException(message2);
                                }
                                if (Log.DEBUG) {
                                    Log.debug("kryo", message2);
                                }
                                inputChunked.nextChunk();
                            } else {
                                cachedField.setCanBeNull(false);
                                cachedField.setValueClass(valueClass);
                                cachedField.setReuseSerializer(false);
                            }
                        }
                    }
                } catch (KryoException ex2) {
                    readUnknownTagData = readUnknownTagData2;
                    n = n2;
                    String message3 = "Unable to read unknown data (unknown type). (" + getType().getName() + "#" + cachedField + ")";
                    if (!chunked) {
                        throw new KryoException(message3, ex2);
                    }
                    if (Log.DEBUG) {
                        Log.debug("kryo", message3, ex2);
                    }
                    inputChunked.nextChunk();
                }
                i++;
                kryo2 = kryo;
                fields2 = fields3;
                readUnknownTagData2 = readUnknownTagData;
            } else {
                readUnknownTagData = readUnknownTagData2;
                n = n2;
                if (cachedField == null) {
                    if (!chunked) {
                        throw new KryoException("Unknown field. (" + getType().getName() + ")");
                    }
                    if (Log.TRACE) {
                        Log.trace("kryo", "Skip unknown field.");
                    }
                    inputChunked.nextChunk();
                    i++;
                    kryo2 = kryo;
                    fields2 = fields3;
                    readUnknownTagData2 = readUnknownTagData;
                }
            }
            if (Log.TRACE) {
                log("Read", cachedField, input.position());
            }
            cachedField.read(fieldInput, object);
            if (chunked) {
                inputChunked.nextChunk();
            }
            i++;
            kryo2 = kryo;
            fields2 = fields3;
            readUnknownTagData2 = readUnknownTagData;
        }
        popTypeVariables(pop);
        return object;
    }

    private FieldSerializer.CachedField[] readFields(Kryo kryo, Input input) {
        if (Log.TRACE) {
            Log.trace("kryo", "Read fields for class: " + this.type.getName());
        }
        int length = input.readVarInt(true);
        String[] names = new String[length];
        for (int i = 0; i < length; i++) {
            names[i] = input.readString();
            if (Log.TRACE) {
                Log.trace("kryo", "Read field name: " + names[i]);
            }
        }
        FieldSerializer.CachedField[] fields = new FieldSerializer.CachedField[length];
        FieldSerializer.CachedField[] allFields = this.cachedFields.fields;
        if (length >= 32) {
            int lastFieldIndex = allFields.length - 1;
            for (int i2 = 0; i2 < length; i2++) {
                String schemaName = names[i2];
                int low = 0;
                int high = lastFieldIndex;
                while (true) {
                    if (low <= high) {
                        int mid = (low + high) >>> 1;
                        int compare = schemaName.compareTo(allFields[mid].name);
                        if (compare >= 0) {
                            if (compare > 0) {
                                low = mid + 1;
                            } else {
                                fields[i2] = allFields[mid];
                                break;
                            }
                        } else {
                            high = mid - 1;
                        }
                    } else if (Log.TRACE) {
                        Log.trace("kryo", "Unknown field will be skipped: " + schemaName);
                    }
                }
            }
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                String schemaName2 = names[i3];
                int ii = 0;
                int nn = allFields.length;
                while (true) {
                    if (ii < nn) {
                        if (!allFields[ii].name.equals(schemaName2)) {
                            ii++;
                        } else {
                            fields[i3] = allFields[ii];
                            break;
                        }
                    } else if (Log.TRACE) {
                        Log.trace("kryo", "Unknown field will be skipped: " + schemaName2);
                    }
                }
            }
        }
        kryo.getGraphContext().put(this, fields);
        return fields;
    }

    public CompatibleFieldSerializerConfig getCompatibleFieldSerializerConfig() {
        return this.config;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\CompatibleFieldSerializer$CompatibleFieldSerializerConfig.smali */
    public static class CompatibleFieldSerializerConfig extends FieldSerializer.FieldSerializerConfig {
        boolean chunked;
        boolean readUnknownFieldData = true;
        int chunkSize = 1024;

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.FieldSerializerConfig
        /* renamed from: clone */
        public CompatibleFieldSerializerConfig mo108clone() {
            return (CompatibleFieldSerializerConfig) super.mo108clone();
        }

        public void setReadUnknownFieldData(boolean readUnknownTagData) {
            this.readUnknownFieldData = readUnknownTagData;
        }

        public boolean getReadUnknownTagData() {
            return this.readUnknownFieldData;
        }

        public void setChunkedEncoding(boolean chunked) {
            this.chunked = chunked;
            if (Log.TRACE) {
                Log.trace("kryo", "CompatibleFieldSerializerConfig setChunked: " + chunked);
            }
        }

        public boolean getChunkedEncoding() {
            return this.chunked;
        }

        public void setChunkSize(int chunkSize) {
            this.chunkSize = chunkSize;
            if (Log.TRACE) {
                Log.trace("kryo", "CompatibleFieldSerializerConfig setChunkSize: " + chunkSize);
            }
        }

        public int getChunkSize() {
            return this.chunkSize;
        }
    }
}
