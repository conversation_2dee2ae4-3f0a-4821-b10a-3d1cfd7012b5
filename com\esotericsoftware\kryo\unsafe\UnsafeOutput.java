package com.esotericsoftware.kryo.unsafe;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Output;
import java.io.OutputStream;
import sun.misc.Unsafe;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\unsafe\UnsafeOutput.smali */
public class UnsafeOutput extends Output {
    public UnsafeOutput() {
    }

    public UnsafeOutput(int bufferSize) {
        this(bufferSize, bufferSize);
    }

    public UnsafeOutput(int bufferSize, int maxBufferSize) {
        super(bufferSize, maxBufferSize);
    }

    public UnsafeOutput(byte[] buffer) {
        this(buffer, buffer.length);
    }

    public UnsafeOutput(byte[] buffer, int maxBufferSize) {
        super(buffer, maxBufferSize);
    }

    public UnsafeOutput(OutputStream outputStream) {
        super(outputStream);
    }

    public UnsafeOutput(OutputStream outputStream, int bufferSize) {
        super(outputStream, bufferSize);
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream
    public void write(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(bArr, j + i, (byte) value);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeByte(byte value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(bArr, j + i, value);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeByte(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(bArr, j + i, (byte) value);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeInt(int value) throws KryoException {
        require(4);
        UnsafeUtil.unsafe.putInt(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, value);
        this.position += 4;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeLong(long value) throws KryoException {
        require(8);
        UnsafeUtil.unsafe.putLong(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, value);
        this.position += 8;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeFloat(float value) throws KryoException {
        require(4);
        UnsafeUtil.unsafe.putFloat(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, value);
        this.position += 4;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeDouble(double value) throws KryoException {
        require(8);
        UnsafeUtil.unsafe.putDouble(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, value);
        this.position += 8;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeShort(int value) throws KryoException {
        require(2);
        UnsafeUtil.unsafe.putShort(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, (short) value);
        this.position += 2;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeChar(char value) throws KryoException {
        require(2);
        UnsafeUtil.unsafe.putChar(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, value);
        this.position += 2;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBoolean(boolean z) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(bArr, j + i, z ? (byte) 1 : (byte) 0);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeInts(int[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.intArrayBaseOffset, array.length << 2);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeLongs(long[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.longArrayBaseOffset, array.length << 3);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeFloats(float[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.floatArrayBaseOffset, array.length << 2);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeDoubles(double[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.doubleArrayBaseOffset, array.length << 3);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeShorts(short[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.shortArrayBaseOffset, array.length << 1);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeChars(char[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.charArrayBaseOffset, array.length << 1);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBooleans(boolean[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.booleanArrayBaseOffset, array.length);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBytes(byte[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.byteArrayBaseOffset + offset, count);
    }

    public void writeBytes(Object from, long offset, int count) throws KryoException {
        int copyCount = Math.min(this.capacity - this.position, count);
        while (true) {
            UnsafeUtil.unsafe.copyMemory(from, offset, this.buffer, this.position + UnsafeUtil.byteArrayBaseOffset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(this.capacity, count);
                require(copyCount);
            } else {
                return;
            }
        }
    }
}
