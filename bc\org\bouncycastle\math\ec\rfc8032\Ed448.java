package bc.org.bouncycastle.math.ec.rfc8032;

import bc.org.bouncycastle.math.ec.rfc7748.X448;
import bc.org.bouncycastle.math.ec.rfc7748.X448Field;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k8;
import java.security.SecureRandom;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448.smali */
public abstract class Ed448 {
    public static final int PREHASH_SIZE = 64;
    public static final int PUBLIC_KEY_SIZE = 57;
    public static final int SECRET_KEY_SIZE = 57;
    public static final int SIGNATURE_SIZE = 114;
    private static final byte[] a = {83, 105, 103, 69, 100, 52, 52, 56};
    private static final int[] b = {-1, -1, -1, -1, -1, -1, -1, -2, -1, -1, -1, -1, -1, -1};
    private static final int[] c = {118276190, 40534716, 9670182, 135141552, 85017403, 259173222, 68333082, 171784774, 174973732, 15824510, 73756743, 57518561, 94773951, 248652241, 107736333, 82941708};
    private static final int[] d = {36764180, 8885695, 130592152, 20104429, 163904957, 30304195, 121295871, 5901357, 125344798, 171541512, 175338348, 209069246, 3626697, 38307682, 24032956, 110359655};
    private static final int[] e = {110141154, 30892124, 160820362, 264558960, 217232225, 47722141, 19029845, 8326902, 183409749, 170134547, 90340180, 222600478, 61097333, 7431335, 198491505, 102372861};
    private static final int[] f = {221945828, 50763449, 132637478, 109250759, 216053960, 61612587, 50649998, 138339097, 98949899, 248139835, 186410297, 126520782, 47339196, 78164062, 198835543, 169622712};
    private static final Object g = new Object();
    private static b[] h;
    private static b[] i;
    private static int[] j;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448$Algorithm.smali */
    public static final class Algorithm {
        public static final int Ed448 = 0;
        public static final int Ed448ph = 1;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448$PublicPoint.smali */
    public static final class PublicPoint {
        final int[] a;

        PublicPoint(int[] iArr) {
            this.a = iArr;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448$b.smali */
    private static class b {
        int[] a;
        int[] b;

        private b() {
            this.a = X448Field.create();
            this.b = X448Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448$c.smali */
    private static class c {
        int[] a;
        int[] b;
        int[] c;

        private c() {
            this.a = X448Field.create();
            this.b = X448Field.create();
            this.c = X448Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed448$d.smali */
    private static class d {
        int[] a;
        int[] b;
        int[] c;
        int[] d;
        int[] e;
        int[] f;
        int[] g;
        int[] h;

        private d() {
            this.a = X448Field.create();
            this.b = X448Field.create();
            this.c = X448Field.create();
            this.d = X448Field.create();
            this.e = X448Field.create();
            this.f = X448Field.create();
            this.g = X448Field.create();
            this.h = X448Field.create();
        }
    }

    private static byte[] a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int[] iArr = new int[28];
        bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr, iArr);
        int[] iArr2 = new int[14];
        bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr2, iArr2);
        int[] iArr3 = new int[14];
        bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr3, iArr3);
        c6.c(14, iArr2, iArr3, iArr);
        byte[] bArr4 = new byte[114];
        bc.org.bouncycastle.math.ec.rfc8032.a.a(iArr, 0, 28, bArr4, 0);
        return bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr4);
    }

    private static boolean b(byte[] bArr) {
        if ((bArr[56] & ByteCompanionObject.MAX_VALUE) != 0) {
            return false;
        }
        int c2 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 52);
        int i2 = b[13] ^ c2;
        for (int i3 = 12; i3 > 0; i3--) {
            int c3 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, i3 * 4);
            if (i2 == 0 && c3 - 2147483648 > b[i3] - 2147483648) {
                return false;
            }
            c2 |= c3;
            i2 |= b[i3] ^ c3;
        }
        int c4 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 0);
        if (c2 != 0 || c4 - 2147483648 > -2147483647) {
            return i2 != 0 || c4 + Integer.MIN_VALUE < (b[0] - 1) + Integer.MIN_VALUE;
        }
        return false;
    }

    private static boolean c(byte[] bArr) {
        if ((bArr[56] & ByteCompanionObject.MAX_VALUE) != 0) {
            return false;
        }
        if (bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 52) != b[13]) {
            return true;
        }
        bc.org.bouncycastle.math.ec.rfc8032.a.a(bArr, 0, new int[14], 0, 14);
        return !c6.d(14, r3, r2);
    }

    public static k8 createPrehash() {
        return a();
    }

    public static void encodePublicPoint(PublicPoint publicPoint, byte[] bArr, int i2) {
        X448Field.encode(publicPoint.a, 16, bArr, i2);
        bArr[(i2 + 57) - 1] = (byte) ((publicPoint.a[0] & 1) << 7);
    }

    public static void generatePrivateKey(SecureRandom secureRandom, byte[] bArr) {
        if (bArr.length != 57) {
            throw new IllegalArgumentException("k");
        }
        secureRandom.nextBytes(bArr);
    }

    public static void generatePublicKey(byte[] bArr, int i2, byte[] bArr2, int i3) {
        k8 a2 = a();
        byte[] bArr3 = new byte[114];
        a2.update(bArr, i2, 57);
        a2.a(bArr3, 0, 114);
        byte[] bArr4 = new byte[57];
        a(bArr3, 0, bArr4);
        a(bArr4, bArr2, i3);
    }

    public static void precompute() {
        int i2;
        synchronized (g) {
            if (j != null) {
                return;
            }
            c[] cVarArr = new c[Opcodes.D2F];
            d dVar = new d();
            b bVar = new b();
            X448Field.copy(c, 0, bVar.a, 0);
            X448Field.copy(d, 0, bVar.b, 0);
            a(bVar, cVarArr, 0, 32, dVar);
            b bVar2 = new b();
            X448Field.copy(e, 0, bVar2.a, 0);
            X448Field.copy(f, 0, bVar2.b, 0);
            a(bVar2, cVarArr, 32, 32, dVar);
            c cVar = new c();
            a(bVar, cVar);
            int i3 = 5;
            c[] cVarArr2 = new c[5];
            for (int i4 = 0; i4 < 5; i4++) {
                cVarArr2[i4] = new c();
            }
            int i5 = 0;
            int i6 = 64;
            while (i5 < i3) {
                int i7 = i6 + 1;
                c cVar2 = new c();
                cVarArr[i6] = cVar2;
                int i8 = 0;
                while (true) {
                    i2 = 1;
                    if (i8 >= i3) {
                        break;
                    }
                    if (i8 == 0) {
                        a(cVar, cVar2);
                    } else {
                        a(cVar, cVar2, dVar);
                    }
                    a(cVar, dVar);
                    a(cVar, cVarArr2[i8]);
                    if (i5 + i8 != 8) {
                        while (i2 < 18) {
                            a(cVar, dVar);
                            i2++;
                        }
                    }
                    i8++;
                    i3 = 5;
                }
                int[] iArr = cVar2.a;
                X448Field.negate(iArr, iArr);
                int i9 = 0;
                i6 = i7;
                while (i9 < 4) {
                    int i10 = i2 << i9;
                    int i11 = 0;
                    while (i11 < i10) {
                        c cVar3 = new c();
                        cVarArr[i6] = cVar3;
                        a(cVarArr[i6 - i10], cVar3);
                        a(cVarArr2[i9], cVarArr[i6], dVar);
                        i11++;
                        i6++;
                    }
                    i9++;
                    i2 = 1;
                }
                i5++;
                i3 = 5;
            }
            a(cVarArr);
            h = new b[32];
            for (int i12 = 0; i12 < 32; i12++) {
                c cVar4 = cVarArr[i12];
                b[] bVarArr = h;
                b bVar3 = new b();
                bVarArr[i12] = bVar3;
                X448Field.mul(cVar4.a, cVar4.c, bVar3.a);
                X448Field.normalize(bVar3.a);
                X448Field.mul(cVar4.b, cVar4.c, bVar3.b);
                X448Field.normalize(bVar3.b);
            }
            i = new b[32];
            for (int i13 = 0; i13 < 32; i13++) {
                c cVar5 = cVarArr[32 + i13];
                b[] bVarArr2 = i;
                b bVar4 = new b();
                bVarArr2[i13] = bVar4;
                X448Field.mul(cVar5.a, cVar5.c, bVar4.a);
                X448Field.normalize(bVar4.a);
                X448Field.mul(cVar5.b, cVar5.c, bVar4.b);
                X448Field.normalize(bVar4.b);
            }
            j = X448Field.createTable(Opcodes.IF_ICMPNE);
            int i14 = 0;
            for (int i15 = 64; i15 < 144; i15++) {
                c cVar6 = cVarArr[i15];
                int[] iArr2 = cVar6.a;
                X448Field.mul(iArr2, cVar6.c, iArr2);
                X448Field.normalize(cVar6.a);
                int[] iArr3 = cVar6.b;
                X448Field.mul(iArr3, cVar6.c, iArr3);
                X448Field.normalize(cVar6.b);
                X448Field.copy(cVar6.a, 0, j, i14);
                int i16 = i14 + 16;
                X448Field.copy(cVar6.b, 0, j, i16);
                i14 = i16 + 16;
            }
        }
    }

    public static void scalarMultBaseXY(X448.Friend friend, byte[] bArr, int i2, int[] iArr, int[] iArr2) {
        if (friend == null) {
            throw new NullPointerException("This method is only for use by X448");
        }
        byte[] bArr2 = new byte[57];
        a(bArr, i2, bArr2);
        c cVar = new c();
        a(bArr2, cVar);
        if (a(cVar) == 0) {
            throw new IllegalStateException();
        }
        X448Field.copy(cVar.a, 0, iArr, 0);
        X448Field.copy(cVar.b, 0, iArr2, 0);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, byte[] bArr3, int i3, int i4, byte[] bArr4, int i5) {
        a(bArr, i2, bArr2, (byte) 0, bArr3, i3, i4, bArr4, i5);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, byte[] bArr3, int i3, byte[] bArr4, int i4) {
        a(bArr, i2, bArr2, (byte) 1, bArr3, i3, 64, bArr4, i4);
    }

    public static boolean validatePublicKeyFull(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 57);
        if (!b(a2)) {
            return false;
        }
        b bVar = new b();
        if (a(a2, false, bVar)) {
            return b(bVar);
        }
        return false;
    }

    public static PublicPoint validatePublicKeyFullExport(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 57);
        if (!b(a2)) {
            return null;
        }
        b bVar = new b();
        if (a(a2, false, bVar) && b(bVar)) {
            return c(bVar);
        }
        return null;
    }

    public static boolean validatePublicKeyPartial(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 57);
        if (b(a2)) {
            return a(a2, false, new b());
        }
        return false;
    }

    public static PublicPoint validatePublicKeyPartialExport(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 57);
        if (!b(a2)) {
            return null;
        }
        b bVar = new b();
        if (a(a2, false, bVar)) {
            return c(bVar);
        }
        return null;
    }

    public static boolean verify(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, int i5) {
        return a(bArr, i2, bArr2, i3, bArr3, (byte) 0, bArr4, i4, i5);
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4) {
        return a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, i4, 64);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, int i5, byte[] bArr5, int i6) {
        a(bArr, i2, bArr2, i3, bArr3, (byte) 0, bArr4, i4, i5, bArr5, i6);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, byte[] bArr5, int i5) {
        a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, i4, 64, bArr5, i5);
    }

    public static boolean verify(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte[] bArr3, int i3, int i4) {
        return a(bArr, i2, publicPoint, bArr2, (byte) 0, bArr3, i3, i4);
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte[] bArr3, int i3) {
        return a(bArr, i2, publicPoint, bArr2, (byte) 1, bArr3, i3, 64);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, k8 k8Var, byte[] bArr3, int i3) {
        byte[] bArr4 = new byte[64];
        if (64 == k8Var.a(bArr4, 0, 64)) {
            a(bArr, i2, bArr2, (byte) 1, bArr4, 0, 64, bArr3, i3);
            return;
        }
        throw new IllegalArgumentException("ph");
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, k8 k8Var) {
        byte[] bArr4 = new byte[64];
        if (64 == k8Var.a(bArr4, 0, 64)) {
            return a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, 0, 64);
        }
        throw new IllegalArgumentException("ph");
    }

    private static boolean a(byte[] bArr) {
        return bArr != null && bArr.length < 256;
    }

    private static int a(b bVar) {
        int[] create = X448Field.create();
        int[] create2 = X448Field.create();
        int[] create3 = X448Field.create();
        X448Field.sqr(bVar.a, create2);
        X448Field.sqr(bVar.b, create3);
        X448Field.mul(create2, create3, create);
        X448Field.add(create2, create3, create2);
        X448Field.mul(create, 39081, create);
        X448Field.subOne(create);
        X448Field.add(create, create2, create);
        X448Field.normalize(create);
        X448Field.normalize(create3);
        return X448Field.isZero(create) & (~X448Field.isZero(create3));
    }

    public static PublicPoint generatePublicKey(byte[] bArr, int i2) {
        k8 a2 = a();
        byte[] bArr2 = new byte[114];
        a2.update(bArr, i2, 57);
        a2.a(bArr2, 0, 114);
        byte[] bArr3 = new byte[57];
        a(bArr2, 0, bArr3);
        c cVar = new c();
        a(bArr3, cVar);
        b bVar = new b();
        a(cVar, bVar);
        if (a(bVar) != 0) {
            return c(bVar);
        }
        throw new IllegalStateException();
    }

    private static PublicPoint c(b bVar) {
        int[] iArr = new int[32];
        X448Field.copy(bVar.a, 0, iArr, 0);
        X448Field.copy(bVar.b, 0, iArr, 16);
        return new PublicPoint(iArr);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, k8 k8Var, byte[] bArr4, int i4) {
        byte[] bArr5 = new byte[64];
        if (64 == k8Var.a(bArr5, 0, 64)) {
            a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr5, 0, 64, bArr4, i4);
            return;
        }
        throw new IllegalArgumentException("ph");
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, k8 k8Var) {
        byte[] bArr3 = new byte[64];
        if (64 == k8Var.a(bArr3, 0, 64)) {
            return a(bArr, i2, publicPoint, bArr2, (byte) 1, bArr3, 0, 64);
        }
        throw new IllegalArgumentException("ph");
    }

    private static void c(c cVar) {
        X448Field.zero(cVar.a);
        X448Field.one(cVar.b);
        X448Field.one(cVar.c);
    }

    private static int a(c cVar) {
        int[] create = X448Field.create();
        int[] create2 = X448Field.create();
        int[] create3 = X448Field.create();
        int[] create4 = X448Field.create();
        X448Field.sqr(cVar.a, create2);
        X448Field.sqr(cVar.b, create3);
        X448Field.sqr(cVar.c, create4);
        X448Field.mul(create2, create3, create);
        X448Field.add(create2, create3, create2);
        X448Field.mul(create2, create4, create2);
        X448Field.sqr(create4, create4);
        X448Field.mul(create, 39081, create);
        X448Field.sub(create, create4, create);
        X448Field.add(create, create2, create);
        X448Field.normalize(create);
        X448Field.normalize(create3);
        X448Field.normalize(create4);
        return X448Field.isZero(create) & (~X448Field.isZero(create3)) & (~X448Field.isZero(create4));
    }

    private static boolean b(b bVar) {
        c cVar = new c();
        b(bVar, cVar);
        return b(cVar);
    }

    private static boolean b(c cVar) {
        X448Field.normalize(cVar.a);
        X448Field.normalize(cVar.b);
        X448Field.normalize(cVar.c);
        return X448Field.isZeroVar(cVar.a) && !X448Field.isZeroVar(cVar.b) && X448Field.areEqualVar(cVar.b, cVar.c);
    }

    private static void b(b bVar, c cVar) {
        byte[] bArr = new byte[447];
        bc.org.bouncycastle.math.ec.rfc8032.c.a(5, bArr);
        c[] cVarArr = new c[8];
        d dVar = new d();
        a(bVar, cVarArr, 0, 8, dVar);
        c(cVar);
        int i2 = 446;
        while (true) {
            byte b2 = bArr[i2];
            if (b2 != 0) {
                a(b2 < 0, cVarArr[(b2 >> 1) ^ (b2 >> 31)], cVar, dVar);
            }
            i2--;
            if (i2 < 0) {
                return;
            } else {
                a(cVar, dVar);
            }
        }
    }

    private static byte[] a(byte[] bArr, int i2, int i3) {
        byte[] bArr2 = new byte[i3];
        System.arraycopy(bArr, i2, bArr2, 0, i3);
        return bArr2;
    }

    private static k8 a() {
        return new bc.org.bouncycastle.crypto.digests.e(256);
    }

    private static boolean a(byte[] bArr, boolean z, b bVar) {
        int i2 = (bArr[56] & ByteCompanionObject.MIN_VALUE) >>> 7;
        X448Field.decode(bArr, bVar.b);
        int[] create = X448Field.create();
        int[] create2 = X448Field.create();
        X448Field.sqr(bVar.b, create);
        X448Field.mul(create, 39081, create2);
        X448Field.negate(create, create);
        X448Field.addOne(create);
        X448Field.addOne(create2);
        if (!X448Field.sqrtRatioVar(create, create2, bVar.a)) {
            return false;
        }
        X448Field.normalize(bVar.a);
        if (i2 == 1 && X448Field.isZeroVar(bVar.a)) {
            return false;
        }
        int[] iArr = bVar.a;
        if (z ^ (i2 != (iArr[0] & 1))) {
            X448Field.negate(iArr, iArr);
            X448Field.normalize(bVar.a);
        }
        return true;
    }

    private static void a(k8 k8Var, byte b2, byte[] bArr) {
        byte[] bArr2 = a;
        int length = bArr2.length;
        int i2 = length + 2;
        int length2 = bArr.length + i2;
        byte[] bArr3 = new byte[length2];
        System.arraycopy(bArr2, 0, bArr3, 0, length);
        bArr3[length] = b2;
        bArr3[length + 1] = (byte) bArr.length;
        System.arraycopy(bArr, 0, bArr3, i2, bArr.length);
        k8Var.update(bArr3, 0, length2);
    }

    private static void a(b bVar, byte[] bArr, int i2) {
        X448Field.encode(bVar.b, bArr, i2);
        bArr[(i2 + 57) - 1] = (byte) ((bVar.a[0] & 1) << 7);
    }

    private static int a(c cVar, byte[] bArr, int i2) {
        b bVar = new b();
        a(cVar, bVar);
        int a2 = a(bVar);
        a(bVar, bArr, i2);
        return a2;
    }

    private static void a(k8 k8Var, byte[] bArr, byte[] bArr2, byte[] bArr3, int i2, byte[] bArr4, byte b2, byte[] bArr5, int i3, int i4, byte[] bArr6, int i5) {
        a(k8Var, b2, bArr4);
        k8Var.update(bArr, 57, 57);
        k8Var.update(bArr5, i3, i4);
        k8Var.a(bArr, 0, bArr.length);
        byte[] b3 = bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr);
        byte[] bArr7 = new byte[57];
        a(b3, bArr7, 0);
        a(k8Var, b2, bArr4);
        k8Var.update(bArr7, 0, 57);
        k8Var.update(bArr3, i2, 57);
        k8Var.update(bArr5, i3, i4);
        k8Var.a(bArr, 0, bArr.length);
        byte[] a2 = a(b3, bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr), bArr2);
        System.arraycopy(bArr7, 0, bArr6, i5, 57);
        System.arraycopy(a2, 0, bArr6, i5 + 57, 57);
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2, byte b2, byte[] bArr3, int i3, int i4, byte[] bArr4, int i5) {
        if (a(bArr2)) {
            k8 a2 = a();
            byte[] bArr5 = new byte[114];
            a2.update(bArr, i2, 57);
            a2.a(bArr5, 0, 114);
            byte[] bArr6 = new byte[57];
            a(bArr5, 0, bArr6);
            byte[] bArr7 = new byte[57];
            a(bArr6, bArr7, 0);
            a(a2, bArr5, bArr6, bArr7, 0, bArr2, b2, bArr3, i3, i4, bArr4, i5);
            return;
        }
        throw new IllegalArgumentException("ctx");
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte b2, byte[] bArr4, int i4, int i5, byte[] bArr5, int i6) {
        if (a(bArr3)) {
            k8 a2 = a();
            byte[] bArr6 = new byte[114];
            a2.update(bArr, i2, 57);
            a2.a(bArr6, 0, 114);
            byte[] bArr7 = new byte[57];
            a(bArr6, 0, bArr7);
            a(a2, bArr6, bArr7, bArr2, i3, bArr3, b2, bArr4, i4, i5, bArr5, i6);
            return;
        }
        throw new IllegalArgumentException("ctx");
    }

    private static boolean a(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte b2, byte[] bArr4, int i4, int i5) {
        if (a(bArr3)) {
            byte[] a2 = a(bArr, i2, 57);
            byte[] a3 = a(bArr, i2 + 57, 57);
            byte[] a4 = a(bArr2, i3, 57);
            if (!c(a2)) {
                return false;
            }
            int[] iArr = new int[14];
            if (!bc.org.bouncycastle.math.ec.rfc8032.c.a(a3, iArr) || !b(a4)) {
                return false;
            }
            b bVar = new b();
            if (!a(a2, true, bVar)) {
                return false;
            }
            b bVar2 = new b();
            if (!a(a4, true, bVar2)) {
                return false;
            }
            k8 a5 = a();
            byte[] bArr5 = new byte[114];
            a(a5, b2, bArr3);
            a5.update(a2, 0, 57);
            a5.update(a4, 0, 57);
            a5.update(bArr4, i4, i5);
            a5.a(bArr5, 0, 114);
            int[] iArr2 = new int[14];
            bc.org.bouncycastle.math.ec.rfc8032.c.b(bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr5), iArr2);
            int[] iArr3 = new int[8];
            int[] iArr4 = new int[8];
            bc.org.bouncycastle.math.ec.rfc8032.c.b(iArr2, iArr3, iArr4);
            bc.org.bouncycastle.math.ec.rfc8032.c.a(iArr, iArr4, iArr);
            c cVar = new c();
            a(iArr, iArr3, bVar2, iArr4, bVar, cVar);
            return b(cVar);
        }
        throw new IllegalArgumentException("ctx");
    }

    private static boolean a(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte b2, byte[] bArr3, int i3, int i4) {
        if (a(bArr2)) {
            byte[] a2 = a(bArr, i2, 57);
            byte[] a3 = a(bArr, i2 + 57, 57);
            if (!c(a2)) {
                return false;
            }
            int[] iArr = new int[14];
            if (!bc.org.bouncycastle.math.ec.rfc8032.c.a(a3, iArr)) {
                return false;
            }
            b bVar = new b();
            if (!a(a2, true, bVar)) {
                return false;
            }
            b bVar2 = new b();
            X448Field.negate(publicPoint.a, bVar2.a);
            X448Field.copy(publicPoint.a, 16, bVar2.b, 0);
            byte[] bArr4 = new byte[57];
            encodePublicPoint(publicPoint, bArr4, 0);
            k8 a4 = a();
            byte[] bArr5 = new byte[114];
            a(a4, b2, bArr2);
            a4.update(a2, 0, 57);
            a4.update(bArr4, 0, 57);
            a4.update(bArr3, i3, i4);
            a4.a(bArr5, 0, 114);
            int[] iArr2 = new int[14];
            bc.org.bouncycastle.math.ec.rfc8032.c.b(bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr5), iArr2);
            int[] iArr3 = new int[8];
            int[] iArr4 = new int[8];
            bc.org.bouncycastle.math.ec.rfc8032.c.b(iArr2, iArr3, iArr4);
            bc.org.bouncycastle.math.ec.rfc8032.c.a(iArr, iArr4, iArr);
            c cVar = new c();
            a(iArr, iArr3, bVar2, iArr4, bVar, cVar);
            return b(cVar);
        }
        throw new IllegalArgumentException("ctx");
    }

    private static void a(c[] cVarArr) {
        int length = cVarArr.length;
        int[] createTable = X448Field.createTable(length);
        int[] create = X448Field.create();
        X448Field.copy(cVarArr[0].c, 0, create, 0);
        X448Field.copy(create, 0, createTable, 0);
        int i2 = 0;
        while (true) {
            i2++;
            if (i2 >= length) {
                break;
            }
            X448Field.mul(create, cVarArr[i2].c, create);
            X448Field.copy(create, 0, createTable, i2 * 16);
        }
        X448Field.invVar(create, create);
        int i3 = i2 - 1;
        int[] create2 = X448Field.create();
        while (i3 > 0) {
            int i4 = i3 - 1;
            X448Field.copy(createTable, i4 * 16, create2, 0);
            X448Field.mul(create2, create, create2);
            X448Field.mul(create, cVarArr[i3].c, create);
            X448Field.copy(create2, 0, cVarArr[i3].c, 0);
            i3 = i4;
        }
        X448Field.copy(create, 0, cVarArr[0].c, 0);
    }

    private static void a(c cVar, b bVar) {
        X448Field.inv(cVar.c, bVar.b);
        X448Field.mul(bVar.b, cVar.a, bVar.a);
        int[] iArr = bVar.b;
        X448Field.mul(iArr, cVar.b, iArr);
        X448Field.normalize(bVar.a);
        X448Field.normalize(bVar.b);
    }

    private static void a(b bVar, c cVar, d dVar) {
        int[] iArr = dVar.b;
        int[] iArr2 = dVar.c;
        int[] iArr3 = dVar.d;
        int[] iArr4 = dVar.e;
        int[] iArr5 = dVar.f;
        int[] iArr6 = dVar.g;
        int[] iArr7 = dVar.h;
        X448Field.sqr(cVar.c, iArr);
        X448Field.mul(bVar.a, cVar.a, iArr2);
        X448Field.mul(bVar.b, cVar.b, iArr3);
        X448Field.mul(iArr2, iArr3, iArr4);
        X448Field.mul(iArr4, 39081, iArr4);
        X448Field.add(iArr, iArr4, iArr5);
        X448Field.sub(iArr, iArr4, iArr6);
        X448Field.add(bVar.b, bVar.a, iArr7);
        X448Field.add(cVar.b, cVar.a, iArr4);
        X448Field.mul(iArr7, iArr4, iArr7);
        X448Field.add(iArr3, iArr2, iArr);
        X448Field.sub(iArr3, iArr2, iArr4);
        X448Field.carry(iArr);
        X448Field.sub(iArr7, iArr, iArr7);
        X448Field.mul(iArr7, cVar.c, iArr7);
        X448Field.mul(iArr4, cVar.c, iArr4);
        X448Field.mul(iArr5, iArr7, cVar.a);
        X448Field.mul(iArr4, iArr6, cVar.b);
        X448Field.mul(iArr5, iArr6, cVar.c);
    }

    private static void a(c cVar, c cVar2, d dVar) {
        int[] iArr = dVar.a;
        int[] iArr2 = dVar.b;
        int[] iArr3 = dVar.c;
        int[] iArr4 = dVar.d;
        int[] iArr5 = dVar.e;
        int[] iArr6 = dVar.f;
        int[] iArr7 = dVar.g;
        int[] iArr8 = dVar.h;
        X448Field.mul(cVar.c, cVar2.c, iArr);
        X448Field.sqr(iArr, iArr2);
        X448Field.mul(cVar.a, cVar2.a, iArr3);
        X448Field.mul(cVar.b, cVar2.b, iArr4);
        X448Field.mul(iArr3, iArr4, iArr5);
        X448Field.mul(iArr5, 39081, iArr5);
        X448Field.add(iArr2, iArr5, iArr6);
        X448Field.sub(iArr2, iArr5, iArr7);
        X448Field.add(cVar.b, cVar.a, iArr8);
        X448Field.add(cVar2.b, cVar2.a, iArr5);
        X448Field.mul(iArr8, iArr5, iArr8);
        X448Field.add(iArr4, iArr3, iArr2);
        X448Field.sub(iArr4, iArr3, iArr5);
        X448Field.carry(iArr2);
        X448Field.sub(iArr8, iArr2, iArr8);
        X448Field.mul(iArr8, iArr, iArr8);
        X448Field.mul(iArr5, iArr, iArr5);
        X448Field.mul(iArr6, iArr8, cVar2.a);
        X448Field.mul(iArr5, iArr7, cVar2.b);
        X448Field.mul(iArr6, iArr7, cVar2.c);
    }

    private static void a(boolean z, b bVar, c cVar, d dVar) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        int[] iArr5 = dVar.b;
        int[] iArr6 = dVar.c;
        int[] iArr7 = dVar.d;
        int[] iArr8 = dVar.e;
        int[] iArr9 = dVar.f;
        int[] iArr10 = dVar.g;
        int[] iArr11 = dVar.h;
        if (z) {
            X448Field.sub(bVar.b, bVar.a, iArr11);
            iArr2 = iArr5;
            iArr = iArr8;
            iArr4 = iArr9;
            iArr3 = iArr10;
        } else {
            X448Field.add(bVar.b, bVar.a, iArr11);
            iArr = iArr5;
            iArr2 = iArr8;
            iArr3 = iArr9;
            iArr4 = iArr10;
        }
        X448Field.sqr(cVar.c, iArr5);
        X448Field.mul(bVar.a, cVar.a, iArr6);
        X448Field.mul(bVar.b, cVar.b, iArr7);
        X448Field.mul(iArr6, iArr7, iArr8);
        X448Field.mul(iArr8, 39081, iArr8);
        X448Field.add(iArr5, iArr8, iArr3);
        X448Field.sub(iArr5, iArr8, iArr4);
        X448Field.add(cVar.b, cVar.a, iArr8);
        X448Field.mul(iArr11, iArr8, iArr11);
        X448Field.add(iArr7, iArr6, iArr);
        X448Field.sub(iArr7, iArr6, iArr2);
        X448Field.carry(iArr);
        X448Field.sub(iArr11, iArr5, iArr11);
        X448Field.mul(iArr11, cVar.c, iArr11);
        X448Field.mul(iArr8, cVar.c, iArr8);
        X448Field.mul(iArr9, iArr11, cVar.a);
        X448Field.mul(iArr8, iArr10, cVar.b);
        X448Field.mul(iArr9, iArr10, cVar.c);
    }

    private static void a(boolean z, c cVar, c cVar2, d dVar) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        int[] iArr5 = dVar.a;
        int[] iArr6 = dVar.b;
        int[] iArr7 = dVar.c;
        int[] iArr8 = dVar.d;
        int[] iArr9 = dVar.e;
        int[] iArr10 = dVar.f;
        int[] iArr11 = dVar.g;
        int[] iArr12 = dVar.h;
        if (z) {
            X448Field.sub(cVar.b, cVar.a, iArr12);
            iArr2 = iArr6;
            iArr = iArr9;
            iArr4 = iArr10;
            iArr3 = iArr11;
        } else {
            X448Field.add(cVar.b, cVar.a, iArr12);
            iArr = iArr6;
            iArr2 = iArr9;
            iArr3 = iArr10;
            iArr4 = iArr11;
        }
        X448Field.mul(cVar.c, cVar2.c, iArr5);
        X448Field.sqr(iArr5, iArr6);
        X448Field.mul(cVar.a, cVar2.a, iArr7);
        X448Field.mul(cVar.b, cVar2.b, iArr8);
        X448Field.mul(iArr7, iArr8, iArr9);
        X448Field.mul(iArr9, 39081, iArr9);
        X448Field.add(iArr6, iArr9, iArr3);
        X448Field.sub(iArr6, iArr9, iArr4);
        X448Field.add(cVar2.b, cVar2.a, iArr9);
        X448Field.mul(iArr12, iArr9, iArr12);
        X448Field.add(iArr8, iArr7, iArr);
        X448Field.sub(iArr8, iArr7, iArr2);
        X448Field.carry(iArr);
        X448Field.sub(iArr12, iArr6, iArr12);
        X448Field.mul(iArr12, iArr5, iArr12);
        X448Field.mul(iArr9, iArr5, iArr9);
        X448Field.mul(iArr10, iArr12, cVar2.a);
        X448Field.mul(iArr9, iArr11, cVar2.b);
        X448Field.mul(iArr10, iArr11, cVar2.c);
    }

    private static void a(b bVar, c cVar) {
        X448Field.copy(bVar.a, 0, cVar.a, 0);
        X448Field.copy(bVar.b, 0, cVar.b, 0);
        X448Field.one(cVar.c);
    }

    private static void a(c cVar, c cVar2) {
        X448Field.copy(cVar.a, 0, cVar2.a, 0);
        X448Field.copy(cVar.b, 0, cVar2.b, 0);
        X448Field.copy(cVar.c, 0, cVar2.c, 0);
    }

    private static void a(c cVar, d dVar) {
        int[] iArr = dVar.b;
        int[] iArr2 = dVar.c;
        int[] iArr3 = dVar.d;
        int[] iArr4 = dVar.e;
        int[] iArr5 = dVar.h;
        int[] iArr6 = dVar.a;
        X448Field.add(cVar.a, cVar.b, iArr);
        X448Field.sqr(iArr, iArr);
        X448Field.sqr(cVar.a, iArr2);
        X448Field.sqr(cVar.b, iArr3);
        X448Field.add(iArr2, iArr3, iArr4);
        X448Field.carry(iArr4);
        X448Field.sqr(cVar.c, iArr5);
        X448Field.add(iArr5, iArr5, iArr5);
        X448Field.carry(iArr5);
        X448Field.sub(iArr4, iArr5, iArr6);
        X448Field.sub(iArr, iArr4, iArr);
        X448Field.sub(iArr2, iArr3, iArr2);
        X448Field.mul(iArr, iArr6, cVar.a);
        X448Field.mul(iArr4, iArr2, cVar.b);
        X448Field.mul(iArr4, iArr6, cVar.c);
    }

    private static void a(int i2, int i3, b bVar) {
        int i4 = i2 * 16 * 2 * 16;
        for (int i5 = 0; i5 < 16; i5++) {
            int i6 = ((i5 ^ i3) - 1) >> 31;
            X448Field.cmov(i6, j, i4, bVar.a, 0);
            int i7 = i4 + 16;
            X448Field.cmov(i6, j, i7, bVar.b, 0);
            i4 = i7 + 16;
        }
    }

    private static void a(b bVar, c[] cVarArr, int i2, int i3, d dVar) {
        c cVar = new c();
        a(bVar, cVar);
        a(cVar, dVar);
        c cVar2 = new c();
        cVarArr[i2] = cVar2;
        a(bVar, cVar2);
        for (int i4 = 1; i4 < i3; i4++) {
            int i5 = i2 + i4;
            c cVar3 = new c();
            cVarArr[i5] = cVar3;
            a(cVarArr[i5 - 1], cVar3);
            a(cVar, cVarArr[i5], dVar);
        }
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2) {
        System.arraycopy(bArr, i2, bArr2, 0, 56);
        bArr2[0] = (byte) (bArr2[0] & 252);
        bArr2[55] = (byte) (bArr2[55] | ByteCompanionObject.MIN_VALUE);
        bArr2[56] = 0;
    }

    private static void a(byte[] bArr, c cVar) {
        precompute();
        int[] iArr = new int[15];
        bc.org.bouncycastle.math.ec.rfc8032.c.b(bArr, iArr);
        bc.org.bouncycastle.math.ec.rfc8032.c.a(450, iArr, iArr);
        b bVar = new b();
        d dVar = new d();
        c(cVar);
        int i2 = 17;
        while (true) {
            int i3 = i2;
            for (int i4 = 0; i4 < 5; i4++) {
                int i5 = 0;
                for (int i6 = 0; i6 < 5; i6++) {
                    i5 = (i5 & (~(1 << i6))) ^ ((iArr[i3 >>> 5] >>> (i3 & 31)) << i6);
                    i3 += 18;
                }
                int i7 = (i5 >>> 4) & 1;
                a(i4, ((-i7) ^ i5) & 15, bVar);
                X448Field.cnegate(i7, bVar.a);
                a(bVar, cVar, dVar);
            }
            i2--;
            if (i2 < 0) {
                return;
            } else {
                a(cVar, dVar);
            }
        }
    }

    private static void a(byte[] bArr, byte[] bArr2, int i2) {
        c cVar = new c();
        a(bArr, cVar);
        if (a(cVar, bArr2, i2) == 0) {
            throw new IllegalStateException();
        }
    }

    private static void a(int[] iArr, int[] iArr2, b bVar, int[] iArr3, b bVar2, c cVar) {
        precompute();
        byte[] bArr = new byte[450];
        int i2 = 225;
        byte[] bArr2 = new byte[225];
        byte[] bArr3 = new byte[225];
        e.a(iArr, 7, bArr);
        e.a(iArr2, 5, bArr2);
        e.a(iArr3, 5, bArr3);
        c[] cVarArr = new c[8];
        c[] cVarArr2 = new c[8];
        d dVar = new d();
        a(bVar, cVarArr, 0, 8, dVar);
        a(bVar2, cVarArr2, 0, 8, dVar);
        c(cVar);
        do {
            i2--;
            if (i2 < 0) {
                break;
            }
        } while ((bArr[i2] | bArr[i2 + 225] | bArr2[i2] | bArr3[i2]) == 0);
        while (i2 >= 0) {
            byte b2 = bArr[i2];
            if (b2 != 0) {
                a(b2 < 0, h[(b2 >> 1) ^ (b2 >> 31)], cVar, dVar);
            }
            byte b3 = bArr[i2 + 225];
            if (b3 != 0) {
                a(b3 < 0, i[(b3 >> 1) ^ (b3 >> 31)], cVar, dVar);
            }
            byte b4 = bArr2[i2];
            if (b4 != 0) {
                a(b4 < 0, cVarArr[(b4 >> 1) ^ (b4 >> 31)], cVar, dVar);
            }
            byte b5 = bArr3[i2];
            if (b5 != 0) {
                a(b5 < 0, cVarArr2[(b5 >> 1) ^ (b5 >> 31)], cVar, dVar);
            }
            a(cVar, dVar);
            i2--;
        }
        a(cVar, dVar);
    }
}
