package org.bouncycastle.jcajce.provider.symmetric;

import org.bouncycastle.crypto.engines.Shacal2Engine;
import org.bouncycastle.crypto.macs.CMac;
import org.bouncycastle.jcajce.provider.symmetric.util.BaseMac;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\jcajce\provider\symmetric\Shacal2$CMAC.smali */
public class Shacal2$CMAC extends BaseMac {
    public Shacal2$CMAC() {
        super(new CMac(new Shacal2Engine()));
    }
}
