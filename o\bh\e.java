package o.bh;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.cf.c;
import o.cf.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\e.smali */
final class e extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static long d;
    private static int e;
    private static int f;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        f = 1;
        b = new char[]{11505, 25164, 45468, 49373, 5669, 42356, 62651, 2567, 22856, 59546, 16358, 19759, 40055, 54215, 24846, 45147, 51089, 5868};
        d = -8767084372976639361L;
    }

    static void init$0() {
        $$a = new byte[]{38, 115, -18, 59};
        $$b = Opcodes.LMUL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 102
            byte[] r0 = o.bh.e.$$a
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r6 = r6 * 3
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r6 = r6 + 1
            int r4 = -r4
            int r7 = r7 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bh.e.x(short, int, short, java.lang.Object[]):void");
    }

    static /* synthetic */ i.c c(e eVar) throws c, o.bi.c {
        int i = e + 13;
        f = i % 128;
        char c = i % 2 == 0 ? 'B' : 'J';
        i.c c_ = super.c_();
        switch (c) {
            case 'B':
                int i2 = 10 / 0;
                break;
        }
        int i3 = f + 33;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return c_;
        }
    }

    e(Context context) {
        super(context, 27);
    }

    @Override // o.cf.i
    public final void b() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        int i = e + 11;
        f = i % 128;
        switch (i % 2 == 0 ? Typography.less : 'S') {
            case Opcodes.AASTORE /* 83 */:
                h();
                t();
                o();
                m();
                l();
                n();
                k();
                return;
            default:
                h();
                t();
                o();
                m();
                l();
                n();
                k();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.cf.i
    public final i.c c_() {
        new Thread() { // from class: o.bh.e.2
            private static int c = 0;
            private static int d = 1;

            @Override // java.lang.Thread, java.lang.Runnable
            public final void run() {
                int i = (c + Opcodes.IAND) - 1;
                d = i % 128;
                int i2 = i % 2;
                try {
                    e.c(e.this);
                    int i3 = c;
                    int i4 = ((i3 | Opcodes.DNEG) << 1) - (i3 ^ Opcodes.DNEG);
                    d = i4 % 128;
                    switch (i4 % 2 == 0 ? (char) 29 : '4') {
                        case 29:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return;
                    }
                } catch (o.bi.c | c e2) {
                    e2.printStackTrace();
                }
            }
        }.start();
        i.c cVar = new i.c(new o.eg.b(), true);
        int i = f + 19;
        e = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    @Override // o.cf.i
    public final String c() {
        int i = f + 51;
        e = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        v((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), Process.getGidForName("") + 1, 18 - TextUtils.getOffsetBefore("", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = f + 33;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return intern;
        }
    }

    private static void v(char c, int i, int i2, Object[] objArr) {
        o.a.b bVar = new o.a.b();
        long[] jArr = new long[i2];
        bVar.c = 0;
        while (bVar.c < i2) {
            int i3 = $11 + 5;
            $10 = i3 % 128;
            switch (i3 % 2 != 0 ? 'U' : '7') {
                case '7':
                    int i4 = bVar.c;
                    try {
                        Object[] objArr2 = {Integer.valueOf(b[i + bVar.c])};
                        Object obj = o.e.a.s.get(-1667701760);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(10 - Color.alpha(0), (char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 8856), 324 - (ViewConfiguration.getScrollDefaultDelay() >> 16));
                            byte b2 = (byte) 0;
                            byte b3 = b2;
                            Object[] objArr3 = new Object[1];
                            x(b2, b3, (byte) (b3 + 3), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1667701760, obj);
                        }
                        try {
                            Object[] objArr4 = {Long.valueOf(((Long) ((Method) obj).invoke(null, objArr2)).longValue()), Long.valueOf(bVar.c), Long.valueOf(d), Integer.valueOf(c)};
                            Object obj2 = o.e.a.s.get(-1772233796);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(AndroidCharacter.getMirror('0') - 29, (char) (15095 - ((Process.getThreadPriority(0) + 20) >> 6)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 188);
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                x(b4, b5, (byte) (b5 + 2), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Long.TYPE, Long.TYPE, Long.TYPE, Integer.TYPE);
                                o.e.a.s.put(-1772233796, obj2);
                            }
                            jArr[i4] = ((Long) ((Method) obj2).invoke(null, objArr4)).longValue();
                            try {
                                Object[] objArr6 = {bVar, bVar};
                                Object obj3 = o.e.a.s.get(128511541);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(Color.blue(0) + 11, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 46211), 437 - TextUtils.indexOf("", ""));
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    x(b6, b7, b7, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(128511541, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr6);
                                break;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                default:
                    int i5 = bVar.c;
                    try {
                        Object[] objArr8 = {Integer.valueOf(b[bVar.c * i])};
                        Object obj4 = o.e.a.s.get(-1667701760);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(10 - TextUtils.indexOf("", ""), (char) (8857 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 324 - View.combineMeasuredStates(0, 0));
                            byte b8 = (byte) 0;
                            byte b9 = b8;
                            Object[] objArr9 = new Object[1];
                            x(b8, b9, (byte) (b9 + 3), objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE);
                            o.e.a.s.put(-1667701760, obj4);
                        }
                        try {
                            Object[] objArr10 = {Long.valueOf(((Long) ((Method) obj4).invoke(null, objArr8)).longValue()), Long.valueOf(bVar.c), Long.valueOf(d), Integer.valueOf(c)};
                            Object obj5 = o.e.a.s.get(-1772233796);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 18, (char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 15095), 188 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                                byte b10 = (byte) 0;
                                byte b11 = b10;
                                Object[] objArr11 = new Object[1];
                                x(b10, b11, (byte) (b11 + 2), objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Long.TYPE, Long.TYPE, Long.TYPE, Integer.TYPE);
                                o.e.a.s.put(-1772233796, obj5);
                            }
                            jArr[i5] = ((Long) ((Method) obj5).invoke(null, objArr10)).longValue();
                            try {
                                Object[] objArr12 = {bVar, bVar};
                                Object obj6 = o.e.a.s.get(128511541);
                                if (obj6 == null) {
                                    Class cls6 = (Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 11, (char) (46212 - Color.alpha(0)), 438 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                                    byte b12 = (byte) 0;
                                    byte b13 = b12;
                                    Object[] objArr13 = new Object[1];
                                    x(b12, b13, b13, objArr13);
                                    obj6 = cls6.getMethod((String) objArr13[0], Object.class, Object.class);
                                    o.e.a.s.put(128511541, obj6);
                                }
                                ((Method) obj6).invoke(null, objArr12);
                                break;
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    } catch (Throwable th6) {
                        Throwable cause6 = th6.getCause();
                        if (cause6 == null) {
                            throw th6;
                        }
                        throw cause6;
                    }
            }
            int i6 = $10 + 65;
            $11 = i6 % 128;
            int i7 = i6 % 2;
        }
        char[] cArr = new char[i2];
        bVar.c = 0;
        while (bVar.c < i2) {
            int i8 = $10 + 51;
            $11 = i8 % 128;
            switch (i8 % 2 == 0) {
                case false:
                    cArr[bVar.c] = (char) jArr[bVar.c];
                    try {
                        Object[] objArr14 = {bVar, bVar};
                        Object obj7 = o.e.a.s.get(128511541);
                        if (obj7 == null) {
                            Class cls7 = (Class) o.e.a.c((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 11, (char) (46212 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), Color.blue(0) + 437);
                            byte b14 = (byte) 0;
                            byte b15 = b14;
                            Object[] objArr15 = new Object[1];
                            x(b14, b15, b15, objArr15);
                            obj7 = cls7.getMethod((String) objArr15[0], Object.class, Object.class);
                            o.e.a.s.put(128511541, obj7);
                        }
                        ((Method) obj7).invoke(null, objArr14);
                        break;
                    } catch (Throwable th7) {
                        Throwable cause7 = th7.getCause();
                        if (cause7 == null) {
                            throw th7;
                        }
                        throw cause7;
                    }
                default:
                    cArr[bVar.c] = (char) jArr[bVar.c];
                    try {
                        Object[] objArr16 = {bVar, bVar};
                        Object obj8 = o.e.a.s.get(128511541);
                        if (obj8 == null) {
                            Class cls8 = (Class) o.e.a.c(KeyEvent.normalizeMetaState(0) + 11, (char) (46212 - View.MeasureSpec.makeMeasureSpec(0, 0)), TextUtils.indexOf("", "", 0) + 437);
                            byte b16 = (byte) 0;
                            byte b17 = b16;
                            Object[] objArr17 = new Object[1];
                            x(b16, b17, b17, objArr17);
                            obj8 = cls8.getMethod((String) objArr17[0], Object.class, Object.class);
                            o.e.a.s.put(128511541, obj8);
                        }
                        ((Method) obj8).invoke(null, objArr16);
                        int i9 = 19 / 0;
                        break;
                    } catch (Throwable th8) {
                        Throwable cause8 = th8.getCause();
                        if (cause8 == null) {
                            throw th8;
                        }
                        throw cause8;
                    }
            }
        }
        String str = new String(cArr);
        int i10 = $11 + 37;
        $10 = i10 % 128;
        int i11 = i10 % 2;
        objArr[0] = str;
    }
}
