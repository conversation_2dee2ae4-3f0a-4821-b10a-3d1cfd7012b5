package o.bl;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.m;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\d.smali */
public final class d implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static char d;
    private static int e;
    private final Context c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        g();
        View.MeasureSpec.getMode(0);
        ViewConfiguration.getLongPressTimeout();
        TextUtils.lastIndexOf("", '0');
        ViewConfiguration.getEdgeSlop();
        TextUtils.getTrimmedLength("");
        KeyEvent.keyCodeFromString("");
        int i = b + 95;
        e = i % 128;
        switch (i % 2 == 0 ? '[' : 'W') {
            case Opcodes.DUP_X2 /* 91 */:
                throw null;
            default:
                return;
        }
    }

    static void g() {
        a = new char[]{30530, 30570, 30540, 30544, 30537, 30569, 30497, 30585, 30566, 30561, 30568, 30560, 30572, 30571, 30543, 30559, 30574, 30588, 30587, 30542, 30586, 30541, 30563, 30591, 30589};
        d = (char) 17040;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bl.d.$$a
            int r6 = r6 + 4
            int r8 = 73 - r8
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L19:
            r3 = r2
        L1a:
            int r6 = r6 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = -r6
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.d.h(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{94, -116, 51, -9};
        $$b = 218;
    }

    @Override // o.bl.a
    public final void c(Context context) {
        int i = e + 71;
        b = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    public d(Context context) {
        this.c = context;
    }

    @Override // o.bl.a
    public final String d() {
        Object obj;
        int i = b + 61;
        e = i % 128;
        switch (i % 2 == 0 ? '?' : 'X') {
            case Opcodes.POP2 /* 88 */:
                Object[] objArr = new Object[1];
                f(View.MeasureSpec.getMode(0) + 32, "\u0012\u0018\u0014\u0003\u0013\u0006\t\u0006\u0002\u0010\u0011\u0003\u000e\u0006\u0006\t\u000f\u0000\u0015\u0013\u0017\r\u000e\u0006\u0013\u0014\f\u0006\r\u0012\u0004\u0015", (byte) ((Process.myPid() >> 22) + 17), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f(62 >>> View.MeasureSpec.getMode(1), "\u0012\u0018\u0014\u0003\u0013\u0006\t\u0006\u0002\u0010\u0011\u0003\u000e\u0006\u0006\t\u000f\u0000\u0015\u0013\u0017\r\u000e\u0006\u0013\u0014\f\u0006\r\u0012\u0004\u0015", (byte) (115 - (Process.myPid() - 70)), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.bl.a
    public final String e() {
        Context context;
        Object obj;
        int i = b + 81;
        e = i % 128;
        boolean z = i % 2 == 0;
        o.ee.e.a();
        switch (z) {
            case true:
                context = this.c;
                Object[] objArr = new Object[1];
                f(35 / ((Process.getThreadPriority(1) >>> 94) - 98), "\t\u0014\u000b\u0015\b\u0013\u0002\u0015\r\u0015\u000b\u0015㘄㘄\u0017\u0007\u000b\u0011\u0017\r\u000e\u0006\b\r㘘", (byte) (TextUtils.lastIndexOf("", 'j', 1) * 127), objArr);
                obj = objArr[0];
                break;
            default:
                context = this.c;
                Object[] objArr2 = new Object[1];
                f(((Process.getThreadPriority(0) + 20) >> 6) + 25, "\t\u0014\u000b\u0015\b\u0013\u0002\u0015\r\u0015\u000b\u0015㘄㘄\u0017\u0007\u000b\u0011\u0017\r\u000e\u0006\b\r㘘", (byte) (25 - TextUtils.lastIndexOf("", '0', 0)), objArr2);
                obj = objArr2[0];
                break;
        }
        String c = o.ee.c.c(context, ((String) obj).intern());
        int i2 = b + 99;
        e = i2 % 128;
        int i3 = i2 % 2;
        return c;
    }

    /* JADX WARN: Code restructure failed: missing block: B:39:0x006f, code lost:
    
        if (r0 != null) goto L18;
     */
    @Override // o.bl.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String b() {
        /*
            r11 = this;
            int r0 = o.bl.d.b
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.bl.d.e = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 42
            goto L11
        Lf:
            r0 = 85
        L11:
            r1 = 0
            java.lang.String r2 = "\t\u0014\u000b\u0015\b\u0013\u0002\u0015\r\u0015\u0007\t㘑㘑\u0015\u0000\u0017\u0004\r\u0012"
            r3 = 0
            r5 = 1
            r6 = 0
            switch(r0) {
                case 85: goto L48;
                default: goto L1b;
            }
        L1b:
            o.ee.e.a()
            android.content.Context r0 = r11.c
            long r7 = android.widget.ExpandableListView.getPackedPositionForChild(r5, r6)
            int r3 = (r7 > r3 ? 1 : (r7 == r3 ? 0 : -1))
            r4 = 65
            int r4 = r4 % r3
            long r7 = android.widget.ExpandableListView.getPackedPositionForChild(r5, r6)
            r9 = 1
            int r3 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            int r3 = r3 * 126
            byte r3 = (byte) r3
            java.lang.Object[] r7 = new java.lang.Object[r5]
            f(r4, r2, r3, r7)
            r2 = r7[r6]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r0 = o.ee.c.c(r0, r2)
            if (r0 == 0) goto L75
            goto L72
        L48:
            o.ee.e.a()
            android.content.Context r0 = r11.c
            long r7 = android.widget.ExpandableListView.getPackedPositionForChild(r6, r6)
            int r7 = (r7 > r3 ? 1 : (r7 == r3 ? 0 : -1))
            int r7 = 19 - r7
            long r8 = android.widget.ExpandableListView.getPackedPositionForChild(r6, r6)
            int r3 = (r8 > r3 ? 1 : (r8 == r3 ? 0 : -1))
            int r3 = 39 - r3
            byte r3 = (byte) r3
            java.lang.Object[] r4 = new java.lang.Object[r5]
            f(r7, r2, r3, r4)
            r2 = r4[r6]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r0 = o.ee.c.c(r0, r2)
            if (r0 == 0) goto Lb1
        L71:
            goto L7b
        L72:
            r2 = 70
            goto L77
        L75:
            r2 = 27
        L77:
            switch(r2) {
                case 27: goto Lb1;
                default: goto L7a;
            }
        L7a:
            goto L71
        L7b:
            int r2 = o.bl.d.b
            int r2 = r2 + r5
            int r3 = r2 % 128
            o.bl.d.e = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L88
            r2 = r6
            goto L89
        L88:
            r2 = r5
        L89:
            switch(r2) {
                case 0: goto L93;
                default: goto L8c;
            }
        L8c:
            boolean r2 = r0.isEmpty()
            if (r2 == 0) goto L9a
            goto L99
        L93:
            r0.isEmpty()
            throw r1     // Catch: java.lang.Throwable -> L97
        L97:
            r0 = move-exception
            throw r0
        L99:
            r5 = r6
        L9a:
            switch(r5) {
                case 1: goto L9e;
                default: goto L9d;
            }
        L9d:
            goto Lb1
        L9e:
            int r1 = o.bl.d.e
            int r1 = r1 + 45
            int r2 = r1 % 128
            o.bl.d.b = r2
            int r1 = r1 % 2
            if (r1 == 0) goto Lb0
            r1 = 96
            int r1 = r1 / r6
            return r0
        Lae:
            r0 = move-exception
            throw r0
        Lb0:
            return r0
        Lb1:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.d.b():java.lang.String");
    }

    @Override // o.bl.a
    public final o.bi.a a() {
        int i = e + 19;
        b = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? (char) 20 : '/') {
            case 20:
                obj.hashCode();
                throw null;
            default:
                return null;
        }
    }

    @Override // o.bl.a
    public final o.bi.d c() {
        int i = b;
        int i2 = i + 73;
        e = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 3;
        e = i4 % 128;
        Object obj = null;
        switch (i4 % 2 == 0) {
            case false:
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    private static void f(int i, String str, byte b2, Object[] objArr) {
        int i2;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr = a;
        long j = 0;
        int i3 = -1401577988;
        int i4 = -1;
        int i5 = 1;
        if (cArr != null) {
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i6 = 0;
            while (i6 < length) {
                try {
                    Object[] objArr2 = new Object[i5];
                    objArr2[0] = Integer.valueOf(cArr[i6]);
                    Object obj = o.e.a.s.get(Integer.valueOf(i3));
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(18 - (SystemClock.elapsedRealtime() > j ? 1 : (SystemClock.elapsedRealtime() == j ? 0 : -1)), (char) ((SystemClock.elapsedRealtime() > j ? 1 : (SystemClock.elapsedRealtime() == j ? 0 : -1)) + i4), Color.alpha(0) + 76);
                        byte b3 = (byte) i4;
                        byte b4 = (byte) (b3 + 1);
                        Object[] objArr3 = new Object[1];
                        h(b3, b4, b4, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj);
                    }
                    cArr2[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i6++;
                    j = 0;
                    i3 = -1401577988;
                    i4 = -1;
                    i5 = 1;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr = cArr2;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(d)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - TextUtils.indexOf("", ""), (char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 76);
                byte b5 = (byte) (-1);
                byte b6 = (byte) (b5 + 1);
                Object[] objArr5 = new Object[1];
                h(b5, b6, b6, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i];
            int i7 = 2;
            switch (i % 2 != 0) {
                case false:
                    i2 = i;
                    break;
                default:
                    int i8 = $10 + 79;
                    $11 = i8 % 128;
                    if (i8 % 2 != 0) {
                        i2 = i - 1;
                        cArr3[i2] = (char) (charArray[i2] - b2);
                        break;
                    } else {
                        i2 = i + 98;
                        cArr3[i2] = (char) (charArray[i2] - b2);
                        break;
                    }
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (mVar.b < i2) {
                    int i9 = $11 + 99;
                    $10 = i9 % 128;
                    int i10 = i9 % i7;
                    mVar.e = charArray[mVar.b];
                    mVar.a = charArray[mVar.b + 1];
                    if (mVar.e == mVar.a) {
                        int i11 = $11 + 27;
                        $10 = i11 % 128;
                        int i12 = i11 % i7;
                        cArr3[mVar.b] = (char) (mVar.e - b2);
                        cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                    } else {
                        try {
                            Object[] objArr6 = new Object[13];
                            objArr6[12] = mVar;
                            objArr6[11] = Integer.valueOf(charValue);
                            objArr6[10] = mVar;
                            objArr6[9] = mVar;
                            objArr6[8] = Integer.valueOf(charValue);
                            objArr6[7] = mVar;
                            objArr6[6] = mVar;
                            objArr6[5] = Integer.valueOf(charValue);
                            objArr6[4] = mVar;
                            objArr6[3] = mVar;
                            objArr6[i7] = Integer.valueOf(charValue);
                            objArr6[1] = mVar;
                            objArr6[0] = mVar;
                            Object obj3 = o.e.a.s.get(696901393);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - Color.alpha(0), (char) (ExpandableListView.getPackedPositionChild(0L) + 8857), Color.green(0) + 324);
                                byte b7 = (byte) (-1);
                                Object[] objArr7 = new Object[1];
                                h(b7, (byte) (b7 + 1), (byte) $$a.length, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                o.e.a.s.put(696901393, obj3);
                            }
                            switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h ? '#' : (char) 21) {
                                case 21:
                                    switch (mVar.c == mVar.d ? '%' : (char) 28) {
                                        case '%':
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i13 = (mVar.c * charValue) + mVar.i;
                                            int i14 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[i13];
                                            cArr3[mVar.b + 1] = cArr[i14];
                                            int i15 = $11 + 91;
                                            $10 = i15 % 128;
                                            int i16 = i15 % 2;
                                            break;
                                        default:
                                            int i17 = (mVar.c * charValue) + mVar.h;
                                            int i18 = (mVar.d * charValue) + mVar.i;
                                            cArr3[mVar.b] = cArr[i17];
                                            cArr3[mVar.b + 1] = cArr[i18];
                                            break;
                                    }
                                default:
                                    int i19 = $10 + 49;
                                    $11 = i19 % 128;
                                    if (i19 % 2 == 0) {
                                    }
                                    try {
                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                        Object obj4 = o.e.a.s.get(1075449051);
                                        if (obj4 == null) {
                                            Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getEdgeSlop() >> 16), (char) (ViewConfiguration.getTapTimeout() >> 16), ExpandableListView.getPackedPositionChild(0L) + 66);
                                            byte b8 = (byte) (-1);
                                            byte b9 = (byte) (b8 + 1);
                                            Object[] objArr9 = new Object[1];
                                            h(b8, b9, (byte) (b9 + 3), objArr9);
                                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(1075449051, obj4);
                                        }
                                        int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                        int i20 = (mVar.d * charValue) + mVar.h;
                                        cArr3[mVar.b] = cArr[intValue];
                                        cArr3[mVar.b + 1] = cArr[i20];
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                            }
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    mVar.b += 2;
                    i7 = 2;
                }
            }
            int i21 = 0;
            while (i21 < i) {
                int i22 = $10 + 19;
                $11 = i22 % 128;
                if (i22 % 2 == 0) {
                    cArr3[i21] = (char) (cArr3[i21] & 29562);
                    i21 += 32;
                } else {
                    cArr3[i21] = (char) (cArr3[i21] ^ 13722);
                    i21++;
                }
            }
            objArr[0] = new String(cArr3);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
