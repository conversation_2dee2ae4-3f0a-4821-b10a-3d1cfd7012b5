package com.avaldigitallabs.mbocc.wallet.types;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes7\com\avaldigitallabs\mbocc\wallet\types\ProvisionStatus.smali */
public class ProvisionStatus {
    public static final String ACTIVITY_IGNORE = "provisionActivityIgnore";
    public static final String DEVICE_ERROR = "provisionDeviceError";
    public static final String DEVICE_NOT_ELIGIBLE = "provisionDeviceNotEligible";
    public static final String INITIALIZATION_ERROR = "provisionInitializationError";
    public static final String INITIALIZATION_SUCCESS = "provisionInitializationSuccess";
    public static final String PROCESS_ERROR = "provisionProcessError";
    public static final String PROCESS_GRANTED = "provisionProcessGranted";
    public static final String PROCESS_NOT_GRANTED = "provisionProcessNotGranted";
    public static final String PROCESS_PENDING = "provisionProcessPending";
    public static final String PROCESS_SUCCESS = "provisionProcessSuccess";
}
