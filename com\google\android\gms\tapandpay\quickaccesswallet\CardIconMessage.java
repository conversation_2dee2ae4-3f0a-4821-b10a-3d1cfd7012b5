package com.google.android.gms.tapandpay.quickaccesswallet;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\CardIconMessage.smali */
public final class CardIconMessage extends AbstractSafeParcelable {
    public static final Parcelable.Creator<CardIconMessage> CREATOR = new zzb();
    private int[] zza;
    private int zzb;
    private String zzc;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\CardIconMessage$Builder.smali */
    public static final class Builder {
        private final CardIconMessage zza;

        public Builder() {
            this.zza = new CardIconMessage(null);
        }

        public CardIconMessage build() {
            return this.zza;
        }

        public Builder setConditions(int[] conditions) {
            this.zza.zza = conditions;
            return this;
        }

        public Builder setIcon(int icon) {
            this.zza.zzb = icon;
            return this;
        }

        public Builder setMessage(String message) {
            this.zza.zzc = message;
            return this;
        }

        public Builder(CardIconMessage origin) {
            CardIconMessage cardIconMessage = new CardIconMessage(null);
            this.zza = cardIconMessage;
            cardIconMessage.zza = origin.zza;
            cardIconMessage.zzb = origin.zzb;
            cardIconMessage.zzc = origin.zzc;
        }
    }

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    @Retention(RetentionPolicy.SOURCE)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\CardIconMessage$Condition.smali */
    public @interface Condition {
        public static final int NFC_OFF = 3;
        public static final int NFC_ON = 2;
        public static final int PHONE_LOCKED = 4;
        public static final int TRUE = 1;
        public static final int UNKNOWN_CONDITION = 0;
    }

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    @Retention(RetentionPolicy.SOURCE)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\CardIconMessage$Icon.smali */
    public @interface Icon {
        public static final int LOCK = 4;
        public static final int NFC = 2;
        public static final int NFC_DISABLED = 3;
        public static final int NONE = 1;
        public static final int UNKNOWN_ICON = 0;
    }

    private CardIconMessage() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof CardIconMessage) {
            CardIconMessage cardIconMessage = (CardIconMessage) other;
            if (Arrays.equals(this.zza, cardIconMessage.zza) && Objects.equal(Integer.valueOf(this.zzb), Integer.valueOf(cardIconMessage.zzb)) && Objects.equal(this.zzc, cardIconMessage.zzc)) {
                return true;
            }
        }
        return false;
    }

    public int[] getConditions() {
        return this.zza;
    }

    public int getIcon() {
        return this.zzb;
    }

    public String getMessage() {
        return this.zzc;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(Arrays.hashCode(this.zza)), Integer.valueOf(this.zzb), this.zzc);
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeIntArray(dest, 1, getConditions(), false);
        SafeParcelWriter.writeInt(dest, 2, getIcon());
        SafeParcelWriter.writeString(dest, 3, getMessage(), false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    /* synthetic */ CardIconMessage(zza zzaVar) {
    }

    CardIconMessage(int[] iArr, int i, String str) {
        this.zza = iArr;
        this.zzb = i;
        this.zzc = str;
    }
}
