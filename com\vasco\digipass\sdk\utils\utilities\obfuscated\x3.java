package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x3.smali */
class x3 extends j5 {
    private static final byte[] R = new byte[0];
    private final int C;
    private int L;

    x3(InputStream inputStream, int i, int i2) {
        super(inputStream, i2);
        if (i <= 0) {
            if (i < 0) {
                throw new IllegalArgumentException("negative lengths not allowed");
            }
            a(true);
        }
        this.C = i;
        this.L = i;
    }

    void a(byte[] bArr) throws IOException {
        int i = this.L;
        if (i != bArr.length) {
            throw new IllegalArgumentException("buffer length not right for data");
        }
        if (i == 0) {
            return;
        }
        int a = a();
        int i2 = this.L;
        if (i2 >= a) {
            throw new IOException("corrupted stream - out of bounds length found: " + this.L + " >= " + a);
        }
        int a2 = i2 - n7.a(this.b, bArr, 0, bArr.length);
        this.L = a2;
        if (a2 != 0) {
            throw new EOFException("DEF length " + this.C + " object truncated by " + this.L);
        }
        a(true);
    }

    int b() {
        return this.L;
    }

    byte[] c() throws IOException {
        if (this.L == 0) {
            return R;
        }
        int a = a();
        int i = this.L;
        if (i >= a) {
            throw new IOException("corrupted stream - out of bounds length found: " + this.L + " >= " + a);
        }
        byte[] bArr = new byte[i];
        int a2 = i - n7.a(this.b, bArr, 0, i);
        this.L = a2;
        if (a2 != 0) {
            throw new EOFException("DEF length " + this.C + " object truncated by " + this.L);
        }
        a(true);
        return bArr;
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (this.L == 0) {
            return -1;
        }
        int read = this.b.read();
        if (read < 0) {
            throw new EOFException("DEF length " + this.C + " object truncated by " + this.L);
        }
        int i = this.L - 1;
        this.L = i;
        if (i == 0) {
            a(true);
        }
        return read;
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i, int i2) throws IOException {
        int i3 = this.L;
        if (i3 == 0) {
            return -1;
        }
        int read = this.b.read(bArr, i, Math.min(i2, i3));
        if (read >= 0) {
            int i4 = this.L - read;
            this.L = i4;
            if (i4 == 0) {
                a(true);
            }
            return read;
        }
        throw new EOFException("DEF length " + this.C + " object truncated by " + this.L);
    }
}
