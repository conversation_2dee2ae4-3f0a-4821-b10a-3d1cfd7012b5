package o.cz;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import o.ds.f;
import o.ee.g;
import o.ei.i;
import o.fn.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\j.smali */
public final class j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static short[] b;
    private static int c;
    private static int d;
    private static byte[] e;
    private static int g;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        g = 1;
        e();
        ViewConfiguration.getGlobalActionKeyTimeout();
        Color.argb(0, 0, 0, 0);
        Drawable.resolveOpacity(0, 0);
        TextUtils.getCapsMode("", 0, 0);
        Process.myTid();
        int i = h + Opcodes.LMUL;
        g = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        e = new byte[]{70, 19, -25, -23, 4, -7, -38, 77, -21, 17, 29, 3, -6, 6, -6, 124, 10, -44, -44, -105, -29, 48, 13, -38, -5, 68, 15, 14, -35, -105, 120, -42, -36, 8, 14, -93, 15, 3, 100, 74, 10, -42, 6, -92, 9, 6, -41, -34, 62, -46, -42, -42, 72, 33, -15, 41, -63, 43, 33, 83, -61, -6, 92, -15, -9, 44, -50, 37, -15, 76, -1, -8, -55, -6, -26, -38, -63, -21, -31, -61, -32, -26, -13, -3, -52, -64, 5, -51, -28, -32, 73, 23, 126, 17, 77, 22, -19, 97, 25, 90, 57, 104, 21, 97, 17, 124, 103, -22, 114, 33, 24, 32, 79, 7, 20, 55, 56, -15, 45, 11, 45, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 24, 67, -2, 45, 10, 33, 43, 50, -16, 46, 37, 38, 45, 71, -29, -86, -3, -81, -83, -6, -29, -76, 19, -86, -47, -32, -4, -64, -7, 124, -83, -42, -89, -55, -67, -81, -21, -115, -86, -82, -52, -55, -72, -83, -42, -89, -55, -82, -46, -89, -42, -61, -81, -88, -83, -24, UtilitiesSDKConstants.SRP_LABEL_ENC, -91, -41, -81, -42, -36, -59, -66, -82, -84, 110, 46, 122, -19, 75, 73, 93, 83, 98, 86, 66, -95, 23, 87, 75, 94, 112, 70, 114, 92, 83, 114, -96, 25, 87, 113, 79, 95, -67, 20, 85, 65, 67, 95, 78, 87, 78, 68, 91, 71, -117, 2, 85, 78, 91, 76, 73, 114, 41, -127, 107, 2, 85, 84, 103};
        c = 909053609;
        d = -580351852;
        a = 392373156;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 108
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.cz.j.$$a
            int r8 = r8 * 2
            int r8 = r8 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = -r8
            int r6 = r6 + r8
            int r8 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.j.i(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{14, -53, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -35};
        $$b = Opcodes.RET;
    }

    public static h b(o.eg.b bVar) throws i {
        try {
            g.c();
            Object[] objArr = new Object[1];
            f((byte) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) - 15), (-558640436) - View.getDefaultSize(0, 0), (short) (121 - ExpandableListView.getPackedPositionGroup(0L)), TextUtils.indexOf("", "", 0, 0) - 58, 347634255 + Color.blue(0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((byte) (80 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), (-558640421) - View.MeasureSpec.getMode(0), (short) (14 - Process.getGidForName("")), (-57) - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 347634277 + KeyEvent.getDeadChar(0, 0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f((byte) (TextUtils.getOffsetBefore("", 0) + 45), (-558640385) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (short) ((ViewConfiguration.getLongPressTimeout() >> 16) + 114), TextUtils.indexOf("", "") - 58, 347634275 - Color.green(0), objArr3);
            boolean booleanValue = bVar.b(((String) objArr3[0]).intern(), Boolean.FALSE).booleanValue();
            Object[] objArr4 = new Object[1];
            f((byte) ((ViewConfiguration.getScrollDefaultDelay() >> 16) - 7), (-558640367) - (Process.myPid() >> 22), (short) ((-105) - View.MeasureSpec.getMode(0)), Color.blue(0) - 58, 347634289 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr4);
            boolean booleanValue2 = bVar.b(((String) objArr4[0]).intern(), Boolean.FALSE).booleanValue();
            Object[] objArr5 = new Object[1];
            f((byte) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 50), (-558640346) - TextUtils.indexOf("", "", 0), (short) (70 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), (ViewConfiguration.getJumpTapTimeout() >> 16) - 58, 347634288 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr5);
            boolean booleanValue3 = bVar.b(((String) objArr5[0]).intern(), Boolean.FALSE).booleanValue();
            Object[] objArr6 = new Object[1];
            f((byte) ((ViewConfiguration.getLongPressTimeout() >> 16) - 106), (-558640327) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) ((-32) - Drawable.resolveOpacity(0, 0)), (-59) - TextUtils.lastIndexOf("", '0', 0, 0), 347634268 - Process.getGidForName(""), objArr6);
            boolean booleanValue4 = bVar.b(((String) objArr6[0]).intern(), Boolean.FALSE).booleanValue();
            Object[] objArr7 = new Object[1];
            f((byte) ((ViewConfiguration.getWindowTouchSlop() >> 8) - 30), (-558640302) - Process.getGidForName(""), (short) ((-87) - ExpandableListView.getPackedPositionChild(0L)), (Process.myPid() >> 22) - 58, View.getDefaultSize(0, 0) + 347634284, objArr7);
            f a2 = f.a(bVar.r(((String) objArr7[0]).intern()));
            Object[] objArr8 = new Object[1];
            f((byte) (Process.getGidForName("") + 8), (KeyEvent.getMaxKeyCode() >> 16) - 558640285, (short) ((-69) - TextUtils.getOffsetAfter("", 0)), Process.getGidForName("") - 57, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 347634288, objArr8);
            h hVar = new h(booleanValue, booleanValue2, booleanValue3, booleanValue4, a2, bVar.e(((String) objArr8[0]).intern(), (Integer) (-1)).intValue());
            int i = h + Opcodes.DSUB;
            g = i % 128;
            int i2 = i % 2;
            return hVar;
        } catch (o.eg.d e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr9 = new Object[1];
            f((byte) (MotionEvent.axisFromString("") + 14), TextUtils.indexOf("", "", 0, 0) - 558640248, (short) (45 - View.MeasureSpec.getSize(0)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 58, View.getDefaultSize(0, 0) + 347634246, objArr9);
            throw new i(sb.append(((String) objArr9[0]).intern()).append(e2.getMessage()).toString());
        }
    }

    private static void f(byte b2, int i, short s, int i2, int i3, Object[] objArr) {
        boolean z;
        int i4;
        boolean z2;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i2), Integer.valueOf(c)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0', 0), (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), 64 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)));
                byte b3 = (byte) 0;
                byte b4 = b3;
                Object[] objArr3 = new Object[1];
                i(b3, b4, b4, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            switch (intValue != -1) {
                case true:
                    z = false;
                    break;
                default:
                    z = true;
                    break;
            }
            switch (!z) {
                case true:
                    break;
                default:
                    byte[] bArr = e;
                    switch (bArr == null) {
                        default:
                            int i5 = $10 + 29;
                            $11 = i5 % 128;
                            int i6 = i5 % 2;
                            int length = bArr.length;
                            byte[] bArr2 = new byte[length];
                            int i7 = 0;
                            while (i7 < length) {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr[i7])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(18 - ImageFormat.getBitsPerPixel(0), (char) (View.combineMeasuredStates(0, 0) + 16425), Color.rgb(0, 0, 0) + 16777366);
                                        byte b5 = (byte) ($$b & 7);
                                        byte b6 = (byte) (b5 - 1);
                                        Object[] objArr5 = new Object[1];
                                        i(b5, b6, b6, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr2[i7] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i7++;
                                    int i8 = $11 + 97;
                                    $10 = i8 % 128;
                                    switch (i8 % 2 != 0 ? '9' : (char) 21) {
                                    }
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            }
                            bArr = bArr2;
                            break;
                        case true:
                            if (bArr == null) {
                                intValue = (short) (((short) (b[i + ((int) (a ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                                break;
                            } else {
                                int i9 = $10 + 87;
                                $11 = i9 % 128;
                                int i10 = i9 % 2;
                                byte[] bArr3 = e;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(i), Integer.valueOf(a)};
                                    Object obj3 = o.e.a.s.get(-2120899312);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 11, (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 64 - TextUtils.indexOf((CharSequence) "", '0', 0));
                                        byte b7 = (byte) 0;
                                        byte b8 = b7;
                                        Object[] objArr7 = new Object[1];
                                        i(b7, b8, b8, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj3);
                                    }
                                    intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                    }
            }
            if (intValue > 0) {
                int i11 = $10;
                int i12 = i11 + 69;
                $11 = i12 % 128;
                int i13 = i12 % 2;
                int i14 = ((i + intValue) - 2) + ((int) (a ^ (-5810760824076169584L)));
                switch (z ? '\b' : 'R') {
                    case Opcodes.DASTORE /* 82 */:
                        i4 = 0;
                        break;
                    default:
                        int i15 = i11 + 53;
                        $11 = i15 % 128;
                        if (i15 % 2 == 0) {
                        }
                        i4 = 1;
                        break;
                }
                fVar.d = i14 + i4;
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i3), Integer.valueOf(d), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 12, (char) TextUtils.indexOf("", "", 0, 0), Color.green(0) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = e;
                    if (bArr4 != null) {
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        for (int i16 = 0; i16 < length2; i16++) {
                            bArr5[i16] = (byte) (bArr4[i16] ^ (-5810760824076169584L));
                        }
                        bArr4 = bArr5;
                    }
                    switch (bArr4 == null) {
                        case true:
                            z2 = false;
                            break;
                        default:
                            int i17 = $10 + 35;
                            $11 = i17 % 128;
                            int i18 = i17 % 2;
                            z2 = true;
                            break;
                    }
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        if (z2) {
                            int i19 = $11 + Opcodes.DREM;
                            $10 = i19 % 128;
                            int i20 = i19 % 2;
                            byte[] bArr6 = e;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                        } else {
                            short[] sArr = b;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
