package kotlin.contracts;

import kotlin.Metadata;

/* compiled from: Effect.kt */
@Metadata(d1 = {"\u0000\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\bg\u0018\u00002\u00020\u0001¨\u0006\u0002"}, d2 = {"Lkotlin/contracts/Returns;", "Lkotlin/contracts/SimpleEffect;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\contracts\Returns.smali */
public interface Returns extends SimpleEffect {
}
