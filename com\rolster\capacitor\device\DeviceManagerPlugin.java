package com.rolster.capacitor.device;

import android.content.Context;
import android.content.pm.PackageManager;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "DeviceManager")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\rolster\capacitor\device\DeviceManagerPlugin.smali */
public class DeviceManagerPlugin extends Plugin {
    private DeviceManagerResolver deviceManagerResolver;

    @Override // com.getcapacitor.Plugin
    public void load() {
        try {
            this.deviceManagerResolver = (DeviceManagerResolver) Class.forName("com.rolster.capacitor.device.google.GoogleDeviceManagerResolver").getConstructor(Context.class).newInstance(getContext());
        } catch (Exception e) {
            throw new RuntimeException("Error inicializando DeviceManagerResolver", e);
        }
    }

    @PluginMethod
    public void requestInformation(PluginCall call) {
        JSObject result = new JSObject();
        result.put("compilationCode", String.valueOf(getCompilationCode()));
        result.put("versionCode", getVersionCode());
        boolean googleServices = hasGoogleServicesAvailable();
        boolean huaweiServices = hasHuaweiServicesAvailable();
        if (googleServices) {
            result.put("services", "google");
        } else if (huaweiServices) {
            result.put("services", "huawei");
        } else {
            result.put("services", "none");
        }
        call.resolve(result);
    }

    @PluginMethod
    public void hasGoogleServices(PluginCall call) {
        JSObject result = new JSObject();
        result.put("availability", hasGoogleServicesAvailable());
        call.resolve(result);
    }

    @PluginMethod
    public void hasHuaweiServices(PluginCall call) {
        JSObject result = new JSObject();
        result.put("availability", hasHuaweiServicesAvailable());
        call.resolve(result);
    }

    @PluginMethod
    public void hasAppleServices(PluginCall call) {
        JSObject result = new JSObject();
        result.put("availability", false);
        call.resolve(result);
    }

    private boolean hasGoogleServicesAvailable() {
        return this.deviceManagerResolver.hasGoogle();
    }

    private boolean hasHuaweiServicesAvailable() {
        return this.deviceManagerResolver.hasHuawei();
    }

    private String getVersionCode() {
        try {
            PackageManager packageManager = getContext().getPackageManager();
            String packageName = getContext().getPackageName();
            return packageManager.getPackageInfo(packageName, 0).versionName;
        } catch (PackageManager.NameNotFoundException e) {
            return "0.0.0";
        }
    }

    private int getCompilationCode() {
        try {
            PackageManager packageManager = getContext().getPackageManager();
            String packageName = getContext().getPackageName();
            return packageManager.getPackageInfo(packageName, 0).versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            return 0;
        }
    }
}
