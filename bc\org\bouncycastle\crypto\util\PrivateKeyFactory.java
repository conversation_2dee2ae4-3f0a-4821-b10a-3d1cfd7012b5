package bc.org.bouncycastle.crypto.util;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.crypto.params.ECDomainParameters;
import bc.org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.a4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.y6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z7;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PrivateKeyFactory.smali */
public class PrivateKeyFactory {
    private static byte[] a(q6 q6Var) throws IOException {
        return x.a(q6Var.h()).h();
    }

    public static AsymmetricKeyParameter createKey(byte[] bArr) throws IOException {
        if (bArr == null) {
            throw new IllegalArgumentException("privateKeyInfoData array null");
        }
        if (bArr.length != 0) {
            return createKey(q6.a(b0.a(bArr)));
        }
        throw new IllegalArgumentException("privateKeyInfoData array empty");
    }

    public static AsymmetricKeyParameter createKey(InputStream inputStream) throws IOException {
        return createKey(q6.a(new q(inputStream).c()));
    }

    public static AsymmetricKeyParameter createKey(q6 q6Var) throws IOException {
        BigInteger e;
        b4 b4Var;
        ECDomainParameters eCDomainParameters;
        if (q6Var != null) {
            s0 f = q6Var.f();
            w e2 = f.e();
            if (!e2.b(i6.b) && !e2.b(i6.k) && !e2.b(b8.m)) {
                b4 b4Var2 = null;
                l3 l3Var = null;
                if (e2.b(i6.s)) {
                    s2 a = s2.a(f.f());
                    r rVar = (r) q6Var.h();
                    BigInteger f2 = a.f();
                    return new u2(rVar.i(), new t2(a.g(), a.e(), null, f2 == null ? 0 : f2.intValue()));
                }
                if (e2.b(e6.l)) {
                    l4 a2 = l4.a(f.f());
                    return new n4(((r) q6Var.h()).i(), new m4(a2.f(), a2.e()));
                }
                if (e2.b(j8.Z)) {
                    r rVar2 = (r) q6Var.h();
                    h f3 = f.f();
                    if (f3 != null) {
                        k3 a3 = k3.a(f3.toASN1Primitive());
                        l3Var = new l3(a3.f(), a3.g(), a3.e());
                    }
                    return new m3(rVar2.i(), l3Var);
                }
                if (e2.b(j8.l)) {
                    c8 a4 = c8.a(f.f());
                    if (a4.g()) {
                        w wVar = (w) a4.e();
                        X9ECParameters a5 = u1.a(wVar);
                        if (a5 == null) {
                            a5 = c4.a(wVar);
                        }
                        eCDomainParameters = new d4(wVar, a5);
                    } else {
                        X9ECParameters x9ECParameters = X9ECParameters.getInstance(a4.e());
                        eCDomainParameters = new ECDomainParameters(x9ECParameters.getCurve(), x9ECParameters.getG(), x9ECParameters.getN(), x9ECParameters.getH(), x9ECParameters.getSeed());
                    }
                    return new ECPrivateKeyParameters(e4.a(q6Var.h()).e(), eCDomainParameters);
                }
                if (e2.b(j4.b)) {
                    if (32 == q6Var.g()) {
                        return new x7(q6Var.e().h());
                    }
                    return new x7(a(q6Var));
                }
                if (e2.b(j4.c)) {
                    if (56 == q6Var.g()) {
                        return new z7(q6Var.e().h());
                    }
                    return new z7(a(q6Var));
                }
                if (e2.b(j4.d)) {
                    return new f4(a(q6Var));
                }
                if (e2.b(j4.e)) {
                    return new h4(a(q6Var));
                }
                if (!e2.b(o1.m) && !e2.b(y6.h) && !e2.b(y6.g)) {
                    throw new RuntimeException("algorithm identifier in private key not recognised");
                }
                h f4 = f.f();
                x4 a6 = x4.a(f4);
                b0 aSN1Primitive = f4.toASN1Primitive();
                if ((aSN1Primitive instanceof e0) && (e0.a((Object) aSN1Primitive).size() == 2 || e0.a((Object) aSN1Primitive).size() == 3)) {
                    b4Var = new b4(new d4(a6.g(), a4.b(a6.g())), a6.g(), a6.e(), a6.f());
                    int g = q6Var.g();
                    if (g != 32 && g != 64) {
                        h h = q6Var.h();
                        if (h instanceof r) {
                            e = r.a(h).h();
                        } else {
                            e = new BigInteger(1, Arrays.reverse(x.a(h).h()));
                        }
                    } else {
                        e = new BigInteger(1, Arrays.reverse(q6Var.e().h()));
                    }
                } else {
                    c8 a7 = c8.a(f.f());
                    if (a7.g()) {
                        w a8 = w.a((Object) a7.e());
                        b4Var2 = new b4(new d4(a8, c4.a(a8)), a6.g(), a6.e(), a6.f());
                    } else if (!a7.f()) {
                        b4Var2 = new b4(new d4(e2, X9ECParameters.getInstance(a7.e())), a6.g(), a6.e(), a6.f());
                    }
                    h h2 = q6Var.h();
                    if (h2 instanceof r) {
                        e = r.a(h2).i();
                        b4Var = b4Var2;
                    } else {
                        e = e4.a(h2).e();
                        b4Var = b4Var2;
                    }
                }
                return new ECPrivateKeyParameters(e, new b4(b4Var, a6.g(), a6.e(), a6.f()));
            }
            w6 a9 = w6.a(q6Var.h());
            return new v6(a9.h(), a9.l(), a9.k(), a9.i(), a9.j(), a9.f(), a9.g(), a9.e());
        }
        throw new IllegalArgumentException("keyInfo argument null");
    }
}
