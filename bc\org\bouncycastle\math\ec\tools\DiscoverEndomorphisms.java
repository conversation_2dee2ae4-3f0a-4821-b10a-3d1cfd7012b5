package bc.org.bouncycastle.math.ec.tools;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u1;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.TreeSet;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\tools\DiscoverEndomorphisms.smali */
public class DiscoverEndomorphisms {
    private static void a(String str) {
        X9ECParameters a = u1.a(str);
        if (a == null && (a = c4.a(str)) == null) {
            System.err.println("Unknown curve: " + str);
        } else {
            a(a, str);
        }
    }

    private static void b(X9ECParameters x9ECParameters) {
        BigInteger n = x9ECParameters.getN();
        BigInteger bigInteger = ECConstants.ONE;
        BigInteger[] b = b(n, bigInteger, bigInteger, bigInteger);
        ECFieldElement[] a = a(x9ECParameters.getCurve());
        b(x9ECParameters, b[0], a);
        System.out.println("OR");
        b(x9ECParameters, b[1], a);
    }

    private static boolean c(BigInteger[] bigIntegerArr, BigInteger[] bigIntegerArr2) {
        BigInteger abs = bigIntegerArr[0].abs();
        BigInteger abs2 = bigIntegerArr[1].abs();
        BigInteger abs3 = bigIntegerArr2[0].abs();
        BigInteger abs4 = bigIntegerArr2[1].abs();
        boolean z = abs.compareTo(abs3) < 0;
        return z == (abs2.compareTo(abs4) < 0) ? z : abs.multiply(abs).add(abs2.multiply(abs2)).compareTo(abs3.multiply(abs3).add(abs4.multiply(abs4))) < 0;
    }

    private static boolean d(BigInteger bigInteger, BigInteger bigInteger2) {
        BigInteger abs = bigInteger.abs();
        BigInteger abs2 = bigInteger2.abs();
        int bitLength = abs2.bitLength();
        int bitLength2 = abs.bitLength() * 2;
        return bitLength2 + (-1) <= bitLength && (bitLength2 < bitLength || abs.multiply(abs).compareTo(abs2) < 0);
    }

    public static void discoverEndomorphisms(X9ECParameters x9ECParameters) {
        if (x9ECParameters == null) {
            throw new NullPointerException("x9");
        }
        a(x9ECParameters, "<UNKNOWN>");
    }

    private static BigInteger e(BigInteger bigInteger, BigInteger bigInteger2) {
        if (!bigInteger2.testBit(0)) {
            throw new IllegalStateException();
        }
        BigInteger bigInteger3 = ECConstants.ONE;
        BigInteger shiftRight = bigInteger2.subtract(bigInteger3).shiftRight(1);
        if (!bigInteger.modPow(shiftRight, bigInteger2).equals(bigInteger3)) {
            return null;
        }
        BigInteger bigInteger4 = shiftRight;
        while (!bigInteger4.testBit(0)) {
            bigInteger4 = bigInteger4.shiftRight(1);
            if (!bigInteger.modPow(bigInteger4, bigInteger2).equals(ECConstants.ONE)) {
                return a(bigInteger, bigInteger4, bigInteger2, shiftRight);
            }
        }
        return bigInteger.modPow(bigInteger4.add(ECConstants.ONE).shiftRight(1), bigInteger2);
    }

    private static BigInteger[] f(BigInteger bigInteger, BigInteger bigInteger2) {
        return bigInteger.compareTo(bigInteger2) <= 0 ? new BigInteger[]{bigInteger, bigInteger2} : new BigInteger[]{bigInteger2, bigInteger};
    }

    private static void g(BigInteger bigInteger, BigInteger bigInteger2) {
        BigInteger[] b = b(bigInteger, bigInteger2);
        BigInteger[] bigIntegerArr = {b[2], b[3].negate()};
        BigInteger[] a = a(new BigInteger[]{b[0], b[1].negate()}, new BigInteger[]{b[4], b[5].negate()});
        if (!a(a, bigInteger) && a(bigIntegerArr[0], bigIntegerArr[1])) {
            BigInteger bigInteger3 = bigIntegerArr[0];
            BigInteger bigInteger4 = bigIntegerArr[1];
            BigInteger divide = bigInteger3.add(bigInteger4.multiply(bigInteger2)).divide(bigInteger);
            BigInteger[] a2 = a(new BigInteger[]{divide.abs(), bigInteger4.abs()});
            if (a2 != null) {
                BigInteger bigInteger5 = a2[0];
                BigInteger bigInteger6 = a2[1];
                if (divide.signum() < 0) {
                    bigInteger5 = bigInteger5.negate();
                }
                if (bigInteger4.signum() > 0) {
                    bigInteger6 = bigInteger6.negate();
                }
                BigInteger subtract = divide.multiply(bigInteger5).subtract(bigInteger4.multiply(bigInteger6));
                BigInteger bigInteger7 = ECConstants.ONE;
                if (!subtract.equals(bigInteger7)) {
                    throw new IllegalStateException();
                }
                BigInteger subtract2 = bigInteger6.multiply(bigInteger).subtract(bigInteger5.multiply(bigInteger2));
                BigInteger negate = bigInteger5.negate();
                BigInteger negate2 = subtract2.negate();
                BigInteger add = a(bigInteger.subtract(bigInteger7)).add(bigInteger7);
                BigInteger[] b2 = b(a(negate, add, bigInteger4), a(negate2, add, bigInteger3));
                if (b2 != null) {
                    for (BigInteger bigInteger8 = b2[0]; bigInteger8.compareTo(b2[1]) <= 0; bigInteger8 = bigInteger8.add(ECConstants.ONE)) {
                        BigInteger[] bigIntegerArr2 = {subtract2.add(bigInteger8.multiply(bigInteger3)), bigInteger5.add(bigInteger8.multiply(bigInteger4))};
                        if (c(bigIntegerArr2, a)) {
                            a = bigIntegerArr2;
                        }
                    }
                }
            }
        }
        BigInteger subtract3 = bigIntegerArr[0].multiply(a[1]).subtract(bigIntegerArr[1].multiply(a[0]));
        int bitLength = (bigInteger.bitLength() + 16) - (bigInteger.bitLength() & 7);
        BigInteger h = h(a[1].shiftLeft(bitLength), subtract3);
        BigInteger negate3 = h(bigIntegerArr[1].shiftLeft(bitLength), subtract3).negate();
        a("v1", "{ " + bigIntegerArr[0].toString(16) + ", " + bigIntegerArr[1].toString(16) + " }");
        a("v2", "{ " + a[0].toString(16) + ", " + a[1].toString(16) + " }");
        a("d", subtract3.toString(16));
        a("(OPT) g1", h.toString(16));
        a("(OPT) g2", negate3.toString(16));
        a("(OPT) bits", Integer.toString(bitLength));
    }

    private static BigInteger h(BigInteger bigInteger, BigInteger bigInteger2) {
        boolean z = bigInteger.signum() != bigInteger2.signum();
        BigInteger abs = bigInteger.abs();
        BigInteger abs2 = bigInteger2.abs();
        BigInteger divide = abs.add(abs2.shiftRight(1)).divide(abs2);
        return z ? divide.negate() : divide;
    }

    public static void main(String[] strArr) {
        if (strArr.length > 0) {
            for (String str : strArr) {
                a(str);
            }
            return;
        }
        TreeSet treeSet = new TreeSet(a(c4.a()));
        treeSet.addAll(a(u1.a()));
        Iterator it = treeSet.iterator();
        while (it.hasNext()) {
            a((String) it.next());
        }
    }

    private static void b(X9ECParameters x9ECParameters, BigInteger bigInteger, ECFieldElement[] eCFieldElementArr) {
        ECPoint normalize = x9ECParameters.getG().normalize();
        ECPoint normalize2 = normalize.multiply(bigInteger).normalize();
        if (normalize.getYCoord().equals(normalize2.getYCoord())) {
            ECFieldElement eCFieldElement = eCFieldElementArr[0];
            if (!normalize.getXCoord().multiply(eCFieldElement).equals(normalize2.getXCoord())) {
                eCFieldElement = eCFieldElementArr[1];
                if (!normalize.getXCoord().multiply(eCFieldElement).equals(normalize2.getXCoord())) {
                    throw new IllegalStateException("Derivation of GLV Type B parameters failed unexpectedly");
                }
            }
            a("Point map", "lambda * (x, y) = (beta * x, y)");
            a("beta", eCFieldElement.toBigInteger().toString(16));
            a("lambda", bigInteger.toString(16));
            g(x9ECParameters.getN(), bigInteger);
            return;
        }
        throw new IllegalStateException("Derivation of GLV Type B parameters failed unexpectedly");
    }

    private static void a(X9ECParameters x9ECParameters, String str) {
        ECCurve curve = x9ECParameters.getCurve();
        if (ECAlgorithms.isFpCurve(curve)) {
            BigInteger c = curve.getField().c();
            if (curve.getB().isZero() && c.mod(ECConstants.FOUR).equals(ECConstants.ONE)) {
                System.out.println("Curve '" + str + "' has a 'GLV Type A' endomorphism with these parameters:");
                a(x9ECParameters);
            }
            if (curve.getA().isZero() && c.mod(ECConstants.THREE).equals(ECConstants.ONE)) {
                System.out.println("Curve '" + str + "' has a 'GLV Type B' endomorphism with these parameters:");
                b(x9ECParameters);
            }
        }
    }

    private static BigInteger c(BigInteger bigInteger, BigInteger bigInteger2) {
        for (int i = 2; i < 1000; i++) {
            BigInteger valueOf = BigInteger.valueOf(i);
            if (!valueOf.modPow(bigInteger2, bigInteger).equals(ECConstants.ONE)) {
                return valueOf;
            }
        }
        throw new IllegalStateException();
    }

    private static void a(X9ECParameters x9ECParameters) {
        BigInteger n = x9ECParameters.getN();
        BigInteger bigInteger = ECConstants.ONE;
        BigInteger[] b = b(n, bigInteger, ECConstants.ZERO, bigInteger);
        ECFieldElement[] b2 = b(x9ECParameters.getCurve());
        a(x9ECParameters, b[0], b2);
        System.out.println("OR");
        a(x9ECParameters, b[1], b2);
    }

    private static BigInteger[] b(BigInteger bigInteger, BigInteger bigInteger2) {
        BigInteger bigInteger3 = ECConstants.ZERO;
        BigInteger bigInteger4 = ECConstants.ONE;
        BigInteger bigInteger5 = bigInteger;
        while (true) {
            BigInteger[] divideAndRemainder = bigInteger5.divideAndRemainder(bigInteger2);
            BigInteger bigInteger6 = divideAndRemainder[0];
            BigInteger bigInteger7 = divideAndRemainder[1];
            BigInteger subtract = bigInteger3.subtract(bigInteger6.multiply(bigInteger4));
            if (d(bigInteger2, bigInteger)) {
                return new BigInteger[]{bigInteger5, bigInteger3, bigInteger2, bigInteger4, bigInteger7, subtract};
            }
            bigInteger5 = bigInteger2;
            bigInteger3 = bigInteger4;
            bigInteger2 = bigInteger7;
            bigInteger4 = subtract;
        }
    }

    private static void a(X9ECParameters x9ECParameters, BigInteger bigInteger, ECFieldElement[] eCFieldElementArr) {
        ECPoint normalize = x9ECParameters.getG().normalize();
        ECPoint normalize2 = normalize.multiply(bigInteger).normalize();
        if (normalize.getXCoord().negate().equals(normalize2.getXCoord())) {
            ECFieldElement eCFieldElement = eCFieldElementArr[0];
            if (!normalize.getYCoord().multiply(eCFieldElement).equals(normalize2.getYCoord())) {
                eCFieldElement = eCFieldElementArr[1];
                if (!normalize.getYCoord().multiply(eCFieldElement).equals(normalize2.getYCoord())) {
                    throw new IllegalStateException("Derivation of GLV Type A parameters failed unexpectedly");
                }
            }
            a("Point map", "lambda * (x, y) = (-x, i * y)");
            a("i", eCFieldElement.toBigInteger().toString(16));
            a("lambda", bigInteger.toString(16));
            g(x9ECParameters.getN(), bigInteger);
            return;
        }
        throw new IllegalStateException("Derivation of GLV Type A parameters failed unexpectedly");
    }

    private static BigInteger[] b(BigInteger[] bigIntegerArr, BigInteger[] bigIntegerArr2) {
        BigInteger max = bigIntegerArr[0].max(bigIntegerArr2[0]);
        BigInteger min = bigIntegerArr[1].min(bigIntegerArr2[1]);
        if (max.compareTo(min) > 0) {
            return null;
        }
        return new BigInteger[]{max, min};
    }

    private static BigInteger[] b(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4) {
        BigInteger e = e(bigInteger3.multiply(bigInteger3).subtract(bigInteger2.multiply(bigInteger4).shiftLeft(2)).mod(bigInteger), bigInteger);
        if (e != null) {
            BigInteger modInverse = bigInteger2.shiftLeft(1).modInverse(bigInteger);
            return new BigInteger[]{e.subtract(bigInteger3).multiply(modInverse).mod(bigInteger), e.negate().subtract(bigInteger3).multiply(modInverse).mod(bigInteger)};
        }
        throw new IllegalStateException("Solving quadratic equation failed unexpectedly");
    }

    private static void a(String str, Object obj) {
        StringBuffer stringBuffer = new StringBuffer("  ");
        stringBuffer.append(str);
        while (stringBuffer.length() < 20) {
            stringBuffer.append(' ');
        }
        stringBuffer.append(": ");
        stringBuffer.append(obj.toString());
        System.out.println(stringBuffer.toString());
    }

    private static ECFieldElement[] b(ECCurve eCCurve) {
        ECFieldElement sqrt = eCCurve.fromBigInteger(ECConstants.ONE).negate().sqrt();
        if (sqrt != null) {
            return new ECFieldElement[]{sqrt, sqrt.negate()};
        }
        throw new IllegalStateException("Calculation of non-trivial order-4  field elements failed unexpectedly");
    }

    private static boolean a(BigInteger bigInteger, BigInteger bigInteger2) {
        return bigInteger.gcd(bigInteger2).equals(ECConstants.ONE);
    }

    private static void b(BigInteger[] bigIntegerArr) {
        BigInteger bigInteger = bigIntegerArr[0];
        bigIntegerArr[0] = bigIntegerArr[1];
        bigIntegerArr[1] = bigInteger;
    }

    private static BigInteger[] a(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
        return f(bigInteger.subtract(bigInteger2).divide(bigInteger3), bigInteger.add(bigInteger2).divide(bigInteger3));
    }

    private static ArrayList a(Enumeration enumeration) {
        ArrayList arrayList = new ArrayList();
        while (enumeration.hasMoreElements()) {
            arrayList.add(enumeration.nextElement());
        }
        return arrayList;
    }

    private static BigInteger[] a(BigInteger[] bigIntegerArr) {
        boolean z = bigIntegerArr[0].compareTo(bigIntegerArr[1]) < 0;
        if (z) {
            b(bigIntegerArr);
        }
        BigInteger bigInteger = bigIntegerArr[0];
        BigInteger bigInteger2 = bigIntegerArr[1];
        BigInteger bigInteger3 = ECConstants.ONE;
        BigInteger bigInteger4 = ECConstants.ZERO;
        BigInteger bigInteger5 = bigInteger4;
        BigInteger bigInteger6 = bigInteger3;
        BigInteger bigInteger7 = bigInteger2;
        BigInteger bigInteger8 = bigInteger;
        while (bigInteger7.compareTo(ECConstants.ONE) > 0) {
            BigInteger[] divideAndRemainder = bigInteger8.divideAndRemainder(bigInteger7);
            BigInteger bigInteger9 = divideAndRemainder[0];
            BigInteger bigInteger10 = divideAndRemainder[1];
            BigInteger subtract = bigInteger6.subtract(bigInteger9.multiply(bigInteger4));
            BigInteger subtract2 = bigInteger5.subtract(bigInteger9.multiply(bigInteger3));
            BigInteger bigInteger11 = bigInteger7;
            bigInteger7 = bigInteger10;
            bigInteger8 = bigInteger11;
            bigInteger5 = bigInteger3;
            bigInteger3 = subtract2;
            BigInteger bigInteger12 = bigInteger4;
            bigInteger4 = subtract;
            bigInteger6 = bigInteger12;
        }
        if (bigInteger7.signum() <= 0) {
            return null;
        }
        BigInteger[] bigIntegerArr2 = {bigInteger4, bigInteger3};
        if (z) {
            b(bigIntegerArr2);
        }
        return bigIntegerArr2;
    }

    private static BigInteger[] a(BigInteger[] bigIntegerArr, BigInteger[] bigIntegerArr2) {
        return c(bigIntegerArr, bigIntegerArr2) ? bigIntegerArr : bigIntegerArr2;
    }

    private static boolean a(BigInteger[] bigIntegerArr, BigInteger bigInteger) {
        return d(bigIntegerArr[0].abs().max(bigIntegerArr[1].abs()), bigInteger);
    }

    private static ECFieldElement[] a(ECCurve eCCurve) {
        BigInteger modPow;
        BigInteger c = eCCurve.getField().c();
        BigInteger divide = c.divide(ECConstants.THREE);
        SecureRandom secureRandom = new SecureRandom();
        do {
            BigInteger bigInteger = ECConstants.TWO;
            modPow = f1.a(bigInteger, c.subtract(bigInteger), secureRandom).modPow(divide, c);
        } while (modPow.equals(ECConstants.ONE));
        ECFieldElement fromBigInteger = eCCurve.fromBigInteger(modPow);
        return new ECFieldElement[]{fromBigInteger, fromBigInteger.square()};
    }

    private static BigInteger a(BigInteger bigInteger) {
        BigInteger shiftRight = bigInteger.shiftRight(bigInteger.bitLength() / 2);
        while (true) {
            BigInteger shiftRight2 = shiftRight.add(bigInteger.divide(shiftRight)).shiftRight(1);
            if (shiftRight2.equals(shiftRight)) {
                return shiftRight2;
            }
            shiftRight = shiftRight2;
        }
    }

    private static BigInteger a(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4) {
        BigInteger c = c(bigInteger3, bigInteger4);
        BigInteger bigInteger5 = bigInteger4;
        while (!bigInteger2.testBit(0)) {
            bigInteger2 = bigInteger2.shiftRight(1);
            bigInteger5 = bigInteger5.shiftRight(1);
            if (!bigInteger.modPow(bigInteger2, bigInteger3).equals(c.modPow(bigInteger5, bigInteger3))) {
                bigInteger5 = bigInteger5.add(bigInteger4);
            }
        }
        return bigInteger.modInverse(bigInteger3).modPow(bigInteger2.subtract(ECConstants.ONE).shiftRight(1), bigInteger3).multiply(c.modPow(bigInteger5.shiftRight(1), bigInteger3)).mod(bigInteger3);
    }
}
