package o.i;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.DeviceBiometricCustomerAuthenticationPrompt;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\j.smali */
public final class j extends k<o.r.a> {
    private static int $10 = 0;
    private static int $11 = 1;
    private static int a = 0;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int g;

    static {
        g = 1;
        c();
        View.MeasureSpec.getSize(0);
        int i = a + 19;
        g = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = (char) 28352;
        b = (char) 35141;
        c = (char) 48886;
        d = (char) 7821;
    }

    public j() {
        super(f.a, new o.r.a());
    }

    @Override // o.i.g
    protected final o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException {
        int i = a + 73;
        g = i % 128;
        int i2 = i % 2;
        if (!(customerAuthenticationPrompt instanceof DeviceBiometricCustomerAuthenticationPrompt)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", (KeyEvent.getMaxKeyCode() >> 16) + 43, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("컲\udc81\u0bd5鏌๛ᾜ≸淞挃营ℤ巘∪䧓６➜\ud858㌱ゐ갽銎絓\ue3bd꙾佼鉾\ue0fe\ued09⯺Ყ⟡㷣ፚ䯓\uf457㺔ధ䣌ᷬ朗ధ䣌矾嗩妳仈\ue0fe\ued09赶㲰\u1cfc⪴≸淞ۤ\ue8c6붮\udd33\udcb1逆", 58 - MotionEvent.axisFromString(""), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr3 = new Object[1];
            l("\ue3bd꙾佼鉾\ue0fe\ued09⯺Ყ⟡㷣ፚ䯓\uf457㺔ధ䣌ᷬ朗ధ䣌矾嗩妳仈\ue0fe\ued09赶㲰", KeyEvent.keyCodeFromString("") + 28, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        DeviceBiometricCustomerAuthenticationPrompt deviceBiometricCustomerAuthenticationPrompt = (DeviceBiometricCustomerAuthenticationPrompt) customerAuthenticationPrompt;
        o.k.a aVar = new o.k.a(context, deviceBiometricCustomerAuthenticationPrompt.getTitle(), deviceBiometricCustomerAuthenticationPrompt.getSubtitle(), this);
        int i3 = g + 91;
        a = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return aVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.i.g
    public final void d(Context context, o.f.e eVar, o.g.a aVar, b bVar, boolean z) {
        try {
            aVar.d(new o.f.a(e.d.c, new Date(), new o.f.d(e().d(((o.f.a) eVar).h())), null));
            int i = a + Opcodes.LSHR;
            g = i % 128;
            switch (i % 2 == 0 ? '.' : (char) 18) {
                case '.':
                    int i2 = 50 / 0;
                    return;
                default:
                    return;
            }
        } catch (IllegalArgumentException e2) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", 44 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("\ud858㌱ゐ갽꜓譻襨贵ꢚꚖ黝ᐋ\uf457㺔ధ䣌ٽ嶨⟔뮡∪䧓\uf3eb굷矾嗩≤쨳謭㱠⟬韑⯺Ყ鍝흻陜䁨\uf457㺔", 40 - TextUtils.indexOf("", "", 0), objArr2);
            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
            aVar.c(o.g.b.c);
        } catch (BadPaddingException e3) {
            e = e3;
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", 42 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l("\ud858㌱ゐ갽꜓譻襨贵ꢚꚖ黝ᐋ\uf457㺔ధ䣌ٽ嶨⟔뮡∪䧓켍䤳劇ꮁ赶㲰㭡\ue76e燙\u0e78", 31 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
            o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e);
            aVar.c(o.g.b.c);
        } catch (IllegalBlockSizeException e4) {
            e = e4;
            o.ee.g.c();
            Object[] objArr32 = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", 42 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr32);
            String intern22 = ((String) objArr32[0]).intern();
            Object[] objArr42 = new Object[1];
            l("\ud858㌱ゐ갽꜓譻襨贵ꢚꚖ黝ᐋ\uf457㺔ధ䣌ٽ嶨⟔뮡∪䧓켍䤳劇ꮁ赶㲰㭡\ue76e燙\u0e78", 31 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr42);
            o.ee.g.a(intern22, ((String) objArr42[0]).intern(), e);
            aVar.c(o.g.b.c);
        } catch (o.r.b e5) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", TextUtils.indexOf((CharSequence) "", '0', 0) + 44, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            l("\ud858㌱ゐ갽꜓譻襨贵ꢚꚖ黝ᐋ\uf457㺔ధ䣌ٽ嶨⟔뮡∪䧓켍䤳劇ꮁ赶㲰㭡\ue76e燙\u0e78", 30 - MotionEvent.axisFromString(""), objArr6);
            o.ee.g.a(intern3, ((String) objArr6[0]).intern(), e5);
            aVar.c(o.g.b.a);
        }
    }

    public final Cipher b() {
        int i = a + 7;
        g = i % 128;
        Object obj = null;
        try {
            switch (i % 2 == 0) {
                case true:
                    j();
                    c cVar = c.c;
                    obj.hashCode();
                    throw null;
                default:
                    switch (j() != c.c) {
                        case false:
                            e();
                            Cipher d2 = o.r.a.d();
                            int i2 = g + 25;
                            a = i2 % 128;
                            switch (i2 % 2 != 0) {
                                case false:
                                    return d2;
                                default:
                                    int i3 = 48 / 0;
                                    return d2;
                            }
                        default:
                            int i4 = a + Opcodes.LMUL;
                            g = i4 % 128;
                            int i5 = i4 % 2;
                            return null;
                    }
            }
        } catch (o.r.b e2) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l("⏼黖ۤ\ue8c6劇ꮁ䉨㵟\ue0fe\ued09饝㞐㉃\u000b眗ꥭ蘭쨭釂풑\ueb6fᢳ鋹ﻨ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e팁壣饝㞐뷻빑\udcb1逆", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 42, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("⢚灥추ᦣ\u09c5펔\udccf붎쟦涱囫녾托뉝㭡\ue76e䞜ᚫ䞫킝\udccf붎\uda54嶄∪䧓켍䤳劇ꮁ赶㲰㭡\ue76e燙\u0e78", Drawable.resolveOpacity(0, 0) + 35, objArr2);
            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
            return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 572
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.j.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
