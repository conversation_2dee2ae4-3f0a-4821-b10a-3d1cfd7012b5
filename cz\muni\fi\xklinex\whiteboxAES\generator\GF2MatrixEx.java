package cz.muni.fi.xklinex.whiteboxAES.generator;

import bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w7;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\generator\GF2MatrixEx.smali */
public class GF2MatrixEx extends a implements Serializable {
    public static final int BLOCKEXP = 5;
    public static final int INTMASK = 31;
    public static final int INTSIZE = 32;
    private static final long serialVersionUID = 2691400975418627511L;
    private int length;
    private int[][] matrix;

    public GF2MatrixEx() {
    }

    public static void addToRow(int[] iArr, int[] iArr2, int i) {
        for (int length = iArr2.length - 1; length >= i; length--) {
            iArr2[length] = iArr[length] ^ iArr2[length];
        }
    }

    public static int get(int[] iArr, int i) {
        int i2 = iArr[i >>> 5];
        int i3 = i & 31;
        return (i2 & (1 << i3)) >> i3;
    }

    private void readObject(ObjectInputStream objectInputStream) throws ClassNotFoundException, IOException {
        this.numRows = objectInputStream.readInt();
        this.numColumns = objectInputStream.readInt();
        objectInputStream.defaultReadObject();
    }

    public static void set(int[] iArr, int i, int i2) {
        if (i2 == 0) {
            int i3 = i >>> 5;
            iArr[i3] = (~(1 << (i & 31))) & iArr[i3];
        } else {
            int i4 = i >>> 5;
            iArr[i4] = (1 << (i & 31)) | iArr[i4];
        }
    }

    public static void swapRows(int[][] iArr, int i, int i2) {
        int[] iArr2 = iArr[i];
        iArr[i] = iArr[i2];
        iArr[i2] = iArr2;
    }

    private void writeObject(ObjectOutputStream objectOutputStream) throws IOException {
        objectOutputStream.writeInt(this.numRows);
        objectOutputStream.writeInt(this.numColumns);
        objectOutputStream.defaultWriteObject();
    }

    public void assignZeroMatrix(int i, int i2) {
        this.numRows = i;
        this.numColumns = i2;
        int i3 = (i2 + 31) >>> 5;
        this.length = i3;
        this.matrix = (int[][]) Array.newInstance((Class<?>) Integer.TYPE, i, i3);
        for (int i4 = 0; i4 < this.numRows; i4++) {
            for (int i5 = 0; i5 < this.length; i5++) {
                this.matrix[i4][i5] = 0;
            }
        }
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public a computeInverse() {
        int i = this.numRows;
        if (i != this.numColumns) {
            throw new ArithmeticException("Matrix is not invertible.");
        }
        int[][] iArr = (int[][]) Array.newInstance((Class<?>) Integer.TYPE, i, this.length);
        for (int i2 = i - 1; i2 >= 0; i2--) {
            iArr[i2] = d5.a(this.matrix[i2]);
        }
        int i3 = this.numRows;
        int[][] iArr2 = (int[][]) Array.newInstance((Class<?>) Integer.TYPE, i3, this.length);
        for (int i4 = i3 - 1; i4 >= 0; i4--) {
            iArr2[i4][i4 >> 5] = 1 << (i4 & 31);
        }
        for (int i5 = 0; i5 < this.numRows; i5++) {
            int i6 = i5 >> 5;
            int i7 = 1 << (i5 & 31);
            if ((iArr[i5][i6] & i7) == 0) {
                int i8 = i5 + 1;
                boolean z = false;
                while (i8 < this.numRows) {
                    if ((iArr[i8][i6] & i7) != 0) {
                        swapRows(iArr, i5, i8);
                        swapRows(iArr2, i5, i8);
                        i8 = this.numRows;
                        z = true;
                    }
                    i8++;
                }
                if (!z) {
                    throw new ArithmeticException("Matrix is not invertible.");
                }
            }
            for (int i9 = this.numRows - 1; i9 >= 0; i9--) {
                if (i9 != i5) {
                    int[] iArr3 = iArr[i9];
                    if ((iArr3[i6] & i7) != 0) {
                        addToRow(iArr[i5], iArr3, i6);
                        addToRow(iArr2[i5], iArr2[i9], 0);
                    }
                }
            }
        }
        return new GF2MatrixEx(this.numColumns, iArr2);
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof GF2MatrixEx)) {
            return false;
        }
        GF2MatrixEx gF2MatrixEx = (GF2MatrixEx) obj;
        if (this.numRows != gF2MatrixEx.numRows || this.numColumns != gF2MatrixEx.numColumns || this.length != gF2MatrixEx.length) {
            return false;
        }
        for (int i = 0; i < this.numRows; i++) {
            if (!d5.a(this.matrix[i], gF2MatrixEx.matrix[i])) {
                return false;
            }
        }
        return true;
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public byte[] getEncoded() {
        int i = (this.numColumns + 7) >>> 3;
        int i2 = this.numRows;
        int i3 = 8;
        byte[] bArr = new byte[(i * i2) + 8];
        k5.a(i2, bArr, 0);
        k5.a(this.numColumns, bArr, 4);
        int i4 = this.numColumns;
        int i5 = i4 >>> 5;
        int i6 = i4 & 31;
        for (int i7 = 0; i7 < this.numRows; i7++) {
            int i8 = 0;
            while (i8 < i5) {
                k5.a(this.matrix[i7][i8], bArr, i3);
                i8++;
                i3 += 4;
            }
            int i9 = 0;
            while (i9 < i6) {
                bArr[i3] = (byte) ((this.matrix[i7][i5] >>> i9) & 255);
                i9 += 8;
                i3++;
            }
        }
        return bArr;
    }

    public int hashCode() {
        int i = (((this.numRows * 31) + this.numColumns) * 31) + this.length;
        for (int i2 = 0; i2 < this.numRows; i2++) {
            i = (i * 31) + Arrays.hashCode(this.matrix[i2]);
        }
        return i;
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public boolean isZero() {
        for (int i = 0; i < this.numRows; i++) {
            for (int i2 = 0; i2 < this.length; i2++) {
                if (this.matrix[i][i2] != 0) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public w7 leftMultiply(w7 w7Var) {
        if (!(w7Var instanceof u4)) {
            throw new ArithmeticException("vector is not defined over GF(2)");
        }
        if (w7Var.a() != this.numRows) {
            throw new ArithmeticException("length mismatch");
        }
        int[] b = ((u4) w7Var).b();
        int[] iArr = new int[this.length];
        int i = this.numRows;
        int i2 = i >> 5;
        int i3 = 1 << (i & 31);
        int i4 = 0;
        for (int i5 = 0; i5 < i2; i5++) {
            int i6 = 1;
            do {
                if ((b[i5] & i6) != 0) {
                    for (int i7 = 0; i7 < this.length; i7++) {
                        iArr[i7] = iArr[i7] ^ this.matrix[i4][i7];
                    }
                }
                i4++;
                i6 <<= 1;
            } while (i6 != 0);
        }
        for (int i8 = 1; i8 != i3; i8 <<= 1) {
            if ((b[i2] & i8) != 0) {
                for (int i9 = 0; i9 < this.length; i9++) {
                    iArr[i9] = iArr[i9] ^ this.matrix[i4][i9];
                }
            }
            i4++;
        }
        return new u4(this.numColumns, iArr);
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public a rightMultiply(a aVar) {
        if (!(aVar instanceof GF2MatrixEx)) {
            throw new ArithmeticException("matrix is not defined over GF(2)");
        }
        if (aVar.getNumRows() != this.numColumns) {
            throw new ArithmeticException("length mismatch");
        }
        GF2MatrixEx gF2MatrixEx = (GF2MatrixEx) aVar;
        GF2MatrixEx gF2MatrixEx2 = new GF2MatrixEx(this.numRows, aVar.getNumColumns());
        int i = this.numColumns & 31;
        int i2 = i == 0 ? this.length : this.length - 1;
        for (int i3 = 0; i3 < this.numRows; i3++) {
            int i4 = 0;
            for (int i5 = 0; i5 < i2; i5++) {
                int i6 = this.matrix[i3][i5];
                for (int i7 = 0; i7 < 32; i7++) {
                    if (((1 << i7) & i6) != 0) {
                        for (int i8 = 0; i8 < gF2MatrixEx.length; i8++) {
                            int[] iArr = gF2MatrixEx2.matrix[i3];
                            iArr[i8] = iArr[i8] ^ gF2MatrixEx.matrix[i4][i8];
                        }
                    }
                    i4++;
                }
            }
            int i9 = this.matrix[i3][this.length - 1];
            for (int i10 = 0; i10 < i; i10++) {
                if (((1 << i10) & i9) != 0) {
                    for (int i11 = 0; i11 < gF2MatrixEx.length; i11++) {
                        int[] iArr2 = gF2MatrixEx2.matrix[i3];
                        iArr2[i11] = iArr2[i11] ^ gF2MatrixEx.matrix[i4][i11];
                    }
                }
                i4++;
            }
        }
        return gF2MatrixEx2;
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public String toString() {
        int i = this.numColumns & 31;
        int i2 = i == 0 ? this.length : this.length - 1;
        StringBuilder sb = new StringBuilder();
        for (int i3 = 0; i3 < this.numRows; i3++) {
            sb.append(i3).append(": ");
            for (int i4 = 0; i4 < i2; i4++) {
                int i5 = this.matrix[i3][i4];
                for (int i6 = 0; i6 < 32; i6++) {
                    if (((i5 >>> i6) & 1) == 0) {
                        sb.append('0');
                    } else {
                        sb.append('1');
                    }
                }
                sb.append(' ');
            }
            int i7 = this.matrix[i3][this.length - 1];
            for (int i8 = 0; i8 < i; i8++) {
                if (((i7 >>> i8) & 1) == 0) {
                    sb.append('0');
                } else {
                    sb.append('1');
                }
            }
            sb.append('\n');
        }
        return sb.toString();
    }

    public GF2MatrixEx(int i, int[][] iArr) {
        int[] iArr2 = iArr[0];
        if (iArr2.length != ((i + 31) >> 5)) {
            throw new ArithmeticException("Int array does not match given number of columns.");
        }
        this.numColumns = i;
        this.numRows = iArr.length;
        this.length = iArr2.length;
        int i2 = i & 31;
        int i3 = i2 == 0 ? -1 : (1 << i2) - 1;
        for (int i4 = 0; i4 < this.numRows; i4++) {
            int[] iArr3 = iArr[i4];
            int i5 = this.length - 1;
            iArr3[i5] = iArr3[i5] & i3;
        }
        this.matrix = iArr;
    }

    public static int get(int[][] iArr, int i, int i2) {
        return get(iArr[i], i2);
    }

    public int get(int i, int i2) {
        return get(this.matrix, i, i2);
    }

    public static void set(int[][] iArr, int i, int i2, int i3) {
        set(iArr[i], i2, i3);
    }

    public void set(int i, int i2, int i3) {
        set(this.matrix, i, i2, i3);
    }

    public GF2MatrixEx(int i, int i2) {
        if (i2 > 0 && i > 0) {
            assignZeroMatrix(i, i2);
            return;
        }
        throw new ArithmeticException("size of matrix is non-positive");
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public a rightMultiply(l6 l6Var) {
        throw null;
    }

    @Override // bc.org.bouncycastle.pqc.legacy.math.linearalgebra.a
    public w7 rightMultiply(w7 w7Var) {
        if (w7Var instanceof u4) {
            if (w7Var.a() == this.numColumns) {
                int[] b = ((u4) w7Var).b();
                int[] iArr = new int[(this.numRows + 31) >>> 5];
                int i = 0;
                while (true) {
                    int i2 = this.numRows;
                    if (i < i2) {
                        int i3 = 0;
                        for (int i4 = 0; i4 < this.length; i4++) {
                            i3 ^= this.matrix[i][i4] & b[i4];
                        }
                        int i5 = 0;
                        for (int i6 = 0; i6 < 32; i6++) {
                            i5 ^= (i3 >>> i6) & 1;
                        }
                        if (i5 == 1) {
                            int i7 = i >>> 5;
                            iArr[i7] = iArr[i7] | (1 << (i & 31));
                        }
                        i++;
                    } else {
                        return new u4(i2, iArr);
                    }
                }
            } else {
                throw new ArithmeticException("length mismatch");
            }
        } else {
            throw new ArithmeticException("vector is not defined over GF(2)");
        }
    }
}
