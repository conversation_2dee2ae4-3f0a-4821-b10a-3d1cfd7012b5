package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\o0.smali */
abstract class o0 extends k0 {
    final i0 b;

    o0(Class cls, int i) {
        super(cls);
        this.b = i0.a(0, i);
    }

    final b0 a(b0 b0Var) {
        if (this.a.isInstance(b0Var)) {
            return b0Var;
        }
        throw new IllegalStateException("unexpected object: " + b0Var.getClass().getName());
    }

    b0 a(f2 f2Var) {
        throw new IllegalStateException("unexpected implicit primitive encoding");
    }

    b0 a(e0 e0Var) {
        throw new IllegalStateException("unexpected implicit constructed encoding");
    }

    final b0 a(byte[] bArr) throws IOException {
        return a(b0.a(bArr));
    }

    final b0 a(j0 j0Var, boolean z) {
        if (128 == j0Var.i()) {
            return a(j0Var.a(z, this));
        }
        throw new IllegalStateException("this method only valid for CONTEXT_SPECIFIC tags");
    }
}
