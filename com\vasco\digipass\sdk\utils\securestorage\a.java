package com.vasco.digipass.sdk.utils.securestorage;

import android.content.Context;
import android.security.keystore.KeyInfo;
import androidx.biometric.BiometricManager;
import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.BiometricWriteProtectionSettings;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.SecureStorageBiometricWriteCallback;
import com.vasco.digipass.sdk.utils.securestorage.initialization.SecureStorageInitCallback;
import com.vasco.digipass.sdk.utils.securestorage.model.IVEncryptedData;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.c;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.e;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.f;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.g;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.i;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.j;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.k;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.l;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.m;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.n;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.o;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.p;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.r;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.t;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.v;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.x;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.y;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import kotlin.Deprecated;
import kotlin.Pair;
import kotlin.Result;
import kotlin.ResultKt;
import kotlin.Unit;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\a.smali */
public class a implements SecureStorage {
    public static final m Companion = new m();
    public String a;
    public String b;
    public byte[] c;
    public volatile l d = new l(new HashMap(), new HashMap());

    public final void a(l lVar) {
        synchronized (this) {
            this.d = lVar;
            Unit unit = Unit.INSTANCE;
        }
    }

    public final void cipherAndWriteData$lib_release(byte[] key, Context context, boolean z, boolean z2) throws SecureStorageSDKException {
        String str;
        Intrinsics.checkNotNullParameter(key, "sek");
        Intrinsics.checkNotNullParameter(context, "context");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            Iterator it = this.d.a.entrySet().iterator();
            boolean z3 = true;
            while (true) {
                str = "0";
                if (!it.hasNext()) {
                    break;
                }
                Map.Entry entry = (Map.Entry) it.next();
                if (z3) {
                    z3 = false;
                } else {
                    byte[] bytes = k.a.getBytes(Charsets.UTF_8);
                    Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
                    byteArrayOutputStream.write(bytes);
                }
                String str2 = (String) entry.getKey();
                Charset charset = Charsets.UTF_8;
                byte[] bytes2 = str2.getBytes(charset);
                Intrinsics.checkNotNullExpressionValue(bytes2, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes2);
                String str3 = k.b;
                byte[] bytes3 = str3.getBytes(charset);
                Intrinsics.checkNotNullExpressionValue(bytes3, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes3);
                IVEncryptedData iVEncryptedData = (IVEncryptedData) entry.getValue();
                byte[] bArr = this.c;
                if (bArr == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                    bArr = null;
                }
                byteArrayOutputStream.write(f.a(iVEncryptedData, bArr));
                byte[] bytes4 = str3.getBytes(charset);
                Intrinsics.checkNotNullExpressionValue(bytes4, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes4);
                byte[] bytes5 = "0".getBytes(charset);
                Intrinsics.checkNotNullExpressionValue(bytes5, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes5);
            }
            for (Map.Entry entry2 : this.d.b.entrySet()) {
                IVEncryptedData iVEncryptedData2 = (IVEncryptedData) entry2.getValue();
                byte[] bArr2 = this.c;
                if (bArr2 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                    bArr2 = null;
                }
                byte[] a = f.a(iVEncryptedData2, bArr2);
                if (z3) {
                    z3 = false;
                } else {
                    byte[] bytes6 = k.a.getBytes(Charsets.UTF_8);
                    Intrinsics.checkNotNullExpressionValue(bytes6, "this as java.lang.String).getBytes(charset)");
                    byteArrayOutputStream.write(bytes6);
                }
                String str4 = (String) entry2.getKey();
                Charset charset2 = Charsets.UTF_8;
                byte[] bytes7 = str4.getBytes(charset2);
                Intrinsics.checkNotNullExpressionValue(bytes7, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes7);
                String str5 = k.b;
                byte[] bytes8 = str5.getBytes(charset2);
                Intrinsics.checkNotNullExpressionValue(bytes8, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes8);
                String a2 = y.a(a);
                Intrinsics.checkNotNullExpressionValue(a2, "bytesToHexa(value)");
                byte[] bytes9 = a2.getBytes(charset2);
                Intrinsics.checkNotNullExpressionValue(bytes9, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes9);
                byte[] bytes10 = str5.getBytes(charset2);
                Intrinsics.checkNotNullExpressionValue(bytes10, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes10);
                byte[] bytes11 = "1".getBytes(charset2);
                Intrinsics.checkNotNullExpressionValue(bytes11, "this as java.lang.String).getBytes(charset)");
                byteArrayOutputStream.write(bytes11);
            }
            byte[] data = byteArrayOutputStream.toByteArray();
            Intrinsics.checkNotNullExpressionValue(data, "stream.toByteArray()");
            byteArrayOutputStream.flush();
            byte[] bArr3 = e.a;
            UtilitiesSDKCryptoResponse generateRandomByteArray = UtilitiesSDK.generateRandomByteArray(16);
            if (generateRandomByteArray.getReturnCode() != 0) {
                v vVar = SecureStorageSDKException.Companion;
                int returnCode = generateRandomByteArray.getReturnCode();
                vVar.getClass();
                v.a(returnCode);
                throw null;
            }
            byte[] vector = generateRandomByteArray.getOutputData();
            Intrinsics.checkNotNullExpressionValue(vector, "res.outputData");
            Intrinsics.checkNotNullParameter(key, "key");
            Intrinsics.checkNotNullParameter(data, "data");
            Intrinsics.checkNotNullParameter(vector, "vector");
            UtilitiesSDKCryptoResponse encrypt = UtilitiesSDK.encrypt((byte) 3, (byte) 3, key, vector, data);
            y.b(data);
            Intrinsics.checkNotNull(encrypt);
            if (encrypt.getReturnCode() != 0) {
                v vVar2 = SecureStorageSDKException.Companion;
                int returnCode2 = encrypt.getReturnCode();
                vVar2.getClass();
                v.a(returnCode2);
                throw null;
            }
            if (z && z2) {
                str = "2";
            } else if (z && !z2) {
                str = "1";
            }
            String str6 = k.c + str + y.a(vector) + y.a(encrypt.getOutputData());
            byte[] a3 = y.a(str6);
            Intrinsics.checkNotNullExpressionValue(a3, "hexaToBytes(dataToHash)");
            String str7 = str6 + e.a(a3, key);
            FileOutputStream fileOutputStream = new FileOutputStream(new File(context.getNoBackupFilesDir(), g.a(this.a)));
            try {
                byte[] bytes12 = str7.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes12, "this as java.lang.String).getBytes(charset)");
                fileOutputStream.write(bytes12);
                Unit unit = Unit.INSTANCE;
                CloseableKt.closeFinally(fileOutputStream, null);
            } finally {
            }
        } catch (IOException e) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void clear() throws SecureStorageSDKException {
        this.d.b.clear();
        this.d.a.clear();
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public boolean contains(String key) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            l lVar = this.d;
            lVar.getClass();
            Intrinsics.checkNotNullParameter(key, "key");
            if (!lVar.a.containsKey(key)) {
                if (!lVar.b.containsKey(key)) {
                    return false;
                }
            }
            return true;
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void delete(Context context) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(context, "context");
        String str = this.a;
        if (str != null) {
            Companion.getClass();
            m.a(str, context);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public Map<String, Object> getAll() throws SecureStorageSDKException {
        try {
            HashMap hashMap = new HashMap();
            Iterator<T> it = getStringsMap().entrySet().iterator();
            while (true) {
                byte[] bArr = null;
                if (!it.hasNext()) {
                    break;
                }
                Map.Entry entry = (Map.Entry) it.next();
                IVEncryptedData iVEncryptedData = (IVEncryptedData) entry.getValue();
                byte[] bArr2 = this.c;
                if (bArr2 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                } else {
                    bArr = bArr2;
                }
                hashMap.put(entry.getKey(), new String(f.a(iVEncryptedData, bArr), Charsets.UTF_8));
            }
            Iterator<T> it2 = getBytesMap().entrySet().iterator();
            while (it2.hasNext()) {
                Map.Entry entry2 = (Map.Entry) it2.next();
                Object key = entry2.getKey();
                IVEncryptedData iVEncryptedData2 = (IVEncryptedData) entry2.getValue();
                byte[] bArr3 = this.c;
                if (bArr3 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                    bArr3 = null;
                }
                hashMap.put(key, f.a(iVEncryptedData2, bArr3));
            }
            return hashMap;
        } catch (Exception e) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public byte[] getBytes(String key) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            byte[] bArr = null;
            if (getStringsMap().containsKey(key)) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INCORRECT_GETTER, null, 2, null);
            }
            if (!getBytesMap().containsKey(key)) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_KEY, null, 2, null);
            }
            IVEncryptedData iVEncryptedData = getBytesMap().get(key);
            byte[] bArr2 = this.c;
            if (bArr2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
            } else {
                bArr = bArr2;
            }
            return f.a(iVEncryptedData, bArr);
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public Map<String, IVEncryptedData> getBytesMap() {
        return this.d.b;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public String getKeyName() throws SecureStorageSDKException {
        String a;
        String str = this.a;
        if (str == null || (a = f.a(str)) == null) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        }
        return a;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public String getString(String key) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            byte[] bArr = null;
            if (getBytesMap().containsKey(key)) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INCORRECT_GETTER, null, 2, null);
            }
            if (!getStringsMap().containsKey(key)) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_KEY, null, 2, null);
            }
            IVEncryptedData iVEncryptedData = getStringsMap().get(key);
            byte[] bArr2 = this.c;
            if (bArr2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
            } else {
                bArr = bArr2;
            }
            return new String(f.a(iVEncryptedData, bArr), Charsets.UTF_8);
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public Map<String, IVEncryptedData> getStringsMap() {
        return this.d.a;
    }

    @Deprecated(message = "This initialization method only works for non biometry protected storages")
    public final void init$lib_release(String fileName, String str, int i, Context context) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(fileName, "fileName");
        Intrinsics.checkNotNullParameter(context, "context");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.c(fileName);
            if (i <= 0) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ITERATION_COUNT_INCORRECT, null, 2, null);
            }
            i.a(fileName, context);
            String packageName = context.getPackageName();
            Intrinsics.checkNotNullExpressionValue(packageName, "context.packageName");
            this.b = packageName;
            this.a = fileName;
            byte[] bArr = e.a;
            UtilitiesSDKCryptoResponse generateRandomByteArray = UtilitiesSDK.generateRandomByteArray(32);
            if (generateRandomByteArray.getReturnCode() != 0) {
                v vVar = SecureStorageSDKException.Companion;
                int returnCode = generateRandomByteArray.getReturnCode();
                vVar.getClass();
                v.a(returnCode);
                throw null;
            }
            byte[] outputData = generateRandomByteArray.getOutputData();
            Intrinsics.checkNotNullExpressionValue(outputData, "res.outputData");
            this.c = outputData;
            if (!g.a(fileName, context)) {
                a(new l());
                return;
            }
            byte[] bArr2 = this.c;
            if (bArr2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                bArr2 = null;
            }
            Pair a = f.a(str, i, fileName, context, bArr2);
            boolean booleanValue = ((Boolean) a.component1()).booleanValue();
            l lVar = (l) a.component2();
            if (booleanValue) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_CORRUPTED_HMAC_INCONSISTENT, null, 2, null);
            }
            if (lVar != null) {
                a(lVar);
            }
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public boolean isSecureHardwareProtected() {
        try {
            String str = this.a;
            SecretKey c = str != null ? e.c(f.a(str)) : null;
            KeySpec keySpec = SecretKeyFactory.getInstance(c != null ? c.getAlgorithm() : null, k.d).getKeySpec(c, KeyInfo.class);
            Intrinsics.checkNotNull(keySpec, "null cannot be cast to non-null type android.security.keystore.KeyInfo");
            return ((KeyInfo) keySpec).isInsideSecureHardware();
        } catch (Exception e) {
            if ((e instanceof KeyStoreException) || (e instanceof CertificateException) || (e instanceof IOException) || (e instanceof NoSuchAlgorithmException) || (e instanceof InvalidKeySpecException) || (e instanceof NoSuchProviderException) || (e instanceof UnrecoverableKeyException) || (e instanceof SecureStorageSDKException)) {
                e.getMessage();
            }
            return false;
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void putBytes(String key, byte[] value) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        Intrinsics.checkNotNullParameter(value, "value");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            byte[] bArr = null;
            if (value == null) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.VALUE_NULL, null, 2, null);
            }
            this.d.a.remove(key);
            Map map = this.d.b;
            byte[] bArr2 = this.c;
            if (bArr2 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
            } else {
                bArr = bArr2;
            }
            map.put(key, f.b(value, bArr));
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void putString(String key, String value) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        Intrinsics.checkNotNullParameter(value, "value");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.b(value);
            this.d.b.remove(key);
            Map map = this.d.a;
            byte[] bytes = value.getBytes(Charsets.UTF_8);
            Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
            byte[] bArr = this.c;
            if (bArr == null) {
                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                bArr = null;
            }
            map.put(key, f.b(bytes, bArr));
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void remove(String key) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(key, "key");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a(key);
            if (this.d.a.containsKey(key)) {
                this.d.a.remove(key);
            } else {
                if (!this.d.b.containsKey(key)) {
                    throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_KEY, null, 2, null);
                }
                this.d.b.remove(key);
            }
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public int size() {
        return getBytesMap().size() + getStringsMap().size();
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void write(String str, int i, Context context) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(context, "context");
        try {
            String str2 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a;
            if (i <= 0) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ITERATION_COUNT_INCORRECT, null, 2, null);
            }
            byte[] a = a(str, i);
            cipherAndWriteData$lib_release(a, context, false, false);
            y.b(a);
        } catch (SecureStorageSDKException e) {
            y.b((byte[]) null);
            throw e;
        } catch (Exception e2) {
            y.b((byte[]) null);
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.SecureStorage
    public void writeBiometryProtected(String str, int i, BiometricWriteProtectionSettings biometricWriteProtectionSettings, SecureStorageBiometricWriteCallback biometricWriteCallback) throws SecureStorageSDKException {
        Intrinsics.checkNotNullParameter(biometricWriteProtectionSettings, "biometricWriteProtectionSettings");
        Intrinsics.checkNotNullParameter(biometricWriteCallback, "biometricWriteCallback");
        try {
            String str2 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.a;
            if (i <= 0) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ITERATION_COUNT_INCORRECT, null, 2, null);
            }
            if (biometricWriteProtectionSettings == null) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.VALUE_NULL, null, 2, null);
            }
            if (biometricWriteProtectionSettings.getTimeout() < 0) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INVALID_TIMEOUT_VALUE, null, 2, null);
            }
            FragmentActivity activity = biometricWriteProtectionSettings.getFragmentActivity();
            Intrinsics.checkNotNullParameter(activity, "activity");
            int canAuthenticate = BiometricManager.from(activity).canAuthenticate(32783);
            if (canAuthenticate == 0) {
                byte[] a = a(str, i);
                p pVar = new p(this, biometricWriteProtectionSettings, a, biometricWriteCallback);
                String str3 = this.a;
                if (str3 != null) {
                    c.a(str3, a, biometricWriteProtectionSettings, pVar);
                    return;
                }
                return;
            }
            FragmentActivity context = biometricWriteProtectionSettings.getFragmentActivity();
            Intrinsics.checkNotNullParameter(context, "context");
            if (canAuthenticate == 11) {
                String string = context.getString(R.string.biometric_none_enrolled_error_msg);
                Intrinsics.checkNotNullExpressionValue(string, "context.getString(R.stri…_none_enrolled_error_msg)");
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.BIOMETRIC_AUTH_NOT_ENROLLED_ERROR, new Throwable(string));
            }
            if (canAuthenticate != 12) {
                String string2 = context.getString(R.string.biometric_unspecified_error_msg);
                Intrinsics.checkNotNullExpressionValue(string2, "context.getString(R.stri…ic_unspecified_error_msg)");
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.BIOMETRIC_UNSPECIFIED_ERROR, new Throwable(string2));
            }
            String string3 = context.getString(R.string.biometric_hardware_error_msg);
            Intrinsics.checkNotNullExpressionValue(string3, "context.getString(R.stri…etric_hardware_error_msg)");
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.BIOMETRIC_NO_HARDWARE_ERROR, new Throwable(string3));
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    public final void init$lib_release(String filename, String str, int i, Context context, SecureStorageInitCallback initCallback) throws SecureStorageSDKException {
        boolean z;
        r rVar;
        Object m237constructorimpl;
        byte[] bArr;
        Intrinsics.checkNotNullParameter(filename, "fileName");
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(initCallback, "initCallback");
        try {
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.a.c(filename);
            if (i > 0) {
                i.a(filename, context);
                String packageName = context.getPackageName();
                Intrinsics.checkNotNullExpressionValue(packageName, "context.packageName");
                this.b = packageName;
                this.a = filename;
                byte[] bArr2 = e.a;
                UtilitiesSDKCryptoResponse generateRandomByteArray = UtilitiesSDK.generateRandomByteArray(32);
                if (generateRandomByteArray.getReturnCode() == 0) {
                    byte[] outputData = generateRandomByteArray.getOutputData();
                    Intrinsics.checkNotNullExpressionValue(outputData, "res.outputData");
                    this.c = outputData;
                    if (g.a(filename, context)) {
                        String c = g.c(filename, context);
                        Intrinsics.checkNotNullParameter(c, "<this>");
                        String substring = c.substring(0, 4);
                        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
                        byte[] storageEncryptionKey = new byte[32];
                        boolean areEqual = Intrinsics.areEqual(substring, x.c);
                        String newKeyName = f.a(filename);
                        String packageName2 = context.getPackageName();
                        Intrinsics.checkNotNullExpressionValue(packageName2, "context.packageName");
                        String str2 = j.a;
                        Intrinsics.checkNotNullParameter(newKeyName, "newKeyName");
                        Intrinsics.checkNotNullParameter(packageName2, "packageName");
                        Intrinsics.checkNotNullParameter(newKeyName, "newKeyName");
                        Intrinsics.checkNotNullParameter(packageName2, "packageName");
                        if (areEqual) {
                            newKeyName = packageName2;
                        }
                        try {
                            KeyStore keyStore = KeyStore.getInstance(j.a);
                            keyStore.load(null);
                            z = keyStore.containsAlias(newKeyName);
                        } catch (Exception e) {
                            z = false;
                        }
                        Intrinsics.checkNotNullParameter(filename, "filename");
                        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
                        Intrinsics.checkNotNullParameter(context, "context");
                        byte[] a = e.a();
                        String a2 = f.a(filename);
                        String packageName3 = context.getPackageName();
                        Intrinsics.checkNotNullExpressionValue(packageName3, "context.packageName");
                        boolean a3 = e.a(str, i, a, z, storageEncryptionKey, a2, packageName3);
                        if (Intrinsics.areEqual(substring, "0006")) {
                            t tVar = new t(initCallback.getFragmentActivity());
                            n nVar = new n(initCallback, this);
                            Intrinsics.checkNotNullParameter(nVar, "<set-?>");
                            tVar.b = nVar;
                            rVar = tVar;
                        } else {
                            r rVar2 = new r(substring, context);
                            o oVar = new o(initCallback, this);
                            Intrinsics.checkNotNullParameter(oVar, "<set-?>");
                            rVar2.c = oVar;
                            rVar = rVar2;
                        }
                        try {
                            Result.Companion companion = Result.INSTANCE;
                            byte[] bArr3 = this.c;
                            if (bArr3 == null) {
                                Intrinsics.throwUninitializedPropertyAccessException("dataEncryptionKey");
                                bArr = null;
                            } else {
                                bArr = bArr3;
                            }
                            rVar.a(c, bArr, filename, i, storageEncryptionKey, a3, str);
                            m237constructorimpl = Result.m237constructorimpl(Unit.INSTANCE);
                        } catch (Throwable th) {
                            Result.Companion companion2 = Result.INSTANCE;
                            m237constructorimpl = Result.m237constructorimpl(ResultKt.createFailure(th));
                        }
                        Throwable m240exceptionOrNullimpl = Result.m240exceptionOrNullimpl(m237constructorimpl);
                        if (m240exceptionOrNullimpl != null) {
                            if (!(m240exceptionOrNullimpl instanceof SecureStorageSDKException)) {
                                initCallback.onInitFailed(new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, m240exceptionOrNullimpl));
                                return;
                            } else {
                                initCallback.onInitFailed((SecureStorageSDKException) m240exceptionOrNullimpl);
                                return;
                            }
                        }
                        return;
                    }
                    a(new l());
                    initCallback.onInitSuccess(this);
                    return;
                }
                v vVar = SecureStorageSDKException.Companion;
                int returnCode = generateRandomByteArray.getReturnCode();
                vVar.getClass();
                v.a(returnCode);
                throw null;
            }
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.ITERATION_COUNT_INCORRECT, null, 2, null);
        } catch (SecureStorageSDKException e2) {
            initCallback.onInitFailed(e2);
        } catch (Exception e3) {
            initCallback.onInitFailed(new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e3));
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:9:0x0032, code lost:
    
        if (r4.containsAlias(r0) == false) goto L12;
     */
    /* JADX WARN: Removed duplicated region for block: B:17:0x004d A[Catch: Exception -> 0x0098, TryCatch #1 {Exception -> 0x0098, blocks: (B:15:0x0045, B:17:0x004d, B:18:0x0051, B:22:0x0076, B:24:0x007e, B:25:0x0084, B:30:0x008f, B:31:0x0097), top: B:14:0x0045 }] */
    /* JADX WARN: Removed duplicated region for block: B:24:0x007e A[Catch: Exception -> 0x0098, TryCatch #1 {Exception -> 0x0098, blocks: (B:15:0x0045, B:17:0x004d, B:18:0x0051, B:22:0x0076, B:24:0x007e, B:25:0x0084, B:30:0x008f, B:31:0x0097), top: B:14:0x0045 }] */
    /* JADX WARN: Removed duplicated region for block: B:27:0x008e  */
    /* JADX WARN: Removed duplicated region for block: B:30:0x008f A[Catch: Exception -> 0x0098, TryCatch #1 {Exception -> 0x0098, blocks: (B:15:0x0045, B:17:0x004d, B:18:0x0051, B:22:0x0076, B:24:0x007e, B:25:0x0084, B:30:0x008f, B:31:0x0097), top: B:14:0x0045 }] */
    /* JADX WARN: Removed duplicated region for block: B:33:0x0083  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final byte[] a(java.lang.String r13, int r14) {
        /*
            r12 = this;
            boolean r0 = r12.isSecureHardwareProtected()
            java.lang.String r1 = "newKeyName"
            r2 = 0
            java.lang.String r3 = "packageName"
            if (r0 == 0) goto L3d
            java.lang.String r0 = r12.getKeyName()
            java.lang.String r4 = r12.b
            if (r4 != 0) goto L17
            kotlin.jvm.internal.Intrinsics.throwUninitializedPropertyAccessException(r3)
            r4 = r2
        L17:
            java.lang.String r5 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.j.a
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r0, r1)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r4, r3)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r0, r1)
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r4, r3)
            java.lang.String r4 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.j.a     // Catch: java.lang.Exception -> L35
            java.security.KeyStore r4 = java.security.KeyStore.getInstance(r4)     // Catch: java.lang.Exception -> L35
            r4.load(r2)     // Catch: java.lang.Exception -> L35
            boolean r0 = r4.containsAlias(r0)     // Catch: java.lang.Exception -> L35
            if (r0 != 0) goto L3d
            goto L36
        L35:
            r0 = move-exception
        L36:
            java.lang.String r0 = r12.getKeyName()
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.a(r0)
        L3d:
            byte[] r0 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.a()
            r4 = 32
            byte[] r11 = new byte[r4]
            java.lang.String r4 = r12.getKeyName()     // Catch: java.lang.Exception -> L98
            java.lang.String r5 = r12.b     // Catch: java.lang.Exception -> L98
            if (r5 != 0) goto L51
            kotlin.jvm.internal.Intrinsics.throwUninitializedPropertyAccessException(r3)     // Catch: java.lang.Exception -> L98
            r5 = r2
        L51:
            java.lang.String r6 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.j.a     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r4, r1)     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r5, r3)     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r4, r1)     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r5, r3)     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r4, r1)     // Catch: java.lang.Exception -> L98
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r5, r3)     // Catch: java.lang.Exception -> L98
            java.lang.String r1 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.j.a     // Catch: java.lang.Exception -> L73
            java.security.KeyStore r1 = java.security.KeyStore.getInstance(r1)     // Catch: java.lang.Exception -> L73
            r1.load(r2)     // Catch: java.lang.Exception -> L73
            boolean r1 = r1.containsAlias(r4)     // Catch: java.lang.Exception -> L73
            goto L75
        L73:
            r1 = move-exception
            r1 = 0
        L75:
            r7 = r1
            java.lang.String r9 = r12.getKeyName()     // Catch: java.lang.Exception -> L98
            java.lang.String r1 = r12.b     // Catch: java.lang.Exception -> L98
            if (r1 != 0) goto L83
            kotlin.jvm.internal.Intrinsics.throwUninitializedPropertyAccessException(r3)     // Catch: java.lang.Exception -> L98
            r10 = r2
            goto L84
        L83:
            r10 = r1
        L84:
            r4 = r13
            r5 = r14
            r6 = r0
            r8 = r11
            boolean r1 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.a(r4, r5, r6, r7, r8, r9, r10)     // Catch: java.lang.Exception -> L98
            if (r1 != 0) goto L8f
            goto Lca
        L8f:
            com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException r1 = new com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException     // Catch: java.lang.Exception -> L98
            r4 = -4316(0xffffffffffffef24, float:NaN)
            r5 = 2
            r1.<init>(r4, r2, r5, r2)     // Catch: java.lang.Exception -> L98
            throw r1     // Catch: java.lang.Exception -> L98
        L98:
            r1 = move-exception
            java.lang.String r1 = r12.getKeyName()
            java.lang.String r4 = "keyName"
            kotlin.jvm.internal.Intrinsics.checkNotNullParameter(r1, r4)
            java.lang.String r4 = com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.i     // Catch: java.lang.Exception -> Lcb
            java.security.KeyStore r4 = java.security.KeyStore.getInstance(r4)     // Catch: java.lang.Exception -> Lcb
            r4.load(r2)     // Catch: java.lang.Exception -> Lcb
            boolean r5 = r4.containsAlias(r1)     // Catch: java.lang.Exception -> Lcb
            if (r5 == 0) goto Lb4
            r4.deleteEntry(r1)     // Catch: java.lang.Exception -> Lcb
        Lb4:
            r7 = 0
            java.lang.String r9 = r12.getKeyName()
            java.lang.String r1 = r12.b
            if (r1 != 0) goto Lc2
            kotlin.jvm.internal.Intrinsics.throwUninitializedPropertyAccessException(r3)
            r10 = r2
            goto Lc3
        Lc2:
            r10 = r1
        Lc3:
            r4 = r13
            r5 = r14
            r6 = r0
            r8 = r11
            com.vasco.digipass.sdk.utils.securestorage.obfuscated.e.a(r4, r5, r6, r7, r8, r9, r10)
        Lca:
            return r11
        Lcb:
            r13 = move-exception
            com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException r14 = new com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException
            r0 = -4300(0xffffffffffffef34, float:NaN)
            r14.<init>(r0, r13)
            throw r14
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.utils.securestorage.a.a(java.lang.String, int):byte[]");
    }
}
