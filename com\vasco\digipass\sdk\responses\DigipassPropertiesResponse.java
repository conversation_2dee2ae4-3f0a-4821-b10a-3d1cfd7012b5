package com.vasco.digipass.sdk.responses;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\DigipassPropertiesResponse.smali */
public class DigipassPropertiesResponse extends DigipassResponse {
    private boolean A;
    private byte B;
    private byte C;
    private byte D;
    private boolean E;
    private boolean F;
    private boolean G;
    private Application H;
    private byte I;
    private byte J;
    private byte K;
    private byte L;
    private boolean M;
    private byte c;
    private byte d;
    private String e;
    private boolean f;
    private boolean g;
    private int h;
    private int i;
    private boolean j;
    private byte k;
    private boolean l;
    private byte m;
    private byte n;

    /* renamed from: o, reason: collision with root package name */
    private byte f19o;
    private byte p;
    private boolean q;
    private boolean r;
    private boolean s;
    private boolean t;
    private boolean u;
    private Application[] v;
    private long w;
    private byte[] x;
    private int y;
    private byte z;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\DigipassPropertiesResponse$Application.smali */
    public static class Application {
        private String authenticationMode;
        private boolean checkDigit;
        private byte[] codeword;
        private byte dataFieldNumber;
        private byte[] dataFieldsMaxLength;
        private byte[] dataFieldsMinLength;
        private boolean dpPlus;
        private boolean enabled;
        private boolean eventBased;
        private long eventCounter;
        private int hostCodeLength;
        private byte index;
        private long lastTimeUsed;
        private String name;
        private byte outputType;
        private int responseLength;
        private boolean scoreCapable;
        private boolean timeBased;
        private int timeStep;

        public String getAuthenticationMode() {
            return this.authenticationMode;
        }

        public byte[] getCodeword() {
            return this.codeword;
        }

        public byte getDataFieldNumber() {
            return this.dataFieldNumber;
        }

        public byte[] getDataFieldsMaxLength() {
            return this.dataFieldsMaxLength;
        }

        public byte[] getDataFieldsMinLength() {
            return this.dataFieldsMinLength;
        }

        public long getEventCounter() {
            return this.eventCounter;
        }

        public int getHostCodeLength() {
            return this.hostCodeLength;
        }

        public byte getIndex() {
            return this.index;
        }

        public long getLastTimeUsed() {
            return this.lastTimeUsed;
        }

        public String getName() {
            return this.name;
        }

        public byte getOutputType() {
            return this.outputType;
        }

        public int getResponseLength() {
            return this.responseLength;
        }

        public int getTimeStep() {
            return this.timeStep;
        }

        public boolean isCheckDigit() {
            return this.checkDigit;
        }

        public boolean isDpPlus() {
            return this.dpPlus;
        }

        public boolean isEnabled() {
            return this.enabled;
        }

        public boolean isEventBased() {
            return this.eventBased;
        }

        public boolean isScoreCapable() {
            return this.scoreCapable;
        }

        public boolean isTimeBased() {
            return this.timeBased;
        }

        public void setAuthenticationMode(String str) {
            this.authenticationMode = str;
        }

        public void setCheckDigit(boolean z) {
            this.checkDigit = z;
        }

        public void setCodeword(byte[] bArr) {
            this.codeword = bArr;
        }

        public void setDataFieldNumber(byte b) {
            this.dataFieldNumber = b;
        }

        public void setDataFieldsMaxLength(byte[] bArr) {
            this.dataFieldsMaxLength = bArr;
        }

        public void setDataFieldsMinLength(byte[] bArr) {
            this.dataFieldsMinLength = bArr;
        }

        public void setDpPlus(boolean z) {
            this.dpPlus = z;
        }

        public void setEnabled(boolean z) {
            this.enabled = z;
        }

        public void setEventBased(boolean z) {
            this.eventBased = z;
        }

        public void setEventCounter(long j) {
            this.eventCounter = j;
        }

        public void setHostCodeLength(int i) {
            this.hostCodeLength = i;
        }

        public void setIndex(byte b) {
            this.index = b;
        }

        public void setLastTimeUsed(long j) {
            this.lastTimeUsed = j;
        }

        public void setName(String str) {
            this.name = str;
        }

        public void setOutputType(byte b) {
            this.outputType = b;
        }

        public void setResponseLength(int i) {
            this.responseLength = i;
        }

        public void setScoreCapable(boolean z) {
            this.scoreCapable = z;
        }

        public void setTimeBased(boolean z) {
            this.timeBased = z;
        }

        public void setTimeStep(int i) {
            this.timeStep = i;
        }
    }

    public DigipassPropertiesResponse(int i) {
        super(i);
    }

    public Application getActivationCryptoApplication() {
        return this.H;
    }

    public Application[] getApplications() {
        return this.v;
    }

    public byte getCreationVersion() {
        return this.D;
    }

    public byte getDeviceIdBitsNumber() {
        return this.I;
    }

    public byte getDeviceType() {
        return this.J;
    }

    public int getIterationNumber() {
        return this.y;
    }

    public byte getIterationPower() {
        return this.z;
    }

    public byte[] getMasterKey() {
        return this.x;
    }

    public byte getPasswordCheckLevel() {
        return this.k;
    }

    public byte getPasswordFatal() {
        return this.m;
    }

    public byte getPasswordFatalCounter() {
        return this.n;
    }

    public int getPasswordMaxLength() {
        return this.i;
    }

    public int getPasswordMinLength() {
        return this.h;
    }

    public byte getPayloadKeyType() {
        return this.L;
    }

    public byte getPinVersion() {
        return this.C;
    }

    public byte getReactivationFatal() {
        return this.f19o;
    }

    public byte getReactivationFatalCounter() {
        return this.p;
    }

    public byte getSequenceNumber() {
        return this.K;
    }

    public String getSerialNumber() {
        return this.e;
    }

    public byte getStatus() {
        return this.d;
    }

    public byte getStorageVersion() {
        return this.B;
    }

    public long getUtcTime() {
        return this.w;
    }

    public byte getVersion() {
        return this.c;
    }

    public boolean isActivationCodeFormatHexa() {
        return this.t;
    }

    public boolean isDpPlusHighSecurity() {
        return this.s;
    }

    public boolean isHighSecurity() {
        return this.r;
    }

    public boolean isMultiDeviceActivationEnabled() {
        return this.G;
    }

    public boolean isPasswordDerivationActivated() {
        return this.F;
    }

    @Deprecated
    public boolean isPasswordEnabled() {
        return this.f;
    }

    public boolean isPasswordMandatory() {
        return this.f;
    }

    public boolean isPasswordProtected() {
        return this.g;
    }

    public boolean isPenaltyResetAction() {
        return this.l;
    }

    public boolean isSecureChannelEnabled() {
        return this.M;
    }

    public boolean isTokenDerivationActivated() {
        return this.E;
    }

    public boolean isTokenDerivationSupported() {
        return this.q;
    }

    public boolean isUseChecksumForActivationCode() {
        return this.u;
    }

    public boolean isUseSecretDerivation() {
        return this.A;
    }

    public boolean isWeakPasswordControl() {
        return this.j;
    }

    public void setActivationCodeFormatHexa(boolean z) {
        this.t = z;
    }

    public void setActivationCryptoApplication(Application application) {
        this.H = application;
    }

    public void setApplications(Application[] applicationArr) {
        this.v = applicationArr;
    }

    public void setCreationVersion(byte b) {
        this.D = b;
    }

    public void setDeviceIdBitsNumber(byte b) {
        this.I = b;
    }

    public void setDeviceType(byte b) {
        this.J = b;
    }

    public void setDpPlusHighSecurity(boolean z) {
        this.s = z;
    }

    public void setHighSecurity(boolean z) {
        this.r = z;
    }

    public void setIterationNumber(int i) {
        this.y = i;
    }

    public void setIterationPower(byte b) {
        this.z = b;
    }

    public void setMasterKey(byte[] bArr) {
        this.x = bArr;
    }

    public void setMultiDeviceActivationEnabled(boolean z) {
        this.G = z;
    }

    public void setPasswordCheckLevel(byte b) {
        this.k = b;
    }

    public void setPasswordDerivationActivated(boolean z) {
        this.F = z;
    }

    public void setPasswordFatal(byte b) {
        this.m = b;
    }

    public void setPasswordFatalCounter(byte b) {
        this.n = b;
    }

    public void setPasswordMandatory(boolean z) {
        this.f = z;
    }

    public void setPasswordMaxLength(int i) {
        this.i = i;
    }

    public void setPasswordMinLength(int i) {
        this.h = i;
    }

    public void setPasswordProtected(boolean z) {
        this.g = z;
    }

    public void setPayloadKeyType(byte b) {
        this.L = b;
    }

    public void setPenaltyResetAction(boolean z) {
        this.l = z;
    }

    public void setPinVersion(byte b) {
        this.C = b;
    }

    public void setReactivationFatal(byte b) {
        this.f19o = b;
    }

    public void setReactivationFatalCounter(byte b) {
        this.p = b;
    }

    public void setSecureChannelEnabled(boolean z) {
        this.M = z;
    }

    public void setSequenceNumber(byte b) {
        this.K = b;
    }

    public void setSerialNumber(String str) {
        this.e = str;
    }

    public void setStatus(byte b) {
        this.d = b;
    }

    public void setStorageVersion(byte b) {
        this.B = b;
    }

    public void setTokenDerivationActivated(boolean z) {
        this.E = z;
    }

    public void setTokenDerivationSupported(boolean z) {
        this.q = z;
    }

    public void setUseChecksumForActivationCode(boolean z) {
        this.u = z;
    }

    public void setUseSecretDerivation(boolean z) {
        this.A = z;
    }

    public void setUtcTime(long j) {
        this.w = j;
    }

    public void setVersion(byte b) {
        this.c = b;
    }

    public void setWeakPasswordControl(boolean z) {
        this.j = z;
    }

    public DigipassPropertiesResponse(int i, Throwable th) {
        super(i, th);
    }
}
