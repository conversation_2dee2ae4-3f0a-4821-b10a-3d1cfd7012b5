package o.b;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.recyclerview.widget.ItemTouchHelper;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.io.File;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.az.d;
import o.b.c;
import o.bf.b;
import o.bg.b;
import o.bh.a;
import o.de.f;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    static boolean b;
    static final Object c;
    static final List<a> d;
    private static char[] f;
    private static final List<a> g;
    private static boolean h;
    private static boolean i;
    private static long j;
    private static int l;

    /* renamed from: o, reason: collision with root package name */
    private static int f38o;
    final o.ei.c a;
    o.az.d e;

    static void b() {
        char[] cArr = new char[2159];
        ByteBuffer.wrap(",\u008aëJ£\u007f{y3\u001cË-\u00835[\u009d\u0013Ê+äã\u009f»\u0083s¶\nAÂ\u0001\u009a\u007fRljE\"?ú8²ÕJùN\u001a\u0089ÙÁ÷\u0019úQ\u0087©¨á£9xqZI{\u0081\u000fÙ\u001a\u0011?hê Òøá0ú\b\u0090@¦\u0098\u00adÝ\u0080\u001a`RU\u008aIÂ6:\u001cr\u001f,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0090»\u008es\u00ad\n\\ÂW\u009a|R}j\f\">ú#²\u0099Jú\u0002ïÚÝ\u0092\u008eªªaX9Cñ~\u00195ÞÕ\u0096àNü\u0006\u0083þ©¶ªn\u0002&\u001b\u001e:Ö\u001c\u008e\u0017F!?Ã÷Í¯ögó_\u0088\u0017§Ï¼\u0087A\u007f*7}ï\r§\u0018\u009f4TË\fÑÄò¼Êt\u008c,§ä§ÜO\u0094kLa\u0004\u0012üj´?mÑ%\u0096\u001døÕ\u0081\u008d\u0087E¨=Nõ\u001e\u00adpes]\u000b\u0015;Í7\u0082Õzþ,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0083»\u0088s¾\n\\ÂR\u009aiRlj\u0017\"8ú#²ÞJµ\u0002âÚ\u0092\u0092\u0087ª«aT9Nñm\u0089UA\u0013\u00198Ñ8éÐ¡ôyþ1\u008dÉõ\u0081 XN\u0010\t(uà\u0014¸\u0003p=\bÜÀÏ\u0098úP©h\u0097 ´ø¼·LOp\u0007rß\t,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0082»\u0099s¸\nGÂU\u009atRgj\u0002\"qú.²ÖJû\u0002ïÚ\u0098\u0092\u008aª±O\f\u0088ìÀÙ\u0018ÅPº¨\u0090à\u00938;p\"H\u0003\u0080 Ø*\u0010\u0013iÿ¡âùÏ1\u008f\tªA\u0084\u0099ËÑ~)_aU¹>ñ.É\u0007\u0002îZ«\u0092Íê¦\"©z\u0095²\u0086\u008amÂP,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0086»\u008csµ\nYÂD\u009aiR)j\f\"\"úm²×Jú\u0002õÚÝ\u0092\u0090ª aE9\rñk\u0089\u0000A\u000f\u00193Ñ éË¡ö,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0092»\u0082s·\nAÂD\u009aeR}jE\"!ú?²ÖJã\u0002èÚ\u0099\u0092\u008cª¡a\u0011Gf\u0080\u0086È³\u0010¯XÐ úèù0QxH@i\u0088\\ÐT\u0018aa\u0091©\u0088ñ¿9±\u0001ÀIþ\u0091àÙ\u0001!0i\"±_ù\u0005Áj\n\u0092R\u008f\u009a¡âÜ*Õråº¥\u0082\u0019Ê/\u0012.ZC¢pêi3\u0094{\u0081,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0090»\u0098s\u00ad\n]ÂD\u009asR}j\f\"2ú,²ÍJü\u0002îÚ\u0093\u0092Éª°aA9Iñx\u0089\u0001A\u0004\u0019}Ñ;éÀ¡àyø1\u009cÉ¦\u0081µX\u001d\u0010Y(wà\u001e¸\u001bp0\bÑÀÄ\u0098ùz ½Àõõ-ée\u0096\u009d¼Õ¿\r\u0017E\u000e}/µ\u0018í\u000f%6\\Ü\u0094ÀÌþ\u0004í<\u0088tû¬¡ä\\\u001cmT+\u008c\u0016ÄCü?7ÔoÔ§àß\u0096\u0017\u0089O»\u0087¦¿\u000f÷z/dg\u0007\u009f6×=\u000eÖF×~æ¶\u0094î\u0089&ó^K\u0096YÎ~\u0006d>\bv>®5áÚ\u0019ñQì,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0086»\u008csµ\nYÂD\u009aiR)j\u0016\"%ú,²ÍJà\u0002òÚÝ\u0092Óªå,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0086»\u008csµ\nYÂD\u009aiR)j\u000b\">ú9²\u0099Jå\u0002óÚ\u0092\u0092\u009fª¬aB9Dñv\u0089\u001bA\u0004\u00199,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0092»\u0085s¼\nVÂJ\u009a=RFj6\"qú>²ÜJç\u0002÷Ú\u0094\u0092\u008aª a\u00119^ñl\u0089\u0005A\u0011\u00192Ñ;éÑ¡±yâ1\u0097Éõ\u0081¥XX\u0010_(là\u0012¸\b,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u009f»\u0082sù\nzÂr\u009a=RZj\u0000\"#ú;²ÐJö\u0002äÚÝ\u0092\u0088ª³aP9Dñu\u0089\u0014A\u0003\u00191Ñ,é\u0085¡þyã1ÙÉ±\u0081¤XK\u0010@(fà\u0014¸WpyÉ\u0014\u000eôFÁ\u009eÝÖ¢.\u0088f\u008b¾#ö:Î\u001b\u0006,^;\u0096\u0002ïè'ô\u007f\u0083·Ç\u008f¾Ç\u009d\u001f\u009eWn¯XçL?*w8O\u0015\u0084ü,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u009c»\u008cs·\nQÂ@\u009aiRfj\u0017\"(úm²ÉJð\u0002óÚ\u0090\u0092\u0080ª¶aB9Dñv\u0089\u001bA\u0012\u0019}Ñ(é×¡ôy\u00ad1\u0094É¼\u0081²XN\u0010@(kà\u0016;Jüª´\u009fl\u0083$üÜÖ\u0094ÕL}\u0004d<Eôr¬bdW\u001d¦Õ´\u008d\u0091E\u009d}ì5ßíÊ¥y]\u0016\u0015\u000eÍs\u0085g½@v².¹æ\u0090\u009eúVï\u000e\u009dÆÈþ0¶\u0005n\u0005&vÞG\u0096HO©\u0007°K³\u008cSÄf\u001czT\u0005¬/ä,<\u0084t\u009dL¼\u0084\u0089Ü\u0097\u0014´mE¥Nýe5d\r\u0015E'\u009d:Õ\u0080-íeô½\u0088õ\u009fÍ«\u0006M^P¯óh\u0013 &ø:°EHo\u0000lØÄ\u0090Ý¨ü`É8×ðô\u0089\u0005A\u000e\u0019%Ñ$éU¡gyz1ÀÉ\u00ad\u0081´YÈ\u0011ß)ëâ\rº\u0010r`\n[ÂQ\u009apRxjÜ\"©ú¡²ÔJä\u0002ýÛ\n\u0093\u0004«5cK;Uót\u008b\u0085C\u0097\u001bª,ªëJ£\u007f{c3\u001cË6\u00835[\u009d\u0013\u0084+¥ã\u0083»\u0088s¨\n@ÂH\u009aoRlj\u0001\"qú,²ÌJá\u0002éÚ\u0098\u0092\u0087ª±aX9Nñx\u0089\u0001A\b\u00192Ñ'é\u0085¡ôyõ1\u0089É¹\u0081¨X^\u0010@(qà\u001d¸\u0014py\bÜÀÏ\u0098ùPàh\u0086 °ø¹·\\Oq\u0007!ß\t\u0097\u0006¯egÓ?È÷¹\u008f\u0086G\u008a\u001f´×¹îU¦t~iºk}\u008b5¾í¢¥Ý]÷\u0015ôÍ\\\u0085E½duQ-Oål\u009c\u009dT\u0096\f½Ä¼üÍ´ÿlâ$XÜ:\u0094/LH\u0004\b<e÷\u009c¯\u0080g·\u001fÃ×Å\u008føG¤\u007fD74ï)§[_x\u0017iÎ\u0092\u0086\u0081¾ªv×,¥ëD£d{c3\u001aË=\u0083\u0002[Ò\u0013Ç+ëã\u0094»\u008es\u00ad\nsÂS\u009arRdj+\"4ú5²ÍJÅ\u0002äÚ\u0093\u0092\u008dª¬a_9JñK\u0089\u0010A\u0010\u0019(Ñ,éÖ¡åy\u00ad1ÔÉõ\u0081\u00adX\\\u0010\\(kà\u0012¸\u0005p0\bÛÀÆ\u0098½Pçh\u0080 ©ø¹·\u0019Og\u0007dß\f\u0097\u001c¯ gÂ?Ù÷¹\u008f\u0087G\u0084\u001fº× îV¦e~h6\u000bÎ0\u0086%,½ëW£p{c3\nË3\u0083$[Ï\u0013è+æã\u0085»\u0084s¯\nTÂU\u009atRfj\u000b\"\u0005ú\"²÷Jð\u0002ùÚ\u0089\u0092«ªªaD9Cñ}\u00898A\u0000\u00193Ñ(éÂ¡ôyÿ1ÙÉø\u0081áXI\u0010[(dà\u001f¸\u001ep?\bÐÀÓ\u0098ïPàh\u008b ¶øí·XOv\u0007uß\u0014\u0097\u001f¯$gÅ?Ä÷ö\u008f\u009bGÁ\u001f©×¦î\u0005¦\u007f~h6\u0001Î!\u0086a^ß\u0016Æ.ðæ\u009f¾\u0089vù\rGÅD\u009dlU|m\u0000%\"ý9Qí\u0096\nÞ\u0010\u0006.NM¶|þw&\u009cn\u009dV¬\u009eÞÆÃ,ºë@£e{X3\tË\u0014\u0083\"[É\u0013À+óã\u0090»\u0099s°\nZÂO\u009a^Rhj\t\"=ú/²ØJö\u0002ê,®ë@£e{D3\u0017Ë;\u0083$[Ï\u0013þ+äã\u009d»\u0081s¼\nAÂ\u0001\u009a0R)j\u0017\">ú8²ÍJü\u0002ïÚ\u0098\u0092Éªªa_9\rñz\u0089\u001aA\u000f\u00193Ñ,éÆ¡åy\u00ad1ÔÉõ\u0081³XX\u0010O(wà\u0014¸\u001ep1\b\u0095ÀÂ\u0098ëPä\"8åÙ\u00ad÷uû=\u0083Å®\u008dºUM\u001d_%díDµB}l,\u00adëL£b{n3\u0016Ë;\u0083/[Ø\u0013Ê+ñãÑ»×sù\nTÂB\u009aiR`j\u0013\"0ú9²ÐJú\u0002ïÚÝ\u0092\u0086ª«a\u00119Jñv\u0089\u001cA\u000f\u0019:ù\b>évÇ®Ëæ³\u001e\u009eV\u008a\u008e}ÆoþT6tne¦\\ßô\u0017íOË\u0087Ï¿¯÷\u009a/\u0086gy\u009fS×P\u000f1G#\u007f\u000e´´ìî$Î\\¿\u0094©ÌØ\u0004\u008d<ct@¬Aä*\u001c\u0011T\u0010\u008dñÅãýÎ5ômº¥\u0099Ýc\u0015tMW\u0085B½3õ\u001d-\nbð\u009aÕÒ\u0084\n¹B¢z\u0094²qêd\"SZ \u0092dÊ\u000f\u0002\r;ìsØ«Íã¨\u001bÐS\u0089\u008byÃbûA33k-£\u000e|\u0010»ñóß+Óc«\u009b\u0086Ó\u0092\u000beCw{L³lë}#DZæ\u0092óÊ\u0080\u0002Û:¬r\u0084ª\u0095âv\u001a\bR_\u008a!Â8ú\u00141îiñ¡ÇÙ£\u0011üI\u0089\u0081\u0087¹8ñ^)Ua#\u0099\u0001Ñ\u000f\bô@ñxÊ°©è´ ÄX2\u0090<ÈK\u0000]84p\u0000¨\u0019çê\u001fÏW\u009c\u008f¡Ç·ÿ\u008c7eof§Eß<\u00175O\u000f\u0087\u001aG\u0018\u0080ùÈ×\u0010ÛX£ \u008eè\u009a0mx\u007f@D\u0088dÐu\u0018Laä©ýñÛ9ß\u0001¿I\u008a\u0091\u0096Ùi!Ci@±!ù3Á\u001e\n¤Rþ\u009aÞâ¯*¹rÈº\u0091\u0082qÊJ\u0012YZ+¢\u0005ê\u00063¨{òCß\u008b°Óø\u001b\u009ece«góX;S\u0003>K\u0017\u0093\u0011Üî$ÌlÑ´èü³Ä\u0096\f$Ty\u009cOä4,=t\u001e¼\u001d\u0085äÍÍ\u0015×]¢,¥ëJ£r{f3?Ë'\u0083.[Ð\u0013è+õã\u0081Ø\u0092\u001fHWq\u008fmÇ\u0010?-,\u009eëD£}{a3\u001cË!\u0083a[Ô\u0013Ú+¥ã\u009f»\u0082s\u00ad\n\u0015ÂS\u009ahRgj\u000b\"8ú#²ÞJ¹\u0002¡Ú\u009e\u0092\u0086ª«a_9Hñz\u0089\u0001A\b\u00192Ñ'é\u0085¡ãyè1\u0088É \u0081¨XO\u0010L(a½°z_2cêw¢\u0019Z4\u0012\u0012ÊÚ\u0082Óºýr¥*\u0088â¼,ªëD£\u007f{n3\u001cË9\u0083\u0000[È\u0013Ý+íã\u0094»\u0083s\u00ad\n\\ÂB\u009a|R}j\f\">ú#²øJû\u0002åÚ©\u0092\u009bª¤a_9^ñx\u0089\u0016A\u0015\u00194Ñ&éË,¯ëW£?{l3\u0017Ë!\u0083$[Ñ\u0013Æ+õãß»\u008cs·\nAÂD\u009aqRfj\u0015\"9ú.²ÜJù\u0002èÚ\u009f\u0092\u009bª¤aC9TñF\u0089\u0006A\t\u0019<Ñ;éÀ¡õyÒ1\u0089É§\u0081¤X[\u0010L(wà\u0014¸\u0003p:\bÐÀÒ,ºëQ#'äÉ¬ìt×<\u0084Ä½\u008c¼TA\u001cS$,ìU´D|\"\u0005ÙÍÜ\u0095æ]ée\u0089-®õ¡½TE<\r~Õ\u0015\u009d\f¥9nÝ6\u0084þô\u0086\u0093N\u008d\u0016§ÞàæB®wvp>PÆ1\u008e)WÀ\u001fÃ'äïØ·\u0085\u007f¾\u0007EÏ\b\u0097g_tg\r/,÷1¸Ã@¼\b²ÐÔ\u0098\u0082 \u00adh[0Oø0\u0080\bH\u0007\u0010tØ\u000eáÃ©ìqÔ9\u0082Á³\u0089¾Q]\u0019S!eé\u0017±\ny5\u0002Ø,®ë@£e{^3\rË4\u00835[È\u0013Ú+¥ãÜ»Ís«\nPÂU\u009ahR{j\u000b\"kúm'òà\b¨-p\u00168EÀ|\u0088}P\u0080\u0018\u0092 íè\u0094°\u0085xâ\u0001\tÉ\b\u0091!Y4a^)#ñ%(\u000bïÿ§É\u007fÌ7\u0098Ï\u0085\u0087\u0083_\u007f\u0017{/[ç$¿9w'\u000eêÆÜ\u009eÃVßn½&\u008e(\u0014ïû,ºë@£e{^3\u0012Ë<\u00831[í\u0013È+öã\u0082»\u008es¶\nQÂD\u009aRRgj)\">ú*²ÐJû\u0002¡ÚÐ\u0092Éª¶aZ9Dñi\u0089%A\u0000\u0019.Ñ:éÆ¡þyé1\u009cÉï\u0081á,¥ëJ£r{f3.Ë4\u0083-[Ñ\u0013Ì+ñ,¥ëJ£r{f3.Ë4\u0083-[Ñ\u0013Ì+ñãÑ»Àsù\nvÂT\u009aoR{j\u0000\"?ú9²\u0099Jæ\u0002õÚ\u009c\u0092\u009dª°aB9\u0017ñ9\u0094¯SB\u001b\u007fÃj\u008b\u000fs2\u0016éÑ\u001e\u0099(A7\tOñk¹Ca\u0089)\u0090\u0011¼ÙÁ\u0081ÌI¬0MøT ;h7PY\u0018tÀH\u0088\u008dp³8§àË¨Ó\u0090ô[\u0001\u0003BËl\u009d\u009cZk\u0012]ÊB\u0082:z\u001e26êü¢å\u009aÉR´\n¹ÂÙ»8s!+PãFÛ3\u0093\u0018K\u0003\u0003þû\u0095³Îk¨#½\u001bÅÐw\u0088\u007f@V88ða¨\u0011`\u0006Xæ\u0010ÚÈÈ\u0080½xÕ0\u0092éi¡h\u0099QQ4mÏª9â):\u0003rz\u008aVÂK\u001a¦R\u0087j\u0086¢ìúç2×K>\u00837Û\"\u0013\u0014+ecH»Kó¥\u000b\u0093C\u0081\u009büÓãëÎ ~xo°VÈi\u0000zXS\u0090R¨¿à\u008d8Âp¬\u0088\u009a,¹ëW£~{{3\u0010Ë&\u0083([Ò\u0013Ç+ìã\u009f»\u008as\u009a\nZÂL\u009amRej\u0000\"%ú$²ÖJû\u0002ÔÚ\u008d\u0092\u008dª¤aE9HñJ\u0089\u0001A\u0000\u0019)Ñ<éÖ¡±y 1ÙÉ¥\u0081³XR\u0010_(là\u0002¸\u0004p6\bÛÀÈ\u0098óPîhÅ ¢ø¸·ZOv\u0007dß\u000e\u0097\u001a¯#gÄ?Á÷µ\u008fÕG\u008c\u001f²×¿îL¦\u007f~j6YÎ!\u0086.^\u009d\u0016Å.êæ\u0096¾\u008av¼\rQÅ\u0001\u009drU|m\u0011%qý>µÍMô\u0005õÝ\u0098,¥ëJ£v{b3\fË!\u0083a[\u0090\u0013\u0089+æã\u0084»\u009fs«\nPÂO\u009aiR)j\u0016\"%ú,²ÍJà\u0002òÚÝ\u0092Óªå,¥ëJ£v{b3\fË!\u0083a[\u0090\u0013\u0089+éã\u009e»\u008as¾\n\\ÂO\u009azR)j\n\"$ú9²\u0099Jó\u0002óÚ\u0092\u0092\u0084ªåa_9Bñm\u0089UA\u0011\u00198Ñ;éÈ¡øyù1\u008dÉ°\u0081¥X\u001d\u0010Z(qà\u0010¸\u0019p<\b\u0095À\u009b\u0098½Pèh\u0087 ¾ø¿·MO|\u0007oß\u001a\u0097I¯)gÞ?Ê÷ö\u008f\u0080G\u0095,\u00adë@£}{h3\rË0\u0083\u0000[Ó\u0013Í+×ã\u0094»\u009es¼\nAÂd\u009akRlj\u0017\"(ú9²ÑJü\u0002ïÚ\u009a,\u009dëW£p{c3\nË4\u0083\"[É\u0013À+êã\u009f»©s\u009b\n\u001bÂE\u009a\u007fÛ\u0097\u001c~T_\u008cVÄ\u0007<\u0006t\t¬§äãÜÞ\u0014¿L¿\u0084Ãý55;,¦ëG£s,¦ëG£s{-3\tË4\u00835[Õ\u0013\u0089+¿ãÑ{ï¼\u000eô7,nd\u0007\u009c:Ô4\fÏD\u008f|¼´×\u0096\u0090Qq\u0019EÁ;\u0089)q*9;áÎ©\u009f\u0091÷Y\u0082\u0001\u0097Éª°WxR oè?ÐI\u0098g".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 2159);
        f = cArr;
        j = -9008976166544413915L;
    }

    static void init$0() {
        $$a = new byte[]{118, -84, -110, 65};
        $$b = 17;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r8 = 105 - r8
            byte[] r0 = o.b.c.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r6
            goto L35
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r4 = r0[r8]
            r5 = r9
            r9 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L35:
            int r7 = -r7
            int r6 = r6 + r7
            int r8 = r8 + 1
            r7 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.c.m(int, short, byte, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        f38o = 1;
        b();
        Process.myPid();
        Process.getThreadPriority(0);
        ViewConfiguration.getZoomControlsTimeout();
        c = new Object();
        i = false;
        b = false;
        g = new ArrayList();
        d = new ArrayList();
        int i2 = l + 97;
        f38o = i2 % 128;
        switch (i2 % 2 == 0 ? ':' : 'B') {
            case Opcodes.ASTORE /* 58 */:
                throw null;
            default:
                return;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\c$a.smali */
    static final class a {
        final c b;
        final g c;

        a(c cVar, g gVar) {
            this.b = cVar;
            this.c = gVar;
        }
    }

    public c(Context context) throws IllegalArgumentException {
        synchronized (c) {
            try {
                if (context == null) {
                    Object[] objArr = new Object[1];
                    k((char) (ViewConfiguration.getFadingEdgeLength() >> 16), Color.blue(0), KeyEvent.keyCodeFromString("") + 22, objArr);
                    throw new IllegalArgumentException(((String) objArr[0]).intern());
                }
                o.fl.d.a(context);
                o.ee.g.e();
                this.a = o.ei.c.c();
            } catch (Throwable th) {
                throw th;
            }
        }
    }

    public final void c(Context context, o.b.a aVar, b bVar, o.h.d dVar, o.c.a aVar2) {
        synchronized (c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (25234 - Color.red(0)), ExpandableListView.getPackedPositionGroup(0L) + 22, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 20, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) (Drawable.resolveOpacity(0, 0) + 61738), 43 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 6 - TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            o.ee.b bVar2 = new o.ee.b(Looper.myLooper());
            g gVar = new g(context, aVar, a(context, aVar, bVar2), bVar, dVar, aVar2, bVar2);
            if (!b) {
                o.bb.d d2 = d(gVar);
                if (d2 != null) {
                    if (d2.b()) {
                        aVar.c(d(context), d2, null);
                    } else {
                        aVar.d(d2);
                    }
                }
            } else {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((char) (TextUtils.indexOf("", "") + 25234), TextUtils.lastIndexOf("", '0') + 23, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 20, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                k((char) ExpandableListView.getPackedPositionType(0L), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 49, View.getDefaultSize(0, 0) + 29, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                if (gVar.a()) {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    k((char) (25234 - (ViewConfiguration.getTouchSlop() >> 8)), MotionEvent.axisFromString("") + 23, 20 - Color.argb(0, 0, 0, 0), objArr5);
                    String intern3 = ((String) objArr5[0]).intern();
                    Object[] objArr6 = new Object[1];
                    k((char) (13726 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), ((Process.getThreadPriority(0) + 20) >> 6) + 78, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 54, objArr6);
                    o.ee.g.d(intern3, ((String) objArr6[0]).intern());
                    d.add(new a(this, gVar));
                } else {
                    o.ee.g.c();
                    Object[] objArr7 = new Object[1];
                    k((char) ((Process.myTid() >> 22) + 25234), 23 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 20 - (Process.myTid() >> 22), objArr7);
                    String intern4 = ((String) objArr7[0]).intern();
                    Object[] objArr8 = new Object[1];
                    k((char) Color.blue(0), 132 - View.combineMeasuredStates(0, 0), 55 - ExpandableListView.getPackedPositionChild(0L), objArr8);
                    o.ee.g.d(intern4, ((String) objArr8[0]).intern());
                    g.add(new a(this, gVar));
                }
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:56:0x0427, code lost:
    
        r21 = false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.bb.d d(o.b.g r23) {
        /*
            Method dump skipped, instructions count: 1214
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.c.d(o.b.g):o.bb.d");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x0089. Please report as an issue. */
    static boolean a() {
        int i2 = l + Opcodes.LREM;
        f38o = i2 % 128;
        int i3 = i2 % 2;
        List<a> list = g;
        switch (!list.isEmpty() ? '8' : (char) 21) {
            case '8':
                a remove = list.remove(0);
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((char) (25234 - TextUtils.getOffsetBefore("", 0)), (-16777194) - Color.rgb(0, 0, 0), 19 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((char) (Process.getGidForName("") + 1), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 899, 70 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                remove.b.d(remove.c);
                int i4 = f38o + 75;
                l = i4 % 128;
                switch (i4 % 2 == 0) {
                }
                return true;
            default:
                return false;
        }
    }

    private boolean e(o.az.d dVar) {
        boolean z;
        int i2 = l + 41;
        f38o = i2 % 128;
        int i3 = i2 % 2;
        List<a> list = d;
        switch (!list.isEmpty() ? (char) 25 : '=') {
            case 25:
                int i4 = f38o + 3;
                l = i4 % 128;
                int i5 = i4 % 2;
                a remove = list.remove(0);
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((char) ((ViewConfiguration.getTouchSlop() >> 8) + 25234), 22 - TextUtils.indexOf("", ""), 20 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 970, 84 - Color.green(0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                remove.b.e = dVar;
                dVar.e(remove.c.i(), remove.c.b());
                this.e = null;
                z = true;
                break;
            default:
                z = false;
                break;
        }
        int i6 = l + 31;
        f38o = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                return z;
            default:
                int i7 = 42 / 0;
                return z;
        }
    }

    private void b(Context context, d.c cVar, o.h.d dVar, o.c.a aVar, boolean z, f fVar, boolean z2) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (25282 - AndroidCharacter.getMirror('0')), 22 - (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (32064 - ((Process.getThreadPriority(0) + 20) >> 6)), 1055 - TextUtils.indexOf("", "", 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 12, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.az.d dVar2 = new o.az.d(context, this.a, cVar, fVar);
        this.e = dVar2;
        b = true;
        dVar2.d(dVar, aVar, z, z2);
        int i2 = l + 41;
        f38o = i2 % 128;
        int i3 = i2 % 2;
    }

    /* renamed from: o.b.c$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\c$5.smali */
    final class AnonymousClass5 implements d.c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int c;
        private static short[] f;
        private static int g;
        private static byte[] h;
        private static int i;
        private static int j;
        private static int m;
        private /* synthetic */ Context a;
        private /* synthetic */ Handler b;
        private /* synthetic */ o.b.a e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            m = 1;
            h = new byte[]{14, -64, -47, -39, -90, -64, -57, -116, -62, -84, -45, -34, -35, -70, -44, -42, -38, -92, -39, -32, 66, 21, 41, -63, 14, 11, 33, 11, 2, 97, -53, 14, 2, 56, 3, 51, 59, 2, 97, 3, -9, 14, 2, 56, 3, 51, 59, 34, -18, 54, 58, 7, 4, 53, 56, 48, 14, 2, 94, -28, 14, 53, 4, 34, 26, 60, 4, 32, 81, -30, 14, 126, 2, 60, -60, 55, 49, 14, 5, 15, 58, 45, -28, 14, 53, 4, 34, 26, 60, 4, 32, 81, 15, 113, -123, Base64.padSymbol, 106, 103, -99, 103, 126, -35, 50, -112, -106, -112, 102, 104, -106, 118, -109, -112, -44, 45, -58, 26, -114, -29, -3, -32, -19, 37, -13, -19, -69, -13, -3, -23, -29, -24, 27, -40, -1, -26, -11, 19, -53, -19, -11, 17, 2, -45, -1, 47, -13, -19, -75, -24, -30, -1, -10, -32, -21, 30, -43, -1, -26, -11, 19, -53, -19, -11, 17, 2, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 2, 20, 50, 28, 31, 6, 1, 47, -8, 2, 9, 24, 54, -18, 48, 24, 52, 37, -10, 2, 114, 22, 48, -40, 11, 5, 2, 25, 3, 14, 33, -8, 2, 9, 24, 54, -18, 48, 24, 52, 37, 71, 37, 55, 87, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 55, 85, 39, 106, -8, 37, 44, 59, 89, 49, 83, 59, 87, 72, 71, -14, 39, 39, 47, 41, 108, -14, 85, 63, 38, 81, 80, 125, 57, 83, -30, 37, 55, 85, 63, 34, 41, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 114, 27, 37, 44, 59, 89, 49, 83, 59, 87, 72, 25, 37, -107, 57, 83, -5, 46, 40, 37, 60, 38, 81, 68, 27, 37, 44, 59, 89, 49, 83, 59, 87, 72, 40, 12, 56, 13, 91, -20, 82, 20, 50, 57, 8, 38, 30, 32, 8, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 85, -17, 10, 56, 48, 49, 82, 20, 50, 98, 6, 32, -56, 59, 53, 50, 9, 51, 62, 81, -24, 50, 57, 8, 38, 30, 32, 8, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 85, 41, -92, -48, -91, -13, -124, -54, -116, -86, -47, -96, -34, -74, -40, -96, -36, -51, -121, -94, -48, -88, -47, -85, -62, -116, -86, 26, -66, -40, 96, -45, -83, -86, -95, -85, -42, -55, ByteCompanionObject.MIN_VALUE, -86, -47, -96, -34, -74, -40, -96, -36, -51, Base64.padSymbol, -88, -54, -44, -85, -14, -66, -64, -47, -45, -14, -73, -56, -43, -48, -35, -40, -86, 9, -86, -60, 111, -84, -59, -86, -61, -40, -54, -69, -64, -47, -45, -14, -67, -42};
            g = 909053670;
            j = 792590673;
            c = -551848970;
        }

        static void init$0() {
            $$a = new byte[]{45, -21, -97, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
            $$b = ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r6, int r7, int r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.b.c.AnonymousClass5.$$a
                int r8 = r8 * 2
                int r8 = 110 - r8
                int r7 = r7 * 2
                int r7 = r7 + 4
                int r6 = r6 * 3
                int r6 = r6 + 1
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L1d
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                r7 = r6
                goto L38
            L1d:
                r3 = r2
            L1e:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r6) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r5
            L38:
                int r6 = r6 + r9
                int r8 = r8 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r5
                goto L1e
            */
            throw new UnsupportedOperationException("Method not decompiled: o.b.c.AnonymousClass5.l(short, int, int, java.lang.Object[]):void");
        }

        AnonymousClass5(o.b.a aVar, Handler handler, Context context) {
            this.e = aVar;
            this.b = handler;
            this.a = context;
        }

        @Override // o.az.d.c
        public final void a(final o.cb.a aVar, final o.g.b bVar, final o.bb.d dVar) {
            ArrayList<a> arrayList;
            synchronized (c.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 382435993, (short) ((-67) - (ViewConfiguration.getEdgeSlop() >> 16)), (ViewConfiguration.getScrollDefaultDelay() >> 16) - 119, TextUtils.indexOf("", "") - 420673408, objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                k((byte) (ViewConfiguration.getPressedStateDuration() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) + 382436014, (short) (((Process.getThreadPriority(0) + 20) >> 6) + 97), TextUtils.lastIndexOf("", '0') - 118, (ViewConfiguration.getFadingEdgeLength() >> 16) - 420673408, objArr2);
                StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(aVar);
                Object[] objArr3 = new Object[1];
                k((byte) View.MeasureSpec.makeMeasureSpec(0, 0), 382436086 + (ViewConfiguration.getLongPressTimeout() >> 16), (short) (6 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), View.MeasureSpec.getSize(0) - 119, ExpandableListView.getPackedPositionType(0L) - 420673441, objArr3);
                o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).append(bVar).toString());
                c.b = false;
                c.this.e = null;
                arrayList = new ArrayList(c.d);
                c.d.clear();
                c.a();
            }
            for (a aVar2 : arrayList) {
                final o.b.a h2 = aVar2.c.h();
                if (h2 != null) {
                    aVar2.c.f().post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda0
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass5.d(a.this, aVar, bVar, dVar);
                        }
                    });
                }
            }
            final o.b.a aVar3 = this.e;
            if (aVar3 != null) {
                this.b.post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda2
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.b(a.this, aVar, bVar, dVar);
                    }
                });
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void d(o.b.a aVar, o.cb.a aVar2, o.g.b bVar, o.bb.d dVar) {
            int i2 = m + Opcodes.LNEG;
            i = i2 % 128;
            int i3 = i2 % 2;
            aVar.d(aVar2, bVar, dVar);
            int i4 = m + 13;
            i = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void b(o.b.a aVar, o.cb.a aVar2, o.g.b bVar, o.bb.d dVar) {
            int i2 = m + 89;
            i = i2 % 128;
            int i3 = i2 % 2;
            aVar.d(aVar2, bVar, dVar);
            int i4 = i + 53;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // o.az.d.c
        public final void b(final o.bb.d dVar) {
            ArrayList<a> arrayList;
            synchronized (c.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), Color.blue(0) + 382435994, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 68), (-118) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 420673409, objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                k((byte) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), 382436107 - View.MeasureSpec.getSize(0), (short) ((-112) - KeyEvent.normalizeMetaState(0)), (-119) - ExpandableListView.getPackedPositionType(0L), (-420673408) - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr2);
                o.ee.g.e(intern, sb.append(((String) objArr2[0]).intern()).append(dVar.d()).toString());
                c.b = false;
                c.this.e = null;
                arrayList = new ArrayList(c.d);
                c.d.clear();
                c.a();
            }
            for (a aVar : arrayList) {
                final o.b.a h2 = aVar.c.h();
                if (h2 != null) {
                    aVar.c.f().post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda3
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass5.d(a.this, dVar);
                        }
                    });
                }
            }
            final o.b.a aVar2 = this.e;
            if (aVar2 != null) {
                this.b.post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda4
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.e(a.this, dVar);
                    }
                });
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void d(o.b.a aVar, o.bb.d dVar) {
            int i2 = m + 15;
            i = i2 % 128;
            boolean z = i2 % 2 != 0;
            aVar.d(dVar);
            switch (z) {
                case false:
                    int i3 = m + 47;
                    i = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case true:
                            return;
                        default:
                            int i4 = 86 / 0;
                            return;
                    }
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void e(o.b.a aVar, o.bb.d dVar) {
            int i2 = m + 53;
            i = i2 % 128;
            int i3 = i2 % 2;
            aVar.d(dVar);
            int i4 = i + 77;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // o.az.d.c
        public final void d(final o.bb.d dVar, boolean z, final o.bv.g gVar) {
            ArrayList<a> arrayList;
            synchronized (c.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) TextUtils.indexOf("", ""), 382435993 - ImageFormat.getBitsPerPixel(0), (short) ((ViewConfiguration.getTouchSlop() >> 8) - 67), (-119) - (Process.myPid() >> 22), (-420673408) - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((byte) KeyEvent.normalizeMetaState(0), 382436158 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) (ExpandableListView.getPackedPositionGroup(0L) + 109), ((byte) KeyEvent.getModifierMetaStateMask()) - 118, (-420673407) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                c.b = false;
                c.this.e = null;
                c.b(this.a, e.c);
                c.d(this.a, (Boolean) null);
                c.this.d(this.a).c(true);
                arrayList = new ArrayList(c.d);
                c.d.clear();
                c.a();
            }
            for (a aVar : arrayList) {
                final o.b.a h2 = aVar.c.h();
                if (h2 != null) {
                    aVar.c.f().post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda9
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass5.this.c(h2, dVar, gVar);
                        }
                    });
                }
            }
            final o.b.a aVar2 = this.e;
            if (aVar2 != null) {
                this.b.post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda10
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.this.d(aVar2, dVar, gVar);
                    }
                });
            }
            o.ei.c.c();
            o.ei.c.c(this.a, true);
            if (z) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((byte) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getTapTimeout() >> 16) + 382435994, (short) ((-67) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (-119) - Drawable.resolveOpacity(0, 0), (-437450624) - Color.rgb(0, 0, 0), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                k((byte) (ViewConfiguration.getEdgeSlop() >> 16), (ViewConfiguration.getJumpTapTimeout() >> 16) + 382436200, (short) (74 - (ViewConfiguration.getFadingEdgeLength() >> 16)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 118, (-420673407) - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                if (o.i.d.c().h()) {
                    o.j.c.b();
                }
                new o.cd.e();
                o.cd.e.a(this.a).a(this.a);
            }
            if (dVar.a().j()) {
                final Context context = this.a;
                new Thread(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda1
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.d(context);
                    }
                }).start();
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void c(o.b.a aVar, o.bb.d dVar, o.bv.g gVar) {
            int i2 = i + 77;
            m = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    aVar.c(c.this.a, dVar, gVar);
                    int i3 = 80 / 0;
                    break;
                default:
                    aVar.c(c.this.a, dVar, gVar);
                    break;
            }
            int i4 = m + 85;
            i = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void d(o.b.a aVar, o.bb.d dVar, o.bv.g gVar) {
            int i2 = i + 23;
            m = i2 % 128;
            switch (i2 % 2 == 0 ? 'I' : '[') {
                case Opcodes.DUP_X2 /* 91 */:
                    aVar.c(c.this.a, dVar, gVar);
                    return;
                default:
                    aVar.c(c.this.a, dVar, gVar);
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void d(Context context) {
            try {
                new o.bo.d();
                o.bo.d.a(context);
                int i2 = i + 67;
                m = i2 % 128;
                int i3 = i2 % 2;
            } catch (o.bo.g e) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) (ViewConfiguration.getWindowTouchSlop() >> 8), 382435994 - View.resolveSizeAndState(0, 0, 0), (short) ((-67) - (Process.myTid() >> 22)), TextUtils.indexOf((CharSequence) "", '0', 0) - 118, (-420673409) + (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((byte) (Process.myPid() >> 22), 382436369 - ImageFormat.getBitsPerPixel(0), (short) ((-71) - Color.argb(0, 0, 0, 0)), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 119, Color.green(0) - 420673362, objArr2);
                o.ee.g.a(intern, ((String) objArr2[0]).intern(), e);
            }
        }

        @Override // o.az.d.c
        public final void e() {
            ArrayList<a> arrayList;
            synchronized (c.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) Color.green(0), 365658778 - Color.rgb(0, 0, 0), (short) ((-67) - (ViewConfiguration.getWindowTouchSlop() >> 8)), (-119) - View.getDefaultSize(0, 0), View.resolveSizeAndState(0, 0, 0) - 420673408, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 382436277, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 92), (-119) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), View.MeasureSpec.getSize(0) - 420673408, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                arrayList = new ArrayList(c.d);
            }
            for (a aVar : arrayList) {
                final o.b.a h2 = aVar.c.h();
                if (h2 != null) {
                    aVar.c.f().post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda7
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass5.e(a.this);
                        }
                    });
                }
            }
            final o.b.a aVar2 = this.e;
            if (aVar2 != null) {
                this.b.post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda8
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.b(a.this);
                    }
                });
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void e(o.b.a aVar) {
            int i2 = i + 17;
            m = i2 % 128;
            char c2 = i2 % 2 == 0 ? '!' : (char) 28;
            aVar.e();
            switch (c2) {
                case '!':
                    throw null;
                default:
                    int i3 = m + Opcodes.LSHL;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void b(o.b.a aVar) {
            int i2 = m + 39;
            i = i2 % 128;
            int i3 = i2 % 2;
            aVar.e();
            int i4 = i + 91;
            m = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    return;
                default:
                    throw null;
            }
        }

        @Override // o.az.d.c
        public final void a() {
            ArrayList<a> arrayList;
            synchronized (c.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((byte) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 382435994 + ExpandableListView.getPackedPositionGroup(0L), (short) ((-67) - KeyEvent.getDeadChar(0, 0)), (-119) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (-420673408) - View.getDefaultSize(0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((byte) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), Color.alpha(0) + 382436323, (short) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 59), (-119) - View.MeasureSpec.getMode(0), (-420673408) - Color.red(0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                arrayList = new ArrayList(c.d);
            }
            for (a aVar : arrayList) {
                final o.b.a h2 = aVar.c.h();
                if (h2 != null) {
                    aVar.c.f().post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda5
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass5.c(a.this);
                        }
                    });
                }
            }
            final o.b.a aVar2 = this.e;
            if (aVar2 != null) {
                this.b.post(new Runnable() { // from class: o.b.c$5$$ExternalSyntheticLambda6
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.AnonymousClass5.a(a.this);
                    }
                });
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void c(o.b.a aVar) {
            int i2 = i + 29;
            m = i2 % 128;
            int i3 = i2 % 2;
            aVar.a();
            int i4 = i + 1;
            m = i4 % 128;
            switch (i4 % 2 == 0 ? ']' : '!') {
                case '!':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void a(o.b.a aVar) {
            int i2 = m + 61;
            i = i2 % 128;
            int i3 = i2 % 2;
            aVar.a();
            int i4 = i + 73;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX WARN: Code restructure failed: missing block: B:121:0x03ed, code lost:
        
            r3 = r7;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r20, int r21, short r22, int r23, int r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 1202
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.b.c.AnonymousClass5.k(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    private d.c a(Context context, o.b.a aVar, Handler handler) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (25234 - ExpandableListView.getPackedPositionGroup(0L)), 22 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getScrollBarSize() >> 8) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), ImageFormat.getBitsPerPixel(0) + 1068, TextUtils.getOffsetBefore("", 0) + 23, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        AnonymousClass5 anonymousClass5 = new AnonymousClass5(aVar, handler, context);
        int i2 = l + 95;
        f38o = i2 % 128;
        switch (i2 % 2 == 0 ? '8' : '@') {
            case '8':
                int i3 = 83 / 0;
                return anonymousClass5;
            default:
                return anonymousClass5;
        }
    }

    final o.ei.c d(Context context) {
        int i2 = f38o + 57;
        l = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (25235 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 22 - (ViewConfiguration.getTouchSlop() >> 8), KeyEvent.getDeadChar(0, 0) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getJumpTapTimeout() >> 16), View.MeasureSpec.getMode(0) + 1090, 49 - View.combineMeasuredStates(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.i.d.c().e(context, this.a);
        o.ei.c cVar = this.a;
        int i4 = l + 49;
        f38o = i4 % 128;
        switch (i4 % 2 == 0 ? ';' : (char) 20) {
            case 20:
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void a(Context context) {
        Object[] objArr = new Object[1];
        k((char) (((Process.getThreadPriority(0) + 20) >> 6) + 25234), Drawable.resolveOpacity(0, 0) + 22, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        synchronized (c) {
            o.ee.g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 3733), 1139 - View.MeasureSpec.makeMeasureSpec(0, 0), 13 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(context).toString());
            Iterator<a> it = d.iterator();
            while (it.hasNext()) {
                if (it.next().b == this) {
                    it.remove();
                }
            }
            Iterator<a> it2 = g.iterator();
            while (it2.hasNext()) {
                if (it2.next().b == this) {
                    it2.remove();
                }
            }
            if (b) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((char) View.combineMeasuredStates(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1153, 33 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr3);
                o.ee.g.d(intern, ((String) objArr3[0]).intern());
                if (this.e != null) {
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    k((char) (((Process.getThreadPriority(0) + 20) >> 6) + 54693), 1183 - TextUtils.lastIndexOf("", '0', 0), 78 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr4);
                    o.ee.g.d(intern, ((String) objArr4[0]).intern());
                    if (!e(this.e)) {
                        o.ee.g.c();
                        Object[] objArr5 = new Object[1];
                        k((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 20668), View.MeasureSpec.makeMeasureSpec(0, 0) + 1261, 66 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr5);
                        o.ee.g.d(intern, ((String) objArr5[0]).intern());
                        o.az.d dVar = this.e;
                        if (dVar != null) {
                            dVar.e();
                        }
                        b = false;
                        a();
                    }
                } else {
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    k((char) (27572 - ImageFormat.getBitsPerPixel(0)), 1325 - ImageFormat.getBitsPerPixel(0), 69 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr6);
                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                }
            }
        }
    }

    public final void d(Context context, b.c cVar, WalletLockReason walletLockReason) throws WalletValidationException {
        int i2 = f38o + 89;
        l = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 25234), 22 - KeyEvent.normalizeMetaState(0), View.MeasureSpec.getSize(0) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 1395 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 11 - View.resolveSize(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!this.a.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            k((char) (62476 - (Process.myPid() >> 22)), 1406 - View.resolveSizeAndState(0, 0, 0), 5 - TextUtils.indexOf((CharSequence) "", '0'), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 1412 - (KeyEvent.getMaxKeyCode() >> 16), ExpandableListView.getPackedPositionType(0L) + 42, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.bg.b(context, cVar, this.a).d(walletLockReason);
        int i4 = l + 33;
        f38o = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void e(Context context, b.e eVar) throws WalletValidationException {
        int i2 = l + 87;
        f38o = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 25234), 22 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.lastIndexOf("", '0', 0) + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (37141 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1453, (ViewConfiguration.getEdgeSlop() >> 16) + 13, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!this.a.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            k((char) (KeyEvent.getDeadChar(0, 0) + 62476), Color.rgb(0, 0, 0) + 16778622, '6' - AndroidCharacter.getMirror('0'), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((char) ('0' - AndroidCharacter.getMirror('0')), 1413 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 42 - KeyEvent.keyCodeFromString(""), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.bf.b(context, eVar, this.a).m();
        int i4 = l + 83;
        f38o = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public static void d() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 25234), (ViewConfiguration.getTapTimeout() >> 16) + 22, 19 - TextUtils.lastIndexOf("", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 1467 - (ViewConfiguration.getTouchSlop() >> 8), TextUtils.getOffsetBefore("", 0) + 34, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.dl.c();
        o.dl.c.b().a();
        o.p002do.b.e().d();
        int i2 = l + Opcodes.DREM;
        f38o = i2 % 128;
        switch (i2 % 2 != 0 ? ';' : (char) 2) {
            case ';':
                return;
            default:
                int i3 = 26 / 0;
                return;
        }
    }

    public static e e(Context context) {
        int i2 = f38o + 73;
        l = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k((char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 1501, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 46, objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 1548, 2 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
        e e = e.e(sharedPreferences.getInt(((String) objArr2[0]).intern(), e.b.d()));
        switch (e == null ? 'b' : (char) 16) {
            case 16:
                break;
            default:
                int i4 = f38o + Opcodes.DMUL;
                l = i4 % 128;
                if (i4 % 2 != 0) {
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((char) (View.resolveSize(0, 0) + 25234), 22 - (ViewConfiguration.getTapTimeout() >> 16), Drawable.resolveOpacity(0, 0) + 20, objArr3);
                String intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                k((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 3976), 1550 - View.resolveSize(0, 0), 78 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr4);
                o.ee.g.d(intern, ((String) objArr4[0]).intern());
                e = e.b;
                break;
        }
        o.ee.g.c();
        Object[] objArr5 = new Object[1];
        k((char) (25234 - (ViewConfiguration.getTapTimeout() >> 16)), 22 - View.combineMeasuredStates(0, 0), Color.argb(0, 0, 0, 0) + 20, objArr5);
        String intern2 = ((String) objArr5[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr6 = new Object[1];
        k((char) View.resolveSizeAndState(0, 0, 0), 1628 - View.MeasureSpec.getSize(0), 20 - (ViewConfiguration.getEdgeSlop() >> 16), objArr6);
        o.ee.g.d(intern2, sb.append(((String) objArr6[0]).intern()).append(e).toString());
        return e;
    }

    static void b(Context context, e eVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 25235), View.resolveSizeAndState(0, 0, 0) + 22, ImageFormat.getBitsPerPixel(0) + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 2888), 1648 - KeyEvent.normalizeMetaState(0), 20 - (Process.myTid() >> 22), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar).toString());
        Object[] objArr3 = new Object[1];
        k((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 1501 - Color.alpha(0), 47 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        k((char) TextUtils.getOffsetAfter("", 0), 1548 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 2 - Color.blue(0), objArr4);
        edit.putInt(((String) objArr4[0]).intern(), eVar.d()).commit();
        int i2 = l + 67;
        f38o = i2 % 128;
        int i3 = i2 % 2;
    }

    private static boolean h(Context context) {
        int i2 = f38o + 43;
        l = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (TextUtils.getCapsMode("", 0, 0) + 25234), 22 - View.combineMeasuredStates(0, 0), View.getDefaultSize(0, 0) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (1201 - (ViewConfiguration.getTouchSlop() >> 8)), TextUtils.getTrimmedLength("") + 1668, 20 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k((char) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 1501, 46 - TextUtils.lastIndexOf("", '0', 0), objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        Object[] objArr4 = new Object[1];
        k((char) (1198 - TextUtils.indexOf("", "", 0)), 1686 - ExpandableListView.getPackedPositionChild(0L), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 1, objArr4);
        boolean z = sharedPreferences.getBoolean(((String) objArr4[0]).intern(), false);
        int i4 = l + 21;
        f38o = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return z;
            default:
                int i5 = 29 / 0;
                return z;
        }
    }

    static void d(Context context, Boolean bool) {
        Object obj;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 25234), Color.alpha(0) + 22, Process.getGidForName("") + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k((char) TextUtils.getTrimmedLength(""), 1689 - Color.green(0), 39 - (ViewConfiguration.getEdgeSlop() >> 16), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bool).toString());
        Object[] objArr3 = new Object[1];
        k((char) Color.red(0), View.resolveSize(0, 0) + 1501, Drawable.resolveOpacity(0, 0) + 47, objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        switch (bool == null ? (char) 11 : (char) 6) {
            case 6:
                Object[] objArr4 = new Object[1];
                k((char) (1199 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 1687 - (ViewConfiguration.getKeyRepeatDelay() >> 16), TextUtils.getTrimmedLength("") + 2, objArr4);
                edit.putBoolean(((String) objArr4[0]).intern(), bool.booleanValue());
                break;
            default:
                int i2 = f38o + Opcodes.LSHL;
                l = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 27 : 'X') {
                    case Opcodes.POP2 /* 88 */:
                        Object[] objArr5 = new Object[1];
                        k((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 1198), ImageFormat.getBitsPerPixel(0) + 1688, (ViewConfiguration.getPressedStateDuration() >> 16) + 2, objArr5);
                        obj = objArr5[0];
                        break;
                    default:
                        Object[] objArr6 = new Object[1];
                        k((char) (19137 - (ViewConfiguration.getLongPressTimeout() * Opcodes.FNEG)), 20493 / ImageFormat.getBitsPerPixel(0), 4 << (ViewConfiguration.getPressedStateDuration() / 93), objArr6);
                        obj = objArr6[0];
                        break;
                }
                edit.remove(((String) obj).intern());
                break;
        }
        edit.commit();
        int i3 = l + 65;
        f38o = i3 % 128;
        int i4 = i3 % 2;
    }

    public static boolean c(Context context) {
        boolean z;
        synchronized (c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (View.getDefaultSize(0, 0) + 25234), TextUtils.lastIndexOf("", '0', 0) + 23, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 20, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) ExpandableListView.getPackedPositionGroup(0L), 1729 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), TextUtils.indexOf("", "", 0, 0) + 10, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            e e = e(context);
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            k((char) ((ViewConfiguration.getTapTimeout() >> 16) + 25234), ExpandableListView.getPackedPositionType(0L) + 22, 19 - TextUtils.indexOf((CharSequence) "", '0'), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr4 = new Object[1];
            k((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 1737 - TextUtils.lastIndexOf("", '0', 0, 0), 29 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr4);
            o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(e.name()).toString());
            if (e == e.b) {
                return false;
            }
            if (e != e.e) {
                o.ei.c.c().c(false);
                o.ei.c.c();
                o.ei.c.c(context, false);
                o.j.c.c();
                b(context, e.e);
                d(context, (Boolean) null);
                z = true;
            } else {
                z = false;
            }
            o.ei.c.c().c(context);
            new o.dd.e(context).b(8, 8, "", null, null, null, null);
            i = true;
            return z;
        }
    }

    public final void a(Context context, a.c cVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 25234), 22 - (Process.myTid() >> 22), 20 - TextUtils.indexOf("", "", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 47106), 1767 - ((Process.getThreadPriority(0) + 20) >> 6), TextUtils.indexOf("", "") + 6, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.bh.a(context, cVar, this.a).e(o.bh.d.b);
        int i2 = f38o + Opcodes.DMUL;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? 'Y' : '5') {
            case Opcodes.DUP /* 89 */:
                throw null;
            default:
                return;
        }
    }

    public static boolean d(Context context, boolean z) {
        synchronized (c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (25235 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 23 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 21 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 14933), 1773 - View.combineMeasuredStates(0, 0), 28 - ImageFormat.getBitsPerPixel(0), objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z).toString());
            switch (AnonymousClass4.d[e(context).ordinal()]) {
                case 1:
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    k((char) (Color.argb(0, 0, 0, 0) + 25234), Process.getGidForName("") + 23, 21 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    k((char) (Color.red(0) + 45344), 1803 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 42 - TextUtils.lastIndexOf("", '0', 0), objArr4);
                    o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                    i = false;
                    b(context, e.d);
                    d(context, Boolean.valueOf(z));
                    return true;
                default:
                    return false;
            }
        }
    }

    /* renamed from: o.b.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        private static int c;
        static final /* synthetic */ int[] d;
        static final /* synthetic */ int[] e;

        /* JADX WARN: Failed to find 'out' block for switch in B:16:0x0058. Please report as an issue. */
        /* JADX WARN: Failed to find 'out' block for switch in B:7:0x002c. Please report as an issue. */
        static {
            b = 0;
            c = 1;
            int[] iArr = new int[e.values().length];
            d = iArr;
            try {
                iArr[e.e.ordinal()] = 1;
                int i = b;
                int i2 = (i ^ 45) + ((i & 45) << 1);
                c = i2 % 128;
                switch (i2 % 2 == 0 ? 'F' : '+') {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[e.b.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[e.c.ordinal()] = 3;
                int i3 = c + 97;
                b = i3 % 128;
                switch (i3 % 2 != 0 ? 'W' : 'U') {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[e.d.ordinal()] = 4;
                int i4 = c;
                int i5 = ((i4 | 93) << 1) - (i4 ^ 93);
                b = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e5) {
            }
            int[] iArr2 = new int[d.values().length];
            e = iArr2;
            try {
                iArr2[d.d.ordinal()] = 1;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[d.e.ordinal()] = 2;
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[d.b.ordinal()] = 3;
                int i7 = c + 69;
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e8) {
            }
        }
    }

    public static boolean b(Context context) {
        boolean z;
        synchronized (c) {
            e e = e(context);
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (View.combineMeasuredStates(0, 0) + 25234), ExpandableListView.getPackedPositionGroup(0L) + 22, 20 - Color.blue(0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) (16751 - ExpandableListView.getPackedPositionType(0L)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 1844, 38 - View.MeasureSpec.getMode(0), objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(e).toString());
            z = e != e.b;
        }
        return z;
    }

    public static void i(Context context) {
        synchronized (c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (25233 - MotionEvent.axisFromString("")), 22 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), Color.rgb(0, 0, 0) + 16777236, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), View.getDefaultSize(0, 0) + 1883, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 88, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            b(context, e.d);
            d(context, (Boolean) null);
        }
    }

    public static boolean f(Context context) {
        synchronized (c) {
            e e = e(context);
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (25234 - (ViewConfiguration.getEdgeSlop() >> 16)), 22 - Color.blue(0), 'D' - AndroidCharacter.getMirror('0'), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 1971 - TextUtils.getOffsetAfter("", 0), View.MeasureSpec.makeMeasureSpec(0, 0) + 26, objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(e).toString());
            switch (AnonymousClass4.d[e.ordinal()]) {
                case 1:
                case 2:
                case 4:
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    k((char) (25233 - TextUtils.lastIndexOf("", '0', 0, 0)), 22 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.indexOf("", "") + 20, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    k((char) TextUtils.indexOf("", "", 0, 0), 1998 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 63 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr4);
                    o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                    return false;
                case 3:
                    o.ei.c.c().c(false);
                    o.ei.c.c();
                    o.ei.c.c(context, false);
                    o.j.c.c();
                    b(context, e.d);
                    d(context, (Boolean) null);
                    o.ei.c.c().a(context);
                    new o.dd.e(context).b(9, 8, "", null, null, null, null);
                    return true;
                default:
                    throw new IllegalArgumentException();
            }
        }
    }

    public static boolean c() {
        int i2 = f38o + Opcodes.LSHR;
        int i3 = i2 % 128;
        l = i3;
        int i4 = i2 % 2;
        boolean z = i;
        int i5 = i3 + 1;
        f38o = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    public static void j(Context context) {
        synchronized (c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            k((char) (25234 - View.MeasureSpec.getMode(0)), 22 - (ViewConfiguration.getTouchSlop() >> 8), ((Process.getThreadPriority(0) + 20) >> 6) + 20, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), 2059 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 24 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            o.ei.c.c().c(false);
            o.ei.c.c();
            o.ei.c.c(context, false);
            o.j.c.c();
            i = false;
            b(context, e.b);
            d(context, (Boolean) null);
            o.ei.c.c().e(context);
            o.ei.g.c();
            new o.dd.e(context).b(15, 8, "", null, null, null, null);
            Object[] objArr3 = new Object[1];
            k((char) TextUtils.getOffsetAfter("", 0), 1501 - (ViewConfiguration.getJumpTapTimeout() >> 16), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 47, objArr3);
            context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit().clear().commit();
            Object[] objArr4 = new Object[1];
            k((char) Drawable.resolveOpacity(0, 0), (-16775132) - Color.rgb(0, 0, 0), 16 - TextUtils.getOffsetAfter("", 0), objArr4);
            context.deleteDatabase(((String) objArr4[0]).intern());
            String str = context.getApplicationInfo().dataDir;
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            k((char) (25233 - TextUtils.lastIndexOf("", '0')), (KeyEvent.getMaxKeyCode() >> 16) + 22, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 19, objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr6 = new Object[1];
            k((char) ((-16713926) - Color.rgb(0, 0, 0)), TextUtils.indexOf("", "") + 2100, 16 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr6);
            o.ee.g.d(intern2, sb.append(((String) objArr6[0]).intern()).append(str).toString());
            File file = new File(str);
            Object[] objArr7 = new Object[1];
            k((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), ExpandableListView.getPackedPositionChild(0L) + 2116, KeyEvent.keyCodeFromString("") + 3, objArr7);
            File file2 = new File(file, ((String) objArr7[0]).intern());
            o.ee.g.c();
            Object[] objArr8 = new Object[1];
            k((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 25234), TextUtils.lastIndexOf("", '0', 0) + 23, TextUtils.lastIndexOf("", '0') + 21, objArr8);
            String intern3 = ((String) objArr8[0]).intern();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr9 = new Object[1];
            k((char) TextUtils.indexOf("", "", 0), TextUtils.indexOf((CharSequence) "", '0') + 2119, 11 - (Process.myPid() >> 22), objArr9);
            StringBuilder append = sb2.append(((String) objArr9[0]).intern()).append(file2.getAbsolutePath());
            Object[] objArr10 = new Object[1];
            k((char) (Color.red(0) + 22278), 2130 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), Color.green(0) + 11, objArr10);
            o.ee.g.d(intern3, append.append(((String) objArr10[0]).intern()).append(file2.exists()).toString());
            boolean delete = file2.delete();
            o.ee.g.c();
            Object[] objArr11 = new Object[1];
            k((char) (25234 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), View.getDefaultSize(0, 0) + 22, 20 - KeyEvent.getDeadChar(0, 0), objArr11);
            String intern4 = ((String) objArr11[0]).intern();
            StringBuilder sb3 = new StringBuilder();
            Object[] objArr12 = new Object[1];
            k((char) (47638 - (Process.myTid() >> 22)), 2188 - AndroidCharacter.getMirror('0'), 18 - TextUtils.lastIndexOf("", '0', 0, 0), objArr12);
            o.ee.g.d(intern4, sb3.append(((String) objArr12[0]).intern()).append(delete).toString());
            o.bn.e.b().e();
        }
    }

    public static void b(boolean z) {
        int i2 = l + 17;
        int i3 = i2 % 128;
        f38o = i3;
        char c2 = i2 % 2 == 0 ? Typography.amp : 'Z';
        h = z;
        switch (c2) {
            case 'Z':
                int i4 = i3 + 23;
                l = i4 % 128;
                int i5 = i4 % 2;
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x003c  */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0045 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0046  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x003f  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x002c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static void e(o.ei.c r3, android.content.Context r4) {
        /*
            int r0 = o.b.c.f38o
            int r0 = r0 + 101
            int r1 = r0 % 128
            o.b.c.l = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L1e
            boolean r0 = r3.b()
            r1 = 76
            r2 = 0
            int r1 = r1 / r2
            if (r0 != 0) goto L17
            goto L18
        L17:
            r2 = 1
        L18:
            switch(r2) {
                case 0: goto L2c;
                default: goto L1b;
            }
        L1b:
            goto L2f
        L1c:
            r3 = move-exception
            throw r3
        L1e:
            boolean r0 = r3.b()
            if (r0 != 0) goto L27
            r0 = 33
            goto L29
        L27:
            r0 = 83
        L29:
            switch(r0) {
                case 83: goto L2f;
                default: goto L2c;
            }
        L2c:
            r3.b(r4)
        L2f:
            int r3 = o.b.c.l
            int r3 = r3 + 39
            int r4 = r3 % 128
            o.b.c.f38o = r4
            int r3 = r3 % 2
            if (r3 != 0) goto L3f
            r3 = 63
            goto L41
        L3f:
            r3 = 35
        L41:
            switch(r3) {
                case 35: goto L45;
                default: goto L44;
            }
        L44:
            goto L46
        L45:
            return
        L46:
            r3 = 0
            r3.hashCode()     // Catch: java.lang.Throwable -> L4b
            throw r3     // Catch: java.lang.Throwable -> L4b
        L4b:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.c.e(o.ei.c, android.content.Context):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 722
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.c.k(char, int, int, java.lang.Object[]):void");
    }
}
