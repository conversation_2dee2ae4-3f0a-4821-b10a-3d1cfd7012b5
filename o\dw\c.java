package o.dw;

import android.content.Context;
import android.os.CancellationSignal;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dw\c.smali */
public abstract class c extends d<a> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static char[] d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        a = 1;
        d = new char[]{42903, 19102, 32201, 24580, 4962, 1620, 10386, 56293, 52770, 61747, 58446, 38564, 47607, 44251, 24326, 17006, 29841, 26526, 2754, 15648, 8266, 54104, 50563, 59634, 39723, 34358, 27403, 23631, 16804, 13049, 10178, 2394, 64039, 61434, 53412, 50645, 46910, 38970, 36185, 32399, 25594, 21802, 17925, 11080, 7358, 511, 62158, 58458, 51504, 47866, 44972, 37087, 33323, 30574, 22623, 19848, 16111, 8314, 5433, 1631, 60329, 56559, 49624, 45855, 42051, 35252, 31390, 28639, 20792, 17020, 14155, 6297, 3567, 65402, 57390, 54611, 50873, 44027, 40136, 36374, 29551, 25790};
        b = 5250443086678376953L;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Type inference failed for: r7v1, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = 105 - r7
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r0 = o.dw.c.$$a
            int r6 = r6 * 2
            int r6 = 4 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r6 = r6 + r4
            int r7 = r7 + 1
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.c.g(byte, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{96, 104, -93, 9};
        $$b = 192;
    }

    @Override // o.dw.d
    public /* synthetic */ void launch(Context context, a aVar, String str, CancellationSignal cancellationSignal) {
        int i = a + 43;
        e = i % 128;
        boolean z = i % 2 != 0;
        c();
        switch (z) {
            case true:
                int i2 = 67 / 0;
                return;
            default:
                return;
        }
    }

    private static void c() {
        int i = a + Opcodes.DREM;
        e = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) (35598 - TextUtils.getTrimmedLength("")), ViewConfiguration.getWindowTouchSlop() >> 8, 25 - (ViewConfiguration.getTouchSlop() >> 8), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (TextUtils.getOffsetBefore("", 0) + 43667), 25 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 57 - TextUtils.indexOf("", ""), objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = e + 73;
        a = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 990
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.c.f(char, int, int, java.lang.Object[]):void");
    }
}
