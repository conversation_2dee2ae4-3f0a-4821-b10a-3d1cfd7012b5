package o.cf;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import o.a.l;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int c;
    static final Map<String, String> d;
    static final Map<String, String> e;
    private static int j;

    static void c() {
        c = 874635269;
        a = new char[]{50816, 50789, 50794, 50769, 50799, 50797, 50774, 50768, 50792, 50794, 50794, 50799, 50775, 50769, 50875, 50727, 50723, 50720, 50745, 51144, 51146, 51143, 51165, 51139, 51161, 50918, 50847, 50819, 50858, 50869, 50879, 50873, 50816, 50775, 50774, 50814, 50697, 50810, 50786, 50814, 50927, 50825, 50821, 50931, 50914, 50914, 50821, 50836, 50943, 50852, 50851, 50853, 50856, 50855, 50846, 50785, 50793, 50756, 50768, 50804, 50807, 50815, 50754, 50907, 50818, 50854, 50848, 50873, 50878, 50835, 50934, 50831, 50835, 50843, 50872, 50749, 50743, 50721, 50699, 50715, 50746, 50722, 50751, 50746, 50745, 50706, 50925, 50847, 50862, 50857, 50855, 50879, 50845, 50932, 50836, 50853, 50852, 50852, 50859, 50854, 50855, 50916, 50817, 50825, 50821, 51143, 51142, 51136, 50861, 50696, 50694, 50692, 50697, 50699, 50930, 50866, 50766, 50856, 50692, 50695, 50846, 50794, 50813, 50924, 50831, 50820, 50822, 50825, 50827, 50821, 50821, 50819, 50823, 50827, 50840};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.cf.e.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r8 = 122 - r8
            int r7 = r7 * 4
            int r7 = r7 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = r6 + r9
            int r8 = r8 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.e.h(short, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{114, -113, -41, 111};
        $$b = 4;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        j = 1;
        c();
        Color.rgb(0, 0, 0);
        Process.getElapsedCpuTime();
        KeyEvent.getModifierMetaStateMask();
        ViewConfiguration.getScrollFriction();
        ViewConfiguration.getMaximumDrawingCacheSize();
        ViewConfiguration.getScrollDefaultDelay();
        ViewConfiguration.getJumpTapTimeout();
        ViewConfiguration.getGlobalActionKeyTimeout();
        Color.rgb(0, 0, 0);
        SystemClock.elapsedRealtimeNanos();
        TextUtils.getTrimmedLength("");
        ViewConfiguration.getKeyRepeatTimeout();
        KeyEvent.getModifierMetaStateMask();
        ViewConfiguration.getScrollDefaultDelay();
        Color.green(0);
        ViewConfiguration.getWindowTouchSlop();
        View.resolveSize(0, 0);
        SystemClock.elapsedRealtime();
        ViewConfiguration.getTapTimeout();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        ImageFormat.getBitsPerPixel(0);
        ViewConfiguration.getTouchSlop();
        ViewConfiguration.getGlobalActionKeyTimeout();
        HashMap hashMap = new HashMap();
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000", new int[]{40, 8, 0, 3}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("\u0001\u0000\u0001\u0001", new int[]{Opcodes.LSUB, 4, 0, 4}, true, objArr2);
        hashMap.put(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g((Process.myTid() >> 22) + 7, "￭\ufff0\u001e\u0012\u0004\u0003\f\b\u0000\u0005￬\uffef", View.resolveSizeAndState(0, 0, 0) + 12, 187 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), true, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g(View.resolveSize(0, 0) + 1, "\u0005�￪�\u0017\u000b�￼", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 8, View.MeasureSpec.getSize(0) + Opcodes.MONITORENTER, true, objArr4);
        hashMap.put(intern2, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        f("\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001", new int[]{32, 8, Opcodes.DMUL, 0}, true, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        f("\u0001\u0000\u0001\u0001", new int[]{Opcodes.LSUB, 4, 0, 4}, true, objArr6);
        hashMap.put(intern3, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        g((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 4, "\uffef￬\u0005\u0000\b\f\u0003\u0004\u0012\u001e\ufff1￭", ExpandableListView.getPackedPositionType(0L) + 12, (Process.myTid() >> 22) + Opcodes.NEW, false, objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        g(-MotionEvent.axisFromString(""), "\u0005�￪�\u0017\u000b�￼", 8 - Color.red(0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + Opcodes.MONITORENTER, true, objArr8);
        hashMap.put(intern4, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        f("\u0001\u0000\u0001\u0000\u0000\u0001\u0000", new int[]{25, 7, 31, 0}, true, objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        f(null, new int[]{Opcodes.LMUL, 3, Opcodes.INVOKESPECIAL, 3}, true, objArr10);
        hashMap.put(intern5, ((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        f("\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000", new int[]{14, 11, Opcodes.NEW, 7}, false, objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        g(TextUtils.getOffsetBefore("", 0) + 3, "\ufff9￦\ufff9\n\b\u0007\u0013", View.MeasureSpec.getSize(0) + 7, 198 - (ViewConfiguration.getTouchSlop() >> 8), false, objArr12);
        hashMap.put(intern6, ((String) objArr12[0]).intern());
        Object[] objArr13 = new Object[1];
        f("\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{0, 14, 87, 8}, true, objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        f("\u0001\u0000\u0000\u0000\u0001\u0001", new int[]{108, 6, 128, 0}, true, objArr14);
        hashMap.put(intern7, ((String) objArr14[0]).intern());
        Object[] objArr15 = new Object[1];
        g((-16777215) - Color.rgb(0, 0, 0), "\n\u0000\ufff9\ufff8\u0015\ufffa\ufffb￼\ufff7\u000b\u0002", 11 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 196, false, objArr15);
        String intern8 = ((String) objArr15[0]).intern();
        Object[] objArr16 = new Object[1];
        f("\u0001\u0001\u0001", new int[]{114, 3, 61, 0}, true, objArr16);
        hashMap.put(intern8, ((String) objArr16[0]).intern());
        e = Collections.unmodifiableMap(hashMap);
        HashMap hashMap2 = new HashMap();
        Object[] objArr17 = new Object[1];
        f("\u0000\u0000\u0001", new int[]{Opcodes.LNEG, 3, Opcodes.I2L, 0}, true, objArr17);
        String intern9 = ((String) objArr17[0]).intern();
        Object[] objArr18 = new Object[1];
        f("\u0000\u0000\u0001", new int[]{Opcodes.LNEG, 3, Opcodes.I2L, 0}, true, objArr18);
        hashMap2.put(intern9, ((String) objArr18[0]).intern());
        Object[] objArr19 = new Object[1];
        f("\u0000\u0000\u0000", new int[]{Opcodes.ISHL, 3, Opcodes.LSUB, 0}, true, objArr19);
        String intern10 = ((String) objArr19[0]).intern();
        Object[] objArr20 = new Object[1];
        f("\u0000\u0000\u0000", new int[]{Opcodes.ISHL, 3, Opcodes.LSUB, 0}, true, objArr20);
        hashMap2.put(intern10, ((String) objArr20[0]).intern());
        Object[] objArr21 = new Object[1];
        f("\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001", new int[]{Opcodes.LSHR, 12, 0, 4}, false, objArr21);
        String intern11 = ((String) objArr21[0]).intern();
        Object[] objArr22 = new Object[1];
        f("\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001", new int[]{Opcodes.LSHR, 12, 0, 4}, false, objArr22);
        hashMap2.put(intern11, ((String) objArr22[0]).intern());
        d = Collections.unmodifiableMap(hashMap2);
        int i = b + 17;
        j = i % 128;
        int i2 = i % 2;
    }

    private static void g(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        switch (str != null ? (char) 24 : (char) 7) {
            case 7:
                cArr = str;
                break;
            default:
                int i4 = $11 + 7;
                $10 = i4 % 128;
                if (i4 % 2 != 0) {
                }
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        o.a.h hVar = new o.a.h();
        char[] cArr3 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            int i5 = $11 + Opcodes.LNEG;
            $10 = i5 % 128;
            int i6 = i5 % 2;
            hVar.b = cArr2[hVar.a];
            cArr3[hVar.a] = (char) (i3 + hVar.b);
            int i7 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr3[i7]), Integer.valueOf(c)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(TextUtils.getCapsMode("", 0, 0) + 12, (char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), ExpandableListView.getPackedPositionType(0L) + 459);
                    byte b2 = (byte) ($$b - 4);
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    h(b2, b3, (byte) (b3 | 15), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(Color.argb(0, 0, 0, 0) + 11, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), TextUtils.indexOf("", "") + 313);
                        byte b4 = (byte) ($$b - 4);
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        h(b4, b5, (byte) (b5 | 13), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            int i8 = $11 + 81;
            $10 = i8 % 128;
            int i9 = i8 % 2;
            hVar.c = i;
            char[] cArr4 = new char[i2];
            System.arraycopy(cArr3, 0, cArr4, 0, i2);
            System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
        }
        if (z) {
            int i10 = $11 + Opcodes.LSHR;
            $10 = i10 % 128;
            int i11 = i10 % 2;
            char[] cArr5 = new char[i2];
            hVar.a = 0;
            while (true) {
                switch (hVar.a < i2 ? '_' : '2') {
                    case Opcodes.SWAP /* 95 */:
                        cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj3 = o.e.a.s.get(-1412673904);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 10, (char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 312 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)));
                                byte b6 = (byte) ($$b - 4);
                                byte b7 = b6;
                                Object[] objArr7 = new Object[1];
                                h(b6, b7, (byte) (b7 | 13), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    default:
                        cArr3 = cArr5;
                        break;
                }
            }
        }
        objArr[0] = new String(cArr3);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v16, types: [byte[]] */
    private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
        int i;
        int i2;
        int i3;
        int i4;
        ?? r0 = str;
        int i5 = 1;
        int i6 = 0;
        switch (r0 == 0) {
            case false:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i7 = iArr[0];
        int i8 = iArr[1];
        int i9 = 2;
        int i10 = iArr[2];
        int i11 = iArr[3];
        char[] cArr = a;
        if (cArr != null) {
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i12 = 0;
            while (i12 < length) {
                int i13 = $10 + 89;
                $11 = i13 % 128;
                switch (i13 % i9 == 0 ? i6 : i5) {
                    case 1:
                        try {
                            Object[] objArr2 = new Object[i5];
                            objArr2[i6] = Integer.valueOf(cArr[i12]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                i3 = i10;
                                i4 = length;
                            } else {
                                Class cls = (Class) o.e.a.c(View.resolveSize(i6, i6) + 11, (char) TextUtils.getCapsMode("", i6, i6), Process.getGidForName("") + 44);
                                byte b2 = (byte) ($$b - 4);
                                byte b3 = b2;
                                i3 = i10;
                                i4 = length;
                                Object[] objArr3 = new Object[1];
                                h(b2, b3, (byte) (b3 + 2), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr2[i12] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i12++;
                            length = i4;
                            i10 = i3;
                            i5 = 1;
                            i6 = 0;
                            i9 = 2;
                            break;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        int i14 = i10;
                        int i15 = length;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr[i12])};
                            Object obj2 = o.e.a.s.get(1951085128);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(Gravity.getAbsoluteGravity(0, 0) + 11, (char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), 44 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)));
                                byte b4 = (byte) ($$b - 4);
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                h(b4, b5, (byte) (b5 + 2), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj2);
                            }
                            cArr2[i12] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            i12--;
                            length = i15;
                            i10 = i14;
                            i5 = 1;
                            i6 = 0;
                            i9 = 2;
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                }
            }
            i = i10;
            cArr = cArr2;
        } else {
            i = i10;
        }
        char[] cArr3 = new char[i8];
        System.arraycopy(cArr, i7, cArr3, 0, i8);
        if (bArr != null) {
            char[] cArr4 = new char[i8];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i8 ? (char) 27 : '#') {
                    case 27:
                        int i16 = $11 + 89;
                        $10 = i16 % 128;
                        int i17 = i16 % 2;
                        if (bArr[lVar.d] == 1) {
                            int i18 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                                Object obj3 = o.e.a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(View.getDefaultSize(0, 0) + 11, (char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), 448 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                                    byte b6 = (byte) ($$b - 4);
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    h(b6, b7, (byte) (b7 + 3), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj3);
                                }
                                cArr4[i18] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        } else {
                            int i19 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                                Object obj4 = o.e.a.s.get(804049217);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 11, (char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 208 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)));
                                    byte b8 = (byte) ($$b - 4);
                                    byte b9 = b8;
                                    Object[] objArr9 = new Object[1];
                                    h(b8, b9, b9, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj4);
                                }
                                cArr4[i19] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        c2 = cArr4[lVar.d];
                        try {
                            Object[] objArr10 = {lVar, lVar};
                            Object obj5 = o.e.a.s.get(-2112603350);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c(11 - Color.red(0), (char) (Process.myTid() >> 22), 260 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)));
                                byte b10 = (byte) ($$b - 4);
                                byte b11 = b10;
                                Object[] objArr11 = new Object[1];
                                h(b10, b11, (byte) (b11 | 56), objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    default:
                        cArr3 = cArr4;
                        break;
                }
            }
        }
        if (i11 > 0) {
            char[] cArr5 = new char[i8];
            i2 = 0;
            System.arraycopy(cArr3, 0, cArr5, 0, i8);
            int i20 = i8 - i11;
            System.arraycopy(cArr5, 0, cArr3, i20, i11);
            System.arraycopy(cArr5, i11, cArr3, 0, i20);
        } else {
            i2 = 0;
        }
        if (z) {
            char[] cArr6 = new char[i8];
            lVar.d = i2;
            while (lVar.d < i8) {
                cArr6[lVar.d] = cArr3[(i8 - lVar.d) - 1];
                lVar.d++;
            }
            cArr3 = cArr6;
        }
        if (i > 0) {
            lVar.d = 0;
            while (true) {
                switch (lVar.d >= i8) {
                    case true:
                        break;
                    default:
                        cArr3[lVar.d] = (char) (cArr3[lVar.d] - iArr[2]);
                        lVar.d++;
                }
            }
        }
        objArr[0] = new String(cArr3);
    }
}
