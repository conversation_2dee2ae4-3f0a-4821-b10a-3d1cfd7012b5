package o.ey;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.g;
import o.ey.b;
import o.fc.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\d.smali */
public abstract class d<T extends o.fc.e, U extends b<T>> extends a<T, U> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int[] c;
    private static int e;
    private final String d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        c = new int[]{1239152856, 1789392275, -1360170491, 1184463239, -768718909, -2119871963, -498301237, -1875686604, 446108452, -2092961150, -1428398018, 396605687, 282158781, -414105992, -743168161, 102369134, -1736191240, 2147422649};
    }

    static void init$0() {
        $$d = new byte[]{85, 91, 121, -13};
        $$e = Opcodes.DRETURN;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r5, byte r6, short r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 4
            int r5 = r5 + 1
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r7 = r7 + 115
            byte[] r0 = o.ey.d.$$d
            byte[] r1 = new byte[r5]
            r2 = -1
            int r5 = r5 + r2
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r2
            r6 = r5
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L38
        L1a:
            r4 = r7
            r7 = r6
            r6 = r4
        L1d:
            int r2 = r2 + 1
            byte r3 = (byte) r6
            r1[r2] = r3
            if (r2 != r5) goto L2d
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2d:
            r3 = r0[r7]
            r4 = r6
            r6 = r5
            r5 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            r7 = r4
        L38:
            int r5 = -r5
            int r5 = r5 + r7
            int r7 = r8 + 1
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            r4 = r6
            r6 = r5
            r5 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.d.m(short, byte, short, java.lang.Object[]):void");
    }

    public d() {
        Object[] objArr = new Object[1];
        k(new int[]{-1421872363, -840058120}, ExpandableListView.getPackedPositionType(0L) + 3, objArr);
        this.d = ((String) objArr[0]).intern();
    }

    /* JADX INFO: Access modifiers changed from: protected */
    /* JADX WARN: Multi-variable type inference failed */
    @Override // o.ey.a
    public /* synthetic */ o.eg.b e(o.fc.d dVar) throws o.eg.d {
        int i = e + Opcodes.LSHL;
        b = i % 128;
        int i2 = i % 2;
        o.eg.b b2 = b((d<T, U>) dVar);
        int i3 = e + Opcodes.DNEG;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return b2;
        }
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // o.ey.a
    public /* synthetic */ o.fc.d e(o.eg.b bVar) throws o.eg.d {
        int i = b + 3;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                return d(bVar);
            default:
                d(bVar);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public o.eg.b b(T t) throws o.eg.d {
        o.eg.b e2;
        Object obj;
        int i = b + Opcodes.LSUB;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                e2 = super.e((d<T, U>) t);
                Object[] objArr = new Object[1];
                k(new int[]{-1421872363, -840058120}, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 2, objArr);
                obj = objArr[0];
                break;
            default:
                e2 = super.e((d<T, U>) t);
                Object[] objArr2 = new Object[1];
                k(new int[]{-1421872363, -840058120}, 5 >> (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        e2.d(((String) obj).intern(), t.h());
        return e2;
    }

    public T d(o.eg.b bVar) throws o.eg.d {
        T t;
        Object obj;
        int i = e + 99;
        b = i % 128;
        switch (i % 2 != 0 ? '\r' : ')') {
            case '\r':
                t = (T) super.e(bVar);
                Object[] objArr = new Object[1];
                k(new int[]{-1421872363, -840058120}, 4 / Color.red(1), objArr);
                obj = objArr[0];
                break;
            default:
                t = (T) super.e(bVar);
                Object[] objArr2 = new Object[1];
                k(new int[]{-1421872363, -840058120}, 3 - Color.red(0), objArr2);
                obj = objArr2[0];
                break;
        }
        t.a(bVar.i(((String) obj).intern()).intValue());
        return t;
    }

    private static void k(int[] iArr, int i, Object[] objArr) {
        String str;
        String str2;
        Object method;
        int i2;
        String str3;
        g gVar = new g();
        char[] cArr = new char[4];
        int i3 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr2 = c;
        int i4 = 6;
        String str4 = "";
        int i5 = -1667374059;
        int i6 = 1;
        int i7 = 0;
        switch (iArr2 != null ? 'N' : ']') {
            case 'N':
                int length = iArr2.length;
                int[] iArr3 = new int[length];
                int i8 = 0;
                while (true) {
                    switch (i8 < length ? '!' : (char) 3) {
                        case '!':
                            int i9 = $11 + 41;
                            $10 = i9 % 128;
                            if (i9 % i3 != 0) {
                                try {
                                    Object[] objArr2 = new Object[i6];
                                    objArr2[i7] = Integer.valueOf(iArr2[i8]);
                                    Object obj = o.e.a.s.get(Integer.valueOf(i5));
                                    if (obj == null) {
                                        Class cls = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', i7, i7) + 11, (char) (8856 - ((Process.getThreadPriority(i7) + 20) >> i4)), 324 - TextUtils.indexOf("", "", i7, i7));
                                        byte b2 = (byte) i7;
                                        byte b3 = b2;
                                        Object[] objArr3 = new Object[1];
                                        m(b2, b3, (byte) (b3 + 1), objArr3);
                                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj);
                                    }
                                    iArr3[i8] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                                    i8 <<= 1;
                                    i3 = 2;
                                    i4 = 6;
                                    i5 = -1667374059;
                                    i6 = 1;
                                    i7 = 0;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            } else {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(iArr2[i8])};
                                    Object obj2 = o.e.a.s.get(-1667374059);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(':' - AndroidCharacter.getMirror('0'), (char) (8856 - KeyEvent.normalizeMetaState(0)), 324 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)));
                                        byte b4 = (byte) 0;
                                        byte b5 = b4;
                                        Object[] objArr5 = new Object[1];
                                        m(b4, b5, (byte) (b5 + 1), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj2);
                                    }
                                    iArr3[i8] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                    i8++;
                                    i3 = 2;
                                    i4 = 6;
                                    i5 = -1667374059;
                                    i6 = 1;
                                    i7 = 0;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                        default:
                            iArr2 = iArr3;
                            break;
                    }
                }
        }
        int length2 = iArr2.length;
        int[] iArr4 = new int[length2];
        int[] iArr5 = c;
        if (iArr5 != null) {
            int length3 = iArr5.length;
            int[] iArr6 = new int[length3];
            int i10 = 0;
            while (i10 < length3) {
                int i11 = $11 + Opcodes.LSHL;
                $10 = i11 % 128;
                switch (i11 % 2 != 0 ? '2' : (char) 6) {
                    case 6:
                        try {
                            Object[] objArr6 = {Integer.valueOf(iArr5[i10])};
                            Object obj3 = o.e.a.s.get(-1667374059);
                            if (obj3 != null) {
                                i2 = length3;
                                str3 = str4;
                            } else {
                                Class cls3 = (Class) o.e.a.c(View.resolveSize(0, 0) + 10, (char) (8856 - Color.argb(0, 0, 0, 0)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 323);
                                byte b6 = (byte) 0;
                                byte b7 = b6;
                                i2 = length3;
                                str3 = str4;
                                Object[] objArr7 = new Object[1];
                                m(b6, b7, (byte) (b7 + 1), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj3);
                            }
                            iArr6[i10] = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                            i10++;
                            length3 = i2;
                            str4 = str3;
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    default:
                        int i12 = length3;
                        String str5 = str4;
                        try {
                            Object[] objArr8 = {Integer.valueOf(iArr5[i10])};
                            Object obj4 = o.e.a.s.get(-1667374059);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 10, (char) (View.combineMeasuredStates(0, 0) + 8856), 325 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)));
                                byte b8 = (byte) 0;
                                byte b9 = b8;
                                Object[] objArr9 = new Object[1];
                                m(b8, b9, (byte) (b9 + 1), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj4);
                            }
                            iArr6[i10] = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                            i10 <<= 1;
                            length3 = i12;
                            str4 = str5;
                            break;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
            str = str4;
            int i13 = $11 + 15;
            $10 = i13 % 128;
            int i14 = i13 % 2;
            iArr5 = iArr6;
        } else {
            str = "";
        }
        System.arraycopy(iArr5, 0, iArr4, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr4);
            for (int i15 = 0; i15 < 16; i15++) {
                gVar.e ^= iArr4[i15];
                try {
                    Object[] objArr10 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                    Object obj5 = o.e.a.s.get(-2036901605);
                    if (obj5 == null) {
                        obj5 = ((Class) o.e.a.c(11 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), Gravity.getAbsoluteGravity(0, 0) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj5);
                    }
                    int intValue = ((Integer) ((Method) obj5).invoke(null, objArr10)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
            }
            int i16 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i16;
            gVar.c ^= iArr4[16];
            gVar.e ^= iArr4[17];
            int i17 = gVar.e;
            int i18 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            g.d(iArr4);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr11 = {gVar, gVar};
                Object obj6 = o.e.a.s.get(-331007466);
                if (obj6 != null) {
                    method = obj6;
                    str2 = str;
                } else {
                    str2 = str;
                    Class cls5 = (Class) o.e.a.c(TextUtils.indexOf(str2, str2, 0, 0) + 12, (char) (55184 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 515 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                    byte b10 = (byte) 0;
                    byte b11 = b10;
                    Object[] objArr12 = new Object[1];
                    m(b10, b11, b11, objArr12);
                    method = cls5.getMethod((String) objArr12[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, method);
                }
                ((Method) method).invoke(null, objArr11);
                str = str2;
            } catch (Throwable th6) {
                Throwable cause6 = th6.getCause();
                if (cause6 == null) {
                    throw th6;
                }
                throw cause6;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }
}
