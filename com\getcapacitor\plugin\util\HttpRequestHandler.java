package com.getcapacitor.plugin.util;

import android.text.TextUtils;
import android.util.Base64;
import androidx.core.app.NotificationCompat;
import com.getcapacitor.Bridge;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.JSValue;
import com.getcapacitor.PluginCall;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.messaging.Constants;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes13\com\getcapacitor\plugin\util\HttpRequestHandler.smali */
public class HttpRequestHandler {

    @FunctionalInterface
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes13\com\getcapacitor\plugin\util\HttpRequestHandler$ProgressEmitter.smali */
    public interface ProgressEmitter {
        void emit(Integer num, Integer num2);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes13\com\getcapacitor\plugin\util\HttpRequestHandler$HttpURLConnectionBuilder.smali */
    public static class HttpURLConnectionBuilder {
        public Integer connectTimeout;
        public CapacitorHttpUrlConnection connection;
        public Boolean disableRedirects;
        public JSObject headers;
        public String method;
        public Integer readTimeout;
        public URL url;

        public HttpURLConnectionBuilder setConnectTimeout(Integer connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }

        public HttpURLConnectionBuilder setReadTimeout(Integer readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        public HttpURLConnectionBuilder setDisableRedirects(Boolean disableRedirects) {
            this.disableRedirects = disableRedirects;
            return this;
        }

        public HttpURLConnectionBuilder setHeaders(JSObject headers) {
            this.headers = headers;
            return this;
        }

        public HttpURLConnectionBuilder setMethod(String method) {
            this.method = method;
            return this;
        }

        public HttpURLConnectionBuilder setUrl(URL url) {
            this.url = url;
            return this;
        }

        public HttpURLConnectionBuilder openConnection() throws IOException {
            CapacitorHttpUrlConnection capacitorHttpUrlConnection = new CapacitorHttpUrlConnection((HttpURLConnection) this.url.openConnection());
            this.connection = capacitorHttpUrlConnection;
            capacitorHttpUrlConnection.setAllowUserInteraction(false);
            this.connection.setRequestMethod(this.method);
            Integer num = this.connectTimeout;
            if (num != null) {
                this.connection.setConnectTimeout(num.intValue());
            }
            Integer num2 = this.readTimeout;
            if (num2 != null) {
                this.connection.setReadTimeout(num2.intValue());
            }
            Boolean bool = this.disableRedirects;
            if (bool != null) {
                this.connection.setDisableRedirects(bool.booleanValue());
            }
            this.connection.setRequestHeaders(this.headers);
            return this;
        }

        public HttpURLConnectionBuilder setUrlParams(JSObject params) throws MalformedURLException, URISyntaxException, JSONException {
            return setUrlParams(params, true);
        }

        public HttpURLConnectionBuilder setUrlParams(JSObject params, boolean shouldEncode) throws URISyntaxException, MalformedURLException {
            String initialQuery = this.url.getQuery();
            String initialQueryBuilderStr = initialQuery == null ? "" : initialQuery;
            Iterator<String> keys = params.keys();
            if (!keys.hasNext()) {
                return this;
            }
            StringBuilder urlQueryBuilder = new StringBuilder(initialQueryBuilderStr);
            while (keys.hasNext()) {
                String key = keys.next();
                try {
                    StringBuilder value = new StringBuilder();
                    JSONArray arr = params.getJSONArray(key);
                    for (int x = 0; x < arr.length(); x++) {
                        addUrlParam(value, key, arr.getString(x), shouldEncode);
                        if (x != arr.length() - 1) {
                            value.append("&");
                        }
                    }
                    int x2 = urlQueryBuilder.length();
                    if (x2 > 0) {
                        urlQueryBuilder.append("&");
                    }
                    urlQueryBuilder.append((CharSequence) value);
                } catch (JSONException e) {
                    if (urlQueryBuilder.length() > 0) {
                        urlQueryBuilder.append("&");
                    }
                    addUrlParam(urlQueryBuilder, key, params.getString(key), shouldEncode);
                }
            }
            String urlQuery = urlQueryBuilder.toString();
            URI uri = this.url.toURI();
            String unEncodedUrlString = uri.getScheme() + "://" + uri.getAuthority() + uri.getPath() + (!urlQuery.equals("") ? "?" + urlQuery : "") + (uri.getFragment() != null ? uri.getFragment() : "");
            this.url = new URL(unEncodedUrlString);
            return this;
        }

        private static void addUrlParam(StringBuilder sb, String key, String value, boolean shouldEncode) {
            if (shouldEncode) {
                try {
                    key = URLEncoder.encode(key, "UTF-8");
                    value = URLEncoder.encode(value, "UTF-8");
                } catch (UnsupportedEncodingException ex) {
                    throw new RuntimeException(ex.getCause());
                }
            }
            sb.append(key).append("=").append(value);
        }

        public CapacitorHttpUrlConnection build() {
            return this.connection;
        }
    }

    public static JSObject buildResponse(CapacitorHttpUrlConnection connection) throws IOException, JSONException {
        return buildResponse(connection, ResponseType.DEFAULT);
    }

    public static JSObject buildResponse(CapacitorHttpUrlConnection connection, ResponseType responseType) throws IOException, JSONException {
        int statusCode = connection.getResponseCode();
        JSObject output = new JSObject();
        output.put(NotificationCompat.CATEGORY_STATUS, statusCode);
        output.put("headers", (Object) buildResponseHeaders(connection));
        output.put(ImagesContract.URL, (Object) connection.getURL());
        output.put(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, readData(connection, responseType));
        InputStream errorStream = connection.getErrorStream();
        if (errorStream != null) {
            output.put(Constants.IPC_BUNDLE_KEY_SEND_ERROR, true);
        }
        return output;
    }

    public static Object readData(ICapacitorHttpUrlConnection connection, ResponseType responseType) throws IOException, JSONException {
        InputStream errorStream = connection.getErrorStream();
        String contentType = connection.getHeaderField("Content-Type");
        if (errorStream != null) {
            if (isOneOf(contentType, MimeType.APPLICATION_JSON, MimeType.APPLICATION_VND_API_JSON)) {
                return parseJSON(readStreamAsString(errorStream));
            }
            return readStreamAsString(errorStream);
        }
        if (contentType != null && contentType.contains(MimeType.APPLICATION_JSON.getValue())) {
            return parseJSON(readStreamAsString(connection.getInputStream()));
        }
        InputStream stream = connection.getInputStream();
        switch (AnonymousClass1.$SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType[responseType.ordinal()]) {
            case 1:
            case 2:
                return readStreamAsBase64(stream);
            case 3:
                return parseJSON(readStreamAsString(stream));
            default:
                return readStreamAsString(stream);
        }
    }

    /* renamed from: com.getcapacitor.plugin.util.HttpRequestHandler$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes13\com\getcapacitor\plugin\util\HttpRequestHandler$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType;

        static {
            int[] iArr = new int[ResponseType.values().length];
            $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType = iArr;
            try {
                iArr[ResponseType.ARRAY_BUFFER.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType[ResponseType.BLOB.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType[ResponseType.JSON.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType[ResponseType.DOCUMENT.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$com$getcapacitor$plugin$util$HttpRequestHandler$ResponseType[ResponseType.TEXT.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public static boolean isOneOf(String contentType, MimeType... mimeTypes) {
        if (contentType != null) {
            for (MimeType mimeType : mimeTypes) {
                if (contentType.contains(mimeType.getValue())) {
                    return true;
                }
            }
        }
        return false;
    }

    public static JSObject buildResponseHeaders(CapacitorHttpUrlConnection connection) {
        JSObject output = new JSObject();
        for (Map.Entry<String, List<String>> entry : connection.getHeaderFields().entrySet()) {
            String valuesString = TextUtils.join(", ", entry.getValue());
            output.put(entry.getKey(), valuesString);
        }
        return output;
    }

    public static Object parseJSON(String input) throws JSONException {
        new JSONObject();
        try {
            if ("null".equals(input.trim())) {
                return JSONObject.NULL;
            }
            if ("true".equals(input.trim())) {
                return true;
            }
            if ("false".equals(input.trim())) {
                return false;
            }
            if (input.trim().length() <= 0) {
                return "";
            }
            if (input.trim().matches("^\".*\"$")) {
                return input.trim().substring(1, input.trim().length() - 1);
            }
            if (input.trim().matches("^-?\\d+$")) {
                return Integer.valueOf(Integer.parseInt(input.trim()));
            }
            if (input.trim().matches("^-?\\d+(\\.\\d+)?$")) {
                return Double.valueOf(Double.parseDouble(input.trim()));
            }
            try {
                return new JSObject(input);
            } catch (JSONException e) {
                return new JSArray(input);
            }
        } catch (JSONException e2) {
            return input;
        }
    }

    public static String readStreamAsBase64(InputStream in) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[1024];
            while (true) {
                int readBytes = in.read(buffer);
                if (readBytes != -1) {
                    out.write(buffer, 0, readBytes);
                } else {
                    byte[] result = out.toByteArray();
                    String encodeToString = Base64.encodeToString(result, 0, result.length, 0);
                    out.close();
                    return encodeToString;
                }
            }
        } catch (Throwable th) {
            try {
                out.close();
            } catch (Throwable th2) {
                th.addSuppressed(th2);
            }
            throw th;
        }
    }

    public static String readStreamAsString(InputStream in) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(in));
        try {
            StringBuilder builder = new StringBuilder();
            String line = reader.readLine();
            while (line != null) {
                builder.append(line);
                line = reader.readLine();
                if (line != null) {
                    builder.append(System.getProperty("line.separator"));
                }
            }
            String sb = builder.toString();
            reader.close();
            return sb;
        } catch (Throwable th) {
            try {
                reader.close();
            } catch (Throwable th2) {
                th.addSuppressed(th2);
            }
            throw th;
        }
    }

    public static JSObject request(PluginCall call, String httpMethod, Bridge bridge) throws IOException, URISyntaxException, JSONException {
        String urlString = call.getString(ImagesContract.URL, "");
        JSObject headers = call.getObject("headers", new JSObject());
        JSObject params = call.getObject("params", new JSObject());
        Integer connectTimeout = call.getInt("connectTimeout");
        Integer readTimeout = call.getInt("readTimeout");
        Boolean disableRedirects = call.getBoolean("disableRedirects");
        Boolean shouldEncode = call.getBoolean("shouldEncodeUrlParams", true);
        ResponseType responseType = ResponseType.parse(call.getString("responseType"));
        String dataType = call.getString("dataType");
        String method = httpMethod != null ? httpMethod.toUpperCase(Locale.ROOT) : call.getString("method", "GET").toUpperCase(Locale.ROOT);
        boolean isHttpMutate = method.equals("DELETE") || method.equals("PATCH") || method.equals("POST") || method.equals("PUT");
        URL url = new URL(urlString);
        HttpURLConnectionBuilder connectionBuilder = new HttpURLConnectionBuilder().setUrl(url).setMethod(method).setHeaders(headers).setUrlParams(params, shouldEncode.booleanValue()).setConnectTimeout(connectTimeout).setReadTimeout(readTimeout).setDisableRedirects(disableRedirects).openConnection();
        CapacitorHttpUrlConnection connection = connectionBuilder.build();
        if (bridge != null && !isDomainExcludedFromSSL(bridge, url).booleanValue()) {
            connection.setSSLSocketFactory(bridge);
        }
        if (isHttpMutate) {
            JSValue data = new JSValue(call, Constants.ScionAnalytics.MessageType.DATA_MESSAGE);
            if (data.getValue() != null) {
                connection.setDoOutput(true);
                connection.setRequestBody(call, data, dataType);
            }
        }
        call.getData().put("activeCapacitorHttpUrlConnection", (Object) connection);
        connection.connect();
        JSObject response = buildResponse(connection, responseType);
        connection.disconnect();
        call.getData().remove("activeCapacitorHttpUrlConnection");
        return response;
    }

    public static Boolean isDomainExcludedFromSSL(Bridge bridge, URL url) {
        try {
            Class<?> sslPinningImpl = Class.forName("io.ionic.sslpinning.SSLPinning");
            Method method = sslPinningImpl.getDeclaredMethod("isDomainExcluded", Bridge.class, URL.class);
            return (Boolean) method.invoke(sslPinningImpl.getDeclaredConstructor(new Class[0]).newInstance(new Object[0]), bridge, url);
        } catch (Exception e) {
            return false;
        }
    }
}
