package o.bm;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ad.b;
import o.c.a;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bm\c.smali */
final class c extends b {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        d();
        KeyEvent.getDeadChar(0, 0);
        int i = b + 89;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 38 / 0;
                break;
        }
    }

    static void d() {
        d = 8394144820991287865L;
    }

    static void init$0() {
        $$d = new byte[]{43, 59, -40, 18};
        $$e = 206;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = 71 - r9
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.bm.c.$$d
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r9
            r5 = r2
            r9 = r8
            goto L2c
        L15:
            r3 = r2
        L16:
            int r7 = r7 + 1
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r6
        L2c:
            int r3 = -r3
            int r8 = r8 + r3
            r3 = r5
            r6 = r9
            r9 = r8
            r8 = r6
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bm.c.j(byte, short, short, java.lang.Object[]):void");
    }

    c(Context context, a aVar) {
        super(context, aVar);
    }

    @Override // o.ad.b
    public final boolean c() {
        String intern;
        Object obj;
        if (!e()) {
            return false;
        }
        switch (o.j.c.a()) {
            case true:
                int i = b + 9;
                e = i % 128;
                switch (i % 2 != 0) {
                    case true:
                        g.c();
                        Object[] objArr = new Object[1];
                        h("쭐쬅\ue935䮣程㾣뽖䂼垅斸ꬬ⎠\udc0e\uf1d3⚞ꠀ槛\u0e62鋲\udc92\ue551骋ใ䃖狝ᜉ薧\uf54a躮ꎡ\uf13e禤ᨙ㿙沖\uee30韾둈\ud8e6ኙ⍩샺告蛽", 1 / (AudioTrack.getMaxVolume() > 1.0f ? 1 : (AudioTrack.getMaxVolume() == 1.0f ? 0 : -1)), objArr);
                        intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        h("횔훽坑\uf5c4㻸筵梏嵸聈\udbd2\ueff9\uf46c쇐侻执翋琨뀃혃ଡ଼\uf8a6ⓧ䪥霯漅ꥨ셙⊆錤ᶟ떄깩ߎ膯⡨㧷診ਖ鰝앛㺠纉ჼ儤ꔖ\ue377蜡\udcca⥬埏篞栛巛\udba6\uee34\uf3f5쁂䱺戛罍璭난횃୪ﭑ┪䴢隽漴꧃쇁∎鏤\u125f둲귶ٍ蘱⣥㥞誵અ鲂쒒ㄆ罧ጥ傯ꖅ\ue3e8蟙\udc06", KeyEvent.getMaxKeyCode() / Opcodes.LMUL, objArr2);
                        obj = objArr2[0];
                        break;
                    default:
                        g.c();
                        Object[] objArr3 = new Object[1];
                        h("쭐쬅\ue935䮣程㾣뽖䂼垅斸ꬬ⎠\udc0e\uf1d3⚞ꠀ槛\u0e62鋲\udc92\ue551骋ใ䃖狝ᜉ薧\uf54a躮ꎡ\uf13e禤ᨙ㿙沖\uee30韾둈\ud8e6ኙ⍩샺告蛽", 1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr3);
                        intern = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        h("횔훽坑\uf5c4㻸筵梏嵸聈\udbd2\ueff9\uf46c쇐侻执翋琨뀃혃ଡ଼\uf8a6ⓧ䪥霯漅ꥨ셙⊆錤ᶟ떄깩ߎ膯⡨㧷診ਖ鰝앛㺠纉ჼ儤ꔖ\ue377蜡\udcca⥬埏篞栛巛\udba6\uee34\uf3f5쁂䱺戛罍璭난횃୪ﭑ┪䴢隽漴꧃쇁∎鏤\u125f둲귶ٍ蘱⣥㥞誵અ鲂쒒ㄆ罧ጥ傯ꖅ\ue3e8蟙\udc06", KeyEvent.getMaxKeyCode() >> 16, objArr4);
                        obj = objArr4[0];
                        break;
                }
                g.d(intern, ((String) obj).intern());
                return false;
            default:
                g.c();
                Object[] objArr5 = new Object[1];
                h("쭐쬅\ue935䮣程㾣뽖䂼垅斸ꬬ⎠\udc0e\uf1d3⚞ꠀ槛\u0e62鋲\udc92\ue551骋ใ䃖狝ᜉ薧\uf54a躮ꎡ\uf13e禤ᨙ㿙沖\uee30韾둈\ud8e6ኙ⍩샺告蛽", Color.alpha(0), objArr5);
                String intern2 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                h("⡏⠦⻩豼볔陵\uda7eꎣ㊹ꉪ淕䚝㼋㘃\ue04b촺諳즻启릭ٽ嵟좉◞釞탐䍵遷淿搧㞨Ვ龜\uf852ꩃ謑瓪玾ḽ瞥쁣ܫ銑\ue3df富骊Ԏ湩힢\u2e77裂\udaf3ꌊꈈ汑䄄㺓㗂\ue03f춽訿줭咧맒\u05c9峕콀␎釯큻䏭郿洿毧㙞ἇ\uf896ﾉ\uaac9讯瑮猽Ắ癣쿝۟鄉\ue25e孞驐\u05f5滷", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr6);
                g.d(intern2, ((String) objArr6[0]).intern());
                int i2 = b + 25;
                e = i2 % 128;
                if (i2 % 2 == 0) {
                    return true;
                }
                int i3 = 79 / 0;
                return true;
        }
    }

    @Override // o.ad.b
    public final boolean e(o.h.d dVar) {
        int i = b + 109;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        int i4 = i2 + 69;
        b = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 364
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bm.c.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
