package o.fm;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.dd.e;
import o.ee.g;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fm\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static char d;
    private static char[] e;
    private static char f;
    private static char g;
    private static char h;
    private static int i;
    private static int j;
    private final b c = new b();
    private final d a = new d();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        b();
        Color.blue(0);
        KeyEvent.getMaxKeyCode();
        View.getDefaultSize(0, 0);
        View.MeasureSpec.getMode(0);
        AudioTrack.getMaxVolume();
        TextUtils.getOffsetAfter("", 0);
        View.getDefaultSize(0, 0);
        View.MeasureSpec.getSize(0);
        View.getDefaultSize(0, 0);
        int i2 = j + 67;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void b() {
        e = new char[]{1455, 51868, 39849, 26845, 14651, 3710, 57158, 44980, 31902, 19942, 4705, 58117, 45171, 33094, 20878, 9964, 63446, 50222, 38175, 23119, 10912, 64406, 51450, 39210, 28179, 16231, 3149, 56493, 44498, 29393, 17203, 4113, 57691, 45483, 34435, 22467, 9413, 62744, 47718, 35678, 23476, 10372, 63946, 52778, 40734, 27767, 15544, 11420, 58269, 45794, 16846, 4101, 10104, 63046, 34494, 21908, 25828, 15146, 51722, 39294, 43095, 30872, 50609, 2705, 23551, 43225, 63789, 52858, 8030, 28576, 48282, 36322, 53886, 9048, 28716, 16726, 37257, 59124, 14298, 1135, 21782, 39503, 60081, 15245, 2279, 22827, 44565, 65401, 52301, 7328, 28143, 45715, 33571, 53266, 8472, 29100, 18050, 38882, 58582, 13598, 31350, 19209, 39855, 59529, 14750, 3633, 24325, 44144, 64689, 11424, 58240, 45806, 16840, 4156, 10091, 63055, 34481, 21899, 25843, 15215, 51785, 39229, 43094, 30862, 4067, 57035, 60711, 48135, 29528, 928, 53918, 57779, 45117, 18194, 5731, 9549, 62964, 34045, 23504, 27198, 14614, 51276, 39100, 44930, 32498, 3542, 56335, 37744, 41496, 29419, 470, 11424, 58240, 45809, 16861, 4153, 10083, 63047, 34552, 21889, 25843, 15165, 51735, 39284, 43073, 30879, 4069, 57053, 60798, 48130, 29535, 928, 53896, 57779, 45112, 18195, 5731, 9561, 62897, 34047, 23495, 27189, 14611, 51276, 39101, 44999, 32422, 3477, 26954, 42607, 63253, 1080, 21958, 49148, 28894, 8611, 53917, 33633, 46127, 25923, 5557, 50833, 63395, 43135, 22848, 2620, 15110, 60354, 40110, 19870, 32318, 12116, 57357, 37111, 16862, 29427, 9080, 54355, 34083, 46617, 26353, 6079, 51335, 63861, 43603, 23308, 3069, 11434, 58264, 45802, 16847, 11452, 58270, 45795, 16861, 4129, 10095, 62979, 34549, 21969, 25827, 15167, 51712, 39292, 43078, 30850, 4078, 57054, 60798, 48148, 29530, 936, 53978, 57827, 45114, 18180, 5728, 9562, 62886, 34024, 23500, 27192, 14613, 51290, 18703, 34340, 55119, 9332, 30105, 17115, 37842, 58130, 12304, 346, 24217, 44970, 64664, 52666, 7534, 27200, 47985, 34955, 55718, 5872, 26176, 46908, 33879, 54687, 8864, 29571, 16618, 36867, 57677, 15969, 3995, 23719, 44521, 64773, 51745, 7004, 26723, 50863, 2436, 22767, 43988, 64057, 52603, 7282, 27826, 49072, 36602, 53561, 8202, 29496, 16922, 37582, 58870, 13529, 1833, 22043, 39240, 59820, 14486, 3052, 23080, 44288, 64547, 53081, 8103, 28389, 45447, 32814, 54023, 8777, 29357, 17799, 38123, 59349, 13825, 31077, 18520, 39079, 60371, 15040, 3378, 23575, 44921, 65518, 52959, 4540, 11434, 58241, 45802, 16849, 4156, 10110, 63095, 34487, 21941, 25855, 15164, 51727, 39229, 43039, 30923, 4083, 57037, 60721, 48133, 29509, 939, 53917, 57779, 45099, 18184, 5750, 9559, 62897, 34047, 23495, 27199, 14672, 51267, 39101, 44936, 32498, 3477, 56350, 37740, 41496, 29365, 415, 53468, 59183, 11434, 58241, 45802, 16849, 4156, 10110, 63095, 34487, 21941, 25855, 15164, 51727, 39229, 43039, 30923, 4074, 57034, 60721, 48153, 29452, 928, 53890, 57840, 45101, 18193, 5746, 9558, 62907, 34019, 23426, 27180, 14616, 51264, 39074, 44930, 32444, 3526, 56335, 37745, 41553, 29360, 410, 53446, 59198, 46612, 17788, 5548, 9408, 64492, 35533, 22834, 26750, 16133, 53162, 40577, 44493, 31783, 13059, 49773, 37201, 41347, 28897, 2014, 54819, 11439, 58242, 45810, 16847, 4157};
        b = 7232756355671122926L;
        h = (char) 27506;
        g = (char) 36228;
        f = (char) 26164;
        d = (char) 34023;
    }

    static void init$0() {
        $$a = new byte[]{17, -112, 34, 95};
        $$b = Opcodes.FCMPL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = r6 + 1
            byte[] r0 = o.fm.c.$$a
            int r8 = r8 + 102
            int r7 = r7 * 2
            int r7 = r7 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r8
            r4 = r2
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r3 = -r3
            int r6 = r6 + r3
            int r8 = r8 + 1
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.c.m(byte, short, int, java.lang.Object[]):void");
    }

    public final b e() {
        int i2 = i + 71;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? ']' : 'Z') {
            case 'Z':
                return this.c;
            default:
                throw null;
        }
    }

    public final d c() {
        int i2 = j + 45;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        d dVar = this.a;
        int i5 = i3 + Opcodes.DREM;
        j = i5 % 128;
        int i6 = i5 % 2;
        return dVar;
    }

    public final void a(Context context) throws i {
        String string;
        Object[] objArr = new Object[1];
        k((char) (10496 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), ViewConfiguration.getScrollBarSize() >> 8, 48 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        try {
            Object[] objArr2 = new Object[1];
            l("ꝳ\ue3dc", View.MeasureSpec.getMode(0) + 2, objArr2);
            string = sharedPreferences.getString(((String) objArr2[0]).intern(), "");
        } catch (o.eg.d e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr3 = new Object[1];
            l("夸趁羄⅏ㅛ큳딩뮑潹㲦騞滸贀\uefdd\u2454踗멑䛀\ue160\ud880驛ꐂ夸趁髝ﶼꝳ\ue3dc캯\ue714촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080\ue078䈪ꭊ鏬", 45 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr3);
            throw new i(sb.append(((String) objArr3[0]).intern()).append(e2.getMessage()).toString());
        }
        if (string.isEmpty()) {
            g.c();
            Object[] objArr4 = new Object[1];
            k((char) Color.argb(0, 0, 0, 0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 48, 15 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr4);
            String intern = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            l("夸趁䥶䄀ݗ\uf884ㅛ큳횂샱帗ੇ㞭墿氇鈚ꝳ\ue3dc캯\ue714촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080疰䷄\uf4c2郵榭᭧딩뮑ⲧ쪴῁嗁啒啻\u139fफͶ쳬촳뤉傷蕝ボ\uf39b緢玢夸趁䶳쉦", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 60, objArr5);
            g.d(intern, ((String) objArr5[0]).intern());
            int i2 = j + 9;
            i = i2 % 128;
            int i3 = i2 % 2;
            return;
        }
        g.c();
        Object[] objArr6 = new Object[1];
        k((char) (TextUtils.lastIndexOf("", '0') + 1), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 47, 16 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr6);
        String intern2 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        k((char) (59665 - ExpandableListView.getPackedPositionGroup(0L)), 62 - KeyEvent.keyCodeFromString(""), TextUtils.lastIndexOf("", '0') + 48, objArr7);
        g.d(intern2, ((String) objArr7[0]).intern());
        String d2 = new e(context).d(string);
        g.c();
        Object[] objArr8 = new Object[1];
        k((char) Color.red(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 47, (ViewConfiguration.getTouchSlop() >> 8) + 15, objArr8);
        String intern3 = ((String) objArr8[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr9 = new Object[1];
        k((char) (KeyEvent.getMaxKeyCode() >> 16), AndroidCharacter.getMirror('0') + '=', (ViewConfiguration.getLongPressTimeout() >> 16) + 42, objArr9);
        g.d(intern3, sb2.append(((String) objArr9[0]).intern()).append(d2).toString());
        switch (d2 == null) {
            case false:
                int i4 = i + 49;
                j = i4 % 128;
                int i5 = i4 % 2;
                switch (d2.isEmpty() ? 'G' : '.') {
                    case '.':
                        try {
                            a(new o.eg.b(d2));
                            return;
                        } catch (o.eg.d e3) {
                            StringBuilder sb3 = new StringBuilder();
                            Object[] objArr10 = new Object[1];
                            k((char) View.combineMeasuredStates(0, 0), 151 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), 37 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr10);
                            throw new o.eg.d(sb3.append(((String) objArr10[0]).intern()).append(e3.getMessage()).toString());
                        }
                }
                StringBuilder sb4 = new StringBuilder();
                Object[] objArr32 = new Object[1];
                l("夸趁羄⅏ㅛ큳딩뮑潹㲦騞滸贀\uefdd\u2454踗멑䛀\ue160\ud880驛ꐂ夸趁髝ﶼꝳ\ue3dc캯\ue714촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080\ue078䈪ꭊ鏬", 45 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr32);
                throw new i(sb4.append(((String) objArr32[0]).intern()).append(e2.getMessage()).toString());
            default:
                g.c();
                Object[] objArr11 = new Object[1];
                k((char) TextUtils.getCapsMode("", 0, 0), TextUtils.indexOf("", "", 0, 0) + 47, (ViewConfiguration.getEdgeSlop() >> 16) + 15, objArr11);
                String intern4 = ((String) objArr11[0]).intern();
                Object[] objArr12 = new Object[1];
                l("夸趁䥶䄀ݗ\uf884ㅛ큳횂샱帗ੇ㞭墿氇鈚ꝳ\ue3dc캯\ue714촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080疰䷄\uf4c2郵榭᭧딩뮑ⲧ쪴῁嗁啒啻\u139fफͶ쳬촳뤉傷蕝ボ\uf39b緢玢夸趁䶳쉦", 61 - TextUtils.getOffsetAfter("", 0), objArr12);
                g.d(intern4, ((String) objArr12[0]).intern());
                return;
        }
    }

    private void a(o.eg.b bVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        k((char) (17888 - (Process.myTid() >> 22)), View.MeasureSpec.getSize(0) + 188, (ViewConfiguration.getWindowTouchSlop() >> 8) + 5, objArr);
        switch (bVar.b(((String) objArr[0]).intern()) ? (char) 19 : (char) 21) {
            case 19:
                g.c();
                Object[] objArr2 = new Object[1];
                k((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 47, 15 - Gravity.getAbsoluteGravity(0, 0), objArr2);
                String intern = ((String) objArr2[0]).intern();
                Object[] objArr3 = new Object[1];
                k((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 37695), (ViewConfiguration.getJumpTapTimeout() >> 16) + Opcodes.INSTANCEOF, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 34, objArr3);
                g.d(intern, ((String) objArr3[0]).intern());
                b bVar2 = this.c;
                Object[] objArr4 = new Object[1];
                k((char) (KeyEvent.getDeadChar(0, 0) + 17888), TextUtils.lastIndexOf("", '0', 0, 0) + Opcodes.ANEWARRAY, 5 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr4);
                bVar2.d(bVar.v(((String) objArr4[0]).intern()));
                break;
        }
        Object[] objArr5 = new Object[1];
        k((char) ('0' - AndroidCharacter.getMirror('0')), TextUtils.indexOf((CharSequence) "", '0') + 228, (-16777212) - Color.rgb(0, 0, 0), objArr5);
        switch (!bVar.b(((String) objArr5[0]).intern())) {
            case false:
                int i2 = j + 97;
                i = i2 % 128;
                int i3 = i2 % 2;
                g.c();
                Object[] objArr6 = new Object[1];
                k((char) KeyEvent.normalizeMetaState(0), 47 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 15 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr6);
                String intern2 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                k((char) ExpandableListView.getPackedPositionType(0L), Process.getGidForName("") + 232, TextUtils.getTrimmedLength("") + 33, objArr7);
                g.d(intern2, ((String) objArr7[0]).intern());
                d dVar = this.a;
                Object[] objArr8 = new Object[1];
                k((char) View.getDefaultSize(0, 0), TextUtils.getOffsetBefore("", 0) + 227, 4 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr8);
                dVar.d(bVar.v(((String) objArr8[0]).intern()));
                int i4 = i + 35;
                j = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
    }

    public final void b(Context context) {
        String str;
        Object[] objArr = new Object[1];
        k((char) (ViewConfiguration.getPressedStateDuration() >> 16), 47 - (ViewConfiguration.getTouchSlop() >> 8), 15 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 10497), (-1) - TextUtils.lastIndexOf("", '0', 0, 0), '_' - AndroidCharacter.getMirror('0'), objArr2);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr2[0]).intern(), 0);
        try {
            o.eg.b bVar = new o.eg.b();
            o.eg.b b2 = this.c.b();
            if (b2.d() != 0) {
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                l("瓅⌞뙘쨊䥶䄀嵇隻潢橫ロ㸰帗ੇ疰䷄캯\ue714ݗ\uf884ㅛ큳횂샱딩뮑䉀糷몐\udfba촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080\ue6b0ଳ⠠\ud990켖ᚘⲧ쪴", TextUtils.lastIndexOf("", '0', 0, 0) + 51, objArr3);
                g.d(intern, sb.append(((String) objArr3[0]).intern()).append(b2.b()).toString());
                Object[] objArr4 = new Object[1];
                k((char) (17889 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 188 - (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.getCapsMode("", 0, 0) + 5, objArr4);
                bVar.d(((String) objArr4[0]).intern(), b2);
                int i2 = j + 73;
                i = i2 % 128;
                int i3 = i2 % 2;
            } else {
                g.c();
                Object[] objArr5 = new Object[1];
                k((char) (TextUtils.indexOf((CharSequence) "", '0') + 26022), 264 - (Process.myTid() >> 22), (ViewConfiguration.getTapTimeout() >> 16) + 37, objArr5);
                g.d(intern, ((String) objArr5[0]).intern());
            }
            o.eg.b e2 = this.a.e();
            if (e2.d() != 0) {
                g.c();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr6 = new Object[1];
                k((char) (View.MeasureSpec.getSize(0) + 59909), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 300, TextUtils.indexOf("", "", 0, 0) + 49, objArr6);
                g.d(intern, sb2.append(((String) objArr6[0]).intern()).append(e2.b()).toString());
                Object[] objArr7 = new Object[1];
                k((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), TextUtils.getOffsetAfter("", 0) + 227, View.resolveSizeAndState(0, 0, 0) + 4, objArr7);
                bVar.d(((String) objArr7[0]).intern(), e2);
            } else {
                g.c();
                Object[] objArr8 = new Object[1];
                l("瓅⌞뙘쨊䥶䄀嵇隻潢橫ロ㸰帗ੇ謟⮶\ue577\uf3b5緉佼♗龲Ꙉ늳촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080", 36 - Color.red(0), objArr8);
                g.d(intern, ((String) objArr8[0]).intern());
            }
            if (bVar.d() != 0) {
                g.c();
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr9 = new Object[1];
                l("瓅⌞뙘쨊䥶䄀嵇隻潢橫ロ㸰帗ੇ疰䷄캯\ue714ݗ\uf884ㅛ큳횂샱딩뮑潹㲦騞滸\ue078䈪ꭊ鏬", 33 - Color.green(0), objArr9);
                g.d(intern, sb3.append(((String) objArr9[0]).intern()).append(bVar.b()).toString());
                str = new e(context).a(bVar.b());
                g.c();
                Object[] objArr10 = new Object[1];
                k((char) Color.argb(0, 0, 0, 0), 350 - Color.blue(0), (ViewConfiguration.getLongPressTimeout() >> 16) + 44, objArr10);
                g.d(intern, ((String) objArr10[0]).intern());
            } else {
                g.c();
                Object[] objArr11 = new Object[1];
                l("瓅⌞뙘쨊䥶䄀嵇隻潢橫ロ㸰帗ੇ㞭墿氇鈚ꝳ\ue3dc캯\ue714촳뤉榭᭧氽\uf5fb榭᭧ﵜ晵굓\uf080膁䮟氇鈚毲ꐀ\u2430㭵\ue160\ud880驛ꐂ姢뵺", 48 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr11);
                g.d(intern, ((String) objArr11[0]).intern());
                str = "";
            }
            SharedPreferences.Editor edit = sharedPreferences.edit();
            Object[] objArr12 = new Object[1];
            l("ꝳ\ue3dc", 2 - (KeyEvent.getMaxKeyCode() >> 16), objArr12);
            edit.putString(((String) objArr12[0]).intern(), str).commit();
            this.a.b();
            this.c.e();
            int i4 = i + 41;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    int i5 = 62 / 0;
                    return;
                default:
                    return;
            }
        } catch (o.eg.d e3) {
            g.c();
            Object[] objArr13 = new Object[1];
            k((char) TextUtils.indexOf("", "", 0, 0), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 393, 64 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr13);
            g.a(intern, ((String) objArr13[0]).intern(), e3);
            SharedPreferences.Editor edit2 = sharedPreferences.edit();
            Object[] objArr14 = new Object[1];
            l("ꝳ\ue3dc", TextUtils.getCapsMode("", 0, 0) + 2, objArr14);
            edit2.putString(((String) objArr14[0]).intern(), "").commit();
        }
    }

    public final void d() {
        int i2 = i + 71;
        j = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((char) (MotionEvent.axisFromString("") + 1), 46 - TextUtils.indexOf((CharSequence) "", '0', 0), 15 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) View.resolveSize(0, 0), 458 - (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 5, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.c.c();
        this.a.d();
        int i4 = j + 93;
        i = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x0029, code lost:
    
        if (r4.c.a() == false) goto L15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a() {
        /*
            r4 = this;
            int r0 = o.fm.c.i
            int r0 = r0 + 25
            int r1 = r0 % 128
            o.fm.c.j = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L23
            o.fm.b r0 = r4.c
            boolean r0 = r0.a()
            r3 = 68
            int r3 = r3 / r2
            if (r0 != 0) goto L1c
            r0 = 77
            goto L1d
        L1c:
            r0 = r1
        L1d:
            switch(r0) {
                case 77: goto L2b;
                default: goto L20;
            }
        L20:
            goto L3a
        L21:
            r0 = move-exception
            throw r0
        L23:
            o.fm.b r0 = r4.c
            boolean r0 = r0.a()
            if (r0 != 0) goto L3a
        L2b:
            o.fm.d r0 = r4.a
            boolean r0 = r0.c()
            if (r0 == 0) goto L35
            r0 = r2
            goto L36
        L35:
            r0 = r1
        L36:
            switch(r0) {
                case 0: goto L3a;
                default: goto L39;
            }
        L39:
            return r2
        L3a:
            int r0 = o.fm.c.i
            int r0 = r0 + 87
            int r2 = r0 % 128
            o.fm.c.j = r2
            int r0 = r0 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.c.a():boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 570
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.c.k(char, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0027. Please report as an issue. */
    private static void l(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 570
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.c.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
