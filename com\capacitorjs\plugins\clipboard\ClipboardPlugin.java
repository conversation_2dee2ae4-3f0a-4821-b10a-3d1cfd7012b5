package com.capacitorjs.plugins.clipboard;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.android.gms.common.internal.ImagesContract;

@CapacitorPlugin(name = "Clipboard")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\clipboard\ClipboardPlugin.smali */
public class ClipboardPlugin extends Plugin {
    private Clipboard implementation;

    @Override // com.getcapacitor.Plugin
    public void load() {
        this.implementation = new Clipboard(getContext());
    }

    @PluginMethod
    public void write(PluginCall call) {
        ClipboardWriteResponse response;
        String strVal = call.getString("string");
        String imageVal = call.getString("image");
        String urlVal = call.getString(ImagesContract.URL);
        String label = call.getString("label");
        if (strVal != null) {
            response = this.implementation.write(label, strVal);
        } else if (imageVal != null) {
            response = this.implementation.write(label, imageVal);
        } else if (urlVal != null) {
            response = this.implementation.write(label, urlVal);
        } else {
            call.reject("No data provided");
            return;
        }
        if (response.isSuccess()) {
            call.resolve();
        } else {
            call.reject(response.getErrorMessage());
        }
    }

    @PluginMethod
    public void read(PluginCall call) {
        ClipboardData result = this.implementation.read();
        if (result == null) {
            call.reject("Unable to read clipboard from the given Context");
            return;
        }
        if (result.getValue() == null) {
            call.reject("There is no data on the clipboard");
            return;
        }
        JSObject resultJS = new JSObject();
        resultJS.put("value", result.getValue());
        resultJS.put("type", result.getType());
        call.resolve(resultJS);
    }
}
