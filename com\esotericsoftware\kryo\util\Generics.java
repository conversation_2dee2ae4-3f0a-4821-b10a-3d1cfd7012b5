package com.esotericsoftware.kryo.util;

import java.lang.reflect.Array;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.GenericDeclaration;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.util.ArrayList;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Generics.smali */
public interface Generics {
    int getGenericTypesSize();

    Class nextGenericClass();

    GenericType[] nextGenericTypes();

    void popGenericType();

    void popTypeVariables(int i);

    void pushGenericType(GenericType genericType);

    int pushTypeVariables(GenericsHierarchy genericsHierarchy, GenericType[] genericTypeArr);

    Class resolveTypeVariable(TypeVariable typeVariable);

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Generics$GenericsHierarchy.smali */
    public static class GenericsHierarchy {
        final int[] counts;
        final TypeVariable[] parameters;
        final int rootTotal;
        final int total;

        public GenericsHierarchy(Class type) {
            TypeVariable[] params;
            int i;
            int i2;
            IntArray counts = new IntArray();
            ArrayList<TypeVariable> parameters = new ArrayList<>();
            int total = 0;
            Class current = type;
            do {
                TypeVariable[] params2 = current.getTypeParameters();
                int i3 = 0;
                int n = params2.length;
                while (i3 < n) {
                    TypeVariable param = params2[i3];
                    parameters.add(param);
                    int i4 = 1;
                    counts.add(1);
                    Class currentSuper = current;
                    while (true) {
                        Type genericSuper = currentSuper.getGenericSuperclass();
                        currentSuper = currentSuper.getSuperclass();
                        if (!(genericSuper instanceof ParameterizedType)) {
                            break;
                        }
                        TypeVariable[] superParams = currentSuper.getTypeParameters();
                        Type[] superArgs = ((ParameterizedType) genericSuper).getActualTypeArguments();
                        int ii = 0;
                        int nn = superArgs.length;
                        while (ii < nn) {
                            Type superArg = superArgs[ii];
                            if (superArg != param) {
                                params = params2;
                                i = i3;
                                i2 = 1;
                            } else {
                                param = superParams[ii];
                                parameters.add(param);
                                params = params2;
                                i = i3;
                                i2 = 1;
                                counts.incr(counts.size - 1, 1);
                            }
                            ii++;
                            i4 = i2;
                            params2 = params;
                            i3 = i;
                        }
                        i3 = i3;
                    }
                    total += counts.peek();
                    i3++;
                }
                current = current.getSuperclass();
            } while (current != null);
            this.total = total;
            this.rootTotal = type.getTypeParameters().length;
            this.counts = counts.toArray();
            this.parameters = (TypeVariable[]) parameters.toArray(new TypeVariable[parameters.size()]);
        }

        public String toString() {
            StringBuilder buffer = new StringBuilder();
            buffer.append("[");
            int[] counts = this.counts;
            TypeVariable[] parameters = this.parameters;
            int p = 0;
            for (int count : counts) {
                int nn = p + count;
                while (p < nn) {
                    if (buffer.length() > 1) {
                        buffer.append(", ");
                    }
                    GenericDeclaration declaration = parameters[p].getGenericDeclaration();
                    if (declaration instanceof Class) {
                        buffer.append(((Class) declaration).getSimpleName());
                    } else {
                        buffer.append(declaration);
                    }
                    buffer.append(Typography.less);
                    buffer.append(parameters[p].getName());
                    buffer.append(Typography.greater);
                    p++;
                }
            }
            buffer.append("]");
            return buffer.toString();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Generics$GenericType.smali */
    public static class GenericType {
        GenericType[] arguments;
        Type type;

        public GenericType(Class fromClass, Class toClass, Type context) {
            initialize(fromClass, toClass, context);
        }

        private void initialize(Class fromClass, Class toClass, Type context) {
            if (context instanceof ParameterizedType) {
                ParameterizedType paramType = (ParameterizedType) context;
                Class rawType = (Class) paramType.getRawType();
                this.type = rawType;
                Type[] actualArgs = paramType.getActualTypeArguments();
                int n = actualArgs.length;
                this.arguments = new GenericType[n];
                for (int i = 0; i < n; i++) {
                    this.arguments[i] = new GenericType(fromClass, toClass, actualArgs[i]);
                }
                return;
            }
            if (context instanceof GenericArrayType) {
                int dimensions = 1;
                while (true) {
                    context = ((GenericArrayType) context).getGenericComponentType();
                    if (!(context instanceof GenericArrayType)) {
                        break;
                    } else {
                        dimensions++;
                    }
                }
                initialize(fromClass, toClass, context);
                Type componentType = GenericsUtil.resolveType(fromClass, toClass, context);
                if (componentType instanceof Class) {
                    if (dimensions == 1) {
                        this.type = Array.newInstance((Class<?>) componentType, 0).getClass();
                        return;
                    } else {
                        this.type = Array.newInstance((Class<?>) componentType, new int[dimensions]).getClass();
                        return;
                    }
                }
                return;
            }
            this.type = GenericsUtil.resolveType(fromClass, toClass, context);
        }

        public Class resolve(Generics generics) {
            Type type = this.type;
            return type instanceof Class ? (Class) type : generics.resolveTypeVariable((TypeVariable) type);
        }

        public Type getType() {
            return this.type;
        }

        public GenericType[] getTypeParameters() {
            return this.arguments;
        }

        public String toString() {
            StringBuilder buffer = new StringBuilder(32);
            boolean array = false;
            Type type = this.type;
            if (type instanceof Class) {
                Class c = (Class) type;
                array = c.isArray();
                buffer.append((array ? Util.getElementClass(c) : c).getSimpleName());
                if (this.arguments != null) {
                    buffer.append(Typography.less);
                    int n = this.arguments.length;
                    for (int i = 0; i < n; i++) {
                        if (i > 0) {
                            buffer.append(", ");
                        }
                        buffer.append(this.arguments[i].toString());
                    }
                    buffer.append(Typography.greater);
                }
            } else {
                buffer.append(type.toString());
            }
            if (array) {
                int n2 = Util.getDimensionCount((Class) this.type);
                for (int i2 = 0; i2 < n2; i2++) {
                    buffer.append("[]");
                }
            }
            return buffer.toString();
        }
    }
}
