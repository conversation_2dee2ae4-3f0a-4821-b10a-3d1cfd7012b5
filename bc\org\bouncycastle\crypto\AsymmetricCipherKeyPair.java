package bc.org.bouncycastle.crypto;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\AsymmetricCipherKeyPair.smali */
public class AsymmetricCipherKeyPair {
    private AsymmetricKeyParameter a;
    private AsymmetricKeyParameter b;

    public AsymmetricCipherKeyPair(AsymmetricKeyParameter asymmetricKeyParameter, AsymmetricKeyParameter asymmetricKeyParameter2) {
        this.a = asymmetricKeyParameter;
        this.b = asymmetricKeyParameter2;
    }

    public AsymmetricKeyParameter getPrivate() {
        return this.b;
    }

    public AsymmetricKeyParameter getPublic() {
        return this.a;
    }

    public AsymmetricCipherKeyPair(CipherParameters cipherParameters, CipherParameters cipherParameters2) {
        this.a = (AsymmetricKeyParameter) cipherParameters;
        this.b = (AsymmetricKeyParameter) cipherParameters2;
    }
}
