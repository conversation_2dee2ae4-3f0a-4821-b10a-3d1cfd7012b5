package o.ee;

import android.content.Context;
import android.view.MotionEvent;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\k.smali */
public abstract class k implements View.OnTouchListener {
    private static int c = 0;
    private static int e = 1;

    public abstract void b(Context context);

    @Override // android.view.View.OnTouchListener
    public boolean onTouch(View view, MotionEvent motionEvent) {
        int i = c;
        int i2 = ((i | Opcodes.LMUL) << 1) - (i ^ Opcodes.LMUL);
        e = i2 % 128;
        int i3 = i2 % 2;
        switch ((motionEvent.getFlags() & 1) == 0) {
            case true:
                return false;
            default:
                int i4 = c;
                int i5 = (i4 & Opcodes.LSHL) + (i4 | Opcodes.LSHL);
                e = i5 % 128;
                if (i5 % 2 == 0) {
                }
                switch (motionEvent.getAction() == 1) {
                    case true:
                        int i6 = e;
                        int i7 = ((i6 | 47) << 1) - (i6 ^ 47);
                        c = i7 % 128;
                        boolean z = i7 % 2 != 0;
                        b(view.getContext());
                        switch (z) {
                            case true:
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                            default:
                                int i8 = e;
                                int i9 = ((i8 | 3) << 1) - (i8 ^ 3);
                                c = i9 % 128;
                                int i10 = i9 % 2;
                                break;
                        }
                }
                int i11 = e;
                int i12 = ((i11 | 79) << 1) - (i11 ^ 79);
                c = i12 % 128;
                int i13 = i12 % 2;
                return true;
        }
    }
}
