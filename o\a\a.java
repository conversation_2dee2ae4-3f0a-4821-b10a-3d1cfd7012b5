package o.a;

import androidx.core.view.MotionEventCompat;
import androidx.core.view.ViewCompat;
import java.io.BufferedInputStream;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\a\a.smali */
public final class a extends FilterInputStream {
    private byte[] a;
    private byte[] b;
    private final int c;
    private d d;
    private byte[] e;
    private int f;
    private int g;
    private int h;
    private int[] i;
    private int j;

    public a(InputStream inputStream, int[] iArr, byte[] bArr, int i, boolean z, int i2) throws IOException {
        super(new BufferedInputStream(inputStream, 4096));
        this.g = Integer.MAX_VALUE;
        int min = Math.min(Math.max(i, 3), 16);
        this.c = min;
        this.a = new byte[8];
        byte[] bArr2 = new byte[8];
        this.e = bArr2;
        this.b = new byte[8];
        this.i = new int[2];
        this.f = 8;
        this.j = 8;
        this.h = i2;
        if (i2 == 2) {
            System.arraycopy(bArr, 0, bArr2, 0, 8);
        }
        this.d = new d(iArr, min, true, false);
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read() throws IOException {
        a();
        int i = this.f;
        if (i >= this.j) {
            return -1;
        }
        byte[] bArr = this.a;
        this.f = i + 1;
        return bArr[i] & 255;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read(byte[] bArr, int i, int i2) throws IOException {
        int i3 = i + i2;
        for (int i4 = i; i4 < i3; i4++) {
            a();
            int i5 = this.f;
            if (i5 >= this.j) {
                if (i4 == i) {
                    return -1;
                }
                return i2 - (i3 - i4);
            }
            byte[] bArr2 = this.a;
            this.f = i5 + 1;
            bArr[i4] = bArr2[i5];
        }
        return i2;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final long skip(long j) throws IOException {
        long j2 = 0;
        while (j2 < j && read() != -1) {
            j2++;
        }
        return j2;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int available() throws IOException {
        a();
        return this.j - this.f;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final boolean markSupported() {
        return false;
    }

    private void b() {
        if (this.h == 2) {
            byte[] bArr = this.a;
            System.arraycopy(bArr, 0, this.b, 0, bArr.length);
        }
        byte[] bArr2 = this.a;
        e.b(((bArr2[0] << 24) & ViewCompat.MEASURED_STATE_MASK) + ((bArr2[1] << Tnaf.POW_2_WIDTH) & 16711680) + ((bArr2[2] << 8) & MotionEventCompat.ACTION_POINTER_INDEX_MASK) + (bArr2[3] & 255), ((-16777216) & (bArr2[4] << 24)) + (16711680 & (bArr2[5] << Tnaf.POW_2_WIDTH)) + (65280 & (bArr2[6] << 8)) + (bArr2[7] & 255), false, this.c, this.d.b, this.d.c, this.i);
        int[] iArr = this.i;
        int i = iArr[0];
        int i2 = iArr[1];
        byte[] bArr3 = this.a;
        bArr3[0] = (byte) (i >> 24);
        bArr3[1] = (byte) (i >> 16);
        bArr3[2] = (byte) (i >> 8);
        bArr3[3] = (byte) i;
        bArr3[4] = (byte) (i2 >> 24);
        bArr3[5] = (byte) (i2 >> 16);
        bArr3[6] = (byte) (i2 >> 8);
        bArr3[7] = (byte) i2;
        if (this.h != 2) {
            return;
        }
        for (int i3 = 0; i3 < 8; i3++) {
            byte[] bArr4 = this.a;
            bArr4[i3] = (byte) (bArr4[i3] ^ this.e[i3]);
        }
        byte[] bArr5 = this.b;
        System.arraycopy(bArr5, 0, this.e, 0, bArr5.length);
    }

    private int a() throws IOException {
        if (this.g == Integer.MAX_VALUE) {
            this.g = ((FilterInputStream) this).in.read();
        }
        if (this.f == 8) {
            byte[] bArr = this.a;
            int i = this.g;
            bArr[0] = (byte) i;
            if (i < 0) {
                throw new IllegalStateException("unexpected block size");
            }
            int i2 = 1;
            do {
                int read = ((FilterInputStream) this).in.read(this.a, i2, 8 - i2);
                if (read <= 0) {
                    break;
                }
                i2 += read;
            } while (i2 < 8);
            if (i2 < 8) {
                throw new IllegalStateException("unexpected block size");
            }
            b();
            int read2 = ((FilterInputStream) this).in.read();
            this.g = read2;
            this.f = 0;
            this.j = read2 < 0 ? 8 - (this.a[7] & 255) : 8;
        }
        return this.j;
    }
}
