package kotlin;

/* compiled from: HashCode.kt */
@Metadata(d1 = {"\u0000\f\n\u0000\n\u0002\u0010\b\n\u0002\u0010\u0000\n\u0000\u001a\u000f\u0010\u0000\u001a\u00020\u0001*\u0004\u0018\u00010\u0002H\u0087\b¨\u0006\u0003"}, d2 = {"hashCode", "", "", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\HashCodeKt.smali */
public final class HashCodeKt {
    private static final int hashCode(Object $this$hashCode) {
        if ($this$hashCode != null) {
            return $this$hashCode.hashCode();
        }
        return 0;
    }
}
