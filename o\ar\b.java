package o.ar;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.proxy.AuthApiStatusCodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import o.a.h;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.y.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ar\b.smali */
public final class b extends d<a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int b;
    private static int d;
    o.el.d c;
    String e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ar\b$a.smali */
    public interface a {
        void a();

        void c(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        n();
        ViewConfiguration.getScrollFriction();
        Color.rgb(0, 0, 0);
        int i = d + 71;
        b = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{106, -103, -121, 51};
        $$e = 254;
    }

    private static void l(short s, short s2, short s3, Object[] objArr) {
        int i = s + Opcodes.DREM;
        byte[] bArr = $$d;
        int i2 = (s3 * 2) + 4;
        int i3 = 1 - (s2 * 4);
        byte[] bArr2 = new byte[i3];
        int i4 = -1;
        int i5 = i3 - 1;
        if (bArr == null) {
            int i6 = i5 + i2;
            i2++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
            i5 = i5;
            i = i6;
        }
        while (true) {
            int i7 = i4 + 1;
            bArr2[i7] = (byte) i;
            if (i7 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b2 = bArr[i2];
            int i8 = i2;
            int i9 = i;
            int i10 = i5;
            int i11 = b2 + i9;
            i2 = i8 + 1;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = i7;
            i5 = i10;
            i = i11;
        }
    }

    static void n() {
        a = new int[]{-538702573, -1931818717, 1884208575, -567212269, 963631180, 518163989, -2071996350, -1593674174, 852744476, -219926313, 823605898, 1075429044, 1182035191, 1521956739, -1018945186, 1972999296, -1835157666, -978683281};
    }

    static /* synthetic */ void d(b bVar, o.el.d dVar) {
        int i = b + Opcodes.DSUB;
        d = i % 128;
        char c2 = i % 2 != 0 ? 'Y' : '1';
        bVar.c(dVar);
        switch (c2) {
            case '1':
                break;
            default:
                int i2 = 95 / 0;
                break;
        }
        int i3 = b + 89;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = b + 99;
        d = i % 128;
        int i2 = i % 2;
        c m = m();
        int i3 = b + 89;
        d = i3 % 128;
        int i4 = i3 % 2;
        return m;
    }

    public b(Context context, a aVar, o.ei.c cVar) {
        super(context, aVar, cVar, e.l);
    }

    public final void a(o.el.d dVar, String str) throws WalletValidationException {
        int i = b + 23;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(new int[]{-472690580, 925129287, -639596903, -43663194, -121495978, -1656318349, -1859910801, -1618616500, 606721607, -178589854, -47453362, -1013427839, -58235748, -1209113358, 1212785171, -2101660646}, 'M' - AndroidCharacter.getMirror('0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(new int[]{443410628, 271973310, 2095383773, 1862079139, -582627865, 277155977, -1696789910, 1135856715, -1474696387, -1998717486, 1930823522, 389686124, -507853691, -1836721079, -552172693, -1162950474, 247257900, -498484782}, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 36, objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), dVar.n()));
        this.c = dVar;
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            k(new int[]{-472690580, 925129287, -1244868741, -1680549169, -47453362, -1013427839, -2047973962, 1047983066}, (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 13, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern(), "");
        }
        if (str.length() > 48) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr4 = new Object[1];
            k(new int[]{-472690580, 925129287, -1244868741, -1680549169, -47453362, -1013427839, -2047973962, 1047983066}, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 14, objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern(), "");
        }
        this.e = str;
        c();
        int i3 = d + 27;
        b = i3 % 128;
        int i4 = i3 % 2;
    }

    private c m() {
        c cVar = new c(this);
        int i = b + 33;
        d = i % 128;
        switch (i % 2 != 0 ? (char) 15 : (char) 25) {
            case 25:
                return cVar;
            default:
                int i2 = 11 / 0;
                return cVar;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i = b + 71;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k(new int[]{-472690580, 925129287, -639596903, -43663194, -121495978, -1656318349, -1859910801, -1618616500, 606721607, -178589854, -47453362, -1013427839, -58235748, -1209113358, 1212785171, -2101660646}, 29 - View.MeasureSpec.getSize(0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = b + 15;
        d = i3 % 128;
        switch (i3 % 2 != 0 ? 'L' : 'E') {
            case Base64.mimeLineLength /* 76 */:
                int i4 = 11 / 0;
                return intern;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ar\b$c.smali */
    static final class c extends o.y.c<b> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int b;
        private static int c;
        private static int d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            c = 1;
            d = 874635463;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(int r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 + 4
                int r7 = r7 * 3
                int r7 = 1 - r7
                int r8 = r8 * 2
                int r8 = 109 - r8
                byte[] r0 = o.ar.b.c.$$d
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L36
            L1a:
                r3 = r2
            L1b:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r6 = r6 + 1
                if (r3 != r7) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L36:
                int r7 = r7 + r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ar.b.c.B(int, byte, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{21, -84, -91, -118};
            $$e = 78;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = c + Opcodes.LSHL;
            b = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = b + 53;
            c = i % 128;
            int i2 = i % 2;
        }

        c(b bVar) {
            super(bVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = b + 41;
            c = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(View.MeasureSpec.makeMeasureSpec(0, 0) + 14, "\uffde\u0006\u000fￚ\t\t\u0005\u0002￼\ufffa\r\u0002\b\u0007\ufffa￼\r\u0002\u000f\ufffa\r\ufffe", 22 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), 287 - (ViewConfiguration.getTouchSlop() >> 8), false, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = b + 71;
            c = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(11 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), "￼\u0000\u0003\u0004\u0000\u0003￼\u0002�\u0004\u0002\uffff\u0004\ufffe\u0002\u0002\u0001\u0003�", 19 - View.getDefaultSize(0, 0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 236, false, objArr);
            o.cf.d dVar = new o.cf.d(context, 24, ((String) objArr[0]).intern());
            int i = b + 95;
            c = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return dVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(Color.green(0) + 6, "\u000e\u0003\t\b￣\ufffe\uffff\u0007\u0010ￛ\n\n\u0006\u0003�\ufffb", 16 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 286 - View.MeasureSpec.getSize(0), false, objArr);
            bVar.d(((String) objArr[0]).intern(), ((b) e()).c.n());
            Object[] objArr2 = new Object[1];
            w((ViewConfiguration.getPressedStateDuration() >> 16) + 3, "\u0005\b\u0006\u000f\ufff9\uffff\u0002\u0005￦�\u0004\uffff\t\t\ufffb\ufff9", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 16, 291 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), true, objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((b) e()).c.r());
            Object[] objArr3 = new Object[1];
            w(12 - TextUtils.getCapsMode("", 0, 0), "\r\u0002\u000f\ufffa\r\u0002\b\u0007ￜ\b�\ufffe\ufffa￼", 14 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 286, false, objArr3);
            bVar.d(((String) objArr3[0]).intern(), ((b) e()).e);
            int i = c + 91;
            b = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        @Override // o.y.c
        public final j n() {
            int i = b + 97;
            int i2 = i % 128;
            c = i2;
            Object obj = null;
            switch (i % 2 != 0) {
                case true:
                    int i3 = i2 + 29;
                    b = i3 % 128;
                    int i4 = i3 % 2;
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b;
            int i2 = i + 53;
            c = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 23;
            c = i4 % 128;
            switch (i4 % 2 == 0 ? ')' : '\r') {
                case '\r':
                    return null;
                default:
                    int i5 = 97 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = c + 31;
            b = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case AuthApiStatusCodes.AUTH_API_ACCESS_FORBIDDEN /* 3001 */:
                    return o.bb.a.av;
                case AuthApiStatusCodes.AUTH_API_CLIENT_ERROR /* 3002 */:
                    return o.bb.a.aw;
                case AuthApiStatusCodes.AUTH_API_SERVER_ERROR /* 3003 */:
                    o.bb.a aVar = o.bb.a.au;
                    int i4 = c + Opcodes.LSHL;
                    b = i4 % 128;
                    int i5 = i4 % 2;
                    return aVar;
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    return super.c(i);
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = b + 55;
            c = i % 128;
            int i2 = i % 2;
            f().a().i().a(((b) e()).c);
            f().d(g());
            int i3 = c + 91;
            b = i3 % 128;
            switch (i3 % 2 != 0 ? 'T' : (char) 31) {
                case Opcodes.BASTORE /* 84 */:
                    int i4 = 7 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + Opcodes.LMUL;
            b = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass3.a[h().d().ordinal()]) {
                case 1:
                case 2:
                    b.d((b) e(), ((b) e()).c);
                    return;
                case 3:
                    f().c(g(), ((b) e()).c.u());
                    int i3 = c + 71;
                    b = i3 % 128;
                    switch (i3 % 2 != 0 ? '8' : (char) 7) {
                        case '8':
                            throw null;
                        default:
                            return;
                    }
                case 4:
                    f().e(g(), ((b) e()).c.u());
                    return;
                default:
                    super.t();
                    int i4 = c + 57;
                    b = i4 % 128;
                    int i5 = i4 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = c + Opcodes.LSUB;
            b = i % 128;
            switch (i % 2 == 0) {
                case false:
                    ((b) e()).j().a();
                    int i2 = 94 / 0;
                    break;
                default:
                    ((b) e()).j().a();
                    break;
            }
            int i3 = b + 63;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = b + 37;
            c = i % 128;
            int i2 = i % 2;
            ((b) e()).j().c(dVar);
            int i3 = c + 25;
            b = i3 % 128;
            int i4 = i3 % 2;
        }

        private static void w(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
            char[] cArr;
            if (str != null) {
                int i4 = $11 + Opcodes.DSUB;
                $10 = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str.toCharArray();
                        int i5 = 78 / 0;
                        break;
                }
            } else {
                cArr = str;
            }
            char[] cArr2 = cArr;
            h hVar = new h();
            char[] cArr3 = new char[i2];
            hVar.a = 0;
            while (hVar.a < i2) {
                int i6 = $11 + 77;
                $10 = i6 % 128;
                int i7 = i6 % 2;
                hVar.b = cArr2[hVar.a];
                cArr3[hVar.a] = (char) (i3 + hVar.b);
                int i8 = hVar.a;
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr3[i8]), Integer.valueOf(d)};
                    Object obj = o.e.a.s.get(2038615114);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 11, (char) View.getDefaultSize(0, 0), 460 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)));
                        byte b2 = (byte) (-1);
                        byte b3 = (byte) (b2 + 1);
                        Object[] objArr3 = new Object[1];
                        B(b2, b3, (byte) (b3 + 1), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                        o.e.a.s.put(2038615114, obj);
                    }
                    cArr3[i8] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    try {
                        Object[] objArr4 = {hVar, hVar};
                        Object obj2 = o.e.a.s.get(-1412673904);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(11 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) (ViewConfiguration.getTouchSlop() >> 8), KeyEvent.getDeadChar(0, 0) + 313);
                            byte b4 = (byte) (-1);
                            byte b5 = (byte) (b4 + 1);
                            Object[] objArr5 = new Object[1];
                            B(b4, b5, b5, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj2);
                        }
                        ((Method) obj2).invoke(null, objArr4);
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            if (i > 0) {
                hVar.c = i;
                char[] cArr4 = new char[i2];
                System.arraycopy(cArr3, 0, cArr4, 0, i2);
                System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
            }
            switch (!z) {
                case false:
                    int i9 = $11 + 47;
                    $10 = i9 % 128;
                    int i10 = i9 % 2;
                    char[] cArr5 = new char[i2];
                    hVar.a = 0;
                    while (true) {
                        switch (hVar.a >= i2) {
                            case true:
                                cArr3 = cArr5;
                                break;
                            default:
                                cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                                try {
                                    Object[] objArr6 = {hVar, hVar};
                                    Object obj3 = o.e.a.s.get(-1412673904);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(11 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), (ViewConfiguration.getEdgeSlop() >> 16) + 313);
                                        byte b6 = (byte) (-1);
                                        byte b7 = (byte) (b6 + 1);
                                        Object[] objArr7 = new Object[1];
                                        B(b6, b7, b7, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                        o.e.a.s.put(-1412673904, obj3);
                                    }
                                    ((Method) obj3).invoke(null, objArr6);
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                    }
            }
            objArr[0] = new String(cArr3);
        }
    }

    /* renamed from: o.ar.b$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ar\b$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int e;

        static {
            e = 0;
            b = 1;
            int[] iArr = new int[o.bb.a.values().length];
            a = iArr;
            try {
                iArr[o.bb.a.au.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[o.bb.a.aw.ordinal()] = 2;
                int i = e;
                int i2 = (i ^ Opcodes.LSHR) + ((i & Opcodes.LSHR) << 1);
                b = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[o.bb.a.ay.ordinal()] = 3;
                int i4 = b;
                int i5 = (i4 & 37) + (i4 | 37);
                e = i5 % 128;
                if (i5 % 2 == 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[o.bb.a.az.ordinal()] = 4;
                int i6 = b;
                int i7 = (i6 ^ 27) + ((i6 & 27) << 1);
                e = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int[] r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 860
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ar.b.k(int[], int, java.lang.Object[]):void");
    }
}
