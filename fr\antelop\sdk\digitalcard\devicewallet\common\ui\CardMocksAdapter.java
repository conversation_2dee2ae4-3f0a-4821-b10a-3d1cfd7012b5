package fr.antelop.sdk.digitalcard.devicewallet.common.ui;

import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import java.util.List;
import o.ep.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\CardMocksAdapter.smali */
public final class CardMocksAdapter extends FragmentStateAdapter {
    private final CardMocksAdapterCallback cardMocksAdapterCallback;
    private final List<e> tokens;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\CardMocksAdapter$CardMocksAdapterCallback.smali */
    public interface CardMocksAdapterCallback {
        void deleteToken(e eVar);
    }

    public CardMocksAdapter(Fragment fragment, List<e> list, CardMocksAdapterCallback cardMocksAdapterCallback) {
        super(fragment);
        this.tokens = list;
        this.cardMocksAdapterCallback = cardMocksAdapterCallback;
    }

    @Override // androidx.recyclerview.widget.RecyclerView.Adapter
    public final int getItemCount() {
        return this.tokens.size();
    }

    @Override // androidx.viewpager2.adapter.FragmentStateAdapter
    public final Fragment createFragment(int i) {
        return new TokenMockViewFragment(this.tokens.get(i), this.cardMocksAdapterCallback);
    }
}
