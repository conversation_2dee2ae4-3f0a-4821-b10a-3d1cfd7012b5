package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Generics;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.HashMap;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\MapSerializer.smali */
public class MapSerializer<T extends Map> extends Serializer<T> {
    private Class keyClass;
    private Serializer keySerializer;
    private Class valueClass;
    private Serializer valueSerializer;
    private boolean keysCanBeNull = true;
    private boolean valuesCanBeNull = true;

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\MapSerializer$BindMap.smali */
    public @interface BindMap {
        Class keyClass() default Object.class;

        Class<? extends Serializer> keySerializer() default Serializer.class;

        Class<? extends SerializerFactory> keySerializerFactory() default SerializerFactory.class;

        boolean keysCanBeNull() default true;

        Class valueClass() default Object.class;

        Class<? extends Serializer> valueSerializer() default Serializer.class;

        Class<? extends SerializerFactory> valueSerializerFactory() default SerializerFactory.class;

        boolean valuesCanBeNull() default true;
    }

    public MapSerializer() {
        setAcceptsNull(true);
    }

    public void setKeysCanBeNull(boolean keysCanBeNull) {
        this.keysCanBeNull = keysCanBeNull;
    }

    public void setKeyClass(Class keyClass) {
        this.keyClass = keyClass;
    }

    public Class getKeyClass() {
        return this.keyClass;
    }

    public void setKeyClass(Class keyClass, Serializer keySerializer) {
        this.keyClass = keyClass;
        this.keySerializer = keySerializer;
    }

    public void setKeySerializer(Serializer keySerializer) {
        this.keySerializer = keySerializer;
    }

    public Serializer getKeySerializer() {
        return this.keySerializer;
    }

    public void setValueClass(Class valueClass) {
        this.valueClass = valueClass;
    }

    public Class getValueClass() {
        return this.valueClass;
    }

    public void setValueClass(Class valueClass, Serializer valueSerializer) {
        this.valueClass = valueClass;
        this.valueSerializer = valueSerializer;
    }

    public void setValueSerializer(Serializer valueSerializer) {
        this.valueSerializer = valueSerializer;
    }

    public Serializer getValueSerializer() {
        return this.valueSerializer;
    }

    public void setValuesCanBeNull(boolean valuesCanBeNull) {
        this.valuesCanBeNull = valuesCanBeNull;
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T map) {
        Class valueType;
        Class keyType;
        if (map == null) {
            output.writeByte(0);
            return;
        }
        int size = map.size();
        if (size != 0) {
            output.writeVarInt(size + 1, true);
            writeHeader(kryo, output, map);
            Serializer keySerializer = this.keySerializer;
            Serializer valueSerializer = this.valueSerializer;
            Generics.GenericType[] genericTypes = kryo.getGenerics().nextGenericTypes();
            if (genericTypes != null) {
                if (keySerializer == null && (keyType = genericTypes[0].resolve(kryo.getGenerics())) != null && kryo.isFinal(keyType)) {
                    keySerializer = kryo.getSerializer(keyType);
                }
                if (valueSerializer == null && (valueType = genericTypes[1].resolve(kryo.getGenerics())) != null && kryo.isFinal(valueType)) {
                    valueSerializer = kryo.getSerializer(valueType);
                }
            }
            for (Map.Entry entry : map.entrySet()) {
                if (genericTypes != null) {
                    kryo.getGenerics().pushGenericType(genericTypes[0]);
                }
                if (keySerializer != null) {
                    if (this.keysCanBeNull) {
                        kryo.writeObjectOrNull(output, entry.getKey(), keySerializer);
                    } else {
                        kryo.writeObject(output, entry.getKey(), keySerializer);
                    }
                } else {
                    kryo.writeClassAndObject(output, entry.getKey());
                }
                if (genericTypes != null) {
                    kryo.getGenerics().popGenericType();
                }
                if (valueSerializer != null) {
                    if (this.valuesCanBeNull) {
                        kryo.writeObjectOrNull(output, entry.getValue(), valueSerializer);
                    } else {
                        kryo.writeObject(output, entry.getValue(), valueSerializer);
                    }
                } else {
                    kryo.writeClassAndObject(output, entry.getValue());
                }
            }
            kryo.getGenerics().popGenericType();
            return;
        }
        output.writeByte(1);
        writeHeader(kryo, output, map);
    }

    protected void writeHeader(Kryo kryo, Output output, T map) {
    }

    protected T create(Kryo kryo, Input input, Class<? extends T> type, int size) {
        if (type == HashMap.class) {
            if (size < 3) {
                size++;
            } else if (size < 1073741824) {
                size = (int) ((size / 0.75f) + 1.0f);
            }
            return new HashMap(size);
        }
        return (T) kryo.newInstance(type);
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        Object key;
        Object value;
        Class genericClass;
        Class genericClass2;
        int length = input.readVarInt(true);
        if (length == 0) {
            return null;
        }
        int length2 = length - 1;
        T map = create(kryo, input, type, length2);
        kryo.reference(map);
        if (length2 == 0) {
            return map;
        }
        Class keyClass = this.keyClass;
        Class valueClass = this.valueClass;
        Serializer keySerializer = this.keySerializer;
        Serializer valueSerializer = this.valueSerializer;
        Generics.GenericType[] genericTypes = kryo.getGenerics().nextGenericTypes();
        if (genericTypes != null) {
            if (keySerializer == null && (genericClass2 = genericTypes[0].resolve(kryo.getGenerics())) != null && kryo.isFinal(genericClass2)) {
                keySerializer = kryo.getSerializer(genericClass2);
                keyClass = genericClass2;
            }
            if (valueSerializer == null && (genericClass = genericTypes[1].resolve(kryo.getGenerics())) != null && kryo.isFinal(genericClass)) {
                valueSerializer = kryo.getSerializer(genericClass);
                valueClass = genericClass;
            }
        }
        for (int i = 0; i < length2; i++) {
            if (genericTypes != null) {
                kryo.getGenerics().pushGenericType(genericTypes[0]);
            }
            if (keySerializer != null) {
                if (this.keysCanBeNull) {
                    key = kryo.readObjectOrNull(input, keyClass, keySerializer);
                } else {
                    key = kryo.readObject(input, keyClass, keySerializer);
                }
            } else {
                key = kryo.readClassAndObject(input);
            }
            if (genericTypes != null) {
                kryo.getGenerics().popGenericType();
            }
            if (valueSerializer != null) {
                if (this.valuesCanBeNull) {
                    value = kryo.readObjectOrNull(input, valueClass, valueSerializer);
                } else {
                    value = kryo.readObject(input, valueClass, valueSerializer);
                }
            } else {
                value = kryo.readClassAndObject(input);
            }
            map.put(key, value);
        }
        kryo.getGenerics().popGenericType();
        return map;
    }

    protected T createCopy(Kryo kryo, T original) {
        return (T) kryo.newInstance(original.getClass());
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T copy(Kryo kryo, T original) {
        T copy = createCopy(kryo, original);
        for (Map.Entry entry : original.entrySet()) {
            copy.put(kryo.copy(entry.getKey()), kryo.copy(entry.getValue()));
        }
        return copy;
    }
}
