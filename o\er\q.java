package o.er;

import android.graphics.Color;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\q.smali */
public final class q extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static char e;
    private static int h;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        d();
        ViewConfiguration.getZoomControlsTimeout();
        Color.rgb(0, 0, 0);
        int i = j + Opcodes.LSUB;
        h = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        e = (char) 17957;
        a = 182430242;
        b = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r8, byte r9, int r10, java.lang.Object[] r11) {
        /*
            byte[] r0 = o.er.q.$$a
            int r8 = r8 + 99
            int r10 = r10 * 3
            int r10 = r10 + 4
            int r9 = r9 * 2
            int r9 = r9 + 1
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r9
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r11
            r11 = r10
            goto L33
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L28
            java.lang.String r8 = new java.lang.String
            r8.<init>(r1, r2)
            r11[r2] = r8
            return
        L28:
            r3 = r0[r10]
            r6 = r9
            r9 = r8
            r8 = r6
            r7 = r11
            r11 = r10
            r10 = r3
            r3 = r1
            r1 = r0
            r0 = r7
        L33:
            int r10 = -r10
            int r9 = r9 + r10
            int r10 = r11 + 1
            r11 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.q.g(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{52, -61, 40, Tnaf.POW_2_WIDTH};
        $$b = 13;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = h + 89;
        j = i % 128;
        int i2 = i % 2;
        boolean b2 = super.b();
        int i3 = h + 31;
        j = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    public q(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        int i = j + Opcodes.LREM;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                a[] aVarArr = new a[1];
                aVarArr[1] = this.d.i();
                return aVarArr;
            default:
                return new a[]{this.d.i()};
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0194  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x01d8  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(android.content.Context r17, final fr.antelop.sdk.util.OperationCallback<java.util.List<fr.antelop.sdk.digitalcard.Token>> r18) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 680
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.q.a(android.content.Context, fr.antelop.sdk.util.OperationCallback):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r18, java.lang.String r19, char r20, java.lang.String r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 672
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.q.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
