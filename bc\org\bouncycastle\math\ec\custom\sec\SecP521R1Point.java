package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP521R1Point.smali */
public class SecP521R1Point extends ECPoint.AbstractFp {
    SecP521R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP521R1FieldElement secP521R1FieldElement = (SecP521R1FieldElement) this.b;
        SecP521R1FieldElement secP521R1FieldElement2 = (SecP521R1FieldElement) this.c;
        SecP521R1FieldElement secP521R1FieldElement3 = (SecP521R1FieldElement) eCPoint.getXCoord();
        SecP521R1FieldElement secP521R1FieldElement4 = (SecP521R1FieldElement) eCPoint.getYCoord();
        SecP521R1FieldElement secP521R1FieldElement5 = (SecP521R1FieldElement) this.d[0];
        SecP521R1FieldElement secP521R1FieldElement6 = (SecP521R1FieldElement) eCPoint.getZCoord(0);
        int[] a = c6.a(33);
        int[] a2 = c6.a(17);
        int[] a3 = c6.a(17);
        int[] a4 = c6.a(17);
        int[] a5 = c6.a(17);
        boolean isOne = secP521R1FieldElement5.isOne();
        if (isOne) {
            iArr = secP521R1FieldElement3.a;
            iArr2 = secP521R1FieldElement4.a;
        } else {
            SecP521R1Field.square(secP521R1FieldElement5.a, a4, a);
            SecP521R1Field.multiply(a4, secP521R1FieldElement3.a, a3, a);
            SecP521R1Field.multiply(a4, secP521R1FieldElement5.a, a4, a);
            SecP521R1Field.multiply(a4, secP521R1FieldElement4.a, a4, a);
            iArr = a3;
            iArr2 = a4;
        }
        boolean isOne2 = secP521R1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP521R1FieldElement.a;
            iArr4 = secP521R1FieldElement2.a;
        } else {
            SecP521R1Field.square(secP521R1FieldElement6.a, a5, a);
            SecP521R1Field.multiply(a5, secP521R1FieldElement.a, a2, a);
            SecP521R1Field.multiply(a5, secP521R1FieldElement6.a, a5, a);
            SecP521R1Field.multiply(a5, secP521R1FieldElement2.a, a5, a);
            iArr3 = a2;
            iArr4 = a5;
        }
        int[] a6 = c6.a(17);
        SecP521R1Field.subtract(iArr3, iArr, a6);
        SecP521R1Field.subtract(iArr4, iArr2, a3);
        if (c6.e(17, a6)) {
            return c6.e(17, a3) ? twice() : curve.getInfinity();
        }
        SecP521R1Field.square(a6, a4, a);
        int[] a7 = c6.a(17);
        SecP521R1Field.multiply(a4, a6, a7, a);
        SecP521R1Field.multiply(a4, iArr3, a4, a);
        SecP521R1Field.multiply(iArr4, a7, a2, a);
        SecP521R1FieldElement secP521R1FieldElement7 = new SecP521R1FieldElement(a5);
        SecP521R1Field.square(a3, secP521R1FieldElement7.a, a);
        int[] iArr5 = secP521R1FieldElement7.a;
        SecP521R1Field.add(iArr5, a7, iArr5);
        int[] iArr6 = secP521R1FieldElement7.a;
        SecP521R1Field.subtract(iArr6, a4, iArr6);
        int[] iArr7 = secP521R1FieldElement7.a;
        SecP521R1Field.subtract(iArr7, a4, iArr7);
        SecP521R1FieldElement secP521R1FieldElement8 = new SecP521R1FieldElement(a7);
        SecP521R1Field.subtract(a4, secP521R1FieldElement7.a, secP521R1FieldElement8.a);
        SecP521R1Field.multiply(secP521R1FieldElement8.a, a3, a3, a);
        SecP521R1Field.subtract(a3, a2, secP521R1FieldElement8.a);
        SecP521R1FieldElement secP521R1FieldElement9 = new SecP521R1FieldElement(a6);
        if (!isOne) {
            int[] iArr8 = secP521R1FieldElement9.a;
            SecP521R1Field.multiply(iArr8, secP521R1FieldElement5.a, iArr8, a);
        }
        if (!isOne2) {
            int[] iArr9 = secP521R1FieldElement9.a;
            SecP521R1Field.multiply(iArr9, secP521R1FieldElement6.a, iArr9, a);
        }
        return new SecP521R1Point(curve, secP521R1FieldElement7, secP521R1FieldElement8, new ECFieldElement[]{secP521R1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP521R1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP521R1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP521R1FieldElement secP521R1FieldElement = (SecP521R1FieldElement) this.c;
        if (secP521R1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP521R1FieldElement secP521R1FieldElement2 = (SecP521R1FieldElement) this.b;
        SecP521R1FieldElement secP521R1FieldElement3 = (SecP521R1FieldElement) this.d[0];
        int[] a = c6.a(33);
        int[] a2 = c6.a(17);
        int[] a3 = c6.a(17);
        int[] a4 = c6.a(17);
        SecP521R1Field.square(secP521R1FieldElement.a, a4, a);
        int[] a5 = c6.a(17);
        SecP521R1Field.square(a4, a5, a);
        boolean isOne = secP521R1FieldElement3.isOne();
        int[] iArr = secP521R1FieldElement3.a;
        if (!isOne) {
            SecP521R1Field.square(iArr, a3, a);
            iArr = a3;
        }
        SecP521R1Field.subtract(secP521R1FieldElement2.a, iArr, a2);
        SecP521R1Field.add(secP521R1FieldElement2.a, iArr, a3);
        SecP521R1Field.multiply(a3, a2, a3, a);
        c6.b(17, a3, a3, a3);
        SecP521R1Field.reduce23(a3);
        SecP521R1Field.multiply(a4, secP521R1FieldElement2.a, a4, a);
        c6.c(17, a4, 2, 0);
        SecP521R1Field.reduce23(a4);
        c6.a(17, a5, 3, 0, a2);
        SecP521R1Field.reduce23(a2);
        SecP521R1FieldElement secP521R1FieldElement4 = new SecP521R1FieldElement(a5);
        SecP521R1Field.square(a3, secP521R1FieldElement4.a, a);
        int[] iArr2 = secP521R1FieldElement4.a;
        SecP521R1Field.subtract(iArr2, a4, iArr2);
        int[] iArr3 = secP521R1FieldElement4.a;
        SecP521R1Field.subtract(iArr3, a4, iArr3);
        SecP521R1FieldElement secP521R1FieldElement5 = new SecP521R1FieldElement(a4);
        SecP521R1Field.subtract(a4, secP521R1FieldElement4.a, secP521R1FieldElement5.a);
        int[] iArr4 = secP521R1FieldElement5.a;
        SecP521R1Field.multiply(iArr4, a3, iArr4, a);
        int[] iArr5 = secP521R1FieldElement5.a;
        SecP521R1Field.subtract(iArr5, a2, iArr5);
        SecP521R1FieldElement secP521R1FieldElement6 = new SecP521R1FieldElement(a3);
        SecP521R1Field.twice(secP521R1FieldElement.a, secP521R1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP521R1FieldElement6.a;
            SecP521R1Field.multiply(iArr6, secP521R1FieldElement3.a, iArr6, a);
        }
        return new SecP521R1Point(curve, secP521R1FieldElement4, secP521R1FieldElement5, new ECFieldElement[]{secP521R1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP521R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
