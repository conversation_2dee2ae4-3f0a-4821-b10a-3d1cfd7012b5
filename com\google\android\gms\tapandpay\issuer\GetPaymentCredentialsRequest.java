package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\GetPaymentCredentialsRequest.smali */
public class GetPaymentCredentialsRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GetPaymentCredentialsRequest> CREATOR = new zzc();
    final String zza;
    final String zzb;
    final String zzc;
    final String zzd;
    final boolean zze;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\GetPaymentCredentialsRequest$Builder.smali */
    public static class Builder {
        private String zza;
        private String zzb;
        private String zzc;
        private String zzd;
        private boolean zze;

        public GetPaymentCredentialsRequest build() {
            return new GetPaymentCredentialsRequest(this.zza, this.zzb, this.zzc, this.zzd, this.zze);
        }

        public Builder setGoogleOpaquePaymentCardRequested(boolean z) {
            this.zze = z;
            return this;
        }

        public Builder setServerSessionId(String str) {
            this.zzd = str;
            return this;
        }

        public Builder setStableHardwareId(String str) {
            this.zza = str;
            return this;
        }

        public Builder setTokenRequestorId(String str) {
            this.zzc = str;
            return this;
        }

        public Builder setWalletId(String str) {
            this.zzb = str;
            return this;
        }
    }

    GetPaymentCredentialsRequest(String str, String str2, String str3, String str4, boolean z) {
        this.zza = str;
        this.zzb = str2;
        this.zzc = str3;
        this.zzd = str4;
        this.zze = z;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, this.zza, false);
        SafeParcelWriter.writeString(dest, 2, this.zzb, false);
        SafeParcelWriter.writeString(dest, 3, this.zzc, false);
        SafeParcelWriter.writeString(dest, 4, this.zzd, false);
        SafeParcelWriter.writeBoolean(dest, 5, this.zze);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
