package o.n;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.dz.a;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\a.smali */
public class a extends o.dz.a {
    boolean callbackFired;
    private InterfaceC0045a keypadCallback;
    private ByteBuffer pinBuffer;
    private e pinEntryBullet;
    private int pinIdx;
    private d theming;
    private static int b = 0;
    private static int a = 1;

    /* renamed from: o.n.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\a$a.smali */
    public interface InterfaceC0045a {
        void onExtraButtonPressed();

        void onKeyPressed();

        void onPasscodeEntryDone(byte[] bArr);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\a$d.smali */
    public static class d extends a.d {
        final int bulletDrawable;
        final int bulletSelectedColor;
        final int bulletUnselectedColor;
        final int pinNumber;

        public d(int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8, int i9, boolean z) {
            super(i5, i6, i7, z, i8, i9);
            this.pinNumber = i;
            this.bulletDrawable = i2;
            this.bulletSelectedColor = i3;
            this.bulletUnselectedColor = i4;
        }
    }

    public a(Context context) {
        super(context);
        this.pinIdx = 0;
        this.callbackFired = false;
    }

    public a(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.pinIdx = 0;
        this.callbackFired = false;
    }

    public a(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.pinIdx = 0;
        this.callbackFired = false;
    }

    public final void initializeView(final InterfaceC0045a interfaceC0045a, d dVar) {
        this.theming = dVar;
        this.keypadCallback = interfaceC0045a;
        this.pinBuffer = ByteBuffer.allocate(dVar.pinNumber);
        super.initializeView(new a.InterfaceC0038a() { // from class: o.n.a.2
            private static int a = 0;
            private static int d = 1;

            @Override // o.dz.a.InterfaceC0038a
            public final void c(char c) {
                int i = a;
                int i2 = (i & 79) + (i | 79);
                d = i2 % 128;
                int i3 = i2 % 2;
                a.this.onDigitPressed(c);
                int i4 = a;
                int i5 = (i4 ^ 57) + ((i4 & 57) << 1);
                d = i5 % 128;
                switch (i5 % 2 == 0 ? ')' : (char) 20) {
                    case 20:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.dz.a.InterfaceC0038a
            public final void c() {
                int i = d + Opcodes.LSHR;
                a = i % 128;
                int i2 = i % 2;
                a.this.onDigitRemoved();
                int i3 = a;
                int i4 = ((i3 | 39) << 1) - (i3 ^ 39);
                d = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return;
                    default:
                        int i5 = 61 / 0;
                        return;
                }
            }

            @Override // o.dz.a.InterfaceC0038a
            public final void b() {
                int i = d;
                int i2 = (i ^ 15) + ((i & 15) << 1);
                a = i2 % 128;
                int i3 = i2 % 2;
                interfaceC0045a.onExtraButtonPressed();
                int i4 = d;
                int i5 = (i4 ^ 91) + ((i4 & 91) << 1);
                a = i5 % 128;
                switch (i5 % 2 != 0 ? (char) 22 : (char) 29) {
                    case 22:
                        int i6 = 13 / 0;
                        return;
                    default:
                        return;
                }
            }
        }, dVar);
        int i = a + 7;
        b = i % 128;
        int i2 = i % 2;
    }

    void onDigitPressed(char c) {
        switch (this.pinIdx < this.theming.pinNumber ? 'E' : (char) 5) {
            case 'E':
                int i = a + 83;
                b = i % 128;
                switch (i % 2 != 0 ? (char) 16 : (char) 14) {
                    case 16:
                        this.pinBuffer.put((byte) c);
                        this.pinEntryBullet.setSelected(this.pinBuffer.position() >>> 0);
                        this.pinIdx >>>= 1;
                        break;
                    default:
                        this.pinBuffer.put((byte) c);
                        e eVar = this.pinEntryBullet;
                        int position = this.pinBuffer.position();
                        eVar.setSelected((position & (-1)) + (position | (-1)));
                        int i2 = this.pinIdx;
                        this.pinIdx = (i2 ^ 1) + ((i2 & 1) << 1);
                        break;
                }
                this.callbackFired = false;
                break;
        }
        switch (this.pinBuffer.position() == this.theming.pinNumber) {
            case false:
                InterfaceC0045a interfaceC0045a = this.keypadCallback;
                if (interfaceC0045a != null) {
                    int i3 = a;
                    int i4 = ((i3 | 109) << 1) - (i3 ^ 109);
                    b = i4 % 128;
                    switch (i4 % 2 != 0 ? (char) 28 : (char) 6) {
                        case 6:
                            interfaceC0045a.onKeyPressed();
                            break;
                        default:
                            interfaceC0045a.onKeyPressed();
                            throw null;
                    }
                }
                break;
            default:
                int i5 = a;
                int i6 = (i5 & 91) + (i5 | 91);
                b = i6 % 128;
                if (i6 % 2 != 0) {
                }
                InterfaceC0045a interfaceC0045a2 = this.keypadCallback;
                switch (interfaceC0045a2 == null) {
                    case true:
                        break;
                    default:
                        int i7 = ((i5 | 97) << 1) - (i5 ^ 97);
                        b = i7 % 128;
                        int i8 = i7 % 2;
                        switch (!this.callbackFired ? '1' : ')') {
                            case ')':
                                break;
                            default:
                                int i9 = ((i5 | 7) << 1) - (i5 ^ 7);
                                b = i9 % 128;
                                switch (i9 % 2 == 0) {
                                    case false:
                                        interfaceC0045a2.onPasscodeEntryDone((byte[]) this.pinBuffer.array().clone());
                                        break;
                                    default:
                                        interfaceC0045a2.onPasscodeEntryDone((byte[]) this.pinBuffer.array().clone());
                                        break;
                                }
                                this.callbackFired = true;
                                return;
                        }
                }
        }
        int i10 = (a + Opcodes.IREM) - 1;
        b = i10 % 128;
        int i11 = i10 % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:17:0x009b. Please report as an issue. */
    public void onDigitRemoved() {
        int i = a;
        int i2 = (i ^ 85) + ((i & 85) << 1);
        b = i2 % 128;
        switch (i2 % 2 != 0 ? '=' : 'c') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                int i3 = 50 / 0;
                switch (this.pinBuffer.position() > 0 ? (char) 26 : (char) 24) {
                    case 24:
                        return;
                }
            default:
                switch (this.pinBuffer.position() > 0 ? (char) 22 : (char) 6) {
                    case 22:
                        break;
                    default:
                        return;
                }
        }
        int i4 = (a + Opcodes.ISHL) - 1;
        b = i4 % 128;
        int i5 = i4 % 2;
        this.pinEntryBullet.setNotSelected((this.pinBuffer.position() - 0) - 1);
        ByteBuffer byteBuffer = this.pinBuffer;
        int position = byteBuffer.position();
        byteBuffer.position((position & (-1)) + (position | (-1)));
        int i6 = this.pinIdx;
        this.pinIdx = (i6 & (-1)) + (i6 | (-1));
        InterfaceC0045a interfaceC0045a = this.keypadCallback;
        switch (interfaceC0045a == null) {
            case true:
                return;
            default:
                int i7 = a + 75;
                b = i7 % 128;
                int i8 = i7 % 2;
                interfaceC0045a.onKeyPressed();
                int i9 = b;
                int i10 = ((i9 | 85) << 1) - (i9 ^ 85);
                a = i10 % 128;
                switch (i10 % 2 == 0 ? '7' : ')') {
                }
                return;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0040  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void resetPasscode() {
        /*
            r3 = this;
            int r0 = o.n.a.a
            int r0 = r0 + 107
            int r1 = r0 % 128
            o.n.a.b = r1
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            if (r0 == 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 0: goto L25;
                default: goto L14;
            }
        L14:
            o.n.a$d r0 = r3.theming
            int r0 = r0.pinNumber
            java.nio.ByteBuffer r0 = java.nio.ByteBuffer.allocate(r0)
            r3.pinBuffer = r0
            r3.pinIdx = r2
            o.n.a$e r0 = r3.pinEntryBullet
            if (r0 == 0) goto L3b
            goto L3c
        L25:
            o.n.a$d r0 = r3.theming
            int r0 = r0.pinNumber
            java.nio.ByteBuffer r0 = java.nio.ByteBuffer.allocate(r0)
            r3.pinBuffer = r0
            r3.pinIdx = r1
            o.n.a$e r0 = r3.pinEntryBullet
            if (r0 == 0) goto L36
            goto L37
        L36:
            r1 = r2
        L37:
            switch(r1) {
                case 1: goto L50;
                default: goto L3a;
            }
        L3a:
            goto L40
        L3b:
            r1 = r2
        L3c:
            switch(r1) {
                case 0: goto L3a;
                default: goto L3f;
            }
        L3f:
            goto L50
        L40:
            o.n.a$e r0 = r3.pinEntryBullet
            r0.b()
            int r0 = o.n.a.b
            int r0 = r0 + 3
            int r1 = r0 % 128
            o.n.a.a = r1
            int r0 = r0 % 2
        L50:
            int r0 = o.n.a.b
            int r0 = r0 + 92
            int r0 = r0 - r2
            int r1 = r0 % 128
            o.n.a.a = r1
            int r0 = r0 % 2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.n.a.resetPasscode():void");
    }

    @Override // o.dz.a
    public void customizeView() {
        int e2 = o.e(getContext(), 1.0f);
        this.pinEntryBullet = new e(getContext(), this.theming);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(-1, -2);
        int i = e2 << 3;
        ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = i;
        this.pinEntryBullet.setLayoutParams(layoutParams);
        addView(this.pinEntryBullet);
        View linearLayout = new LinearLayout(getContext());
        linearLayout.setBackgroundColor(this.theming.bulletUnselectedColor);
        LinearLayout.LayoutParams layoutParams2 = new LinearLayout.LayoutParams(-1, e2);
        int i2 = e2 * 24;
        ((ViewGroup.MarginLayoutParams) layoutParams2).leftMargin = i2;
        ((ViewGroup.MarginLayoutParams) layoutParams2).rightMargin = i2;
        ((ViewGroup.MarginLayoutParams) layoutParams2).bottomMargin = i;
        ((ViewGroup.MarginLayoutParams) layoutParams2).topMargin = i;
        linearLayout.setLayoutParams(layoutParams2);
        addView(linearLayout);
        int i3 = b;
        int i4 = (i3 & 7) + (i3 | 7);
        a = i4 % 128;
        int i5 = i4 % 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\a$e.smali */
    public static final class e extends LinearLayout {
        private final d c;
        private final ImageView[] e;
        private static int d = 0;
        private static int a = 1;

        public e(Context context, d dVar) {
            super(context);
            this.e = new ImageView[dVar.pinNumber];
            this.c = dVar;
            setOrientation(0);
            setGravity(1);
            for (int i = 0; i < dVar.pinNumber; i++) {
                this.e[i] = new ImageView(context);
                this.e[i].setImageResource(dVar.bulletDrawable);
                this.e[i].setColorFilter(dVar.bulletUnselectedColor);
                int e = o.e(context, 6.0f);
                this.e[i].setPadding(e, e, e, e);
                addView(this.e[i]);
            }
        }

        public final void setSelected(int i) {
            int i2 = d;
            int i3 = ((i2 | Opcodes.LUSHR) << 1) - (i2 ^ Opcodes.LUSHR);
            a = i3 % 128;
            int i4 = i3 % 2;
            this.e[i].setColorFilter(this.c.bulletSelectedColor);
            int i5 = d;
            int i6 = ((i5 | 51) << 1) - (i5 ^ 51);
            a = i6 % 128;
            int i7 = i6 % 2;
        }

        public final void setNotSelected(int i) {
            int i2 = d;
            int i3 = ((i2 | 41) << 1) - (i2 ^ 41);
            a = i3 % 128;
            switch (i3 % 2 == 0 ? ']' : '%') {
                case '%':
                    this.e[i].setColorFilter(this.c.bulletUnselectedColor);
                    int i4 = (a + 62) - 1;
                    d = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case true:
                            int i5 = 13 / 0;
                            return;
                        default:
                            return;
                    }
                default:
                    this.e[i].setColorFilter(this.c.bulletUnselectedColor);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        public final void b() {
            /*
                r4 = this;
                int r0 = o.n.a.e.a
                r1 = r0 & 25
                r2 = r0 | 25
                int r1 = r1 + r2
                int r2 = r1 % 128
                o.n.a.e.d = r2
                int r1 = r1 % 2
                r2 = 0
                r3 = 1
                if (r1 == 0) goto L13
                r1 = r2
                goto L14
            L13:
                r1 = r3
            L14:
                switch(r1) {
                    case 1: goto L18;
                    default: goto L17;
                }
            L17:
                r2 = r3
            L18:
                int r0 = r0 + 28
                int r0 = r0 - r3
                int r1 = r0 % 128
                o.n.a.e.d = r1
                int r0 = r0 % 2
                if (r0 == 0) goto L26
                r0 = 40
                goto L28
            L26:
                r0 = 49
            L28:
                switch(r0) {
                    case 40: goto L2b;
                    default: goto L2b;
                }
            L2b:
                o.n.a$d r0 = r4.c
                int r0 = r0.pinNumber
                if (r2 >= r0) goto L34
                r0 = 16
                goto L36
            L34:
                r0 = 50
            L36:
                switch(r0) {
                    case 16: goto L3a;
                    default: goto L39;
                }
            L39:
                goto L80
            L3a:
                int r0 = o.n.a.e.a
                int r0 = r0 + 108
                int r0 = r0 - r3
                int r1 = r0 % 128
                o.n.a.e.d = r1
                int r0 = r0 % 2
                if (r0 == 0) goto L5d
                android.widget.ImageView[] r0 = r4.e
                r0 = r0[r2]
                o.n.a$d r1 = r4.c
                int r1 = r1.bulletUnselectedColor
                r0.setColorFilter(r1)
                int r2 = r2 + 214
                int r2 = r2 - r3
                r0 = r2 ^ (-112(0xffffffffffffff90, float:NaN))
                r1 = r2 & (-112(0xffffffffffffff90, float:NaN))
                int r1 = r1 << r3
                int r0 = r0 + r1
                r2 = r0
                goto L6a
            L5d:
                android.widget.ImageView[] r0 = r4.e
                r0 = r0[r2]
                o.n.a$d r1 = r4.c
                int r1 = r1.bulletUnselectedColor
                r0.setColorFilter(r1)
                int r2 = r2 + 1
            L6a:
                int r0 = o.n.a.e.d
                r1 = r0 ^ 9
                r0 = r0 & 9
                int r0 = r0 << r3
                int r1 = r1 + r0
                int r0 = r1 % 128
                o.n.a.e.a = r0
                int r1 = r1 % 2
                if (r1 != 0) goto L7d
                r0 = 53
                goto L2b
            L7d:
                r0 = 10
                goto L2b
            L80:
                int r0 = o.n.a.e.d
                int r0 = r0 + 110
                int r0 = r0 - r3
                int r1 = r0 % 128
                o.n.a.e.a = r1
                int r0 = r0 % 2
                if (r0 != 0) goto L90
                r0 = 34
                goto L92
            L90:
                r0 = 35
            L92:
                switch(r0) {
                    case 35: goto L96;
                    default: goto L95;
                }
            L95:
                goto L97
            L96:
                return
            L97:
                r0 = 0
                throw r0     // Catch: java.lang.Throwable -> L99
            L99:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.n.a.e.b():void");
        }
    }
}
