package kotlin.reflect;

import kotlin.Metadata;

/* compiled from: KType.kt */
@Metadata(k = 3, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\reflect\KType$DefaultImpls.smali */
public final class KType$DefaultImpls {
    public static /* synthetic */ void getArguments$annotations() {
    }

    public static /* synthetic */ void getClassifier$annotations() {
    }
}
