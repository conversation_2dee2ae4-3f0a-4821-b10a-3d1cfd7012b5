package com.google.android.material;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$anim.smali */
    public static final class anim {
        public static int abc_fade_in = 0x7f010000;
        public static int abc_fade_out = 0x7f010001;
        public static int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static int abc_popup_enter = 0x7f010003;
        public static int abc_popup_exit = 0x7f010004;
        public static int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static int abc_slide_in_bottom = 0x7f010006;
        public static int abc_slide_in_top = 0x7f010007;
        public static int abc_slide_out_bottom = 0x7f010008;
        public static int abc_slide_out_top = 0x7f010009;
        public static int abc_tooltip_enter = 0x7f01000a;
        public static int abc_tooltip_exit = 0x7f01000b;
        public static int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
        public static int design_bottom_sheet_slide_in = 0x7f010018;
        public static int design_bottom_sheet_slide_out = 0x7f010019;
        public static int design_snackbar_in = 0x7f01001a;
        public static int design_snackbar_out = 0x7f01001b;
        public static int mtrl_bottom_sheet_slide_in = 0x7f01001d;
        public static int mtrl_bottom_sheet_slide_out = 0x7f01001e;
        public static int mtrl_card_lowers_interpolator = 0x7f01001f;

        private anim() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$animator.smali */
    public static final class animator {
        public static int design_appbar_state_list_animator = 0x7f020000;
        public static int design_fab_hide_motion_spec = 0x7f020001;
        public static int design_fab_show_motion_spec = 0x7f020002;
        public static int mtrl_btn_state_list_anim = 0x7f020009;
        public static int mtrl_btn_unelevated_state_list_anim = 0x7f02000a;
        public static int mtrl_card_state_list_anim = 0x7f02000b;
        public static int mtrl_chip_state_list_anim = 0x7f02000c;
        public static int mtrl_extended_fab_change_size_motion_spec = 0x7f02000d;
        public static int mtrl_extended_fab_hide_motion_spec = 0x7f02000e;
        public static int mtrl_extended_fab_show_motion_spec = 0x7f02000f;
        public static int mtrl_extended_fab_state_list_animator = 0x7f020010;
        public static int mtrl_fab_hide_motion_spec = 0x7f020011;
        public static int mtrl_fab_show_motion_spec = 0x7f020012;
        public static int mtrl_fab_transformation_sheet_collapse_spec = 0x7f020013;
        public static int mtrl_fab_transformation_sheet_expand_spec = 0x7f020014;

        private animator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$attr.smali */
    public static final class attr {
        public static int actionBarDivider = 0x7f040001;
        public static int actionBarItemBackground = 0x7f040002;
        public static int actionBarPopupTheme = 0x7f040003;
        public static int actionBarSize = 0x7f040004;
        public static int actionBarSplitStyle = 0x7f040005;
        public static int actionBarStyle = 0x7f040006;
        public static int actionBarTabBarStyle = 0x7f040007;
        public static int actionBarTabStyle = 0x7f040008;
        public static int actionBarTabTextStyle = 0x7f040009;
        public static int actionBarTheme = 0x7f04000a;
        public static int actionBarWidgetTheme = 0x7f04000b;
        public static int actionButtonStyle = 0x7f04000c;
        public static int actionDropDownStyle = 0x7f04000d;
        public static int actionLayout = 0x7f04000e;
        public static int actionMenuTextAppearance = 0x7f04000f;
        public static int actionMenuTextColor = 0x7f040010;
        public static int actionModeBackground = 0x7f040011;
        public static int actionModeCloseButtonStyle = 0x7f040012;
        public static int actionModeCloseDrawable = 0x7f040014;
        public static int actionModeCopyDrawable = 0x7f040015;
        public static int actionModeCutDrawable = 0x7f040016;
        public static int actionModeFindDrawable = 0x7f040017;
        public static int actionModePasteDrawable = 0x7f040018;
        public static int actionModePopupWindowStyle = 0x7f040019;
        public static int actionModeSelectAllDrawable = 0x7f04001a;
        public static int actionModeShareDrawable = 0x7f04001b;
        public static int actionModeSplitBackground = 0x7f04001c;
        public static int actionModeStyle = 0x7f04001d;
        public static int actionModeWebSearchDrawable = 0x7f04001f;
        public static int actionOverflowButtonStyle = 0x7f040020;
        public static int actionOverflowMenuStyle = 0x7f040021;
        public static int actionProviderClass = 0x7f040022;
        public static int actionTextColorAlpha = 0x7f040023;
        public static int actionViewClass = 0x7f040024;
        public static int activityChooserViewStyle = 0x7f040025;
        public static int alertDialogButtonGroupStyle = 0x7f040026;
        public static int alertDialogCenterButtons = 0x7f040027;
        public static int alertDialogStyle = 0x7f040028;
        public static int alertDialogTheme = 0x7f040029;
        public static int allowStacking = 0x7f04002a;
        public static int alpha = 0x7f04002b;
        public static int alphabeticModifiers = 0x7f04002c;
        public static int animationMode = 0x7f04002d;
        public static int appBarLayoutStyle = 0x7f0400ac;
        public static int arrowHeadLength = 0x7f0400ae;
        public static int arrowShaftLength = 0x7f0400af;
        public static int autoCompleteTextViewStyle = 0x7f0400b0;
        public static int autoSizeMaxTextSize = 0x7f0400b1;
        public static int autoSizeMinTextSize = 0x7f0400b2;
        public static int autoSizePresetSizes = 0x7f0400b3;
        public static int autoSizeStepGranularity = 0x7f0400b4;
        public static int autoSizeTextType = 0x7f0400b5;
        public static int background = 0x7f0400b6;
        public static int backgroundColor = 0x7f0400b7;
        public static int backgroundInsetBottom = 0x7f0400b8;
        public static int backgroundInsetEnd = 0x7f0400b9;
        public static int backgroundInsetStart = 0x7f0400ba;
        public static int backgroundInsetTop = 0x7f0400bb;
        public static int backgroundOverlayColorAlpha = 0x7f0400bc;
        public static int backgroundSplit = 0x7f0400bd;
        public static int backgroundStacked = 0x7f0400be;
        public static int backgroundTint = 0x7f0400bf;
        public static int backgroundTintMode = 0x7f0400c0;
        public static int badgeGravity = 0x7f0400c1;
        public static int badgeStyle = 0x7f0400c2;
        public static int badgeTextColor = 0x7f0400c3;
        public static int barLength = 0x7f0400c4;
        public static int behavior_autoHide = 0x7f0400c7;
        public static int behavior_autoShrink = 0x7f0400c8;
        public static int behavior_expandedOffset = 0x7f0400c9;
        public static int behavior_fitToContents = 0x7f0400ca;
        public static int behavior_halfExpandedRatio = 0x7f0400cb;
        public static int behavior_hideable = 0x7f0400cc;
        public static int behavior_overlapTop = 0x7f0400cd;
        public static int behavior_peekHeight = 0x7f0400ce;
        public static int behavior_saveFlags = 0x7f0400cf;
        public static int behavior_skipCollapsed = 0x7f0400d0;
        public static int borderWidth = 0x7f0400d1;
        public static int borderlessButtonStyle = 0x7f0400d2;
        public static int bottomAppBarStyle = 0x7f0400d3;
        public static int bottomNavigationStyle = 0x7f0400d4;
        public static int bottomSheetDialogTheme = 0x7f0400d5;
        public static int bottomSheetStyle = 0x7f0400d6;
        public static int boxBackgroundColor = 0x7f0400d7;
        public static int boxBackgroundMode = 0x7f0400d8;
        public static int boxCollapsedPaddingTop = 0x7f0400d9;
        public static int boxCornerRadiusBottomEnd = 0x7f0400da;
        public static int boxCornerRadiusBottomStart = 0x7f0400db;
        public static int boxCornerRadiusTopEnd = 0x7f0400dc;
        public static int boxCornerRadiusTopStart = 0x7f0400dd;
        public static int boxStrokeColor = 0x7f0400de;
        public static int boxStrokeWidth = 0x7f0400df;
        public static int boxStrokeWidthFocused = 0x7f0400e0;
        public static int buttonBarButtonStyle = 0x7f0400e1;
        public static int buttonBarNegativeButtonStyle = 0x7f0400e2;
        public static int buttonBarNeutralButtonStyle = 0x7f0400e3;
        public static int buttonBarPositiveButtonStyle = 0x7f0400e4;
        public static int buttonBarStyle = 0x7f0400e5;
        public static int buttonCompat = 0x7f0400e6;
        public static int buttonGravity = 0x7f0400e7;
        public static int buttonIconDimen = 0x7f0400e8;
        public static int buttonPanelSideLayout = 0x7f0400e9;
        public static int buttonStyle = 0x7f0400eb;
        public static int buttonStyleSmall = 0x7f0400ec;
        public static int buttonTint = 0x7f0400ed;
        public static int buttonTintMode = 0x7f0400ee;
        public static int cardBackgroundColor = 0x7f0400ef;
        public static int cardCornerRadius = 0x7f0400f0;
        public static int cardElevation = 0x7f0400f1;
        public static int cardForegroundColor = 0x7f0400f2;
        public static int cardMaxElevation = 0x7f0400f3;
        public static int cardPreventCornerOverlap = 0x7f0400f4;
        public static int cardUseCompatPadding = 0x7f0400f5;
        public static int cardViewStyle = 0x7f0400f6;
        public static int checkboxStyle = 0x7f0400fb;
        public static int checkedButton = 0x7f0400fc;
        public static int checkedChip = 0x7f0400fd;
        public static int checkedIcon = 0x7f0400fe;
        public static int checkedIconEnabled = 0x7f0400ff;
        public static int checkedIconTint = 0x7f040100;
        public static int checkedIconVisible = 0x7f040101;
        public static int checkedTextViewStyle = 0x7f040102;
        public static int chipBackgroundColor = 0x7f040103;
        public static int chipCornerRadius = 0x7f040104;
        public static int chipEndPadding = 0x7f040105;
        public static int chipGroupStyle = 0x7f040106;
        public static int chipIcon = 0x7f040107;
        public static int chipIconEnabled = 0x7f040108;
        public static int chipIconSize = 0x7f040109;
        public static int chipIconTint = 0x7f04010a;
        public static int chipIconVisible = 0x7f04010b;
        public static int chipMinHeight = 0x7f04010c;
        public static int chipMinTouchTargetSize = 0x7f04010d;
        public static int chipSpacing = 0x7f04010e;
        public static int chipSpacingHorizontal = 0x7f04010f;
        public static int chipSpacingVertical = 0x7f040110;
        public static int chipStandaloneStyle = 0x7f040111;
        public static int chipStartPadding = 0x7f040112;
        public static int chipStrokeColor = 0x7f040113;
        public static int chipStrokeWidth = 0x7f040114;
        public static int chipStyle = 0x7f040115;
        public static int chipSurfaceColor = 0x7f040116;
        public static int closeIcon = 0x7f040118;
        public static int closeIconEnabled = 0x7f040119;
        public static int closeIconEndPadding = 0x7f04011a;
        public static int closeIconSize = 0x7f04011b;
        public static int closeIconStartPadding = 0x7f04011c;
        public static int closeIconTint = 0x7f04011d;
        public static int closeIconVisible = 0x7f04011e;
        public static int closeItemLayout = 0x7f04011f;
        public static int collapseContentDescription = 0x7f040120;
        public static int collapseIcon = 0x7f040121;
        public static int collapsedTitleGravity = 0x7f040122;
        public static int collapsedTitleTextAppearance = 0x7f040123;
        public static int color = 0x7f040124;
        public static int colorAccent = 0x7f040125;
        public static int colorBackgroundFloating = 0x7f040126;
        public static int colorButtonNormal = 0x7f040127;
        public static int colorControlActivated = 0x7f040128;
        public static int colorControlHighlight = 0x7f040129;
        public static int colorControlNormal = 0x7f04012a;
        public static int colorError = 0x7f04012b;
        public static int colorOnBackground = 0x7f04012c;
        public static int colorOnError = 0x7f04012d;
        public static int colorOnPrimary = 0x7f04012e;
        public static int colorOnPrimarySurface = 0x7f04012f;
        public static int colorOnSecondary = 0x7f040130;
        public static int colorOnSurface = 0x7f040131;
        public static int colorPrimary = 0x7f040132;
        public static int colorPrimaryDark = 0x7f040133;
        public static int colorPrimarySurface = 0x7f040134;
        public static int colorPrimaryVariant = 0x7f040135;
        public static int colorSecondary = 0x7f040138;
        public static int colorSecondaryVariant = 0x7f040139;
        public static int colorSurface = 0x7f04013a;
        public static int colorSwitchThumbNormal = 0x7f04013b;
        public static int commitIcon = 0x7f04013c;
        public static int contentDescription = 0x7f040140;
        public static int contentInsetEnd = 0x7f040141;
        public static int contentInsetEndWithActions = 0x7f040142;
        public static int contentInsetLeft = 0x7f040143;
        public static int contentInsetRight = 0x7f040144;
        public static int contentInsetStart = 0x7f040145;
        public static int contentInsetStartWithNavigation = 0x7f040146;
        public static int contentPadding = 0x7f040147;
        public static int contentPaddingBottom = 0x7f040148;
        public static int contentPaddingLeft = 0x7f040149;
        public static int contentPaddingRight = 0x7f04014a;
        public static int contentPaddingTop = 0x7f04014b;
        public static int contentScrim = 0x7f04014c;
        public static int controlBackground = 0x7f04014d;
        public static int coordinatorLayoutStyle = 0x7f04014e;
        public static int cornerFamily = 0x7f04014f;
        public static int cornerFamilyBottomLeft = 0x7f040150;
        public static int cornerFamilyBottomRight = 0x7f040151;
        public static int cornerFamilyTopLeft = 0x7f040152;
        public static int cornerFamilyTopRight = 0x7f040153;
        public static int cornerRadius = 0x7f040154;
        public static int cornerSize = 0x7f040155;
        public static int cornerSizeBottomLeft = 0x7f040156;
        public static int cornerSizeBottomRight = 0x7f040157;
        public static int cornerSizeTopLeft = 0x7f040158;
        public static int cornerSizeTopRight = 0x7f040159;
        public static int counterEnabled = 0x7f04015a;
        public static int counterMaxLength = 0x7f04015b;
        public static int counterOverflowTextAppearance = 0x7f04015c;
        public static int counterOverflowTextColor = 0x7f04015d;
        public static int counterTextAppearance = 0x7f04015e;
        public static int counterTextColor = 0x7f04015f;
        public static int customNavigationLayout = 0x7f040160;
        public static int dayInvalidStyle = 0x7f040163;
        public static int daySelectedStyle = 0x7f040164;
        public static int dayStyle = 0x7f040165;
        public static int dayTodayStyle = 0x7f040166;
        public static int defaultQueryHint = 0x7f040168;
        public static int dialogCornerRadius = 0x7f04016a;
        public static int dialogPreferredPadding = 0x7f04016b;
        public static int dialogTheme = 0x7f04016c;
        public static int displayOptions = 0x7f04016d;
        public static int divider = 0x7f04016e;
        public static int dividerHorizontal = 0x7f04016f;
        public static int dividerPadding = 0x7f040170;
        public static int dividerVertical = 0x7f040171;
        public static int drawableBottomCompat = 0x7f040172;
        public static int drawableEndCompat = 0x7f040173;
        public static int drawableLeftCompat = 0x7f040174;
        public static int drawableRightCompat = 0x7f040175;
        public static int drawableSize = 0x7f040176;
        public static int drawableStartCompat = 0x7f040177;
        public static int drawableTint = 0x7f040178;
        public static int drawableTintMode = 0x7f040179;
        public static int drawableTopCompat = 0x7f04017a;
        public static int drawerArrowStyle = 0x7f04017b;
        public static int dropDownListViewStyle = 0x7f04017c;
        public static int dropdownListPreferredItemHeight = 0x7f04017d;
        public static int editTextBackground = 0x7f04017e;
        public static int editTextColor = 0x7f04017f;
        public static int editTextStyle = 0x7f040180;
        public static int elevation = 0x7f040181;
        public static int elevationOverlayColor = 0x7f040182;
        public static int elevationOverlayEnabled = 0x7f040183;
        public static int endIconCheckable = 0x7f040186;
        public static int endIconContentDescription = 0x7f040187;
        public static int endIconDrawable = 0x7f040188;
        public static int endIconMode = 0x7f040189;
        public static int endIconTint = 0x7f04018a;
        public static int endIconTintMode = 0x7f04018b;
        public static int enforceMaterialTheme = 0x7f04018c;
        public static int enforceTextAppearance = 0x7f04018d;
        public static int ensureMinTouchTargetSize = 0x7f04018e;
        public static int errorEnabled = 0x7f040190;
        public static int errorIconDrawable = 0x7f040191;
        public static int errorIconTint = 0x7f040192;
        public static int errorIconTintMode = 0x7f040193;
        public static int errorTextAppearance = 0x7f040194;
        public static int errorTextColor = 0x7f040195;
        public static int expandActivityOverflowButtonDrawable = 0x7f040197;
        public static int expanded = 0x7f040198;
        public static int expandedTitleGravity = 0x7f040199;
        public static int expandedTitleMargin = 0x7f04019a;
        public static int expandedTitleMarginBottom = 0x7f04019b;
        public static int expandedTitleMarginEnd = 0x7f04019c;
        public static int expandedTitleMarginStart = 0x7f04019d;
        public static int expandedTitleMarginTop = 0x7f04019e;
        public static int expandedTitleTextAppearance = 0x7f04019f;
        public static int extendMotionSpec = 0x7f0401a0;
        public static int extendedFloatingActionButtonStyle = 0x7f0401a1;
        public static int fabAlignmentMode = 0x7f0401a2;
        public static int fabAnimationMode = 0x7f0401a3;
        public static int fabCradleMargin = 0x7f0401a4;
        public static int fabCradleRoundedCornerRadius = 0x7f0401a5;
        public static int fabCradleVerticalOffset = 0x7f0401a6;
        public static int fabCustomSize = 0x7f0401a7;
        public static int fabSize = 0x7f0401a8;
        public static int fastScrollEnabled = 0x7f0401a9;
        public static int fastScrollHorizontalThumbDrawable = 0x7f0401aa;
        public static int fastScrollHorizontalTrackDrawable = 0x7f0401ab;
        public static int fastScrollVerticalThumbDrawable = 0x7f0401ac;
        public static int fastScrollVerticalTrackDrawable = 0x7f0401ad;
        public static int firstBaselineToTopHeight = 0x7f0401ae;
        public static int floatingActionButtonStyle = 0x7f0401af;
        public static int font = 0x7f0401b0;
        public static int fontFamily = 0x7f0401b1;
        public static int fontProviderAuthority = 0x7f0401b2;
        public static int fontProviderCerts = 0x7f0401b3;
        public static int fontProviderFetchStrategy = 0x7f0401b4;
        public static int fontProviderFetchTimeout = 0x7f0401b5;
        public static int fontProviderPackage = 0x7f0401b6;
        public static int fontProviderQuery = 0x7f0401b7;
        public static int fontStyle = 0x7f0401b9;
        public static int fontVariationSettings = 0x7f0401ba;
        public static int fontWeight = 0x7f0401bb;
        public static int foregroundInsidePadding = 0x7f0401bc;
        public static int gapBetweenBars = 0x7f0401bd;
        public static int goIcon = 0x7f0401be;
        public static int headerLayout = 0x7f0401c0;
        public static int height = 0x7f0401c1;
        public static int helperText = 0x7f0401c2;
        public static int helperTextEnabled = 0x7f0401c3;
        public static int helperTextTextAppearance = 0x7f0401c4;
        public static int helperTextTextColor = 0x7f0401c5;
        public static int hideMotionSpec = 0x7f0401c6;
        public static int hideOnContentScroll = 0x7f0401c7;
        public static int hideOnScroll = 0x7f0401c8;
        public static int hintAnimationEnabled = 0x7f0401c9;
        public static int hintEnabled = 0x7f0401ca;
        public static int hintTextAppearance = 0x7f0401cb;
        public static int hintTextColor = 0x7f0401cc;
        public static int homeAsUpIndicator = 0x7f0401cd;
        public static int homeLayout = 0x7f0401ce;
        public static int hoveredFocusedTranslationZ = 0x7f0401cf;
        public static int icon = 0x7f0401d0;
        public static int iconEndPadding = 0x7f0401d1;
        public static int iconGravity = 0x7f0401d2;
        public static int iconPadding = 0x7f0401d3;
        public static int iconSize = 0x7f0401d4;
        public static int iconStartPadding = 0x7f0401d5;
        public static int iconTint = 0x7f0401d6;
        public static int iconTintMode = 0x7f0401d7;
        public static int iconifiedByDefault = 0x7f0401d8;
        public static int imageButtonStyle = 0x7f0401db;
        public static int indeterminateProgressStyle = 0x7f0401dc;
        public static int initialActivityCount = 0x7f0401dd;
        public static int insetForeground = 0x7f0401de;
        public static int isLightTheme = 0x7f0401df;
        public static int isMaterialTheme = 0x7f0401e0;
        public static int itemBackground = 0x7f0401e1;
        public static int itemFillColor = 0x7f0401e2;
        public static int itemHorizontalPadding = 0x7f0401e3;
        public static int itemHorizontalTranslationEnabled = 0x7f0401e4;
        public static int itemIconPadding = 0x7f0401e5;
        public static int itemIconSize = 0x7f0401e6;
        public static int itemIconTint = 0x7f0401e7;
        public static int itemMaxLines = 0x7f0401e8;
        public static int itemPadding = 0x7f0401e9;
        public static int itemRippleColor = 0x7f0401ea;
        public static int itemShapeAppearance = 0x7f0401eb;
        public static int itemShapeAppearanceOverlay = 0x7f0401ec;
        public static int itemShapeFillColor = 0x7f0401ed;
        public static int itemShapeInsetBottom = 0x7f0401ee;
        public static int itemShapeInsetEnd = 0x7f0401ef;
        public static int itemShapeInsetStart = 0x7f0401f0;
        public static int itemShapeInsetTop = 0x7f0401f1;
        public static int itemSpacing = 0x7f0401f2;
        public static int itemStrokeColor = 0x7f0401f3;
        public static int itemStrokeWidth = 0x7f0401f4;
        public static int itemTextAppearance = 0x7f0401f5;
        public static int itemTextAppearanceActive = 0x7f0401f6;
        public static int itemTextAppearanceInactive = 0x7f0401f7;
        public static int itemTextColor = 0x7f0401f8;
        public static int keylines = 0x7f0401f9;
        public static int labelVisibilityMode = 0x7f0401fb;
        public static int lastBaselineToBottomHeight = 0x7f0401fc;
        public static int layout = 0x7f0401fe;
        public static int layoutManager = 0x7f0401ff;
        public static int layout_anchor = 0x7f040200;
        public static int layout_anchorGravity = 0x7f040201;
        public static int layout_behavior = 0x7f040202;
        public static int layout_collapseMode = 0x7f040203;
        public static int layout_collapseParallaxMultiplier = 0x7f040204;
        public static int layout_dodgeInsetEdges = 0x7f04022e;
        public static int layout_insetEdge = 0x7f040237;
        public static int layout_keyline = 0x7f040238;
        public static int layout_scrollFlags = 0x7f04023a;
        public static int layout_scrollInterpolator = 0x7f04023b;
        public static int liftOnScroll = 0x7f04023c;
        public static int liftOnScrollTargetViewId = 0x7f04023d;
        public static int lineHeight = 0x7f04023e;
        public static int lineSpacing = 0x7f04023f;
        public static int listChoiceBackgroundIndicator = 0x7f040240;
        public static int listChoiceIndicatorMultipleAnimated = 0x7f040241;
        public static int listChoiceIndicatorSingleAnimated = 0x7f040242;
        public static int listDividerAlertDialog = 0x7f040243;
        public static int listItemLayout = 0x7f040244;
        public static int listLayout = 0x7f040245;
        public static int listMenuViewStyle = 0x7f040246;
        public static int listPopupWindowStyle = 0x7f040247;
        public static int listPreferredItemHeight = 0x7f040248;
        public static int listPreferredItemHeightLarge = 0x7f040249;
        public static int listPreferredItemHeightSmall = 0x7f04024a;
        public static int listPreferredItemPaddingEnd = 0x7f04024b;
        public static int listPreferredItemPaddingLeft = 0x7f04024c;
        public static int listPreferredItemPaddingRight = 0x7f04024d;
        public static int listPreferredItemPaddingStart = 0x7f04024e;
        public static int logo = 0x7f04024f;
        public static int logoDescription = 0x7f040250;
        public static int materialAlertDialogBodyTextStyle = 0x7f040251;
        public static int materialAlertDialogTheme = 0x7f040252;
        public static int materialAlertDialogTitleIconStyle = 0x7f040253;
        public static int materialAlertDialogTitlePanelStyle = 0x7f040254;
        public static int materialAlertDialogTitleTextStyle = 0x7f040255;
        public static int materialButtonOutlinedStyle = 0x7f040256;
        public static int materialButtonStyle = 0x7f040257;
        public static int materialButtonToggleGroupStyle = 0x7f040258;
        public static int materialCalendarDay = 0x7f040259;
        public static int materialCalendarFullscreenTheme = 0x7f04025a;
        public static int materialCalendarHeaderConfirmButton = 0x7f04025b;
        public static int materialCalendarHeaderDivider = 0x7f04025c;
        public static int materialCalendarHeaderLayout = 0x7f04025d;
        public static int materialCalendarHeaderSelection = 0x7f04025e;
        public static int materialCalendarHeaderTitle = 0x7f04025f;
        public static int materialCalendarHeaderToggleButton = 0x7f040260;
        public static int materialCalendarStyle = 0x7f040261;
        public static int materialCalendarTheme = 0x7f040262;
        public static int materialCardViewStyle = 0x7f040263;
        public static int materialThemeOverlay = 0x7f040264;
        public static int maxActionInlineWidth = 0x7f040265;
        public static int maxButtonHeight = 0x7f040266;
        public static int maxCharacterCount = 0x7f040267;
        public static int maxImageSize = 0x7f040268;
        public static int measureWithLargestChild = 0x7f040269;
        public static int menu = 0x7f04026a;
        public static int minTouchTargetSize = 0x7f04026b;
        public static int multiChoiceItemLayout = 0x7f04026c;
        public static int navigationContentDescription = 0x7f04026e;
        public static int navigationIcon = 0x7f04026f;
        public static int navigationMode = 0x7f040270;
        public static int navigationViewStyle = 0x7f040271;
        public static int number = 0x7f040274;
        public static int numericModifiers = 0x7f040275;
        public static int overlapAnchor = 0x7f040276;
        public static int paddingBottomNoButtons = 0x7f040277;
        public static int paddingEnd = 0x7f040278;
        public static int paddingStart = 0x7f040279;
        public static int paddingTopNoTitle = 0x7f04027a;
        public static int panelBackground = 0x7f04027b;
        public static int panelMenuListTheme = 0x7f04027c;
        public static int panelMenuListWidth = 0x7f04027d;
        public static int passwordToggleContentDescription = 0x7f04027e;
        public static int passwordToggleDrawable = 0x7f04027f;
        public static int passwordToggleEnabled = 0x7f040280;
        public static int passwordToggleTint = 0x7f040281;
        public static int passwordToggleTintMode = 0x7f040282;
        public static int popupMenuBackground = 0x7f040287;
        public static int popupMenuStyle = 0x7f040288;
        public static int popupTheme = 0x7f040289;
        public static int popupWindowStyle = 0x7f04028a;
        public static int preserveIconSpacing = 0x7f04028c;
        public static int pressedTranslationZ = 0x7f04028d;
        public static int progressBarPadding = 0x7f04028e;
        public static int progressBarStyle = 0x7f04028f;
        public static int queryBackground = 0x7f040290;
        public static int queryHint = 0x7f040291;
        public static int radioButtonStyle = 0x7f040293;
        public static int rangeFillColor = 0x7f040294;
        public static int ratingBarStyle = 0x7f040295;
        public static int ratingBarStyleIndicator = 0x7f040296;
        public static int ratingBarStyleSmall = 0x7f040297;
        public static int recyclerViewStyle = 0x7f040298;
        public static int reverseLayout = 0x7f040299;
        public static int rippleColor = 0x7f04029a;
        public static int scrimAnimationDuration = 0x7f04029c;
        public static int scrimBackground = 0x7f04029d;
        public static int scrimVisibleHeightTrigger = 0x7f04029e;
        public static int searchHintIcon = 0x7f04029f;
        public static int searchIcon = 0x7f0402a0;
        public static int searchViewStyle = 0x7f0402a1;
        public static int seekBarStyle = 0x7f0402a2;
        public static int selectableItemBackground = 0x7f0402a3;
        public static int selectableItemBackgroundBorderless = 0x7f0402a4;
        public static int shapeAppearance = 0x7f0402a5;
        public static int shapeAppearanceLargeComponent = 0x7f0402a6;
        public static int shapeAppearanceMediumComponent = 0x7f0402a7;
        public static int shapeAppearanceOverlay = 0x7f0402a8;
        public static int shapeAppearanceSmallComponent = 0x7f0402a9;
        public static int showAsAction = 0x7f0402ab;
        public static int showDividers = 0x7f0402ac;
        public static int showMotionSpec = 0x7f0402ad;
        public static int showText = 0x7f0402ae;
        public static int showTitle = 0x7f0402af;
        public static int shrinkMotionSpec = 0x7f0402b0;
        public static int singleChoiceItemLayout = 0x7f0402b1;
        public static int singleLine = 0x7f0402b2;
        public static int singleSelection = 0x7f0402b3;
        public static int snackbarButtonStyle = 0x7f0402b4;
        public static int snackbarStyle = 0x7f0402b5;
        public static int spanCount = 0x7f0402b6;
        public static int spinBars = 0x7f0402b7;
        public static int spinnerDropDownItemStyle = 0x7f0402b8;
        public static int spinnerStyle = 0x7f0402b9;
        public static int splitTrack = 0x7f0402bb;
        public static int srcCompat = 0x7f0402bc;
        public static int stackFromEnd = 0x7f0402bd;
        public static int startIconCheckable = 0x7f0402bf;
        public static int startIconContentDescription = 0x7f0402c0;
        public static int startIconDrawable = 0x7f0402c1;
        public static int startIconTint = 0x7f0402c2;
        public static int startIconTintMode = 0x7f0402c3;
        public static int state_above_anchor = 0x7f0402c5;
        public static int state_collapsed = 0x7f0402c6;
        public static int state_collapsible = 0x7f0402c7;
        public static int state_dragged = 0x7f0402c8;
        public static int state_liftable = 0x7f0402c9;
        public static int state_lifted = 0x7f0402ca;
        public static int statusBarBackground = 0x7f0402cb;
        public static int statusBarForeground = 0x7f0402cc;
        public static int statusBarScrim = 0x7f0402cd;
        public static int strokeColor = 0x7f0402ce;
        public static int strokeWidth = 0x7f0402cf;
        public static int subMenuArrow = 0x7f0402d0;
        public static int submitBackground = 0x7f0402d1;
        public static int subtitle = 0x7f0402d2;
        public static int subtitleTextAppearance = 0x7f0402d3;
        public static int subtitleTextColor = 0x7f0402d4;
        public static int subtitleTextStyle = 0x7f0402d5;
        public static int suggestionRowLayout = 0x7f0402d6;
        public static int switchMinWidth = 0x7f0402d7;
        public static int switchPadding = 0x7f0402d8;
        public static int switchStyle = 0x7f0402d9;
        public static int switchTextAppearance = 0x7f0402da;
        public static int tabBackground = 0x7f0402db;
        public static int tabContentStart = 0x7f0402dc;
        public static int tabGravity = 0x7f0402dd;
        public static int tabIconTint = 0x7f0402de;
        public static int tabIconTintMode = 0x7f0402df;
        public static int tabIndicator = 0x7f0402e0;
        public static int tabIndicatorAnimationDuration = 0x7f0402e1;
        public static int tabIndicatorColor = 0x7f0402e2;
        public static int tabIndicatorFullWidth = 0x7f0402e3;
        public static int tabIndicatorGravity = 0x7f0402e4;
        public static int tabIndicatorHeight = 0x7f0402e5;
        public static int tabInlineLabel = 0x7f0402e6;
        public static int tabMaxWidth = 0x7f0402e7;
        public static int tabMinWidth = 0x7f0402e8;
        public static int tabMode = 0x7f0402e9;
        public static int tabPadding = 0x7f0402ea;
        public static int tabPaddingBottom = 0x7f0402eb;
        public static int tabPaddingEnd = 0x7f0402ec;
        public static int tabPaddingStart = 0x7f0402ed;
        public static int tabPaddingTop = 0x7f0402ee;
        public static int tabRippleColor = 0x7f0402ef;
        public static int tabSelectedTextColor = 0x7f0402f0;
        public static int tabStyle = 0x7f0402f1;
        public static int tabTextAppearance = 0x7f0402f2;
        public static int tabTextColor = 0x7f0402f3;
        public static int tabUnboundedRipple = 0x7f0402f4;
        public static int textAllCaps = 0x7f0402f5;
        public static int textAppearanceBody1 = 0x7f0402f6;
        public static int textAppearanceBody2 = 0x7f0402f7;
        public static int textAppearanceButton = 0x7f0402f8;
        public static int textAppearanceCaption = 0x7f0402f9;
        public static int textAppearanceHeadline1 = 0x7f0402fa;
        public static int textAppearanceHeadline2 = 0x7f0402fb;
        public static int textAppearanceHeadline3 = 0x7f0402fc;
        public static int textAppearanceHeadline4 = 0x7f0402fd;
        public static int textAppearanceHeadline5 = 0x7f0402fe;
        public static int textAppearanceHeadline6 = 0x7f0402ff;
        public static int textAppearanceLargePopupMenu = 0x7f040300;
        public static int textAppearanceLineHeightEnabled = 0x7f040301;
        public static int textAppearanceListItem = 0x7f040302;
        public static int textAppearanceListItemSecondary = 0x7f040303;
        public static int textAppearanceListItemSmall = 0x7f040304;
        public static int textAppearanceOverline = 0x7f040305;
        public static int textAppearancePopupMenuHeader = 0x7f040306;
        public static int textAppearanceSearchResultSubtitle = 0x7f040307;
        public static int textAppearanceSearchResultTitle = 0x7f040308;
        public static int textAppearanceSmallPopupMenu = 0x7f040309;
        public static int textAppearanceSubtitle1 = 0x7f04030a;
        public static int textAppearanceSubtitle2 = 0x7f04030b;
        public static int textColorAlertDialogListItem = 0x7f04030c;
        public static int textColorSearchUrl = 0x7f04030d;
        public static int textEndPadding = 0x7f04030e;
        public static int textInputStyle = 0x7f04030f;
        public static int textLocale = 0x7f040310;
        public static int textStartPadding = 0x7f040311;
        public static int theme = 0x7f040312;
        public static int themeLineHeight = 0x7f040313;
        public static int thickness = 0x7f040314;
        public static int thumbTextPadding = 0x7f040315;
        public static int thumbTint = 0x7f040316;
        public static int thumbTintMode = 0x7f040317;
        public static int tickMark = 0x7f040318;
        public static int tickMarkTint = 0x7f040319;
        public static int tickMarkTintMode = 0x7f04031a;
        public static int tint = 0x7f04031b;
        public static int tintMode = 0x7f04031c;
        public static int title = 0x7f04031d;
        public static int titleEnabled = 0x7f04031e;
        public static int titleMargin = 0x7f04031f;
        public static int titleMarginBottom = 0x7f040320;
        public static int titleMarginEnd = 0x7f040321;
        public static int titleMarginStart = 0x7f040322;
        public static int titleMarginTop = 0x7f040323;
        public static int titleMargins = 0x7f040324;
        public static int titleTextAppearance = 0x7f040325;
        public static int titleTextColor = 0x7f040326;
        public static int titleTextStyle = 0x7f040327;
        public static int toolbarId = 0x7f040328;
        public static int toolbarNavigationButtonStyle = 0x7f040329;
        public static int toolbarStyle = 0x7f04032a;
        public static int tooltipForegroundColor = 0x7f04032b;
        public static int tooltipFrameBackground = 0x7f04032c;
        public static int tooltipText = 0x7f04032d;
        public static int track = 0x7f04032e;
        public static int trackTint = 0x7f04032f;
        public static int trackTintMode = 0x7f040330;
        public static int ttcIndex = 0x7f040331;
        public static int useCompatPadding = 0x7f040333;
        public static int useMaterialThemeColors = 0x7f040334;
        public static int viewInflaterClass = 0x7f040335;
        public static int voiceIcon = 0x7f040336;
        public static int windowActionBar = 0x7f040337;
        public static int windowActionBarOverlay = 0x7f040338;
        public static int windowActionModeOverlay = 0x7f040339;
        public static int windowFixedHeightMajor = 0x7f04033a;
        public static int windowFixedHeightMinor = 0x7f04033b;
        public static int windowFixedWidthMajor = 0x7f04033c;
        public static int windowFixedWidthMinor = 0x7f04033d;
        public static int windowMinWidthMajor = 0x7f04033e;
        public static int windowMinWidthMinor = 0x7f04033f;
        public static int windowNoTitle = 0x7f040340;
        public static int yearSelectedStyle = 0x7f040345;
        public static int yearStyle = 0x7f040346;
        public static int yearTodayStyle = 0x7f040347;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$bool.smali */
    public static final class bool {
        public static int abc_action_bar_embed_tabs = 0x7f050000;
        public static int abc_config_actionMenuItemAllCaps = 0x7f050001;
        public static int mtrl_btn_textappearance_all_caps = 0x7f050019;

        private bool() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$color.smali */
    public static final class color {
        public static int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static int abc_btn_colored_text_material = 0x7f060003;
        public static int abc_color_highlight_material = 0x7f060004;
        public static int abc_hint_foreground_material_dark = 0x7f060007;
        public static int abc_hint_foreground_material_light = 0x7f060008;
        public static int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static int abc_primary_text_material_dark = 0x7f06000b;
        public static int abc_primary_text_material_light = 0x7f06000c;
        public static int abc_search_url_text = 0x7f06000d;
        public static int abc_search_url_text_normal = 0x7f06000e;
        public static int abc_search_url_text_pressed = 0x7f06000f;
        public static int abc_search_url_text_selected = 0x7f060010;
        public static int abc_secondary_text_material_dark = 0x7f060011;
        public static int abc_secondary_text_material_light = 0x7f060012;
        public static int abc_tint_btn_checkable = 0x7f060013;
        public static int abc_tint_default = 0x7f060014;
        public static int abc_tint_edittext = 0x7f060015;
        public static int abc_tint_seek_thumb = 0x7f060016;
        public static int abc_tint_spinner = 0x7f060017;
        public static int abc_tint_switch_track = 0x7f060018;
        public static int accent_material_dark = 0x7f060019;
        public static int accent_material_light = 0x7f06001a;
        public static int background_floating_material_dark = 0x7f060040;
        public static int background_floating_material_light = 0x7f060041;
        public static int background_material_dark = 0x7f060042;
        public static int background_material_light = 0x7f060043;
        public static int bright_foreground_disabled_material_dark = 0x7f060045;
        public static int bright_foreground_disabled_material_light = 0x7f060046;
        public static int bright_foreground_inverse_material_dark = 0x7f060047;
        public static int bright_foreground_inverse_material_light = 0x7f060048;
        public static int bright_foreground_material_dark = 0x7f060049;
        public static int bright_foreground_material_light = 0x7f06004a;
        public static int button_material_dark = 0x7f06004b;
        public static int button_material_light = 0x7f06004c;
        public static int cardview_dark_background = 0x7f06004f;
        public static int cardview_light_background = 0x7f060050;
        public static int cardview_shadow_end_color = 0x7f060051;
        public static int cardview_shadow_start_color = 0x7f060052;
        public static int checkbox_themeable_attribute_color = 0x7f060053;
        public static int design_bottom_navigation_shadow_color = 0x7f060062;
        public static int design_box_stroke_color = 0x7f060063;
        public static int design_dark_default_color_background = 0x7f060064;
        public static int design_dark_default_color_error = 0x7f060065;
        public static int design_dark_default_color_on_background = 0x7f060066;
        public static int design_dark_default_color_on_error = 0x7f060067;
        public static int design_dark_default_color_on_primary = 0x7f060068;
        public static int design_dark_default_color_on_secondary = 0x7f060069;
        public static int design_dark_default_color_on_surface = 0x7f06006a;
        public static int design_dark_default_color_primary = 0x7f06006b;
        public static int design_dark_default_color_primary_dark = 0x7f06006c;
        public static int design_dark_default_color_primary_variant = 0x7f06006d;
        public static int design_dark_default_color_secondary = 0x7f06006e;
        public static int design_dark_default_color_secondary_variant = 0x7f06006f;
        public static int design_dark_default_color_surface = 0x7f060070;
        public static int design_default_color_background = 0x7f060071;
        public static int design_default_color_error = 0x7f060072;
        public static int design_default_color_on_background = 0x7f060073;
        public static int design_default_color_on_error = 0x7f060074;
        public static int design_default_color_on_primary = 0x7f060075;
        public static int design_default_color_on_secondary = 0x7f060076;
        public static int design_default_color_on_surface = 0x7f060077;
        public static int design_default_color_primary = 0x7f060078;
        public static int design_default_color_primary_dark = 0x7f060079;
        public static int design_default_color_primary_variant = 0x7f06007a;
        public static int design_default_color_secondary = 0x7f06007b;
        public static int design_default_color_secondary_variant = 0x7f06007c;
        public static int design_default_color_surface = 0x7f06007d;
        public static int design_error = 0x7f06007e;
        public static int design_fab_shadow_end_color = 0x7f06007f;
        public static int design_fab_shadow_mid_color = 0x7f060080;
        public static int design_fab_shadow_start_color = 0x7f060081;
        public static int design_fab_stroke_end_inner_color = 0x7f060082;
        public static int design_fab_stroke_end_outer_color = 0x7f060083;
        public static int design_fab_stroke_top_inner_color = 0x7f060084;
        public static int design_fab_stroke_top_outer_color = 0x7f060085;
        public static int design_icon_tint = 0x7f060086;
        public static int design_snackbar_background_color = 0x7f060087;
        public static int dim_foreground_disabled_material_dark = 0x7f060088;
        public static int dim_foreground_disabled_material_light = 0x7f060089;
        public static int dim_foreground_material_dark = 0x7f06008a;
        public static int dim_foreground_material_light = 0x7f06008b;
        public static int error_color_material_dark = 0x7f06008c;
        public static int error_color_material_light = 0x7f06008d;
        public static int foreground_material_dark = 0x7f06008e;
        public static int foreground_material_light = 0x7f06008f;
        public static int highlighted_text_material_dark = 0x7f060090;
        public static int highlighted_text_material_light = 0x7f060091;
        public static int material_blue_grey_800 = 0x7f060093;
        public static int material_blue_grey_900 = 0x7f060094;
        public static int material_blue_grey_950 = 0x7f060095;
        public static int material_deep_teal_200 = 0x7f060096;
        public static int material_deep_teal_500 = 0x7f060097;
        public static int material_grey_100 = 0x7f060098;
        public static int material_grey_300 = 0x7f060099;
        public static int material_grey_50 = 0x7f06009a;
        public static int material_grey_600 = 0x7f06009b;
        public static int material_grey_800 = 0x7f06009c;
        public static int material_grey_850 = 0x7f06009d;
        public static int material_grey_900 = 0x7f06009e;
        public static int material_on_background_disabled = 0x7f06009f;
        public static int material_on_background_emphasis_high_type = 0x7f0600a0;
        public static int material_on_background_emphasis_medium = 0x7f0600a1;
        public static int material_on_primary_disabled = 0x7f0600a2;
        public static int material_on_primary_emphasis_high_type = 0x7f0600a3;
        public static int material_on_primary_emphasis_medium = 0x7f0600a4;
        public static int material_on_surface_disabled = 0x7f0600a5;
        public static int material_on_surface_emphasis_high_type = 0x7f0600a6;
        public static int material_on_surface_emphasis_medium = 0x7f0600a7;
        public static int mtrl_bottom_nav_colored_item_tint = 0x7f0600a8;
        public static int mtrl_bottom_nav_colored_ripple_color = 0x7f0600a9;
        public static int mtrl_bottom_nav_item_tint = 0x7f0600aa;
        public static int mtrl_bottom_nav_ripple_color = 0x7f0600ab;
        public static int mtrl_btn_bg_color_selector = 0x7f0600ac;
        public static int mtrl_btn_ripple_color = 0x7f0600ad;
        public static int mtrl_btn_stroke_color_selector = 0x7f0600ae;
        public static int mtrl_btn_text_btn_bg_color_selector = 0x7f0600af;
        public static int mtrl_btn_text_btn_ripple_color = 0x7f0600b0;
        public static int mtrl_btn_text_color_disabled = 0x7f0600b1;
        public static int mtrl_btn_text_color_selector = 0x7f0600b2;
        public static int mtrl_btn_transparent_bg_color = 0x7f0600b3;
        public static int mtrl_calendar_item_stroke_color = 0x7f0600b4;
        public static int mtrl_calendar_selected_range = 0x7f0600b5;
        public static int mtrl_card_view_foreground = 0x7f0600b6;
        public static int mtrl_card_view_ripple = 0x7f0600b7;
        public static int mtrl_chip_background_color = 0x7f0600b8;
        public static int mtrl_chip_close_icon_tint = 0x7f0600b9;
        public static int mtrl_chip_ripple_color = 0x7f0600ba;
        public static int mtrl_chip_surface_color = 0x7f0600bb;
        public static int mtrl_chip_text_color = 0x7f0600bc;
        public static int mtrl_choice_chip_background_color = 0x7f0600bd;
        public static int mtrl_choice_chip_ripple_color = 0x7f0600be;
        public static int mtrl_choice_chip_text_color = 0x7f0600bf;
        public static int mtrl_error = 0x7f0600c0;
        public static int mtrl_extended_fab_bg_color_selector = 0x7f0600c1;
        public static int mtrl_extended_fab_ripple_color = 0x7f0600c2;
        public static int mtrl_extended_fab_text_color_selector = 0x7f0600c3;
        public static int mtrl_fab_ripple_color = 0x7f0600c4;
        public static int mtrl_filled_background_color = 0x7f0600c5;
        public static int mtrl_filled_icon_tint = 0x7f0600c6;
        public static int mtrl_filled_stroke_color = 0x7f0600c7;
        public static int mtrl_indicator_text_color = 0x7f0600c8;
        public static int mtrl_navigation_item_background_color = 0x7f0600c9;
        public static int mtrl_navigation_item_icon_tint = 0x7f0600ca;
        public static int mtrl_navigation_item_text_color = 0x7f0600cb;
        public static int mtrl_on_primary_text_btn_text_color_selector = 0x7f0600cc;
        public static int mtrl_outlined_icon_tint = 0x7f0600cd;
        public static int mtrl_outlined_stroke_color = 0x7f0600ce;
        public static int mtrl_popupmenu_overlay_color = 0x7f0600cf;
        public static int mtrl_scrim_color = 0x7f0600d0;
        public static int mtrl_tabs_colored_ripple_color = 0x7f0600d1;
        public static int mtrl_tabs_icon_color_selector = 0x7f0600d2;
        public static int mtrl_tabs_icon_color_selector_colored = 0x7f0600d3;
        public static int mtrl_tabs_legacy_text_color_selector = 0x7f0600d4;
        public static int mtrl_tabs_ripple_color = 0x7f0600d5;
        public static int mtrl_text_btn_text_color_selector = 0x7f0600d6;
        public static int mtrl_textinput_default_box_stroke_color = 0x7f0600d7;
        public static int mtrl_textinput_disabled_color = 0x7f0600d8;
        public static int mtrl_textinput_filled_box_default_background_color = 0x7f0600d9;
        public static int mtrl_textinput_focused_box_stroke_color = 0x7f0600da;
        public static int mtrl_textinput_hovered_box_stroke_color = 0x7f0600db;
        public static int notification_action_color_filter = 0x7f0600dc;
        public static int notification_icon_bg_color = 0x7f0600dd;
        public static int primary_dark_material_dark = 0x7f0600df;
        public static int primary_dark_material_light = 0x7f0600e0;
        public static int primary_material_dark = 0x7f0600e1;
        public static int primary_material_light = 0x7f0600e2;
        public static int primary_text_default_material_dark = 0x7f0600e3;
        public static int primary_text_default_material_light = 0x7f0600e4;
        public static int primary_text_disabled_material_dark = 0x7f0600e5;
        public static int primary_text_disabled_material_light = 0x7f0600e6;
        public static int ripple_material_dark = 0x7f0600e7;
        public static int ripple_material_light = 0x7f0600e8;
        public static int secondary_text_default_material_dark = 0x7f0600e9;
        public static int secondary_text_default_material_light = 0x7f0600ea;
        public static int secondary_text_disabled_material_dark = 0x7f0600eb;
        public static int secondary_text_disabled_material_light = 0x7f0600ec;
        public static int switch_thumb_disabled_material_dark = 0x7f0600ed;
        public static int switch_thumb_disabled_material_light = 0x7f0600ee;
        public static int switch_thumb_material_dark = 0x7f0600ef;
        public static int switch_thumb_material_light = 0x7f0600f0;
        public static int switch_thumb_normal_material_dark = 0x7f0600f1;
        public static int switch_thumb_normal_material_light = 0x7f0600f2;
        public static int test_mtrl_calendar_day = 0x7f0600f6;
        public static int test_mtrl_calendar_day_selected = 0x7f0600f7;
        public static int tooltip_background_dark = 0x7f0600f8;
        public static int tooltip_background_light = 0x7f0600f9;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$dimen.smali */
    public static final class dimen {
        public static int abc_action_bar_content_inset_material = 0x7f070000;
        public static int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static int abc_action_bar_default_height_material = 0x7f070002;
        public static int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static int abc_action_bar_elevation_material = 0x7f070005;
        public static int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static int abc_action_bar_stacked_max_height = 0x7f070009;
        public static int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static int abc_action_button_min_height_material = 0x7f07000d;
        public static int abc_action_button_min_width_material = 0x7f07000e;
        public static int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static int abc_alert_dialog_button_dimen = 0x7f070011;
        public static int abc_button_inset_horizontal_material = 0x7f070012;
        public static int abc_button_inset_vertical_material = 0x7f070013;
        public static int abc_button_padding_horizontal_material = 0x7f070014;
        public static int abc_button_padding_vertical_material = 0x7f070015;
        public static int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static int abc_config_prefDialogWidth = 0x7f070017;
        public static int abc_control_corner_material = 0x7f070018;
        public static int abc_control_inset_material = 0x7f070019;
        public static int abc_control_padding_material = 0x7f07001a;
        public static int abc_dialog_corner_radius_material = 0x7f07001b;
        public static int abc_dialog_fixed_height_major = 0x7f07001c;
        public static int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static int abc_dialog_fixed_width_major = 0x7f07001e;
        public static int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static int abc_dialog_min_width_major = 0x7f070022;
        public static int abc_dialog_min_width_minor = 0x7f070023;
        public static int abc_dialog_padding_material = 0x7f070024;
        public static int abc_dialog_padding_top_material = 0x7f070025;
        public static int abc_dialog_title_divider_material = 0x7f070026;
        public static int abc_disabled_alpha_material_dark = 0x7f070027;
        public static int abc_disabled_alpha_material_light = 0x7f070028;
        public static int abc_dropdownitem_icon_width = 0x7f070029;
        public static int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static int abc_edit_text_inset_top_material = 0x7f07002e;
        public static int abc_floating_window_z = 0x7f07002f;
        public static int abc_list_item_height_large_material = 0x7f070030;
        public static int abc_list_item_height_material = 0x7f070031;
        public static int abc_list_item_height_small_material = 0x7f070032;
        public static int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static int abc_panel_menu_list_width = 0x7f070034;
        public static int abc_progress_bar_height_material = 0x7f070035;
        public static int abc_search_view_preferred_height = 0x7f070036;
        public static int abc_search_view_preferred_width = 0x7f070037;
        public static int abc_seekbar_track_background_height_material = 0x7f070038;
        public static int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static int abc_switch_padding = 0x7f07003e;
        public static int abc_text_size_body_1_material = 0x7f07003f;
        public static int abc_text_size_body_2_material = 0x7f070040;
        public static int abc_text_size_button_material = 0x7f070041;
        public static int abc_text_size_caption_material = 0x7f070042;
        public static int abc_text_size_display_1_material = 0x7f070043;
        public static int abc_text_size_display_2_material = 0x7f070044;
        public static int abc_text_size_display_3_material = 0x7f070045;
        public static int abc_text_size_display_4_material = 0x7f070046;
        public static int abc_text_size_headline_material = 0x7f070047;
        public static int abc_text_size_large_material = 0x7f070048;
        public static int abc_text_size_medium_material = 0x7f070049;
        public static int abc_text_size_menu_header_material = 0x7f07004a;
        public static int abc_text_size_menu_material = 0x7f07004b;
        public static int abc_text_size_small_material = 0x7f07004c;
        public static int abc_text_size_subhead_material = 0x7f07004d;
        public static int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static int abc_text_size_title_material = 0x7f07004f;
        public static int abc_text_size_title_material_toolbar = 0x7f070050;
        public static int action_bar_size = 0x7f070051;
        public static int appcompat_dialog_background_inset = 0x7f07008a;
        public static int cardview_compat_inset_shadow = 0x7f07008b;
        public static int cardview_default_elevation = 0x7f07008c;
        public static int cardview_default_radius = 0x7f07008d;
        public static int compat_button_inset_horizontal_material = 0x7f07008e;
        public static int compat_button_inset_vertical_material = 0x7f07008f;
        public static int compat_button_padding_horizontal_material = 0x7f070090;
        public static int compat_button_padding_vertical_material = 0x7f070091;
        public static int compat_control_corner_material = 0x7f070092;
        public static int compat_notification_large_icon_max_height = 0x7f070093;
        public static int compat_notification_large_icon_max_width = 0x7f070094;
        public static int default_dimension = 0x7f070095;
        public static int design_appbar_elevation = 0x7f070096;
        public static int design_bottom_navigation_active_item_max_width = 0x7f070097;
        public static int design_bottom_navigation_active_item_min_width = 0x7f070098;
        public static int design_bottom_navigation_active_text_size = 0x7f070099;
        public static int design_bottom_navigation_elevation = 0x7f07009a;
        public static int design_bottom_navigation_height = 0x7f07009b;
        public static int design_bottom_navigation_icon_size = 0x7f07009c;
        public static int design_bottom_navigation_item_max_width = 0x7f07009d;
        public static int design_bottom_navigation_item_min_width = 0x7f07009e;
        public static int design_bottom_navigation_margin = 0x7f07009f;
        public static int design_bottom_navigation_shadow_height = 0x7f0700a0;
        public static int design_bottom_navigation_text_size = 0x7f0700a1;
        public static int design_bottom_sheet_elevation = 0x7f0700a2;
        public static int design_bottom_sheet_modal_elevation = 0x7f0700a3;
        public static int design_bottom_sheet_peek_height_min = 0x7f0700a4;
        public static int design_fab_border_width = 0x7f0700a5;
        public static int design_fab_elevation = 0x7f0700a6;
        public static int design_fab_image_size = 0x7f0700a7;
        public static int design_fab_size_mini = 0x7f0700a8;
        public static int design_fab_size_normal = 0x7f0700a9;
        public static int design_fab_translation_z_hovered_focused = 0x7f0700aa;
        public static int design_fab_translation_z_pressed = 0x7f0700ab;
        public static int design_navigation_elevation = 0x7f0700ac;
        public static int design_navigation_icon_padding = 0x7f0700ad;
        public static int design_navigation_icon_size = 0x7f0700ae;
        public static int design_navigation_item_horizontal_padding = 0x7f0700af;
        public static int design_navigation_item_icon_padding = 0x7f0700b0;
        public static int design_navigation_max_width = 0x7f0700b1;
        public static int design_navigation_padding_bottom = 0x7f0700b2;
        public static int design_navigation_separator_vertical_padding = 0x7f0700b3;
        public static int design_snackbar_action_inline_max_width = 0x7f0700b4;
        public static int design_snackbar_action_text_color_alpha = 0x7f0700b5;
        public static int design_snackbar_background_corner_radius = 0x7f0700b6;
        public static int design_snackbar_elevation = 0x7f0700b7;
        public static int design_snackbar_extra_spacing_horizontal = 0x7f0700b8;
        public static int design_snackbar_max_width = 0x7f0700b9;
        public static int design_snackbar_min_width = 0x7f0700ba;
        public static int design_snackbar_padding_horizontal = 0x7f0700bb;
        public static int design_snackbar_padding_vertical = 0x7f0700bc;
        public static int design_snackbar_padding_vertical_2lines = 0x7f0700bd;
        public static int design_snackbar_text_size = 0x7f0700be;
        public static int design_tab_max_width = 0x7f0700bf;
        public static int design_tab_scrollable_min_width = 0x7f0700c0;
        public static int design_tab_text_size = 0x7f0700c1;
        public static int design_tab_text_size_2line = 0x7f0700c2;
        public static int design_textinput_caption_translate_y = 0x7f0700c3;
        public static int disabled_alpha_material_dark = 0x7f0700c4;
        public static int disabled_alpha_material_light = 0x7f0700c5;
        public static int fastscroll_default_thickness = 0x7f0700c7;
        public static int fastscroll_margin = 0x7f0700c8;
        public static int fastscroll_minimum_range = 0x7f0700c9;
        public static int highlight_alpha_material_colored = 0x7f0700cb;
        public static int highlight_alpha_material_dark = 0x7f0700cc;
        public static int highlight_alpha_material_light = 0x7f0700cd;
        public static int hint_alpha_material_dark = 0x7f0700ce;
        public static int hint_alpha_material_light = 0x7f0700cf;
        public static int hint_pressed_alpha_material_dark = 0x7f0700d0;
        public static int hint_pressed_alpha_material_light = 0x7f0700d1;
        public static int item_touch_helper_max_drag_scroll_per_frame = 0x7f0700d2;
        public static int item_touch_helper_swipe_escape_max_velocity = 0x7f0700d3;
        public static int item_touch_helper_swipe_escape_velocity = 0x7f0700d4;
        public static int material_emphasis_disabled = 0x7f0700d5;
        public static int material_emphasis_high_type = 0x7f0700d6;
        public static int material_emphasis_medium = 0x7f0700d7;
        public static int material_text_view_test_line_height = 0x7f0700d8;
        public static int material_text_view_test_line_height_override = 0x7f0700d9;
        public static int mtrl_alert_dialog_background_inset_bottom = 0x7f0700da;
        public static int mtrl_alert_dialog_background_inset_end = 0x7f0700db;
        public static int mtrl_alert_dialog_background_inset_start = 0x7f0700dc;
        public static int mtrl_alert_dialog_background_inset_top = 0x7f0700dd;
        public static int mtrl_alert_dialog_picker_background_inset = 0x7f0700de;
        public static int mtrl_badge_horizontal_edge_offset = 0x7f0700df;
        public static int mtrl_badge_long_text_horizontal_padding = 0x7f0700e0;
        public static int mtrl_badge_radius = 0x7f0700e1;
        public static int mtrl_badge_text_horizontal_edge_offset = 0x7f0700e2;
        public static int mtrl_badge_text_size = 0x7f0700e3;
        public static int mtrl_badge_with_text_radius = 0x7f0700e4;
        public static int mtrl_bottomappbar_fabOffsetEndMode = 0x7f0700e5;
        public static int mtrl_bottomappbar_fab_bottom_margin = 0x7f0700e6;
        public static int mtrl_bottomappbar_fab_cradle_margin = 0x7f0700e7;
        public static int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e8;
        public static int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f0700e9;
        public static int mtrl_bottomappbar_height = 0x7f0700ea;
        public static int mtrl_btn_corner_radius = 0x7f0700eb;
        public static int mtrl_btn_dialog_btn_min_width = 0x7f0700ec;
        public static int mtrl_btn_disabled_elevation = 0x7f0700ed;
        public static int mtrl_btn_disabled_z = 0x7f0700ee;
        public static int mtrl_btn_elevation = 0x7f0700ef;
        public static int mtrl_btn_focused_z = 0x7f0700f0;
        public static int mtrl_btn_hovered_z = 0x7f0700f1;
        public static int mtrl_btn_icon_btn_padding_left = 0x7f0700f2;
        public static int mtrl_btn_icon_padding = 0x7f0700f3;
        public static int mtrl_btn_inset = 0x7f0700f4;
        public static int mtrl_btn_letter_spacing = 0x7f0700f5;
        public static int mtrl_btn_padding_bottom = 0x7f0700f6;
        public static int mtrl_btn_padding_left = 0x7f0700f7;
        public static int mtrl_btn_padding_right = 0x7f0700f8;
        public static int mtrl_btn_padding_top = 0x7f0700f9;
        public static int mtrl_btn_pressed_z = 0x7f0700fa;
        public static int mtrl_btn_stroke_size = 0x7f0700fb;
        public static int mtrl_btn_text_btn_icon_padding = 0x7f0700fc;
        public static int mtrl_btn_text_btn_padding_left = 0x7f0700fd;
        public static int mtrl_btn_text_btn_padding_right = 0x7f0700fe;
        public static int mtrl_btn_text_size = 0x7f0700ff;
        public static int mtrl_btn_z = 0x7f070100;
        public static int mtrl_calendar_action_height = 0x7f070101;
        public static int mtrl_calendar_action_padding = 0x7f070102;
        public static int mtrl_calendar_bottom_padding = 0x7f070103;
        public static int mtrl_calendar_content_padding = 0x7f070104;
        public static int mtrl_calendar_day_corner = 0x7f070105;
        public static int mtrl_calendar_day_height = 0x7f070106;
        public static int mtrl_calendar_day_horizontal_padding = 0x7f070107;
        public static int mtrl_calendar_day_today_stroke = 0x7f070108;
        public static int mtrl_calendar_day_vertical_padding = 0x7f070109;
        public static int mtrl_calendar_day_width = 0x7f07010a;
        public static int mtrl_calendar_days_of_week_height = 0x7f07010b;
        public static int mtrl_calendar_dialog_background_inset = 0x7f07010c;
        public static int mtrl_calendar_header_content_padding = 0x7f07010d;
        public static int mtrl_calendar_header_content_padding_fullscreen = 0x7f07010e;
        public static int mtrl_calendar_header_divider_thickness = 0x7f07010f;
        public static int mtrl_calendar_header_height = 0x7f070110;
        public static int mtrl_calendar_header_height_fullscreen = 0x7f070111;
        public static int mtrl_calendar_header_selection_line_height = 0x7f070112;
        public static int mtrl_calendar_header_text_padding = 0x7f070113;
        public static int mtrl_calendar_header_toggle_margin_bottom = 0x7f070114;
        public static int mtrl_calendar_header_toggle_margin_top = 0x7f070115;
        public static int mtrl_calendar_landscape_header_width = 0x7f070116;
        public static int mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f070117;
        public static int mtrl_calendar_month_horizontal_padding = 0x7f070118;
        public static int mtrl_calendar_month_vertical_padding = 0x7f070119;
        public static int mtrl_calendar_navigation_bottom_padding = 0x7f07011a;
        public static int mtrl_calendar_navigation_height = 0x7f07011b;
        public static int mtrl_calendar_navigation_top_padding = 0x7f07011c;
        public static int mtrl_calendar_pre_l_text_clip_padding = 0x7f07011d;
        public static int mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f07011e;
        public static int mtrl_calendar_selection_text_baseline_to_bottom = 0x7f07011f;
        public static int mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f070120;
        public static int mtrl_calendar_selection_text_baseline_to_top = 0x7f070121;
        public static int mtrl_calendar_text_input_padding_top = 0x7f070122;
        public static int mtrl_calendar_title_baseline_to_top = 0x7f070123;
        public static int mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f070124;
        public static int mtrl_calendar_year_corner = 0x7f070125;
        public static int mtrl_calendar_year_height = 0x7f070126;
        public static int mtrl_calendar_year_horizontal_padding = 0x7f070127;
        public static int mtrl_calendar_year_vertical_padding = 0x7f070128;
        public static int mtrl_calendar_year_width = 0x7f070129;
        public static int mtrl_card_checked_icon_margin = 0x7f07012a;
        public static int mtrl_card_checked_icon_size = 0x7f07012b;
        public static int mtrl_card_corner_radius = 0x7f07012c;
        public static int mtrl_card_dragged_z = 0x7f07012d;
        public static int mtrl_card_elevation = 0x7f07012e;
        public static int mtrl_card_spacing = 0x7f07012f;
        public static int mtrl_chip_pressed_translation_z = 0x7f070130;
        public static int mtrl_chip_text_size = 0x7f070131;
        public static int mtrl_exposed_dropdown_menu_popup_elevation = 0x7f070132;
        public static int mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f070133;
        public static int mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f070134;
        public static int mtrl_extended_fab_bottom_padding = 0x7f070135;
        public static int mtrl_extended_fab_corner_radius = 0x7f070136;
        public static int mtrl_extended_fab_disabled_elevation = 0x7f070137;
        public static int mtrl_extended_fab_disabled_translation_z = 0x7f070138;
        public static int mtrl_extended_fab_elevation = 0x7f070139;
        public static int mtrl_extended_fab_end_padding = 0x7f07013a;
        public static int mtrl_extended_fab_end_padding_icon = 0x7f07013b;
        public static int mtrl_extended_fab_icon_size = 0x7f07013c;
        public static int mtrl_extended_fab_icon_text_spacing = 0x7f07013d;
        public static int mtrl_extended_fab_min_height = 0x7f07013e;
        public static int mtrl_extended_fab_min_width = 0x7f07013f;
        public static int mtrl_extended_fab_start_padding = 0x7f070140;
        public static int mtrl_extended_fab_start_padding_icon = 0x7f070141;
        public static int mtrl_extended_fab_top_padding = 0x7f070142;
        public static int mtrl_extended_fab_translation_z_base = 0x7f070143;
        public static int mtrl_extended_fab_translation_z_hovered_focused = 0x7f070144;
        public static int mtrl_extended_fab_translation_z_pressed = 0x7f070145;
        public static int mtrl_fab_elevation = 0x7f070146;
        public static int mtrl_fab_min_touch_target = 0x7f070147;
        public static int mtrl_fab_translation_z_hovered_focused = 0x7f070148;
        public static int mtrl_fab_translation_z_pressed = 0x7f070149;
        public static int mtrl_high_ripple_default_alpha = 0x7f07014a;
        public static int mtrl_high_ripple_focused_alpha = 0x7f07014b;
        public static int mtrl_high_ripple_hovered_alpha = 0x7f07014c;
        public static int mtrl_high_ripple_pressed_alpha = 0x7f07014d;
        public static int mtrl_large_touch_target = 0x7f07014e;
        public static int mtrl_low_ripple_default_alpha = 0x7f07014f;
        public static int mtrl_low_ripple_focused_alpha = 0x7f070150;
        public static int mtrl_low_ripple_hovered_alpha = 0x7f070151;
        public static int mtrl_low_ripple_pressed_alpha = 0x7f070152;
        public static int mtrl_min_touch_target_size = 0x7f070153;
        public static int mtrl_navigation_elevation = 0x7f070154;
        public static int mtrl_navigation_item_horizontal_padding = 0x7f070155;
        public static int mtrl_navigation_item_icon_padding = 0x7f070156;
        public static int mtrl_navigation_item_icon_size = 0x7f070157;
        public static int mtrl_navigation_item_shape_horizontal_margin = 0x7f070158;
        public static int mtrl_navigation_item_shape_vertical_margin = 0x7f070159;
        public static int mtrl_shape_corner_size_large_component = 0x7f07015a;
        public static int mtrl_shape_corner_size_medium_component = 0x7f07015b;
        public static int mtrl_shape_corner_size_small_component = 0x7f07015c;
        public static int mtrl_snackbar_action_text_color_alpha = 0x7f07015d;
        public static int mtrl_snackbar_background_corner_radius = 0x7f07015e;
        public static int mtrl_snackbar_background_overlay_color_alpha = 0x7f07015f;
        public static int mtrl_snackbar_margin = 0x7f070160;
        public static int mtrl_switch_thumb_elevation = 0x7f070161;
        public static int mtrl_textinput_box_corner_radius_medium = 0x7f070162;
        public static int mtrl_textinput_box_corner_radius_small = 0x7f070163;
        public static int mtrl_textinput_box_label_cutout_padding = 0x7f070164;
        public static int mtrl_textinput_box_stroke_width_default = 0x7f070165;
        public static int mtrl_textinput_box_stroke_width_focused = 0x7f070166;
        public static int mtrl_textinput_end_icon_margin_start = 0x7f070167;
        public static int mtrl_textinput_outline_box_expanded_padding = 0x7f070168;
        public static int mtrl_textinput_start_icon_margin_end = 0x7f070169;
        public static int mtrl_toolbar_default_height = 0x7f07016a;
        public static int notification_action_icon_size = 0x7f07016b;
        public static int notification_action_text_size = 0x7f07016c;
        public static int notification_big_circle_margin = 0x7f07016d;
        public static int notification_content_margin_start = 0x7f07016e;
        public static int notification_large_icon_height = 0x7f07016f;
        public static int notification_large_icon_width = 0x7f070170;
        public static int notification_main_column_padding_top = 0x7f070171;
        public static int notification_media_narrow_margin = 0x7f070172;
        public static int notification_right_icon_size = 0x7f070173;
        public static int notification_right_side_padding_top = 0x7f070174;
        public static int notification_small_icon_background_padding = 0x7f070175;
        public static int notification_small_icon_size_as_large = 0x7f070176;
        public static int notification_subtext_size = 0x7f070177;
        public static int notification_top_pad = 0x7f070178;
        public static int notification_top_pad_large_text = 0x7f070179;
        public static int test_mtrl_calendar_day_cornerSize = 0x7f070185;
        public static int tooltip_corner_radius = 0x7f070186;
        public static int tooltip_horizontal_padding = 0x7f070187;
        public static int tooltip_margin = 0x7f070188;
        public static int tooltip_precise_anchor_extra_offset = 0x7f070189;
        public static int tooltip_precise_anchor_threshold = 0x7f07018a;
        public static int tooltip_vertical_padding = 0x7f07018b;
        public static int tooltip_y_offset_non_touch = 0x7f07018c;
        public static int tooltip_y_offset_touch = 0x7f07018d;

        private dimen() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$drawable.smali */
    public static final class drawable {
        public static int abc_ab_share_pack_mtrl_alpha = 0x7f080076;
        public static int abc_action_bar_item_background_material = 0x7f080077;
        public static int abc_btn_borderless_material = 0x7f080078;
        public static int abc_btn_check_material = 0x7f080079;
        public static int abc_btn_check_material_anim = 0x7f08007a;
        public static int abc_btn_check_to_on_mtrl_000 = 0x7f08007b;
        public static int abc_btn_check_to_on_mtrl_015 = 0x7f08007c;
        public static int abc_btn_colored_material = 0x7f08007d;
        public static int abc_btn_default_mtrl_shape = 0x7f08007e;
        public static int abc_btn_radio_material = 0x7f08007f;
        public static int abc_btn_radio_material_anim = 0x7f080080;
        public static int abc_btn_radio_to_on_mtrl_000 = 0x7f080081;
        public static int abc_btn_radio_to_on_mtrl_015 = 0x7f080082;
        public static int abc_btn_switch_to_on_mtrl_00001 = 0x7f080083;
        public static int abc_btn_switch_to_on_mtrl_00012 = 0x7f080084;
        public static int abc_cab_background_internal_bg = 0x7f080085;
        public static int abc_cab_background_top_material = 0x7f080086;
        public static int abc_cab_background_top_mtrl_alpha = 0x7f080087;
        public static int abc_control_background_material = 0x7f080088;
        public static int abc_dialog_material_background = 0x7f080089;
        public static int abc_edit_text_material = 0x7f08008a;
        public static int abc_ic_ab_back_material = 0x7f08008b;
        public static int abc_ic_arrow_drop_right_black_24dp = 0x7f08008c;
        public static int abc_ic_clear_material = 0x7f08008d;
        public static int abc_ic_commit_search_api_mtrl_alpha = 0x7f08008e;
        public static int abc_ic_go_search_api_material = 0x7f08008f;
        public static int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080090;
        public static int abc_ic_menu_cut_mtrl_alpha = 0x7f080091;
        public static int abc_ic_menu_overflow_material = 0x7f080092;
        public static int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080093;
        public static int abc_ic_menu_selectall_mtrl_alpha = 0x7f080094;
        public static int abc_ic_menu_share_mtrl_alpha = 0x7f080095;
        public static int abc_ic_search_api_material = 0x7f080096;
        public static int abc_ic_voice_search_api_material = 0x7f080097;
        public static int abc_item_background_holo_dark = 0x7f080098;
        public static int abc_item_background_holo_light = 0x7f080099;
        public static int abc_list_divider_material = 0x7f08009a;
        public static int abc_list_divider_mtrl_alpha = 0x7f08009b;
        public static int abc_list_focused_holo = 0x7f08009c;
        public static int abc_list_longpressed_holo = 0x7f08009d;
        public static int abc_list_pressed_holo_dark = 0x7f08009e;
        public static int abc_list_pressed_holo_light = 0x7f08009f;
        public static int abc_list_selector_background_transition_holo_dark = 0x7f0800a0;
        public static int abc_list_selector_background_transition_holo_light = 0x7f0800a1;
        public static int abc_list_selector_disabled_holo_dark = 0x7f0800a2;
        public static int abc_list_selector_disabled_holo_light = 0x7f0800a3;
        public static int abc_list_selector_holo_dark = 0x7f0800a4;
        public static int abc_list_selector_holo_light = 0x7f0800a5;
        public static int abc_menu_hardkey_panel_mtrl_mult = 0x7f0800a6;
        public static int abc_popup_background_mtrl_mult = 0x7f0800a7;
        public static int abc_ratingbar_indicator_material = 0x7f0800a8;
        public static int abc_ratingbar_material = 0x7f0800a9;
        public static int abc_ratingbar_small_material = 0x7f0800aa;
        public static int abc_scrubber_control_off_mtrl_alpha = 0x7f0800ab;
        public static int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f0800ac;
        public static int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f0800ad;
        public static int abc_scrubber_primary_mtrl_alpha = 0x7f0800ae;
        public static int abc_scrubber_track_mtrl_alpha = 0x7f0800af;
        public static int abc_seekbar_thumb_material = 0x7f0800b0;
        public static int abc_seekbar_tick_mark_material = 0x7f0800b1;
        public static int abc_seekbar_track_material = 0x7f0800b2;
        public static int abc_spinner_mtrl_am_alpha = 0x7f0800b3;
        public static int abc_spinner_textfield_background_material = 0x7f0800b4;
        public static int abc_switch_thumb_material = 0x7f0800b7;
        public static int abc_switch_track_mtrl_alpha = 0x7f0800b8;
        public static int abc_tab_indicator_material = 0x7f0800b9;
        public static int abc_tab_indicator_mtrl_alpha = 0x7f0800ba;
        public static int abc_text_cursor_material = 0x7f0800bb;
        public static int abc_textfield_activated_mtrl_alpha = 0x7f0800bf;
        public static int abc_textfield_default_mtrl_alpha = 0x7f0800c0;
        public static int abc_textfield_search_activated_mtrl_alpha = 0x7f0800c1;
        public static int abc_textfield_search_default_mtrl_alpha = 0x7f0800c2;
        public static int abc_textfield_search_material = 0x7f0800c3;
        public static int abc_vector_test = 0x7f0800c4;
        public static int avd_hide_password = 0x7f080112;
        public static int avd_show_password = 0x7f080113;
        public static int btn_checkbox_checked_mtrl = 0x7f080114;
        public static int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080115;
        public static int btn_checkbox_unchecked_mtrl = 0x7f080116;
        public static int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080117;
        public static int btn_radio_off_mtrl = 0x7f080118;
        public static int btn_radio_off_to_on_mtrl_animation = 0x7f080119;
        public static int btn_radio_on_mtrl = 0x7f08011a;
        public static int btn_radio_on_to_off_mtrl_animation = 0x7f08011b;
        public static int design_bottom_navigation_item_background = 0x7f080131;
        public static int design_fab_background = 0x7f080132;
        public static int design_ic_visibility = 0x7f080133;
        public static int design_ic_visibility_off = 0x7f080134;
        public static int design_password_eye = 0x7f080135;
        public static int design_snackbar_background = 0x7f080136;
        public static int ic_calendar_black_24dp = 0x7f08013b;
        public static int ic_clear_black_24dp = 0x7f080142;
        public static int ic_edit_black_24dp = 0x7f080144;
        public static int ic_keyboard_arrow_left_black_24dp = 0x7f080145;
        public static int ic_keyboard_arrow_right_black_24dp = 0x7f080146;
        public static int ic_menu_arrow_down_black_24dp = 0x7f080149;
        public static int ic_menu_arrow_up_black_24dp = 0x7f08014a;
        public static int ic_mtrl_checked_circle = 0x7f08014b;
        public static int ic_mtrl_chip_checked_black = 0x7f08014c;
        public static int ic_mtrl_chip_checked_circle = 0x7f08014d;
        public static int ic_mtrl_chip_close_circle = 0x7f08014e;
        public static int mtrl_dialog_background = 0x7f080153;
        public static int mtrl_dropdown_arrow = 0x7f080154;
        public static int mtrl_ic_arrow_drop_down = 0x7f080155;
        public static int mtrl_ic_arrow_drop_up = 0x7f080156;
        public static int mtrl_ic_cancel = 0x7f080157;
        public static int mtrl_ic_error = 0x7f080158;
        public static int mtrl_popupmenu_background = 0x7f080159;
        public static int mtrl_popupmenu_background_dark = 0x7f08015a;
        public static int mtrl_tabs_default_indicator = 0x7f08015b;
        public static int navigation_empty_icon = 0x7f08015c;
        public static int notification_action_background = 0x7f08015d;
        public static int notification_bg = 0x7f08015e;
        public static int notification_bg_low = 0x7f08015f;
        public static int notification_bg_low_normal = 0x7f080160;
        public static int notification_bg_low_pressed = 0x7f080161;
        public static int notification_bg_normal = 0x7f080162;
        public static int notification_bg_normal_pressed = 0x7f080163;
        public static int notification_icon_background = 0x7f080164;
        public static int notification_template_icon_bg = 0x7f080166;
        public static int notification_template_icon_low_bg = 0x7f080167;
        public static int notification_tile_bg = 0x7f080168;
        public static int notify_panel_notification_icon_bg = 0x7f080169;
        public static int test_custom_background = 0x7f08016b;
        public static int tooltip_frame_dark = 0x7f08016d;
        public static int tooltip_frame_light = 0x7f08016e;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$id.smali */
    public static final class id {
        public static int BOTTOM_END = 0x7f0a0001;
        public static int BOTTOM_START = 0x7f0a0002;
        public static int TOP_END = 0x7f0a000b;
        public static int TOP_START = 0x7f0a000c;
        public static int accessibility_action_clickable_span = 0x7f0a000d;
        public static int accessibility_custom_action_0 = 0x7f0a000e;
        public static int accessibility_custom_action_1 = 0x7f0a000f;
        public static int accessibility_custom_action_10 = 0x7f0a0010;
        public static int accessibility_custom_action_11 = 0x7f0a0011;
        public static int accessibility_custom_action_12 = 0x7f0a0012;
        public static int accessibility_custom_action_13 = 0x7f0a0013;
        public static int accessibility_custom_action_14 = 0x7f0a0014;
        public static int accessibility_custom_action_15 = 0x7f0a0015;
        public static int accessibility_custom_action_16 = 0x7f0a0016;
        public static int accessibility_custom_action_17 = 0x7f0a0017;
        public static int accessibility_custom_action_18 = 0x7f0a0018;
        public static int accessibility_custom_action_19 = 0x7f0a0019;
        public static int accessibility_custom_action_2 = 0x7f0a001a;
        public static int accessibility_custom_action_20 = 0x7f0a001b;
        public static int accessibility_custom_action_21 = 0x7f0a001c;
        public static int accessibility_custom_action_22 = 0x7f0a001d;
        public static int accessibility_custom_action_23 = 0x7f0a001e;
        public static int accessibility_custom_action_24 = 0x7f0a001f;
        public static int accessibility_custom_action_25 = 0x7f0a0020;
        public static int accessibility_custom_action_26 = 0x7f0a0021;
        public static int accessibility_custom_action_27 = 0x7f0a0022;
        public static int accessibility_custom_action_28 = 0x7f0a0023;
        public static int accessibility_custom_action_29 = 0x7f0a0024;
        public static int accessibility_custom_action_3 = 0x7f0a0025;
        public static int accessibility_custom_action_30 = 0x7f0a0026;
        public static int accessibility_custom_action_31 = 0x7f0a0027;
        public static int accessibility_custom_action_4 = 0x7f0a0028;
        public static int accessibility_custom_action_5 = 0x7f0a0029;
        public static int accessibility_custom_action_6 = 0x7f0a002a;
        public static int accessibility_custom_action_7 = 0x7f0a002b;
        public static int accessibility_custom_action_8 = 0x7f0a002c;
        public static int accessibility_custom_action_9 = 0x7f0a002d;
        public static int action_bar = 0x7f0a0031;
        public static int action_bar_activity_content = 0x7f0a0032;
        public static int action_bar_container = 0x7f0a0033;
        public static int action_bar_root = 0x7f0a0034;
        public static int action_bar_spinner = 0x7f0a0035;
        public static int action_bar_subtitle = 0x7f0a0036;
        public static int action_bar_title = 0x7f0a0037;
        public static int action_container = 0x7f0a0038;
        public static int action_context_bar = 0x7f0a0039;
        public static int action_divider = 0x7f0a003a;
        public static int action_image = 0x7f0a003b;
        public static int action_menu_divider = 0x7f0a003c;
        public static int action_menu_presenter = 0x7f0a003d;
        public static int action_mode_bar = 0x7f0a003e;
        public static int action_mode_bar_stub = 0x7f0a003f;
        public static int action_mode_close_button = 0x7f0a0040;
        public static int action_text = 0x7f0a0041;
        public static int actions = 0x7f0a0042;
        public static int activity_chooser_view_content = 0x7f0a0043;
        public static int add = 0x7f0a0044;
        public static int alertTitle = 0x7f0a0047;
        public static int async = 0x7f0a005b;
        public static int auto = 0x7f0a005c;
        public static int blocking = 0x7f0a005f;
        public static int bottom = 0x7f0a0060;
        public static int buttonPanel = 0x7f0a0061;
        public static int cancel_button = 0x7f0a0064;
        public static int center = 0x7f0a0065;
        public static int checkbox = 0x7f0a006a;
        public static int checked = 0x7f0a006b;
        public static int chip = 0x7f0a006c;
        public static int chip_group = 0x7f0a006d;
        public static int chronometer = 0x7f0a006e;
        public static int clear_text = 0x7f0a006f;
        public static int confirm_button = 0x7f0a0074;
        public static int container = 0x7f0a0075;
        public static int content = 0x7f0a0076;
        public static int contentPanel = 0x7f0a0077;
        public static int coordinator = 0x7f0a0078;
        public static int custom = 0x7f0a0079;
        public static int customPanel = 0x7f0a007a;
        public static int cut = 0x7f0a007b;
        public static int date_picker_actions = 0x7f0a007d;
        public static int decor_content_parent = 0x7f0a007e;
        public static int default_activity_button = 0x7f0a007f;
        public static int design_bottom_sheet = 0x7f0a0081;
        public static int design_menu_item_action_area = 0x7f0a0082;
        public static int design_menu_item_action_area_stub = 0x7f0a0083;
        public static int design_menu_item_text = 0x7f0a0084;
        public static int design_navigation_view = 0x7f0a0085;
        public static int dialog_button = 0x7f0a0087;
        public static int dropdown_menu = 0x7f0a008b;
        public static int edit_query = 0x7f0a008c;
        public static int end = 0x7f0a008e;
        public static int expand_activities_button = 0x7f0a0093;
        public static int expanded_menu = 0x7f0a0094;
        public static int fade = 0x7f0a0095;
        public static int fill = 0x7f0a0096;
        public static int filled = 0x7f0a0099;
        public static int filter_chip = 0x7f0a009a;
        public static int fixed = 0x7f0a00a2;
        public static int forever = 0x7f0a00a3;
        public static int ghost_view = 0x7f0a00a6;
        public static int ghost_view_holder = 0x7f0a00a7;
        public static int group_divider = 0x7f0a00a9;
        public static int home = 0x7f0a00ae;
        public static int icon = 0x7f0a00b1;
        public static int icon_group = 0x7f0a00b2;
        public static int image = 0x7f0a00b5;
        public static int info = 0x7f0a00b6;
        public static int italic = 0x7f0a00b8;
        public static int item_touch_helper_previous_elevation = 0x7f0a00b9;
        public static int labeled = 0x7f0a00ba;
        public static int largeLabel = 0x7f0a00bb;
        public static int left = 0x7f0a00bd;
        public static int line1 = 0x7f0a00bf;
        public static int line3 = 0x7f0a00c0;
        public static int listMode = 0x7f0a00c1;
        public static int list_item = 0x7f0a00c2;
        public static int masked = 0x7f0a00c3;
        public static int message = 0x7f0a00c5;
        public static int mini = 0x7f0a00c7;
        public static int month_grid = 0x7f0a00c8;
        public static int month_navigation_bar = 0x7f0a00c9;
        public static int month_navigation_fragment_toggle = 0x7f0a00ca;
        public static int month_navigation_next = 0x7f0a00cb;
        public static int month_navigation_previous = 0x7f0a00cc;
        public static int month_title = 0x7f0a00cd;
        public static int mtrl_calendar_day_selector_frame = 0x7f0a00ce;
        public static int mtrl_calendar_days_of_week = 0x7f0a00cf;
        public static int mtrl_calendar_frame = 0x7f0a00d0;
        public static int mtrl_calendar_main_pane = 0x7f0a00d1;
        public static int mtrl_calendar_months = 0x7f0a00d2;
        public static int mtrl_calendar_selection_frame = 0x7f0a00d3;
        public static int mtrl_calendar_text_input_frame = 0x7f0a00d4;
        public static int mtrl_calendar_year_selector_frame = 0x7f0a00d5;
        public static int mtrl_card_checked_layer_id = 0x7f0a00d6;
        public static int mtrl_child_content_container = 0x7f0a00d7;
        public static int mtrl_internal_children_alpha_tag = 0x7f0a00d8;
        public static int mtrl_picker_fullscreen = 0x7f0a00d9;
        public static int mtrl_picker_header = 0x7f0a00da;
        public static int mtrl_picker_header_selection_text = 0x7f0a00db;
        public static int mtrl_picker_header_title_and_selection = 0x7f0a00dc;
        public static int mtrl_picker_header_toggle = 0x7f0a00dd;
        public static int mtrl_picker_text_input_date = 0x7f0a00de;
        public static int mtrl_picker_text_input_range_end = 0x7f0a00df;
        public static int mtrl_picker_text_input_range_start = 0x7f0a00e0;
        public static int mtrl_picker_title_text = 0x7f0a00e1;
        public static int multiply = 0x7f0a00e2;
        public static int navigation_header_container = 0x7f0a00e5;
        public static int none = 0x7f0a00e9;
        public static int normal = 0x7f0a00ea;
        public static int notification_background = 0x7f0a00eb;
        public static int notification_main_column = 0x7f0a00ec;
        public static int notification_main_column_container = 0x7f0a00ed;
        public static int off = 0x7f0a00ee;
        public static int on = 0x7f0a00ef;
        public static int outline = 0x7f0a00f0;
        public static int parallax = 0x7f0a00f3;
        public static int parentPanel = 0x7f0a00f5;
        public static int parent_matrix = 0x7f0a00f6;
        public static int password_toggle = 0x7f0a00f7;
        public static int pin = 0x7f0a00fa;
        public static int progress_circular = 0x7f0a00fb;
        public static int progress_horizontal = 0x7f0a00fc;
        public static int radio = 0x7f0a00fd;
        public static int right = 0x7f0a00ff;
        public static int right_icon = 0x7f0a0100;
        public static int right_side = 0x7f0a0101;
        public static int rounded = 0x7f0a0102;
        public static int save_non_transition_alpha = 0x7f0a0103;
        public static int save_overlay_view = 0x7f0a0104;
        public static int scale = 0x7f0a0105;
        public static int screen = 0x7f0a0106;
        public static int scrollIndicatorDown = 0x7f0a0108;
        public static int scrollIndicatorUp = 0x7f0a0109;
        public static int scrollView = 0x7f0a010a;
        public static int scrollable = 0x7f0a010b;
        public static int search_badge = 0x7f0a010c;
        public static int search_bar = 0x7f0a010d;
        public static int search_button = 0x7f0a010e;
        public static int search_close_btn = 0x7f0a010f;
        public static int search_edit_frame = 0x7f0a0110;
        public static int search_go_btn = 0x7f0a0111;
        public static int search_mag_icon = 0x7f0a0112;
        public static int search_plate = 0x7f0a0113;
        public static int search_src_text = 0x7f0a0114;
        public static int search_voice_btn = 0x7f0a0115;
        public static int select_dialog_listview = 0x7f0a0116;
        public static int selected = 0x7f0a0117;
        public static int shortcut = 0x7f0a011c;
        public static int slide = 0x7f0a0121;
        public static int smallLabel = 0x7f0a0122;
        public static int snackbar_action = 0x7f0a0123;
        public static int snackbar_text = 0x7f0a0124;
        public static int spacer = 0x7f0a0127;
        public static int split_action_bar = 0x7f0a012a;
        public static int src_atop = 0x7f0a012d;
        public static int src_in = 0x7f0a012e;
        public static int src_over = 0x7f0a012f;
        public static int start = 0x7f0a0131;
        public static int stretch = 0x7f0a0133;
        public static int submenuarrow = 0x7f0a0134;
        public static int submit_area = 0x7f0a0135;
        public static int tabMode = 0x7f0a0136;
        public static int tag_accessibility_actions = 0x7f0a0138;
        public static int tag_accessibility_clickable_spans = 0x7f0a0139;
        public static int tag_accessibility_heading = 0x7f0a013a;
        public static int tag_accessibility_pane_title = 0x7f0a013b;
        public static int tag_screen_reader_focusable = 0x7f0a013f;
        public static int tag_transition_group = 0x7f0a0141;
        public static int tag_unhandled_key_event_manager = 0x7f0a0142;
        public static int tag_unhandled_key_listeners = 0x7f0a0143;
        public static int test_checkbox_android_button_tint = 0x7f0a0145;
        public static int test_checkbox_app_button_tint = 0x7f0a0146;
        public static int text = 0x7f0a0147;
        public static int text2 = 0x7f0a0148;
        public static int textSpacerNoButtons = 0x7f0a014a;
        public static int textSpacerNoTitle = 0x7f0a014b;
        public static int text_input_end_icon = 0x7f0a014e;
        public static int text_input_start_icon = 0x7f0a014f;
        public static int textinput_counter = 0x7f0a0150;
        public static int textinput_error = 0x7f0a0151;
        public static int textinput_helper_text = 0x7f0a0152;
        public static int time = 0x7f0a0153;
        public static int title = 0x7f0a0154;
        public static int titleDividerNoCustom = 0x7f0a0155;
        public static int title_template = 0x7f0a0156;
        public static int top = 0x7f0a0158;
        public static int topPanel = 0x7f0a0159;
        public static int touch_outside = 0x7f0a015a;
        public static int transition_current_scene = 0x7f0a015d;
        public static int transition_layout_save = 0x7f0a015e;
        public static int transition_position = 0x7f0a015f;
        public static int transition_scene_layoutid_cache = 0x7f0a0160;
        public static int transition_transform = 0x7f0a0161;
        public static int unchecked = 0x7f0a0162;
        public static int uniform = 0x7f0a0163;
        public static int unlabeled = 0x7f0a0164;
        public static int up = 0x7f0a0165;
        public static int view_offset_helper = 0x7f0a0168;
        public static int visible = 0x7f0a016d;
        public static int wrap_content = 0x7f0a0174;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$integer.smali */
    public static final class integer {
        public static int abc_config_activityDefaultDur = 0x7f0b0000;
        public static int abc_config_activityShortDur = 0x7f0b0001;
        public static int app_bar_elevation_anim_duration = 0x7f0b0005;
        public static int bottom_sheet_slide_duration = 0x7f0b0006;
        public static int cancel_button_image_alpha = 0x7f0b0007;
        public static int config_tooltipAnimTime = 0x7f0b0008;
        public static int design_snackbar_text_max_lines = 0x7f0b000a;
        public static int design_tab_indicator_anim_duration_ms = 0x7f0b000b;
        public static int hide_password_duration = 0x7f0b000d;
        public static int mtrl_badge_max_character_count = 0x7f0b000e;
        public static int mtrl_btn_anim_delay_ms = 0x7f0b000f;
        public static int mtrl_btn_anim_duration_ms = 0x7f0b0010;
        public static int mtrl_calendar_header_orientation = 0x7f0b0011;
        public static int mtrl_calendar_selection_text_lines = 0x7f0b0012;
        public static int mtrl_calendar_year_selector_span = 0x7f0b0013;
        public static int mtrl_card_anim_delay_ms = 0x7f0b0014;
        public static int mtrl_card_anim_duration_ms = 0x7f0b0015;
        public static int mtrl_chip_anim_duration = 0x7f0b0016;
        public static int mtrl_tab_indicator_anim_duration_ms = 0x7f0b0017;
        public static int show_password_duration = 0x7f0b0018;
        public static int status_bar_notification_info_maxnum = 0x7f0b0019;

        private integer() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$interpolator.smali */
    public static final class interpolator {
        public static int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000;
        public static int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003;
        public static int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004;
        public static int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005;
        public static int fast_out_slow_in = 0x7f0c0006;
        public static int mtrl_fast_out_linear_in = 0x7f0c0007;
        public static int mtrl_fast_out_slow_in = 0x7f0c0008;
        public static int mtrl_linear = 0x7f0c0009;
        public static int mtrl_linear_out_slow_in = 0x7f0c000a;

        private interpolator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$layout.smali */
    public static final class layout {
        public static int abc_action_bar_title_item = 0x7f0d0000;
        public static int abc_action_bar_up_container = 0x7f0d0001;
        public static int abc_action_menu_item_layout = 0x7f0d0002;
        public static int abc_action_menu_layout = 0x7f0d0003;
        public static int abc_action_mode_bar = 0x7f0d0004;
        public static int abc_action_mode_close_item_material = 0x7f0d0005;
        public static int abc_activity_chooser_view = 0x7f0d0006;
        public static int abc_activity_chooser_view_list_item = 0x7f0d0007;
        public static int abc_alert_dialog_button_bar_material = 0x7f0d0008;
        public static int abc_alert_dialog_material = 0x7f0d0009;
        public static int abc_alert_dialog_title_material = 0x7f0d000a;
        public static int abc_cascading_menu_item_layout = 0x7f0d000b;
        public static int abc_dialog_title_material = 0x7f0d000c;
        public static int abc_expanded_menu_layout = 0x7f0d000d;
        public static int abc_list_menu_item_checkbox = 0x7f0d000e;
        public static int abc_list_menu_item_icon = 0x7f0d000f;
        public static int abc_list_menu_item_layout = 0x7f0d0010;
        public static int abc_list_menu_item_radio = 0x7f0d0011;
        public static int abc_popup_menu_header_item_layout = 0x7f0d0012;
        public static int abc_popup_menu_item_layout = 0x7f0d0013;
        public static int abc_screen_content_include = 0x7f0d0014;
        public static int abc_screen_simple = 0x7f0d0015;
        public static int abc_screen_simple_overlay_action_mode = 0x7f0d0016;
        public static int abc_screen_toolbar = 0x7f0d0017;
        public static int abc_search_dropdown_item_icons_2line = 0x7f0d0018;
        public static int abc_search_view = 0x7f0d0019;
        public static int abc_select_dialog_material = 0x7f0d001a;
        public static int abc_tooltip = 0x7f0d001b;
        public static int custom_dialog = 0x7f0d0026;
        public static int design_bottom_navigation_item = 0x7f0d0027;
        public static int design_bottom_sheet_dialog = 0x7f0d0028;
        public static int design_layout_snackbar = 0x7f0d0029;
        public static int design_layout_snackbar_include = 0x7f0d002a;
        public static int design_layout_tab_icon = 0x7f0d002b;
        public static int design_layout_tab_text = 0x7f0d002c;
        public static int design_menu_item_action_area = 0x7f0d002d;
        public static int design_navigation_item = 0x7f0d002e;
        public static int design_navigation_item_header = 0x7f0d002f;
        public static int design_navigation_item_separator = 0x7f0d0030;
        public static int design_navigation_item_subheader = 0x7f0d0031;
        public static int design_navigation_menu = 0x7f0d0032;
        public static int design_navigation_menu_item = 0x7f0d0033;
        public static int design_text_input_end_icon = 0x7f0d0034;
        public static int design_text_input_start_icon = 0x7f0d0035;
        public static int mtrl_alert_dialog = 0x7f0d003e;
        public static int mtrl_alert_dialog_actions = 0x7f0d003f;
        public static int mtrl_alert_dialog_title = 0x7f0d0040;
        public static int mtrl_alert_select_dialog_item = 0x7f0d0041;
        public static int mtrl_alert_select_dialog_multichoice = 0x7f0d0042;
        public static int mtrl_alert_select_dialog_singlechoice = 0x7f0d0043;
        public static int mtrl_calendar_day = 0x7f0d0044;
        public static int mtrl_calendar_day_of_week = 0x7f0d0045;
        public static int mtrl_calendar_days_of_week = 0x7f0d0046;
        public static int mtrl_calendar_horizontal = 0x7f0d0047;
        public static int mtrl_calendar_month = 0x7f0d0048;
        public static int mtrl_calendar_month_labeled = 0x7f0d0049;
        public static int mtrl_calendar_month_navigation = 0x7f0d004a;
        public static int mtrl_calendar_months = 0x7f0d004b;
        public static int mtrl_calendar_vertical = 0x7f0d004c;
        public static int mtrl_calendar_year = 0x7f0d004d;
        public static int mtrl_layout_snackbar = 0x7f0d004e;
        public static int mtrl_layout_snackbar_include = 0x7f0d004f;
        public static int mtrl_picker_actions = 0x7f0d0050;
        public static int mtrl_picker_dialog = 0x7f0d0051;
        public static int mtrl_picker_fullscreen = 0x7f0d0052;
        public static int mtrl_picker_header_dialog = 0x7f0d0053;
        public static int mtrl_picker_header_fullscreen = 0x7f0d0054;
        public static int mtrl_picker_header_selection_text = 0x7f0d0055;
        public static int mtrl_picker_header_title_text = 0x7f0d0056;
        public static int mtrl_picker_header_toggle = 0x7f0d0057;
        public static int mtrl_picker_text_input_date = 0x7f0d0058;
        public static int mtrl_picker_text_input_date_range = 0x7f0d0059;
        public static int notification_action = 0x7f0d005b;
        public static int notification_action_tombstone = 0x7f0d005c;
        public static int notification_template_custom_big = 0x7f0d0063;
        public static int notification_template_icon_group = 0x7f0d0064;
        public static int notification_template_part_chronometer = 0x7f0d0068;
        public static int notification_template_part_time = 0x7f0d0069;
        public static int select_dialog_item_material = 0x7f0d006a;
        public static int select_dialog_multichoice_material = 0x7f0d006b;
        public static int select_dialog_singlechoice_material = 0x7f0d006c;
        public static int support_simple_spinner_dropdown_item = 0x7f0d006e;
        public static int test_action_chip = 0x7f0d006f;
        public static int test_design_checkbox = 0x7f0d0070;
        public static int test_reflow_chipgroup = 0x7f0d0071;
        public static int test_toolbar = 0x7f0d0072;
        public static int test_toolbar_custom_background = 0x7f0d0073;
        public static int test_toolbar_elevation = 0x7f0d0074;
        public static int test_toolbar_surface = 0x7f0d0075;
        public static int text_view_with_line_height_from_appearance = 0x7f0d0076;
        public static int text_view_with_line_height_from_layout = 0x7f0d0077;
        public static int text_view_with_line_height_from_style = 0x7f0d0078;
        public static int text_view_with_theme_line_height = 0x7f0d0079;
        public static int text_view_without_line_height = 0x7f0d007a;

        private layout() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$plurals.smali */
    public static final class plurals {
        public static int mtrl_badge_content_description = 0x7f100000;

        private plurals() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$string.smali */
    public static final class string {
        public static int abc_action_bar_home_description = 0x7f120000;
        public static int abc_action_bar_up_description = 0x7f120001;
        public static int abc_action_menu_overflow_description = 0x7f120002;
        public static int abc_action_mode_done = 0x7f120003;
        public static int abc_activity_chooser_view_see_all = 0x7f120004;
        public static int abc_activitychooserview_choose_application = 0x7f120005;
        public static int abc_capital_off = 0x7f120006;
        public static int abc_capital_on = 0x7f120007;
        public static int abc_menu_alt_shortcut_label = 0x7f120008;
        public static int abc_menu_ctrl_shortcut_label = 0x7f120009;
        public static int abc_menu_delete_shortcut_label = 0x7f12000a;
        public static int abc_menu_enter_shortcut_label = 0x7f12000b;
        public static int abc_menu_function_shortcut_label = 0x7f12000c;
        public static int abc_menu_meta_shortcut_label = 0x7f12000d;
        public static int abc_menu_shift_shortcut_label = 0x7f12000e;
        public static int abc_menu_space_shortcut_label = 0x7f12000f;
        public static int abc_menu_sym_shortcut_label = 0x7f120010;
        public static int abc_prepend_shortcut_label = 0x7f120011;
        public static int abc_search_hint = 0x7f120012;
        public static int abc_searchview_description_clear = 0x7f120013;
        public static int abc_searchview_description_query = 0x7f120014;
        public static int abc_searchview_description_search = 0x7f120015;
        public static int abc_searchview_description_submit = 0x7f120016;
        public static int abc_searchview_description_voice = 0x7f120017;
        public static int abc_shareactionprovider_share_with = 0x7f120018;
        public static int abc_shareactionprovider_share_with_application = 0x7f120019;
        public static int abc_toolbar_collapse_description = 0x7f12001a;
        public static int appbar_scrolling_view_behavior = 0x7f1200bb;
        public static int bottom_sheet_behavior = 0x7f1200c2;
        public static int character_counter_content_description = 0x7f1200ca;
        public static int character_counter_overflowed_content_description = 0x7f1200cb;
        public static int character_counter_pattern = 0x7f1200cc;
        public static int chip_text = 0x7f1200cd;
        public static int clear_text_end_icon_content_description = 0x7f1200ce;
        public static int error_icon_content_description = 0x7f1200e6;
        public static int exposed_dropdown_menu_content_description = 0x7f1200e7;
        public static int fab_transformation_scrim_behavior = 0x7f1200e8;
        public static int fab_transformation_sheet_behavior = 0x7f1200e9;
        public static int hide_bottom_view_on_scroll_behavior = 0x7f1200fc;
        public static int icon_content_description = 0x7f1200fd;
        public static int mtrl_badge_numberless_content_description = 0x7f120107;
        public static int mtrl_chip_close_icon_content_description = 0x7f120108;
        public static int mtrl_exceed_max_badge_number_suffix = 0x7f120109;
        public static int mtrl_picker_a11y_next_month = 0x7f12010a;
        public static int mtrl_picker_a11y_prev_month = 0x7f12010b;
        public static int mtrl_picker_announce_current_selection = 0x7f12010c;
        public static int mtrl_picker_cancel = 0x7f12010d;
        public static int mtrl_picker_confirm = 0x7f12010e;
        public static int mtrl_picker_date_header_selected = 0x7f12010f;
        public static int mtrl_picker_date_header_title = 0x7f120110;
        public static int mtrl_picker_date_header_unselected = 0x7f120111;
        public static int mtrl_picker_day_of_week_column_header = 0x7f120112;
        public static int mtrl_picker_invalid_format = 0x7f120113;
        public static int mtrl_picker_invalid_format_example = 0x7f120114;
        public static int mtrl_picker_invalid_format_use = 0x7f120115;
        public static int mtrl_picker_invalid_range = 0x7f120116;
        public static int mtrl_picker_navigate_to_year_description = 0x7f120117;
        public static int mtrl_picker_out_of_range = 0x7f120118;
        public static int mtrl_picker_range_header_only_end_selected = 0x7f120119;
        public static int mtrl_picker_range_header_only_start_selected = 0x7f12011a;
        public static int mtrl_picker_range_header_selected = 0x7f12011b;
        public static int mtrl_picker_range_header_title = 0x7f12011c;
        public static int mtrl_picker_range_header_unselected = 0x7f12011d;
        public static int mtrl_picker_save = 0x7f12011e;
        public static int mtrl_picker_text_input_date_hint = 0x7f12011f;
        public static int mtrl_picker_text_input_date_range_end_hint = 0x7f120120;
        public static int mtrl_picker_text_input_date_range_start_hint = 0x7f120121;
        public static int mtrl_picker_text_input_day_abbr = 0x7f120122;
        public static int mtrl_picker_text_input_month_abbr = 0x7f120123;
        public static int mtrl_picker_text_input_year_abbr = 0x7f120124;
        public static int mtrl_picker_toggle_to_calendar_input_mode = 0x7f120125;
        public static int mtrl_picker_toggle_to_day_selection = 0x7f120126;
        public static int mtrl_picker_toggle_to_text_input_mode = 0x7f120127;
        public static int mtrl_picker_toggle_to_year_selection = 0x7f120128;
        public static int password_toggle_content_description = 0x7f12012f;
        public static int path_password_eye = 0x7f120130;
        public static int path_password_eye_mask_strike_through = 0x7f120131;
        public static int path_password_eye_mask_visible = 0x7f120132;
        public static int path_password_strike_through = 0x7f120133;
        public static int search_menu_title = 0x7f120136;
        public static int status_bar_notification_info_overflow = 0x7f120138;

        private string() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$style.smali */
    public static final class style {
        public static int AlertDialog_AppCompat = 0x7f130000;
        public static int AlertDialog_AppCompat_Light = 0x7f130001;
        public static int Animation_AppCompat_Dialog = 0x7f130002;
        public static int Animation_AppCompat_DropDownUp = 0x7f130003;
        public static int Animation_AppCompat_Tooltip = 0x7f130004;
        public static int Animation_Design_BottomSheetDialog = 0x7f130005;
        public static int Animation_MaterialComponents_BottomSheetDialog = 0x7f130006;
        public static int Base_AlertDialog_AppCompat = 0x7f130026;
        public static int Base_AlertDialog_AppCompat_Light = 0x7f130027;
        public static int Base_Animation_AppCompat_Dialog = 0x7f130028;
        public static int Base_Animation_AppCompat_DropDownUp = 0x7f130029;
        public static int Base_Animation_AppCompat_Tooltip = 0x7f13002a;
        public static int Base_CardView = 0x7f13002b;
        public static int Base_DialogWindowTitleBackground_AppCompat = 0x7f13002d;
        public static int Base_DialogWindowTitle_AppCompat = 0x7f13002c;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f13002e;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f13002f;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f130030;
        public static int Base_TextAppearance_AppCompat = 0x7f130031;
        public static int Base_TextAppearance_AppCompat_Body1 = 0x7f130032;
        public static int Base_TextAppearance_AppCompat_Body2 = 0x7f130033;
        public static int Base_TextAppearance_AppCompat_Button = 0x7f130034;
        public static int Base_TextAppearance_AppCompat_Caption = 0x7f130035;
        public static int Base_TextAppearance_AppCompat_Display1 = 0x7f130036;
        public static int Base_TextAppearance_AppCompat_Display2 = 0x7f130037;
        public static int Base_TextAppearance_AppCompat_Display3 = 0x7f130038;
        public static int Base_TextAppearance_AppCompat_Display4 = 0x7f130039;
        public static int Base_TextAppearance_AppCompat_Headline = 0x7f13003a;
        public static int Base_TextAppearance_AppCompat_Inverse = 0x7f13003b;
        public static int Base_TextAppearance_AppCompat_Large = 0x7f13003c;
        public static int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f13003d;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f13003e;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f13003f;
        public static int Base_TextAppearance_AppCompat_Medium = 0x7f130040;
        public static int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f130041;
        public static int Base_TextAppearance_AppCompat_Menu = 0x7f130042;
        public static int Base_TextAppearance_AppCompat_SearchResult = 0x7f130043;
        public static int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f130044;
        public static int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f130045;
        public static int Base_TextAppearance_AppCompat_Small = 0x7f130046;
        public static int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f130047;
        public static int Base_TextAppearance_AppCompat_Subhead = 0x7f130048;
        public static int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f130049;
        public static int Base_TextAppearance_AppCompat_Title = 0x7f13004a;
        public static int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f13004b;
        public static int Base_TextAppearance_AppCompat_Tooltip = 0x7f13004c;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f13004d;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f13004e;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f13004f;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f130050;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f130051;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f130052;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f130053;
        public static int Base_TextAppearance_AppCompat_Widget_Button = 0x7f130054;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f130055;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f130056;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f130057;
        public static int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f130058;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f130059;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f13005a;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f13005b;
        public static int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f13005c;
        public static int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f13005d;
        public static int Base_TextAppearance_MaterialComponents_Badge = 0x7f13005e;
        public static int Base_TextAppearance_MaterialComponents_Button = 0x7f13005f;
        public static int Base_TextAppearance_MaterialComponents_Headline6 = 0x7f130060;
        public static int Base_TextAppearance_MaterialComponents_Subtitle2 = 0x7f130061;
        public static int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f130062;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f130063;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f130064;
        public static int Base_ThemeOverlay_AppCompat = 0x7f130089;
        public static int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f13008a;
        public static int Base_ThemeOverlay_AppCompat_Dark = 0x7f13008b;
        public static int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f13008c;
        public static int Base_ThemeOverlay_AppCompat_Dialog = 0x7f13008d;
        public static int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f13008e;
        public static int Base_ThemeOverlay_AppCompat_Light = 0x7f13008f;
        public static int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f130090;
        public static int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f130091;
        public static int Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f130092;
        public static int Base_Theme_AppCompat = 0x7f130065;
        public static int Base_Theme_AppCompat_CompactMenu = 0x7f130066;
        public static int Base_Theme_AppCompat_Dialog = 0x7f130067;
        public static int Base_Theme_AppCompat_DialogWhenLarge = 0x7f13006b;
        public static int Base_Theme_AppCompat_Dialog_Alert = 0x7f130068;
        public static int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f130069;
        public static int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f13006a;
        public static int Base_Theme_AppCompat_Light = 0x7f13006c;
        public static int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f13006d;
        public static int Base_Theme_AppCompat_Light_Dialog = 0x7f13006e;
        public static int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f130072;
        public static int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f13006f;
        public static int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f130070;
        public static int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f130071;
        public static int Base_Theme_MaterialComponents = 0x7f130073;
        public static int Base_Theme_MaterialComponents_Bridge = 0x7f130074;
        public static int Base_Theme_MaterialComponents_CompactMenu = 0x7f130075;
        public static int Base_Theme_MaterialComponents_Dialog = 0x7f130076;
        public static int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f13007b;
        public static int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f130077;
        public static int Base_Theme_MaterialComponents_Dialog_Bridge = 0x7f130078;
        public static int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f130079;
        public static int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f13007a;
        public static int Base_Theme_MaterialComponents_Light = 0x7f13007c;
        public static int Base_Theme_MaterialComponents_Light_Bridge = 0x7f13007d;
        public static int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f13007e;
        public static int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f13007f;
        public static int Base_Theme_MaterialComponents_Light_Dialog = 0x7f130080;
        public static int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f130085;
        public static int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f130081;
        public static int Base_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f130082;
        public static int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f130083;
        public static int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f130084;
        public static int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f13009c;
        public static int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f13009d;
        public static int Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f13009e;
        public static int Base_V14_Theme_MaterialComponents = 0x7f130093;
        public static int Base_V14_Theme_MaterialComponents_Bridge = 0x7f130094;
        public static int Base_V14_Theme_MaterialComponents_Dialog = 0x7f130095;
        public static int Base_V14_Theme_MaterialComponents_Dialog_Bridge = 0x7f130096;
        public static int Base_V14_Theme_MaterialComponents_Light = 0x7f130097;
        public static int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f130098;
        public static int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f130099;
        public static int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f13009a;
        public static int Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f13009b;
        public static int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f1300a3;
        public static int Base_V21_Theme_AppCompat = 0x7f13009f;
        public static int Base_V21_Theme_AppCompat_Dialog = 0x7f1300a0;
        public static int Base_V21_Theme_AppCompat_Light = 0x7f1300a1;
        public static int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f1300a2;
        public static int Base_V22_Theme_AppCompat = 0x7f1300a4;
        public static int Base_V22_Theme_AppCompat_Light = 0x7f1300a5;
        public static int Base_V23_Theme_AppCompat = 0x7f1300a6;
        public static int Base_V23_Theme_AppCompat_Light = 0x7f1300a7;
        public static int Base_V26_Theme_AppCompat = 0x7f1300a8;
        public static int Base_V26_Theme_AppCompat_Light = 0x7f1300a9;
        public static int Base_V26_Widget_AppCompat_Toolbar = 0x7f1300aa;
        public static int Base_V28_Theme_AppCompat = 0x7f1300ab;
        public static int Base_V28_Theme_AppCompat_Light = 0x7f1300ac;
        public static int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1300b1;
        public static int Base_V7_Theme_AppCompat = 0x7f1300ad;
        public static int Base_V7_Theme_AppCompat_Dialog = 0x7f1300ae;
        public static int Base_V7_Theme_AppCompat_Light = 0x7f1300af;
        public static int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1300b0;
        public static int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1300b2;
        public static int Base_V7_Widget_AppCompat_EditText = 0x7f1300b3;
        public static int Base_V7_Widget_AppCompat_Toolbar = 0x7f1300b4;
        public static int Base_Widget_AppCompat_ActionBar = 0x7f1300b5;
        public static int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1300b6;
        public static int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1300b7;
        public static int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1300b8;
        public static int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1300b9;
        public static int Base_Widget_AppCompat_ActionButton = 0x7f1300ba;
        public static int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1300bb;
        public static int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1300bc;
        public static int Base_Widget_AppCompat_ActionMode = 0x7f1300bd;
        public static int Base_Widget_AppCompat_ActivityChooserView = 0x7f1300be;
        public static int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1300bf;
        public static int Base_Widget_AppCompat_Button = 0x7f1300c0;
        public static int Base_Widget_AppCompat_ButtonBar = 0x7f1300c6;
        public static int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1300c7;
        public static int Base_Widget_AppCompat_Button_Borderless = 0x7f1300c1;
        public static int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1300c2;
        public static int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1300c3;
        public static int Base_Widget_AppCompat_Button_Colored = 0x7f1300c4;
        public static int Base_Widget_AppCompat_Button_Small = 0x7f1300c5;
        public static int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1300c8;
        public static int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1300c9;
        public static int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1300ca;
        public static int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1300cb;
        public static int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1300cc;
        public static int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1300cd;
        public static int Base_Widget_AppCompat_EditText = 0x7f1300ce;
        public static int Base_Widget_AppCompat_ImageButton = 0x7f1300cf;
        public static int Base_Widget_AppCompat_Light_ActionBar = 0x7f1300d0;
        public static int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1300d1;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1300d2;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1300d3;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1300d4;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1300d5;
        public static int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1300d6;
        public static int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1300d7;
        public static int Base_Widget_AppCompat_ListMenuView = 0x7f1300d8;
        public static int Base_Widget_AppCompat_ListPopupWindow = 0x7f1300d9;
        public static int Base_Widget_AppCompat_ListView = 0x7f1300da;
        public static int Base_Widget_AppCompat_ListView_DropDown = 0x7f1300db;
        public static int Base_Widget_AppCompat_ListView_Menu = 0x7f1300dc;
        public static int Base_Widget_AppCompat_PopupMenu = 0x7f1300dd;
        public static int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1300de;
        public static int Base_Widget_AppCompat_PopupWindow = 0x7f1300df;
        public static int Base_Widget_AppCompat_ProgressBar = 0x7f1300e0;
        public static int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1300e1;
        public static int Base_Widget_AppCompat_RatingBar = 0x7f1300e2;
        public static int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1300e3;
        public static int Base_Widget_AppCompat_RatingBar_Small = 0x7f1300e4;
        public static int Base_Widget_AppCompat_SearchView = 0x7f1300e5;
        public static int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1300e6;
        public static int Base_Widget_AppCompat_SeekBar = 0x7f1300e7;
        public static int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1300e8;
        public static int Base_Widget_AppCompat_Spinner = 0x7f1300e9;
        public static int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1300ea;
        public static int Base_Widget_AppCompat_TextView = 0x7f1300eb;
        public static int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1300ec;
        public static int Base_Widget_AppCompat_Toolbar = 0x7f1300ed;
        public static int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1300ee;
        public static int Base_Widget_Design_TabLayout = 0x7f1300ef;
        public static int Base_Widget_MaterialComponents_AutoCompleteTextView = 0x7f1300f0;
        public static int Base_Widget_MaterialComponents_CheckedTextView = 0x7f1300f1;
        public static int Base_Widget_MaterialComponents_Chip = 0x7f1300f2;
        public static int Base_Widget_MaterialComponents_PopupMenu = 0x7f1300f3;
        public static int Base_Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1300f4;
        public static int Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1300f5;
        public static int Base_Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1300f6;
        public static int Base_Widget_MaterialComponents_TextInputEditText = 0x7f1300f7;
        public static int Base_Widget_MaterialComponents_TextInputLayout = 0x7f1300f8;
        public static int Base_Widget_MaterialComponents_TextView = 0x7f1300f9;
        public static int CardView = 0x7f1300fe;
        public static int CardView_Dark = 0x7f1300ff;
        public static int CardView_Light = 0x7f130100;
        public static int EmptyTheme = 0x7f130102;
        public static int MaterialAlertDialog_MaterialComponents = 0x7f130103;
        public static int MaterialAlertDialog_MaterialComponents_Body_Text = 0x7f130104;
        public static int MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar = 0x7f130105;
        public static int MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner = 0x7f130106;
        public static int MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f130107;
        public static int MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked = 0x7f130108;
        public static int MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f130109;
        public static int MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked = 0x7f13010a;
        public static int MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f13010b;
        public static int MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked = 0x7f13010c;
        public static int Platform_AppCompat = 0x7f13010d;
        public static int Platform_AppCompat_Light = 0x7f13010e;
        public static int Platform_MaterialComponents = 0x7f13010f;
        public static int Platform_MaterialComponents_Dialog = 0x7f130110;
        public static int Platform_MaterialComponents_Light = 0x7f130111;
        public static int Platform_MaterialComponents_Light_Dialog = 0x7f130112;
        public static int Platform_ThemeOverlay_AppCompat = 0x7f130113;
        public static int Platform_ThemeOverlay_AppCompat_Dark = 0x7f130114;
        public static int Platform_ThemeOverlay_AppCompat_Light = 0x7f130115;
        public static int Platform_V21_AppCompat = 0x7f130116;
        public static int Platform_V21_AppCompat_Light = 0x7f130117;
        public static int Platform_V25_AppCompat = 0x7f130118;
        public static int Platform_V25_AppCompat_Light = 0x7f130119;
        public static int Platform_Widget_AppCompat_Spinner = 0x7f13011a;
        public static int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f13011b;
        public static int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f13011c;
        public static int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f13011d;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f13011e;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f13011f;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f130120;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f130121;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f130122;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f130123;
        public static int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f130129;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f130124;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f130125;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f130126;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f130127;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f130128;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f13012a;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f13012b;
        public static int ShapeAppearanceOverlay = 0x7f130131;
        public static int ShapeAppearanceOverlay_BottomLeftDifferentCornerSize = 0x7f130132;
        public static int ShapeAppearanceOverlay_BottomRightCut = 0x7f130133;
        public static int ShapeAppearanceOverlay_Cut = 0x7f130134;
        public static int ShapeAppearanceOverlay_DifferentCornerSize = 0x7f130135;
        public static int ShapeAppearanceOverlay_MaterialComponents_BottomSheet = 0x7f130136;
        public static int ShapeAppearanceOverlay_MaterialComponents_Chip = 0x7f130137;
        public static int ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton = 0x7f130138;
        public static int ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton = 0x7f130139;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f13013a;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen = 0x7f13013b;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year = 0x7f13013c;
        public static int ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox = 0x7f13013d;
        public static int ShapeAppearanceOverlay_TopLeftCut = 0x7f13013f;
        public static int ShapeAppearanceOverlay_TopRightDifferentCornerSize = 0x7f130140;
        public static int ShapeAppearance_MaterialComponents = 0x7f13012c;
        public static int ShapeAppearance_MaterialComponents_LargeComponent = 0x7f13012d;
        public static int ShapeAppearance_MaterialComponents_MediumComponent = 0x7f13012e;
        public static int ShapeAppearance_MaterialComponents_SmallComponent = 0x7f13012f;
        public static int ShapeAppearance_MaterialComponents_Test = 0x7f130130;
        public static int TestStyleWithLineHeight = 0x7f130147;
        public static int TestStyleWithLineHeightAppearance = 0x7f130148;
        public static int TestStyleWithThemeLineHeightAttribute = 0x7f130149;
        public static int TestStyleWithoutLineHeight = 0x7f13014a;
        public static int TestThemeWithLineHeight = 0x7f13014b;
        public static int TestThemeWithLineHeightDisabled = 0x7f13014c;
        public static int Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f130142;
        public static int Test_Theme_MaterialComponents_MaterialCalendar = 0x7f130143;
        public static int Test_Widget_MaterialComponents_MaterialCalendar = 0x7f130144;
        public static int Test_Widget_MaterialComponents_MaterialCalendar_Day = 0x7f130145;
        public static int Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f130146;
        public static int TextAppearance_AppCompat = 0x7f13014d;
        public static int TextAppearance_AppCompat_Body1 = 0x7f13014e;
        public static int TextAppearance_AppCompat_Body2 = 0x7f13014f;
        public static int TextAppearance_AppCompat_Button = 0x7f130150;
        public static int TextAppearance_AppCompat_Caption = 0x7f130151;
        public static int TextAppearance_AppCompat_Display1 = 0x7f130152;
        public static int TextAppearance_AppCompat_Display2 = 0x7f130153;
        public static int TextAppearance_AppCompat_Display3 = 0x7f130154;
        public static int TextAppearance_AppCompat_Display4 = 0x7f130155;
        public static int TextAppearance_AppCompat_Headline = 0x7f130156;
        public static int TextAppearance_AppCompat_Inverse = 0x7f130157;
        public static int TextAppearance_AppCompat_Large = 0x7f130158;
        public static int TextAppearance_AppCompat_Large_Inverse = 0x7f130159;
        public static int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f13015a;
        public static int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f13015b;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f13015c;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f13015d;
        public static int TextAppearance_AppCompat_Medium = 0x7f13015e;
        public static int TextAppearance_AppCompat_Medium_Inverse = 0x7f13015f;
        public static int TextAppearance_AppCompat_Menu = 0x7f130160;
        public static int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f130161;
        public static int TextAppearance_AppCompat_SearchResult_Title = 0x7f130162;
        public static int TextAppearance_AppCompat_Small = 0x7f130163;
        public static int TextAppearance_AppCompat_Small_Inverse = 0x7f130164;
        public static int TextAppearance_AppCompat_Subhead = 0x7f130165;
        public static int TextAppearance_AppCompat_Subhead_Inverse = 0x7f130166;
        public static int TextAppearance_AppCompat_Title = 0x7f130167;
        public static int TextAppearance_AppCompat_Title_Inverse = 0x7f130168;
        public static int TextAppearance_AppCompat_Tooltip = 0x7f130169;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f13016a;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f13016b;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f13016c;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f13016d;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f13016e;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f13016f;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f130170;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f130171;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f130172;
        public static int TextAppearance_AppCompat_Widget_Button = 0x7f130173;
        public static int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f130174;
        public static int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f130175;
        public static int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f130176;
        public static int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f130177;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f130178;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f130179;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f13017a;
        public static int TextAppearance_AppCompat_Widget_Switch = 0x7f13017b;
        public static int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f13017c;
        public static int TextAppearance_Compat_Notification = 0x7f13017d;
        public static int TextAppearance_Compat_Notification_Info = 0x7f13017e;
        public static int TextAppearance_Compat_Notification_Line2 = 0x7f130180;
        public static int TextAppearance_Compat_Notification_Time = 0x7f130183;
        public static int TextAppearance_Compat_Notification_Title = 0x7f130185;
        public static int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f130187;
        public static int TextAppearance_Design_Counter = 0x7f130188;
        public static int TextAppearance_Design_Counter_Overflow = 0x7f130189;
        public static int TextAppearance_Design_Error = 0x7f13018a;
        public static int TextAppearance_Design_HelperText = 0x7f13018b;
        public static int TextAppearance_Design_Hint = 0x7f13018c;
        public static int TextAppearance_Design_Snackbar_Message = 0x7f13018d;
        public static int TextAppearance_Design_Tab = 0x7f13018e;
        public static int TextAppearance_MaterialComponents_Badge = 0x7f13018f;
        public static int TextAppearance_MaterialComponents_Body1 = 0x7f130190;
        public static int TextAppearance_MaterialComponents_Body2 = 0x7f130191;
        public static int TextAppearance_MaterialComponents_Button = 0x7f130192;
        public static int TextAppearance_MaterialComponents_Caption = 0x7f130193;
        public static int TextAppearance_MaterialComponents_Chip = 0x7f130194;
        public static int TextAppearance_MaterialComponents_Headline1 = 0x7f130195;
        public static int TextAppearance_MaterialComponents_Headline2 = 0x7f130196;
        public static int TextAppearance_MaterialComponents_Headline3 = 0x7f130197;
        public static int TextAppearance_MaterialComponents_Headline4 = 0x7f130198;
        public static int TextAppearance_MaterialComponents_Headline5 = 0x7f130199;
        public static int TextAppearance_MaterialComponents_Headline6 = 0x7f13019a;
        public static int TextAppearance_MaterialComponents_Overline = 0x7f13019b;
        public static int TextAppearance_MaterialComponents_Subtitle1 = 0x7f13019c;
        public static int TextAppearance_MaterialComponents_Subtitle2 = 0x7f13019d;
        public static int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f13019e;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f13019f;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f1301a0;
        public static int ThemeOverlay_AppCompat = 0x7f1301fb;
        public static int ThemeOverlay_AppCompat_ActionBar = 0x7f1301fc;
        public static int ThemeOverlay_AppCompat_Dark = 0x7f1301fd;
        public static int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1301fe;
        public static int ThemeOverlay_AppCompat_DayNight = 0x7f1301ff;
        public static int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f130200;
        public static int ThemeOverlay_AppCompat_Dialog = 0x7f130201;
        public static int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f130202;
        public static int ThemeOverlay_AppCompat_Light = 0x7f130203;
        public static int ThemeOverlay_Design_TextInputEditText = 0x7f130204;
        public static int ThemeOverlay_MaterialComponents = 0x7f130205;
        public static int ThemeOverlay_MaterialComponents_ActionBar = 0x7f130206;
        public static int ThemeOverlay_MaterialComponents_ActionBar_Primary = 0x7f130207;
        public static int ThemeOverlay_MaterialComponents_ActionBar_Surface = 0x7f130208;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView = 0x7f130209;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f13020a;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f13020b;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f13020c;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f13020d;
        public static int ThemeOverlay_MaterialComponents_BottomAppBar_Primary = 0x7f13020e;
        public static int ThemeOverlay_MaterialComponents_BottomAppBar_Surface = 0x7f13020f;
        public static int ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f130210;
        public static int ThemeOverlay_MaterialComponents_Dark = 0x7f130211;
        public static int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f130212;
        public static int ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog = 0x7f130213;
        public static int ThemeOverlay_MaterialComponents_Dialog = 0x7f130214;
        public static int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f130215;
        public static int ThemeOverlay_MaterialComponents_Light = 0x7f130216;
        public static int ThemeOverlay_MaterialComponents_Light_BottomSheetDialog = 0x7f130217;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f130218;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered = 0x7f130219;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date = 0x7f13021a;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar = 0x7f13021b;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text = 0x7f13021c;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day = 0x7f13021d;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner = 0x7f13021e;
        public static int ThemeOverlay_MaterialComponents_MaterialCalendar = 0x7f13021f;
        public static int ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f130220;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f130221;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f130222;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f130223;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f130224;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f130225;
        public static int ThemeOverlay_MaterialComponents_Toolbar_Primary = 0x7f130226;
        public static int ThemeOverlay_MaterialComponents_Toolbar_Surface = 0x7f130227;
        public static int Theme_AppCompat = 0x7f1301aa;
        public static int Theme_AppCompat_CompactMenu = 0x7f1301ab;
        public static int Theme_AppCompat_DayNight = 0x7f1301ac;
        public static int Theme_AppCompat_DayNight_DarkActionBar = 0x7f1301ad;
        public static int Theme_AppCompat_DayNight_Dialog = 0x7f1301ae;
        public static int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f1301b1;
        public static int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f1301af;
        public static int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f1301b0;
        public static int Theme_AppCompat_DayNight_NoActionBar = 0x7f1301b2;
        public static int Theme_AppCompat_Dialog = 0x7f1301b3;
        public static int Theme_AppCompat_DialogWhenLarge = 0x7f1301b6;
        public static int Theme_AppCompat_Dialog_Alert = 0x7f1301b4;
        public static int Theme_AppCompat_Dialog_MinWidth = 0x7f1301b5;
        public static int Theme_AppCompat_Light = 0x7f1301b8;
        public static int Theme_AppCompat_Light_DarkActionBar = 0x7f1301b9;
        public static int Theme_AppCompat_Light_Dialog = 0x7f1301ba;
        public static int Theme_AppCompat_Light_DialogWhenLarge = 0x7f1301bd;
        public static int Theme_AppCompat_Light_Dialog_Alert = 0x7f1301bb;
        public static int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f1301bc;
        public static int Theme_AppCompat_Light_NoActionBar = 0x7f1301be;
        public static int Theme_AppCompat_NoActionBar = 0x7f1301bf;
        public static int Theme_Design = 0x7f1301c0;
        public static int Theme_Design_BottomSheetDialog = 0x7f1301c1;
        public static int Theme_Design_Light = 0x7f1301c2;
        public static int Theme_Design_Light_BottomSheetDialog = 0x7f1301c3;
        public static int Theme_Design_Light_NoActionBar = 0x7f1301c4;
        public static int Theme_Design_NoActionBar = 0x7f1301c5;
        public static int Theme_MaterialComponents = 0x7f1301c6;
        public static int Theme_MaterialComponents_BottomSheetDialog = 0x7f1301c7;
        public static int Theme_MaterialComponents_Bridge = 0x7f1301c8;
        public static int Theme_MaterialComponents_CompactMenu = 0x7f1301c9;
        public static int Theme_MaterialComponents_DayNight = 0x7f1301ca;
        public static int Theme_MaterialComponents_DayNight_BottomSheetDialog = 0x7f1301cb;
        public static int Theme_MaterialComponents_DayNight_Bridge = 0x7f1301cc;
        public static int Theme_MaterialComponents_DayNight_DarkActionBar = 0x7f1301cd;
        public static int Theme_MaterialComponents_DayNight_DarkActionBar_Bridge = 0x7f1301ce;
        public static int Theme_MaterialComponents_DayNight_Dialog = 0x7f1301cf;
        public static int Theme_MaterialComponents_DayNight_DialogWhenLarge = 0x7f1301d7;
        public static int Theme_MaterialComponents_DayNight_Dialog_Alert = 0x7f1301d0;
        public static int Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge = 0x7f1301d1;
        public static int Theme_MaterialComponents_DayNight_Dialog_Bridge = 0x7f1301d2;
        public static int Theme_MaterialComponents_DayNight_Dialog_FixedSize = 0x7f1301d3;
        public static int Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge = 0x7f1301d4;
        public static int Theme_MaterialComponents_DayNight_Dialog_MinWidth = 0x7f1301d5;
        public static int Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge = 0x7f1301d6;
        public static int Theme_MaterialComponents_DayNight_NoActionBar = 0x7f1301d8;
        public static int Theme_MaterialComponents_DayNight_NoActionBar_Bridge = 0x7f1301d9;
        public static int Theme_MaterialComponents_Dialog = 0x7f1301da;
        public static int Theme_MaterialComponents_DialogWhenLarge = 0x7f1301e2;
        public static int Theme_MaterialComponents_Dialog_Alert = 0x7f1301db;
        public static int Theme_MaterialComponents_Dialog_Alert_Bridge = 0x7f1301dc;
        public static int Theme_MaterialComponents_Dialog_Bridge = 0x7f1301dd;
        public static int Theme_MaterialComponents_Dialog_FixedSize = 0x7f1301de;
        public static int Theme_MaterialComponents_Dialog_FixedSize_Bridge = 0x7f1301df;
        public static int Theme_MaterialComponents_Dialog_MinWidth = 0x7f1301e0;
        public static int Theme_MaterialComponents_Dialog_MinWidth_Bridge = 0x7f1301e1;
        public static int Theme_MaterialComponents_Light = 0x7f1301e3;
        public static int Theme_MaterialComponents_Light_BarSize = 0x7f1301e4;
        public static int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f1301e5;
        public static int Theme_MaterialComponents_Light_Bridge = 0x7f1301e6;
        public static int Theme_MaterialComponents_Light_DarkActionBar = 0x7f1301e7;
        public static int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f1301e8;
        public static int Theme_MaterialComponents_Light_Dialog = 0x7f1301e9;
        public static int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f1301f1;
        public static int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f1301ea;
        public static int Theme_MaterialComponents_Light_Dialog_Alert_Bridge = 0x7f1301eb;
        public static int Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f1301ec;
        public static int Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f1301ed;
        public static int Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge = 0x7f1301ee;
        public static int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f1301ef;
        public static int Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge = 0x7f1301f0;
        public static int Theme_MaterialComponents_Light_LargeTouch = 0x7f1301f2;
        public static int Theme_MaterialComponents_Light_NoActionBar = 0x7f1301f3;
        public static int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f1301f4;
        public static int Theme_MaterialComponents_NoActionBar = 0x7f1301f5;
        public static int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f1301f6;
        public static int Widget_AppCompat_ActionBar = 0x7f130229;
        public static int Widget_AppCompat_ActionBar_Solid = 0x7f13022a;
        public static int Widget_AppCompat_ActionBar_TabBar = 0x7f13022b;
        public static int Widget_AppCompat_ActionBar_TabText = 0x7f13022c;
        public static int Widget_AppCompat_ActionBar_TabView = 0x7f13022d;
        public static int Widget_AppCompat_ActionButton = 0x7f13022e;
        public static int Widget_AppCompat_ActionButton_CloseMode = 0x7f13022f;
        public static int Widget_AppCompat_ActionButton_Overflow = 0x7f130230;
        public static int Widget_AppCompat_ActionMode = 0x7f130231;
        public static int Widget_AppCompat_ActivityChooserView = 0x7f130232;
        public static int Widget_AppCompat_AutoCompleteTextView = 0x7f130233;
        public static int Widget_AppCompat_Button = 0x7f130234;
        public static int Widget_AppCompat_ButtonBar = 0x7f13023a;
        public static int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f13023b;
        public static int Widget_AppCompat_Button_Borderless = 0x7f130235;
        public static int Widget_AppCompat_Button_Borderless_Colored = 0x7f130236;
        public static int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f130237;
        public static int Widget_AppCompat_Button_Colored = 0x7f130238;
        public static int Widget_AppCompat_Button_Small = 0x7f130239;
        public static int Widget_AppCompat_CompoundButton_CheckBox = 0x7f13023c;
        public static int Widget_AppCompat_CompoundButton_RadioButton = 0x7f13023d;
        public static int Widget_AppCompat_CompoundButton_Switch = 0x7f13023e;
        public static int Widget_AppCompat_DrawerArrowToggle = 0x7f13023f;
        public static int Widget_AppCompat_DropDownItem_Spinner = 0x7f130240;
        public static int Widget_AppCompat_EditText = 0x7f130241;
        public static int Widget_AppCompat_ImageButton = 0x7f130242;
        public static int Widget_AppCompat_Light_ActionBar = 0x7f130243;
        public static int Widget_AppCompat_Light_ActionBar_Solid = 0x7f130244;
        public static int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f130245;
        public static int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f130246;
        public static int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f130247;
        public static int Widget_AppCompat_Light_ActionBar_TabText = 0x7f130248;
        public static int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f130249;
        public static int Widget_AppCompat_Light_ActionBar_TabView = 0x7f13024a;
        public static int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f13024b;
        public static int Widget_AppCompat_Light_ActionButton = 0x7f13024c;
        public static int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f13024d;
        public static int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f13024e;
        public static int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f13024f;
        public static int Widget_AppCompat_Light_ActivityChooserView = 0x7f130250;
        public static int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f130251;
        public static int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f130252;
        public static int Widget_AppCompat_Light_ListPopupWindow = 0x7f130253;
        public static int Widget_AppCompat_Light_ListView_DropDown = 0x7f130254;
        public static int Widget_AppCompat_Light_PopupMenu = 0x7f130255;
        public static int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f130256;
        public static int Widget_AppCompat_Light_SearchView = 0x7f130257;
        public static int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f130258;
        public static int Widget_AppCompat_ListMenuView = 0x7f130259;
        public static int Widget_AppCompat_ListPopupWindow = 0x7f13025a;
        public static int Widget_AppCompat_ListView = 0x7f13025b;
        public static int Widget_AppCompat_ListView_DropDown = 0x7f13025c;
        public static int Widget_AppCompat_ListView_Menu = 0x7f13025d;
        public static int Widget_AppCompat_PopupMenu = 0x7f13025e;
        public static int Widget_AppCompat_PopupMenu_Overflow = 0x7f13025f;
        public static int Widget_AppCompat_PopupWindow = 0x7f130260;
        public static int Widget_AppCompat_ProgressBar = 0x7f130261;
        public static int Widget_AppCompat_ProgressBar_Horizontal = 0x7f130262;
        public static int Widget_AppCompat_RatingBar = 0x7f130263;
        public static int Widget_AppCompat_RatingBar_Indicator = 0x7f130264;
        public static int Widget_AppCompat_RatingBar_Small = 0x7f130265;
        public static int Widget_AppCompat_SearchView = 0x7f130266;
        public static int Widget_AppCompat_SearchView_ActionBar = 0x7f130267;
        public static int Widget_AppCompat_SeekBar = 0x7f130268;
        public static int Widget_AppCompat_SeekBar_Discrete = 0x7f130269;
        public static int Widget_AppCompat_Spinner = 0x7f13026a;
        public static int Widget_AppCompat_Spinner_DropDown = 0x7f13026b;
        public static int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f13026c;
        public static int Widget_AppCompat_Spinner_Underlined = 0x7f13026d;
        public static int Widget_AppCompat_TextView = 0x7f13026e;
        public static int Widget_AppCompat_TextView_SpinnerItem = 0x7f13026f;
        public static int Widget_AppCompat_Toolbar = 0x7f130270;
        public static int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f130271;
        public static int Widget_Compat_NotificationActionContainer = 0x7f130272;
        public static int Widget_Compat_NotificationActionText = 0x7f130273;
        public static int Widget_Design_AppBarLayout = 0x7f130274;
        public static int Widget_Design_BottomNavigationView = 0x7f130275;
        public static int Widget_Design_BottomSheet_Modal = 0x7f130276;
        public static int Widget_Design_CollapsingToolbar = 0x7f130277;
        public static int Widget_Design_FloatingActionButton = 0x7f130278;
        public static int Widget_Design_NavigationView = 0x7f130279;
        public static int Widget_Design_ScrimInsetsFrameLayout = 0x7f13027a;
        public static int Widget_Design_Snackbar = 0x7f13027b;
        public static int Widget_Design_TabLayout = 0x7f13027c;
        public static int Widget_Design_TextInputLayout = 0x7f13027d;
        public static int Widget_MaterialComponents_ActionBar_Primary = 0x7f13027e;
        public static int Widget_MaterialComponents_ActionBar_PrimarySurface = 0x7f13027f;
        public static int Widget_MaterialComponents_ActionBar_Solid = 0x7f130280;
        public static int Widget_MaterialComponents_ActionBar_Surface = 0x7f130281;
        public static int Widget_MaterialComponents_AppBarLayout_Primary = 0x7f130282;
        public static int Widget_MaterialComponents_AppBarLayout_PrimarySurface = 0x7f130283;
        public static int Widget_MaterialComponents_AppBarLayout_Surface = 0x7f130284;
        public static int Widget_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f130285;
        public static int Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f130286;
        public static int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f130287;
        public static int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f130288;
        public static int Widget_MaterialComponents_Badge = 0x7f130289;
        public static int Widget_MaterialComponents_BottomAppBar = 0x7f13028a;
        public static int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f13028b;
        public static int Widget_MaterialComponents_BottomAppBar_PrimarySurface = 0x7f13028c;
        public static int Widget_MaterialComponents_BottomNavigationView = 0x7f13028d;
        public static int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f13028e;
        public static int Widget_MaterialComponents_BottomNavigationView_PrimarySurface = 0x7f13028f;
        public static int Widget_MaterialComponents_BottomSheet = 0x7f130290;
        public static int Widget_MaterialComponents_BottomSheet_Modal = 0x7f130291;
        public static int Widget_MaterialComponents_Button = 0x7f130292;
        public static int Widget_MaterialComponents_Button_Icon = 0x7f130293;
        public static int Widget_MaterialComponents_Button_OutlinedButton = 0x7f130294;
        public static int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f130295;
        public static int Widget_MaterialComponents_Button_TextButton = 0x7f130296;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f130297;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog_Flush = 0x7f130298;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f130299;
        public static int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f13029a;
        public static int Widget_MaterialComponents_Button_TextButton_Snackbar = 0x7f13029b;
        public static int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f13029c;
        public static int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f13029d;
        public static int Widget_MaterialComponents_CardView = 0x7f13029e;
        public static int Widget_MaterialComponents_CheckedTextView = 0x7f13029f;
        public static int Widget_MaterialComponents_ChipGroup = 0x7f1302a4;
        public static int Widget_MaterialComponents_Chip_Action = 0x7f1302a0;
        public static int Widget_MaterialComponents_Chip_Choice = 0x7f1302a1;
        public static int Widget_MaterialComponents_Chip_Entry = 0x7f1302a2;
        public static int Widget_MaterialComponents_Chip_Filter = 0x7f1302a3;
        public static int Widget_MaterialComponents_CompoundButton_CheckBox = 0x7f1302a5;
        public static int Widget_MaterialComponents_CompoundButton_RadioButton = 0x7f1302a6;
        public static int Widget_MaterialComponents_CompoundButton_Switch = 0x7f1302a7;
        public static int Widget_MaterialComponents_ExtendedFloatingActionButton = 0x7f1302a8;
        public static int Widget_MaterialComponents_ExtendedFloatingActionButton_Icon = 0x7f1302a9;
        public static int Widget_MaterialComponents_FloatingActionButton = 0x7f1302aa;
        public static int Widget_MaterialComponents_Light_ActionBar_Solid = 0x7f1302ab;
        public static int Widget_MaterialComponents_MaterialButtonToggleGroup = 0x7f1302ac;
        public static int Widget_MaterialComponents_MaterialCalendar = 0x7f1302ad;
        public static int Widget_MaterialComponents_MaterialCalendar_Day = 0x7f1302ae;
        public static int Widget_MaterialComponents_MaterialCalendar_DayTextView = 0x7f1302b2;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Invalid = 0x7f1302af;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f1302b0;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Today = 0x7f1302b1;
        public static int Widget_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f1302b3;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton = 0x7f1302b4;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderDivider = 0x7f1302b5;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderLayout = 0x7f1302b6;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderSelection = 0x7f1302b7;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f1302b8;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderTitle = 0x7f1302b9;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f1302ba;
        public static int Widget_MaterialComponents_MaterialCalendar_Item = 0x7f1302bb;
        public static int Widget_MaterialComponents_MaterialCalendar_Year = 0x7f1302bc;
        public static int Widget_MaterialComponents_MaterialCalendar_Year_Selected = 0x7f1302bd;
        public static int Widget_MaterialComponents_MaterialCalendar_Year_Today = 0x7f1302be;
        public static int Widget_MaterialComponents_NavigationView = 0x7f1302bf;
        public static int Widget_MaterialComponents_PopupMenu = 0x7f1302c0;
        public static int Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1302c1;
        public static int Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1302c2;
        public static int Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1302c3;
        public static int Widget_MaterialComponents_Snackbar = 0x7f1302c4;
        public static int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f1302c5;
        public static int Widget_MaterialComponents_TabLayout = 0x7f1302c6;
        public static int Widget_MaterialComponents_TabLayout_Colored = 0x7f1302c7;
        public static int Widget_MaterialComponents_TabLayout_PrimarySurface = 0x7f1302c8;
        public static int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f1302c9;
        public static int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f1302ca;
        public static int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f1302cb;
        public static int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f1302cc;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f1302cd;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f1302ce;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f1302cf;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f1302d0;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f1302d1;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f1302d2;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f1302d3;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f1302d4;
        public static int Widget_MaterialComponents_TextView = 0x7f1302d5;
        public static int Widget_MaterialComponents_Toolbar = 0x7f1302d6;
        public static int Widget_MaterialComponents_Toolbar_Primary = 0x7f1302d7;
        public static int Widget_MaterialComponents_Toolbar_PrimarySurface = 0x7f1302d8;
        public static int Widget_MaterialComponents_Toolbar_Surface = 0x7f1302d9;
        public static int Widget_Support_CoordinatorLayout = 0x7f1302da;

        private style() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\material\R$xml.smali */
    public static final class xml {
        public static int standalone_badge = 0x7f150003;
        public static int standalone_badge_gravity_bottom_end = 0x7f150004;
        public static int standalone_badge_gravity_bottom_start = 0x7f150005;
        public static int standalone_badge_gravity_top_start = 0x7f150006;

        private xml() {
        }
    }

    private R() {
    }
}
