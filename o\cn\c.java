package o.cn;

import android.text.TextUtils;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import o.cc.e;
import o.et.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cn\c.smali */
public final class c implements e<i> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static boolean b;
    private static char[] c;
    private static int d;
    private static boolean e;
    private static char f;
    private static int g;
    private static char h;
    private static char i;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        g = 1;
        a();
        TextUtils.getOffsetAfter("", 0);
        int i2 = g + 57;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    static void a() {
        c = new char[]{61595, 61584, 61580, 61578, 61585, 61767, 61599, 61589, 61574, 61587, 61582, 61579, 61586, 61588, 61576, 61591, 61811, 61810, 61594, 61815, 61577, 61775, 61774, 61778, 61593, 61782, 61777, 61783, 61781, 61818, 61573, 61813, 61789};
        e = true;
        b = true;
        d = 782102823;
        i = (char) 35249;
        f = (char) 1862;
        h = (char) 46094;
        a = (char) 2626;
    }

    static void init$0() {
        $$a = new byte[]{46, 74, -29, UtilitiesSDKConstants.SRP_LABEL_MAC};
        $$b = 72;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.cn.c.$$a
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r6 = 121 - r6
            int r8 = r8 * 2
            int r8 = r8 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r9
            r9 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.c.m(short, byte, byte, java.lang.Object[]):void");
    }

    @Override // o.cc.e
    public final /* synthetic */ i d(String str, String str2, int i2, String str3) {
        int i3 = j + 65;
        g = i3 % 128;
        boolean z = i3 % 2 == 0;
        i b2 = b(str, str2, i2, str3);
        switch (z) {
            case true:
                int i4 = 50 / 0;
            default:
                return b2;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0020, code lost:
    
        r5 = new java.lang.Object[1];
        k(null, 127 - android.view.View.resolveSize(0, 0), null, "\u0084\u008a\u0093\u008c\u0082\u0092\u0084\u0081\u008b\u0091\u0090\u0090\u008d", r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x003d, code lost:
    
        if (r15.b(((java.lang.String) r5[0]).intern()) == false) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x003f, code lost:
    
        o.ee.g.c();
        r3 = new java.lang.Object[1];
        l("⮰般씢渻鐤稍ä弝ﾙꦏꪇ쌷ﴑƺ⎝੦笣撔\ue0cfʔ遅晅", android.graphics.drawable.Drawable.resolveOpacity(0, 0) + 21, r3);
        r0 = ((java.lang.String) r3[0]).intern();
        r1 = new java.lang.Object[1];
        k(null, android.graphics.Color.red(0) + 127, null, "\u008c\u0085\u0093\u0082\u0095\u0086\u009c\u009b\u009a\u0086\u0085\u0082\u008b\u008e\u0088\u0084\u0099\u0086\u0084\u008a\u008b\u0095\u0082\u0088\u0094\u0086\u0098\u0086\u0097\u0096\u0084\u008a\u008b\u0095\u0082\u0088\u0094\u008c\u0089\u0084\u0088", r1);
        o.ee.g.d(r0, ((java.lang.String) r1[0]).intern());
        r4 = new o.cn.d();
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0138, code lost:
    
        r11 = r4.a(r11, r12, r13, r14, r15);
        r12 = o.cn.c.g + 25;
        o.cn.c.j = r12 % 128;
        r12 = r12 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x014b, code lost:
    
        return r11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0074, code lost:
    
        r8 = new java.lang.Object[1];
        k(null, (android.view.ViewConfiguration.getTouchSlop() >> 8) + 127, null, "\u0085\u0082\u008b\u008e\u0088\u0084\u0099", r8);
        r0 = r15.r(((java.lang.String) r8[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x008f, code lost:
    
        r7 = o.cn.c.j + 93;
        o.cn.c.g = r7 % 128;
        r7 = r7 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x009d, code lost:
    
        switch(r0.hashCode()) {
            case 49524: goto L22;
            default: goto L28;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00a1, code lost:
    
        r7 = new java.lang.Object[1];
        k(null, (android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 127, null, "\u009c\u009b\u009d", r7);
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00bc, code lost:
    
        if (r0.equals(((java.lang.String) r7[0]).intern()) == false) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00be, code lost:
    
        r3 = 21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00c2, code lost:
    
        switch(r3) {
            case 80: goto L28;
            default: goto L27;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00c5, code lost:
    
        r3 = o.cn.c.g + 99;
        o.cn.c.j = r3 % 128;
        r3 = r3 % 2;
        r3 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00d2, code lost:
    
        switch(r3) {
            case 0: goto L32;
            default: goto L30;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00d5, code lost:
    
        r12 = new java.lang.StringBuilder();
        r14 = new java.lang.Object[1];
        k(null, 127 - android.graphics.Color.alpha(0), null, "\u0086¡\u0085\u0082\u008b\u008e\u0088\u0084\u0099\u0086\u0088\u0082\u0095\u0086\u0088\u0084\u008c\u0089\u0084 \u0084\u008a\u008b\u0095\u0082\u0088\u0094\u008e\u0084\u008c\u0092\u0086\u008c\u0085\u008b\u0095\u0086\u0082\u0081\u0086\u0084\u008a\u009f\u0089\u0085\u009e", r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0100, code lost:
    
        throw new o.ei.i(r12.append(((java.lang.String) r14[0]).intern()).append(r0).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0101, code lost:
    
        r0 = new o.cn.b();
        o.ee.g.c();
        r5 = new java.lang.Object[1];
        l("⮰般씢渻鐤稍ä弝ﾙꦏꪇ쌷ﴑƺ⎝੦笣撔\ue0cfʔ遅晅", android.view.KeyEvent.keyCodeFromString("") + 21, r5);
        r3 = ((java.lang.String) r5[0]).intern();
        r1 = new java.lang.Object[1];
        k(null, (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16) + 127, null, "\u008c\u0085\u0093\u0082\u0095\u0086\u009c\u009b\u009d\u0086\u0084\u008a\u008b\u0095\u0082\u0088\u0094\u0086\u0098\u0086\u0097\u0096\u0084\u008a\u008b\u0095\u0082\u0088\u0094\u008c\u0089\u0084\u0088", r1);
        o.ee.g.d(r3, ((java.lang.String) r1[0]).intern());
        r4 = r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00c0, code lost:
    
        r3 = 'P';
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00d1, code lost:
    
        r3 = 65535;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x014c, code lost:
    
        r11 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x014d, code lost:
    
        o.ee.g.c();
        r13 = new java.lang.Object[1];
        l("⮰般씢渻鐤稍ä弝ﾙꦏꪇ쌷ﴑƺ⎝੦笣撔\ue0cfʔ遅晅", android.text.AndroidCharacter.getMirror('0') - 27, r13);
        r12 = ((java.lang.String) r13[0]).intern();
        r14 = new java.lang.Object[1];
        k(null, 127 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), null, "\u0097\u0096\u0084\u008a\u008b\u0095\u0082\u0088\u0094\u008c\u0089\u0084\u0088", r14);
        o.ee.g.a(r12, ((java.lang.String) r14[0]).intern(), r11);
        r13 = new java.lang.Object[1];
        l("⹏妮鎶汯潸\ufdd5峩押\ue5eaᆏ흤\uf660诹ﮁ\uead2\ude76❦๊畊⧮\u09deݦ絇؛閭洅㾀憂\uf4b3脽\u09deݦ", 32 - (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), r13);
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x019d, code lost:
    
        throw new o.ei.i(((java.lang.String) r13[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x001d, code lost:
    
        if (r12 != null) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0017, code lost:
    
        if (r12 != null) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x019e, code lost:
    
        r13 = new java.lang.Object[1];
        k(null, 127 - (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), null, "\u008f\u0085\u008b\u008e\u008e\u008b\u008d\u0086\u008c\u008b\u0086\u008a\u0089\u0085\u0088\u0084\u0081\u0087\u0084\u0086\u0085\u0084\u0083\u0082\u0081", r13);
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x01ba, code lost:
    
        throw new o.ei.i(((java.lang.String) r13[0]).intern());
     */
    @Override // o.cc.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.List<o.et.i> a(java.lang.String r11, java.lang.String r12, int r13, java.lang.String r14, o.eg.b r15) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 470
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.c.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    private static i b(String str, String str2, int i2, String str3) {
        i iVar = new i(str, str2, i2, str3);
        int i3 = g + 9;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return iVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 814
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.c.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.c.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
