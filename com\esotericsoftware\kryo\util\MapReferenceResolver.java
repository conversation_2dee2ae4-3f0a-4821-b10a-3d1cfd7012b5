package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.ReferenceResolver;
import java.util.ArrayList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\MapReferenceResolver.smali */
public class MapReferenceResolver implements ReferenceResolver {
    private static final int DEFAULT_CAPACITY = 2048;
    protected Kryo kryo;
    private final int maximumCapacity;
    protected final ArrayList<Object> readObjects;
    protected final IdentityObjectIntMap<Object> writtenObjects;

    public MapReferenceResolver() {
        this(2048);
    }

    public MapReferenceResolver(int maximumCapacity) {
        this.writtenObjects = new IdentityObjectIntMap<>();
        this.readObjects = new ArrayList<>();
        this.maximumCapacity = maximumCapacity;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setKryo(Kryo kryo) {
        this.kryo = kryo;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int addWrittenObject(Object object) {
        int id = this.writtenObjects.size;
        this.writtenObjects.put(object, id);
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int getWrittenId(Object object) {
        return this.writtenObjects.get(object, -1);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int nextReadId(Class type) {
        int id = this.readObjects.size();
        this.readObjects.add(null);
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setReadObject(int id, Object object) {
        this.readObjects.set(id, object);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public Object getReadObject(Class type, int id) {
        return this.readObjects.get(id);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void reset() {
        int size = this.readObjects.size();
        this.readObjects.clear();
        if (size > this.maximumCapacity) {
            this.readObjects.trimToSize();
            this.readObjects.ensureCapacity(this.maximumCapacity);
        }
        this.writtenObjects.clear(this.maximumCapacity);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public boolean useReferences(Class type) {
        return (Util.isWrapperClass(type) || Util.isEnum(type)) ? false : true;
    }
}
