package o.g;

import android.os.SystemClock;
import android.view.View;
import android.view.ViewConfiguration;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\g\b.smali */
public final class b implements o.ee.a<LocalAuthenticationErrorReason> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    private static final /* synthetic */ b[] d;
    public static final b e;
    private static int g;
    private static int h;
    private static int[] i;

    static void c() {
        i = new int[]{110729783, 280224727, -787423342, -1634070357, 1812212694, 537810861, 1321922803, 1267125376, 1716598845, -1683562601, -708366627, 1553960305, -412382229, 1545618523, -288083891, 636832596, 2012831804, -1613298847};
    }

    static void init$0() {
        $$a = new byte[]{102, 46, -74, -23};
        $$b = 100;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r9 = r9 + 115
            byte[] r0 = o.g.b.$$a
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r9 = r8
            r3 = r9
            r4 = r2
            r8 = r7
            goto L2c
        L16:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
        L2c:
            int r7 = r7 + r3
            int r8 = r8 + 1
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.g.b.j(byte, int, int, java.lang.Object[]):void");
    }

    private b(String str, int i2) {
    }

    private static /* synthetic */ b[] e() {
        int i2 = h + 49;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return new b[]{e, c, b, a};
            default:
                b[] bVarArr = new b[2];
                bVarArr[1] = e;
                bVarArr[1] = c;
                bVarArr[3] = b;
                bVarArr[4] = a;
                return bVarArr;
        }
    }

    public static b valueOf(String str) {
        int i2 = h + 7;
        g = i2 % 128;
        int i3 = i2 % 2;
        b bVar = (b) Enum.valueOf(b.class, str);
        int i4 = g + 43;
        h = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    public static b[] values() {
        int i2 = g + 83;
        h = i2 % 128;
        int i3 = i2 % 2;
        b[] bVarArr = (b[]) d.clone();
        int i4 = h + 109;
        g = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                int i5 = 92 / 0;
                return bVarArr;
            default:
                return bVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = h + 83;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                b();
                throw null;
            default:
                return b();
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        g = 1;
        c();
        Object[] objArr = new Object[1];
        f(new int[]{-556841123, 1338170594, -963596552, -1339212871, 541002017, -659392665, -1560818383, 884571720, 467993811, -555291012}, View.MeasureSpec.makeMeasureSpec(0, 0) + 20, objArr);
        e = new b(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        f(new int[]{-1377330818, 767700023, 133748479, 1211698120, 618088688, -819661681, -1057597173, 1871069734}, 13 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
        c = new b(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        f(new int[]{2115197394, -349481307, -584170059, 1082334230, 2081563629, -220632112}, 9 - View.getDefaultSize(0, 0), objArr3);
        b = new b(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        f(new int[]{-1418938956, 314422014, 1102041828, -1527333835, 1875317603, -1777350171, -1671054038, 1406001130}, 14 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr4);
        a = new b(((String) objArr4[0]).intern(), 3);
        d = e();
        int i2 = g + 83;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    /* renamed from: o.g.b$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\g\b$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] c;

        static {
            a = 0;
            b = 1;
            int[] iArr = new int[b.values().length];
            c = iArr;
            try {
                iArr[b.e.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                c[b.b.ordinal()] = 2;
                int i = a;
                int i2 = (i ^ 5) + ((i & 5) << 1);
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[b.c.ordinal()] = 3;
                int i3 = b + 85;
                a = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[b.a.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    public final LocalAuthenticationErrorReason b() {
        switch (AnonymousClass2.c[ordinal()]) {
            case 1:
                return LocalAuthenticationErrorReason.UserNotRecognized;
            case 2:
                return LocalAuthenticationErrorReason.Forbidden;
            case 3:
                return LocalAuthenticationErrorReason.InvalidFormat;
            case 4:
                LocalAuthenticationErrorReason localAuthenticationErrorReason = LocalAuthenticationErrorReason.InternalError;
                int i2 = h + 31;
                g = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        return localAuthenticationErrorReason;
                    default:
                        int i3 = 92 / 0;
                        return localAuthenticationErrorReason;
                }
            default:
                LocalAuthenticationErrorReason localAuthenticationErrorReason2 = LocalAuthenticationErrorReason.InternalError;
                int i4 = g + 7;
                h = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 23 : 'T') {
                    case 23:
                        throw null;
                    default:
                        return localAuthenticationErrorReason2;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 830
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.g.b.f(int[], int, java.lang.Object[]):void");
    }
}
