package o.el;

import android.content.Context;
import android.graphics.PointF;
import android.telephony.cdma.CdmaCellLocation;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import fr.antelop.sdk.card.emvapplication.EmvApplicationStatus;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import o.co.a;
import o.ei.c;
import o.eo.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\el\d.smali */
public abstract class d implements o.ee.d<EmvApplication> {
    private Map<String, a> a;
    private final String b;
    private b c;
    private a d;
    private final o.ca.a e = new o.ca.a(c.c());
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 0;
    private static int k = 1;
    private static char i = 28348;
    private static char g = 15439;
    private static char j = 28649;
    private static char f = 58440;

    public abstract o.ey.e<? extends o.fc.d> a(String str);

    protected abstract d b(String str);

    public abstract boolean o();

    public abstract String r();

    public abstract o.ei.a z();

    public d(String str) {
        this.b = str;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0031, code lost:
    
        if (r5.a != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x00a1, code lost:
    
        r6 = r5.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x00a3, code lost:
    
        if (r6 == null) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x00a5, code lost:
    
        r1 = o.el.d.k + 9;
        o.el.d.h = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x00af, code lost:
    
        if ((r1 % 2) != 0) goto L40;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x00b1, code lost:
    
        r0.d = r6.c();
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00b8, code lost:
    
        r0.d = r6.c();
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x00bf, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00c2, code lost:
    
        return r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0034, code lost:
    
        r6 = new java.util.HashMap();
        r2 = r5.a.entrySet().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0048, code lost:
    
        if (r2.hasNext() == false) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x004a, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x004d, code lost:
    
        switch(r3) {
            case 1: goto L23;
            default: goto L54;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0053, code lost:
    
        r3 = o.el.d.k + com.esotericsoftware.asm.Opcodes.DMUL;
        o.el.d.h = r3 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x005d, code lost:
    
        if ((r3 % 2) == 0) goto L26;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x005f, code lost:
    
        r3 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x0064, code lost:
    
        switch(r3) {
            case 83: goto L53;
            default: goto L55;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x0081, code lost:
    
        r3 = r2.next();
        r6.put(r3.getKey(), r3.getValue().c());
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0067, code lost:
    
        r3 = r2.next();
        r6.put(r3.getKey(), r3.getValue().c());
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x009d, code lost:
    
        r3 = 70 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0062, code lost:
    
        r3 = 'S';
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x0050, code lost:
    
        r0.a = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x004c, code lost:
    
        r3 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x002b, code lost:
    
        if (r5.a != null) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.el.d c(o.el.b r6) {
        /*
            r5 = this;
            int r0 = o.el.d.k
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.el.d.h = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L10
            r0 = 46
            goto L12
        L10:
            r0 = 90
        L12:
            r1 = 0
            switch(r0) {
                case 90: goto L21;
                default: goto L16;
            }
        L16:
            java.lang.String r0 = r5.b
            o.el.d r0 = r5.b(r0)
            r0.c = r6
            java.util.Map<java.lang.String, o.co.a> r6 = r5.a
            goto L2e
        L21:
            java.lang.String r0 = r5.b
            o.el.d r0 = r5.b(r0)
            r0.c = r6
            java.util.Map<java.lang.String, o.co.a> r6 = r5.a
            if (r6 == 0) goto La1
        L2d:
            goto L34
        L2e:
            r2 = 68
            int r2 = r2 / r1
            if (r6 == 0) goto La1
            goto L2d
        L34:
            java.util.HashMap r6 = new java.util.HashMap
            r6.<init>()
            java.util.Map<java.lang.String, o.co.a> r2 = r5.a
            java.util.Set r2 = r2.entrySet()
            java.util.Iterator r2 = r2.iterator()
        L44:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L4c
            r3 = 1
            goto L4d
        L4c:
            r3 = r1
        L4d:
            switch(r3) {
                case 1: goto L53;
                default: goto L50;
            }
        L50:
            r0.a = r6
            goto La1
        L53:
            int r3 = o.el.d.k
            int r3 = r3 + 107
            int r4 = r3 % 128
            o.el.d.h = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L62
            r3 = 11
            goto L64
        L62:
            r3 = 83
        L64:
            switch(r3) {
                case 83: goto L81;
                default: goto L67;
            }
        L67:
            java.lang.Object r3 = r2.next()
            java.util.Map$Entry r3 = (java.util.Map.Entry) r3
            java.lang.Object r4 = r3.getKey()
            java.lang.String r4 = (java.lang.String) r4
            java.lang.Object r3 = r3.getValue()
            o.co.a r3 = (o.co.a) r3
            o.co.a r3 = r3.c()
            r6.put(r4, r3)
            goto L9b
        L81:
            java.lang.Object r3 = r2.next()
            java.util.Map$Entry r3 = (java.util.Map.Entry) r3
            java.lang.Object r4 = r3.getKey()
            java.lang.String r4 = (java.lang.String) r4
            java.lang.Object r3 = r3.getValue()
            o.co.a r3 = (o.co.a) r3
            o.co.a r3 = r3.c()
            r6.put(r4, r3)
            goto L44
        L9b:
            r3 = 70
            int r3 = r3 / r1
            goto L44
        L9f:
            r6 = move-exception
            throw r6
        La1:
            o.co.a r6 = r5.d
            if (r6 == 0) goto Lc2
            int r1 = o.el.d.k
            int r1 = r1 + 9
            int r2 = r1 % 128
            o.el.d.h = r2
            int r1 = r1 % 2
            if (r1 != 0) goto Lb8
            o.co.a r6 = r6.c()
            r0.d = r6
            goto Lc2
        Lb8:
            o.co.a r6 = r6.c()
            r0.d = r6
            r6 = 0
            throw r6     // Catch: java.lang.Throwable -> Lc0
        Lc0:
            r6 = move-exception
            throw r6
        Lc2:
            return r0
        Lc3:
            r6 = move-exception
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.c(o.el.b):o.el.d");
    }

    public final String n() {
        int i2 = k + 43;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        String str = this.b;
        int i5 = i3 + Opcodes.DREM;
        k = i5 % 128;
        switch (i5 % 2 == 0 ? '*' : (char) 28) {
            case 28:
                return str;
            default:
                int i6 = 96 / 0;
                return str;
        }
    }

    public final EmvApplicationStatus t() {
        int i2 = h + 69;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                this.c.d();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c.d();
        }
    }

    public final b s() {
        int i2 = h;
        int i3 = i2 + 95;
        k = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0) {
            case false:
                b bVar = this.c;
                int i4 = i2 + 87;
                k = i4 % 128;
                switch (i4 % 2 != 0 ? 'J' : (char) 1) {
                    case 'J':
                        return bVar;
                    default:
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void b(b bVar) {
        int i2 = k + Opcodes.LSHL;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        this.c = bVar;
        int i5 = i3 + 71;
        k = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 0 : (char) 4) {
            case 0:
                int i6 = 91 / 0;
                return;
            default:
                return;
        }
    }

    public final Map<String, a> p() {
        int i2 = h + 99;
        int i3 = i2 % 128;
        k = i3;
        Object obj = null;
        switch (i2 % 2 == 0 ? (char) 28 : 'M') {
            case 28:
                obj.hashCode();
                throw null;
            default:
                Map<String, a> map = this.a;
                int i4 = i3 + 29;
                h = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        return map;
                    default:
                        throw null;
                }
        }
    }

    public final void e(Map<String, a> map) {
        int i2 = k + Opcodes.LUSHR;
        h = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.a = map;
        switch (z) {
            case false:
                return;
            default:
                int i3 = 41 / 0;
                return;
        }
    }

    public final EmvApplicationActivationMethod q() {
        switch (this.d == null ? (char) 25 : 'b') {
            case Opcodes.FADD /* 98 */:
                EmvApplicationActivationMethod emvApplicationActivationMethod = new EmvApplicationActivationMethod(this.d);
                int i2 = k + 29;
                h = i2 % 128;
                int i3 = i2 % 2;
                return emvApplicationActivationMethod;
            default:
                int i4 = k + 1;
                h = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    public final void b(a aVar) {
        int i2 = k + Opcodes.LMUL;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        this.d = aVar;
        int i5 = i3 + 1;
        k = i5 % 128;
        int i6 = i5 % 2;
    }

    public final void c(Context context, String str, final AntelopCallback antelopCallback) throws WalletValidationException {
        int i2 = k + 65;
        h = i2 % 128;
        int i3 = i2 % 2;
        if (s() != b.e) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在", 13 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
            Object[] objArr2 = new Object[1];
            P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在䑵₪討黢䩛鷧討黢þ在쭟枒╛닟\ud9cc땧", 30 - View.MeasureSpec.getSize(0), objArr2);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
        }
        Map<String, a> map = this.a;
        if (map == null) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 14, objArr3);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr3[0]).intern());
        }
        a aVar = map.get(str);
        if (aVar == null) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.Unknown;
            Object[] objArr4 = new Object[1];
            P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在䑵₪討黢䩛鷧討黢þ在쭟枒╛닟\ud9cc땧", 30 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr4);
            throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr4[0]).intern());
        }
        if (!aVar.e().hasToSubmitActivationCode()) {
            WalletValidationErrorCode walletValidationErrorCode5 = WalletValidationErrorCode.Unexpected;
            Object[] objArr5 = new Object[1];
            P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在䑵₪討黢䩛鷧討黢þ在쭟枒╛닟\ud9cc땧", 30 - KeyEvent.getDeadChar(0, 0), objArr5);
            String intern = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            P("盧䕊\udbc9ࢴ椲㴝慯\u1ac1\uf530삤㒖᧱寑葲\uda7aޅ꼌퇟╛닟\ud9cc땧楁㟕烰岝戃粀륦꜏\ueb5f뛖\ud909ꤠ㜖曍泓꿞筲⟁Ṩ⛘", View.MeasureSpec.getMode(0) + 41, objArr6);
            throw new WalletValidationException(walletValidationErrorCode5, intern, ((String) objArr6[0]).intern());
        }
        o.eo.e d = d();
        switch (d != null) {
            case false:
                break;
            default:
                if (d.t() != h.a) {
                    WalletValidationErrorCode walletValidationErrorCode6 = WalletValidationErrorCode.WrongState;
                    Object[] objArr7 = new Object[1];
                    P("饗䍔鏐࠙", 4 - View.MeasureSpec.getSize(0), objArr7);
                    String intern2 = ((String) objArr7[0]).intern();
                    Object[] objArr8 = new Object[1];
                    P("씹梎\uf74e瑀쀕\uf226烰岝ੱ⦟ᬶ\uda42ῒ琏玝䷿寑葲\udaea評箾ពＩ읕\uec4a肝㒖᧱寑葲\uda7aޅ\udbc9ࢴ凿ꐀ菧\ue3f9ᾃ\ue4d6\ud8af㮉Ṩ⛘", View.MeasureSpec.getMode(0) + 43, objArr8);
                    throw new WalletValidationException(walletValidationErrorCode6, intern2, ((String) objArr8[0]).intern());
                }
                break;
        }
        this.e.d(context, this, aVar, new o.ca.b() { // from class: o.el.d.2
            private static int e = 0;
            private static int d = 1;

            /* JADX WARN: Removed duplicated region for block: B:9:0x0039  */
            @Override // o.ca.b
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void d() {
                /*
                    r3 = this;
                    int r0 = o.el.d.AnonymousClass2.e
                    r1 = r0 ^ 49
                    r2 = r0 & 49
                    int r2 = r2 << 1
                    int r1 = r1 + r2
                    int r2 = r1 % 128
                    o.el.d.AnonymousClass2.d = r2
                    int r1 = r1 % 2
                    if (r1 != 0) goto L14
                    r1 = 42
                    goto L16
                L14:
                    r1 = 60
                L16:
                    switch(r1) {
                        case 42: goto L1e;
                        default: goto L19;
                    }
                L19:
                    fr.antelop.sdk.AntelopCallback r1 = r2
                    if (r1 == 0) goto L34
                    goto L31
                L1e:
                    fr.antelop.sdk.AntelopCallback r1 = r2
                    r2 = 20
                    int r2 = r2 / 0
                    if (r1 == 0) goto L29
                    r1 = 69
                    goto L2b
                L29:
                    r1 = 26
                L2b:
                    switch(r1) {
                        case 69: goto L39;
                        default: goto L2e;
                    }
                L2e:
                    goto L38
                L2f:
                    r0 = move-exception
                    throw r0
                L31:
                    r1 = 32
                    goto L35
                L34:
                    r1 = 4
                L35:
                    switch(r1) {
                        case 32: goto L39;
                        default: goto L38;
                    }
                L38:
                    goto L70
                L39:
                    r1 = r0 & 101(0x65, float:1.42E-43)
                    r0 = r0 | 101(0x65, float:1.42E-43)
                    int r1 = r1 + r0
                    int r0 = r1 % 128
                    o.el.d.AnonymousClass2.d = r0
                    int r1 = r1 % 2
                    if (r1 != 0) goto L49
                    r0 = 53
                    goto L4b
                L49:
                    r0 = 19
                L4b:
                    switch(r0) {
                        case 19: goto L54;
                        default: goto L4e;
                    }
                L4e:
                    fr.antelop.sdk.AntelopCallback r0 = r2
                    r0.onSuccess()
                    goto L69
                L54:
                    fr.antelop.sdk.AntelopCallback r0 = r2
                    r0.onSuccess()
                    int r0 = o.el.d.AnonymousClass2.d
                    r1 = r0 ^ 21
                    r0 = r0 & 21
                    int r0 = r0 << 1
                    int r1 = r1 + r0
                    int r0 = r1 % 128
                    o.el.d.AnonymousClass2.e = r0
                    int r1 = r1 % 2
                    goto L70
                L69:
                    r0 = 0
                    r0.hashCode()     // Catch: java.lang.Throwable -> L6e
                    throw r0     // Catch: java.lang.Throwable -> L6e
                L6e:
                    r0 = move-exception
                    throw r0
                L70:
                    int r0 = o.el.d.AnonymousClass2.d
                    r1 = r0 | 61
                    int r1 = r1 << 1
                    r0 = r0 ^ 61
                    int r1 = r1 - r0
                    int r0 = r1 % 128
                    o.el.d.AnonymousClass2.e = r0
                    int r1 = r1 % 2
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.el.d.AnonymousClass2.d():void");
            }

            @Override // o.ca.b
            public final void d(o.bb.d dVar) {
                int i4 = d + 65;
                int i5 = i4 % 128;
                e = i5;
                int i6 = i4 % 2;
                AntelopCallback antelopCallback2 = antelopCallback;
                switch (antelopCallback2 == null) {
                    case true:
                        break;
                    default:
                        int i7 = (i5 ^ 47) + ((i5 & 47) << 1);
                        d = i7 % 128;
                        int i8 = i7 % 2;
                        antelopCallback2.onError(o.bv.c.c(dVar).d());
                        int i9 = e;
                        int i10 = ((i9 | Opcodes.DDIV) << 1) - (i9 ^ Opcodes.DDIV);
                        d = i10 % 128;
                        int i11 = i10 % 2;
                        break;
                }
                int i12 = d;
                int i13 = (i12 ^ 41) + ((i12 & 41) << 1);
                e = i13 % 128;
                int i14 = i13 % 2;
            }
        });
        int i4 = h + 23;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0028, code lost:
    
        if (s() == o.el.b.e) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0148, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r0 = new java.lang.Object[1];
        P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在", 14 - android.view.View.resolveSizeAndState(0, 0, 0), r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0164, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, ((java.lang.String) r0[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x002f, code lost:
    
        if (r11 == null) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0035, code lost:
    
        if (e(r11) == false) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0037, code lost:
    
        r0 = r9.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x003b, code lost:
    
        if (r0 == null) goto L40;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0045, code lost:
    
        if (r0.e().hasToSubmitActivationCode() == false) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0051, code lost:
    
        if (d().t() != o.eo.h.a) goto L36;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0053, code lost:
    
        r9.e.d(r10, r9, r11, new o.el.d.AnonymousClass1(r9));
        r10 = o.el.d.h + 47;
        o.el.d.k = r10 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0067, code lost:
    
        if ((r10 % 2) != 0) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0069, code lost:
    
        r10 = '%';
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x006e, code lost:
    
        switch(r10) {
            case 37: goto L32;
            default: goto L31;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0071, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0073, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x006c, code lost:
    
        r10 = ' ';
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0076, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r0 = new java.lang.Object[1];
        P("饗䍔鏐࠙", 3 - android.graphics.ImageFormat.getBitsPerPixel(0), r0);
        r12 = ((java.lang.String) r0[0]).intern();
        r1 = new java.lang.Object[1];
        P("씹梎\uf74e瑀쀕\uf226烰岝ੱ⦟ᬶ\uda42ῒ琏玝䷿寑葲\udaea評箾ពＩ읕\uec4a肝㒖᧱寑葲\uda7aޅ\udbc9ࢴ凿ꐀ菧\ue3f9ᾃ\ue4d6\ud8af㮉Ṩ⛘", 42 - android.os.Process.getGidForName(""), r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00a7, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, r12, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x00a8, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r0 = new java.lang.Object[1];
        P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在䑵₪討黢䩛鷧討黢þ在쭟枒╛닟\ud9cc땧", (android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)) + 29, r0);
        r12 = ((java.lang.String) r0[0]).intern();
        r1 = new java.lang.Object[1];
        P("盧䕊\udbc9ࢴ椲㴝慯\u1ac1\uf530삤㒖᧱寑葲\uda7aޅ꼌퇟╛닟\ud9cc땧\ue580䫖沮㰨쀕\uf226戃粀륦꜏㔿袍\ue9c7ሧ퓫䨛討黢䩛鷧討黢þ在楁㟕\ud9cc땧鐞䃕", android.view.View.MeasureSpec.getSize(0) + 51, r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x00db, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, r12, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x00dc, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.Unknown;
        r0 = new java.lang.Object[1];
        P("\ue433υ虝\uef82\udbb1鉂쟜끏獱\ue733討黢þ在䑵₪討黢䩛鷧討黢þ在쭟枒╛닟\ud9cc땧", 30 - android.text.TextUtils.indexOf("", "", 0), r0);
        r12 = ((java.lang.String) r0[0]).intern();
        r1 = new java.lang.Object[1];
        P("ꑵﶉ椲㴝慯\u1ac1\uf530삤㒖᧱寑葲\uda7aޅ꼌퇟╛닟\ud9cc땧\ud909ꤠ㜖曍泓꿞筲⟁ੱ⦟燀䪑\ue06c\uf2ad", 33 - (android.view.ViewConfiguration.getScrollBarFadeDuration() >> 16), r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x010d, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, r12, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x010e, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.InvalidFormat;
        r1 = new java.lang.Object[1];
        P("䑵₪討黢䩛鷧討黢þ在뢵\ua83e㥐ⷻ", (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16) + 14, r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x012a, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x012b, code lost:
    
        r11 = fr.antelop.sdk.exception.WalletValidationErrorCode.Mandatory;
        r1 = new java.lang.Object[1];
        P("䑵₪討黢䩛鷧討黢þ在뢵\ua83e㥐ⷻ", 13 - android.text.TextUtils.lastIndexOf("", '0'), r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0147, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r11, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0022, code lost:
    
        if (s() == o.el.b.e) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(android.content.Context r10, java.lang.String r11, final fr.antelop.sdk.AntelopCallback r12) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 372
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.d(android.content.Context, java.lang.String, fr.antelop.sdk.AntelopCallback):void");
    }

    private static boolean e(String str) {
        switch (str.length() == 0) {
            case false:
                int i2 = h + Opcodes.LSUB;
                int i3 = i2 % 128;
                k = i3;
                int i4 = i2 % 2;
                int i5 = i3 + Opcodes.DNEG;
                h = i5 % 128;
                int i6 = i5 % 2;
                return true;
            default:
                int i7 = h + 45;
                k = i7 % 128;
                int i8 = i7 % 2;
                return false;
        }
    }

    public final List<EmvApplicationActivationMethod> v() {
        if (this.a != null) {
            ArrayList arrayList = new ArrayList(this.a.size());
            Iterator<a> it = this.a.values().iterator();
            int i2 = k + 21;
            h = i2 % 128;
            int i3 = i2 % 2;
            while (it.hasNext()) {
                arrayList.add(new EmvApplicationActivationMethod(it.next()));
            }
            return arrayList;
        }
        int i4 = h + 15;
        k = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void x() {
        /*
            r3 = this;
            java.util.Map<java.lang.String, o.co.a> r0 = r3.a
            if (r0 != 0) goto L6
            return
        L6:
            java.util.Set r0 = r0.entrySet()
            java.util.Iterator r0 = r0.iterator()
        Le:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L16
            r1 = 0
            goto L17
        L16:
            r1 = 1
        L17:
            switch(r1) {
                case 0: goto L1b;
                default: goto L1a;
            }
        L1a:
            goto L55
        L1b:
            java.lang.Object r1 = r0.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r1 = r1.getValue()
            o.co.a r1 = (o.co.a) r1
            fr.antelop.sdk.card.EmvApplicationActivationMethodType r1 = r1.e()
            boolean r1 = r1.hasToSubmitActivationCode()
            if (r1 == 0) goto L34
            r1 = 75
            goto L36
        L34:
            r1 = 67
        L36:
            switch(r1) {
                case 67: goto Le;
                default: goto L39;
            }
        L39:
            int r1 = o.el.d.h
            int r1 = r1 + 119
            int r2 = r1 % 128
            o.el.d.k = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L48
            r1 = 72
            goto L4a
        L48:
            r1 = 24
        L4a:
            r0.remove()
            switch(r1) {
                case 72: goto L51;
                default: goto L50;
            }
        L50:
            goto Le
        L51:
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L53
        L53:
            r0 = move-exception
            throw r0
        L55:
            int r0 = o.el.d.h
            int r0 = r0 + 31
            int r1 = r0 % 128
            o.el.d.k = r1
            int r0 = r0 % 2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.x():void");
    }

    public final boolean y() {
        int i2 = k;
        int i3 = i2 + 49;
        h = i3 % 128;
        Object obj = null;
        if (i3 % 2 != 0) {
            obj.hashCode();
            throw null;
        }
        if (this.d == null) {
            int i4 = i2 + 45;
            h = i4 % 128;
            int i5 = i4 % 2;
            return false;
        }
        Map<String, a> map = this.a;
        switch (map == null) {
            default:
                Iterator<a> it = map.values().iterator();
                while (it.hasNext()) {
                    int i6 = k + 65;
                    h = i6 % 128;
                    int i7 = i6 % 2;
                    if (it.next().d().equals(this.d.d())) {
                        b((a) null);
                        return true;
                    }
                }
            case true:
                return false;
        }
    }

    public final String u() {
        int i2 = k + 91;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? ':' : 'Y') {
            case Opcodes.DUP /* 89 */:
                return d().e();
            default:
                int i3 = 34 / 0;
                return d().e();
        }
    }

    private o.eo.e d() {
        int i2 = h + Opcodes.LUSHR;
        k = i2 % 128;
        /*  JADX ERROR: Method code generation error
            java.lang.ClassCastException: class jadx.core.dex.nodes.InsnNode cannot be cast to class jadx.core.dex.instructions.SwitchInsn (jadx.core.dex.nodes.InsnNode and jadx.core.dex.instructions.SwitchInsn are in unnamed module of loader 'app')
            	at jadx.core.codegen.RegionGen.makeSwitch(RegionGen.java:245)
            	at jadx.core.dex.regions.SwitchRegion.generate(SwitchRegion.java:84)
            	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
            	at jadx.core.dex.regions.Region.generate(Region.java:35)
            	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
            	at jadx.core.codegen.MethodGen.addRegionInsns(MethodGen.java:297)
            	at jadx.core.codegen.MethodGen.addInstructions(MethodGen.java:276)
            	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:406)
            	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:335)
            	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$3(ClassGen.java:301)
            	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
            	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
            	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
            	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
            */
        /*
            this = this;
            o.ei.c r0 = o.ei.c.c()
            java.util.Map r0 = r0.i()
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.el.d.h
            int r1 = r1 + 125
            int r2 = r1 % 128
            o.el.d.k = r2
            int r1 = r1 % 2
            r2 = 0
            r3 = 1
            if (r1 != 0) goto L21
            r1 = r2
            goto L22
        L21:
            r1 = r3
        L22:
            switch(r1) {
                case 1: goto L25;
                default: goto L25;
            }
        L25:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L4e
            int r1 = o.el.d.h
            int r1 = r1 + 115
            int r4 = r1 % 128
            o.el.d.k = r4
            int r1 = r1 % 2
            java.lang.Object r1 = r0.next()
            o.eo.e r1 = (o.eo.e) r1
            java.lang.String r4 = r5.n()
            boolean r4 = r1.a(r4)
            if (r4 == 0) goto L47
            r4 = r3
            goto L48
        L47:
            r4 = r2
        L48:
            switch(r4) {
                case 0: goto L4c;
                default: goto L4b;
            }
        L4b:
            goto L4d
        L4c:
            goto L25
        L4d:
            return r1
        L4e:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            int r1 = android.view.ViewConfiguration.getMinimumFlingVelocity()
            int r1 = r1 >> 16
            int r1 = r1 + 46
            java.lang.Object[] r3 = new java.lang.Object[r3]
            java.lang.String r4 = "稸㽢办ꄣ씯곃牔⫆嫎碲㒖᧱寑葲\uda7aޅ饤\ude69\ue580䫖沮㰨쀕\uf226戃粀륦꜏\ueb5f뛖ꁼ郍い\ue323ᖃꗳ\uf233\uee6f烰岝\uee94˘獱\ue733鏐࠙"
            P(r4, r1, r3)
            r1 = r3[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.String r2 = r5.n()
            java.lang.Object[] r2 = new java.lang.Object[]{r2}
            java.lang.String r1 = java.lang.String.format(r1, r2)
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.d():o.eo.e");
    }

    /* JADX WARN: Code restructure failed: missing block: B:39:0x0034, code lost:
    
        continue;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String w() {
        /*
            r5 = this;
            int r0 = o.el.d.h
            int r0 = r0 + 59
            int r1 = r0 % 128
            o.el.d.k = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Le
            r0 = 0
            goto Lf
        Le:
            r0 = 1
        Lf:
            r1 = 0
            switch(r0) {
                case 1: goto L24;
                default: goto L13;
            }
        L13:
            o.ei.c r0 = o.ei.c.c()
            java.util.Map r0 = r0.i()
            java.util.Collection r0 = r0.values()
            r0.iterator()
            goto L9a
        L24:
            o.ei.c r0 = o.ei.c.c()
            java.util.Map r0 = r0.i()
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L34:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L99
            java.lang.Object r2 = r0.next()
            o.eo.e r2 = (o.eo.e) r2
            java.util.LinkedHashMap r3 = r2.A()
            if (r3 == 0) goto L98
            java.util.LinkedHashMap r2 = r2.A()
            java.util.Collection r2 = r2.values()
            java.util.Iterator r2 = r2.iterator()
        L52:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L5c
            r3 = 37
            goto L5e
        L5c:
            r3 = 99
        L5e:
            switch(r3) {
                case 37: goto L62;
                default: goto L61;
            }
        L61:
            goto L98
        L62:
            java.lang.Object r3 = r2.next()
            o.eo.d r3 = (o.eo.d) r3
            java.lang.String r4 = r5.n()
            boolean r4 = r3.g(r4)
            if (r4 == 0) goto L8c
            int r0 = o.el.d.k
            int r0 = r0 + 23
            int r2 = r0 % 128
            o.el.d.h = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L83
            java.lang.String r0 = r3.h()
            return r0
        L83:
            r3.h()
            r1.hashCode()     // Catch: java.lang.Throwable -> L8a
            throw r1     // Catch: java.lang.Throwable -> L8a
        L8a:
            r0 = move-exception
            throw r0
        L8c:
            int r3 = o.el.d.h
            int r3 = r3 + 69
            int r4 = r3 % 128
            o.el.d.k = r4
            int r3 = r3 % 2
            goto L52
        L98:
            goto L34
        L99:
            return r1
        L9a:
            throw r1     // Catch: java.lang.Throwable -> L9b
        L9b:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.w():java.lang.String");
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void P(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 570
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.d.P(java.lang.String, int, java.lang.Object[]):void");
    }
}
