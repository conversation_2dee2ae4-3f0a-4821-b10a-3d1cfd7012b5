package kotlin.reflect;

import kotlin.Metadata;

/* compiled from: typeOf.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\u0011\u0010\u0000\u001a\u00020\u0001\"\u0006\b\u0000\u0010\u0002\u0018\u0001H\u0087\b¨\u0006\u0003"}, d2 = {"typeOf", "Lkotlin/reflect/KType;", "T", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\reflect\TypeOfKt.smali */
public final class TypeOfKt {
    public static final /* synthetic */ <T> KType typeOf() {
        throw new UnsupportedOperationException("This function is implemented as an intrinsic on all supported platforms.");
    }
}
