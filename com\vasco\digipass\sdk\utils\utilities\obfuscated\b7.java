package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\b7.smali */
public interface b7 {
    public static final w A;
    public static final w B;
    public static final w C;
    public static final w D;
    public static final w E;
    public static final w F;
    public static final w G;
    public static final w H;
    public static final w I;
    public static final w J;
    public static final w K;
    public static final w L;
    public static final w M;
    public static final w N;
    public static final w O;
    public static final w P;
    public static final w Q;
    public static final w R;
    public static final w S;
    public static final w T;
    public static final w U;
    public static final w V;
    public static final w W;
    public static final w X;
    public static final w Y;
    public static final w a;
    public static final w b;
    public static final w c;
    public static final w d;
    public static final w e;
    public static final w f;
    public static final w g;
    public static final w h;
    public static final w i;
    public static final w j;
    public static final w k;
    public static final w l;
    public static final w m;
    public static final w n;

    /* renamed from: o, reason: collision with root package name */
    public static final w f21o;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.3.132.0");
        a = wVar;
        b = wVar.a("1");
        c = wVar.a("2");
        d = wVar.a("3");
        e = wVar.a("4");
        f = wVar.a("5");
        g = wVar.a("6");
        h = wVar.a("7");
        i = wVar.a("8");
        j = wVar.a("9");
        k = wVar.a("10");
        l = wVar.a("15");
        m = wVar.a("16");
        n = wVar.a("17");
        f21o = wVar.a("22");
        p = wVar.a("23");
        q = wVar.a("24");
        r = wVar.a("25");
        s = wVar.a("26");
        t = wVar.a("27");
        u = wVar.a("28");
        v = wVar.a("29");
        w = wVar.a("30");
        x = wVar.a("31");
        y = wVar.a("32");
        z = wVar.a("33");
        A = wVar.a("34");
        B = wVar.a("35");
        C = wVar.a("36");
        D = wVar.a("37");
        E = wVar.a("38");
        F = wVar.a("39");
        G = j8.S;
        H = j8.Y;
        w wVar2 = new w("1.3.132.1");
        I = wVar2;
        J = wVar2.a("11.0");
        K = wVar2.a("11.1");
        L = wVar2.a("11.2");
        M = wVar2.a("11.3");
        N = wVar2.a("14.0");
        O = wVar2.a("14.1");
        P = wVar2.a("14.2");
        Q = wVar2.a("14.3");
        R = wVar2.a("15.0");
        S = wVar2.a("15.1");
        T = wVar2.a("15.2");
        U = wVar2.a("15.3");
        V = wVar2.a("16.0");
        W = wVar2.a("16.1");
        X = wVar2.a("16.2");
        Y = wVar2.a("16.3");
    }
}
