package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.nio.charset.Charset;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0006\u001a\u00020\u0005¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\t"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/g;", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding;", "", "salt", "fingerprint", "Landroid/content/Context;", "context", "<init>", "(Landroid/content/Context;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\g.smali */
public final class g implements DeviceBinding {
    private final Context a;

    public g(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        this.a = context;
    }

    @Override // com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding
    public String fingerprint(String salt) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        StringBuilder sb = new StringBuilder();
        try {
            if (salt.length() == 0) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_NULL, null, 2, null);
            }
            if (salt.length() < 64) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_TOO_SHORT, null, 2, null);
            }
            sb.append(new f(this.a).a());
            sb.append(new j(this.a).b());
            sb.append(salt);
            sb.append(n.a.a());
            String sb2 = sb.toString();
            Intrinsics.checkNotNullExpressionValue(sb2, "toBeHashed.toString()");
            Charset forName = Charset.forName("UTF-8");
            Intrinsics.checkNotNullExpressionValue(forName, "forName(charsetName)");
            byte[] bytes = sb2.getBytes(forName);
            Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
            UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, bytes);
            if (hash.getReturnCode() == 0) {
                return p.a(hash.getOutputData());
            }
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        } catch (DeviceBindingSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }
}
