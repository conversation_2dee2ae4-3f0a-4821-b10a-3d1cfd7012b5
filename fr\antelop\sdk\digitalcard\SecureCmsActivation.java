package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.i;
import o.v.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCmsActivation.smali */
public final class SecureCmsActivation implements CustomerAuthenticatedProcess {
    private final e innerSecureCmsActivation;

    public SecureCmsActivation(e eVar) {
        this.innerSecureCmsActivation = eVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureCmsActivation.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureCmsActivation.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureCmsActivation.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureCmsActivation.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void launch(Context context, SecurePinInput securePinInput, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureCmsActivation.b(context, securePinInput, new o.p.e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureCmsActivation));
    }

    public final void launch(Context context, SecurePinInput securePinInput, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureCmsActivation.e(context, securePinInput, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureCmsActivation));
    }

    public final boolean isPinCodeUpdated() throws WalletValidationException {
        return this.innerSecureCmsActivation.a();
    }

    public final void setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.innerSecureCmsActivation.d(customerAuthenticatedProcessActivityCallback);
    }
}
