package o.em;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import java.util.List;
import o.aq.b;
import o.em.d;
import o.eo.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\g.smali */
public final class g extends d<o.eo.j> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static long b;
    private static int c;
    private static char[] d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        j();
        ViewConfiguration.getGlobalActionKeyTimeout();
        int i = e + 47;
        c = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$a = new byte[]{29, -34, -102, -75};
        $$b = Opcodes.ATHROW;
    }

    static void j() {
        a = -6822822336208132253L;
        d = new char[]{11435, 27616, 41511, 53060, 34825, 16859, 6812, 53868, 43777, 25807, 15745, 62812, 19995, 22052, 4465, 55484, 33759, 19251, 12913, 5514, 21189, 39689, 49217, 39223, 56955, 6050, 19658, 33851, 64867, 12983, 27647, 41780, 6254, 20905, 38612, 52783, 1911, 31928, 46591, 60712, 46842, 61869, 14446, 25402, 44013, 53947, 7520, 11422, 27624, 41509, 63845, 12716, 18685};
        b = -7564675168898815095L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = 4 - r9
            int r8 = r8 + 102
            byte[] r0 = o.em.g.$$a
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r4 = r0[r9]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r9 = -r9
            int r7 = r7 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.g.m(int, short, int, java.lang.Object[]):void");
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ long a() {
        int i = e + 13;
        c = i % 128;
        int i2 = i % 2;
        long a2 = super.a();
        int i3 = c + 73;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? '*' : 'W') {
            case Opcodes.POP /* 87 */:
                return a2;
            default:
                int i4 = 53 / 0;
                return a2;
        }
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ void b() {
        int i = e + Opcodes.LSHR;
        c = i % 128;
        int i2 = i % 2;
        super.b();
        int i3 = e + 87;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 31 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ List<o.eo.j> d() {
        int i = c + Opcodes.LSHR;
        e = i % 128;
        int i2 = i % 2;
        List<o.eo.j> d2 = super.d();
        int i3 = e + Opcodes.LREM;
        c = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    @Override // o.em.d
    protected final /* synthetic */ o.eg.b d(o.eo.j jVar) throws o.eg.d {
        int i = c + 69;
        e = i % 128;
        o.eo.j jVar2 = jVar;
        switch (i % 2 == 0) {
            case true:
                return a(jVar2);
            default:
                a(jVar2);
                throw null;
        }
    }

    @Override // o.em.d
    protected final /* synthetic */ o.eo.j e(o.eg.b bVar) throws o.eg.d {
        int i = c + 85;
        e = i % 128;
        boolean z = i % 2 != 0;
        o.eo.j c2 = c(bVar);
        switch (z) {
            case true:
                int i2 = 8 / 0;
            default:
                return c2;
        }
    }

    private static o.eg.b a(o.eo.j jVar) throws o.eg.d {
        Object obj;
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        g("憚茐", 57992 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
        bVar.d(((String) objArr[0]).intern(), jVar.e());
        Object[] objArr2 = new Object[1];
        g("憀՜ꠤ伖\uf2ea駇", 25819 - ExpandableListView.getPackedPositionGroup(0L), objArr2);
        bVar.d(((String) objArr2[0]).intern(), jVar.a().e());
        Object[] objArr3 = new Object[1];
        g("憃Ꮤ蔶㝺꣎娾챨䇒\uf30d敭ᛏ蠝㩫", Color.alpha(0) + 29269, objArr3);
        bVar.d(((String) objArr3[0]).intern(), jVar.b());
        Object[] objArr4 = new Object[1];
        l((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), View.MeasureSpec.makeMeasureSpec(0, 0), Drawable.resolveOpacity(0, 0) + 3, objArr4);
        bVar.d(((String) objArr4[0]).intern(), jVar.d());
        Object[] objArr5 = new Object[1];
        l((char) (Color.red(0) + 58337), 3 - Color.green(0), 11 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr5);
        bVar.d(((String) objArr5[0]).intern(), jVar.c());
        Object[] objArr6 = new Object[1];
        g("憖썜\u242d褟\ueadd侹낽ታ眿\ud819", 41687 - ExpandableListView.getPackedPositionGroup(0L), objArr6);
        bVar.d(((String) objArr6[0]).intern(), jVar.g().getTime());
        Object[] objArr7 = new Object[1];
        l((char) (Color.blue(0) + 31387), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 13, (ViewConfiguration.getScrollBarSize() >> 8) + 6, objArr7);
        bVar.d(((String) objArr7[0]).intern(), jVar.h());
        Object[] objArr8 = new Object[1];
        l((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 14637), View.combineMeasuredStates(0, 0) + 19, '4' - AndroidCharacter.getMirror('0'), objArr8);
        bVar.d(((String) objArr8[0]).intern(), jVar.f());
        switch (jVar.j() == null) {
            case false:
                int i = e + 83;
                c = i % 128;
                if (i % 2 == 0) {
                    Object[] objArr9 = new Object[1];
                    g("憐풬ௌ縕딳\ue87b庒閦죟㼇牅ꥹ", 46381 >> (ViewConfiguration.getWindowTouchSlop() / 2), objArr9);
                    obj = objArr9[0];
                } else {
                    Object[] objArr10 = new Object[1];
                    g("憐풬ௌ縕딳\ue87b庒閦죟㼇牅ꥹ", (ViewConfiguration.getWindowTouchSlop() >> 8) + 46381, objArr10);
                    obj = objArr10[0];
                }
                bVar.d(((String) obj).intern(), jVar.j().getTime());
                break;
        }
        Object[] objArr11 = new Object[1];
        l((char) (TextUtils.getOffsetAfter("", 0) + 46483), 24 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 17, objArr11);
        bVar.d(((String) objArr11[0]).intern(), jVar.i());
        int i2 = c + 47;
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return bVar;
            default:
                int i3 = 42 / 0;
                return bVar;
        }
    }

    private static o.eo.j c(o.eg.b bVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        g("憚茐", 57991 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr);
        String r = bVar.r(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g("憀՜ꠤ伖\uf2ea駇", 25819 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        j.b bVar2 = (j.b) bVar.d(j.b.class, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g("憃Ꮤ蔶㝺꣎娾챨䇒\uf30d敭ᛏ蠝㩫", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 29269, objArr3);
        String r2 = bVar.r(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        l((char) View.combineMeasuredStates(0, 0), 1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 3, objArr4);
        String r3 = bVar.r(((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        l((char) ((ViewConfiguration.getTouchSlop() >> 8) + 58337), TextUtils.lastIndexOf("", '0') + 4, 10 - TextUtils.getOffsetBefore("", 0), objArr5);
        String r4 = bVar.r(((String) objArr5[0]).intern());
        Object[] objArr6 = new Object[1];
        g("憖썜\u242d褟\ueadd侹낽ታ眿\ud819", 41687 - (ViewConfiguration.getTapTimeout() >> 16), objArr6);
        Date e2 = bVar.e(((String) objArr6[0]).intern(), true);
        Object[] objArr7 = new Object[1];
        l((char) (KeyEvent.normalizeMetaState(0) + 31387), 13 - KeyEvent.normalizeMetaState(0), 6 - KeyEvent.normalizeMetaState(0), objArr7);
        String r5 = bVar.r(((String) objArr7[0]).intern());
        Object[] objArr8 = new Object[1];
        l((char) (TextUtils.lastIndexOf("", '0') + 14638), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, View.combineMeasuredStates(0, 0) + 4, objArr8);
        String r6 = bVar.r(((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        g("憐풬ௌ縕딳\ue87b庒閦죟㼇牅ꥹ", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 46381, objArr9);
        Date b2 = bVar.b(((String) objArr9[0]).intern(), true);
        Object[] objArr10 = new Object[1];
        l((char) (46482 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 23 - (ViewConfiguration.getLongPressTimeout() >> 16), 17 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr10);
        o.eo.j jVar = new o.eo.j(r, bVar2, r2, r3, r4, e2, r5, r6, b2, bVar.j(((String) objArr10[0]).intern()));
        int i = c + 75;
        e = i % 128;
        int i2 = i % 2;
        return jVar;
    }

    public final void e(Context context, String str, final d.e<o.eo.j> eVar) throws WalletValidationException {
        int i = e + 35;
        c = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        g("憥\uf33b䓃홤⬂벷๙揗\uf49a䘨\udbddⵖ븊Ꮃ敟\uf6f9䮑\udd06⻀聴ᔆ暅\uf844䷶\ude8f』藅\u176d栂ﶫ佃ꃸ", 37537 - TextUtils.getOffsetBefore("", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((char) (View.resolveSize(0, 0) + 39489), 39 - TextUtils.lastIndexOf("", '0', 0), Color.red(0) + 7, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!o.ei.c.c().q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            l((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (ViewConfiguration.getTapTimeout() >> 16) + 47, 6 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g("憤\ue00f报\ue548柢\ue996桽\uead1浨\uef56熿\uf023狛\uf52a眗例硍\ufaf0粐Ｚ䇐쀾䊭쒋䜤질䡯쨙䲼콎凼펟刽퓮坛\ud9e1宖\uda37峔\udf6a℞ꎲ", Color.argb(0, 0, 0, 0) + 33181, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.aq.b(context, new b.d() { // from class: o.em.g.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int b;
            private static int c;
            private static int g;
            private static byte[] h;
            private static int i;
            private static short[] j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                g = 0;
                i = 1;
                h = new byte[]{113, -106, -103, 104, -104, 105, -73, 113, 98, -127, -114, 114, 125, -125, -115, 66, -99, -109, 101, 104, -73, 122, 98, -127, -114, 71, -101, 124, -111, -110, -103, -125, 114, 99, -99, 43, 99, 109, -103, -109, -104, -117, 67, -107, -101, -99, 98, 98, -127, -114, 95, -97, -114, 73, 111, -33, 99, -99, 40, 101, -98, 99, -100, -111, 99};
                a = 909053615;
                b = -349661985;
                c = -299136882;
            }

            static void init$0() {
                $$a = new byte[]{75, 105, 70, 99};
                $$b = 75;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void k(short r6, byte r7, short r8, java.lang.Object[] r9) {
                /*
                    int r7 = r7 * 4
                    int r7 = r7 + 1
                    byte[] r0 = o.em.g.AnonymousClass3.$$a
                    int r6 = r6 * 3
                    int r6 = 4 - r6
                    int r8 = r8 * 2
                    int r8 = 110 - r8
                    byte[] r1 = new byte[r7]
                    int r7 = r7 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L19
                    r4 = r8
                    r3 = r2
                    r8 = r7
                    goto L2e
                L19:
                    r3 = r2
                L1a:
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    if (r3 != r7) goto L27
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L27:
                    int r3 = r3 + 1
                    r4 = r0[r6]
                    r5 = r8
                    r8 = r7
                    r7 = r5
                L2e:
                    int r7 = r7 + r4
                    int r6 = r6 + 1
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    goto L1a
                */
                throw new UnsupportedOperationException("Method not decompiled: o.em.g.AnonymousClass3.k(short, byte, short, java.lang.Object[]):void");
            }

            @Override // o.aq.b.d
            public final void e(List<o.eo.j> list) {
                g.this.a(list, new Date().getTime());
                eVar.e(list);
                int i3 = g + 39;
                i = i3 % 128;
                int i4 = i3 % 2;
            }

            @Override // o.aq.b.d
            public final void c(o.bb.d dVar) {
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                f((byte) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 670792163, (short) (ViewConfiguration.getScrollDefaultDelay() >> 16), Gravity.getAbsoluteGravity(0, 0) - 64, 586704391 + (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                f((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), ExpandableListView.getPackedPositionType(0L) + 670792194, (short) (ViewConfiguration.getKeyRepeatTimeout() >> 16), TextUtils.getCapsMode("", 0, 0) - 64, 586704419 - Drawable.resolveOpacity(0, 0), objArr6);
                o.ee.g.d(intern3, sb.append(((String) objArr6[0]).intern()).append(dVar.e()).toString());
                eVar.e(o.bv.c.c(dVar).d());
                int i3 = g + Opcodes.DNEG;
                i = i3 % 128;
                int i4 = i3 % 2;
            }

            /* JADX WARN: Code restructure failed: missing block: B:19:0x0205, code lost:
            
                if (r4 != false) goto L68;
             */
            /* JADX WARN: Code restructure failed: missing block: B:38:0x02cc, code lost:
            
                r3 = o.em.g.AnonymousClass3.$11 + 29;
                o.em.g.AnonymousClass3.$10 = r3 % 128;
                r3 = r3 % 2;
                r3 = r7;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(byte r18, int r19, short r20, int r21, int r22, java.lang.Object[] r23) {
                /*
                    Method dump skipped, instructions count: 924
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.em.g.AnonymousClass3.f(byte, int, short, int, int, java.lang.Object[]):void");
            }
        }, o.ei.c.c()).a(str);
        int i3 = e + 27;
        c = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 482
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.g.g(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 984
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.g.l(char, int, int, java.lang.Object[]):void");
    }
}
