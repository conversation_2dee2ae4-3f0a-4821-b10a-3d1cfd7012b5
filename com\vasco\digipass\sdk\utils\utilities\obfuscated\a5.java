package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a5.smali */
public class a5 {
    protected final byte[] a = {48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 97, 98, 99, 100, 101, 102};
    protected final byte[] b = new byte[128];

    public a5() {
        a();
    }

    protected void a() {
        int i = 0;
        int i2 = 0;
        while (true) {
            byte[] bArr = this.b;
            if (i2 >= bArr.length) {
                break;
            }
            bArr[i2] = -1;
            i2++;
        }
        while (true) {
            byte[] bArr2 = this.a;
            if (i >= bArr2.length) {
                byte[] bArr3 = this.b;
                bArr3[65] = bArr3[97];
                bArr3[66] = bArr3[98];
                bArr3[67] = bArr3[99];
                bArr3[68] = bArr3[100];
                bArr3[69] = bArr3[101];
                bArr3[70] = bArr3[102];
                return;
            }
            this.b[bArr2[i]] = (byte) i;
            i++;
        }
    }

    public int a(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws IOException {
        int i4 = i2 + i;
        int i5 = i3;
        while (i < i4) {
            int i6 = i + 1;
            int i7 = bArr[i] & 255;
            int i8 = i5 + 1;
            byte[] bArr3 = this.a;
            bArr2[i5] = bArr3[i7 >>> 4];
            i5 = i8 + 1;
            bArr2[i8] = bArr3[i7 & 15];
            i = i6;
        }
        return i5 - i3;
    }

    public int a(byte[] bArr, int i, int i2, OutputStream outputStream) throws IOException {
        if (i2 < 0) {
            return 0;
        }
        byte[] bArr2 = new byte[72];
        int i3 = i2;
        while (i3 > 0) {
            int min = Math.min(36, i3);
            outputStream.write(bArr2, 0, a(bArr, i, min, bArr2, 0));
            i += min;
            i3 -= min;
        }
        return i2 * 2;
    }

    byte[] a(String str, int i, int i2) throws IOException {
        if (str != null) {
            if (i < 0 || i2 < 0 || i > str.length() - i2) {
                throw new IndexOutOfBoundsException("invalid offset and/or length specified");
            }
            if ((i2 & 1) == 0) {
                int i3 = i2 >>> 1;
                byte[] bArr = new byte[i3];
                int i4 = 0;
                while (i4 < i3) {
                    int i5 = i + 1;
                    int i6 = i5 + 1;
                    int i7 = (this.b[str.charAt(i)] << 4) | this.b[str.charAt(i5)];
                    if (i7 >= 0) {
                        bArr[i4] = (byte) i7;
                        i4++;
                        i = i6;
                    } else {
                        throw new IOException("invalid characters encountered in Hex string");
                    }
                }
                return bArr;
            }
            throw new IOException("a hexadecimal encoding must have an even number of characters");
        }
        throw new NullPointerException("'str' cannot be null");
    }
}
