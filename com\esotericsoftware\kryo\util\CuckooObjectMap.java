package com.esotericsoftware.kryo.util;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.NoSuchElementException;
import java.util.Random;

@Deprecated
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap.smali */
public class CuckooObjectMap<K, V> {
    private static final int PRIME2 = -1105259343;
    private static final int PRIME3 = -1262997959;
    private static final int PRIME4 = -825114047;
    static Random random = new Random();
    int capacity;
    private int hashShift;
    private boolean isBigTable;
    K[] keyTable;
    private float loadFactor;
    private int mask;
    private int pushIterations;
    public int size;
    private int stashCapacity;
    int stashSize;
    private int threshold;
    V[] valueTable;

    public CuckooObjectMap() {
        this(32, 0.8f);
    }

    public CuckooObjectMap(int initialCapacity) {
        this(initialCapacity, 0.8f);
    }

    public CuckooObjectMap(int i, float f) {
        if (i < 0) {
            throw new IllegalArgumentException("initialCapacity must be >= 0: " + i);
        }
        if (i > 1073741824) {
            throw new IllegalArgumentException("initialCapacity is too large: " + i);
        }
        int nextPowerOfTwo = nextPowerOfTwo(i);
        this.capacity = nextPowerOfTwo;
        if (f <= 0.0f) {
            throw new IllegalArgumentException("loadFactor must be > 0: " + f);
        }
        this.loadFactor = f;
        this.isBigTable = (nextPowerOfTwo >>> 16) != 0;
        this.threshold = (int) (nextPowerOfTwo * f);
        this.mask = nextPowerOfTwo - 1;
        this.hashShift = 31 - Integer.numberOfTrailingZeros(nextPowerOfTwo);
        this.stashCapacity = Math.max(3, ((int) Math.ceil(Math.log(this.capacity))) * 2);
        this.pushIterations = Math.max(Math.min(this.capacity, 8), ((int) Math.sqrt(this.capacity)) / 8);
        K[] kArr = (K[]) new Object[this.capacity + this.stashCapacity];
        this.keyTable = kArr;
        this.valueTable = (V[]) new Object[kArr.length];
    }

    public CuckooObjectMap(CuckooObjectMap<? extends K, ? extends V> map) {
        this(map.capacity, map.loadFactor);
        this.stashSize = map.stashSize;
        Object[] objArr = map.keyTable;
        System.arraycopy(objArr, 0, this.keyTable, 0, objArr.length);
        Object[] objArr2 = map.valueTable;
        System.arraycopy(objArr2, 0, this.valueTable, 0, objArr2.length);
        this.size = map.size;
    }

    public V put(K key, V value) {
        if (key == null) {
            throw new IllegalArgumentException("key cannot be null.");
        }
        return put_internal(key, value);
    }

    private V put_internal(K key, V value) {
        int index4;
        K key4;
        K[] keyTable = this.keyTable;
        int mask = this.mask;
        boolean isBigTable = this.isBigTable;
        int hashCode = key.hashCode();
        int index1 = hashCode & mask;
        K key1 = keyTable[index1];
        if (key.equals(key1)) {
            V[] vArr = this.valueTable;
            V oldValue = vArr[index1];
            vArr[index1] = value;
            return oldValue;
        }
        int index2 = hash2(hashCode);
        K key2 = keyTable[index2];
        if (key.equals(key2)) {
            V[] vArr2 = this.valueTable;
            V oldValue2 = vArr2[index2];
            vArr2[index2] = value;
            return oldValue2;
        }
        int index3 = hash3(hashCode);
        K key3 = keyTable[index3];
        if (key.equals(key3)) {
            V[] vArr3 = this.valueTable;
            V oldValue3 = vArr3[index3];
            vArr3[index3] = value;
            return oldValue3;
        }
        if (!isBigTable) {
            index4 = -1;
            key4 = null;
        } else {
            int index42 = hash4(hashCode);
            K key42 = keyTable[index42];
            if (!key.equals(key42)) {
                index4 = index42;
                key4 = key42;
            } else {
                V[] vArr4 = this.valueTable;
                V oldValue4 = vArr4[index42];
                vArr4[index42] = value;
                return oldValue4;
            }
        }
        int i = this.capacity;
        int n = this.stashSize + i;
        while (i < n) {
            if (!key.equals(keyTable[i])) {
                i++;
            } else {
                V[] vArr5 = this.valueTable;
                V oldValue5 = vArr5[i];
                vArr5[i] = value;
                return oldValue5;
            }
        }
        if (key1 == null) {
            keyTable[index1] = key;
            this.valueTable[index1] = value;
            int i2 = this.size;
            this.size = i2 + 1;
            if (i2 >= this.threshold) {
                resize(this.capacity << 1);
            }
            return null;
        }
        if (key2 == null) {
            keyTable[index2] = key;
            this.valueTable[index2] = value;
            int i3 = this.size;
            this.size = i3 + 1;
            if (i3 >= this.threshold) {
                resize(this.capacity << 1);
            }
            return null;
        }
        if (key3 == null) {
            keyTable[index3] = key;
            this.valueTable[index3] = value;
            int i4 = this.size;
            this.size = i4 + 1;
            if (i4 >= this.threshold) {
                resize(this.capacity << 1);
            }
            return null;
        }
        if (isBigTable && key4 == null) {
            keyTable[index4] = key;
            this.valueTable[index4] = value;
            int i5 = this.size;
            this.size = i5 + 1;
            if (i5 >= this.threshold) {
                resize(this.capacity << 1);
            }
            return null;
        }
        push(key, value, index1, key1, index2, key2, index3, key3, index4, key4);
        return null;
    }

    public void putAll(CuckooObjectMap<K, V> map) {
        ensureCapacity(map.size);
        Iterator<Entry<K, V>> it = map.entries().iterator();
        while (it.hasNext()) {
            Entry<K, V> entry = it.next();
            put(entry.key, entry.value);
        }
    }

    private void putResize(K key, V value) {
        int index4;
        K key4;
        int hashCode = key.hashCode();
        int index1 = hashCode & this.mask;
        K[] kArr = this.keyTable;
        K key1 = kArr[index1];
        if (key1 == null) {
            kArr[index1] = key;
            this.valueTable[index1] = value;
            int i = this.size;
            this.size = i + 1;
            if (i >= this.threshold) {
                resize(this.capacity << 1);
                return;
            }
            return;
        }
        int index2 = hash2(hashCode);
        K[] kArr2 = this.keyTable;
        K key2 = kArr2[index2];
        if (key2 == null) {
            kArr2[index2] = key;
            this.valueTable[index2] = value;
            int i2 = this.size;
            this.size = i2 + 1;
            if (i2 >= this.threshold) {
                resize(this.capacity << 1);
                return;
            }
            return;
        }
        int index3 = hash3(hashCode);
        K[] kArr3 = this.keyTable;
        K key3 = kArr3[index3];
        if (key3 == null) {
            kArr3[index3] = key;
            this.valueTable[index3] = value;
            int i3 = this.size;
            this.size = i3 + 1;
            if (i3 >= this.threshold) {
                resize(this.capacity << 1);
                return;
            }
            return;
        }
        if (!this.isBigTable) {
            index4 = -1;
            key4 = null;
        } else {
            int index42 = hash4(hashCode);
            K[] kArr4 = this.keyTable;
            K key42 = kArr4[index42];
            if (key42 != null) {
                index4 = index42;
                key4 = key42;
            } else {
                kArr4[index42] = key;
                this.valueTable[index42] = value;
                int i4 = this.size;
                this.size = i4 + 1;
                if (i4 >= this.threshold) {
                    resize(this.capacity << 1);
                    return;
                }
                return;
            }
        }
        push(key, value, index1, key1, index2, key2, index3, key3, index4, key4);
    }

    private void push(K insertKey, V insertValue, int index1, K key1, int index2, K key2, int index3, K key3, int index4, K key4) {
        K evictedKey;
        V evictedValue;
        K[] keyTable;
        K[] keyTable2 = this.keyTable;
        V[] valueTable = this.valueTable;
        int pushIterations = this.mask;
        boolean isBigTable = this.isBigTable;
        int pushIterations2 = this.pushIterations;
        int n = isBigTable ? 4 : 3;
        V insertValue2 = insertValue;
        int index12 = index1;
        K key12 = key1;
        int index22 = index2;
        K key22 = key2;
        int index32 = index3;
        K key32 = key3;
        int index42 = index4;
        K key42 = key4;
        int i = 0;
        K insertKey2 = insertKey;
        while (true) {
            int pushIterations3 = pushIterations2;
            switch (random.nextInt(n)) {
                case 0:
                    evictedKey = key12;
                    V evictedValue2 = valueTable[index12];
                    keyTable2[index12] = insertKey2;
                    valueTable[index12] = insertValue2;
                    evictedValue = evictedValue2;
                    break;
                case 1:
                    evictedKey = key22;
                    V evictedValue3 = valueTable[index22];
                    keyTable2[index22] = insertKey2;
                    valueTable[index22] = insertValue2;
                    evictedValue = evictedValue3;
                    break;
                case 2:
                    evictedKey = key32;
                    V evictedValue4 = valueTable[index32];
                    keyTable2[index32] = insertKey2;
                    valueTable[index32] = insertValue2;
                    evictedValue = evictedValue4;
                    break;
                default:
                    evictedKey = key42;
                    V evictedValue5 = valueTable[index42];
                    keyTable2[index42] = insertKey2;
                    valueTable[index42] = insertValue2;
                    evictedValue = evictedValue5;
                    break;
            }
            int n2 = n;
            int hashCode = evictedKey.hashCode();
            index12 = hashCode & pushIterations;
            key12 = keyTable2[index12];
            if (key12 == null) {
                keyTable2[index12] = evictedKey;
                valueTable[index12] = evictedValue;
                int mask = this.size;
                this.size = mask + 1;
                if (mask >= this.threshold) {
                    resize(this.capacity << 1);
                    return;
                }
                return;
            }
            int mask2 = pushIterations;
            index22 = hash2(hashCode);
            key22 = keyTable2[index22];
            if (key22 == null) {
                keyTable2[index22] = evictedKey;
                valueTable[index22] = evictedValue;
                int i2 = this.size;
                this.size = i2 + 1;
                if (i2 >= this.threshold) {
                    resize(this.capacity << 1);
                    return;
                }
                return;
            }
            index32 = hash3(hashCode);
            key32 = keyTable2[index32];
            if (key32 == null) {
                keyTable2[index32] = evictedKey;
                valueTable[index32] = evictedValue;
                int i3 = this.size;
                this.size = i3 + 1;
                if (i3 >= this.threshold) {
                    resize(this.capacity << 1);
                    return;
                }
                return;
            }
            if (!isBigTable) {
                keyTable = keyTable2;
            } else {
                int index43 = hash4(hashCode);
                K key43 = keyTable2[index43];
                if (key43 != null) {
                    keyTable = keyTable2;
                    index42 = index43;
                    key42 = key43;
                } else {
                    keyTable2[index43] = evictedKey;
                    valueTable[index43] = evictedValue;
                    int i4 = this.size;
                    this.size = i4 + 1;
                    if (i4 >= this.threshold) {
                        resize(this.capacity << 1);
                        return;
                    }
                    return;
                }
            }
            int i5 = i + 1;
            if (i5 != pushIterations3) {
                K insertKey3 = evictedKey;
                V insertValue3 = evictedValue;
                i = i5;
                pushIterations2 = pushIterations3;
                insertKey2 = insertKey3;
                pushIterations = mask2;
                keyTable2 = keyTable;
                insertValue2 = insertValue3;
                n = n2;
            } else {
                putStash(evictedKey, evictedValue);
                return;
            }
        }
    }

    private void putStash(K key, V value) {
        int i = this.stashSize;
        if (i == this.stashCapacity) {
            resize(this.capacity << 1);
            put_internal(key, value);
            return;
        }
        int index = this.capacity + i;
        this.keyTable[index] = key;
        this.valueTable[index] = value;
        this.stashSize = i + 1;
        this.size++;
    }

    public V get(K key) {
        int hashCode = key.hashCode();
        int index = this.mask & hashCode;
        if (!key.equals(this.keyTable[index])) {
            index = hash2(hashCode);
            if (!key.equals(this.keyTable[index])) {
                index = hash3(hashCode);
                if (!key.equals(this.keyTable[index])) {
                    if (this.isBigTable) {
                        index = hash4(hashCode);
                        if (!key.equals(this.keyTable[index])) {
                            return getStash(key);
                        }
                    } else {
                        return getStash(key);
                    }
                }
            }
        }
        return this.valueTable[index];
    }

    private V getStash(K key) {
        K[] keyTable = this.keyTable;
        int i = this.capacity;
        int n = this.stashSize + i;
        while (i < n) {
            if (key.equals(keyTable[i])) {
                return this.valueTable[i];
            }
            i++;
        }
        return null;
    }

    public V get(K key, V defaultValue) {
        int hashCode = key.hashCode();
        int index = this.mask & hashCode;
        if (!key.equals(this.keyTable[index])) {
            index = hash2(hashCode);
            if (!key.equals(this.keyTable[index])) {
                index = hash3(hashCode);
                if (!key.equals(this.keyTable[index])) {
                    if (this.isBigTable) {
                        index = hash4(hashCode);
                        if (!key.equals(this.keyTable[index])) {
                            return getStash(key, defaultValue);
                        }
                    } else {
                        return getStash(key, defaultValue);
                    }
                }
            }
        }
        return this.valueTable[index];
    }

    private V getStash(K key, V defaultValue) {
        K[] keyTable = this.keyTable;
        int i = this.capacity;
        int n = this.stashSize + i;
        while (i < n) {
            if (key.equals(keyTable[i])) {
                return this.valueTable[i];
            }
            i++;
        }
        return defaultValue;
    }

    public V remove(K key) {
        int hashCode = key.hashCode();
        int index = this.mask & hashCode;
        if (key.equals(this.keyTable[index])) {
            this.keyTable[index] = null;
            V[] vArr = this.valueTable;
            V oldValue = vArr[index];
            vArr[index] = null;
            this.size--;
            return oldValue;
        }
        int index2 = hash2(hashCode);
        if (key.equals(this.keyTable[index2])) {
            this.keyTable[index2] = null;
            V[] vArr2 = this.valueTable;
            V oldValue2 = vArr2[index2];
            vArr2[index2] = null;
            this.size--;
            return oldValue2;
        }
        int index3 = hash3(hashCode);
        if (key.equals(this.keyTable[index3])) {
            this.keyTable[index3] = null;
            V[] vArr3 = this.valueTable;
            V oldValue3 = vArr3[index3];
            vArr3[index3] = null;
            this.size--;
            return oldValue3;
        }
        if (this.isBigTable) {
            int index4 = hash4(hashCode);
            if (key.equals(this.keyTable[index4])) {
                this.keyTable[index4] = null;
                V[] vArr4 = this.valueTable;
                V oldValue4 = vArr4[index4];
                vArr4[index4] = null;
                this.size--;
                return oldValue4;
            }
        }
        return removeStash(key);
    }

    V removeStash(K key) {
        K[] keyTable = this.keyTable;
        int i = this.capacity;
        int n = this.stashSize + i;
        while (i < n) {
            if (!key.equals(keyTable[i])) {
                i++;
            } else {
                V oldValue = this.valueTable[i];
                removeStashIndex(i);
                this.size--;
                return oldValue;
            }
        }
        return null;
    }

    void removeStashIndex(int index) {
        int i = this.stashSize - 1;
        this.stashSize = i;
        int lastIndex = this.capacity + i;
        if (index >= lastIndex) {
            this.valueTable[index] = null;
            return;
        }
        K[] kArr = this.keyTable;
        kArr[index] = kArr[lastIndex];
        V[] vArr = this.valueTable;
        vArr[index] = vArr[lastIndex];
        vArr[lastIndex] = null;
    }

    public void shrink(int maximumCapacity) {
        if (maximumCapacity < 0) {
            throw new IllegalArgumentException("maximumCapacity must be >= 0: " + maximumCapacity);
        }
        if (this.size > maximumCapacity) {
            maximumCapacity = this.size;
        }
        if (this.capacity <= maximumCapacity) {
            return;
        }
        resize(nextPowerOfTwo(maximumCapacity));
    }

    public void clear(int maximumCapacity) {
        if (this.capacity <= maximumCapacity) {
            clear();
        } else {
            this.size = 0;
            resize(maximumCapacity);
        }
    }

    public void clear() {
        K[] keyTable = this.keyTable;
        V[] valueTable = this.valueTable;
        int i = this.capacity + this.stashSize;
        while (true) {
            int i2 = i - 1;
            if (i > 0) {
                keyTable[i2] = null;
                valueTable[i2] = null;
                i = i2;
            } else {
                this.size = 0;
                this.stashSize = 0;
                return;
            }
        }
    }

    public boolean containsValue(Object value, boolean identity) {
        V[] valueTable = this.valueTable;
        if (value == null) {
            K[] keyTable = this.keyTable;
            int i = this.capacity + this.stashSize;
            while (true) {
                int i2 = i - 1;
                if (i > 0) {
                    if (keyTable[i2] != null && valueTable[i2] == null) {
                        return true;
                    }
                    i = i2;
                } else {
                    return false;
                }
            }
        } else if (identity) {
            int i3 = this.capacity + this.stashSize;
            while (true) {
                int i4 = i3 - 1;
                if (i3 > 0) {
                    if (valueTable[i4] == value) {
                        return true;
                    }
                    i3 = i4;
                } else {
                    return false;
                }
            }
        } else {
            int i5 = this.capacity + this.stashSize;
            while (true) {
                int i6 = i5 - 1;
                if (i5 > 0) {
                    if (value.equals(valueTable[i6])) {
                        return true;
                    }
                    i5 = i6;
                } else {
                    return false;
                }
            }
        }
    }

    public boolean containsKey(K key) {
        int hashCode = key.hashCode();
        int index = this.mask & hashCode;
        if (!key.equals(this.keyTable[index])) {
            int index2 = hash2(hashCode);
            if (!key.equals(this.keyTable[index2])) {
                int index3 = hash3(hashCode);
                if (!key.equals(this.keyTable[index3])) {
                    if (this.isBigTable) {
                        int index4 = hash4(hashCode);
                        if (key.equals(this.keyTable[index4])) {
                            return true;
                        }
                        return containsKeyStash(key);
                    }
                    return containsKeyStash(key);
                }
                return true;
            }
            return true;
        }
        return true;
    }

    private boolean containsKeyStash(K key) {
        K[] keyTable = this.keyTable;
        int i = this.capacity;
        int n = this.stashSize + i;
        while (i < n) {
            if (key.equals(keyTable[i])) {
                return true;
            }
            i++;
        }
        return false;
    }

    public K findKey(Object value, boolean identity) {
        V[] valueTable = this.valueTable;
        if (value == null) {
            K[] keyTable = this.keyTable;
            int i = this.capacity + this.stashSize;
            while (true) {
                int i2 = i - 1;
                if (i > 0) {
                    if (keyTable[i2] != null && valueTable[i2] == null) {
                        return keyTable[i2];
                    }
                    i = i2;
                } else {
                    return null;
                }
            }
        } else if (identity) {
            int i3 = this.capacity + this.stashSize;
            while (true) {
                int i4 = i3 - 1;
                if (i3 > 0) {
                    if (valueTable[i4] == value) {
                        return this.keyTable[i4];
                    }
                    i3 = i4;
                } else {
                    return null;
                }
            }
        } else {
            int i5 = this.capacity + this.stashSize;
            while (true) {
                int i6 = i5 - 1;
                if (i5 > 0) {
                    if (value.equals(valueTable[i6])) {
                        return this.keyTable[i6];
                    }
                    i5 = i6;
                } else {
                    return null;
                }
            }
        }
    }

    public void ensureCapacity(int additionalCapacity) {
        int sizeNeeded = this.size + additionalCapacity;
        if (sizeNeeded >= this.threshold) {
            resize(nextPowerOfTwo((int) (sizeNeeded / this.loadFactor)));
        }
    }

    private void resize(int i) {
        int i2 = this.capacity + this.stashSize;
        this.capacity = i;
        this.threshold = (int) (i * this.loadFactor);
        this.mask = i - 1;
        this.hashShift = 31 - Integer.numberOfTrailingZeros(i);
        this.stashCapacity = Math.max(3, ((int) Math.ceil(Math.log(i))) * 2);
        this.pushIterations = Math.max(Math.min(i, 8), ((int) Math.sqrt(i)) / 8);
        this.isBigTable = (this.capacity >>> 16) != 0;
        K[] kArr = this.keyTable;
        V[] vArr = this.valueTable;
        int i3 = this.stashCapacity;
        this.keyTable = (K[]) new Object[i + i3];
        this.valueTable = (V[]) new Object[i3 + i];
        int i4 = this.size;
        this.size = 0;
        this.stashSize = 0;
        if (i4 > 0) {
            for (int i5 = 0; i5 < i2; i5++) {
                K k = kArr[i5];
                if (k != null) {
                    putResize(k, vArr[i5]);
                }
            }
        }
    }

    private int hash2(int h) {
        int h2 = h * PRIME2;
        return ((h2 >>> this.hashShift) ^ h2) & this.mask;
    }

    private int hash3(int h) {
        int h2 = h * PRIME3;
        return ((h2 >>> this.hashShift) ^ h2) & this.mask;
    }

    private int hash4(int h) {
        int h2 = h * PRIME4;
        return ((h2 >>> this.hashShift) ^ h2) & this.mask;
    }

    public String toString() {
        int i;
        if (this.size == 0) {
            return "{}";
        }
        StringBuilder buffer = new StringBuilder(32);
        buffer.append('{');
        K[] keyTable = this.keyTable;
        V[] valueTable = this.valueTable;
        int i2 = keyTable.length;
        while (true) {
            i = i2 - 1;
            if (i2 > 0) {
                K key = keyTable[i];
                if (key != null) {
                    buffer.append(key);
                    buffer.append('=');
                    buffer.append(valueTable[i]);
                    break;
                }
                i2 = i;
            } else {
                break;
            }
        }
        while (true) {
            int i3 = i - 1;
            if (i > 0) {
                K key2 = keyTable[i3];
                if (key2 != null) {
                    buffer.append(", ");
                    buffer.append(key2);
                    buffer.append('=');
                    buffer.append(valueTable[i3]);
                }
                i = i3;
            } else {
                buffer.append('}');
                return buffer.toString();
            }
        }
    }

    public Entries<K, V> entries() {
        return new Entries<>(this);
    }

    public Values<V> values() {
        return new Values<>(this);
    }

    public Keys<K> keys() {
        return new Keys<>(this);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap$Entry.smali */
    public static class Entry<K, V> {
        public K key;
        public V value;

        public String toString() {
            return this.key + "=" + this.value;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap$MapIterator.smali */
    private static class MapIterator<K, V> {
        int currentIndex;
        public boolean hasNext;
        final CuckooObjectMap<K, V> map;
        int nextIndex;

        public MapIterator(CuckooObjectMap<K, V> map) {
            this.map = map;
            reset();
        }

        public void reset() {
            this.currentIndex = -1;
            this.nextIndex = -1;
            advance();
        }

        void advance() {
            int i;
            this.hasNext = false;
            K[] keyTable = this.map.keyTable;
            int n = this.map.capacity + this.map.stashSize;
            do {
                i = this.nextIndex + 1;
                this.nextIndex = i;
                if (i >= n) {
                    return;
                }
            } while (keyTable[i] == null);
            this.hasNext = true;
        }

        public void remove() {
            int i = this.currentIndex;
            if (i < 0) {
                throw new IllegalStateException("next must be called before remove.");
            }
            if (i >= this.map.capacity) {
                this.map.removeStashIndex(this.currentIndex);
                this.nextIndex = this.currentIndex - 1;
                advance();
            } else {
                this.map.keyTable[this.currentIndex] = null;
                this.map.valueTable[this.currentIndex] = null;
            }
            this.currentIndex = -1;
            CuckooObjectMap<K, V> cuckooObjectMap = this.map;
            cuckooObjectMap.size--;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap$Entries.smali */
    public static class Entries<K, V> extends MapIterator<K, V> implements Iterable<Entry<K, V>>, Iterator<Entry<K, V>> {
        Entry<K, V> entry;

        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator, java.util.Iterator
        public /* bridge */ /* synthetic */ void remove() {
            super.remove();
        }

        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator
        public /* bridge */ /* synthetic */ void reset() {
            super.reset();
        }

        public Entries(CuckooObjectMap<K, V> map) {
            super(map);
            this.entry = new Entry<>();
        }

        @Override // java.util.Iterator
        public Entry<K, V> next() {
            if (!this.hasNext) {
                throw new NoSuchElementException();
            }
            K[] keyTable = this.map.keyTable;
            this.entry.key = keyTable[this.nextIndex];
            this.entry.value = this.map.valueTable[this.nextIndex];
            this.currentIndex = this.nextIndex;
            advance();
            return this.entry;
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.hasNext;
        }

        @Override // java.lang.Iterable
        public Iterator<Entry<K, V>> iterator() {
            return this;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap$Values.smali */
    public static class Values<V> extends MapIterator<Object, V> implements Iterable<V>, Iterator<V> {
        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator, java.util.Iterator
        public /* bridge */ /* synthetic */ void remove() {
            super.remove();
        }

        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator
        public /* bridge */ /* synthetic */ void reset() {
            super.reset();
        }

        public Values(CuckooObjectMap<?, V> map) {
            super(map);
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.hasNext;
        }

        @Override // java.util.Iterator
        public V next() {
            if (!this.hasNext) {
                throw new NoSuchElementException();
            }
            V value = this.map.valueTable[this.nextIndex];
            this.currentIndex = this.nextIndex;
            advance();
            return value;
        }

        @Override // java.lang.Iterable
        public Iterator<V> iterator() {
            return this;
        }

        public ArrayList<V> toArray() {
            ArrayList array = new ArrayList(this.map.size);
            while (this.hasNext) {
                array.add(next());
            }
            return array;
        }

        public void toArray(ArrayList<V> array) {
            while (this.hasNext) {
                array.add(next());
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\CuckooObjectMap$Keys.smali */
    public static class Keys<K> extends MapIterator<K, Object> implements Iterable<K>, Iterator<K> {
        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator, java.util.Iterator
        public /* bridge */ /* synthetic */ void remove() {
            super.remove();
        }

        @Override // com.esotericsoftware.kryo.util.CuckooObjectMap.MapIterator
        public /* bridge */ /* synthetic */ void reset() {
            super.reset();
        }

        public Keys(CuckooObjectMap<K, ?> map) {
            super(map);
        }

        @Override // java.util.Iterator
        public boolean hasNext() {
            return this.hasNext;
        }

        @Override // java.util.Iterator
        public K next() {
            if (!this.hasNext) {
                throw new NoSuchElementException();
            }
            K key = this.map.keyTable[this.nextIndex];
            this.currentIndex = this.nextIndex;
            advance();
            return key;
        }

        @Override // java.lang.Iterable
        public Iterator<K> iterator() {
            return this;
        }

        public ArrayList<K> toArray() {
            ArrayList array = new ArrayList(this.map.size);
            while (this.hasNext) {
                array.add(next());
            }
            return array;
        }
    }

    public static int nextPowerOfTwo(int value) {
        if (value == 0) {
            return 1;
        }
        int value2 = value - 1;
        int value3 = value2 | (value2 >> 1);
        int value4 = value3 | (value3 >> 2);
        int value5 = value4 | (value4 >> 4);
        int value6 = value5 | (value5 >> 8);
        return (value6 | (value6 >> 16)) + 1;
    }
}
