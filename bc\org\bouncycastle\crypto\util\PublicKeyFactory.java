package bc.org.bouncycastle.crypto.util;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.crypto.params.ECDomainParameters;
import bc.org.bouncycastle.crypto.params.ECPublicKeyParameters;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.a4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.a8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.y6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.y7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z3;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory.smali */
public class PublicKeyFactory {
    private static Map a;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$b.smali */
    private static class b extends m {
        private b() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            s2 a = s2.a(p7Var.e().f());
            r rVar = (r) p7Var.g();
            BigInteger f = a.f();
            return new w2(rVar.i(), new t2(a.g(), a.e(), null, f == null ? 0 : f.intValue()));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$c.smali */
    private static class c extends m {
        private c() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            BigInteger e = v2.a(p7Var.g()).e();
            z3 a = z3.a(p7Var.e().f());
            BigInteger g = a.g();
            BigInteger e2 = a.e();
            BigInteger h = a.h();
            BigInteger f = a.f() != null ? a.f() : null;
            v7 i = a.i();
            return new w2(e, new t2(g, e2, h, f, i != null ? new x2(i.getSeed(), i.e().intValue()) : null));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$d.smali */
    private static class d extends m {
        private d() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            l3 l3Var;
            r rVar = (r) p7Var.g();
            com.vasco.digipass.sdk.utils.utilities.obfuscated.h f = p7Var.e().f();
            if (f != null) {
                k3 a = k3.a(f.toASN1Primitive());
                l3Var = new l3(a.f(), a.g(), a.e());
            } else {
                l3Var = null;
            }
            return new n3(rVar.i(), l3Var);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$f.smali */
    private static class f extends m {
        private f() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            ECDomainParameters eCDomainParameters;
            byte b;
            c8 a = c8.a(p7Var.e().f());
            if (a.g()) {
                w wVar = (w) a.e();
                X9ECParameters a2 = u1.a(wVar);
                if (a2 == null) {
                    a2 = c4.a(wVar);
                }
                eCDomainParameters = new d4(wVar, a2);
            } else {
                eCDomainParameters = a.f() ? (ECDomainParameters) obj : new ECDomainParameters(X9ECParameters.getInstance(a.e()));
            }
            byte[] h = p7Var.f().h();
            x f2Var = new f2(h);
            if (h[0] == 4 && h[1] == h.length - 2 && (((b = h[2]) == 2 || b == 3) && new i8().a(eCDomainParameters.getCurve()) >= h.length - 3)) {
                try {
                    f2Var = (x) b0.a(h);
                } catch (IOException e) {
                    throw new IllegalArgumentException("error recovering public key");
                }
            }
            return new ECPublicKeyParameters(new f8(eCDomainParameters.getCurve(), f2Var).e(), eCDomainParameters);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$g.smali */
    private static class g extends m {
        private g() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            return new g4(PublicKeyFactory.b(p7Var, obj));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$h.smali */
    private static class h extends m {
        private h() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            return new i4(PublicKeyFactory.b(p7Var, obj));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$i.smali */
    private static class i extends m {
        private i() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            l4 a = l4.a(p7Var.e().f());
            return new o4(((r) p7Var.g()).i(), new m4(a.f(), a.e()));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$j.smali */
    private static class j extends m {
        private j() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            x4 a = x4.a(p7Var.e().f());
            w g = a.g();
            b4 b4Var = new b4(new d4(g, a4.b(g)), g, a.e(), a.f());
            try {
                byte[] h = ((x) p7Var.g()).h();
                if (h.length != 64) {
                    throw new IllegalArgumentException("invalid length for GOST3410_2001 public key");
                }
                byte[] bArr = new byte[65];
                bArr[0] = 4;
                for (int i = 1; i <= 32; i++) {
                    bArr[i] = h[32 - i];
                    bArr[i + 32] = h[64 - i];
                }
                return new ECPublicKeyParameters(b4Var.getCurve().decodePoint(bArr), b4Var);
            } catch (IOException e) {
                throw new IllegalArgumentException("error recovering GOST3410_2001 public key");
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$k.smali */
    private static class k extends m {
        private k() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            s0 e = p7Var.e();
            w e2 = e.e();
            x4 a = x4.a(e.f());
            w g = a.g();
            b4 b4Var = new b4(new d4(g, a4.b(g)), g, a.e(), a.f());
            try {
                x xVar = (x) p7Var.g();
                int i = e2.b(y6.h) ? 64 : 32;
                int i2 = i * 2;
                byte[] h = xVar.h();
                if (h.length != i2) {
                    throw new IllegalArgumentException("invalid length for GOST3410_2012 public key");
                }
                byte[] bArr = new byte[i2 + 1];
                bArr[0] = 4;
                for (int i3 = 1; i3 <= i; i3++) {
                    bArr[i3] = h[i - i3];
                    bArr[i3 + i] = h[i2 - i3];
                }
                return new ECPublicKeyParameters(b4Var.getCurve().decodePoint(bArr), b4Var);
            } catch (IOException e3) {
                throw new IllegalArgumentException("error recovering GOST3410_2012 public key");
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$l.smali */
    private static class l extends m {
        private l() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            x6 a = x6.a(p7Var.g());
            return new u6(false, a.e(), a.f());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$m.smali */
    private static abstract class m {
        private m() {
        }

        abstract AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$n.smali */
    private static class n extends m {
        private n() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            return new y7(PublicKeyFactory.b(p7Var, obj));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$o.smali */
    private static class o extends m {
        private o() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) {
            return new a8(PublicKeyFactory.b(p7Var, obj));
        }
    }

    static {
        HashMap hashMap = new HashMap();
        a = hashMap;
        hashMap.put(i6.b, new l());
        a.put(i6.k, new l());
        a.put(b8.m, new l());
        a.put(j8.g0, new c());
        a.put(i6.s, new b());
        a.put(j8.Z, new d());
        a.put(e6.j, new d());
        a.put(e6.l, new i());
        a.put(j8.l, new f());
        a.put(o1.m, new j());
        a.put(y6.g, new k());
        a.put(y6.h, new k());
        a.put(s7.c, new e());
        a.put(s7.b, new e());
        a.put(j4.b, new n());
        a.put(j4.c, new o());
        a.put(j4.d, new g());
        a.put(j4.e, new h());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static byte[] b(p7 p7Var, Object obj) {
        return p7Var.f().i();
    }

    public static AsymmetricKeyParameter createKey(byte[] bArr) throws IOException {
        if (bArr == null) {
            throw new IllegalArgumentException("keyInfoData array null");
        }
        if (bArr.length != 0) {
            return createKey(p7.a(b0.a(bArr)));
        }
        throw new IllegalArgumentException("keyInfoData array empty");
    }

    public static AsymmetricKeyParameter createKey(InputStream inputStream) throws IOException {
        return createKey(p7.a(new q(inputStream).c()));
    }

    public static AsymmetricKeyParameter createKey(p7 p7Var) throws IOException {
        if (p7Var != null) {
            return createKey(p7Var, null);
        }
        throw new IllegalArgumentException("keyInfo argument null");
    }

    public static AsymmetricKeyParameter createKey(p7 p7Var, Object obj) throws IOException {
        if (p7Var != null) {
            s0 e2 = p7Var.e();
            m mVar = (m) a.get(e2.e());
            if (mVar != null) {
                return mVar.a(p7Var, obj);
            }
            throw new IOException("algorithm identifier in public key not recognised: " + e2.e());
        }
        throw new IllegalArgumentException("keyInfo argument null");
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\util\PublicKeyFactory$e.smali */
    private static class e extends m {
        private e() {
            super();
        }

        @Override // bc.org.bouncycastle.crypto.util.PublicKeyFactory.m
        AsymmetricKeyParameter a(p7 p7Var, Object obj) throws IOException {
            ECDomainParameters eCDomainParameters;
            s0 e = p7Var.e();
            w e2 = e.e();
            s3 a = s3.a(e.f());
            try {
                byte[] clone = Arrays.clone(((x) p7Var.g()).h());
                w wVar = s7.b;
                if (e2.b(wVar)) {
                    a(clone);
                }
                if (a.g()) {
                    eCDomainParameters = r3.a(a.f());
                } else {
                    q3 e3 = a.e();
                    byte[] f = e3.f();
                    if (e2.b(wVar)) {
                        a(f);
                    }
                    BigInteger bigInteger = new BigInteger(1, f);
                    p3 g = e3.g();
                    ECCurve.F2m f2m = new ECCurve.F2m(g.h(), g.e(), g.f(), g.g(), e3.e(), bigInteger, (BigInteger) null, (BigInteger) null);
                    byte[] h = e3.h();
                    if (e2.b(wVar)) {
                        a(h);
                    }
                    eCDomainParameters = new ECDomainParameters(f2m, t3.a(f2m, h), e3.getN());
                }
                return new ECPublicKeyParameters(t3.a(eCDomainParameters.getCurve(), clone), eCDomainParameters);
            } catch (IOException e4) {
                throw new IllegalArgumentException("error recovering DSTU public key");
            }
        }

        private void a(byte[] bArr) {
            for (int i = 0; i < bArr.length / 2; i++) {
                byte b = bArr[i];
                bArr[i] = bArr[(bArr.length - 1) - i];
                bArr[(bArr.length - 1) - i] = b;
            }
        }
    }
}
