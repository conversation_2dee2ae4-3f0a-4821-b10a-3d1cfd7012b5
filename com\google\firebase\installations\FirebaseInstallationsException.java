package com.google.firebase.installations;

import com.google.firebase.FirebaseException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\installations\FirebaseInstallationsException.smali */
public class FirebaseInstallationsException extends FirebaseException {
    private final Status status;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\installations\FirebaseInstallationsException$Status.smali */
    public enum Status {
        BAD_CONFIG,
        UNAVAILABLE,
        TOO_MANY_REQUESTS
    }

    public FirebaseInstallationsException(Status status) {
        this.status = status;
    }

    public FirebaseInstallationsException(String message, Status status) {
        super(message);
        this.status = status;
    }

    public FirebaseInstallationsException(String message, Status status, Throwable cause) {
        super(message, cause);
        this.status = status;
    }

    public Status getStatus() {
        return this.status;
    }
}
