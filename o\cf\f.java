package o.cf;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\f.smali */
public final class f {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final f a;
    private static final /* synthetic */ f[] c;
    public static final f d;
    public static final f e;
    private static long g;
    private static int i;
    private static int j;
    private final String b;

    static void d() {
        g = 6516079468415605395L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r8 = r8 * 3
            int r8 = 3 - r8
            int r6 = r6 * 3
            int r6 = 71 - r6
            byte[] r0 = o.cf.f.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L39
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r6
            int r8 = r8 + 1
            r1[r3] = r4
            if (r3 != r7) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L39:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.f.h(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{71, 41, -111, Base64.padSymbol};
        $$b = 246;
    }

    private static /* synthetic */ f[] e() {
        int i2 = i;
        int i3 = i2 + 47;
        j = i3 % 128;
        int i4 = i3 % 2;
        f[] fVarArr = {e, a, d};
        int i5 = i2 + Opcodes.LREM;
        j = i5 % 128;
        switch (i5 % 2 == 0 ? '1' : 'C') {
            case 'C':
                return fVarArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static f valueOf(String str) {
        int i2 = j + 97;
        i = i2 % 128;
        int i3 = i2 % 2;
        f fVar = (f) Enum.valueOf(f.class, str);
        int i4 = i + 35;
        j = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public static f[] values() {
        int i2 = i + Opcodes.DREM;
        j = i2 % 128;
        int i3 = i2 % 2;
        f[] fVarArr = (f[]) c.clone();
        int i4 = j + 11;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return fVarArr;
            default:
                int i5 = 97 / 0;
                return fVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        j = 1;
        d();
        Object[] objArr = new Object[1];
        f("⢴\ueede⣵瓱낁숝葭ಐ䇲\uedc7", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("膖떑臗⾞ꃹꖲ鐵欟\ue8f0뚨", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, objArr2);
        e = new f(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f("튧陏틫౬櫳\uf4c7师㩈믲镗", ViewConfiguration.getDoubleTapTimeout() >> 16, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        f("偲晣倾ﱠ哳ഺ怨쎕㤇敛", TextUtils.getTrimmedLength(""), objArr4);
        a = new f(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        f("밨멣뱬⁊䱚뜻碮禺핬륺퇶", 1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        f("㊄ꟸ㋀㷱㸳攽૧ꮜ寠꓁ꎿ", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr6);
        d = new f(intern3, 2, ((String) objArr6[0]).intern());
        c = e();
        int i2 = i + 35;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    private f(String str, int i2, String str2) {
        this.b = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = i;
        int i3 = i2 + Opcodes.LNEG;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                String str = this.b;
                int i4 = i2 + 57;
                j = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return str;
                    default:
                        int i5 = 40 / 0;
                        return str;
                }
        }
    }

    public static f d(String str) {
        int i2 = j + 93;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.quote : 'Z') {
            case '\"':
                throw null;
            default:
                if (str == null) {
                    return null;
                }
                f[] values = values();
                int length = values.length;
                int i3 = 0;
                while (i3 < length) {
                    int i4 = j + 11;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                    f fVar = values[i3];
                    if (fVar.b.equals(str)) {
                        int i6 = j + 91;
                        i = i6 % 128;
                        int i7 = i6 % 2;
                        return fVar;
                    }
                    i3++;
                    int i8 = j + 29;
                    i = i8 % 128;
                    int i9 = i8 % 2;
                }
                int i10 = i + 87;
                j = i10 % 128;
                int i11 = i10 % 2;
                return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0018. Please report as an issue. */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v1 */
    /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
    private static void f(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 350
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.f.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
