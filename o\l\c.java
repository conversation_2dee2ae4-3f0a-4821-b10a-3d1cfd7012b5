package o.l;

import android.view.View;
import androidx.fragment.app.Fragment;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\l\c.smali */
public final class c extends Fragment {
    private final d a;
    private View b;
    private final a e;
    private static int d = 0;
    private static int c = 1;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\l\c$d.smali */
    protected interface d {
        void b();

        void c();
    }

    public c(a aVar, d dVar) {
        this.e = aVar;
        this.a = dVar;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(View view) {
        int i = d;
        int i2 = (i & 73) + (i | 73);
        c = i2 % 128;
        int i3 = i2 % 2;
        this.a.c();
        int i4 = (d + 4) - 1;
        c = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void c(View view) {
        int i = d;
        int i2 = (i & 99) + (i | 99);
        c = i2 % 128;
        int i3 = i2 % 2;
        this.a.b();
        int i4 = (d + 48) - 1;
        c = i4 % 128;
        switch (i4 % 2 == 0 ? '=' : (char) 28) {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:12:0x0059. Please report as an issue. */
    /* JADX WARN: Failed to find 'out' block for switch in B:19:0x00c8. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0047 A[PHI: r3
  0x0047: PHI (r3v14 o.o.c) = (r3v3 o.o.c), (r3v23 o.o.c) binds: [B:27:0x003d, B:8:0x0044] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // androidx.fragment.app.Fragment
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final android.view.View onCreateView(android.view.LayoutInflater r3, android.view.ViewGroup r4, android.os.Bundle r5) {
        /*
            Method dump skipped, instructions count: 256
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.l.c.onCreateView(android.view.LayoutInflater, android.view.ViewGroup, android.os.Bundle):android.view.View");
    }
}
