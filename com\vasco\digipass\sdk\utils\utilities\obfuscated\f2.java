package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f2.smali */
public class f2 extends x {
    public f2(byte[] bArr) {
        super(bArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.x, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.x, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 4, this.b);
    }

    static void a(z zVar, boolean z, byte[] bArr, int i, int i2) throws IOException {
        zVar.a(z, 4, bArr, i, i2);
    }

    static int a(boolean z, int i) {
        return z.a(z, i);
    }
}
