package o.v;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.an.h;
import o.eo.f;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\f.smali */
public final class f extends c<h.d, h.c, o.an.h<o.ep.b>, o.ep.b> {
    public static final byte[] $$j = null;
    public static final int $$k = 0;
    private static int $10;
    private static int $11;
    private static int l;
    private static int m;
    private static short[] p;
    private static int q;
    private static int r;
    private static int s;
    private static byte[] t;
    private final boolean k;

    /* renamed from: o, reason: collision with root package name */
    private Activity f103o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        q = 0;
        s = 1;
        u();
        ViewConfiguration.getMinimumFlingVelocity();
        Color.alpha(0);
        TextUtils.getOffsetAfter("", 0);
        Process.getThreadPriority(0);
        ViewConfiguration.getScrollFriction();
        int i = s + 3;
        q = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void F(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 4 - r8
            byte[] r0 = o.v.f.$$j
            int r7 = r7 * 2
            int r7 = 110 - r7
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r7 = r6
            r3 = r8
            r4 = r2
            goto L2e
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r5
        L2e:
            int r8 = r8 + 1
            int r3 = -r3
            int r6 = r6 + r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.f.F(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$j = new byte[]{106, 33, -117, 89};
        $$k = 75;
    }

    static void u() {
        t = new byte[]{-49, -63, -51, 59, 50, -19, 24, -41, -34, 38, 54, 54, -51, -55, -61, -63, 43, -44, 35, 58, 49, -22, 35, Base64.padSymbol, -34, -47, 24, -60, 34, -60, -51, 49, -22, Tnaf.POW_2_WIDTH, 60, 50, -35, 49, -35, 46, -62, 56, -49, -22, -42, -33, -2, 119, 55, 55, -52, -56, -62, -64, 68, PSSSigner.TRAILER_IMPLICIT, -75, 66, 75, 84, -100, 94, 87, -81, -65, -65, 68, 64, 74, 72, -53, 55, 121, -102, -107, -88, 63, ByteCompanionObject.MIN_VALUE, 102, ByteCompanionObject.MIN_VALUE, -119, 117, -82, -81, 48, 118, -97, 12, 5, -3, -19, -19, 22, 18, 24, 26, 39, -91, -17, 64, -84, -31, -22, 17, 68, -72, 22, 23, -21, 20, -17, 22, 71, -72, 17, 21, 90, -71, 26, -30, 31, 80, -44, 84, -96, -87, 85, -82, -17, 7, -86, -71, 94, -25, 7, 84, -83, -84, 95, -96, 89, -4, 23, 89, -70, 85, -24, 20, -22, 12, -77, 90, -5, 18, 82, 82, -87, -83, -89, 69, -8, 26, 80, -1, 19, 94, 85, -82, -5, 7, -82, -86, -85, -90, -75, 66, 75, -77, -93, -93, 88, 92, 86, 84, -66, 65, -74, -81, -92, ByteCompanionObject.MAX_VALUE, -74, -88, 75, 68, -124, -87, -89, 72, -92, 72, -112, -112, -112, -112, -112, -112, -112};
        l = 909053617;
        r = -735680187;
        m = 1157056649;
    }

    @Override // o.v.c
    final /* synthetic */ o.ep.b b(Context context) {
        int i = s + 69;
        q = i % 128;
        int i2 = i % 2;
        o.ep.b d = d(context);
        int i3 = s + Opcodes.LSUB;
        q = i3 % 128;
        int i4 = i3 % 2;
        return d;
    }

    @Override // o.v.c
    final /* synthetic */ o.an.h<o.ep.b> e(o.ep.b bVar) {
        int i = q + 35;
        s = i % 128;
        o.ep.b bVar2 = bVar;
        switch (i % 2 == 0) {
            case true:
                d(bVar2);
                throw null;
            default:
                return d(bVar2);
        }
    }

    public f(String str, o.eo.e eVar, boolean z, boolean z2) {
        super(str, eVar, z);
        this.k = z2;
    }

    @Override // o.v.c
    final String a() {
        int i = q + 11;
        s = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        w((byte) (95 - ((Process.getThreadPriority(0) + 20) >> 6)), (-1926776345) + (ViewConfiguration.getMaximumFlingVelocity() >> 16), (short) Drawable.resolveOpacity(0, 0), 12 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 502700148 - KeyEvent.getDeadChar(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = s + 91;
        q = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.v.c
    final String t() {
        Object obj;
        int i = q + 69;
        s = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                w((byte) (94 - TextUtils.getOffsetAfter("", 0)), MotionEvent.axisFromString("") - 1926776300, (short) View.MeasureSpec.makeMeasureSpec(0, 0), (-22) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 502700158 - View.resolveSizeAndState(0, 0, 0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                w((byte) (17 - TextUtils.getOffsetAfter("", 0)), (-1926776300) >> MotionEvent.axisFromString(""), (short) View.MeasureSpec.makeMeasureSpec(0, 1), Opcodes.FDIV >> (AudioTrack.getMinVolume() > 1.0f ? 1 : (AudioTrack.getMinVolume() == 1.0f ? 0 : -1)), 502700158 / View.resolveSizeAndState(1, 1, 1), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = q + Opcodes.LNEG;
        s = i2 % 128;
        switch (i2 % 2 == 0 ? '8' : '0') {
            case '8':
                int i3 = 20 / 0;
                return intern;
            default:
                return intern;
        }
    }

    @Override // o.v.c
    final AntelopErrorCode s() {
        int i = s + 45;
        q = i % 128;
        int i2 = i % 2;
        AntelopErrorCode antelopErrorCode = AntelopErrorCode.SamsungPayWalletNotAvailable;
        int i3 = s + 9;
        q = i3 % 128;
        int i4 = i3 % 2;
        return antelopErrorCode;
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = q + 53;
        s = i % 128;
        int i2 = i % 2;
        if (!((c) this).h) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            w((byte) (6 - AndroidCharacter.getMirror('0')), (-1926776291) - Gravity.getAbsoluteGravity(0, 0), (short) Color.green(0), (-15) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), Color.red(0) + 502700158, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            w((byte) (26 - MotionEvent.axisFromString("")), (-1926776275) - KeyEvent.keyCodeFromString(""), (short) ExpandableListView.getPackedPositionType(0L), (-16) - TextUtils.lastIndexOf("", '0'), 502700158 - TextUtils.lastIndexOf("", '0', 0), objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(((d) this).n.e());
            Object[] objArr3 = new Object[1];
            w((byte) (Color.alpha(0) - 124), ImageFormat.getBitsPerPixel(0) - 1926776257, (short) (ViewConfiguration.getFadingEdgeLength() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + 4, 502700171 - Color.argb(0, 0, 0, 0), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
        }
        switch (((d) this).n.s() != null ? (char) 25 : '@') {
            case 25:
                int i3 = s + 71;
                q = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        if (((d) this).n.s().a() == null) {
                            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                            Object[] objArr4 = new Object[1];
                            w((byte) (TextUtils.getOffsetAfter("", 0) - 42), ExpandableListView.getPackedPositionChild(0L) - 1926776290, (short) View.getDefaultSize(0, 0), (-16) - Gravity.getAbsoluteGravity(0, 0), View.resolveSizeAndState(0, 0, 0) + 502700158, objArr4);
                            String intern2 = ((String) objArr4[0]).intern();
                            Object[] objArr5 = new Object[1];
                            w((byte) (60 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (-1926776222) - TextUtils.getOffsetBefore("", 0), (short) TextUtils.getTrimmedLength(""), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 20, 502700142 - Color.argb(0, 0, 0, 0), objArr5);
                            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
                        }
                        break;
                    default:
                        ((d) this).n.s().a();
                        throw null;
                }
        }
        int i4 = s + 55;
        q = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.v.c
    final Activity y() {
        int i = s + 11;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        Activity activity = this.f103o;
        int i4 = i2 + Opcodes.DREM;
        s = i4 % 128;
        switch (i4 % 2 == 0 ? 'Q' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                return activity;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.p.h
    public final String d() {
        int i = s + 27;
        q = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        w((byte) ((-54) - KeyEvent.getDeadChar(0, 0)), (-1926776170) - KeyEvent.normalizeMetaState(0), (short) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-7) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 502700158 - ExpandableListView.getPackedPositionType(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = s + 61;
        q = i3 % 128;
        switch (i3 % 2 != 0 ? '1' : (char) 25) {
            case 25:
                return intern;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static o.an.h<o.ep.b> d(o.ep.b bVar) {
        o.an.h<o.ep.b> hVar = new o.an.h<>(bVar);
        int i = s + 17;
        q = i % 128;
        switch (i % 2 != 0 ? 'Q' : Typography.quote) {
            case Opcodes.FASTORE /* 81 */:
                throw null;
            default:
                return hVar;
        }
    }

    @Override // o.v.c
    final f.a p() {
        int i = s + 39;
        q = i % 128;
        int i2 = i % 2;
        f.a aVar = f.a.a;
        int i3 = s + 65;
        q = i3 % 128;
        int i4 = i3 % 2;
        return aVar;
    }

    private o.ep.b d(Context context) {
        o.ep.b p2;
        int i = s + Opcodes.DSUB;
        q = i % 128;
        switch (i % 2 != 0) {
            case true:
                throw null;
            default:
                switch (this.k) {
                    case true:
                        o.ee.c.a();
                        p2 = o.ee.c.s(context);
                        break;
                    default:
                        o.ee.c.a();
                        p2 = o.ee.c.p(context);
                        break;
                }
                int i2 = q + 65;
                s = i2 % 128;
                int i3 = i2 % 2;
                return p2;
        }
    }

    @Override // o.v.c
    final o.ee.i v() {
        int i = s + Opcodes.LSHL;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        int i4 = i2 + 21;
        s = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    public final void e(Activity activity, o.p.g gVar) throws WalletValidationException {
        int i = q + 57;
        s = i % 128;
        switch (i % 2 != 0) {
            case true:
                this.f103o = activity;
                d(activity, gVar);
                int i2 = q + 109;
                s = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                this.f103o = activity;
                d(activity, gVar);
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:101:0x009a, code lost:
    
        r4 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 842
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.f.w(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
