package o.ds;

import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.settings.TransactionStartCondition;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\f.smali */
public final class f {
    public static final f a;
    private static final /* synthetic */ f[] b;
    public static final f c;
    public static final f e;
    private static char f;
    private static char g;
    private static int h;
    private static char i;
    private static char j;
    private final String d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int l = 1;

    static void d() {
        g = (char) 38220;
        j = (char) 12924;
        i = (char) 41555;
        f = (char) 53410;
    }

    private static /* synthetic */ f[] a() {
        int i2 = h;
        int i3 = i2 + 89;
        l = i3 % 128;
        int i4 = i3 % 2;
        f[] fVarArr = {c, e, a};
        int i5 = i2 + Opcodes.LUSHR;
        l = i5 % 128;
        int i6 = i5 % 2;
        return fVarArr;
    }

    public static f valueOf(String str) {
        int i2 = h + Opcodes.LSUB;
        l = i2 % 128;
        boolean z = i2 % 2 == 0;
        f fVar = (f) Enum.valueOf(f.class, str);
        switch (z) {
            case false:
                int i3 = l + 45;
                h = i3 % 128;
                int i4 = i3 % 2;
                return fVar;
            default:
                throw null;
        }
    }

    public static f[] values() {
        int i2 = l + 11;
        h = i2 % 128;
        int i3 = i2 % 2;
        f[] fVarArr = (f[]) b.clone();
        int i4 = h + 59;
        l = i4 % 128;
        int i5 = i4 % 2;
        return fVarArr;
    }

    static {
        h = 0;
        d();
        Object[] objArr = new Object[1];
        k("衄\uf30a", AndroidCharacter.getMirror('0') - '.', objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("詑秣", ExpandableListView.getPackedPositionType(0L) + 2, objArr2);
        c = new f(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k("\ud9e9榴眗䊴뜾싛\uddd7쳙", 8 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        k("畨澿穸숋佛ꅔ悀곀感鉒", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 8, objArr4);
        e = new f(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        k("\u0e7a컾曷칤绐蝧蟽镻뙏䒥悆Ἆ\ue9ed遹", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 13, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        k("僷턶⻖곝楜꽹䨱푸邃霋㳟빔橦缾", 15 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr6);
        a = new f(intern3, 2, ((String) objArr6[0]).intern());
        b = a();
        int i2 = l + 21;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '\r' : (char) 16) {
            case '\r':
                return;
            default:
                throw null;
        }
    }

    private f(String str, int i2, String str2) {
        this.d = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = h;
        int i3 = i2 + 29;
        l = i3 % 128;
        int i4 = i3 % 2;
        String str = this.d;
        int i5 = i2 + 63;
        l = i5 % 128;
        switch (i5 % 2 == 0 ? '\r' : (char) 6) {
            case 6:
                return str;
            default:
                int i6 = 35 / 0;
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.ds.f a(java.lang.String r7) {
        /*
            o.ds.f[] r0 = values()
            int r1 = r0.length
            int r2 = o.ds.f.l
            int r2 = r2 + 121
            int r3 = r2 % 128
            o.ds.f.h = r3
            int r2 = r2 % 2
            r2 = 0
            r3 = r2
        L12:
            r4 = 1
            if (r3 >= r1) goto L17
            r5 = r4
            goto L18
        L17:
            r5 = r2
        L18:
            switch(r5) {
                case 0: goto L26;
                default: goto L1b;
            }
        L1b:
            r5 = r0[r3]
            java.lang.String r6 = r5.d
            boolean r6 = r6.equals(r7)
            if (r6 == 0) goto L56
            goto L54
        L26:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            int r3 = android.view.ViewConfiguration.getFadingEdgeLength()
            int r3 = r3 >> 16
            int r3 = 25 - r3
            java.lang.Object[] r4 = new java.lang.Object[r4]
            java.lang.String r5 = "\uf01d\u16fe⒘丄齀ㄯ뿁㾎㳟⦎僩鶩딷罷켙秹勉ꓜ秫䛠덝㯚༻滑部Ɱ"
            k(r5, r3, r4)
            r2 = r4[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r7 = r1.append(r7)
            java.lang.String r7 = r7.toString()
            r0.<init>(r7)
            throw r0
        L54:
            r4 = r2
            goto L57
        L56:
        L57:
            switch(r4) {
                case 0: goto L5d;
                default: goto L5a;
            }
        L5a:
            int r3 = r3 + 1
            goto L69
        L5d:
            int r7 = o.ds.f.h
            int r7 = r7 + 27
            int r0 = r7 % 128
            o.ds.f.l = r0
            int r7 = r7 % 2
            return r5
        L69:
            int r4 = o.ds.f.h
            int r4 = r4 + 17
            int r5 = r4 % 128
            o.ds.f.l = r5
            int r4 = r4 % 2
            goto L12
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ds.f.a(java.lang.String):o.ds.f");
    }

    /* renamed from: o.ds.f$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\f$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] a;
        private static int d = 1;
        private static int e;

        static {
            e = 0;
            int[] iArr = new int[f.values().length];
            a = iArr;
            try {
                iArr[f.c.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[f.e.ordinal()] = 2;
                int i = (d + 36) - 1;
                e = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[f.a.ordinal()] = 3;
                int i2 = d;
                int i3 = (i2 ^ 11) + ((i2 & 11) << 1);
                e = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    public static TransactionStartCondition c(f fVar) {
        int i2 = l + 69;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        switch (fVar != null) {
            case false:
                int i5 = i3 + 3;
                l = i5 % 128;
                int i6 = i5 % 2;
                return null;
            default:
                switch (AnonymousClass1.a[fVar.ordinal()]) {
                    case 1:
                        return TransactionStartCondition.No;
                    case 2:
                        return TransactionStartCondition.ScreenOn;
                    case 3:
                        TransactionStartCondition transactionStartCondition = TransactionStartCondition.PhoneUnlocked;
                        int i7 = l + 61;
                        h = i7 % 128;
                        switch (i7 % 2 == 0) {
                            case false:
                                throw null;
                            default:
                                return transactionStartCondition;
                        }
                    default:
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr = new Object[1];
                        k("밈垈权\ue629딷罷\ue1b6\ued9bꗄꢎۋ籊폢ﰽ咩⪉煡豪", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 18, objArr);
                        throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(fVar.name()).toString());
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 570
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ds.f.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
