package com.esotericsoftware.reflectasm;

import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.security.ProtectionDomain;
import java.util.HashSet;
import java.util.WeakHashMap;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\reflectasm\AccessClassLoader.smali */
class AccessClassLoader extends ClassLoader {
    private static final WeakHashMap<ClassLoader, WeakReference<AccessClassLoader>> accessClassLoaders = new WeakHashMap<>();
    private static volatile Method defineClassMethod;
    private static volatile AccessClassLoader selfContextAccessClassLoader;
    private static final ClassLoader selfContextParentClassLoader;
    private final HashSet<String> localClassNames;

    static {
        ClassLoader parentClassLoader = getParentClassLoader(AccessClassLoader.class);
        selfContextParentClassLoader = parentClassLoader;
        selfContextAccessClassLoader = new AccessClassLoader(parentClassLoader);
    }

    private AccessClassLoader(ClassLoader parent) {
        super(parent);
        this.localClassNames = new HashSet<>();
    }

    Class loadAccessClass(String name) {
        if (this.localClassNames.contains(name)) {
            try {
                return loadClass(name, false);
            } catch (ClassNotFoundException ex) {
                throw new RuntimeException(ex);
            }
        }
        return null;
    }

    Class defineAccessClass(String name, byte[] bytes) throws ClassFormatError {
        this.localClassNames.add(name);
        return defineClass(name, bytes);
    }

    @Override // java.lang.ClassLoader
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        return name.equals(FieldAccess.class.getName()) ? FieldAccess.class : name.equals(MethodAccess.class.getName()) ? MethodAccess.class : name.equals(ConstructorAccess.class.getName()) ? ConstructorAccess.class : name.equals(PublicConstructorAccess.class.getName()) ? PublicConstructorAccess.class : super.loadClass(name, resolve);
    }

    Class<?> defineClass(String name, byte[] bytes) throws ClassFormatError {
        try {
            return (Class) getDefineClassMethod().invoke(getParent(), name, bytes, 0, Integer.valueOf(bytes.length), getClass().getProtectionDomain());
        } catch (Exception e) {
            return defineClass(name, bytes, 0, bytes.length, getClass().getProtectionDomain());
        }
    }

    static boolean areInSameRuntimeClassLoader(Class type1, Class type2) {
        if (type1.getPackage() != type2.getPackage()) {
            return false;
        }
        ClassLoader loader1 = type1.getClassLoader();
        ClassLoader loader2 = type2.getClassLoader();
        ClassLoader systemClassLoader = ClassLoader.getSystemClassLoader();
        return loader1 == null ? loader2 == null || loader2 == systemClassLoader : loader2 == null ? loader1 == systemClassLoader : loader1 == loader2;
    }

    private static ClassLoader getParentClassLoader(Class type) {
        ClassLoader parent = type.getClassLoader();
        return parent == null ? ClassLoader.getSystemClassLoader() : parent;
    }

    private static Method getDefineClassMethod() throws Exception {
        if (defineClassMethod == null) {
            synchronized (accessClassLoaders) {
                if (defineClassMethod == null) {
                    defineClassMethod = ClassLoader.class.getDeclaredMethod("defineClass", String.class, byte[].class, Integer.TYPE, Integer.TYPE, ProtectionDomain.class);
                    try {
                        defineClassMethod.setAccessible(true);
                    } catch (Exception e) {
                    }
                }
            }
        }
        return defineClassMethod;
    }

    static AccessClassLoader get(Class type) {
        ClassLoader parent = getParentClassLoader(type);
        ClassLoader classLoader = selfContextParentClassLoader;
        if (classLoader.equals(parent)) {
            if (selfContextAccessClassLoader == null) {
                synchronized (accessClassLoaders) {
                    if (selfContextAccessClassLoader == null) {
                        selfContextAccessClassLoader = new AccessClassLoader(classLoader);
                    }
                }
            }
            return selfContextAccessClassLoader;
        }
        WeakHashMap<ClassLoader, WeakReference<AccessClassLoader>> weakHashMap = accessClassLoaders;
        synchronized (weakHashMap) {
            WeakReference<AccessClassLoader> ref = weakHashMap.get(parent);
            if (ref != null) {
                AccessClassLoader accessClassLoader = ref.get();
                if (accessClassLoader != null) {
                    return accessClassLoader;
                }
                weakHashMap.remove(parent);
            }
            AccessClassLoader accessClassLoader2 = new AccessClassLoader(parent);
            weakHashMap.put(parent, new WeakReference<>(accessClassLoader2));
            return accessClassLoader2;
        }
    }

    public static void remove(ClassLoader parent) {
        if (selfContextParentClassLoader.equals(parent)) {
            selfContextAccessClassLoader = null;
            return;
        }
        WeakHashMap<ClassLoader, WeakReference<AccessClassLoader>> weakHashMap = accessClassLoaders;
        synchronized (weakHashMap) {
            weakHashMap.remove(parent);
        }
    }

    public static int activeAccessClassLoaders() {
        int sz = accessClassLoaders.size();
        return selfContextAccessClassLoader != null ? sz + 1 : sz;
    }
}
