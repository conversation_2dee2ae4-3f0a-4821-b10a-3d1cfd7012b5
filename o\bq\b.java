package o.bq;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import kotlin.io.encoding.Base64;
import o.eg.d;
import org.bouncycastle.math.Primes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bq\b.smali */
final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int h;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        i = 1;
        a = 874635284;
        e = (char) 3558;
        d = (char) 24714;
        b = (char) 10515;
        c = (char) 9162;
    }

    static void init$0() {
        $$a = new byte[]{71, 41, -111, Base64.padSymbol};
        $$b = 86;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r0 = o.bq.b.$$a
            int r8 = r8 * 3
            int r8 = 3 - r8
            int r6 = r6 * 2
            int r6 = r6 + 107
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L39
        L1b:
            r3 = r2
        L1c:
            r5 = r8
            r8 = r6
            r6 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L39:
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.b.j(byte, byte, byte, java.lang.Object[]):void");
    }

    b() {
    }

    static o.eg.b b(o.dr.a aVar) throws d {
        o.eg.b bVar = new o.eg.b();
        switch (aVar.p() != null) {
            case true:
                Object[] objArr = new Object[1];
                f(7 - Color.argb(0, 0, 0, 0), "\ufff6\u0001\ufff8\u0005\u0005\b\ufff6\f", (Process.myPid() >> 22) + 8, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 216, true, objArr);
                bVar.d(((String) objArr[0]).intern(), aVar.p().d());
                int i2 = i + 85;
                h = i2 % 128;
                int i3 = i2 % 2;
                break;
        }
        Object[] objArr2 = new Object[1];
        g("茌ᙬ톣毟籇䕨", 6 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr2);
        bVar.d(((String) objArr2[0]).intern(), aVar.d());
        Object[] objArr3 = new Object[1];
        g("สᦰ矙䊮", (ViewConfiguration.getScrollBarSize() >> 8) + 4, objArr3);
        bVar.d(((String) objArr3[0]).intern(), aVar.q().getTime());
        Object[] objArr4 = new Object[1];
        f(3 - (ViewConfiguration.getJumpTapTimeout() >> 16), "\r￼\ufffe\uffff\u0004\ufffa\uffff", 7 - (ViewConfiguration.getScrollBarSize() >> 8), 208 - (ViewConfiguration.getTouchSlop() >> 8), true, objArr4);
        bVar.d(((String) objArr4[0]).intern(), aVar.m());
        Object[] objArr5 = new Object[1];
        g("\uea10惾㯤遫㸣、鷳竮誆쁁", Drawable.resolveOpacity(0, 0) + 10, objArr5);
        bVar.d(((String) objArr5[0]).intern(), aVar.h());
        Object[] objArr6 = new Object[1];
        f(TextUtils.getOffsetBefore("", 0) + 11, "\u0000\uffff\u0007\ufff8\t\ufffe\ufff6\ufffb\t\ufff8\ufffa￼\ufffa\t\f\u0006\n￼\t\ufff6\ufffa", 21 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 212, true, objArr6);
        bVar.d(((String) objArr6[0]).intern(), aVar.r());
        Object[] objArr7 = new Object[1];
        g("䆖ㅋ톣毟鵟渏\u2fe9\uf6d4Ɀ\ud8ef鏖䈚", 11 - ((Process.getThreadPriority(0) + 20) >> 6), objArr7);
        bVar.d(((String) objArr7[0]).intern(), aVar.t());
        Object[] objArr8 = new Object[1];
        g("䆖ㅋ톣毟鵟渏Ɗ秳", Color.argb(0, 0, 0, 0) + 8, objArr8);
        bVar.d(((String) objArr8[0]).intern(), aVar.s());
        Object[] objArr9 = new Object[1];
        g("\uea2fᶂ轂ﾛ긙傞팒᧓噷돡髓贤\u243a'\ue5e7\ue609Ɗ秳", 17 - TextUtils.lastIndexOf("", '0'), objArr9);
        bVar.d(((String) objArr9[0]).intern(), aVar.u());
        Object[] objArr10 = new Object[1];
        f(KeyEvent.getDeadChar(0, 0) + 7, "\u0006\f\ufff7\u0006\ufff9\u0005�\u0005�\n\ufffb\u0000\ufff9", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 12, 211 - View.resolveSizeAndState(0, 0, 0), false, objArr10);
        bVar.d(((String) objArr10[0]).intern(), aVar.a());
        Object[] objArr11 = new Object[1];
        f(Drawable.resolveOpacity(0, 0) + 7, "\t\f\u0013\uffdd\t\ufffe\uffff\u0007\uffff\f�\u0002\ufffb\b\u000e\uffdd\ufffb\u000e\uffff\u0001", (ViewConfiguration.getTapTimeout() >> 16) + 20, 209 - (KeyEvent.getMaxKeyCode() >> 16), false, objArr11);
        bVar.d(((String) objArr11[0]).intern(), aVar.y());
        Object[] objArr12 = new Object[1];
        g("\uea10惾㯤遫煜ￖၲ䍜", 8 - ((Process.getThreadPriority(0) + 20) >> 6), objArr12);
        bVar.d(((String) objArr12[0]).intern(), aVar.j());
        Object[] objArr13 = new Object[1];
        f(15 - TextUtils.getCapsMode("", 0, 0), "\u000b\u0000\ufffe\u0000\ufffb\ufff6\u000b\n\ufff8\u0003\ufff6\ufffb\t\ufff8\ufffa\n", ExpandableListView.getPackedPositionGroup(0L) + 16, TextUtils.indexOf((CharSequence) "", '0', 0) + 213, true, objArr13);
        bVar.d(((String) objArr13[0]).intern(), aVar.o());
        Object[] objArr14 = new Object[1];
        f(4 - Color.blue(0), "\t\n\ufff9\ufffa\u0001\ufff6\t\ufffe", Color.red(0) + 8, 214 - Color.blue(0), false, objArr14);
        bVar.c(((String) objArr14[0]).intern(), aVar.g());
        Object[] objArr15 = new Object[1];
        g("轏䏽羴솃교藗\udef2闡ⓖ\uf09b", 9 - View.resolveSize(0, 0), objArr15);
        bVar.c(((String) objArr15[0]).intern(), aVar.i());
        Object[] objArr16 = new Object[1];
        f(4 - Color.argb(0, 0, 0, 0), "\ufffb\u0006\r\ufffe￼￡\u0004\ufff9\u0006\u0007\u0001\f", 12 - View.MeasureSpec.getMode(0), 211 - Color.alpha(0), true, objArr16);
        bVar.d(((String) objArr16[0]).intern(), aVar.x());
        int z = aVar.z();
        switch (z != -1) {
            case false:
                break;
            default:
                int i4 = i + 71;
                h = i4 % 128;
                int i5 = i4 % 2;
                Object[] objArr17 = new Object[1];
                g("\ue6d7\u20f8黍ᡭ廅嚩", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 6, objArr17);
                bVar.d(((String) objArr17[0]).intern(), z);
                break;
        }
        Object[] objArr18 = new Object[1];
        g("\uea10惾矙䊮잆联脑꾅", TextUtils.indexOf("", "", 0) + 8, objArr18);
        bVar.d(((String) objArr18[0]).intern(), 0);
        Object[] objArr19 = new Object[1];
        g("髓贤폁\ud7a5", (Process.myPid() >> 22) + 3, objArr19);
        bVar.d(((String) objArr19[0]).intern(), aVar.A());
        Object[] objArr20 = new Object[1];
        f(KeyEvent.keyCodeFromString("") + 1, "\ufff5\u0004\t\u0000", 4 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 219, false, objArr20);
        bVar.d(((String) objArr20[0]).intern(), aVar.F().c());
        Object[] objArr21 = new Object[1];
        f(Gravity.getAbsoluteGravity(0, 0) + 7, "\ufff8\r\ufff6\uffff\ufffe\u0000\uffff￼\f\u0003", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 10, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Primes.SMALL_FACTOR_LIMIT, true, objArr21);
        bVar.d(((String) objArr21[0]).intern(), aVar.I());
        Object[] objArr22 = new Object[1];
        f(ExpandableListView.getPackedPositionChild(0L) + 18, "\uffff\n\ufff9\ufff7\t\u0004\ufff7\b\n\ufff5\ufffb\u0004\uffff\u0002￼￼\u0005\u0004\u0005", 19 - TextUtils.getOffsetBefore("", 0), (Process.myPid() >> 22) + 213, true, objArr22);
        bVar.d(((String) objArr22[0]).intern(), aVar.E());
        Object[] objArr23 = new Object[1];
        f(TextUtils.indexOf("", "", 0, 0) + 11, "\ufff5\u0004\uffff\u0006\ufff5\ufffb\u0004\uffff\u0002\u0004\u0005\ufffa\ufffb\b\uffff\u000b\u0007\ufffb\b", Process.getGidForName("") + 20, 212 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), true, objArr23);
        bVar.d(((String) objArr23[0]).intern(), aVar.H());
        Object[] objArr24 = new Object[1];
        g("᎘Ҋ䩃\ue7d7髓贤婪㐁챲䩆\ue6d7\u20f8疍笫톎艟艤\udc6f", View.getDefaultSize(0, 0) + 18, objArr24);
        bVar.d(((String) objArr24[0]).intern(), aVar.J());
        Object[] objArr25 = new Object[1];
        g("靂\udfb8鈚랱ၲ䍜\udc16쫨Ɗ秳", KeyEvent.keyCodeFromString("") + 10, objArr25);
        bVar.d(((String) objArr25[0]).intern(), aVar.K());
        Object[] objArr26 = new Object[1];
        g("쭫睡㑘岯䲅倉籇䕨\u0a60誒˻䦽\ude6d凳ᯏ₡諿蘶", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 17, objArr26);
        bVar.d(((String) objArr26[0]).intern(), aVar.v());
        return bVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 506
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.b.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0011, code lost:
    
        if (r20 != null) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 600
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.b.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
