package androidx.core.view;

import android.view.View;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\view\ViewPropertyAnimatorListenerAdapter.smali */
public class ViewPropertyAnimatorListenerAdapter implements ViewPropertyAnimatorListener {
    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationStart(View view) {
    }

    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationEnd(View view) {
    }

    @Override // androidx.core.view.ViewPropertyAnimatorListener
    public void onAnimationCancel(View view) {
    }
}
