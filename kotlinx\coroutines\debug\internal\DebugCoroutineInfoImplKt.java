package kotlinx.coroutines.debug.internal;

import kotlin.Metadata;

/* compiled from: DebugCoroutineInfoImpl.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000\"\u000e\u0010\u0003\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000¨\u0006\u0004"}, d2 = {DebugCoroutineInfoImplKt.CREATED, "", DebugCoroutineInfoImplKt.RUNNING, DebugCoroutineInfoImplKt.SUSPENDED, "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\debug\internal\DebugCoroutineInfoImplKt.smali */
public final class DebugCoroutineInfoImplKt {
    public static final String CREATED = "CREATED";
    public static final String RUNNING = "RUNNING";
    public static final String SUSPENDED = "SUSPENDED";
}
