package bc.org.bouncycastle.math.ec.rfc8032;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\e.smali */
abstract class e {
    static void a(int[] iArr, int i, byte[] bArr) {
        int length = iArr.length * 2;
        int[] iArr2 = new int[length];
        int i2 = iArr[iArr.length - 1] >> 31;
        int length2 = iArr.length;
        int i3 = length;
        while (true) {
            length2--;
            if (length2 < 0) {
                break;
            }
            int i4 = iArr[length2];
            int i5 = i3 - 1;
            iArr2[i5] = (i2 << 16) | (i4 >>> 16);
            i3 = i5 - 1;
            iArr2[i3] = i4;
            i2 = i4;
        }
        int i6 = 32 - i;
        int i7 = 0;
        int i8 = 0;
        int i9 = 0;
        while (i7 < length) {
            int i10 = iArr2[i7];
            while (i8 < 16) {
                int i11 = i10 >>> i8;
                if ((i11 & 1) == i9) {
                    i8++;
                } else {
                    int i12 = (i11 | 1) << i6;
                    bArr[(i7 << 4) + i8] = (byte) (i12 >> i6);
                    i8 += i;
                    i9 = i12 >>> 31;
                }
            }
            i7++;
            i8 -= 16;
        }
    }
}
