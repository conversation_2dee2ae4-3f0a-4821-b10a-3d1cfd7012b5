package org.bouncycastle.i18n.filter;

import com.esotericsoftware.asm.Opcodes;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\i18n\filter\SQLFilter.smali */
public class SQLFilter implements Filter {
    @Override // org.bouncycastle.i18n.filter.Filter
    public String doFilter(String str) {
        int i;
        String str2;
        StringBuffer stringBuffer = new StringBuffer(str);
        int i2 = 0;
        while (i2 < stringBuffer.length()) {
            switch (stringBuffer.charAt(i2)) {
                case '\n':
                    i = i2 + 1;
                    str2 = "\\n";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case '\r':
                    i = i2 + 1;
                    str2 = "\\r";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case '\"':
                    i = i2 + 1;
                    str2 = "\\\"";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case '\'':
                    i = i2 + 1;
                    str2 = "\\'";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case '-':
                    i = i2 + 1;
                    str2 = "\\-";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case '/':
                    i = i2 + 1;
                    str2 = "\\/";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case ';':
                    i = i2 + 1;
                    str2 = "\\;";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    i = i2 + 1;
                    str2 = "\\=";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
                case Opcodes.DUP2 /* 92 */:
                    i = i2 + 1;
                    str2 = "\\\\";
                    stringBuffer.replace(i2, i, str2);
                    i2 = i;
                    break;
            }
            i2++;
        }
        return stringBuffer.toString();
    }

    @Override // org.bouncycastle.i18n.filter.Filter
    public String doFilterUrl(String str) {
        return doFilter(str);
    }
}
