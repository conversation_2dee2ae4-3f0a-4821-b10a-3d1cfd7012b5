package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\t4.smali */
class t4 implements m6 {
    protected final int[] a;

    t4(int[] iArr) {
        this.a = Arrays.clone(iArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m6
    public int[] a() {
        return Arrays.clone(this.a);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m6
    public int b() {
        return this.a[r0.length - 1];
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof t4) {
            return Arrays.areEqual(this.a, ((t4) obj).a);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.hashCode(this.a);
    }
}
