package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureVirtualCardNumberResume.smali */
public final class SecureVirtualCardNumberResume implements CustomerAuthenticatedProcess {
    private final q innerSecureDigitalCardVcnManageResumeProcess;

    public SecureVirtualCardNumberResume(q qVar) {
        this.innerSecureDigitalCardVcnManageResumeProcess = qVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardVcnManageResumeProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardVcnManageResumeProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardVcnManageResumeProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardVcnManageResumeProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void resume(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnManageResumeProcess.e(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnManageResumeProcess));
    }

    public final void resume(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnManageResumeProcess.e(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnManageResumeProcess));
    }
}
