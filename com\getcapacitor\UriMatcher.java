package com.getcapacitor;

import androidx.webkit.ProxyConfig;
import java.util.ArrayList;
import java.util.regex.Pattern;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\UriMatcher.smali */
public class UriMatcher {
    private static final int EXACT = 0;
    private static final int MASK = 3;
    static final Pattern PATH_SPLIT_PATTERN = Pattern.compile("/");
    private static final int REST = 2;
    private static final int TEXT = 1;
    private ArrayList<UriMatcher> mChildren;
    private Object mCode;
    private String mText;
    private int mWhich;

    public UriMatcher(Object code) {
        this.mCode = code;
        this.mWhich = -1;
        this.mChildren = new ArrayList<>();
        this.mText = null;
    }

    private UriMatcher() {
        this.mCode = null;
        this.mWhich = -1;
        this.mChildren = new ArrayList<>();
        this.mText = null;
    }

    public void addURI(String scheme, String authority, String path, Object code) {
        if (code == null) {
            throw new IllegalArgumentException("Code can't be null");
        }
        String[] tokens = null;
        if (path != null) {
            String newPath = path;
            if (!path.isEmpty() && path.charAt(0) == '/') {
                newPath = path.substring(1);
            }
            tokens = PATH_SPLIT_PATTERN.split(newPath);
        }
        int numTokens = tokens != null ? tokens.length : 0;
        UriMatcher node = this;
        int i = -2;
        while (i < numTokens) {
            String token = i == -2 ? scheme : i == -1 ? authority : tokens[i];
            ArrayList<UriMatcher> children = node.mChildren;
            int numChildren = children.size();
            int j = 0;
            while (true) {
                if (j >= numChildren) {
                    break;
                }
                UriMatcher child = children.get(j);
                if (!token.equals(child.mText)) {
                    j++;
                } else {
                    node = child;
                    break;
                }
            }
            if (j == numChildren) {
                UriMatcher child2 = new UriMatcher();
                if (i == -1 && token.contains(ProxyConfig.MATCH_ALL_SCHEMES)) {
                    child2.mWhich = 3;
                } else if (token.equals("**")) {
                    child2.mWhich = 2;
                } else if (token.equals(ProxyConfig.MATCH_ALL_SCHEMES)) {
                    child2.mWhich = 1;
                } else {
                    child2.mWhich = 0;
                }
                child2.mText = token;
                node.mChildren.add(child2);
                node = child2;
            }
            i++;
        }
        node.mCode = code;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0066 A[LOOP:1: B:16:0x0039->B:27:0x0066, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:28:0x0069 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:30:0x006d A[LOOP:0: B:9:0x0015->B:30:0x006d, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:31:0x006b A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.lang.Object match(android.net.Uri r11) {
        /*
            r10 = this;
            java.util.List r0 = r11.getPathSegments()
            int r1 = r0.size()
            r2 = r10
            if (r1 != 0) goto L14
            java.lang.String r3 = r11.getAuthority()
            if (r3 != 0) goto L14
            java.lang.Object r3 = r10.mCode
            return r3
        L14:
            r3 = -2
        L15:
            if (r3 >= r1) goto L70
            r4 = -2
            if (r3 != r4) goto L1f
            java.lang.String r4 = r11.getScheme()
        L1e:
            goto L2e
        L1f:
            r4 = -1
            if (r3 != r4) goto L27
            java.lang.String r4 = r11.getAuthority()
            goto L1e
        L27:
            java.lang.Object r4 = r0.get(r3)
            java.lang.String r4 = (java.lang.String) r4
            goto L1e
        L2e:
            java.util.ArrayList<com.getcapacitor.UriMatcher> r5 = r2.mChildren
            if (r5 != 0) goto L33
            goto L70
        L33:
            r2 = 0
            int r6 = r5.size()
            r7 = 0
        L39:
            if (r7 >= r6) goto L69
            java.lang.Object r8 = r5.get(r7)
            com.getcapacitor.UriMatcher r8 = (com.getcapacitor.UriMatcher) r8
            int r9 = r8.mWhich
            switch(r9) {
                case 0: goto L5a;
                case 1: goto L58;
                case 2: goto L55;
                case 3: goto L47;
                default: goto L46;
            }
        L46:
            goto L63
        L47:
            java.lang.String r9 = r8.mText
            com.getcapacitor.util.HostMask r9 = com.getcapacitor.util.HostMask.Parser.parse(r9)
            boolean r9 = r9.matches(r4)
            if (r9 == 0) goto L63
            r2 = r8
            goto L63
        L55:
            java.lang.Object r9 = r8.mCode
            return r9
        L58:
            r2 = r8
            goto L63
        L5a:
            java.lang.String r9 = r8.mText
            boolean r9 = r9.equals(r4)
            if (r9 == 0) goto L63
            r2 = r8
        L63:
            if (r2 == 0) goto L66
            goto L69
        L66:
            int r7 = r7 + 1
            goto L39
        L69:
            if (r2 != 0) goto L6d
            r7 = 0
            return r7
        L6d:
            int r3 = r3 + 1
            goto L15
        L70:
            java.lang.Object r3 = r2.mCode
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: com.getcapacitor.UriMatcher.match(android.net.Uri):java.lang.Object");
    }
}
