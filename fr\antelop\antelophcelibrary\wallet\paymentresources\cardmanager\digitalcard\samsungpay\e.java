package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.samsung.android.sdk.samsungpay.v2.WatchManager;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.an.h;
import o.ee.g;
import o.ee.i;
import o.ep.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\e.smali */
public final class e extends c {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static final e f;
    private static int g;
    private static int h;
    private static char[] i;

    static void b() {
        i = new char[]{50856, 50703, 50803, 50703, 50801, 50815, 50803, 50702, 50787, 50815, 50806, 50700, 50701, 50815, 50808, 50701, 50698, 50695, 50699, 50800, 50813, 50925, 50823, 50846, 50845, 50820, 50821, 50816, 50844, 50836, 50846, 50824, 50822, 50823, 50817, 50838, 50843, 50819, 50842, 50833, 50816, 50821, 50842, 50846, 50823, 50844, 50841, 50845, 50933, 50766, 50783, 50783, 50850, 50819, 50819, 50878, 50777, 50755, 50777, 50781, 50777, 50879, 50840, 50840, 50854, 50755, 50777, 50752, 50761, 50755, 50777, 50781, 50777, 50905, 50939, 50939, 50840, 50869, 50876, 50819, 50941, 50941, 50930, 50859, 50835, 50874, 50868, 50877, 50851, 50857, 50863, 50874, 50879, 50859, 50877, 50875, 50879, 50875, 50848, 50848, 50873, 50873, 50867, 50867, 50874, 50877, 50937, 50765, 50781, 50759, 50780, 50777, 50752, 50753, 50780, 50760, 50765, 50757, 50753, 50781, 50780, 50777, 50777, 50851, 50707, 50733, 50708, 50700, 50803, 50712, 50714, 50815, 50777};
    }

    static void init$0() {
        $$d = new byte[]{79, 74, -126, -127};
        $$e = 4;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void s(short r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 66
            int r9 = r9 * 4
            int r9 = r9 + 1
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.e.$$d
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L30
        L17:
            r3 = r2
        L18:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L30:
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.e.s(short, short, int, java.lang.Object[]):void");
    }

    @Override // o.ep.a
    public final /* synthetic */ void e(Activity activity, a.c cVar, i iVar, o.eo.e eVar, o.ep.e eVar2, h.d dVar, o.ee.h hVar) {
        int i2 = h + Opcodes.LUSHR;
        g = i2 % 128;
        int i3 = i2 % 2;
        c(activity, cVar, eVar, dVar);
        int i4 = g + 51;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        b();
        f = new e();
        int i2 = h + 59;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    final String a() {
        Object obj;
        int i2 = g + 75;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '=' : (char) 11) {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                Object[] objArr = new Object[1];
                o("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 21, 86, 0}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                o("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 21, 86, 0}, true, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = g + 59;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? 'X' : '\f') {
            case Opcodes.POP2 /* 88 */:
                int i4 = 55 / 0;
                return intern;
            default:
                return intern;
        }
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    final String e() {
        Object obj;
        int i2 = g + 9;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? 'V' : '\b') {
            case '\b':
                Object[] objArr = new Object[1];
                o("\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{21, 27, 0, 2}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                o("\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{21, 27, 0, 2}, true, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = g + 71;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? 'K' : (char) 1) {
            case 1:
                int i4 = 93 / 0;
                return intern;
            default:
                return intern;
        }
    }

    public static e a(Context context) {
        int i2 = g + Opcodes.DDIV;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? Typography.amp : '%') {
            case '%':
                if (j == null) {
                    f.c(context);
                    int i3 = g + 75;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                }
                return f;
            default:
                WatchManager watchManager = j;
                throw null;
        }
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    protected final void c(Context context) {
        super.c(context);
        j = new WatchManager(context, this.b);
        int i2 = h + 57;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? '^' : 'G') {
            case Opcodes.DUP2_X2 /* 94 */:
                throw null;
            default:
                return;
        }
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    public final void b(a.InterfaceC0042a<String> interfaceC0042a, Boolean bool, String str) {
        g.c();
        String a = a();
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        o("\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{48, 25, 40, 16}, false, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(bool);
        Object[] objArr2 = new Object[1];
        o("\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{73, 9, 9, 0}, true, objArr2);
        g.d(a, append.append(((String) objArr2[0]).intern()).append(str).toString());
        ArrayList arrayList = new ArrayList();
        arrayList.add(str);
        j.getWalletInfo(arrayList, new SamsungPayInfoListener(this, interfaceC0042a, bool, str));
        int i2 = h + 57;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 58 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.ep.a
    public final void e(a.InterfaceC0042a<a.b> interfaceC0042a) {
        int i2 = g + Opcodes.DDIV;
        h = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        boolean z = false;
        o("\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000", new int[]{82, 24, 10, 22}, false, objArr);
        g.d(a, ((String) objArr[0]).intern());
        switch (z) {
            case true:
                int i4 = h + 55;
                g = i4 % 128;
                switch (i4 % 2 != 0 ? 'M' : '`') {
                    case Opcodes.IADD /* 96 */:
                        interfaceC0042a.e((a.InterfaceC0042a<a.b>) a.b.b);
                        break;
                    default:
                        interfaceC0042a.e((a.InterfaceC0042a<a.b>) a.b.b);
                        int i5 = 44 / 0;
                        break;
                }
        }
        j.getSamsungPayStatus(new SamsungPayStatusListener(interfaceC0042a));
        int i6 = g + 79;
        h = i6 % 128;
        int i7 = i6 % 2;
    }

    @Override // o.ep.a
    public final void d(a.InterfaceC0042a<List<o.ep.e>> interfaceC0042a) {
        g.c();
        Object[] objArr = new Object[1];
        o("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 21, 86, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o("\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{Opcodes.FMUL, 17, 36, 4}, true, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        j.getAllCards(new Bundle(), new SamsungPayGetCardListener(this, interfaceC0042a));
        int i2 = h + 85;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private void c(Activity activity, a.c cVar, o.eo.e eVar, h.d dVar) {
        g.c();
        Object[] objArr = new Object[1];
        o("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 21, 86, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o("\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001", new int[]{Opcodes.LSHR, 10, 108, 0}, false, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        j.addCard(b(dVar), new SamsungPayAddCardListener(this, activity, eVar.s().a(), cVar));
        int i2 = h + 97;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v24, types: [byte[]] */
    private static void o(String str, int[] iArr, boolean z, Object[] objArr) {
        int i2;
        char[] cArr;
        int i3;
        ?? r0 = str;
        int i4 = $10 + Opcodes.DNEG;
        $11 = i4 % 128;
        int i5 = 2;
        int i6 = i4 % 2;
        char c = 0;
        switch (r0 != 0) {
            case true:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i7 = iArr[0];
        int i8 = iArr[1];
        int i9 = iArr[2];
        int i10 = iArr[3];
        char[] cArr2 = i;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i11 = 0;
            while (true) {
                switch (i11 < length ? ';' : 'W') {
                    case Opcodes.POP /* 87 */:
                        cArr2 = cArr3;
                        break;
                    default:
                        int i12 = $11 + 15;
                        $10 = i12 % 128;
                        if (i12 % i5 != 0) {
                        }
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[c] = Integer.valueOf(cArr2[i11]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr = cArr2;
                                i3 = length;
                            } else {
                                Class cls = (Class) o.e.a.c(ExpandableListView.getPackedPositionChild(0L) + 12, (char) (MotionEvent.axisFromString("") + 1), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 43);
                                int i13 = $$e;
                                byte b = (byte) (i13 | 50);
                                byte b2 = (byte) (i13 - 4);
                                cArr = cArr2;
                                i3 = length;
                                Object[] objArr3 = new Object[1];
                                s(b, b2, b2, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr3[i11] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i11++;
                            cArr2 = cArr;
                            length = i3;
                            i5 = 2;
                            c = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        char[] cArr4 = new char[i8];
        System.arraycopy(cArr2, i7, cArr4, 0, i8);
        int i14 = 83;
        if (bArr != null) {
            int i15 = $11 + 7;
            $10 = i15 % 128;
            int i16 = i15 % 2;
            char[] cArr5 = new char[i8];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i8 ? (char) 3 : '8') {
                    case 3:
                        int i17 = $11 + Opcodes.LUSHR;
                        $10 = i17 % 128;
                        int i18 = i17 % 2;
                        if (bArr[lVar.d] == 1) {
                            int i19 = $11 + i14;
                            $10 = i19 % 128;
                            switch (i19 % 2 != 0) {
                                case false:
                                    int i20 = lVar.d;
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                        Object obj2 = o.e.a.s.get(2016040108);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(11 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), TextUtils.lastIndexOf("", '0', 0) + 449);
                                            int i21 = $$e;
                                            byte b3 = (byte) (i21 | 49);
                                            byte b4 = (byte) (i21 - 4);
                                            Object[] objArr5 = new Object[1];
                                            s(b3, b4, b4, objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(2016040108, obj2);
                                        }
                                        cArr5[i20] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                default:
                                    int i22 = lVar.d;
                                    try {
                                        Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                        Object obj3 = o.e.a.s.get(2016040108);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 11, (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 448);
                                            int i23 = $$e;
                                            byte b5 = (byte) (i23 | 49);
                                            byte b6 = (byte) (i23 - 4);
                                            Object[] objArr7 = new Object[1];
                                            s(b5, b6, b6, objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(2016040108, obj3);
                                        }
                                        cArr5[i22] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                        throw null;
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                            }
                        } else {
                            int i24 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj4 = o.e.a.s.get(804049217);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 9, (char) KeyEvent.getDeadChar(0, 0), 207 - (ViewConfiguration.getScrollDefaultDelay() >> 16));
                                    byte b7 = (byte) ($$e - 4);
                                    Object[] objArr9 = new Object[1];
                                    s((byte) 56, b7, b7, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj4);
                                }
                                cArr5[i24] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        c2 = cArr5[lVar.d];
                        try {
                            Object[] objArr10 = {lVar, lVar};
                            Object obj5 = o.e.a.s.get(-2112603350);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 12, (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 258 - TextUtils.lastIndexOf("", '0', 0));
                                byte b8 = (byte) ($$e - 4);
                                byte b9 = b8;
                                Object[] objArr11 = new Object[1];
                                s(b8, b9, b9, objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                            i14 = 83;
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    default:
                        cArr4 = cArr5;
                        break;
                }
            }
        }
        switch (i10 > 0 ? '1' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                i2 = 0;
                break;
            default:
                char[] cArr6 = new char[i8];
                i2 = 0;
                System.arraycopy(cArr4, 0, cArr6, 0, i8);
                int i25 = i8 - i10;
                System.arraycopy(cArr6, 0, cArr4, i25, i10);
                System.arraycopy(cArr6, i10, cArr4, 0, i25);
                break;
        }
        if (z) {
            char[] cArr7 = new char[i8];
            while (true) {
                lVar.d = i2;
                if (lVar.d < i8) {
                    cArr7[lVar.d] = cArr4[(i8 - lVar.d) - 1];
                    i2 = lVar.d + 1;
                } else {
                    cArr4 = cArr7;
                }
            }
        }
        if (i9 > 0) {
            int i26 = $10 + Opcodes.LMUL;
            $11 = i26 % 128;
            if (i26 % 2 == 0) {
                lVar.d = 1;
            } else {
                lVar.d = 0;
            }
            while (lVar.d < i8) {
                cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                lVar.d++;
            }
        }
        String str2 = new String(cArr4);
        int i27 = $11 + 45;
        $10 = i27 % 128;
        switch (i27 % 2 == 0) {
            case true:
                objArr[0] = str2;
                return;
            default:
                Object obj6 = null;
                obj6.hashCode();
                throw null;
        }
    }
}
