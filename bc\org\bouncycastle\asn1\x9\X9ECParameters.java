package bc.org.bouncycastle.asn1.x9;

import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X9ECParameters.smali */
public class X9ECParameters extends u implements j8 {
    private static final BigInteger v0 = BigInteger.valueOf(1);
    private f8 C;
    private BigInteger L;
    private BigInteger R;
    private h8 b;
    private byte[] u0;
    private ECCurve x;

    private X9ECParameters(e0 e0Var) {
        if (!(e0Var.a(0) instanceof r) || !((r) e0Var.a(0)).a(1)) {
            throw new IllegalArgumentException("bad version in X9ECParameters");
        }
        this.L = ((r) e0Var.a(4)).i();
        if (e0Var.size() == 6) {
            this.R = ((r) e0Var.a(5)).i();
        }
        d8 d8Var = new d8(h8.a(e0Var.a(1)), this.L, this.R, e0.a(e0Var.a(2)));
        this.x = d8Var.getCurve();
        h a = e0Var.a(3);
        if (a instanceof f8) {
            this.C = (f8) a;
        } else {
            this.C = new f8(this.x, (x) a);
        }
        this.u0 = d8Var.getSeed();
    }

    public static X9ECParameters getInstance(Object obj) {
        if (obj instanceof X9ECParameters) {
            return (X9ECParameters) obj;
        }
        if (obj != null) {
            return new X9ECParameters(e0.a(obj));
        }
        return null;
    }

    public f8 getBaseEntry() {
        return this.C;
    }

    public ECCurve getCurve() {
        return this.x;
    }

    public d8 getCurveEntry() {
        return new d8(this.x, this.u0);
    }

    public h8 getFieldIDEntry() {
        return this.b;
    }

    public ECPoint getG() {
        return this.C.e();
    }

    public BigInteger getH() {
        return this.R;
    }

    public BigInteger getN() {
        return this.L;
    }

    public byte[] getSeed() {
        return Arrays.clone(this.u0);
    }

    public boolean hasSeed() {
        return this.u0 != null;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(6);
        iVar.a(new r(v0));
        iVar.a(this.b);
        iVar.a(new d8(this.x, this.u0));
        iVar.a(this.C);
        iVar.a(new r(this.L));
        if (this.R != null) {
            iVar.a(new r(this.R));
        }
        return new j2(iVar);
    }

    public X9ECParameters(ECCurve eCCurve, f8 f8Var, BigInteger bigInteger) {
        this(eCCurve, f8Var, bigInteger, null, null);
    }

    public X9ECParameters(ECCurve eCCurve, f8 f8Var, BigInteger bigInteger, BigInteger bigInteger2) {
        this(eCCurve, f8Var, bigInteger, bigInteger2, null);
    }

    public X9ECParameters(ECCurve eCCurve, f8 f8Var, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr) {
        this.x = eCCurve;
        this.C = f8Var;
        this.L = bigInteger;
        this.R = bigInteger2;
        this.u0 = Arrays.clone(bArr);
        if (ECAlgorithms.isFpCurve(eCCurve)) {
            this.b = new h8(eCCurve.getField().c());
            return;
        }
        if (ECAlgorithms.isF2mCurve(eCCurve)) {
            int[] a = ((n6) eCCurve.getField()).a().a();
            if (a.length == 3) {
                this.b = new h8(a[2], a[1]);
                return;
            } else {
                if (a.length == 5) {
                    this.b = new h8(a[4], a[1], a[2], a[3]);
                    return;
                }
                throw new IllegalArgumentException("Only trinomial and pentomial curves are supported");
            }
        }
        throw new IllegalArgumentException("'curve' is of an unsupported type");
    }
}
