package o.q;

import android.graphics.Color;
import android.view.Gravity;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\q\e.smali */
public final class e implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static long d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        c();
        Color.rgb(0, 0, 0);
        int i = c + 99;
        b = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        d = 9074893868102700838L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = 114 - r7
            byte[] r0 = o.q.e.$$a
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.e.f(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{31, 57, -118, -60};
        $$b = 89;
    }

    @Override // o.q.c
    public final void d(o.eg.b bVar) {
        int i = c + 21;
        b = i % 128;
        int i2 = i % 2;
    }

    @Override // o.q.c
    public final void d(c cVar) {
        int i = c + 87;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = b + 37;
        c = i % 128;
        boolean z = obj instanceof e;
        switch (i % 2 != 0) {
            default:
                int i2 = 42 / 0;
            case false:
                return z;
        }
    }

    @Override // o.q.c
    public final o.eg.b e() {
        int i = b;
        int i2 = i + Opcodes.LSUB;
        c = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 75;
        c = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return null;
            default:
                throw null;
        }
    }

    @Override // o.q.c
    public final String b() {
        int i = c + 37;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        a("ু妦ꤸ\uf889䠗鯷\ueb5d㫀詐\uda2eⶻ紈첗ᱮ濶뽝\u0ef3幄긥\uf1b2䄇邷\ue061㏥荏", 20593 - Gravity.getAbsoluteGravity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = b + 37;
        c = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 29 : 'L') {
            case 29:
                throw null;
            default:
                return intern;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void a(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 658
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.e.a(java.lang.String, int, java.lang.Object[]):void");
    }
}
