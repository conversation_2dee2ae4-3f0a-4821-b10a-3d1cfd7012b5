package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\c.smali */
class c {
    private static final BigInteger a;
    private static final BigInteger b;
    private static final BigInteger c;
    public static final e[] d;
    public static final byte[][] e;
    public static final e[] f;
    public static final byte[][] g;

    static {
        BigInteger bigInteger = ECConstants.ONE;
        BigInteger negate = bigInteger.negate();
        a = negate;
        b = ECConstants.TWO.negate();
        BigInteger bigInteger2 = ECConstants.THREE;
        BigInteger negate2 = bigInteger2.negate();
        c = negate2;
        BigInteger bigInteger3 = ECConstants.ZERO;
        d = new e[]{null, new e(bigInteger, bigInteger3), null, new e(negate2, negate), null, new e(negate, negate), null, new e(bigInteger, negate), null, new e(negate, bigInteger), null, new e(bigInteger, bigInteger), null, new e(bigInteger2, bigInteger), null, new e(negate, bigInteger3)};
        e = new byte[][]{null, new byte[]{1}, null, new byte[]{-1, 0, 1}, null, new byte[]{1, 0, 1}, null, new byte[]{-1, 0, 0, 1}};
        f = new e[]{null, new e(bigInteger, bigInteger3), null, new e(negate2, bigInteger), null, new e(negate, bigInteger), null, new e(bigInteger, bigInteger), null, new e(negate, negate), null, new e(bigInteger, negate), null, new e(bigInteger2, negate), null, new e(negate, bigInteger3)};
        g = new byte[][]{null, new byte[]{1}, null, new byte[]{-1, 0, 1}, null, new byte[]{1, 0, 1}, null, new byte[]{-1, 0, 0, -1}};
    }

    public static byte a(int i) {
        return (byte) (i == 0 ? -1 : 1);
    }

    public static BigInteger a(byte b2, e eVar) {
        BigInteger bigInteger = eVar.a;
        BigInteger multiply = bigInteger.multiply(bigInteger);
        if (b2 == 1) {
            return eVar.b.shiftLeft(1).add(eVar.a).multiply(eVar.b).add(multiply);
        }
        if (b2 == -1) {
            return eVar.b.shiftLeft(1).subtract(eVar.a).multiply(eVar.b).add(multiply);
        }
        throw new IllegalArgumentException("mu must be 1 or -1");
    }

    /* JADX WARN: Code restructure failed: missing block: B:18:0x0067, code lost:
    
        if (r5.a(bc.org.bouncycastle.math.ec.c.a) < 0) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0082, code lost:
    
        if (r5.a(r9) >= 0) goto L34;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x008b, code lost:
    
        if (r8.a(bc.org.bouncycastle.math.ec.c.b) < 0) goto L34;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static bc.org.bouncycastle.math.ec.e a(bc.org.bouncycastle.math.ec.b r8, bc.org.bouncycastle.math.ec.b r9, byte r10) {
        /*
            int r0 = r8.b()
            int r1 = r9.b()
            if (r1 != r0) goto La8
            r0 = -1
            r1 = 1
            if (r10 == r1) goto L19
            if (r10 != r0) goto L11
            goto L19
        L11:
            java.lang.IllegalArgumentException r8 = new java.lang.IllegalArgumentException
            java.lang.String r9 = "mu must be 1 or -1"
            r8.<init>(r9)
            throw r8
        L19:
            java.math.BigInteger r2 = r8.d()
            java.math.BigInteger r3 = r9.d()
            bc.org.bouncycastle.math.ec.b r8 = r8.b(r2)
            bc.org.bouncycastle.math.ec.b r9 = r9.b(r3)
            bc.org.bouncycastle.math.ec.b r4 = r8.a(r8)
            if (r10 != r1) goto L34
            bc.org.bouncycastle.math.ec.b r4 = r4.a(r9)
            goto L38
        L34:
            bc.org.bouncycastle.math.ec.b r4 = r4.c(r9)
        L38:
            bc.org.bouncycastle.math.ec.b r5 = r9.a(r9)
            bc.org.bouncycastle.math.ec.b r5 = r5.a(r9)
            bc.org.bouncycastle.math.ec.b r9 = r5.a(r9)
            if (r10 != r1) goto L4f
            bc.org.bouncycastle.math.ec.b r5 = r8.c(r5)
            bc.org.bouncycastle.math.ec.b r8 = r8.a(r9)
            goto L57
        L4f:
            bc.org.bouncycastle.math.ec.b r5 = r8.a(r5)
            bc.org.bouncycastle.math.ec.b r8 = r8.c(r9)
        L57:
            java.math.BigInteger r9 = bc.org.bouncycastle.math.ec.ECConstants.ONE
            int r6 = r4.a(r9)
            r7 = 0
            if (r6 < 0) goto L6a
            java.math.BigInteger r6 = bc.org.bouncycastle.math.ec.c.a
            int r6 = r5.a(r6)
            if (r6 >= 0) goto L76
            goto L72
        L6a:
            java.math.BigInteger r1 = bc.org.bouncycastle.math.ec.ECConstants.TWO
            int r1 = r8.a(r1)
            if (r1 < 0) goto L75
        L72:
            r1 = r7
            r7 = r10
            goto L76
        L75:
            r1 = r7
        L76:
            java.math.BigInteger r6 = bc.org.bouncycastle.math.ec.c.a
            int r4 = r4.a(r6)
            if (r4 >= 0) goto L85
            int r8 = r5.a(r9)
            if (r8 < 0) goto L90
            goto L8d
        L85:
            java.math.BigInteger r9 = bc.org.bouncycastle.math.ec.c.b
            int r8 = r8.a(r9)
            if (r8 >= 0) goto L8f
        L8d:
            int r8 = -r10
            byte r7 = (byte) r8
        L8f:
            r0 = r1
        L90:
            long r8 = (long) r0
            java.math.BigInteger r8 = java.math.BigInteger.valueOf(r8)
            java.math.BigInteger r8 = r2.add(r8)
            long r9 = (long) r7
            java.math.BigInteger r9 = java.math.BigInteger.valueOf(r9)
            java.math.BigInteger r9 = r3.add(r9)
            bc.org.bouncycastle.math.ec.e r10 = new bc.org.bouncycastle.math.ec.e
            r10.<init>(r8, r9)
            return r10
        La8:
            java.lang.IllegalArgumentException r8 = new java.lang.IllegalArgumentException
            java.lang.String r9 = "lambda0 and lambda1 do not have same scale"
            r8.<init>(r9)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: bc.org.bouncycastle.math.ec.c.a(bc.org.bouncycastle.math.ec.b, bc.org.bouncycastle.math.ec.b, byte):bc.org.bouncycastle.math.ec.e");
    }

    public static b a(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, byte b2, int i, int i2) {
        BigInteger multiply = bigInteger2.multiply(bigInteger.shiftRight(((i - r0) - 2) + b2));
        BigInteger add = multiply.add(bigInteger3.multiply(multiply.shiftRight(i)));
        int i3 = (((i + 5) / 2) + i2) - i2;
        BigInteger shiftRight = add.shiftRight(i3);
        if (add.testBit(i3 - 1)) {
            shiftRight = shiftRight.add(ECConstants.ONE);
        }
        return new b(shiftRight, i2);
    }

    public static BigInteger[] a(byte b2, int i, boolean z) {
        BigInteger bigInteger;
        BigInteger bigInteger2;
        if (b2 != 1 && b2 != -1) {
            throw new IllegalArgumentException("mu must be 1 or -1");
        }
        if (z) {
            bigInteger = ECConstants.TWO;
            bigInteger2 = BigInteger.valueOf(b2);
        } else {
            bigInteger = ECConstants.ZERO;
            bigInteger2 = ECConstants.ONE;
        }
        int i2 = 1;
        while (i2 < i) {
            i2++;
            BigInteger bigInteger3 = bigInteger2;
            bigInteger2 = (b2 < 0 ? bigInteger2.negate() : bigInteger2).subtract(bigInteger.shiftLeft(1));
            bigInteger = bigInteger3;
        }
        return new BigInteger[]{bigInteger, bigInteger2};
    }

    public static BigInteger a(byte b2, int i) {
        if (i == 4) {
            if (b2 == 1) {
                return BigInteger.valueOf(6L);
            }
            return BigInteger.valueOf(10L);
        }
        BigInteger[] a2 = a(b2, i, false);
        BigInteger bit = ECConstants.ZERO.setBit(i);
        return a2[0].shiftLeft(1).multiply(a2[1].modInverse(bit)).mod(bit);
    }

    public static BigInteger[] a(ECCurve.AbstractF2m abstractF2m) {
        if (abstractF2m.isKoblitz()) {
            return a(abstractF2m.getFieldSize(), abstractF2m.getA().toBigInteger().intValue(), abstractF2m.getCofactor());
        }
        throw new IllegalArgumentException("si is defined for Koblitz curves only");
    }

    public static BigInteger[] a(int i, int i2, BigInteger bigInteger) {
        byte a2 = a(i2);
        int a3 = a(bigInteger);
        BigInteger[] a4 = a(a2, (i + 3) - i2, false);
        if (a2 == 1) {
            a4[0] = a4[0].negate();
            a4[1] = a4[1].negate();
        }
        BigInteger bigInteger2 = ECConstants.ONE;
        return new BigInteger[]{bigInteger2.add(a4[1]).shiftRight(a3), bigInteger2.add(a4[0]).shiftRight(a3).negate()};
    }

    protected static int a(BigInteger bigInteger) {
        if (bigInteger != null) {
            if (bigInteger.equals(ECConstants.TWO)) {
                return 1;
            }
            if (bigInteger.equals(ECConstants.FOUR)) {
                return 2;
            }
        }
        throw new IllegalArgumentException("h (Cofactor) must be 2 or 4");
    }

    public static e a(ECCurve.AbstractF2m abstractF2m, BigInteger bigInteger, byte b2, byte b3, byte b4) {
        BigInteger subtract;
        BigInteger bigInteger2;
        int fieldSize = abstractF2m.getFieldSize();
        BigInteger[] c2 = abstractF2m.c();
        if (b3 == 1) {
            subtract = c2[0].add(c2[1]);
        } else {
            subtract = c2[0].subtract(c2[1]);
        }
        if (abstractF2m.isKoblitz()) {
            BigInteger bigInteger3 = ECConstants.ONE;
            bigInteger2 = bigInteger3.shiftLeft(fieldSize).add(bigInteger3).subtract(abstractF2m.getOrder().multiply(abstractF2m.getCofactor()));
        } else {
            bigInteger2 = a(b3, fieldSize, true)[1];
        }
        BigInteger bigInteger4 = bigInteger2;
        e a2 = a(a(bigInteger, c2[0], bigInteger4, b2, fieldSize, b4), a(bigInteger, c2[1], bigInteger4, b2, fieldSize, b4), b3);
        return new e(bigInteger.subtract(subtract.multiply(a2.a)).subtract(c2[1].multiply(a2.b).shiftLeft(1)), c2[1].multiply(a2.a).subtract(c2[0].multiply(a2.b)));
    }

    public static ECPoint.AbstractF2m a(ECPoint.AbstractF2m abstractF2m, ECPoint.AbstractF2m abstractF2m2, byte[] bArr) {
        ECPoint.AbstractF2m abstractF2m3 = (ECPoint.AbstractF2m) abstractF2m.getCurve().getInfinity();
        int i = 0;
        for (int length = bArr.length - 1; length >= 0; length--) {
            i++;
            byte b2 = bArr[length];
            if (b2 != 0) {
                abstractF2m3 = (ECPoint.AbstractF2m) abstractF2m3.tauPow(i).add(b2 > 0 ? abstractF2m : abstractF2m2);
                i = 0;
            }
        }
        return i > 0 ? abstractF2m3.tauPow(i) : abstractF2m3;
    }

    public static byte[] a(byte b2, e eVar, int i, int i2, e[] eVarArr) {
        BigInteger subtract;
        if (b2 != 1 && b2 != -1) {
            throw new IllegalArgumentException("mu must be 1 or -1");
        }
        int bitLength = a(b2, eVar).bitLength();
        byte[] bArr = new byte[bitLength > 30 ? bitLength + 4 + i : i + 34];
        int i3 = (1 << i) - 1;
        int i4 = 32 - i;
        BigInteger bigInteger = eVar.a;
        BigInteger bigInteger2 = eVar.b;
        int i5 = 0;
        while (true) {
            if (bigInteger.bitLength() <= 62 && bigInteger2.bitLength() <= 62) {
                break;
            }
            if (bigInteger.testBit(0)) {
                int intValue = bigInteger.intValue() + (bigInteger2.intValue() * i2);
                int i6 = intValue & i3;
                bArr[i5] = (byte) ((intValue << i4) >> i4);
                bigInteger = bigInteger.subtract(eVarArr[i6].a);
                bigInteger2 = bigInteger2.subtract(eVarArr[i6].b);
            }
            i5++;
            BigInteger shiftRight = bigInteger.shiftRight(1);
            if (b2 == 1) {
                subtract = bigInteger2.add(shiftRight);
            } else {
                subtract = bigInteger2.subtract(shiftRight);
            }
            BigInteger negate = shiftRight.negate();
            bigInteger = subtract;
            bigInteger2 = negate;
        }
        long c2 = f1.c(bigInteger);
        long c3 = f1.c(bigInteger2);
        while ((c2 | c3) != 0) {
            if ((1 & c2) != 0) {
                int i7 = ((int) c2) + (((int) c3) * i2);
                int i8 = i7 & i3;
                bArr[i5] = (byte) ((i7 << i4) >> i4);
                c2 -= eVarArr[i8].a.intValue();
                c3 -= eVarArr[i8].b.intValue();
            }
            i5++;
            long j = c2 >> 1;
            long j2 = b2 == 1 ? c3 + j : c3 - j;
            long j3 = -j;
            c2 = j2;
            c3 = j3;
        }
        return bArr;
    }

    public static ECPoint.AbstractF2m[] a(ECPoint.AbstractF2m abstractF2m, byte b2) {
        ECPoint.AbstractF2m abstractF2m2 = (ECPoint.AbstractF2m) abstractF2m.negate();
        byte[][] bArr = b2 == 0 ? e : g;
        ECPoint.AbstractF2m[] abstractF2mArr = new ECPoint.AbstractF2m[(bArr.length + 1) >>> 1];
        abstractF2mArr[0] = abstractF2m;
        int length = bArr.length;
        for (int i = 3; i < length; i += 2) {
            abstractF2mArr[i >>> 1] = a(abstractF2m, abstractF2m2, bArr[i]);
        }
        abstractF2m.getCurve().normalizeAll(abstractF2mArr);
        return abstractF2mArr;
    }
}
