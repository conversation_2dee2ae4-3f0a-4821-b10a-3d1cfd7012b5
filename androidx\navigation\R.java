package androidx.navigation;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$attr.smali */
    public static final class attr {
        public static int action = 0x7f040000;
        public static int alpha = 0x7f04002b;
        public static int argType = 0x7f0400ad;
        public static int data = 0x7f040161;
        public static int dataPattern = 0x7f040162;
        public static int destination = 0x7f040169;
        public static int enterAnim = 0x7f04018f;
        public static int exitAnim = 0x7f040196;
        public static int font = 0x7f0401b0;
        public static int fontProviderAuthority = 0x7f0401b2;
        public static int fontProviderCerts = 0x7f0401b3;
        public static int fontProviderFetchStrategy = 0x7f0401b4;
        public static int fontProviderFetchTimeout = 0x7f0401b5;
        public static int fontProviderPackage = 0x7f0401b6;
        public static int fontProviderQuery = 0x7f0401b7;
        public static int fontStyle = 0x7f0401b9;
        public static int fontVariationSettings = 0x7f0401ba;
        public static int fontWeight = 0x7f0401bb;
        public static int graph = 0x7f0401bf;
        public static int launchSingleTop = 0x7f0401fd;
        public static int nullable = 0x7f040273;
        public static int popEnterAnim = 0x7f040283;
        public static int popExitAnim = 0x7f040284;
        public static int popUpTo = 0x7f040285;
        public static int popUpToInclusive = 0x7f040286;
        public static int startDestination = 0x7f0402be;
        public static int ttcIndex = 0x7f040331;
        public static int uri = 0x7f040332;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$color.smali */
    public static final class color {
        public static int notification_action_color_filter = 0x7f0600dc;
        public static int notification_icon_bg_color = 0x7f0600dd;
        public static int ripple_material_light = 0x7f0600e8;
        public static int secondary_text_default_material_light = 0x7f0600ea;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$dimen.smali */
    public static final class dimen {
        public static int compat_button_inset_horizontal_material = 0x7f07008e;
        public static int compat_button_inset_vertical_material = 0x7f07008f;
        public static int compat_button_padding_horizontal_material = 0x7f070090;
        public static int compat_button_padding_vertical_material = 0x7f070091;
        public static int compat_control_corner_material = 0x7f070092;
        public static int compat_notification_large_icon_max_height = 0x7f070093;
        public static int compat_notification_large_icon_max_width = 0x7f070094;
        public static int notification_action_icon_size = 0x7f07016b;
        public static int notification_action_text_size = 0x7f07016c;
        public static int notification_big_circle_margin = 0x7f07016d;
        public static int notification_content_margin_start = 0x7f07016e;
        public static int notification_large_icon_height = 0x7f07016f;
        public static int notification_large_icon_width = 0x7f070170;
        public static int notification_main_column_padding_top = 0x7f070171;
        public static int notification_media_narrow_margin = 0x7f070172;
        public static int notification_right_icon_size = 0x7f070173;
        public static int notification_right_side_padding_top = 0x7f070174;
        public static int notification_small_icon_background_padding = 0x7f070175;
        public static int notification_small_icon_size_as_large = 0x7f070176;
        public static int notification_subtext_size = 0x7f070177;
        public static int notification_top_pad = 0x7f070178;
        public static int notification_top_pad_large_text = 0x7f070179;

        private dimen() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$drawable.smali */
    public static final class drawable {
        public static int notification_action_background = 0x7f08015d;
        public static int notification_bg = 0x7f08015e;
        public static int notification_bg_low = 0x7f08015f;
        public static int notification_bg_low_normal = 0x7f080160;
        public static int notification_bg_low_pressed = 0x7f080161;
        public static int notification_bg_normal = 0x7f080162;
        public static int notification_bg_normal_pressed = 0x7f080163;
        public static int notification_icon_background = 0x7f080164;
        public static int notification_template_icon_bg = 0x7f080166;
        public static int notification_template_icon_low_bg = 0x7f080167;
        public static int notification_tile_bg = 0x7f080168;
        public static int notify_panel_notification_icon_bg = 0x7f080169;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$id.smali */
    public static final class id {
        public static int action_container = 0x7f0a0038;
        public static int action_divider = 0x7f0a003a;
        public static int action_image = 0x7f0a003b;
        public static int action_text = 0x7f0a0041;
        public static int actions = 0x7f0a0042;
        public static int async = 0x7f0a005b;
        public static int blocking = 0x7f0a005f;
        public static int chronometer = 0x7f0a006e;
        public static int forever = 0x7f0a00a3;
        public static int icon = 0x7f0a00b1;
        public static int icon_group = 0x7f0a00b2;
        public static int info = 0x7f0a00b6;
        public static int italic = 0x7f0a00b8;
        public static int line1 = 0x7f0a00bf;
        public static int line3 = 0x7f0a00c0;
        public static int nav_controller_view_tag = 0x7f0a00e3;
        public static int normal = 0x7f0a00ea;
        public static int notification_background = 0x7f0a00eb;
        public static int notification_main_column = 0x7f0a00ec;
        public static int notification_main_column_container = 0x7f0a00ed;
        public static int right_icon = 0x7f0a0100;
        public static int right_side = 0x7f0a0101;
        public static int tag_transition_group = 0x7f0a0141;
        public static int tag_unhandled_key_event_manager = 0x7f0a0142;
        public static int tag_unhandled_key_listeners = 0x7f0a0143;
        public static int text = 0x7f0a0147;
        public static int text2 = 0x7f0a0148;
        public static int time = 0x7f0a0153;
        public static int title = 0x7f0a0154;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$integer.smali */
    public static final class integer {
        public static int status_bar_notification_info_maxnum = 0x7f0b0019;

        private integer() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$layout.smali */
    public static final class layout {
        public static int notification_action = 0x7f0d005b;
        public static int notification_action_tombstone = 0x7f0d005c;
        public static int notification_template_custom_big = 0x7f0d0063;
        public static int notification_template_icon_group = 0x7f0d0064;
        public static int notification_template_part_chronometer = 0x7f0d0068;
        public static int notification_template_part_time = 0x7f0d0069;

        private layout() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$string.smali */
    public static final class string {
        public static int status_bar_notification_info_overflow = 0x7f120138;

        private string() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$style.smali */
    public static final class style {
        public static int TextAppearance_Compat_Notification = 0x7f13017d;
        public static int TextAppearance_Compat_Notification_Info = 0x7f13017e;
        public static int TextAppearance_Compat_Notification_Line2 = 0x7f130180;
        public static int TextAppearance_Compat_Notification_Time = 0x7f130183;
        public static int TextAppearance_Compat_Notification_Title = 0x7f130185;
        public static int Widget_Compat_NotificationActionContainer = 0x7f130272;
        public static int Widget_Compat_NotificationActionText = 0x7f130273;

        private style() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\navigation\R$styleable.smali */
    public static final class styleable {
        public static int ActivityNavigator_action = 0x00000001;
        public static int ActivityNavigator_android_name = 0x00000000;
        public static int ActivityNavigator_data = 0x00000002;
        public static int ActivityNavigator_dataPattern = 0x00000003;
        public static int ColorStateListItem_alpha = 0x00000003;
        public static int ColorStateListItem_android_alpha = 0x00000001;
        public static int ColorStateListItem_android_color = 0x00000000;
        public static int ColorStateListItem_android_lStar = 0x00000002;
        public static int ColorStateListItem_lStar = 0x00000004;
        public static int FontFamilyFont_android_font = 0x00000000;
        public static int FontFamilyFont_android_fontStyle = 0x00000002;
        public static int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static int FontFamilyFont_android_fontWeight = 0x00000001;
        public static int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static int FontFamilyFont_font = 0x00000005;
        public static int FontFamilyFont_fontStyle = 0x00000006;
        public static int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static int FontFamilyFont_fontWeight = 0x00000008;
        public static int FontFamilyFont_ttcIndex = 0x00000009;
        public static int FontFamily_fontProviderAuthority = 0x00000000;
        public static int FontFamily_fontProviderCerts = 0x00000001;
        public static int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static int FontFamily_fontProviderPackage = 0x00000004;
        public static int FontFamily_fontProviderQuery = 0x00000005;
        public static int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static int GradientColorItem_android_color = 0x00000000;
        public static int GradientColorItem_android_offset = 0x00000001;
        public static int GradientColor_android_centerColor = 0x00000007;
        public static int GradientColor_android_centerX = 0x00000003;
        public static int GradientColor_android_centerY = 0x00000004;
        public static int GradientColor_android_endColor = 0x00000001;
        public static int GradientColor_android_endX = 0x0000000a;
        public static int GradientColor_android_endY = 0x0000000b;
        public static int GradientColor_android_gradientRadius = 0x00000005;
        public static int GradientColor_android_startColor = 0x00000000;
        public static int GradientColor_android_startX = 0x00000008;
        public static int GradientColor_android_startY = 0x00000009;
        public static int GradientColor_android_tileMode = 0x00000006;
        public static int GradientColor_android_type = 0x00000002;
        public static int NavAction_android_id = 0x00000000;
        public static int NavAction_destination = 0x00000001;
        public static int NavAction_enterAnim = 0x00000002;
        public static int NavAction_exitAnim = 0x00000003;
        public static int NavAction_launchSingleTop = 0x00000004;
        public static int NavAction_popEnterAnim = 0x00000005;
        public static int NavAction_popExitAnim = 0x00000006;
        public static int NavAction_popUpTo = 0x00000007;
        public static int NavAction_popUpToInclusive = 0x00000008;
        public static int NavArgument_android_defaultValue = 0x00000001;
        public static int NavArgument_android_name = 0x00000000;
        public static int NavArgument_argType = 0x00000002;
        public static int NavArgument_nullable = 0x00000003;
        public static int NavDeepLink_android_autoVerify = 0x00000000;
        public static int NavDeepLink_uri = 0x00000001;
        public static int NavGraphNavigator_startDestination = 0x00000000;
        public static int NavInclude_graph = 0x00000000;
        public static int Navigator_android_id = 0x00000001;
        public static int Navigator_android_label;
        public static int[] ActivityNavigator = {android.R.attr.name, 2130968576, 2130968929, 2130968930};
        public static int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, 2130968619, 2130969082};
        public static int[] FontFamily = {2130969010, 2130969011, 2130969012, 2130969013, 2130969014, 2130969015, 2130969016};
        public static int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, 2130969008, 2130969017, 2130969018, 2130969019, 2130969393};
        public static int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static int[] NavAction = {android.R.attr.id, 2130968937, 2130968975, 2130968982, 2130969085, 2130969219, 2130969220, 2130969221, 2130969222};
        public static int[] NavArgument = {android.R.attr.name, android.R.attr.defaultValue, 2130968749, 2130969203};
        public static int[] NavDeepLink = {android.R.attr.autoVerify, 2130969394};
        public static int[] NavGraphNavigator = {2130969278};
        public static int[] NavInclude = {2130969023};
        public static int[] Navigator = {android.R.attr.label, android.R.attr.id};

        private styleable() {
        }
    }

    private R() {
    }
}
