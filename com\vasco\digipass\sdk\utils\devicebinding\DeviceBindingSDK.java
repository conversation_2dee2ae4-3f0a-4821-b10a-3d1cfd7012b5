package com.vasco.digipass.sdk.utils.devicebinding;

import android.content.Context;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.d;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.p;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.nio.charset.Charset;
import kotlin.Deprecated;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

@Deprecated(message = "")
@Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\bÇ\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u001c\u0010\u0005\u001a\u00020\u00042\b\u0010\u0006\u001a\u0004\u0018\u00010\u00042\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0007J\u0012\u0010\t\u001a\u00020\u00042\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000¨\u0006\f"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDK;", "", "()V", "VERSION", "", "getDeviceFingerprint", "salt", "context", "Landroid/content/Context;", "getFingerprint", "params", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDKParams;", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingSDK.smali */
public final class DeviceBindingSDK {
    public static final DeviceBindingSDK INSTANCE = new DeviceBindingSDK();
    public static final String VERSION = "5.3.0";

    private DeviceBindingSDK() {
    }

    @Deprecated(message = "This method is no longer acceptable for getting fingerprints.\n Use the [DeviceBinding.createFingerprint] to get an instance of the DeviceBinding andthen use [DeviceBinding.fingerprint] to fetch the fingerprint.")
    @JvmStatic
    public static final String getDeviceFingerprint(String salt, Context context) throws DeviceBindingSDKException {
        try {
            if (context == null) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.CONTEXT_NULL, null, 2, null);
            }
            d dVar = new d();
            String b = dVar.b(context);
            String d = dVar.d(context);
            StringBuilder append = new StringBuilder().append(b);
            if (d == null) {
                d = "null";
            }
            StringBuilder append2 = append.append(d);
            if (salt == null) {
                salt = "";
            }
            StringBuilder append3 = append2.append(salt);
            Intrinsics.checkNotNullExpressionValue(append3, "StringBuilder().append(a…      .append(salt ?: \"\")");
            String sb = append3.toString();
            Intrinsics.checkNotNullExpressionValue(sb, "toBeHashed.toString()");
            Charset forName = Charset.forName("UTF-8");
            Intrinsics.checkNotNullExpressionValue(forName, "forName(\"UTF-8\")");
            byte[] bytes = sb.getBytes(forName);
            Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
            UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, bytes);
            Intrinsics.checkNotNullExpressionValue(hash, "hash(\n                  …TF-8\"))\n                )");
            if (hash.getReturnCode() == 0) {
                return p.a(hash.getOutputData());
            }
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        } catch (DeviceBindingSDKException e) {
            throw e;
        } catch (SecurityException e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.PERMISSION_DENIED, null, 2, null);
        } catch (Exception e3) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, e3);
        }
    }

    @Deprecated(message = "This method is no longer acceptable for generating fingerprints.\n Use the [DeviceBinding.createFingerprint] to get an instance of the DeviceBinding andthen use [DeviceBinding.fingerprint] to fetch the fingerprint.")
    @JvmStatic
    public static final String getFingerprint(DeviceBindingSDKParams params) throws DeviceBindingSDKException {
        if (params == null) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.PARAMETERS_NULL, null, 2, null);
        }
        Context context = params.getContext();
        if (context == null) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.CONTEXT_NULL, null, 2, null);
        }
        String salt = params.getSalt();
        if (salt == null) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_NULL, null, 2, null);
        }
        if (params.getIsAndroidIdUsed() && params.getIsSerialUsed()) {
            return DeviceBinding.INSTANCE.createDeviceBinding(context, DeviceBinding.FingerprintType.SERIAL_WITH_ANDROID_ID).fingerprint(salt);
        }
        if (params.getIsSerialUsed()) {
            return DeviceBinding.INSTANCE.createDeviceBinding(context, DeviceBinding.FingerprintType.SERIAL).fingerprint(salt);
        }
        if (params.getIsAndroidIdUsed()) {
            return DeviceBinding.INSTANCE.createDeviceBinding(context, DeviceBinding.FingerprintType.ANDROID_ID).fingerprint(salt);
        }
        if (params.getIsHardwareUsed()) {
            return DeviceBinding.INSTANCE.createDeviceBinding(context, DeviceBinding.FingerprintType.HARDWARE).fingerprint(salt);
        }
        throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.UNIQUE_DATA_UNDEFINED, null, 2, null);
    }
}
