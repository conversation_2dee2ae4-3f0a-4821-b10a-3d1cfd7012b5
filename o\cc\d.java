package o.cc;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cc\d.smali */
final class d {
    private static char a;
    private static char c;
    private static char d;
    private static char e;
    private static int i;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int b = 0;

    static {
        i = 1;
        e();
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getFadingEdgeLength();
        Process.getGidForName("");
        int i2 = b + 67;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                int i3 = 51 / 0;
                break;
        }
    }

    static void e() {
        a = (char) 11851;
        e = (char) 32628;
        c = (char) 41361;
        d = (char) 54792;
    }

    d() {
    }

    /* renamed from: o.cc.d$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cc\d$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        static final /* synthetic */ int[] b;
        private static int d;

        static {
            d = 0;
            a = 1;
            int[] iArr = new int[b.values().length];
            b = iArr;
            try {
                iArr[b.c.ordinal()] = 1;
                int i = d;
                int i2 = (i & 95) + (i | 95);
                a = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                b[b.e.ordinal()] = 2;
                int i4 = d;
                int i5 = (i4 & Opcodes.LREM) + (i4 | Opcodes.LREM);
                a = i5 % 128;
                if (i5 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[b.a.ordinal()] = 3;
                int i6 = d + Opcodes.DREM;
                a = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                b[b.d.ordinal()] = 4;
                int i8 = d;
                int i9 = ((i8 | 63) << 1) - (i8 ^ 63);
                a = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                b[b.b.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                b[b.g.ordinal()] = 6;
                int i11 = d;
                int i12 = (i11 ^ 81) + ((i11 & 81) << 1);
                a = i12 % 128;
                int i13 = i12 % 2;
            } catch (NoSuchFieldError e6) {
            }
            try {
                b[b.f.ordinal()] = 7;
                int i14 = d;
                int i15 = (i14 & 41) + (i14 | 41);
                a = i15 % 128;
                if (i15 % 2 == 0) {
                }
            } catch (NoSuchFieldError e7) {
            }
            try {
                b[b.h.ordinal()] = 8;
            } catch (NoSuchFieldError e8) {
            }
            try {
                b[b.j.ordinal()] = 9;
                int i16 = (a + 94) - 1;
                d = i16 % 128;
                if (i16 % 2 != 0) {
                }
            } catch (NoSuchFieldError e9) {
            }
            try {
                b[b.i.ordinal()] = 10;
            } catch (NoSuchFieldError e10) {
            }
        }
    }

    static e<? extends o.el.d> d(b bVar, String str) throws IllegalArgumentException {
        g.c();
        Object[] objArr = new Object[1];
        f("怑\ued3a㢪\ue9c0殓量륔쇃勃㴉蔴쐼ࠉ碰鵇啂ή牧宄ご", 20 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("삫衲Ꭼ᰾眪茕았\ue38c\ud99c넏榴郾ﴤ鲭섢촹㱦几酱㓺ཹ䑷装ㆢ꼑犾眃\ue364驪騠", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 30, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
        switch (AnonymousClass4.b[bVar.ordinal()]) {
            case 1:
                return new o.cn.c();
            case 2:
                o.cq.b bVar2 = new o.cq.b();
                int i2 = i + Opcodes.LMUL;
                b = i2 % 128;
                switch (i2 % 2 != 0 ? '\n' : 'c') {
                    case '\n':
                        int i3 = 2 / 0;
                        return bVar2;
                    default:
                        return bVar2;
                }
            case 3:
                return new o.cp.e();
            case 4:
                return new o.cm.b();
            case 5:
                return new o.ck.c();
            case 6:
                return new o.ci.d();
            case 7:
                return new o.cj.e();
            case 8:
                return new o.cl.a();
            case 9:
                return new o.cl.c();
            case 10:
                Object[] objArr3 = new Object[1];
                f("嘿畣\uf3c8궼䜓퀅\uea84샚䧲\uef2e쮦\uec79", Color.red(0) + 12, objArr3);
                if (!str.equals(((String) objArr3[0]).intern())) {
                    Object[] objArr4 = new Object[1];
                    f("嘿畣\uf3c8궼䜓퀅\uea84샚芩杧誆૿ⶥⴖ륱寙", 15 - KeyEvent.getDeadChar(0, 0), objArr4);
                    if (str.equals(((String) objArr4[0]).intern())) {
                        Object[] objArr5 = new Object[1];
                        f("礯殩翂鷬떓Ꝺ歜椆鐿粛岐똺퇜엲", 15 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr5);
                        return new o.ch.b(((String) objArr5[0]).intern());
                    }
                    Object[] objArr6 = new Object[1];
                    f("嫪褗ﻁ蠼ᴵ⒒\uf32a탲\ud99c넏︮郟㝱\ue573蔴쐼Ϋ튨ꦓ혀饗昩䬝\udd07鴚鋗⼏隘溅䈝늗䵋插콺뜰龤곛絮鰷ﻏ\uf1a8ꐗ插콺蟛ᐡ陦桓ⲑ\uec6b㆟연装ㆢ鰷ﻏ\uf1a8ꐗ插콺蟛ᐡ\ue4ae璤鐿粛岐똺퇜엲", TextUtils.indexOf("", "") + 70, objArr6);
                    throw new RuntimeException(((String) objArr6[0]).intern());
                }
                Object[] objArr7 = new Object[1];
                f("礯殩翂鷬떓Ꝺ诰᥍ⲑ\uec6b辗ጥ", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, objArr7);
                o.ch.b bVar3 = new o.ch.b(((String) objArr7[0]).intern());
                int i4 = b + 49;
                i = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return bVar3;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            default:
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr8 = new Object[1];
                f("\uead5ꚹᠿ㞎\ue18d\uf0b4쳦몸㬧哨ᨾ妧蟛ᐡ䰠砦驪騠", 18 - (Process.myPid() >> 22), objArr8);
                throw new UnsupportedOperationException(sb2.append(((String) objArr8[0]).intern()).append(bVar.name()).toString());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0062, code lost:
    
        r8 = 58224;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0067, code lost:
    
        if (r6 >= 16) goto L69;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0069, code lost:
    
        r11 = o.cc.d.$11 + 57;
        o.cc.d.$10 = r11 % 128;
        r11 = r11 % r4;
        r11 = r5[r7];
        r12 = r5[0];
        r9 = (r12 + r8) ^ ((r12 << 4) + ((char) (o.cc.d.e ^ 8439748517800462901L)));
        r10 = r12 >>> 5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x008e, code lost:
    
        r14 = new java.lang.Object[4];
        r14[3] = java.lang.Integer.valueOf(o.cc.d.c);
        r14[r4] = java.lang.Integer.valueOf(r10);
        r14[r7] = java.lang.Integer.valueOf(r9);
        r14[0] = java.lang.Integer.valueOf(r11);
        r9 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00b8, code lost:
    
        if (r9 == null) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00bb, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(12 - (android.os.SystemClock.elapsedRealtime() > 0 ? 1 : (android.os.SystemClock.elapsedRealtime() == 0 ? 0 : -1)), (char) ((-1) - android.widget.ExpandableListView.getPackedPositionChild(0)), 603 - (android.widget.ExpandableListView.getPackedPositionForGroup(0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForGroup(0) == 0 ? 0 : -1)));
        r10 = new java.lang.Class[4];
        r10[0] = java.lang.Integer.TYPE;
        r10[r7] = java.lang.Integer.TYPE;
        r10[r4] = java.lang.Integer.TYPE;
        r10[3] = java.lang.Integer.TYPE;
        r9 = r9.getMethod("C", r10);
        o.e.a.s.put(-1512468642, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00fd, code lost:
    
        r4 = ((java.lang.Character) ((java.lang.reflect.Method) r9).invoke(null, r14)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x010a, code lost:
    
        r5[r7] = r4;
        r20 = r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0122, code lost:
    
        r10 = new java.lang.Object[]{java.lang.Integer.valueOf(r5[0]), java.lang.Integer.valueOf((r4 + r8) ^ ((r4 << 4) + ((char) (o.cc.d.d ^ 8439748517800462901L)))), java.lang.Integer.valueOf(r4 >>> 5), java.lang.Integer.valueOf(o.cc.d.a)};
        r4 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x014b, code lost:
    
        if (r4 == null) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x019e, code lost:
    
        r5[0] = ((java.lang.Character) ((java.lang.reflect.Method) r4).invoke(null, r10)).charValue();
        r8 = r20 - 40503;
        r6 = r6 + 1;
        r4 = 2;
        r7 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x014e, code lost:
    
        r4 = ((java.lang.Class) o.e.a.c(android.view.KeyEvent.keyCodeFromString("") + 11, (char) (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), 603 - (android.view.ViewConfiguration.getTouchSlop() >> 8))).getMethod("C", java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(-1512468642, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x01ab, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x01ac, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x01b0, code lost:
    
        if (r1 != null) goto L41;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x01b2, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x01b3, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x01b4, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x01b5, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x01b9, code lost:
    
        if (r1 != null) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x01bb, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x01bc, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x01bd, code lost:
    
        r2[r1.b] = r5[0];
        r2[r1.b + 1] = r5[1];
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x01cb, code lost:
    
        r4 = new java.lang.Object[]{r1, r1};
        r6 = o.e.a.s.get(2062727845);
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x01dc, code lost:
    
        if (r6 == null) goto L52;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x01de, code lost:
    
        r9 = 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x021b, code lost:
    
        ((java.lang.reflect.Method) r6).invoke(null, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x0221, code lost:
    
        r4 = r9;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x01e0, code lost:
    
        r9 = 2;
        r6 = ((java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), (char) (30725 - (android.view.ViewConfiguration.getScrollDefaultDelay() >> 16)), (android.os.Process.myTid() >> 22) + 614)).getMethod("A", java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(2062727845, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x0224, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x0225, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0229, code lost:
    
        if (r1 != null) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x022b, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x022c, code lost:
    
        throw r0;
     */
    /* JADX WARN: Removed duplicated region for block: B:6:0x001f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 580
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.d.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
