package o.i;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Handler;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.nio.ByteBuffer;
import java.util.Arrays;
import kotlin.text.Typography;
import o.ee.o;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\h.smali */
public final class h extends n {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final byte[] b;
    private static char[] c;
    private static long e;
    private static int f;
    private static int j;
    private short a;
    private short d;

    static void d() {
        char[] cArr = new char[1171];
        ByteBuffer.wrap("\u0018\u0099PÐ\u0088CÁË9XríªQãð[\u007f\u0094ªÌ\u0014\u0005\u008a}\u0005¶¦î,'¼\u009f#×h\u0000ÔxM±Ùél\"ï\u009amÓø\u000b\nD\u0080¼\rõ\u0098-&f¯Þ\u0014\u0017´OÝ\u0087IðÖ(U,»dÔ¼_õÓ\rLFâ\u009eq×Îog ³ø\u00181\u0082I\u0002\u0082\u0084Ú)\u0013¥«(ãE4Ü9zq\u0015©\u009eà\u0012\u0018\u008dS#\u008b°Â\u000fz¦µríÙ$C\\Ã\u0097EÏè\u0006d¾éö\u0084!\u001dYÀ\u0090EÈð\u0003\u0016»¯ò<*\u0090eo\u009dÁÔD\füG}ÿô6En\u0011¦\u0096Ñ\u0001\t\u008f@5øµ3%k¦¢DÚ\u0098\u0015UMÛ\u0084q<ÿwe,»dÔ¼_õÓ\rLFâ\u009eq×Îog ³ø\u00181\u0082I\u0002\u0082\u0084Ú)\u0013¥«(ãE4ÜL\u0001\u0085\u0084Ý1\u0016Ú®nçä?\u001cp\u0098\u0088\u000fÁ\u008d\u0019qR¿ê # {Ý³LÄÓ\u001cL,»dÔ¼_õÓ\rLFâ\u009eq×Îog ³ø\u00181\u0082I\u0002\u0082\u0084Ú)\u0013¥«(ãE4ÜL\u0001\u0085\u0084Ý1\u0016Ð®oçê?\u0003p\u009c\u0088\fÁ\u008c\u0019?R\u00adêa#\u009b{Ô³TÄÎ\u001c]UôíT&à~g·\u0090Ï\u001e\u0000\u0084X\u0004\u0091´)7bµº\bòE\u000bÍCD\u0094Ä,aeí½Böæ\u000e\u0004G\u0097\u009f\u0015ÐÓhq,»dÔ¼_õÓ\rLFâ\u009eq×Îog ³ø\u00181\u0082I\u0002\u0082\u0084Ú)\u0013¥«(ãE4ÜL\u0001\u0085\u0084Ý1\u0016Ú®nçä?\u001cp\u0098\u0088\u000fÁ\u008d\u0019qRªê4#ª{Ò³\\ÄÒ\u001cZU±ín&è~}·\u0099ÏY\u0000\u0089X\u0010\u0091³)+b¨º-ò\u0011\u000bÉCH\u0094Ç,veµ½!öû\u000e\u0014G\u008a\u009f\u0004Ð\u009dhq¡\u008bù$2¤JÞ\u0082MÛÄ\u0013d¤ðüw5àMn\u0086\u0094Þ\u0014\u0017\u0084¯\u0007à¥8\u0018qµ\u0089=ÁT\u001aÔRQëÝ#Rtö\u008ctÅç\u001d\u0005«\u007fã\u0010;\u0095r\b\u008a\u008dÁ0\u0019\u00adP$è\u008a'C\u007fÜ¶EÎÌ\u0005\u007f]í\u0094h,èd\u009d³\nËÁ\u0002DZñ\u0091.) `%¸Ý÷\\\u000fÕF\t\u009eüÕxmï¤hü\u00164\u009cC\f\u009b\u008cÒ?j\u00ad¡aù¼0BHØ\u0087FßÌ\u00161®£å!=åu\u0098\u008c\u001bÄÁ\u0013\n«ñâ0:²qi\u0089ßÀV\u0018ÕW\tïò&v~ïµzÍ\u0004\u0005\u0095\\\u0015\u0094\u008c#5,¿dÐ¼UõÈ\rMFð\u009em×äoJ \u0083ø\u001c1\u0085I\f\u0082¿Ú-\u0013¨«(ã]4ÊL\u0001\u0085\u0084Ý1\u0016ö®uçá?\u0014p\u008b\u0088AÁ\u009d\u00199R¸ê/#é{Æ³XÄÍ\u001cEUôím&¡~d·\u0090Ï\u0017\u0000\u0080X\u000e\u0091´)4b¤º'òE\u000b\u0099CT\u0094Ú,peþ½dö©\u000eKGÙ\u009f\u0002Ð\u0086h?¡ªù42¥JÅ\u0082PÛÏ\u0013N¤±üu5èMk\u0086ÑÞ\u001a¼\u0002ôm,æej\u009dõÖ[\u000eÈGkÿÄ0)h´¡-Ù£\u0012HJÍ\u0083X;\u0093sþ¤mÜ¸\u0015*M\u0088\u0086\u0005>Ëw\u0010¯åà`\u0018·Q&\u0089\u008dÂ\u0012z\u008a³\u0019ël#äT}\u008cþÅ\b}Ó¶LîÑ'<_µ\u0090+Èð\u0001R¹Àò]*\u0083, dÂ¼qõØ\rKFã\u009ep×åoY \u0098ø\u00171²I\f\u0082¥Ú\u0010\u0013¯«\u0005ãX4ÛLb\u0085\u0089Ý+\u0016¹®Oçü?\u001dp\u0095\u0088AÁª\u0019\u0007R\u0094êa#º{Å³XÄÕ\u001c\\Uâ, dÂ¼jõÄ\r], dÂ¼qõØ\rKFã\u009ep×åoY \u0098ø\u00171²I\f\u0082¥Ú\u0010\u0013¯«\u0005ãX4ÛLb\u0085\u0089Ý<\u0016¹®dçû?\u0003p\u0096\u0088\u0013ÁÉ\u0019&R±ê(#¥{Ô³\u0019ÄÓ\u001cLUðí}&è~g·\u0096ÏY\u0000\u008dX\u0000\u0091³)yb¢ºiòB\u000bÍC@\u0094Ý,deê,»dÔ¼_õÓ\rLFâ\u009eq×Éop \u0093ø\u000b1\u0088I\r\u0082\u0081Ú0\u0013¯«\u0005ãX4ÛLb\u0085êÝ~\u0016ì®oçý?\u0014p\u008b\u0088AÁÓ\u0019qR\u0097ê4#¥{Ý³\u0019Äâ\u001c\u007fUÜí9&ò~}·\u0090Ï\r\u0000\u0094X\u001a,¨dÅ¼MõÄ\rDFá\u009em×Âof \u0084ø\u00171\u0095\u001ctT\u000e\u008c\u0097Å3=\u008av)®±ç2_·\u0090{ÈÊ\u0001Uyÿ²bêá#X\u009bÐÓ\u0084\u0004\u0016|\u0095µ\u0007í®&1\u009eû×~\u000f\u008b@F¸ÉñA)äbqÚ»\u0013dK\u0003\u0083\u008aô\u0017,\u0096ekÝ±\u0016>N²\u0087OÿÊ0UhÔ¡+\u0019ïRr\u008añÂË;\u0000sÛ¤\u0000\u001c¿U\"\u008d¯Æ&>Ø\u0080ñÈ\u008e\u0010\u0001Y\u0084¡Xêí2h{ Ã=\fÒT[\u009dÙåK.ôvm¿ô\u00078O\u0003\u0098\u0087à\u001e)\u009eq)º¯\u0002pKâ\u0093\u0000õÚ½µe5,¯Ô<\u009f\u0095G5\u000e\u0081¶\u0006yñ!\u007fèå\u0090e[Õ\u0003VÊÔrk:?í\u00ad\u0095.\\¼\u0004\u0015Ï\u008aÕÆ\u009d¼E/\f°ô\"¿\u00adg\t.\u0090\u0096\u0010Yç\u0001aÈë°d{Ï#Rê×RY\u001a Íæµs|ö$$ï\u0095W\u0011\u001e\u0098ÆK\u0089þq}8óà^«Ò\u0013WÚÙ\u0082 \u0013![[\u0083ÈÊW2ÅyJ¡îèwP÷\u009f\u0000Ç\u0086\u000e\fv\u0083½(åµ,0\u0094¾ÜÇ\u000b\u0001s\u0094º\u0011âù)s\u0091ðØ\u007f\u0000\u009dO\u0004·\u009dþQ&ªm.Õ·\u001c7D@\u008cÆûL#ÃjhÒõ\u0019pAþ\u0088\u0007ðÁ?\u000eg\u0098®=\u0016©]y\u0085´ÍÑ4U|Ë«P\u0013©Zg\u0082ðÉt1\u0085x\u0005 \u008aïQWó\u009eaÆü\r\"Ã\u0012\u008b@S\u008a\u001a#â°©\u0018q\u008b8\u001e\u0080¢Oc\u0017ìÞY¦çmY5ÖüUDß\f¯Û0£\u009bj'2\u009eù\nA\u009f\b\u001cÐþ\u009fkgù.söÞ½K\u0005ÕÌ\\\u0094\u0007\\§+.óºº\u0005\u0002\u0086É\u0001\u0091\u0091X\u007f ðïh·÷~DÆÖ\u008dnUÀ\u001d«ä,¬©{3Ã\u0089\u008a\u0016R\u0093\u0019\u001dáä¨Cpî?f\u0087ÏNO\u0016ÊÝF¥9m\u008145ü§K\u0004\u0013\u0096ÚG,åd\u0091¼ZõÔ\r[Fã\u009e|×ïo} £ø\u001c1\u008cI\u0006\u0082¥Ú<\u0013\u008c«(ã_4ØLF\u0085ÌÝ|\u0016ü®oçý?0p\u008d\u0088\u0015Á\u008c\u0019<R©ê5#\u008a{Þ³LÄÏ\u001c]U¬,¿dÐ¼UõÈ\rMFð\u009em×äoJ \u0083ø\u001c1\u0085I\f\u0082¿Ú-\u0013¨«(ã]4ÊL\u0001\u0085\u0084Ý1\u0016õ®hçë?Qp\u009a\u0088AÁ\u0099\u00198R·êa#ª{Ù³\\ÄÂ\u001cBU±í#&¡~l·\u009cÏ\t\u0000\u0095X\u0010\u0091ñ)+b¤º:òA\u000bÖCO\u0094Ú,te¹½göû\u000e\u001eG\u0094\u009fAÐ\u008a4é|\u0086¤\u0003í\u009e\u0015\u001b^¦\u0086;Ï²w\u001c¸ÕàJ)ÓQZ\u009aéÂ{\u000bþ³~û\u000b,\u009cTW\u009dÒÅg\u000e£¶>ÿ½'\u0007hÌ\u0090\u0017ÙÏ\u0001nJáò7;üc\u008f«\nÜ\u0094\u0004\u0014Mçõu>÷f6¯É×Y\u0018Ö@S\u0089î1kz·¢|ê\u0015\u0013\u008a[\u0013\u008c\u009a4)}»¥>î¾\u0016K_Ü\u0087\u0017ÈÏpu¹àáa*öR\u0083\u009a\nÃ\u0093\u000b_¼¥ä6-÷U*\u009eÔÆJ\u000fÅ,¿dÐ¼UõÈ\rMFð\u009em×äoJ \u0083ø\u001c1\u0085I\f\u0082¿Ú-\u0013¨«(ã]4ÊL\u0001\u0085\u0084Ý1\u0016õ®hçë?Qp\u009a\u0088AÁ\u0099\u00198R·êa#ª{Ù³\\ÄÂ\u001cBU±í#&¡~j·\u009eÏ\u000b\u0000\u0093X\f\u0091²)-báº*òC\u000bÜCE\u0094Ì,\u007feí½höè\u000e\u001dG\u008a\u009fAÐ\u0099h#¡¶ù72 JÕ\u0082\\ÛÅ\u0013\t¤óü`5¡M|\u0086\u0082Þ\u001c\u0017\u0093".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1171);
        c = cArr;
        e = 8486742561017455793L;
    }

    static void init$0() {
        $$a = new byte[]{124, 92, -85, -9};
        $$b = 57;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r9 = r9 + 102
            byte[] r0 = o.i.h.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r8 = r8 + r10
            int r9 = r9 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.h.q(int, int, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        d();
        TextUtils.lastIndexOf("", '0', 0);
        ViewConfiguration.getLongPressTimeout();
        View.combineMeasuredStates(0, 0);
        KeyEvent.normalizeMetaState(0);
        PointF.length(0.0f, 0.0f);
        View.resolveSize(0, 0);
        TextUtils.lastIndexOf("", '0');
        ViewConfiguration.getMaximumDrawingCacheSize();
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        Drawable.resolveOpacity(0, 0);
        View.getDefaultSize(0, 0);
        ViewConfiguration.getEdgeSlop();
        b = new byte[]{1};
        int i = j + 89;
        f = i % 128;
        int i2 = i % 2;
    }

    public h() {
        super(f.e);
    }

    @Override // o.i.g
    final boolean e(Context context, boolean z, o.bb.d dVar) {
        Object obj;
        Object[] objArr = new Object[1];
        p((char) (13337 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), Process.myPid() >> 22, 36 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        p((char) TextUtils.indexOf("", "", 0), View.resolveSizeAndState(0, 0, 0) + 37, View.MeasureSpec.getMode(0) + 19, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        short s = this.a;
        c(context, z);
        if (!e(i.a)) {
            int i = j + 13;
            f = i % 128;
            if (i % 2 == 0) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                p((char) (20393 << View.MeasureSpec.getSize(0)), 85 / ExpandableListView.getPackedPositionChild(0L), View.resolveSizeAndState(1, 0, 1) + 108, objArr3);
                obj = objArr3[0];
            } else {
                o.ee.g.c();
                Object[] objArr4 = new Object[1];
                p((char) (View.MeasureSpec.getSize(0) + 5569), ExpandableListView.getPackedPositionChild(0L) + 57, 48 - View.resolveSizeAndState(0, 0, 0), objArr4);
                obj = objArr4[0];
            }
            o.ee.g.d(intern, ((String) obj).intern());
            return false;
        }
        switch (!dVar.b()) {
            case true:
                int i2 = f + 37;
                j = i2 % 128;
                int i3 = i2 % 2;
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                p((char) (ViewConfiguration.getTapTimeout() >> 16), TextUtils.getOffsetBefore("", 0) + 104, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 36, objArr5);
                o.ee.g.d(intern, ((String) objArr5[0]).intern());
                if (o.a.c(dVar.d(), o.bb.a.al, o.bb.a.ag, o.bb.a.aA)) {
                    this.d = (short) (this.d + 1);
                    o.ee.g.c();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr6 = new Object[1];
                    p((char) View.MeasureSpec.getMode(0), View.getDefaultSize(0, 0) + Opcodes.F2D, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 61, objArr6);
                    o.ee.g.d(intern, sb.append(((String) objArr6[0]).intern()).append((int) this.d).toString());
                    return true;
                }
                break;
            default:
                if (dVar.a().f() != null && dVar.a().f().contains(f.e)) {
                    o.ee.g.c();
                    Object[] objArr7 = new Object[1];
                    p((char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 203, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 90, objArr7);
                    o.ee.g.d(intern, ((String) objArr7[0]).intern());
                    short s2 = this.d;
                    this.d = (short) 0;
                    if (s2 == 0) {
                        int i4 = f + 9;
                        j = i4 % 128;
                        switch (i4 % 2 != 0 ? (char) 28 : (char) 27) {
                            case 27:
                                switch (s == this.a) {
                                }
                            default:
                                throw null;
                        }
                    }
                    return true;
                }
                break;
        }
        return super.e(context, z, dVar);
    }

    @Override // o.i.g
    public final void c(Context context, boolean z, o.eg.b bVar) {
        int i = j + 59;
        f = i % 128;
        int i2 = i % 2;
        super.c(context, z, bVar);
        c(context, z);
        b(bVar);
        int i3 = j + 27;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.i.n
    public final short e() {
        int i = j;
        int i2 = i + Opcodes.LSHR;
        f = i2 % 128;
        int i3 = i2 % 2;
        short s = (short) (this.d + this.a);
        int i4 = i + 7;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 16 : '(') {
            case '(':
                return s;
            default:
                throw null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void b(o.g.a aVar) {
        int i = f + 63;
        j = i % 128;
        switch (i % 2 != 0) {
            case true:
                aVar.c(o.g.b.a);
                throw null;
            default:
                aVar.c(o.g.b.a);
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void c(o.g.a aVar) {
        int i = f + 15;
        j = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                aVar.c(o.g.b.e);
                obj.hashCode();
                throw null;
            default:
                aVar.c(o.g.b.e);
                int i2 = f + 11;
                j = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return;
                }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void c(o.g.a aVar, o.f.e eVar) {
        int i = f + Opcodes.LSHL;
        j = i % 128;
        char c2 = i % 2 != 0 ? (char) 1 : (char) 11;
        aVar.d(eVar);
        switch (c2) {
            case 1:
                int i2 = 48 / 0;
                return;
            default:
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(Context context, final o.f.e eVar, Handler handler, final o.g.a aVar, b bVar) {
        byte[] b2 = new o.dd.e(context).b(eVar.e());
        short s = this.d;
        short s2 = this.a;
        e(new o.dd.e(context));
        short s3 = this.a;
        switch (b2 == null) {
            case true:
                break;
            default:
                switch (b2.length == 0 ? 'F' : 'a') {
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        break;
                    default:
                        if (Arrays.equals(b2, b)) {
                            o.ee.g.c();
                            Object[] objArr = new Object[1];
                            p((char) (ImageFormat.getBitsPerPixel(0) + 13337), View.combineMeasuredStates(0, 0), TextUtils.indexOf("", "", 0) + 37, objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            p((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 6230), 1018 - MotionEvent.axisFromString(""), 76 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
                            o.ee.g.d(intern, ((String) objArr2[0]).intern());
                            handler.post(new Runnable() { // from class: o.i.h$$ExternalSyntheticLambda2
                                @Override // java.lang.Runnable
                                public final void run() {
                                    h.c(o.g.a.this);
                                }
                            });
                            if (s2 != s3) {
                                new o.cd.e();
                                o.cd.e.a(context).e(context);
                                return;
                            }
                            return;
                        }
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        p((char) (TextUtils.indexOf("", "", 0) + 13336), (-1) - MotionEvent.axisFromString(""), 37 - TextUtils.indexOf("", ""), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        p((char) (AndroidCharacter.getMirror('0') - '0'), TextUtils.lastIndexOf("", '0') + 1096, 76 - TextUtils.indexOf("", "", 0, 0), objArr4);
                        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                        switch (e(i.a) ? false : true) {
                            case true:
                                break;
                            default:
                                this.d = (short) 0;
                                bVar.a(context, a_());
                                int i = f + 51;
                                j = i % 128;
                                int i2 = i % 2;
                                break;
                        }
                        handler.post(new Runnable() { // from class: o.i.h$$ExternalSyntheticLambda3
                            @Override // java.lang.Runnable
                            public final void run() {
                                h.c(o.g.a.this, eVar);
                            }
                        });
                        switch (s == this.d ? 'A' : Typography.dollar) {
                            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                int i3 = f + 29;
                                j = i3 % 128;
                                if (i3 % 2 != 0) {
                                    throw null;
                                }
                                if (s2 == s3) {
                                    return;
                                }
                                break;
                        }
                        new o.cd.e();
                        o.cd.e.a(context).e(context);
                        return;
                }
        }
        o.ee.g.c();
        Object[] objArr5 = new Object[1];
        p((char) (Color.alpha(0) + 13336), KeyEvent.keyCodeFromString(""), 37 - (Process.myPid() >> 22), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        p((char) View.resolveSizeAndState(0, 0, 0), 958 - ((Process.getThreadPriority(0) + 20) >> 6), 61 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr6);
        o.ee.g.e(intern3, ((String) objArr6[0]).intern());
        handler.post(new Runnable() { // from class: o.i.h$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                h.b(o.g.a.this);
            }
        });
    }

    /* JADX WARN: Code restructure failed: missing block: B:18:0x0027, code lost:
    
        if (r15 != false) goto L17;
     */
    @Override // o.i.g
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(final android.content.Context r11, final o.f.e r12, final o.g.a r13, final o.i.b r14, boolean r15) {
        /*
            Method dump skipped, instructions count: 258
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.h.d(android.content.Context, o.f.e, o.g.a, o.i.b, boolean):void");
    }

    private void c(Context context, boolean z) {
        int i = j + Opcodes.LMUL;
        f = i % 128;
        int i2 = i % 2;
        switch (a(context) ? ' ' : 'U') {
            case Opcodes.CASTORE /* 85 */:
                d(c.a);
                break;
            default:
                int i3 = j + Opcodes.DMUL;
                int i4 = i3 % 128;
                f = i4;
                if (i3 % 2 == 0) {
                }
                switch (!z) {
                    case true:
                        d(c.a);
                        break;
                    default:
                        int i5 = i4 + Opcodes.DDIV;
                        j = i5 % 128;
                        if (i5 % 2 != 0) {
                        }
                        d(c.c);
                        break;
                }
                e(new o.dd.e(context));
                int i6 = j + 13;
                f = i6 % 128;
                int i7 = i6 % 2;
                break;
        }
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 13336), '0' - AndroidCharacter.getMirror('0'), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 37, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((char) (Gravity.getAbsoluteGravity(0, 0) + 37049), 438 - TextUtils.indexOf("", ""), (Process.myPid() >> 22) + 49, objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
    }

    private boolean a(Context context) {
        try {
            String c2 = new o.dd.e(context).c(o());
            switch (c2 != null) {
                case false:
                    o.ee.g.c();
                    Object[] objArr = new Object[1];
                    p((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 13337), Color.alpha(0), 37 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    p((char) View.resolveSize(0, 0), TextUtils.indexOf((CharSequence) "", '0') + 488, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 37, objArr2);
                    o.ee.g.d(intern, ((String) objArr2[0]).intern());
                    int i = j + 109;
                    f = i % 128;
                    int i2 = i % 2;
                    return false;
                default:
                    o.eg.b bVar = new o.eg.b(c2);
                    Object[] objArr3 = new Object[1];
                    p((char) ((-1) - ImageFormat.getBitsPerPixel(0)), 525 - (Process.myPid() >> 22), 4 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr3);
                    boolean booleanValue = bVar.g(((String) objArr3[0]).intern()).booleanValue();
                    int i3 = f + 79;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                    return booleanValue;
            }
        } catch (o.eg.d e2) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            p((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 13336), ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 37, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            p((char) View.getDefaultSize(0, 0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 530, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 55, objArr5);
            o.ee.g.e(intern2, ((String) objArr5[0]).intern());
            return false;
        }
    }

    private void e(o.dd.e eVar) {
        try {
            String c2 = eVar.c(o());
            switch (c2 == null ? 'B' : Typography.less) {
                case '<':
                    o.eg.b bVar = new o.eg.b(c2);
                    Object[] objArr = new Object[1];
                    p((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 629 - TextUtils.lastIndexOf("", '0'), (Process.myPid() >> 22) + 12, objArr);
                    short shortValue = bVar.k(((String) objArr[0]).intern()).shortValue();
                    switch (shortValue != -1 ? (char) 16 : Typography.quote) {
                        case 16:
                            this.a = shortValue;
                            break;
                    }
                default:
                    int i = f + 75;
                    j = i % 128;
                    if (i % 2 != 0) {
                    }
                    o.ee.g.c();
                    Object[] objArr2 = new Object[1];
                    p((char) (13336 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), Color.blue(0), 'U' - AndroidCharacter.getMirror('0'), objArr2);
                    String intern = ((String) objArr2[0]).intern();
                    Object[] objArr3 = new Object[1];
                    p((char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 585 - ExpandableListView.getPackedPositionType(0L), (KeyEvent.getMaxKeyCode() >> 16) + 45, objArr3);
                    o.ee.g.e(intern, ((String) objArr3[0]).intern());
                    int i2 = f + 91;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    break;
            }
        } catch (o.eg.d e2) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            p((char) (13336 - (ViewConfiguration.getEdgeSlop() >> 16)), ViewConfiguration.getPressedStateDuration() >> 16, (ViewConfiguration.getWindowTouchSlop() >> 8) + 37, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            p((char) (12506 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), TextUtils.getOffsetBefore("", 0) + 642, TextUtils.indexOf("", "", 0, 0) + 58, objArr5);
            o.ee.g.e(intern2, ((String) objArr5[0]).intern());
        }
    }

    private void b(o.eg.b bVar) {
        switch (bVar != null) {
            case true:
                int i = f + 89;
                j = i % 128;
                Object obj = null;
                if (i % 2 != 0) {
                    bVar.d();
                    throw null;
                }
                if (bVar.d() == 0) {
                    return;
                }
                o.ee.g.c();
                Object[] objArr = new Object[1];
                p((char) (TextUtils.lastIndexOf("", '0', 0) + 13337), 1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 36 - TextUtils.lastIndexOf("", '0', 0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                p((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 44113), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 701, TextUtils.indexOf("", "") + 26, objArr2);
                o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar.b()).toString());
                Object[] objArr3 = new Object[1];
                p((char) (Process.getGidForName("") + 55650), 726 - Color.red(0), 22 - ImageFormat.getBitsPerPixel(0), objArr3);
                this.d = bVar.d(((String) objArr3[0]).intern(), (Short) 0).shortValue();
                int i2 = j + 59;
                f = i2 % 128;
                switch (i2 % 2 != 0 ? 'K' : (char) 0) {
                    case 'K':
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x0052. Please report as an issue. */
    @Override // o.i.g
    protected final o.eg.b a_() {
        int i = f + 75;
        j = i % 128;
        int i2 = i % 2;
        o.eg.b a_ = super.a_();
        try {
            Object[] objArr = new Object[1];
            p((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 55648), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 725, 24 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
            a_.d(((String) objArr[0]).intern(), (int) this.d);
            int i3 = f + 75;
            j = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 16 : (char) 17) {
            }
            o.ee.g.c();
            Object[] objArr2 = new Object[1];
            p((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 13336), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, 37 - View.combineMeasuredStates(0, 0), objArr2);
            String intern = ((String) objArr2[0]).intern();
            Object[] objArr3 = new Object[1];
            p((char) (ExpandableListView.getPackedPositionGroup(0L) + 16280), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 783, 65 - View.MeasureSpec.getSize(0), objArr3);
            o.ee.g.d(intern, String.format(((String) objArr3[0]).intern(), a_));
            return a_;
        } catch (o.eg.d e2) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            p((char) (13336 - (Process.myPid() >> 22)), Process.getGidForName("") + 1, Drawable.resolveOpacity(0, 0) + 37, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            p((char) (View.MeasureSpec.getMode(0) + 63871), 749 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 35, objArr5);
            o.ee.g.a(intern2, ((String) objArr5[0]).intern(), e2);
            return null;
        }
    }

    @Override // o.i.n
    public final String toString() {
        StringBuilder append = new StringBuilder().append(super.toString());
        Object[] objArr = new Object[1];
        p((char) (61434 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 848 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 71 - MotionEvent.axisFromString(""), objArr);
        StringBuilder append2 = append.append(((String) objArr[0]).intern()).append((int) this.a);
        Object[] objArr2 = new Object[1];
        p((char) View.resolveSize(0, 0), 921 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 37, objArr2);
        String obj = append2.append(((String) objArr2[0]).intern()).append((int) this.d).append('}').toString();
        int i = j + 71;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                return obj;
            default:
                int i2 = 16 / 0;
                return obj;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(char r23, int r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 980
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.h.p(char, int, int, java.lang.Object[]):void");
    }
}
