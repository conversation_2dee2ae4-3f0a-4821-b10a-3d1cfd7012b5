package o.bi;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Locale;
import o.bl.b;
import o.bl.i;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static e b;
    private static final Object d;
    private static long e;
    private static char f;
    private static long g;
    private static int h;
    private static int i;
    private static int j;
    private final o.bl.a[] a;
    private final b c;

    static void h() {
        e = -2399447513035073065L;
        f = (char) 39673;
        h = 161105445;
        g = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{43, 59, -40, 18};
        $$b = 42;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r8 = 106 - r8
            int r7 = r7 + 4
            byte[] r0 = o.bi.e.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.m(int, byte, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        h();
        ViewConfiguration.getMinimumFlingVelocity();
        d = new Object();
        int i2 = j + 9;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public static e d(Context context) {
        e eVar;
        synchronized (d) {
            if (b == null) {
                b = new e(context);
            }
            eVar = b;
        }
        return eVar;
    }

    private e(Context context) {
        b bVar = new b(context);
        this.c = bVar;
        this.a = new o.bl.a[]{new o.bl.e(), new o.bl.d(context), new o.bl.c(context), bVar, new i()};
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.lang.String a() throws o.bi.c {
        /*
            r15 = this;
            int r0 = o.bi.e.j
            int r0 = r0 + 109
            int r1 = r0 % 128
            o.bi.e.i = r1
            int r0 = r0 % 2
            o.bl.a[] r0 = r15.a
            int r1 = r0.length
            r2 = 0
            r3 = r2
        Lf:
            r4 = 1
            if (r3 >= r1) goto L14
            r5 = r4
            goto L15
        L14:
            r5 = r2
        L15:
            java.lang.String r6 = "⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌"
            java.lang.String r7 = ""
            switch(r5) {
                case 0: goto L25;
                default: goto L1c;
            }
        L1c:
            r5 = r0[r3]
            java.lang.String r8 = r5.e()
            if (r8 == 0) goto L6b
            goto L69
        L25:
            o.ee.g.c()
            int r0 = android.os.Process.getGidForName(r7)
            int r0 = (-1) - r0
            java.lang.Object[] r1 = new java.lang.Object[r4]
            k(r6, r0, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r1 = android.view.ViewConfiguration.getScrollDefaultDelay()
            int r5 = r1 >> 16
            java.lang.String r6 = "\uf14b몞霶従㗉ꭿ܀堾䪶╯왰㙧⽓욬졥㧟뒕놤\ued8f蚠ሱ\uf8f2ჴ\uf6df鷺⿌茤罯"
            int r1 = android.view.ViewConfiguration.getLongPressTimeout()
            int r1 = r1 >> 16
            int r1 = 26964 - r1
            char r7 = (char) r1
            java.lang.String r8 = "䨒쩍吩ࡩ"
            java.lang.String r9 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r1 = new java.lang.Object[r4]
            r10 = r1
            l(r5, r6, r7, r8, r9, r10)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.e(r0, r1)
            o.bi.c r0 = new o.bi.c
            o.bi.c$b r1 = o.bi.c.b.e
            r0.<init>(r1)
            throw r0
        L69:
            r9 = r2
            goto L6c
        L6b:
            r9 = r4
        L6c:
            switch(r9) {
                case 1: goto L7c;
                default: goto L6f;
            }
        L6f:
            int r0 = o.bi.e.j
            int r0 = r0 + 93
            int r1 = r0 % 128
            o.bi.e.i = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L7f
            goto L7f
        L7c:
            int r3 = r3 + 1
            goto Lf
        L7f:
            o.ee.g.c()
            int r0 = android.text.TextUtils.indexOf(r7, r7, r2)
            java.lang.Object[] r1 = new java.lang.Object[r4]
            k(r6, r0, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.util.Locale r1 = java.util.Locale.getDefault()
            r3 = 0
            float r6 = android.util.TypedValue.complexToFraction(r2, r3, r3)
            int r3 = (r6 > r3 ? 1 : (r6 == r3 ? 0 : -1))
            r6 = -1275724084(0xffffffffb3f5fecc, float:-1.1455049E-7)
            int r9 = r6 - r3
            java.lang.String r10 = "汻ㄚ鱋鄐劊郠⭠罅륉育꾧ᠦ\ud853꿘矷钘췲ȫ鮜헲퀍\ue7d5簒献푫껴畽ﯶ\u16fe\uf2f2烗ḃ\uf710埪덑밎㏟⼇䤴㇉刏嶈퍷\uea76┶\ue6da桸鬈鍔寙鲙뒟⪀戤䊽\udfa3蝿ジᬤ툞躀䑋䉜∈⬌㉲퇦㕀鎓\u2fd8闕"
            int r3 = android.os.Process.myTid()
            int r3 = r3 >> 22
            int r3 = r3 + 16642
            char r11 = (char) r3
            java.lang.String r12 = "찴\uf5feʳ\ufd41"
            java.lang.String r13 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r3 = new java.lang.Object[r4]
            r14 = r3
            l(r9, r10, r11, r12, r13, r14)
            r2 = r3[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r3 = r5.d()
            java.lang.Object[] r3 = new java.lang.Object[]{r8, r3}
            java.lang.String r1 = java.lang.String.format(r1, r2, r3)
            o.ee.g.d(r0, r1)
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.a():java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x002b. Please report as an issue. */
    public final java.lang.String e() throws o.bi.c {
        /*
            Method dump skipped, instructions count: 258
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.e():java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.bi.a d() {
        /*
            r15 = this;
            int r0 = o.bi.e.i
            int r0 = r0 + 49
            int r1 = r0 % 128
            o.bi.e.j = r1
            int r0 = r0 % 2
            o.bl.a[] r0 = r15.a
            int r1 = r0.length
            r2 = 0
            r3 = r2
        Lf:
            if (r3 >= r1) goto L14
            r4 = 61
            goto L16
        L14:
            r4 = 35
        L16:
            java.lang.String r5 = "⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌"
            r6 = 1
            switch(r4) {
                case 35: goto L2a;
                default: goto L1c;
            }
        L1c:
            int r4 = o.bi.e.i
            r7 = 19
            int r4 = r4 + r7
            int r8 = r4 % 128
            o.bi.e.j = r8
            int r4 = r4 % 2
            if (r4 == 0) goto L69
            goto L69
        L2a:
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r0 = r0 >> 16
            java.lang.Object[] r1 = new java.lang.Object[r6]
            k(r5, r0, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r1 = -1118191024(0xffffffffbd59c250, float:-0.053163826)
            int r3 = android.view.View.MeasureSpec.getSize(r2)
            int r7 = r3 + r1
            java.lang.String r8 = "用㕍粂㹛㕆胄샩俲丞⟡湭\uef1e浰榦\udb41ꕅ쪎鄜\u1ac9\ue405櫹\uf2b0\uf39f绲檬\ue70b殅ੂ撮밀勠鵩瘳裋ꠂᎺ遟鼈翳萍踇丄ꅿ裰酺誴푧\ue909\udad3ⲭᮩ"
            int r1 = android.view.ViewConfiguration.getKeyRepeatTimeout()
            int r1 = r1 >> 16
            char r9 = (char) r1
            java.lang.String r10 = "傩姂䶽詇"
            java.lang.String r11 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r1 = new java.lang.Object[r6]
            r12 = r1
            l(r7, r8, r9, r10, r11, r12)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            r0 = 0
            return r0
        L69:
            r4 = r0[r3]
            o.bi.a r8 = r4.a()
            if (r8 == 0) goto L72
            goto L74
        L72:
            r7 = 98
        L74:
            switch(r7) {
                case 98: goto L84;
                default: goto L77;
            }
        L77:
            int r0 = o.bi.e.i
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.bi.e.j = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L87
            goto L87
        L84:
            int r3 = r3 + 1
            goto Lf
        L87:
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getScrollBarSize()
            int r0 = r0 >> 8
            java.lang.Object[] r1 = new java.lang.Object[r6]
            k(r5, r0, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.util.Locale r1 = java.util.Locale.getDefault()
            int r3 = android.os.Process.myPid()
            int r9 = r3 >> 22
            java.lang.String r10 = "\udabb쉠⟽㾛ᯄ㕰瘱끇\ue406뚊㸾p\uec98Ꮉ…簈㤿\ue318ผ쁖ꏑ榴퓥龀剧翱胙ⶾ䁑⥈潺ૹ㑋푇퇺ژ\ued54뼇踌㳐硃섽釵Ḵ\uf381̹넒\udadb\uf483े\ueffb괧\uf89c癝ꦮ䂸\uf6cc껀盰풹烒疸\uf7ac핉踯\ud9bf먨簈휥袺곍ꬭ\ud859৳"
            int r3 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r3 = r3 >> 16
            char r11 = (char) r3
            java.lang.String r12 = "加\u180f紌诀"
            java.lang.String r13 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r3 = new java.lang.Object[r6]
            r14 = r3
            l(r9, r10, r11, r12, r13, r14)
            r2 = r3[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r3 = r4.d()
            java.lang.Object[] r3 = new java.lang.Object[]{r3}
            java.lang.String r1 = java.lang.String.format(r1, r2, r3)
            o.ee.g.d(r0, r1)
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.d():o.bi.a");
    }

    public final d c() {
        o.bl.a[] aVarArr = this.a;
        int length = aVarArr.length;
        int i2 = 0;
        while (true) {
            Object obj = null;
            if (i2 >= length) {
                g.c();
                Object[] objArr = new Object[1];
                k("⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌", 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k("剎⠿⊇利⨱\uf152郣ꅡ㘃ᕤ㓴앦驠褑墾礎繛ⴥﲖ鴽슧䇘恹ㆉꛃ\ue5b7葙嗦મᦔ⠸즇\ueec8붾䰐涼猼퉖\uf1e3虀휁癹ᖗ㩭뭫\uea11릾帇Ὃำ", ExpandableListView.getPackedPositionChild(0L) + 1, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                return null;
            }
            int i3 = i + 15;
            j = i3 % 128;
            if (i3 % 2 != 0) {
                aVarArr[i2].c();
                obj.hashCode();
                throw null;
            }
            o.bl.a aVar = aVarArr[i2];
            d c = aVar.c();
            switch (c != null) {
                case false:
                    i2++;
                default:
                    int i4 = j + Opcodes.LMUL;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k("⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌", Color.red(0), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Locale locale = Locale.getDefault();
                    Object[] objArr4 = new Object[1];
                    l(1642874621 - TextUtils.getOffsetBefore("", 0), "⣄焊\uf4bf玣ⱞ\ue539⧬ℭ\ue85f廧츷戗ᔠ嫫븓䶰㐊鍖\ue87a泔襨\ue380쒯톎췜\ud969컫煫㕚ꅪ⺦ჺ廴וֹ\ue235鐮\uf308\uf451홴Բ晵∺奮㓬\uf5aeↀ찒鍦ꆳ肖农ଘ蹌꣄ꗪ柃먹湾姡㨚ᚕ띺抩\uf196䇯硊ꮓꍡ\ue39e", (char) (7438 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), "ﴂ\uec46\u0e61묝", "\u0000\u0000\u0000\u0000", objArr4);
                    g.d(intern2, String.format(locale, ((String) objArr4[0]).intern(), aVar.d()));
                    return c;
            }
        }
    }

    public final b b() {
        int i2 = i;
        int i3 = i2 + 83;
        j = i3 % 128;
        int i4 = i3 % 2;
        b bVar = this.c;
        int i5 = i2 + Opcodes.LNEG;
        j = i5 % 128;
        int i6 = i5 % 2;
        return bVar;
    }

    public final boolean f() {
        if (this.c.j() == null) {
            g.c();
            Object[] objArr = new Object[1];
            k("⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌", ExpandableListView.getPackedPositionChild(0L) + 1, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l(View.getDefaultSize(0, 0), "̀☤꿑핏\uebcc왮펣\ud98c裞\uee75厕\ueafc\uf30aꇚ䖔初莸⫖ﮓ㚸︿얜\ue442멖䒣쓊澓不ꑻ稳晛⅒ϩ䒭\ue686昭즄狨歏崌梎⯶\u1cce嫦ꂈﮈ⍬˂\uf19c᠇", (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "䮣곔參쮩", "\u0000\u0000\u0000\u0000", objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            return false;
        }
        g.c();
        Object[] objArr3 = new Object[1];
        k("⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌", Process.getGidForName("") + 1, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        l(KeyEvent.getDeadChar(0, 0), "\ue46d⍯獄漛㶪䓻鶗阐㑩ࢪ蒳쟖罥뎌슭ꃉ쨮墰畢桼\uebd5\uea91阻䆩붳窱狞࣓㉛⬩ꆥ￤퇴᯽滖瀖囇\udb80庎䵦⺳쬩\u008b꧖撃렷", (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), "⠺娼결\ue53d", "\u0000\u0000\u0000\u0000", objArr4);
        g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(this.c.j()).toString());
        switch (this.c.j() != b.a.a) {
            case false:
                int i2 = i + 39;
                j = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        int i3 = 64 / 0;
                        return false;
                    default:
                        return false;
                }
            default:
                int i4 = j + 33;
                i = i4 % 128;
                int i5 = i4 % 2;
                return true;
        }
    }

    public final void e(Context context) {
        synchronized (d) {
            g.c();
            Object[] objArr = new Object[1];
            k("⽧㚁\ud9fa⼤팦\uefe6殄塘䬮\u0bce쾿㱬\ue746鞽ꏃ耑ͩ㎤߫搰뾆彮鬏죌", ViewConfiguration.getPressedStateDuration() >> 16, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k("パꐕ댪ゲ鋲統şᦋ咟饾ꕵ綤\uf8f7Դ줝쇟᳃ꄜ洮◣ꀾ췳", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            for (o.bl.a aVar : this.a) {
                aVar.c(context);
            }
            b = null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 396
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.k(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 714
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.e.l(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
