package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP160R2Point.smali */
public class SecP160R2Point extends ECPoint.AbstractFp {
    SecP160R2Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP160R2FieldElement secP160R2FieldElement = (SecP160R2FieldElement) this.b;
        SecP160R2FieldElement secP160R2FieldElement2 = (SecP160R2FieldElement) this.c;
        SecP160R2FieldElement secP160R2FieldElement3 = (SecP160R2FieldElement) eCPoint.getXCoord();
        SecP160R2FieldElement secP160R2FieldElement4 = (SecP160R2FieldElement) eCPoint.getYCoord();
        SecP160R2FieldElement secP160R2FieldElement5 = (SecP160R2FieldElement) this.d[0];
        SecP160R2FieldElement secP160R2FieldElement6 = (SecP160R2FieldElement) eCPoint.getZCoord(0);
        int[] b = t5.b();
        int[] a = t5.a();
        int[] a2 = t5.a();
        int[] a3 = t5.a();
        boolean isOne = secP160R2FieldElement5.isOne();
        if (isOne) {
            iArr = secP160R2FieldElement3.a;
            iArr2 = secP160R2FieldElement4.a;
        } else {
            SecP160R2Field.square(secP160R2FieldElement5.a, a2);
            SecP160R2Field.multiply(a2, secP160R2FieldElement3.a, a);
            SecP160R2Field.multiply(a2, secP160R2FieldElement5.a, a2);
            SecP160R2Field.multiply(a2, secP160R2FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP160R2FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP160R2FieldElement.a;
            iArr4 = secP160R2FieldElement2.a;
        } else {
            SecP160R2Field.square(secP160R2FieldElement6.a, a3);
            SecP160R2Field.multiply(a3, secP160R2FieldElement.a, b);
            SecP160R2Field.multiply(a3, secP160R2FieldElement6.a, a3);
            SecP160R2Field.multiply(a3, secP160R2FieldElement2.a, a3);
            iArr3 = b;
            iArr4 = a3;
        }
        int[] a4 = t5.a();
        SecP160R2Field.subtract(iArr3, iArr, a4);
        SecP160R2Field.subtract(iArr4, iArr2, a);
        if (t5.b(a4)) {
            return t5.b(a) ? twice() : curve.getInfinity();
        }
        SecP160R2Field.square(a4, a2);
        int[] a5 = t5.a();
        SecP160R2Field.multiply(a2, a4, a5);
        SecP160R2Field.multiply(a2, iArr3, a2);
        SecP160R2Field.negate(a5, a5);
        t5.c(iArr4, a5, b);
        SecP160R2Field.reduce32(t5.b(a2, a2, a5), a5);
        SecP160R2FieldElement secP160R2FieldElement7 = new SecP160R2FieldElement(a3);
        SecP160R2Field.square(a, secP160R2FieldElement7.a);
        int[] iArr5 = secP160R2FieldElement7.a;
        SecP160R2Field.subtract(iArr5, a5, iArr5);
        SecP160R2FieldElement secP160R2FieldElement8 = new SecP160R2FieldElement(a5);
        SecP160R2Field.subtract(a2, secP160R2FieldElement7.a, secP160R2FieldElement8.a);
        SecP160R2Field.multiplyAddToExt(secP160R2FieldElement8.a, a, b);
        SecP160R2Field.reduce(b, secP160R2FieldElement8.a);
        SecP160R2FieldElement secP160R2FieldElement9 = new SecP160R2FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP160R2FieldElement9.a;
            SecP160R2Field.multiply(iArr6, secP160R2FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP160R2FieldElement9.a;
            SecP160R2Field.multiply(iArr7, secP160R2FieldElement6.a, iArr7);
        }
        return new SecP160R2Point(curve, secP160R2FieldElement7, secP160R2FieldElement8, new ECFieldElement[]{secP160R2FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP160R2Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP160R2Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP160R2FieldElement secP160R2FieldElement = (SecP160R2FieldElement) this.c;
        if (secP160R2FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP160R2FieldElement secP160R2FieldElement2 = (SecP160R2FieldElement) this.b;
        SecP160R2FieldElement secP160R2FieldElement3 = (SecP160R2FieldElement) this.d[0];
        int[] a = t5.a();
        int[] a2 = t5.a();
        int[] a3 = t5.a();
        SecP160R2Field.square(secP160R2FieldElement.a, a3);
        int[] a4 = t5.a();
        SecP160R2Field.square(a3, a4);
        boolean isOne = secP160R2FieldElement3.isOne();
        int[] iArr = secP160R2FieldElement3.a;
        if (!isOne) {
            SecP160R2Field.square(iArr, a2);
            iArr = a2;
        }
        SecP160R2Field.subtract(secP160R2FieldElement2.a, iArr, a);
        SecP160R2Field.add(secP160R2FieldElement2.a, iArr, a2);
        SecP160R2Field.multiply(a2, a, a2);
        SecP160R2Field.reduce32(t5.b(a2, a2, a2), a2);
        SecP160R2Field.multiply(a3, secP160R2FieldElement2.a, a3);
        SecP160R2Field.reduce32(c6.c(5, a3, 2, 0), a3);
        SecP160R2Field.reduce32(c6.a(5, a4, 3, 0, a), a);
        SecP160R2FieldElement secP160R2FieldElement4 = new SecP160R2FieldElement(a4);
        SecP160R2Field.square(a2, secP160R2FieldElement4.a);
        int[] iArr2 = secP160R2FieldElement4.a;
        SecP160R2Field.subtract(iArr2, a3, iArr2);
        int[] iArr3 = secP160R2FieldElement4.a;
        SecP160R2Field.subtract(iArr3, a3, iArr3);
        SecP160R2FieldElement secP160R2FieldElement5 = new SecP160R2FieldElement(a3);
        SecP160R2Field.subtract(a3, secP160R2FieldElement4.a, secP160R2FieldElement5.a);
        int[] iArr4 = secP160R2FieldElement5.a;
        SecP160R2Field.multiply(iArr4, a2, iArr4);
        int[] iArr5 = secP160R2FieldElement5.a;
        SecP160R2Field.subtract(iArr5, a, iArr5);
        SecP160R2FieldElement secP160R2FieldElement6 = new SecP160R2FieldElement(a2);
        SecP160R2Field.twice(secP160R2FieldElement.a, secP160R2FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP160R2FieldElement6.a;
            SecP160R2Field.multiply(iArr6, secP160R2FieldElement3.a, iArr6);
        }
        return new SecP160R2Point(curve, secP160R2FieldElement4, secP160R2FieldElement5, new ECFieldElement[]{secP160R2FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP160R2Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
