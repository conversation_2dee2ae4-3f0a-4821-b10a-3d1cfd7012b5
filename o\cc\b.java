package o.cc;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cc\b.smali */
final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    public static final b e;
    public static final b f;
    public static final b g;
    public static final b h;
    public static final b i;
    public static final b j;
    private static final /* synthetic */ b[] k;
    private static char m;
    private static char n;

    /* renamed from: o, reason: collision with root package name */
    private static char f46o;
    private static int p;
    private static long r;
    private static int s;
    private static char t;
    private final String l;

    static void d() {
        n = (char) 48803;
        f46o = (char) 62095;
        t = (char) 2498;
        m = (char) 58292;
        r = 7073251499047646818L;
    }

    static void init$0() {
        $$a = new byte[]{37, -23, -22, 37};
        $$b = Opcodes.I2C;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r0 = o.cc.b.$$a
            int r6 = r6 + 4
            int r7 = r7 * 2
            int r7 = 114 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L33
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            int r6 = r6 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L33:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.b.v(short, short, short, java.lang.Object[]):void");
    }

    private static /* synthetic */ b[] e() {
        int i2 = s + 65;
        int i3 = i2 % 128;
        p = i3;
        int i4 = i2 % 2;
        b[] bVarArr = {e, c, a, d, b, g, f, h, j, i};
        int i5 = i3 + 83;
        s = i5 % 128;
        int i6 = i5 % 2;
        return bVarArr;
    }

    public static b valueOf(String str) {
        int i2 = p + 77;
        s = i2 % 128;
        char c2 = i2 % 2 == 0 ? '[' : (char) 26;
        b bVar = (b) Enum.valueOf(b.class, str);
        switch (c2) {
            case Opcodes.DUP_X2 /* 91 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i3 = s + 67;
                p = i3 % 128;
                int i4 = i3 % 2;
                return bVar;
        }
    }

    public static b[] values() {
        b[] bVarArr;
        int i2 = s + 87;
        p = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                bVarArr = (b[]) k.clone();
                break;
            default:
                bVarArr = (b[]) k.clone();
                int i3 = 96 / 0;
                break;
        }
        int i4 = s + 93;
        p = i4 % 128;
        int i5 = i4 % 2;
        return bVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        p = 0;
        s = 1;
        d();
        Object[] objArr = new Object[1];
        u("タ枌麿㗛泫茛㨫兊衸₁", ExpandableListView.getPackedPositionType(0L) + 22303, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u("タ僢\uf00b၉뇿턾煅", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 24659, objArr2);
        e = new b(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        u("タ\ue523鯽놞", (ViewConfiguration.getEdgeSlop() >> 16) + 54709, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        q("粖쎥ｰី", 4 - View.MeasureSpec.getMode(0), objArr4);
        c = new b(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        u("イ쀽퇷", (ViewConfiguration.getWindowTouchSlop() >> 8) + 61627, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        q("澼\ue513䞷侜", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 3, objArr6);
        a = new b(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        q("鬩\udf34㙻ӛ", View.combineMeasuredStates(0, 0) + 4, objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        u("ア\u0b46䝢茔\udf29ᯕ埵鎆\uefbf", 15329 - Color.green(0), objArr8);
        d = new b(intern4, 3, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        u("セㆫ㋭㌨㑯㚢㟌㠬㥨㮲", 317 - Gravity.getAbsoluteGravity(0, 0), objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        q("\uec3b\u2e55荹ߝ﹡\uf2a4넻瑾൛䬎㔢⏤戵睥", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 14, objArr10);
        b = new b(intern5, 4, ((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        q("냋≎\ue0e1膜蜠㵝ￌ득ꨵ綞\ueeed娟", 10 - TextUtils.lastIndexOf("", '0'), objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        q("\uec3b\u2e55荹ߝ﹡\uf2a4㰯얡둧뱌讥\ue2fa", 12 - View.resolveSizeAndState(0, 0, 0), objArr12);
        g = new b(intern6, 5, ((String) objArr12[0]).intern());
        Object[] objArr13 = new Object[1];
        u("セ椉莩㱂囧\uf088⤟䏂ﱹᘀ", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 22942, objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        q("섉ᒍ켞똁", 4 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr14);
        f = new b(intern7, 6, ((String) objArr14[0]).intern());
        Object[] objArr15 = new Object[1];
        u("ジퟰ﹒", TextUtils.indexOf((CharSequence) "", '0', 0) + 59234, objArr15);
        String intern8 = ((String) objArr15[0]).intern();
        Object[] objArr16 = new Object[1];
        u("ジ䀔퇺", View.MeasureSpec.makeMeasureSpec(0, 0) + 28837, objArr16);
        h = new b(intern8, 7, ((String) objArr16[0]).intern());
        Object[] objArr17 = new Object[1];
        u("ジ졮셮\uda5d퍡\uec68\ue565﹢\uf764\uf062", MotionEvent.axisFromString("") + 63744, objArr17);
        String intern9 = ((String) objArr17[0]).intern();
        Object[] objArr18 = new Object[1];
        q("蔅䖞國翉ᴍ輽쮑ꐋ疺\uedd4Ꮶᖩ", View.MeasureSpec.getSize(0) + 11, objArr18);
        j = new b(intern9, 8, ((String) objArr18[0]).intern());
        Object[] objArr19 = new Object[1];
        q("㟄М籙줶䐖㲁䤉櫅㖿蟷뢚\ue290", 11 - ExpandableListView.getPackedPositionGroup(0L), objArr19);
        String intern10 = ((String) objArr19[0]).intern();
        Object[] objArr20 = new Object[1];
        q("\uf67c\udd8f\ua633鋫㥟\ue87e鱙鎋䍋ᄶ䫸ﾟ", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 11, objArr20);
        i = new b(intern10, 9, ((String) objArr20[0]).intern());
        k = e();
        int i2 = p + 45;
        s = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                int i3 = 27 / 0;
                return;
            default:
                return;
        }
    }

    private b(String str, int i2, String str2) {
        this.l = str2;
    }

    public final String b() {
        int i2 = s;
        int i3 = i2 + 97;
        p = i3 % 128;
        int i4 = i3 % 2;
        String str = this.l;
        int i5 = i2 + 21;
        p = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.cc.b e(java.lang.String r6) {
        /*
            o.cc.b[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L8:
            r4 = 1
            if (r3 >= r1) goto Ld
            r5 = r2
            goto Le
        Ld:
            r5 = r4
        Le:
            switch(r5) {
                case 0: goto L3f;
                default: goto L11;
            }
        L11:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            int r3 = android.view.ViewConfiguration.getScrollDefaultDelay()
            int r3 = r3 >> 16
            int r3 = 34 - r3
            java.lang.Object[] r4 = new java.lang.Object[r4]
            java.lang.String r5 = "㮸珮툁攢訐쿶ꪾ黖\ue932′볾鍊흏凯ୄꔢꪾ黖帆ላꬋ\uf666\uf0f3롁獺ꕐ鈳姟톟ﰖ볾鍊\udb66れ"
            q(r5, r3, r4)
            r2 = r4[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r6 = r1.append(r6)
            java.lang.String r6 = r6.toString()
            r0.<init>(r6)
            throw r0
        L3f:
            r4 = r0[r3]
            java.lang.String r5 = r4.l
            boolean r5 = r6.equals(r5)
            if (r5 == 0) goto L64
            int r6 = o.cc.b.p
            int r6 = r6 + 125
            int r0 = r6 % 128
            o.cc.b.s = r0
            int r6 = r6 % 2
            if (r6 != 0) goto L58
            r6 = 68
            goto L5a
        L58:
            r6 = 72
        L5a:
            switch(r6) {
                case 68: goto L5e;
                default: goto L5d;
            }
        L5d:
            return r4
        L5e:
            r6 = 83
            int r6 = r6 / r2
            return r4
        L62:
            r6 = move-exception
            throw r6
        L64:
            int r3 = r3 + 1
            int r4 = o.cc.b.p
            int r4 = r4 + 101
            int r5 = r4 % 128
            o.cc.b.s = r5
            int r4 = r4 % 2
            goto L8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.b.e(java.lang.String):o.cc.b");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(java.lang.String r26, int r27, java.lang.Object[] r28) {
        /*
            Method dump skipped, instructions count: 574
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.b.q(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 656
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.b.u(java.lang.String, int, java.lang.Object[]):void");
    }
}
