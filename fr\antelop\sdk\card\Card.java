package fr.antelop.sdk.card;

import android.content.Context;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.card.TermsAndConditions;
import fr.antelop.sdk.card.emvapplication.EmvApplicationGroup;
import fr.antelop.sdk.exception.WalletValidationException;
import java.net.URI;
import java.util.Date;
import java.util.Map;
import o.eo.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali_classes17\fr\antelop\sdk\card\Card.smali */
public final class Card {
    AccountInfo accountInfo;
    private CardDisplay cardDisplay;
    private CardInfo cardInfo;
    private final e innerCard;

    public Card(e eVar) {
        this.innerCard = eVar;
    }

    public final Map<String, EmvApplicationGroup> groups() {
        return this.innerCard.v();
    }

    public final void delete(Context context, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerCard.e(context, antelopCallback);
    }

    public final int countGroups() {
        return this.innerCard.p();
    }

    public final String getDefaultGroupId() {
        return this.innerCard.u();
    }

    public final void setDefaultGroup(String str) throws WalletValidationException {
        this.innerCard.e(str);
    }

    public final String getNextTransactionGroupId() {
        return this.innerCard.r();
    }

    public final void setNextTransactionGroup(String str) throws WalletValidationException {
        this.innerCard.b(str);
    }

    public final void resetNextTransactionGroup() throws WalletValidationException {
        this.innerCard.x();
    }

    @Deprecated
    public final String getBin() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().c();
        }
        return null;
    }

    public final String getLanguages() {
        return this.innerCard.D();
    }

    @Deprecated
    public final Date getExpiryDate() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().b();
        }
        return null;
    }

    public final CardInfo getCardInfo() {
        if (this.innerCard.s() == null) {
            return null;
        }
        if (this.cardInfo == null) {
            this.cardInfo = new CardInfo(this.innerCard.s());
        }
        return this.cardInfo;
    }

    public final AccountInfo getAccountInfo() {
        if (this.innerCard.q() == null) {
            return null;
        }
        if (this.accountInfo == null) {
            this.accountInfo = new AccountInfo(this.innerCard.q());
        }
        return this.accountInfo;
    }

    public final Short getCountryCodeNumber() {
        return this.innerCard.k();
    }

    public final String getId() {
        return this.innerCard.e();
    }

    public final EmvApplicationGroup getGroup(String str) {
        return this.innerCard.d(str);
    }

    @Deprecated
    public final String getLastDigits() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().a();
        }
        return null;
    }

    public final String getIssuerData() {
        return this.innerCard.m();
    }

    @Deprecated
    public final String getIssuerCardId() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().e();
        }
        if (this.innerCard.q() != null) {
            return this.innerCard.q().a();
        }
        if (!this.innerCard.B()) {
            return this.innerCard.n();
        }
        return null;
    }

    public final boolean isProvisioned() {
        return this.innerCard.B();
    }

    public final CardStatus getStatus() {
        return this.innerCard.z();
    }

    public final int getAvailablePaymentKeyNumber() {
        return this.innerCard.C();
    }

    public final TermsAndConditions getTermsAndConditions(Context context) {
        URI b;
        if (this.innerCard.o() == null || (b = this.innerCard.o().b(context)) == null) {
            return null;
        }
        return new TermsAndConditions(this.innerCard.o().e().d(), b, TermsAndConditions.FileType.extractFromExtensionFile(this.innerCard.o().e().c()), this.innerCard);
    }

    @Deprecated
    public final CardDisplay getDisplay() {
        if (this.cardDisplay == null) {
            if (this.innerCard.s() != null) {
                this.cardDisplay = this.innerCard.s().d();
            } else {
                return null;
            }
        }
        return this.cardDisplay;
    }

    public final String toString() {
        return new StringBuilder("Card{id='").append(getId()).append('\'').append(", bin='").append(getBin() == null ? "" : getBin()).append('\'').append(", expiryDate=").append(getExpiryDate() == null ? "" : getExpiryDate()).append(", lastDigits='").append(getLastDigits() == null ? "" : getLastDigits()).append('\'').append(", issuerData='").append(getIssuerData() == null ? "" : getIssuerData()).append('\'').append(", issuerCardId='").append(getIssuerCardId() == null ? "" : getIssuerCardId()).append('\'').append(", languages=").append(getLanguages() == null ? "" : getLanguages()).append(", countryCodeNumber=").append(getCountryCodeNumber() == null ? "" : getCountryCodeNumber()).append(", status=").append(getStatus() != null ? getStatus() : "").append(", availablePaymentKeyNumber=").append(getAvailablePaymentKeyNumber()).append(", cardDisplay").append(getDisplay()).append('}').toString();
    }
}
