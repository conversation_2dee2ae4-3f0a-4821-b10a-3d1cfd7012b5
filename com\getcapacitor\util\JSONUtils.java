package com.getcapacitor.util;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes8\com\getcapacitor\util\JSONUtils.smali */
public class JSONUtils {
    public static String getString(JSONObject jsonObject, String key, String defaultValue) {
        String k = getDeepestKey(key);
        try {
            JSONObject o2 = getDeepestObject(jsonObject, key);
            String value = o2.getString(k);
            if (value == null) {
                return defaultValue;
            }
            return value;
        } catch (JSONException e) {
            return defaultValue;
        }
    }

    public static boolean getBoolean(JSONObject jsonObject, String key, boolean defaultValue) {
        String k = getDeepestKey(key);
        try {
            JSONObject o2 = getDeepestObject(jsonObject, key);
            return o2.getBoolean(k);
        } catch (JSONException e) {
            return defaultValue;
        }
    }

    public static int getInt(JSONObject jsonObject, String key, int defaultValue) {
        String k = getDeepestKey(key);
        try {
            JSONObject o2 = getDeepestObject(jsonObject, key);
            return o2.getInt(k);
        } catch (JSONException e) {
            return defaultValue;
        }
    }

    public static JSONObject getObject(JSONObject jsonObject, String key) {
        String k = getDeepestKey(key);
        try {
            JSONObject o2 = getDeepestObject(jsonObject, key);
            return o2.getJSONObject(k);
        } catch (JSONException e) {
            return null;
        }
    }

    public static String[] getArray(JSONObject jsonObject, String key, String[] defaultValue) {
        String k = getDeepestKey(key);
        try {
            JSONObject o2 = getDeepestObject(jsonObject, key);
            JSONArray a = o2.getJSONArray(k);
            if (a == null) {
                return defaultValue;
            }
            int l = a.length();
            String[] value = new String[l];
            for (int i = 0; i < l; i++) {
                value[i] = (String) a.get(i);
            }
            return value;
        } catch (JSONException e) {
            return defaultValue;
        }
    }

    private static String getDeepestKey(String key) {
        String[] parts = key.split("\\.");
        if (parts.length > 0) {
            return parts[parts.length - 1];
        }
        return null;
    }

    private static JSONObject getDeepestObject(JSONObject jsonObject, String key) throws JSONException {
        String[] parts = key.split("\\.");
        JSONObject o2 = jsonObject;
        for (int i = 0; i < parts.length - 1; i++) {
            String k = parts[i];
            o2 = o2.getJSONObject(k);
        }
        return o2;
    }
}
