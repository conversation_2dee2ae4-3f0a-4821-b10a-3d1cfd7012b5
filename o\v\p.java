package o.v;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.SecurePinInput;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.ai.e;
import o.ed.c;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\p.smali */
public final class p extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int p;
    private static byte[] q;
    private static int r;
    private static short[] s;
    private static int t;
    private static int u;
    int h;
    final String i;
    CustomerAuthenticatedProcessActivityCallback l;
    private final boolean m;

    /* renamed from: o, reason: collision with root package name */
    private SecurePinInput f111o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        u = 1;
        t();
        ExpandableListView.getPackedPositionType(0L);
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        ViewConfiguration.getZoomControlsTimeout();
        TextUtils.getTrimmedLength("");
        TextUtils.indexOf((CharSequence) "", '0', 0);
        int i = r + 31;
        u = i % 128;
        switch (i % 2 == 0 ? (char) 28 : Typography.dollar) {
            case '$':
                return;
            default:
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{114, -113, -41, 111};
        $$e = 203;
    }

    static void t() {
        q = new byte[]{-33, -41, -48, -63, -54, -49, -85, -82, -16, -38, -47, -8, -92, -62, -10, -23, 11, 21, -20, 51, -1, 29, 49, -29, -21, 21, 10, 22, 10, -7, 5, -17, 24, Base64.padSymbol, -17, 29, 17, -29, -20, 49, -15, -24, 18, -48, -49, -4, 73, -59, 27, 58, 39, -32, 52, 22, 52, 11, 7, 46, 45, -60, 6, Base64.padSymbol, -10, 24, -30, -7, -32, 90, -73, -22, -2, 85, -79, -25, -24, -28, -27, -32, -25, 88, -79, -22, -26, 83, UtilitiesSDKConstants.SRP_LABEL_MAC, 19, -5, Tnaf.POW_2_WIDTH, 41, -91, -112, -112, -112, -112, -112, -112};
        t = 909053570;
        p = 843637244;
        k = 1264273399;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(short r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = 110 - r8
            int r9 = r9 * 3
            int r9 = r9 + 1
            byte[] r0 = o.v.p.$$d
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L19:
            r3 = r2
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L33:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.p.w(short, byte, short, java.lang.Object[]):void");
    }

    static /* synthetic */ void c(p pVar) {
        int i = u + 85;
        r = i % 128;
        boolean z = i % 2 == 0;
        Object obj = null;
        pVar.n();
        switch (z) {
            case true:
                int i2 = u + 49;
                r = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public p(String str, o.eo.e eVar, boolean z) {
        super(str, eVar);
        this.i = str;
        this.m = z;
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i = r + 53;
        u = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                v((byte) (ViewConfiguration.getJumpTapTimeout() % 48), (-2104772967) >> (ViewConfiguration.getDoubleTapTimeout() + 21), (short) (28 - (AudioTrack.getMinVolume() > 2.0f ? 1 : (AudioTrack.getMinVolume() == 2.0f ? 0 : -1))), (-1) >>> Process.getGidForName(""), View.MeasureSpec.getSize(0) - 73920284, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                v((byte) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 2104772967, (short) ((-77) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), Process.getGidForName("") - 1, View.MeasureSpec.getSize(0) - 73920284, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = r + 23;
        u = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    @Override // o.p.h
    public final void a(Context context, o.ei.c cVar, final o.h.d dVar) {
        c.C0039c c0039c;
        c.e eVar;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        v((byte) TextUtils.getCapsMode("", 0, 0), (-2104772953) - TextUtils.lastIndexOf("", '0', 0), (short) (TextUtils.lastIndexOf("", '0') + Opcodes.LSHL), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1, TextUtils.lastIndexOf("", '0') - 73920290, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        v((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), (-2104772933) - (ViewConfiguration.getDoubleTapTimeout() >> 16), (short) ((-127) - Color.alpha(0)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 8, AndroidCharacter.getMirror('0') - 61226, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        final o.p.g l = l();
        final o.ai.e eVar2 = new o.ai.e(context, new e.d() { // from class: o.v.p.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int b;
            private static int e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                e = 0;
                b = 1;
                a = 874635472;
            }

            private static void g(int i, short s2, short s3, Object[] objArr3) {
                int i2 = 109 - (i * 2);
                int i3 = s3 + 4;
                int i4 = (s2 * 2) + 1;
                byte[] bArr = $$a;
                byte[] bArr2 = new byte[i4];
                int i5 = -1;
                int i6 = i4 - 1;
                if (bArr == null) {
                    i2 = (-i2) + i6;
                    i6 = i6;
                    i3 = i3;
                    objArr3 = objArr3;
                    bArr = bArr;
                    bArr2 = bArr2;
                    i5 = -1;
                }
                while (true) {
                    int i7 = i5 + 1;
                    int i8 = i3 + 1;
                    bArr2[i7] = (byte) i2;
                    if (i7 == i6) {
                        objArr3[0] = new String(bArr2, 0);
                        return;
                    }
                    int i9 = i6;
                    i2 = (-bArr[i8]) + i2;
                    i6 = i9;
                    i3 = i8;
                    objArr3 = objArr3;
                    bArr = bArr;
                    bArr2 = bArr2;
                    i5 = i7;
                }
            }

            static void init$0() {
                $$a = new byte[]{62, -87, 120, -83};
                $$b = 7;
            }

            @Override // o.ai.e.d
            public final void c() {
                o.ee.g.c();
                boolean z = false;
                Object[] objArr3 = new Object[1];
                f(14 - KeyEvent.normalizeMetaState(0), "\u0000\ufffe\u0010\r\u0000￫\u0004\t\ufff0\u000b\uffff￼\u000f\u0000￤\t\t\u0000\r￮", 20 - Color.blue(0), Color.rgb(0, 0, 0) + 16777492, false, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f(View.getDefaultSize(0, 0) + 10, "�\u000e\u0000\uffef\u0011\uffff\uffff\u0001\u000f\u000f\u000b\n￮\u0001￬\u0005\n\uffdf", 18 - (KeyEvent.getMaxKeyCode() >> 16), 275 - Color.argb(0, 0, 0, 0), false, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                o.p.g gVar = l;
                if (gVar != null) {
                    z = true;
                }
                switch (z) {
                    case false:
                        break;
                    default:
                        int i = b + 53;
                        e = i % 128;
                        int i2 = i % 2;
                        gVar.onProcessSuccess();
                        break;
                }
                int i3 = e + 9;
                b = i3 % 128;
                int i4 = i3 % 2;
            }

            /* JADX WARN: Removed duplicated region for block: B:26:0x00a6  */
            @Override // o.ai.e.d
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void a(o.bb.d r13, int r14) {
                /*
                    Method dump skipped, instructions count: 236
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.p.AnonymousClass4.a(o.bb.d, int):void");
            }

            private static void f(int i, String str, int i2, int i3, boolean z, Object[] objArr3) {
                char[] cArr;
                switch (str != null ? '*' : 'I') {
                    case 'I':
                        cArr = str;
                        break;
                    default:
                        cArr = str.toCharArray();
                        break;
                }
                char[] cArr2 = cArr;
                o.a.h hVar = new o.a.h();
                char[] cArr3 = new char[i2];
                hVar.a = 0;
                while (hVar.a < i2) {
                    int i4 = $11 + 7;
                    $10 = i4 % 128;
                    int i5 = i4 % 2;
                    hVar.b = cArr2[hVar.a];
                    cArr3[hVar.a] = (char) (i3 + hVar.b);
                    int i6 = hVar.a;
                    try {
                        Object[] objArr4 = {Integer.valueOf(cArr3[i6]), Integer.valueOf(a)};
                        Object obj = o.e.a.s.get(2038615114);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(12 - View.MeasureSpec.makeMeasureSpec(0, 0), (char) Color.blue(0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 459);
                            byte b2 = (byte) ($$b >>> 2);
                            byte b3 = (byte) (b2 - 1);
                            Object[] objArr5 = new Object[1];
                            g(b2, b3, (byte) (b3 - 1), objArr5);
                            obj = cls.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(2038615114, obj);
                        }
                        cArr3[i6] = ((Character) ((Method) obj).invoke(null, objArr4)).charValue();
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj2 = o.e.a.s.get(-1412673904);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(11 - ((Process.getThreadPriority(0) + 20) >> 6), (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), 313 - (ViewConfiguration.getPressedStateDuration() >> 16));
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr7 = new Object[1];
                                g(b4, b5, (byte) (b5 - 1), objArr7);
                                obj2 = cls2.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj2);
                            }
                            ((Method) obj2).invoke(null, objArr6);
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                if (i > 0) {
                    hVar.c = i;
                    char[] cArr4 = new char[i2];
                    System.arraycopy(cArr3, 0, cArr4, 0, i2);
                    System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                    System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
                }
                switch (!z) {
                    case true:
                        break;
                    default:
                        char[] cArr5 = new char[i2];
                        hVar.a = 0;
                        while (hVar.a < i2) {
                            int i7 = $11 + Opcodes.DDIV;
                            $10 = i7 % 128;
                            int i8 = i7 % 2;
                            cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                            try {
                                Object[] objArr8 = {hVar, hVar};
                                Object obj3 = o.e.a.s.get(-1412673904);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 11, (char) (ViewConfiguration.getScrollBarSize() >> 8), 312 - Process.getGidForName(""));
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr9 = new Object[1];
                                    g(b6, b7, (byte) (b7 - 1), objArr9);
                                    obj3 = cls3.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr8);
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        cArr3 = cArr5;
                        break;
                }
                String str2 = new String(cArr3);
                int i9 = $10 + 61;
                $11 = i9 % 128;
                switch (i9 % 2 == 0 ? (char) 20 : ',') {
                    case 20:
                        throw null;
                    default:
                        objArr3[0] = str2;
                        return;
                }
            }
        }, cVar);
        SecurePinInput securePinInput = this.f111o;
        c.C0039c c0039c2 = null;
        switch (securePinInput != null ? 'J' : ';') {
            case 'J':
                int i = u + 11;
                r = i % 128;
                int i2 = i % 2;
                if (securePinInput.getCurrentPinInputProperties() != null) {
                    c0039c = new c.C0039c(this.f111o.getCurrentPinInputProperties());
                    int i3 = r + 97;
                    u = i3 % 128;
                    int i4 = i3 % 2;
                } else {
                    c0039c = null;
                }
                if (this.f111o.getNewPinInputProperties() == null) {
                    c0039c2 = c0039c;
                    eVar = null;
                    break;
                } else {
                    c0039c2 = c0039c;
                    eVar = new c.e(context, this.f111o.getNewPinInputProperties());
                    break;
                }
            default:
                eVar = null;
                break;
        }
        if (c0039c2 == null) {
            int i5 = r + 37;
            u = i5 % 128;
            int i6 = i5 % 2;
            switch (eVar == null ? 'M' : Typography.amp) {
                case 'M':
                    eVar2.c(null, null, dVar, this.i, ((d) this).n);
                    int i7 = u + 53;
                    r = i7 % 128;
                    int i8 = i7 % 2;
                    break;
            }
            return;
        }
        d(context, c0039c2, eVar, new c.b() { // from class: o.v.p.1
            private static int c = 0;
            private static int f = 1;

            @Override // o.ed.c.b
            public final void b() {
                int i9 = c;
                int i10 = ((i9 | 41) << 1) - (i9 ^ 41);
                f = i10 % 128;
                switch (i10 % 2 != 0) {
                    case true:
                        o.p.g gVar = l;
                        if (gVar != null) {
                            gVar.onError(new o.bv.c(AntelopErrorCode.UserCancelled));
                        }
                        int i11 = f;
                        int i12 = (i11 ^ Opcodes.DSUB) + ((i11 & Opcodes.DSUB) << 1);
                        c = i12 % 128;
                        int i13 = i12 % 2;
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.ed.c.b
            public final void c(byte[] bArr, byte[] bArr2) {
                int i9 = f;
                int i10 = (i9 ^ Opcodes.LSHL) + ((i9 & Opcodes.LSHL) << 1);
                c = i10 % 128;
                int i11 = i10 % 2;
                eVar2.c(bArr, bArr2, dVar, p.this.i, ((d) p.this).n);
                int i12 = f;
                int i13 = (i12 ^ 51) + ((i12 & 51) << 1);
                c = i13 % 128;
                switch (i13 % 2 != 0) {
                    case false:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }
        });
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = r + 99;
        u = i % 128;
        int i2 = i % 2;
        if (((d) this).n.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            v((byte) Color.red(0), (-2104772925) + (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (short) ((ViewConfiguration.getTouchSlop() >> 8) - 78), ExpandableListView.getPackedPositionType(0L) - 14, (ViewConfiguration.getWindowTouchSlop() >> 8) - 73920297, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (this.m) {
            int i3 = r + 13;
            u = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }
        WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
        Object[] objArr2 = new Object[1];
        v((byte) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (-2104772968) + (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) ((-77) - KeyEvent.getDeadChar(0, 0)), (-2) - (ViewConfiguration.getKeyRepeatDelay() >> 16), (-73920284) - Color.argb(0, 0, 0, 0), objArr2);
        String intern = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        v((byte) (ViewConfiguration.getPressedStateDuration() >> 16), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 2104772921, (short) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + Opcodes.DSUB), -(SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (-73920280) - View.getDefaultSize(0, 0), objArr3);
        StringBuilder append = sb.append(((String) objArr3[0]).intern()).append(((d) this).n.e());
        Object[] objArr4 = new Object[1];
        v((byte) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.combineMeasuredStates(0, 0) - 2104772905, (short) (ExpandableListView.getPackedPositionGroup(0L) - 117), TextUtils.getTrimmedLength("") + 11, (-73920268) - TextUtils.getTrimmedLength(""), objArr4);
        throw new WalletValidationException(walletValidationErrorCode2, intern, append.append(((String) objArr4[0]).intern()).toString());
    }

    public final int a() {
        int i = r;
        int i2 = i + Opcodes.LNEG;
        u = i2 % 128;
        int i3 = i2 % 2;
        int i4 = this.h;
        int i5 = i + 3;
        u = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return i4;
        }
    }

    private void d(Context context, c.C0039c c0039c, c.e eVar, c.b bVar) {
        new o.ed.c(bVar, c0039c, eVar, new CustomerAuthenticatedProcessActivityCallback() { // from class: o.v.p.2
            private static int a = 0;
            private static int e = 1;

            @Override // fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback
            public final void onActivityStart(Context context2) {
                int i = a;
                int i2 = ((i | 27) << 1) - (i ^ 27);
                e = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 20 : (char) 28) {
                    case 20:
                        int i3 = 99 / 0;
                        switch (p.this.l != null ? '\'' : 'Q') {
                            case '\'':
                                break;
                            default:
                                return;
                        }
                    default:
                        switch (p.this.l != null ? 'a' : 'T') {
                            case Opcodes.BASTORE /* 84 */:
                                return;
                        }
                }
                p.this.l.onActivityStart(context2);
                int i4 = (a + Opcodes.IREM) - 1;
                e = i4 % 128;
                int i5 = i4 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback
            public final void onActivityStop() {
                int i = e + 69;
                a = i % 128;
                int i2 = i % 2;
                switch (p.this.l != null ? '/' : 'Y') {
                    case '/':
                        int i3 = (a + 114) - 1;
                        e = i3 % 128;
                        boolean z = i3 % 2 == 0;
                        p.this.l.onActivityStop();
                        switch (z) {
                            case false:
                                int i4 = a;
                                int i5 = (i4 & 45) + (i4 | 45);
                                e = i5 % 128;
                                int i6 = i5 % 2;
                                break;
                            default:
                                throw null;
                        }
                }
                int i7 = a + 45;
                e = i7 % 128;
                int i8 = i7 % 2;
            }
        }).b(context);
        int i = u + 31;
        r = i % 128;
        int i2 = i % 2;
    }

    public final void e(Context context, SecurePinInput securePinInput, o.p.i iVar) throws WalletValidationException {
        int i = u + Opcodes.LSHR;
        r = i % 128;
        switch (i % 2 != 0 ? (char) 30 : 'P') {
            case 30:
                this.f111o = securePinInput;
                d(context, iVar);
                throw null;
            default:
                this.f111o = securePinInput;
                d(context, iVar);
                return;
        }
    }

    public final void d(Context context, SecurePinInput securePinInput, o.p.g gVar) throws WalletValidationException {
        int i = u + 75;
        r = i % 128;
        switch (i % 2 != 0 ? (char) 24 : 'A') {
            case 24:
                this.f111o = securePinInput;
                d(context, gVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.f111o = securePinInput;
                d(context, gVar);
                int i2 = r + 79;
                u = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 14 : (char) 23) {
                    case 14:
                        int i3 = 34 / 0;
                        return;
                    default:
                        return;
                }
        }
    }

    public final void b(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        int i = r + Opcodes.LNEG;
        u = i % 128;
        char c = i % 2 == 0 ? 'B' : (char) 27;
        this.l = customerAuthenticatedProcessActivityCallback;
        switch (c) {
            case 'B':
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:87:0x02b5, code lost:
    
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 948
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.p.v(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
