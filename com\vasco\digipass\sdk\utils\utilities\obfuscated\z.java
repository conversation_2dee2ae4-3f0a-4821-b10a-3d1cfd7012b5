package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.OutputStream;
import org.bouncycastle.asn1.ASN1Encoding;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\z.smali */
public class z {
    private OutputStream a;

    z(OutputStream outputStream) {
        this.a = outputStream;
    }

    static int a(int i) {
        if (i < 128) {
            return 1;
        }
        int i2 = 2;
        while (true) {
            i >>>= 8;
            if (i == 0) {
                return i2;
            }
            i2++;
        }
    }

    public static z a(OutputStream outputStream) {
        return new z(outputStream);
    }

    static int b(int i) {
        if (i < 31) {
            return 1;
        }
        int i2 = 2;
        while (true) {
            i >>>= 7;
            if (i == 0) {
                return i2;
            }
            i2++;
        }
    }

    void a() throws IOException {
    }

    h2 b() {
        return new h2(this.a);
    }

    c3 c() {
        return new c3(this.a);
    }

    final void d(int i) throws IOException {
        if (i < 128) {
            c(i);
            return;
        }
        int i2 = 5;
        byte[] bArr = new byte[5];
        do {
            i2--;
            bArr[i2] = (byte) i;
            i >>>= 8;
        } while (i != 0);
        int i3 = 5 - i2;
        int i4 = i2 - 1;
        bArr[i4] = (byte) (i3 | 128);
        a(bArr, i4, i3 + 1);
    }

    public static z a(OutputStream outputStream, String str) {
        return str.equals(ASN1Encoding.DER) ? new h2(outputStream) : str.equals(ASN1Encoding.DL) ? new c3(outputStream) : new z(outputStream);
    }

    final void b(boolean z, int i) throws IOException {
        if (z) {
            c(i);
        }
    }

    final void c(int i) throws IOException {
        this.a.write(i);
    }

    final void a(byte[] bArr, int i, int i2) throws IOException {
        this.a.write(bArr, i, i2);
    }

    void a(h[] hVarArr) throws IOException {
        for (h hVar : hVarArr) {
            hVar.toASN1Primitive().a(this, true);
        }
    }

    final void a(boolean z, int i, byte b) throws IOException {
        b(z, i);
        d(1);
        c(b);
    }

    final void a(boolean z, int i, byte[] bArr) throws IOException {
        b(z, i);
        d(bArr.length);
        a(bArr, 0, bArr.length);
    }

    final void a(boolean z, int i, byte[] bArr, int i2, int i3) throws IOException {
        b(z, i);
        d(i3);
        a(bArr, i2, i3);
    }

    final void a(boolean z, int i, byte b, byte[] bArr, int i2, int i3) throws IOException {
        b(z, i);
        d(i3 + 1);
        c(b);
        a(bArr, i2, i3);
    }

    final void a(boolean z, int i, byte[] bArr, int i2, int i3, byte b) throws IOException {
        b(z, i);
        d(i3 + 1);
        a(bArr, i2, i3);
        c(b);
    }

    final void a(boolean z, int i, h[] hVarArr) throws IOException {
        b(z, i);
        c(128);
        a(hVarArr);
        c(0);
        c(0);
    }

    final void a(boolean z, int i, int i2) throws IOException {
        if (z) {
            if (i2 < 31) {
                c(i | i2);
                return;
            }
            byte[] bArr = new byte[6];
            int i3 = 5;
            bArr[5] = (byte) (i2 & 127);
            while (i2 > 127) {
                i2 >>>= 7;
                i3--;
                bArr[i3] = (byte) ((i2 & 127) | 128);
            }
            int i4 = i3 - 1;
            bArr[i4] = (byte) (31 | i);
            a(bArr, i4, 6 - i4);
        }
    }

    void a(b0 b0Var, boolean z) throws IOException {
        b0Var.a(this, z);
    }

    void a(b0[] b0VarArr) throws IOException {
        for (b0 b0Var : b0VarArr) {
            b0Var.a(this, true);
        }
    }

    static int a(boolean z, int i) {
        return (z ? 1 : 0) + a(i) + i;
    }
}
