package o.er;

import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\a.smali */
public abstract class a {
    private final boolean b;
    private static int e = 0;
    private static int c = 1;

    public a(boolean z) {
        this.b = z;
    }

    public final boolean b() {
        boolean z;
        int i = e;
        int i2 = (i & 55) + (i | 55);
        c = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                z = this.b;
                int i3 = 10 / 0;
                break;
            default:
                z = this.b;
                break;
        }
        int i4 = (i ^ 51) + ((i & 51) << 1);
        c = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final int hashCode() {
        int i = e + 47;
        c = i % 128;
        switch (i % 2 == 0 ? (char) 6 : Typography.less) {
            case '<':
                return super.hashCode();
            default:
                int i2 = 8 / 0;
                return super.hashCode();
        }
    }

    public final boolean equals(Object obj) {
        int i = (e + 96) - 1;
        c = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = c + 35;
        e = i3 % 128;
        int i4 = i3 % 2;
        return equals;
    }

    public final String toString() {
        int i = c;
        int i2 = (i ^ 43) + ((i & 43) << 1);
        e = i2 % 128;
        int i3 = i2 % 2;
        String obj = super.toString();
        int i4 = e;
        int i5 = (i4 ^ 19) + ((i4 & 19) << 1);
        c = i5 % 128;
        int i6 = i5 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i = e;
        int i2 = (i ^ 91) + ((i & 91) << 1);
        c = i2 % 128;
        char c2 = i2 % 2 == 0 ? (char) 30 : 'N';
        super.finalize();
        switch (c2) {
            case 'N':
                return;
            default:
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
