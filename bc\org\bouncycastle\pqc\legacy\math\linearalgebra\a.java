package bc.org.bouncycastle.pqc.legacy.math.linearalgebra;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.l6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w7;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\pqc\legacy\math\linearalgebra\a.smali */
public abstract class a {
    public static final char MATRIX_TYPE_RANDOM_LT = 'L';
    public static final char MATRIX_TYPE_RANDOM_REGULAR = 'R';
    public static final char MATRIX_TYPE_RANDOM_UT = 'U';
    public static final char MATRIX_TYPE_UNIT = 'I';
    public static final char MATRIX_TYPE_ZERO = 'Z';
    protected int numColumns;
    protected int numRows;

    public abstract a computeInverse();

    public abstract byte[] getEncoded();

    public int getNumColumns() {
        return this.numColumns;
    }

    public int getNumRows() {
        return this.numRows;
    }

    public abstract boolean isZero();

    public abstract w7 leftMultiply(w7 w7Var);

    public abstract a rightMultiply(a aVar);

    public abstract a rightMultiply(l6 l6Var);

    public abstract w7 rightMultiply(w7 w7Var);

    public abstract String toString();
}
