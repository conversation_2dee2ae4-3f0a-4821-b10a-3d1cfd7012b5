package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\bÀ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0006\u0010\u0007J\u000f\u0010\u0003\u001a\u00020\u0002H\u0000¢\u0006\u0004\b\u0003\u0010\u0004J\u000f\u0010\u0005\u001a\u00020\u0002H\u0000¢\u0006\u0004\b\u0005\u0010\u0004¨\u0006\b"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/n;", "", "", "b", "()Ljava/lang/String;", "a", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\n.smali */
public final class n {
    public static final n a = new n();

    private n() {
    }

    public final String a() {
        StringBuilder sb = new StringBuilder();
        int[] iArr = {39, 53, 34, 41, 39, 34, 52, 39, 42, 56, 35, 35, 55, 52, 40, 34, 56, 53, 54, 43, 36, 37, 43, 37, 56, 53, 54, 54, 36, 37, 53, 35, 37, 35, 54, 42, 52, 37, 40, 52, 51, 38, 40, 40, 37, 35, 42, 54, 40, 42, 52, 35, 51, 37, 38, 56, 51, 53, 40, 52, 52, 38, 38, 51};
        for (int i = 0; i < 64; i++) {
            sb.append((char) ((iArr[i] + 14) & 255));
        }
        String sb2 = sb.toString();
        Intrinsics.checkNotNullExpressionValue(sb2, "out.toString()");
        return sb2;
    }

    public final String b() {
        byte[] bArr = new byte[10];
        int[] iArr = {Opcodes.FDIV, 63, Opcodes.IF_ICMPEQ, 127, 79, 239, Opcodes.IF_ICMPEQ, 78, 239, Opcodes.IF_ICMPEQ};
        for (int i = 0; i < 10; i++) {
            int i2 = iArr[i] - 158;
            bArr[i] = (byte) (((((i2 & 255) >> 4) | (i2 << 4)) & 255) - 172);
        }
        return new String(bArr, Charsets.UTF_8);
    }
}
