package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.os.Build;
import kotlin.Metadata;

@Metadata(bv = {}, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0002\b\u0004\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0004\u0010\u0005J\u0006\u0010\u0003\u001a\u00020\u0002¨\u0006\u0006"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/e;", "", "", "a", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\e.smali */
public final class e {
    public static final e a = new e();

    private e() {
    }

    public final int a() {
        return Build.VERSION.SDK_INT;
    }
}
