package o.eh;

import android.content.Context;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.IOException;
import java.lang.reflect.Method;
import java.security.SecureRandom;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.dk.b;
import o.ec.e;
import o.ee.g;
import o.ef.a;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eh\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static char[] b;
    private static char c;
    private static int f;
    private static char g;
    private static int i;
    private static char j;
    private final String d;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        b();
        int i2 = f + 49;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void b() {
        b = new char[]{50939, 50851, 50852, 50852, 50858, 50836, 50838, 50854, 50878, 50849, 50857, 50833, 50829, 50846, 50875, 50851, 50848, 50878, 50849, 50857, 50849, 50845, 50826, 50854, 50854, 50856, 50897, 50937, 50941, 50936, 50938, 50938, 50933, 50936, 50936, 50936, 50902, 50939, 50942, 50937, 50936, 50943, 50942, 50937, 50937, 50901, 50943, 50942, 50937, 50936, 50942, 50937, 50939, 50936, 50939, 50932, 50877, 50876, 50854, 50857, 50849, 50849, 50841, 50838, 50854, 50855, 50851, 50858, 50839, 50861, 50853, 50838, 50905, 50939, 50939, 50831, 50857, 50876, 50852, 50862, 50877, 50874, 50878, 50873, 50857, 50832, 50872, 50872, 50848, 50873, 50871, 50868, 50847, 50914, 50914, 50818, 50878, 50851, 50854, 50823, 50823, 50879, 50868, 50870, 50873, 50848, 50873, 50846, 50816, 50848, 50878, 50875, 50816, 50841, 50873, 50850, 50868, 50875, 50767, 51139, 51164, 50725, 50722, 51138, 51143, 51161, 51164, 50731, 50716, 50716, 50704, 50738, 51137, 51145, 50739, 51142, 51167, 51139, 51138, 50738, 50741, 51165, 51165, 51141, 51138, 51160, 51161, 50720, 50695, 50695, 50727, 51139, 51140, 51147, 50728, 50728, 51165, 51162, 51163, 51138, 51136, 51160, 50723, 50725};
        a = (char) 60809;
        g = (char) 3830;
        j = (char) 54903;
        c = (char) 58322;
    }

    static void init$0() {
        $$a = new byte[]{3, 65, Tnaf.POW_2_WIDTH, 100};
        $$b = 248;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r5, short r6, short r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 + 4
            int r7 = 122 - r7
            int r6 = r6 * 2
            int r6 = r6 + 1
            byte[] r0 = o.eh.c.$$a
            byte[] r1 = new byte[r6]
            r2 = -1
            int r6 = r6 + r2
            if (r0 != 0) goto L17
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r6
            r6 = r5
            goto L36
        L17:
            r4 = r6
            r6 = r5
            r5 = r7
            r7 = r4
        L1b:
            int r2 = r2 + 1
            byte r3 = (byte) r5
            r1[r2] = r3
            int r6 = r6 + 1
            if (r2 != r7) goto L2d
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2d:
            r3 = r0[r6]
            r4 = r8
            r8 = r7
            r7 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r4
        L36:
            int r7 = -r7
            int r5 = r5 + r7
            r7 = r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eh.c.l(short, short, short, java.lang.Object[]):void");
    }

    public c(Context context, long j2) {
        String valueOf;
        String valueOf2;
        try {
            Object[] objArr = new Object[1];
            h("\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, true, objArr);
            String valueOf3 = String.valueOf(((SecureRandom) Class.forName(((String) objArr[0]).intern()).getDeclaredConstructor(null).newInstance(null)).nextLong());
            try {
                a a2 = o.ef.c.a(context);
                switch (AnonymousClass4.c[a2.b().ordinal()]) {
                    case 1:
                        Object[] objArr2 = new Object[1];
                        h("\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{26, 10, 0, 0}, true, objArr2);
                        valueOf3 = ((String) objArr2[0]).intern();
                        break;
                    case 2:
                        Object[] objArr3 = new Object[1];
                        h("\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{36, 9, 0, 8}, true, objArr3);
                        valueOf3 = ((String) objArr3[0]).intern();
                        break;
                    case 3:
                        Object[] objArr4 = new Object[1];
                        k("♙麧䚅躀ퟤꂫⳛ쑡侳\u0a3d", 10 - (Process.myTid() >> 22), objArr4);
                        valueOf3 = ((String) objArr4[0]).intern();
                        break;
                    case 4:
                        Object[] objArr5 = new Object[1];
                        h("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{45, 10, 0, 6}, true, objArr5);
                        valueOf3 = ((String) objArr5[0]).intern();
                        break;
                }
                g.c();
                Object[] objArr6 = new Object[1];
                h("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{55, 17, 0, 0}, true, objArr6);
                String intern = ((String) objArr6[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr7 = new Object[1];
                h("\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{72, 46, 9, 3}, false, objArr7);
                g.d(intern, sb.append(((String) objArr7[0]).intern()).append(valueOf3).toString());
                valueOf = b.e(b.e(e.c(valueOf3), e.c(String.valueOf(j2))));
                String a3 = a(a2.d());
                g.c();
                Object[] objArr8 = new Object[1];
                h("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{55, 17, 0, 0}, true, objArr8);
                String intern2 = ((String) objArr8[0]).intern();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr9 = new Object[1];
                h("\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000", new int[]{Opcodes.FNEG, 46, Opcodes.IF_ICMPLE, 12}, false, objArr9);
                g.d(intern2, sb2.append(((String) objArr9[0]).intern()).append(a3).toString());
                valueOf2 = b.e(b.e(e.c(a3), e.c(String.valueOf(j2 + 1000))));
                g.c();
                if (!valueOf3.equals(a3)) {
                    Object[] objArr10 = new Object[1];
                    h("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{55, 17, 0, 0}, true, objArr10);
                    String intern3 = ((String) objArr10[0]).intern();
                    Object[] objArr11 = new Object[1];
                    k("ᘯ迮\udc1a쀫젾峩\ueb26￡\uf310፭ꭊ쎧\u05ed\ue9e1ꔡ\ueea0\u0b8c\ue903ㇳ䓄\u0cdb넉ਉ汆矪\uf68b騉굺闪鯬\u0601硆䚉신딅ꆾꪧ쎋꧗纯ꊥ䱛㫓䐧", 44 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr11);
                    g.e(intern3, ((String) objArr11[0]).intern());
                }
            } catch (IOException | o.ef.e e) {
                g.c();
                Object[] objArr12 = new Object[1];
                h("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{55, 17, 0, 0}, true, objArr12);
                String intern4 = ((String) objArr12[0]).intern();
                Object[] objArr13 = new Object[1];
                k("ᘯ迮\udc1a쀫젾峩\ueb26￡\uf310፭ꭊ쎧\u05ed\ue9e1ꔡ\ueea0\u0b8c\ue903ㇳ䓄\ue055ࢪ\ud9e2瀰乨ꆷ\uf1c8\udcbbﮆɚ㫓䐧\u05ed\ue9e1ҩ형\u0cdb넉\uda4c主嶔\ue929⬘擐黗攩톣殢饆䚲߽앪", 51 - (ViewConfiguration.getTapTimeout() >> 16), objArr13);
                g.a(intern4, ((String) objArr13[0]).intern(), e);
                try {
                    Object[] objArr14 = new Object[1];
                    h("\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, true, objArr14);
                    valueOf = String.valueOf(((SecureRandom) Class.forName(((String) objArr14[0]).intern()).getDeclaredConstructor(null).newInstance(null)).nextLong());
                    try {
                        Object[] objArr15 = new Object[1];
                        h("\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, true, objArr15);
                        valueOf2 = String.valueOf(((SecureRandom) Class.forName(((String) objArr15[0]).intern()).getDeclaredConstructor(null).newInstance(null)).nextLong());
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            this.e = valueOf;
            this.d = valueOf2;
        } catch (Throwable th3) {
            Throwable cause3 = th3.getCause();
            if (cause3 == null) {
                throw th3;
            }
            throw cause3;
        }
    }

    /* renamed from: o.eh.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eh\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        static final /* synthetic */ int[] c;
        private static int d;

        static {
            d = 0;
            b = 1;
            int[] iArr = new int[o.ef.b.values().length];
            c = iArr;
            try {
                iArr[o.ef.b.c.ordinal()] = 1;
                int i = d;
                int i2 = i & 27;
                int i3 = ((i | 27) & (~i2)) + (i2 << 1);
                b = i3 % 128;
                if (i3 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                c[o.ef.b.e.ordinal()] = 2;
                int i4 = d;
                int i5 = (i4 & (-54)) | ((~i4) & 53);
                int i6 = (i4 & 53) << 1;
                int i7 = (i5 & i6) + (i6 | i5);
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[o.ef.b.b.ordinal()] = 3;
                int i9 = b + 83;
                d = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[o.ef.b.a.ordinal()] = 4;
                int i11 = b + 99;
                d = i11 % 128;
                int i12 = i11 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    public final String c() {
        int i2 = f + Opcodes.LUSHR;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? '\b' : (char) 21) {
            case '\b':
                throw null;
            default:
                return this.e;
        }
    }

    public final String a() {
        int i2 = i;
        int i3 = i2 + 53;
        f = i3 % 128;
        int i4 = i3 % 2;
        String str = this.d;
        int i5 = i2 + 27;
        f = i5 % 128;
        switch (i5 % 2 == 0 ? '(' : '\'') {
            case '(':
                throw null;
            default:
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.lang.String a(java.io.File r7) throws java.io.IOException {
        /*
            java.util.zip.CRC32 r0 = new java.util.zip.CRC32
            r0.<init>()
            java.io.FileInputStream r1 = new java.io.FileInputStream
            r1.<init>(r7)
            r7 = 16384(0x4000, float:2.2959E-41)
            byte[] r7 = new byte[r7]
        Lf:
            int r2 = r1.read(r7)
            r3 = 1
            r4 = 0
            if (r2 <= 0) goto L19
            r5 = r4
            goto L1a
        L19:
            r5 = r3
        L1a:
            switch(r5) {
                case 0: goto L2e;
                default: goto L1d;
            }
        L1d:
            long r0 = r0.getValue()
            java.lang.String r7 = java.lang.String.valueOf(r0)
            java.util.Locale r0 = o.ee.j.a()
            java.lang.String r7 = r7.toLowerCase(r0)
            return r7
        L2e:
            int r5 = o.eh.c.i
            int r5 = r5 + 75
            int r6 = r5 % 128
            o.eh.c.f = r6
            int r5 = r5 % 2
            r0.update(r7, r4, r2)
            int r2 = o.eh.c.i
            int r2 = r2 + 89
            int r5 = r2 % 128
            o.eh.c.f = r5
            int r2 = r2 % 2
            if (r2 != 0) goto L48
            goto L49
        L48:
            r3 = r4
        L49:
            switch(r3) {
                case 1: goto L4c;
                default: goto L4c;
            }
        L4c:
            goto Lf
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eh.c.a(java.io.File):java.lang.String");
    }

    private static void h(String str, int[] iArr, boolean z, Object[] objArr) {
        String str2;
        char[] cArr;
        int i2;
        char[] cArr2;
        char c2;
        String str3;
        String str4;
        int i3;
        String str5 = str;
        int i4 = 2;
        int i5 = 0;
        byte[] bArr = str5;
        if (str5 != null) {
            int i6 = $10 + 19;
            $11 = i6 % 128;
            switch (i6 % 2 == 0 ? (char) 25 : 'C') {
                case 25:
                    int i7 = 15 / 0;
                    bArr = str5.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
                default:
                    bArr = str5.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
            }
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i8 = iArr[0];
        int i9 = 1;
        int i10 = iArr[1];
        int i11 = iArr[2];
        int i12 = iArr[3];
        char[] cArr3 = b;
        String str6 = "";
        switch (cArr3 != null ? 'F' : (char) 20) {
            case 20:
                str2 = "";
                break;
            default:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i13 = 0;
                while (i13 < length) {
                    int i14 = $10 + 93;
                    $11 = i14 % 128;
                    if (i14 % i4 == 0) {
                        try {
                            Object[] objArr2 = new Object[i9];
                            objArr2[i5] = Integer.valueOf(cArr3[i13]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                str4 = str6;
                                i3 = length;
                            } else {
                                Class cls = (Class) o.e.a.c(TextUtils.indexOf(str6, str6, i5) + 11, (char) TextUtils.indexOf(str6, str6), 44 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)));
                                byte b2 = (byte) (-1);
                                byte b3 = (byte) (b2 + 1);
                                str4 = str6;
                                i3 = length;
                                Object[] objArr3 = new Object[1];
                                l(b2, b3, (byte) (b3 + 2), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr4[i13] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i13 >>= 0;
                            length = i3;
                            str6 = str4;
                            i4 = 2;
                            i5 = 0;
                            i9 = 1;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } else {
                        String str7 = str6;
                        int i15 = length;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr3[i13])};
                            Object obj2 = o.e.a.s.get(1951085128);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 11, (char) (ViewConfiguration.getLongPressTimeout() >> 16), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 43);
                                byte b4 = (byte) (-1);
                                byte b5 = (byte) (b4 + 1);
                                Object[] objArr5 = new Object[1];
                                l(b4, b5, (byte) (b5 + 2), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj2);
                            }
                            cArr4[i13] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            i13++;
                            length = i15;
                            str6 = str7;
                            i4 = 2;
                            i5 = 0;
                            i9 = 1;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                }
                str2 = str6;
                cArr3 = cArr4;
                break;
        }
        char[] cArr5 = new char[i10];
        System.arraycopy(cArr3, i8, cArr5, 0, i10);
        switch (bArr2 == null) {
            case true:
                cArr = cArr5;
                break;
            default:
                int i16 = $10 + 49;
                $11 = i16 % 128;
                switch (i16 % 2 != 0) {
                    case true:
                        cArr2 = new char[i10];
                        c2 = 0;
                        lVar.d = 0;
                        break;
                    default:
                        cArr2 = new char[i10];
                        lVar.d = 0;
                        c2 = 1;
                        break;
                }
                while (true) {
                    switch (lVar.d < i10 ? (char) 2 : '=') {
                        case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                            cArr = cArr2;
                            break;
                        default:
                            switch (bArr2[lVar.d] == 1 ? (char) 17 : Typography.dollar) {
                                case '$':
                                    int i17 = lVar.d;
                                    try {
                                        Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                        Object obj3 = o.e.a.s.get(804049217);
                                        if (obj3 != null) {
                                            str3 = str2;
                                        } else {
                                            str3 = str2;
                                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 10, (char) (TextUtils.indexOf((CharSequence) str3, '0') + 1), 207 - (ViewConfiguration.getTapTimeout() >> 16));
                                            byte b6 = (byte) (-1);
                                            byte b7 = (byte) (b6 + 1);
                                            Object[] objArr7 = new Object[1];
                                            l(b6, b7, b7, objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(804049217, obj3);
                                        }
                                        cArr2[i17] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                        break;
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                default:
                                    str3 = str2;
                                    int i18 = lVar.d;
                                    try {
                                        Object[] objArr8 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                        Object obj4 = o.e.a.s.get(2016040108);
                                        if (obj4 == null) {
                                            Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getTouchSlop() >> 8), (char) ExpandableListView.getPackedPositionGroup(0L), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 448);
                                            byte b8 = (byte) (-1);
                                            Object[] objArr9 = new Object[1];
                                            l(b8, (byte) (b8 + 1), $$a[0], objArr9);
                                            obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(2016040108, obj4);
                                        }
                                        cArr2[i18] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                        break;
                                    } catch (Throwable th4) {
                                        Throwable cause4 = th4.getCause();
                                        if (cause4 == null) {
                                            throw th4;
                                        }
                                        throw cause4;
                                    }
                            }
                            c2 = cArr2[lVar.d];
                            try {
                                Object[] objArr10 = {lVar, lVar};
                                Object obj5 = o.e.a.s.get(-2112603350);
                                if (obj5 == null) {
                                    Class cls5 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), (ViewConfiguration.getJumpTapTimeout() >> 16) + 259);
                                    byte b9 = (byte) (-1);
                                    byte b10 = (byte) (b9 + 1);
                                    Object[] objArr11 = new Object[1];
                                    l(b9, b10, (byte) (b10 | 56), objArr11);
                                    obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj5);
                                }
                                ((Method) obj5).invoke(null, objArr10);
                                str2 = str3;
                            } catch (Throwable th5) {
                                Throwable cause5 = th5.getCause();
                                if (cause5 == null) {
                                    throw th5;
                                }
                                throw cause5;
                            }
                    }
                }
        }
        if (i12 > 0) {
            char[] cArr6 = new char[i10];
            System.arraycopy(cArr, 0, cArr6, 0, i10);
            int i19 = i10 - i12;
            System.arraycopy(cArr6, 0, cArr, i19, i12);
            System.arraycopy(cArr6, i12, cArr, 0, i19);
            int i20 = $11 + Opcodes.LREM;
            $10 = i20 % 128;
            int i21 = i20 % 2;
        }
        switch (z) {
            case false:
                break;
            default:
                int i22 = $10 + 3;
                $11 = i22 % 128;
                int i23 = i22 % 2;
                char[] cArr7 = new char[i10];
                lVar.d = 0;
                while (true) {
                    switch (lVar.d < i10 ? '=' : (char) 7) {
                        case 7:
                            cArr = cArr7;
                            break;
                        default:
                            int i24 = $11 + 19;
                            $10 = i24 % 128;
                            if (i24 % 2 != 0) {
                                cArr7[lVar.d] = cArr[(i10 / lVar.d) % 1];
                                i2 = lVar.d << 1;
                            } else {
                                cArr7[lVar.d] = cArr[(i10 - lVar.d) - 1];
                                i2 = lVar.d + 1;
                            }
                            lVar.d = i2;
                            int i25 = $11 + 19;
                            $10 = i25 % 128;
                            int i26 = i25 % 2;
                    }
                }
        }
        switch (i11 > 0) {
            case false:
                break;
            default:
                int i27 = 0;
                while (true) {
                    lVar.d = i27;
                    if (lVar.d >= i10) {
                        break;
                    } else {
                        int i28 = $11 + 49;
                        $10 = i28 % 128;
                        if (i28 % 2 != 0) {
                            cArr[lVar.d] = (char) (cArr[lVar.d] * iArr[2]);
                            i27 = lVar.d - 0;
                        } else {
                            cArr[lVar.d] = (char) (cArr[lVar.d] - iArr[2]);
                            i27 = lVar.d + 1;
                        }
                    }
                }
        }
        objArr[0] = new String(cArr);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 552
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eh.c.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
