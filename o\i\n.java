package o.i;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.PinCustomerAuthenticationPrompt;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.util.Objects;
import o.n.d;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\n.smali */
public class n extends g {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int c;
    private static int f;
    private static int g;
    private static boolean i;
    private static boolean j;
    private short b;
    private short d;
    private boolean e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        r();
        Drawable.resolveOpacity(0, 0);
        int i2 = f + 85;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$g = new byte[]{1, 25, 123, 58};
        $$h = Opcodes.IXOR;
    }

    static void r() {
        a = new char[]{61753, 61776, 61787, 61742, 61788, 61790, 61789, 61786, 61780, 61772, 61791, 61736, 61777, 61774, 61768, 61748, 61773, 61795, 61781, 61756, 61778, 61705, 61716, 61779, 61775, 61785, 61782, 61792, 61717, 61784, 61943, 61798, 61732, 61793};
        j = true;
        i = true;
        c = 782103017;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Type inference failed for: r6v2, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(byte r5, short r6, byte r7, java.lang.Object[] r8) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r5 = r5 + 117
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.i.n.$$g
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r4 = r6
            r3 = r2
            goto L28
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r5
            r1[r3] = r4
            if (r3 != r7) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            r4 = r0[r6]
            int r3 = r3 + 1
        L28:
            int r6 = r6 + 1
            int r4 = -r4
            int r5 = r5 + r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.n.x(byte, short, byte, java.lang.Object[]):void");
    }

    public n(f fVar) {
        super(fVar);
    }

    public final short b() {
        short s;
        int i2 = g;
        int i3 = i2 + 19;
        f = i3 % 128;
        switch (i3 % 2 != 0 ? '3' : (char) 26) {
            case '3':
                s = this.d;
                int i4 = 62 / 0;
                break;
            default:
                s = this.d;
                break;
        }
        int i5 = i2 + 71;
        f = i5 % 128;
        switch (i5 % 2 != 0 ? ' ' : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return s;
            default:
                throw null;
        }
    }

    public final void c(short s) {
        int i2 = g;
        int i3 = i2 + 15;
        f = i3 % 128;
        int i4 = i3 % 2;
        this.d = s;
        int i5 = i2 + 29;
        f = i5 % 128;
        int i6 = i5 % 2;
    }

    public short e() {
        int i2 = f + 59;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? ':' : (char) 3) {
            case 3:
                return this.b;
            default:
                int i3 = 59 / 0;
                return this.b;
        }
    }

    public final void n() {
        int i2 = f + Opcodes.DREM;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        this.b = (short) 0;
        int i5 = i3 + 29;
        f = i5 % 128;
        int i6 = i5 % 2;
    }

    public final boolean l() {
        int i2 = f + Opcodes.LUSHR;
        g = i2 % 128;
        int i3 = i2 % 2;
        switch (e() >= b() ? 'T' : '\b') {
            case '\b':
                int i4 = g + Opcodes.LNEG;
                f = i4 % 128;
                int i5 = i4 % 2;
                return false;
            default:
                int i6 = g + 91;
                f = i6 % 128;
                switch (i6 % 2 != 0 ? '5' : 'K') {
                    case Opcodes.SALOAD /* 53 */:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return true;
                }
        }
    }

    final boolean c(Short sh) {
        int i2 = f + 75;
        g = i2 % 128;
        boolean z = false;
        switch (i2 % 2 == 0) {
            case false:
                switch (this.b != sh.shortValue() ? (char) 1 : 'I') {
                    case 'I':
                        break;
                    default:
                        z = true;
                        break;
                }
            default:
                if (this.b == sh.shortValue()) {
                    z = true;
                    break;
                }
                z = true;
                break;
        }
        this.b = sh.shortValue();
        int i3 = f + 65;
        g = i3 % 128;
        int i4 = i3 % 2;
        return z;
    }

    public final boolean t() {
        int i2 = g;
        int i3 = i2 + 23;
        f = i3 % 128;
        Object obj = null;
        switch (i3 % 2 != 0 ? 'P' : (char) 3) {
            case 'P':
                obj.hashCode();
                throw null;
            default:
                boolean z = this.e;
                int i4 = i2 + Opcodes.LREM;
                f = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        return z;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final void d(boolean z) {
        int i2 = f;
        int i3 = i2 + 9;
        g = i3 % 128;
        boolean z2 = i3 % 2 != 0;
        this.e = z;
        switch (z2) {
            case false:
                int i4 = 0 / 0;
                break;
        }
        int i5 = i2 + 93;
        g = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0083, code lost:
    
        if (r9 == false) goto L29;
     */
    @Override // o.i.g
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.f.c e(o.f.e r7, o.i.i r8, boolean r9) {
        /*
            r6 = this;
            o.f.c r0 = super.e(r7, r8, r9)
            o.f.c r1 = o.f.c.d
            r2 = 21
            if (r0 == r1) goto Ld
            r1 = r2
            goto Lf
        Ld:
            r1 = 83
        Lf:
            switch(r1) {
                case 21: goto L1e;
                default: goto L12;
            }
        L12:
            boolean r0 = r6.t()
            java.lang.String r1 = "\u0091\u0088\u008d\u0087\u008a\u0090\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            r3 = 1
            r4 = 0
            r5 = 0
            if (r0 == 0) goto L62
            goto L1f
        L1e:
            return r0
        L1f:
            boolean r7 = r7 instanceof o.f.g
            if (r7 != 0) goto L62
            int r7 = o.i.n.f
            int r7 = r7 + 49
            int r8 = r7 % 128
            o.i.n.g = r8
            int r7 = r7 % 2
            o.ee.g.c()
            int r7 = android.view.View.resolveSizeAndState(r5, r5, r5)
            int r7 = 127 - r7
            java.lang.Object[] r8 = new java.lang.Object[r3]
            w(r4, r7, r4, r1, r8)
            r7 = r8[r5]
            java.lang.String r7 = (java.lang.String) r7
            java.lang.String r7 = r7.intern()
            double r8 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r5)
            r0 = 0
            int r8 = (r8 > r0 ? 1 : (r8 == r0 ? 0 : -1))
            int r8 = r8 + 127
            java.lang.Object[] r9 = new java.lang.Object[r3]
            java.lang.String r0 = "\u0091\u008a\u0086\u0086\u008f\u009a\u0096\u008a\u0099\u0096\u0088\u0087\u0096\u0086\u0093\u008f\u0082\u0087\u0083\u008a\u0091\u008a\u008b\u008e\u0096\u008b\u008a\u0085\u0086\u0086\u0082\u0096\u0095\u0083\u0082\u008b\u0082\u0085\u009e\u008a\u008b\u0096\u009d\u008b\u008a\u0085\u0086\u0086\u0082\u0096\u009c\u0099\u0096\u0091\u008a\u009b\u008e\u008a\u008d\u008e\u0096\u0086\u0082\u0096\u0083\u0082\u009a\u0096\u0097\u0096\u0083\u008a\u0091\u0091\u0082\u0099\u008b\u0088\u0098\u0096\u0097\u0096\u008a\u0095\u008f\u0086\u0094\u0086\u0093\u008f\u0082\u0087\u0083\u008a\u0091\u008a\u008b\u0084\u008a\u0087\u008f\u0091\u0082\u0093\u008f\u0092"
            w(r4, r8, r4, r0, r9)
            r8 = r9[r5]
            java.lang.String r8 = (java.lang.String) r8
            java.lang.String r8 = r8.intern()
            o.ee.g.d(r7, r8)
            o.f.c r7 = o.f.c.b
            return r7
        L62:
            o.i.i r7 = o.i.i.e
            if (r8 == r7) goto L68
            r7 = r2
            goto L6a
        L68:
            r7 = 99
        L6a:
            switch(r7) {
                case 21: goto L6e;
                default: goto L6d;
            }
        L6d:
            goto L85
        L6e:
            o.i.i r7 = o.i.i.d
            if (r8 != r7) goto L74
            r7 = r5
            goto L76
        L74:
            r7 = 33
        L76:
            switch(r7) {
                case 0: goto L7a;
                default: goto L79;
            }
        L79:
            goto Lbe
        L7a:
            int r7 = o.i.n.g
            int r7 = r7 + r2
            int r8 = r7 % 128
            o.i.n.f = r8
            int r7 = r7 % 2
            if (r9 != 0) goto Lbe
        L85:
            boolean r7 = r6.l()
            if (r7 == 0) goto Lbe
            o.ee.g.c()
            int r7 = android.graphics.ImageFormat.getBitsPerPixel(r5)
            int r7 = r7 + 128
            java.lang.Object[] r8 = new java.lang.Object[r3]
            w(r4, r7, r4, r1, r8)
            r7 = r8[r5]
            java.lang.String r7 = (java.lang.String) r7
            java.lang.String r7 = r7.intern()
            java.lang.String r8 = ""
            int r8 = android.text.TextUtils.indexOf(r8, r8, r5, r5)
            int r8 = r8 + 127
            java.lang.Object[] r9 = new java.lang.Object[r3]
            java.lang.String r0 = "\u0091\u008a\u008d\u008e\u008f\u008a\u008b\u0096\u0087\u0083\u0085\u0088\u008e\u0096\u0087\u009a\u0089\u008a\u0087\u0087\u008f\u0096\u0097\u0096\u0083\u008a\u0091\u0091\u0082\u0099\u008b\u0088\u0098\u0096\u0097\u0096\u008a\u0095\u008f\u0086\u0094\u0086\u0093\u008f\u0082\u0087\u0083\u008a\u0091\u008a\u008b\u0084\u008a\u0087\u008f\u0091\u0082\u0093\u008f\u0092"
            w(r4, r8, r4, r0, r9)
            r8 = r9[r5]
            java.lang.String r8 = (java.lang.String) r8
            java.lang.String r8 = r8.intern()
            o.ee.g.d(r7, r8)
            o.f.c r7 = o.f.c.c
            return r7
        Lbe:
            o.f.c r7 = o.f.c.d
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.n.e(o.f.e, o.i.i, boolean):o.f.c");
    }

    @Override // o.i.g
    protected final o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException {
        if (!(customerAuthenticationPrompt instanceof PinCustomerAuthenticationPrompt)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            w(null, 127 - View.combineMeasuredStates(0, 0), null, "\u0091\u0088\u008d\u0087\u008a\u0090\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            w(null, TextUtils.indexOf((CharSequence) "", '0', 0) + 128, null, "\u0091\u008a\u0091\u0082\u0092\u0088\u008b\u009a\u0096\u0087\u009a\u0089\u0088\u008b\u0081\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0096\u0091\u0082\u0093\u008f\u0092\u0083\u0082\u0096\u0097\u0096\u0087\u009a\u0089\u0088\u008b\u0081\u0091\u0093\u0082\u0085\u0099", objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr3 = new Object[1];
            w(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u0087\u009a\u0089\u0088\u008b\u0081\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084", objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (t()) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            w(null, 127 - (ViewConfiguration.getPressedStateDuration() >> 16), null, "\u0091\u0088\u008d\u0087\u008a\u0090\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            w(null, 128 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), null, "\u008b\u008a\u0085\u0086\u0086\u0082\u0096\u009c\u0099\u0096\u0091\u008a\u009b\u008e\u008a\u008d\u008e\u0096\u0083\u0082\u009a\u0096\u008f\u0096\u0087\u009a\u0089\u0088\u008b\u009a\u0096\u0087\u0088\u0083\u0083\u008f\u008e\u0096\u0097\u0096\u0087\u009a\u0089\u0088\u008b\u0081\u0091\u0093\u0082\u0085\u0099", objArr5);
            o.ee.g.e(intern2, ((String) objArr5[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unexpected;
            Object[] objArr6 = new Object[1];
            w(null, 128 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), null, "\u0087\u009a\u0089\u0088\u008b\u0081\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084", objArr6);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr6[0]).intern());
        }
        final PinCustomerAuthenticationPrompt pinCustomerAuthenticationPrompt = (PinCustomerAuthenticationPrompt) customerAuthenticationPrompt;
        return new o.n.d(context, pinCustomerAuthenticationPrompt.getTitle(), pinCustomerAuthenticationPrompt.getSubtitle(), pinCustomerAuthenticationPrompt.getInvalidPinMessageProvider() != null ? new d.e() { // from class: o.i.n$$ExternalSyntheticLambda0
            @Override // o.n.d.e
            public final String getMessage(int i2) {
                String b;
                b = n.b(PinCustomerAuthenticationPrompt.this, i2);
                return b;
            }
        } : null, this, pinCustomerAuthenticationPrompt.isPinCheckDisabled());
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ String b(PinCustomerAuthenticationPrompt pinCustomerAuthenticationPrompt, int i2) {
        int i3 = g + 43;
        f = i3 % 128;
        char c2 = i3 % 2 != 0 ? '\f' : (char) 3;
        PinCustomerAuthenticationPrompt.InvalidPinMessageProvider invalidPinMessageProvider = pinCustomerAuthenticationPrompt.getInvalidPinMessageProvider();
        switch (c2) {
            case 3:
                return invalidPinMessageProvider.getMessage(i2);
            default:
                invalidPinMessageProvider.getMessage(i2);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.i.g
    final void e(Context context, g gVar) {
        int i2 = g + Opcodes.LSUB;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 11 : '8') {
            case 11:
                super.e(context, gVar);
                this.d = ((n) gVar).d;
                throw null;
            default:
                super.e(context, gVar);
                this.d = ((n) gVar).d;
                return;
        }
    }

    @Override // o.i.g
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null) {
            int i2 = f + Opcodes.LSUB;
            g = i2 % 128;
            if (i2 % 2 == 0) {
                getClass();
                obj.getClass();
                throw null;
            }
            switch (getClass() != obj.getClass() ? ']' : '=') {
                case Opcodes.DUP2_X1 /* 93 */:
                    break;
                default:
                    if (!super.equals(obj)) {
                        int i3 = f + 51;
                        g = i3 % 128;
                        int i4 = i3 % 2;
                        return false;
                    }
                    n nVar = (n) obj;
                    switch (Objects.equals(Short.valueOf(this.b), Short.valueOf(nVar.b))) {
                        case true:
                            int i5 = g + 87;
                            f = i5 % 128;
                            int i6 = i5 % 2;
                            switch (Objects.equals(Short.valueOf(this.d), Short.valueOf(nVar.d))) {
                                case true:
                                    int i7 = g + 39;
                                    f = i7 % 128;
                                    int i8 = i7 % 2;
                                    return true;
                            }
                        default:
                            return false;
                    }
            }
        }
        int i9 = g + Opcodes.LSUB;
        f = i9 % 128;
        switch (i9 % 2 != 0) {
            case false:
                return false;
            default:
                throw null;
        }
    }

    public String toString() {
        StringBuilder append = new StringBuilder().append(super.toString());
        Object[] objArr = new Object[1];
        w(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "¡\u0087\u0083\u0085\u0088\u0084\u0086\u0087\u009a\u0089\u008a\u0087\u0087\u008c\u0087\u0083\u008a\u008b\u008b\u0085\u008e \u0091\u0088\u008d\u0087\u008a\u0090\u0083\u0088\u0082\u0087\u008f\u008e\u0082\u0087\u0083\u008a\u008d\u0087\u0085\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u009f\u0096", objArr);
        StringBuilder append2 = append.append(((String) objArr[0]).intern()).append((int) this.b);
        Object[] objArr2 = new Object[1];
        w(null, (-16777089) - Color.rgb(0, 0, 0), null, "¡\u0087\u0083\u0085\u0088\u0084\u0086\u0087\u009a\u0089\u008a\u0087\u0087\u008c¢\u008f\u0089\u0096\u009d", objArr2);
        String obj = append2.append(((String) objArr2[0]).intern()).append((int) this.d).append('}').toString();
        int i2 = g + 3;
        f = i2 % 128;
        int i3 = i2 % 2;
        return obj;
    }

    private static void w(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        String str3 = str2;
        int i3 = 2;
        byte[] bArr = str3;
        if (str3 != null) {
            int i4 = $11 + 87;
            $10 = i4 % 128;
            int i5 = i4 % 2;
            bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        if (str != null) {
            int i6 = $11 + 89;
            $10 = i6 % 128;
            if (i6 % 2 != 0) {
                str.toCharArray();
                throw null;
            }
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr2 = cArr;
        o.a.j jVar = new o.a.j();
        char[] cArr3 = a;
        int i7 = 1;
        int i8 = 0;
        long j2 = 0;
        switch (cArr3 != null) {
            case false:
                break;
            default:
                int i9 = $10;
                int i10 = i9 + 47;
                $11 = i10 % 128;
                int i11 = i10 % 2;
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i12 = i9 + 29;
                $11 = i12 % 128;
                int i13 = i12 % 2;
                int i14 = 0;
                while (true) {
                    switch (i14 < length ? i8 : i7) {
                        case 0:
                            int i15 = $11 + 99;
                            $10 = i15 % 128;
                            if (i15 % i3 != 0) {
                                try {
                                    Object[] objArr2 = new Object[i7];
                                    objArr2[i8] = Integer.valueOf(cArr3[i14]);
                                    Object obj = o.e.a.s.get(1085633688);
                                    if (obj == null) {
                                        Class cls = (Class) o.e.a.c(11 - ExpandableListView.getPackedPositionType(j2), (char) (ViewConfiguration.getJumpTapTimeout() >> 16), (TypedValue.complexToFraction(i8, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(i8, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 493);
                                        byte length2 = (byte) $$g.length;
                                        byte b = (byte) (length2 - 4);
                                        Object[] objArr3 = new Object[1];
                                        x(length2, b, b, objArr3);
                                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                        o.e.a.s.put(1085633688, obj);
                                    }
                                    cArr4[i14] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                    i14 += 0;
                                    i3 = 2;
                                    i7 = 1;
                                    i8 = 0;
                                    j2 = 0;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            } else {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr3[i14])};
                                    Object obj2 = o.e.a.s.get(1085633688);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 10, (char) TextUtils.indexOf("", "", 0), View.getDefaultSize(0, 0) + 493);
                                        byte length3 = (byte) $$g.length;
                                        byte b2 = (byte) (length3 - 4);
                                        Object[] objArr5 = new Object[1];
                                        x(length3, b2, b2, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(1085633688, obj2);
                                    }
                                    cArr4[i14] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    i14++;
                                    i3 = 2;
                                    i7 = 1;
                                    i8 = 0;
                                    j2 = 0;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                        default:
                            cArr3 = cArr4;
                            break;
                    }
                }
        }
        try {
            Object[] objArr6 = {Integer.valueOf(c)};
            Object obj3 = o.e.a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, (char) (ExpandableListView.getPackedPositionChild(0L) + 8857), 324 - TextUtils.indexOf("", "", 0));
                byte b3 = $$g[0];
                byte b4 = (byte) (b3 - 1);
                Object[] objArr7 = new Object[1];
                x(b3, b4, b4, objArr7);
                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
            if (i) {
                jVar.e = bArr2.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e - 1) - jVar.c] + i2] - intValue);
                    try {
                        Object[] objArr8 = {jVar, jVar};
                        Object obj4 = o.e.a.s.get(745816316);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(10 - KeyEvent.normalizeMetaState(0), (char) TextUtils.indexOf("", "", 0), 255 - AndroidCharacter.getMirror('0'));
                            byte b5 = (byte) ($$g[0] - 1);
                            byte b6 = b5;
                            Object[] objArr9 = new Object[1];
                            x(b5, b6, b6, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr[0] = new String(cArr5);
                return;
            }
            switch (j) {
                case true:
                    jVar.e = cArr2.length;
                    char[] cArr6 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i2] - intValue);
                        try {
                            Object[] objArr10 = {jVar, jVar};
                            Object obj5 = o.e.a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 9, (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 207);
                                byte b7 = (byte) ($$g[0] - 1);
                                byte b8 = b7;
                                Object[] objArr11 = new Object[1];
                                x(b7, b8, b8, objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    }
                    objArr[0] = new String(cArr6);
                    return;
                default:
                    jVar.e = iArr.length;
                    char[] cArr7 = new char[jVar.e];
                    jVar.c = 0;
                    while (true) {
                        switch (jVar.c < jVar.e) {
                            case false:
                                objArr[0] = new String(cArr7);
                                return;
                            default:
                                cArr7[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                jVar.c++;
                        }
                    }
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
