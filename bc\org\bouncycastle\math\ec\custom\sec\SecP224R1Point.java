package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP224R1Point.smali */
public class SecP224R1Point extends ECPoint.AbstractFp {
    SecP224R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP224R1FieldElement secP224R1FieldElement = (SecP224R1FieldElement) this.b;
        SecP224R1FieldElement secP224R1FieldElement2 = (SecP224R1FieldElement) this.c;
        SecP224R1FieldElement secP224R1FieldElement3 = (SecP224R1FieldElement) eCPoint.getXCoord();
        SecP224R1FieldElement secP224R1FieldElement4 = (SecP224R1FieldElement) eCPoint.getYCoord();
        SecP224R1FieldElement secP224R1FieldElement5 = (SecP224R1FieldElement) this.d[0];
        SecP224R1FieldElement secP224R1FieldElement6 = (SecP224R1FieldElement) eCPoint.getZCoord(0);
        int[] b = v5.b();
        int[] a = v5.a();
        int[] a2 = v5.a();
        int[] a3 = v5.a();
        boolean isOne = secP224R1FieldElement5.isOne();
        if (isOne) {
            iArr = secP224R1FieldElement3.a;
            iArr2 = secP224R1FieldElement4.a;
        } else {
            SecP224R1Field.square(secP224R1FieldElement5.a, a2);
            SecP224R1Field.multiply(a2, secP224R1FieldElement3.a, a);
            SecP224R1Field.multiply(a2, secP224R1FieldElement5.a, a2);
            SecP224R1Field.multiply(a2, secP224R1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP224R1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP224R1FieldElement.a;
            iArr4 = secP224R1FieldElement2.a;
        } else {
            SecP224R1Field.square(secP224R1FieldElement6.a, a3);
            SecP224R1Field.multiply(a3, secP224R1FieldElement.a, b);
            SecP224R1Field.multiply(a3, secP224R1FieldElement6.a, a3);
            SecP224R1Field.multiply(a3, secP224R1FieldElement2.a, a3);
            iArr3 = b;
            iArr4 = a3;
        }
        int[] a4 = v5.a();
        SecP224R1Field.subtract(iArr3, iArr, a4);
        SecP224R1Field.subtract(iArr4, iArr2, a);
        if (v5.b(a4)) {
            return v5.b(a) ? twice() : curve.getInfinity();
        }
        SecP224R1Field.square(a4, a2);
        int[] a5 = v5.a();
        SecP224R1Field.multiply(a2, a4, a5);
        SecP224R1Field.multiply(a2, iArr3, a2);
        SecP224R1Field.negate(a5, a5);
        v5.c(iArr4, a5, b);
        SecP224R1Field.reduce32(v5.b(a2, a2, a5), a5);
        SecP224R1FieldElement secP224R1FieldElement7 = new SecP224R1FieldElement(a3);
        SecP224R1Field.square(a, secP224R1FieldElement7.a);
        int[] iArr5 = secP224R1FieldElement7.a;
        SecP224R1Field.subtract(iArr5, a5, iArr5);
        SecP224R1FieldElement secP224R1FieldElement8 = new SecP224R1FieldElement(a5);
        SecP224R1Field.subtract(a2, secP224R1FieldElement7.a, secP224R1FieldElement8.a);
        SecP224R1Field.multiplyAddToExt(secP224R1FieldElement8.a, a, b);
        SecP224R1Field.reduce(b, secP224R1FieldElement8.a);
        SecP224R1FieldElement secP224R1FieldElement9 = new SecP224R1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP224R1FieldElement9.a;
            SecP224R1Field.multiply(iArr6, secP224R1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP224R1FieldElement9.a;
            SecP224R1Field.multiply(iArr7, secP224R1FieldElement6.a, iArr7);
        }
        return new SecP224R1Point(curve, secP224R1FieldElement7, secP224R1FieldElement8, new ECFieldElement[]{secP224R1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP224R1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP224R1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP224R1FieldElement secP224R1FieldElement = (SecP224R1FieldElement) this.c;
        if (secP224R1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP224R1FieldElement secP224R1FieldElement2 = (SecP224R1FieldElement) this.b;
        SecP224R1FieldElement secP224R1FieldElement3 = (SecP224R1FieldElement) this.d[0];
        int[] a = v5.a();
        int[] a2 = v5.a();
        int[] a3 = v5.a();
        SecP224R1Field.square(secP224R1FieldElement.a, a3);
        int[] a4 = v5.a();
        SecP224R1Field.square(a3, a4);
        boolean isOne = secP224R1FieldElement3.isOne();
        int[] iArr = secP224R1FieldElement3.a;
        if (!isOne) {
            SecP224R1Field.square(iArr, a2);
            iArr = a2;
        }
        SecP224R1Field.subtract(secP224R1FieldElement2.a, iArr, a);
        SecP224R1Field.add(secP224R1FieldElement2.a, iArr, a2);
        SecP224R1Field.multiply(a2, a, a2);
        SecP224R1Field.reduce32(v5.b(a2, a2, a2), a2);
        SecP224R1Field.multiply(a3, secP224R1FieldElement2.a, a3);
        SecP224R1Field.reduce32(c6.c(7, a3, 2, 0), a3);
        SecP224R1Field.reduce32(c6.a(7, a4, 3, 0, a), a);
        SecP224R1FieldElement secP224R1FieldElement4 = new SecP224R1FieldElement(a4);
        SecP224R1Field.square(a2, secP224R1FieldElement4.a);
        int[] iArr2 = secP224R1FieldElement4.a;
        SecP224R1Field.subtract(iArr2, a3, iArr2);
        int[] iArr3 = secP224R1FieldElement4.a;
        SecP224R1Field.subtract(iArr3, a3, iArr3);
        SecP224R1FieldElement secP224R1FieldElement5 = new SecP224R1FieldElement(a3);
        SecP224R1Field.subtract(a3, secP224R1FieldElement4.a, secP224R1FieldElement5.a);
        int[] iArr4 = secP224R1FieldElement5.a;
        SecP224R1Field.multiply(iArr4, a2, iArr4);
        int[] iArr5 = secP224R1FieldElement5.a;
        SecP224R1Field.subtract(iArr5, a, iArr5);
        SecP224R1FieldElement secP224R1FieldElement6 = new SecP224R1FieldElement(a2);
        SecP224R1Field.twice(secP224R1FieldElement.a, secP224R1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP224R1FieldElement6.a;
            SecP224R1Field.multiply(iArr6, secP224R1FieldElement3.a, iArr6);
        }
        return new SecP224R1Point(curve, secP224R1FieldElement4, secP224R1FieldElement5, new ECFieldElement[]{secP224R1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP224R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
