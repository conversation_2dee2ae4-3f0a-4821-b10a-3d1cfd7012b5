package o.z;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.m;
import o.cf.i;
import o.cf.j;
import o.ee.o;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\e.smali */
public final class e extends b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int m;
    private static char[] n;

    /* renamed from: o, reason: collision with root package name */
    private static char f115o;
    String a;
    String b;
    o.h.d c;
    boolean d;
    c e;
    String f;
    boolean g;
    Long h;
    byte[] i;
    String j;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\e$d.smali */
    public interface d {
        void d(String str);

        void d(o.bb.d dVar, boolean z);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        k = 1;
        n();
        PointF.length(0.0f, 0.0f);
        View.resolveSizeAndState(0, 0, 0);
        Process.getElapsedCpuTime();
        Color.red(0);
        int i = m + 3;
        k = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{113, -79, 113, -105};
        $$e = Opcodes.DNEG;
    }

    static void n() {
        n = new char[]{30562, 30574, 30567, 30570, 30571, 30540, 30589, 30588, 30566, 30561, 30573, 30586, 30587, 30572, 30542, 30560};
        f115o = (char) 17041;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r8 = r8 * 2
            int r8 = 3 - r8
            int r7 = 73 - r7
            byte[] r0 = o.z.e.$$d
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            int r8 = r8 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.z.e.p(byte, byte, short, java.lang.Object[]):void");
    }

    public e(Context context, d dVar, o.ei.c cVar) {
        super(context, dVar, cVar, o.bb.e.r);
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        AsyncTaskC0046e asyncTaskC0046e = new AsyncTaskC0046e(this);
        int i = k + 89;
        m = i % 128;
        int i2 = i % 2;
        return asyncTaskC0046e;
    }

    @Override // o.y.b
    public final String a() {
        int i = k + 83;
        m = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        l((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 26, "\u000f\n\u000e\u0000\u0001\u000b\u0000\f\u0001\u0005\u000f\u0000\u0007\t\u0004\u000f\f\u0003\u0002\u0007\u0007\r㗽㗽\u0005\r㘄", (byte) (7 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = m + 43;
        k = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    public final void c(o.h.d dVar, String str, String str2, c cVar, String str3, byte[] bArr, boolean z, Long l) {
        int i = m + Opcodes.DREM;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                this.c = dVar;
                this.a = str;
                this.b = str2;
                this.e = cVar;
                this.d = z;
                this.f = str3;
                this.i = bArr;
                this.h = l;
                c();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.c = dVar;
                this.a = str;
                this.b = str2;
                this.e = cVar;
                this.d = z;
                this.f = str3;
                this.i = bArr;
                this.h = l;
                c();
                return;
        }
    }

    /* renamed from: o.z.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\e$e.smali */
    static final class AsyncTaskC0046e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static long b;
        private static char c;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            a = 1;
            c = (char) 17957;
            e = 161105445;
            b = -728598741570708075L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0037). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(byte r7, int r8, int r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 * 3
                int r7 = r7 + 1
                byte[] r0 = o.z.e.AsyncTaskC0046e.$$d
                int r9 = r9 + 99
                int r8 = r8 * 4
                int r8 = 3 - r8
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L18
                r9 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r8
                goto L37
            L18:
                r3 = r2
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L1d:
                int r9 = r9 + 1
                int r4 = r3 + 1
                byte r5 = (byte) r7
                r1[r3] = r5
                if (r4 != r8) goto L2e
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2e:
                r3 = r0[r9]
                r6 = r9
                r9 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r6
            L37:
                int r8 = -r8
                int r7 = r7 + r8
                r8 = r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.z.e.AsyncTaskC0046e.C(byte, int, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{38, -75, -91, -62};
            $$e = 90;
        }

        AsyncTaskC0046e(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = d + 63;
            a = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            B((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "讶搐ᙡ齴놐㽍쎲臦둣ꇩꔙꡍ僑\uf42b㣊菬\u2ef9ӑퟴ톒", (char) TextUtils.indexOf("", "", 0, 0), "㯤ꆋе金", "\udbb0Ż\ue508껽", objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = a + 83;
            d = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return intern;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final i c(Context context) {
            boolean c2 = c.c(((e) e()).e);
            h().a().c(c2);
            a aVar = new a(g(), c2);
            int i = a + 89;
            d = i % 128;
            switch (i % 2 != 0 ? (char) 26 : (char) 14) {
                case 14:
                    return aVar;
                default:
                    int i2 = 13 / 0;
                    return aVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            int i;
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            B(2130011849 - TextUtils.indexOf("", "", 0, 0), "袻枧赈\u0df5ꔿろ⼌𥉉븥", (char) View.getDefaultSize(0, 0), "즊\uf566\ud97e忂", "\udbb0Ż\ue508껽", objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).e.toString());
            Object[] objArr2 = new Object[1];
            B((ViewConfiguration.getTapTimeout() >> 16) - 574481546, "珽쨁ﷻ荜䀲㐃볻똵ஸ", (char) KeyEvent.getDeadChar(0, 0), "瘥숛᳝㑹", "\udbb0Ż\ue508껽", objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((e) e()).b);
            Object[] objArr3 = new Object[1];
            B(Color.green(0), "뀬鉊蔛㲟딭虦", (char) (8020 - Color.alpha(0)), "䢓釈呍\uf41f", "\udbb0Ż\ue508껽", objArr3);
            String intern = ((String) objArr3[0]).intern();
            if (((e) e()).d) {
                int i2 = d + 15;
                a = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 19 : (char) 16) {
                    case 19:
                        i = 1;
                        break;
                    default:
                        i = 0;
                        break;
                }
            } else {
                i = 0;
            }
            bVar.d(intern, i);
            Object[] objArr4 = new Object[1];
            B((-1811392452) - TextUtils.lastIndexOf("", '0', 0, 0), "锶㮕냛驩㭷ꧺ晝", (char) KeyEvent.normalizeMetaState(0), "㶴ࡘ窔\ufaee", "\udbb0Ż\ue508껽", objArr4);
            bVar.d(((String) objArr4[0]).intern(), ((e) e()).f);
            switch (((e) e()).i != null) {
                default:
                    int i3 = d + Opcodes.DREM;
                    a = i3 % 128;
                    int i4 = i3 % 2;
                    Object[] objArr5 = new Object[1];
                    B(TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1, "稜\u2fd6褌㉲렷倎鄝ˇ눢\uee13", (char) TextUtils.indexOf("", "", 0), "\ue4e5㌙⨂ꬠ", "\udbb0Ż\ue508껽", objArr5);
                    bVar.d(((String) objArr5[0]).intern(), Base64.encodeToString(((e) e()).i, 10));
                    int i5 = d + 13;
                    a = i5 % 128;
                    int i6 = i5 % 2;
                case false:
                    return bVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            int i = d + 65;
            a = i % 128;
            int i2 = i % 2;
            switch (((e) e()).d ? (char) 26 : '\r') {
                case '\r':
                    return new j(((e) e()).a, c.c(((e) e()).e), ((e) e()).c);
                default:
                    int i3 = d + 79;
                    a = i3 % 128;
                    Object obj = null;
                    switch (i3 % 2 == 0 ? (char) 16 : (char) 28) {
                        case 16:
                            obj.hashCode();
                            throw null;
                        default:
                            return null;
                    }
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a;
            int i2 = i + 51;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + Opcodes.DSUB;
            d = i4 % 128;
            Object obj = null;
            switch (i4 % 2 != 0) {
                case false:
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = a + Opcodes.LNEG;
            d = i % 128;
            int i2 = i % 2;
            e eVar = (e) e();
            Object[] objArr = new Object[1];
            B((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, "㥐攮⣴⌲멌㍅\ue781䙒妄巙", (char) (ViewConfiguration.getJumpTapTimeout() >> 16), "ʣ謝飙䩢", "\udbb0Ż\ue508껽", objArr);
            eVar.j = bVar.q(((String) objArr[0]).intern());
            int i3 = d + 3;
            a = i3 % 128;
            switch (i3 % 2 != 0 ? 'b' : (char) 16) {
                case Opcodes.FADD /* 98 */:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 77;
            a = i % 128;
            int i2 = i % 2;
            e eVar = (e) e();
            Object[] objArr = new Object[1];
            B(1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "懕᭟ᗤ\uf4b0\uf097\ud873棕嶐㧛ꌧ퉶ﯢ\u061c좢奚쒿ဤ樥ꎳǽ籊쁚ﴓ褣䴕諊囑덡\u2072\udc93㉍삯", (char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 62163), "苜凷펗㏲", "\udbb0Ż\ue508껽", objArr);
            eVar.g = bVar.b(((String) objArr[0]).intern(), Boolean.FALSE).booleanValue();
            int i3 = d + 91;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // o.y.c
        public final void t() {
            int i = a + 59;
            d = i % 128;
            boolean z = i % 2 == 0;
            w();
            switch (z) {
                case false:
                    int i2 = 9 / 0;
                    break;
            }
            int i3 = a + 71;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // o.y.c
        public final void q() {
            int i = d + 33;
            a = i % 128;
            int i2 = i % 2;
            w();
            int i3 = d + 83;
            a = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 6 : '^') {
                case 6:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        private void w() {
            int i = a + 17;
            d = i % 128;
            if (i % 2 != 0) {
                Long l = ((e) e()).h;
                throw null;
            }
            if (((e) e()).h == null) {
                return;
            }
            switch (!((e) e()).g ? '1' : Typography.amp) {
                case '1':
                    int i2 = d + 85;
                    a = i2 % 128;
                    if (i2 % 2 == 0) {
                        boolean z = ((e) e()).d;
                        throw null;
                    }
                    switch (!((e) e()).d ? '\\' : '`') {
                        case Opcodes.IADD /* 96 */:
                            break;
                        default:
                            if (!((e) e()).d().b()) {
                                switch (o.a.c(((e) e()).d().d(), o.bb.a.aC, o.bb.a.ax, o.bb.a.aB) ? false : true) {
                                    case true:
                                        return;
                                }
                            }
                            break;
                    }
            }
            ((e) e()).d().a().d(((e) e()).h);
            o.dg.b.b(((e) e()).b);
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 49;
            a = i % 128;
            int i2 = i % 2;
            ((e) e()).j().d(((e) e()).j);
            int i3 = a + 39;
            d = i3 % 128;
            switch (i3 % 2 != 0 ? '\\' : (char) 31) {
                case 31:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = d + 19;
            a = i % 128;
            int i2 = i % 2;
            ((e) e()).j().d(dVar, ((e) e()).g);
            int i3 = d + 63;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        private static void B(int i, String str, char c2, String str2, String str3, Object[] objArr) {
            char[] charArray;
            char[] cArr;
            int i2;
            int i3 = $10 + 57;
            $11 = i3 % 128;
            int i4 = 2;
            int i5 = i3 % 2;
            switch (str3 != null ? '\f' : '@') {
                case '\f':
                    charArray = str3.toCharArray();
                    break;
                default:
                    charArray = str3;
                    break;
            }
            char[] cArr2 = charArray;
            char[] charArray2 = str2 != null ? str2.toCharArray() : str2;
            int i6 = 0;
            switch (str != null) {
                case false:
                    cArr = str;
                    break;
                default:
                    int i7 = $10 + 37;
                    $11 = i7 % 128;
                    if (i7 % 2 == 0) {
                    }
                    cArr = str.toCharArray();
                    break;
            }
            o.a.o oVar = new o.a.o();
            int length = charArray2.length;
            char[] cArr3 = new char[length];
            int length2 = cArr2.length;
            char[] cArr4 = new char[length2];
            System.arraycopy(charArray2, 0, cArr3, 0, length);
            System.arraycopy(cArr2, 0, cArr4, 0, length2);
            cArr3[0] = (char) (cArr3[0] ^ c2);
            cArr4[2] = (char) (cArr4[2] + ((char) i));
            int length3 = cArr.length;
            char[] cArr5 = new char[length3];
            oVar.e = 0;
            while (oVar.e < length3) {
                int i8 = $11 + 9;
                $10 = i8 % 128;
                int i9 = i8 % i4;
                try {
                    Object[] objArr2 = {oVar};
                    Object obj = o.e.a.s.get(-429442487);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(View.combineMeasuredStates(i6, i6) + 10, (char) (20954 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), (ViewConfiguration.getEdgeSlop() >> 16) + 344);
                        byte b2 = (byte) i6;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        C(b2, b3, b3, objArr3);
                        String str4 = (String) objArr3[i6];
                        Class<?>[] clsArr = new Class[1];
                        clsArr[i6] = Object.class;
                        obj = cls.getMethod(str4, clsArr);
                        o.e.a.s.put(-429442487, obj);
                    }
                    int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                    try {
                        Object[] objArr4 = {oVar};
                        Object obj2 = o.e.a.s.get(-515165572);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (ViewConfiguration.getPressedStateDuration() >> 16), 207 - View.combineMeasuredStates(i6, i6));
                            byte b4 = (byte) i6;
                            byte b5 = b4;
                            Object[] objArr5 = new Object[1];
                            C(b4, b5, (byte) (b5 + 2), objArr5);
                            String str5 = (String) objArr5[i6];
                            Class<?>[] clsArr2 = new Class[1];
                            clsArr2[i6] = Object.class;
                            obj2 = cls2.getMethod(str5, clsArr2);
                            o.e.a.s.put(-515165572, obj2);
                        }
                        int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                        int i10 = cArr3[oVar.e % 4] * 32718;
                        try {
                            Object[] objArr6 = new Object[3];
                            objArr6[2] = Integer.valueOf(cArr4[intValue]);
                            objArr6[1] = Integer.valueOf(i10);
                            objArr6[i6] = oVar;
                            Object obj3 = o.e.a.s.get(-1614232674);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(11 - ExpandableListView.getPackedPositionType(0L), (char) (TextUtils.indexOf((CharSequence) "", '0', i6, i6) + 1), (-16776935) - Color.rgb(i6, i6, i6));
                                byte b6 = (byte) i6;
                                Object[] objArr7 = new Object[1];
                                C(b6, b6, (byte) $$d.length, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-1614232674, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr3[intValue2] * 32718), Integer.valueOf(cArr4[intValue])};
                                Object obj4 = o.e.a.s.get(406147795);
                                if (obj4 != null) {
                                    i2 = 2;
                                } else {
                                    Class cls4 = (Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 18, (char) (14687 - TextUtils.getOffsetBefore("", 0)), 112 - Color.red(0));
                                    byte b7 = (byte) 0;
                                    byte b8 = b7;
                                    Object[] objArr9 = new Object[1];
                                    C(b7, b8, (byte) (b8 | 7), objArr9);
                                    i2 = 2;
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(406147795, obj4);
                                }
                                cArr4[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                cArr3[intValue2] = oVar.d;
                                cArr5[oVar.e] = (char) ((((cArr3[intValue2] ^ r5[oVar.e]) ^ (b ^ 6565854932352255525L)) ^ ((int) (e ^ 6565854932352255525L))) ^ ((char) (c ^ 6565854932352255525L)));
                                oVar.e++;
                                i4 = i2;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = new String(cArr5);
        }
    }

    private static void l(int i, String str, byte b, Object[] objArr) {
        int i2;
        char c;
        int length;
        char[] cArr;
        int i3;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr2 = n;
        long j = 0;
        switch (cArr2 != null ? (char) 18 : 'H') {
            case 'H':
                break;
            default:
                int i4 = $10 + 71;
                $11 = i4 % 128;
                switch (i4 % 2 == 0 ? Typography.dollar : '-') {
                    case '-':
                        length = cArr2.length;
                        cArr = new char[length];
                        i3 = 0;
                        break;
                    default:
                        length = cArr2.length;
                        cArr = new char[length];
                        i3 = 0;
                        break;
                }
                while (true) {
                    switch (i3 < length ? (char) 3 : 'a') {
                        case 3:
                            try {
                                Object[] objArr2 = {Integer.valueOf(cArr2[i3])};
                                Object obj = o.e.a.s.get(-1401577988);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(18 - (SystemClock.uptimeMillis() > j ? 1 : (SystemClock.uptimeMillis() == j ? 0 : -1)), (char) (ViewConfiguration.getTapTimeout() >> 16), 76 - View.combineMeasuredStates(0, 0));
                                    byte b2 = (byte) 0;
                                    byte b3 = b2;
                                    Object[] objArr3 = new Object[1];
                                    p(b2, b3, b3, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(-1401577988, obj);
                                }
                                cArr[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i3++;
                                j = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr2 = cArr;
                            break;
                    }
                }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(f115o)};
            Object obj2 = o.e.a.s.get(-1401577988);
            char c2 = '0';
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c('A' - AndroidCharacter.getMirror('0'), (char) Color.blue(0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 77);
                byte b4 = (byte) 0;
                byte b5 = b4;
                Object[] objArr5 = new Object[1];
                p(b4, b5, b5, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i];
            switch (i % 2 != 0) {
                case false:
                    i2 = i;
                    break;
                default:
                    int i5 = $10 + 75;
                    $11 = i5 % 128;
                    if (i5 % 2 == 0) {
                        i2 = i + 2;
                        cArr3[i2] = (char) (charArray[i2] >>> b);
                        break;
                    } else {
                        i2 = i - 1;
                        cArr3[i2] = (char) (charArray[i2] - b);
                        break;
                    }
            }
            if (i2 > 1) {
                int i6 = $10 + 65;
                $11 = i6 % 128;
                int i7 = i6 % 2;
                mVar.b = 0;
                while (true) {
                    switch (mVar.b < i2 ? '*' : 'S') {
                        case Opcodes.AASTORE /* 83 */:
                            break;
                        default:
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                int i8 = $11 + Opcodes.LNEG;
                                $10 = i8 % 128;
                                if (i8 % 2 != 0) {
                                    cArr3[mVar.b] = (char) (mVar.e << b);
                                    cArr3[mVar.b >>> 1] = (char) (mVar.a * b);
                                } else {
                                    cArr3[mVar.b] = (char) (mVar.e - b);
                                    cArr3[mVar.b + 1] = (char) (mVar.a - b);
                                }
                                c = c2;
                            } else {
                                try {
                                    Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 10, (char) (8857 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), KeyEvent.normalizeMetaState(0) + 324);
                                        byte length2 = (byte) $$d.length;
                                        Object[] objArr7 = new Object[1];
                                        p((byte) 0, length2, (byte) (length2 - 4), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        case false:
                                            if (mVar.c == mVar.d) {
                                                mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                int i9 = (mVar.c * charValue) + mVar.i;
                                                int i10 = (mVar.d * charValue) + mVar.h;
                                                cArr3[mVar.b] = cArr2[i9];
                                                cArr3[mVar.b + 1] = cArr2[i10];
                                                c = '0';
                                                break;
                                            } else {
                                                int i11 = (mVar.c * charValue) + mVar.h;
                                                int i12 = (mVar.d * charValue) + mVar.i;
                                                cArr3[mVar.b] = cArr2[i11];
                                                cArr3[mVar.b + 1] = cArr2[i12];
                                                c = '0';
                                                break;
                                            }
                                        default:
                                            try {
                                                Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                Object obj4 = o.e.a.s.get(1075449051);
                                                if (obj4 != null) {
                                                    c = '0';
                                                } else {
                                                    c = '0';
                                                    Class cls4 = (Class) o.e.a.c(Color.rgb(0, 0, 0) + 16777227, (char) (TextUtils.lastIndexOf("", '0') + 1), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 65);
                                                    byte b6 = (byte) 0;
                                                    byte b7 = (byte) (b6 + 3);
                                                    Object[] objArr9 = new Object[1];
                                                    p(b6, b7, (byte) (b7 - 3), objArr9);
                                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(1075449051, obj4);
                                                }
                                                int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                int i13 = (mVar.d * charValue) + mVar.h;
                                                cArr3[mVar.b] = cArr2[intValue];
                                                cArr3[mVar.b + 1] = cArr2[i13];
                                                break;
                                            } catch (Throwable th2) {
                                                Throwable cause2 = th2.getCause();
                                                if (cause2 == null) {
                                                    throw th2;
                                                }
                                                throw cause2;
                                            }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                            c2 = c;
                    }
                }
            }
            for (int i14 = 0; i14 < i; i14++) {
                cArr3[i14] = (char) (cArr3[i14] ^ 13722);
            }
            objArr[0] = new String(cArr3);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
