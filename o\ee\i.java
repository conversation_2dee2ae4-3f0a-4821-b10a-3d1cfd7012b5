package o.ee;

import android.content.Intent;
import android.graphics.Color;
import android.os.Process;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\i.smali */
public final class i implements d<AndroidActivityResultCallback> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char c;
    private static char[] e;
    private static int f;
    private final Map<Integer, a> b = new HashMap();
    private boolean d = false;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\i$a.smali */
    public interface a {
        void onActivityResult(int i, Intent intent);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        f = 1;
        c();
        View.resolveSizeAndState(0, 0, 0);
        View.resolveSizeAndState(0, 0, 0);
        int i = f + Opcodes.LSHL;
        a = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = new char[]{30564, 30573, 30585, 30534, 30557, 30588, 30569, 30587, 30498, 30570, 30568, 30566, 30563, 30591, 30586, 30511, 30540, 30542, 30582, 30571, 30574, 30560, 30572, 30561, 30589};
        c = (char) 17040;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 69
            byte[] r0 = o.ee.i.$$a
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r6 = r6 * 2
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.i.h(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -127, -25, -85};
        $$b = Opcodes.I2B;
    }

    @Override // o.ee.d
    public final /* synthetic */ AndroidActivityResultCallback a() {
        int i = a + 45;
        f = i % 128;
        switch (i % 2 == 0 ? 'N' : Typography.less) {
            case '<':
                return e();
            default:
                e();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void e(Integer num, a aVar) {
        int i = f + 33;
        a = i % 128;
        int i2 = i % 2;
        this.b.put(num, aVar);
        int i3 = a + 39;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    public final boolean c(int i, int i2, Intent intent) {
        if (this.d) {
            g.c();
            Object[] objArr = new Object[1];
            g(KeyEvent.keyCodeFromString("") + 34, "\b\u0003\u0018\b\u0016\u0013\u0018\u0012\u0014\u0016\u000e\u0010\u0016\u0002\u0006\f\u0001\f\b\u0011\t\u000e\t\n\u0011\f\u000f\u0015㘨㘨\u0000\u0015\u0014\u0002", (byte) (50 - View.resolveSize(0, 0)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g((Process.myPid() >> 22) + 34, "\u0016\u0018\u0016\u0002\u0006\f\u0001\f\b\u0011\t\u000e\t\n\u0011\f\u0012\u0005\u0010\u0012\u000e\u0016\u0005\u0018\u000f\u0013\u0012\n\u0014\u0016\u0018\u0007\u000e\u0018", (byte) (View.MeasureSpec.getSize(0) + 84), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            return false;
        }
        Iterator<Map.Entry<Integer, a>> it = this.b.entrySet().iterator();
        int i3 = f + 85;
        a = i3 % 128;
        int i4 = i3 % 2;
        while (it.hasNext()) {
            int i5 = a + 81;
            f = i5 % 128;
            if (i5 % 2 == 0) {
                it.next().getKey().intValue();
                Object obj = null;
                obj.hashCode();
                throw null;
            }
            Map.Entry<Integer, a> next = it.next();
            switch (i == next.getKey().intValue()) {
                case true:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    g(34 - View.resolveSizeAndState(0, 0, 0), "\b\u0003\u0018\b\u0016\u0013\u0018\u0012\u0014\u0016\u000e\u0010\u0016\u0002\u0006\f\u0001\f\b\u0011\t\u000e\t\n\u0011\f\u000f\u0015㘨㘨\u0000\u0015\u0014\u0002", (byte) (50 - KeyEvent.getDeadChar(0, 0)), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    g(Color.rgb(0, 0, 0) + 16777260, "\u0016\u0018\u0016\u0002\u0006\f\u0001\f\b\u0011\t\u000e\t\n\u0011\f\u0012\u0005\u0013\u0014\u0005\u000e\n\u0006\b\u0005\u0004\u000e\u000f\u0010\u0017\u0015㙔㙔\u0000\u0015\u0014\u0002\u0010\u0005\u0018\u000b\u0018\u0012", (byte) (94 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), objArr4);
                    g.d(intern2, ((String) objArr4[0]).intern());
                    next.getValue().onActivityResult(i2, intent);
                    this.d = true;
                    return true;
            }
        }
        return false;
    }

    public final AndroidActivityResultCallback e() {
        AndroidActivityResultCallback androidActivityResultCallback = new AndroidActivityResultCallback(this);
        int i = a + 109;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                return androidActivityResultCallback;
            default:
                int i2 = 82 / 0;
                return androidActivityResultCallback;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:32:0x0052, code lost:
    
        r6 = r13;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r29, java.lang.String r30, byte r31, java.lang.Object[] r32) {
        /*
            Method dump skipped, instructions count: 1072
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.i.g(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
