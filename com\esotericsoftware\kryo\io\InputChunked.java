package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.minlog.Log;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\InputChunked.smali */
public class InputChunked extends Input {
    private int chunkSize;

    public InputChunked() {
        this.chunkSize = -1;
    }

    public InputChunked(int bufferSize) {
        super(bufferSize);
        this.chunkSize = -1;
    }

    public InputChunked(InputStream inputStream) {
        super(inputStream);
        this.chunkSize = -1;
    }

    public InputChunked(InputStream inputStream, int bufferSize) {
        super(inputStream, bufferSize);
        this.chunkSize = -1;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setInputStream(InputStream inputStream) {
        super.setInputStream(inputStream);
        this.chunkSize = -1;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setBuffer(byte[] bytes, int offset, int count) {
        super.setBuffer(bytes, offset, count);
        this.chunkSize = -1;
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream, com.esotericsoftware.kryo.util.Pool.Poolable
    public void reset() {
        super.reset();
        this.chunkSize = -1;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    protected int fill(byte[] buffer, int offset, int count) throws KryoException {
        int i = this.chunkSize;
        if (i == -1) {
            if (!readChunkSize()) {
                return -1;
            }
        } else if (i == 0) {
            return -1;
        }
        int actual = super.fill(buffer, offset, Math.min(this.chunkSize, count));
        int i2 = this.chunkSize - actual;
        this.chunkSize = i2;
        if (i2 != 0 || readChunkSize()) {
            return actual;
        }
        return -1;
    }

    private boolean readChunkSize() {
        try {
            InputStream inputStream = getInputStream();
            int result = 0;
            for (int offset = 0; offset < 32; offset += 7) {
                int b = inputStream.read();
                if (b == -1) {
                    return false;
                }
                result |= (b & 127) << offset;
                if ((b & 128) == 0) {
                    this.chunkSize = result;
                    if (!Log.TRACE || this.chunkSize <= 0) {
                        return true;
                    }
                    Log.trace("kryo", "Read chunk: " + this.chunkSize);
                    return true;
                }
            }
            throw new KryoException("Unable to read chunk size: malformed integer");
        } catch (IOException ex) {
            throw new KryoException("Unable to read chunk size.", ex);
        }
    }

    public void nextChunk() {
        this.position = this.limit;
        if (this.chunkSize == -1) {
            readChunkSize();
        }
        while (true) {
            int i = this.chunkSize;
            if (i <= 0) {
                break;
            } else {
                skip(i);
            }
        }
        this.chunkSize = -1;
        if (Log.TRACE) {
            Log.trace("kryo", "Next chunk.");
        }
    }
}
