package o.bv;

import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\a.smali */
public final class a {
    private static int d = 0;
    private static int f = 1;
    private final byte[] a;
    private final byte[] b;
    private final boolean c = true;
    private final int e;

    public a(byte[] bArr, byte[] bArr2) {
        this.a = bArr;
        this.b = bArr2;
        this.e = bArr.length;
    }

    public final byte[] a() {
        int i = d + 69;
        f = i % 128;
        int i2 = i % 2;
        ByteBuffer allocate = ByteBuffer.allocate(this.e);
        allocate.put(this.a);
        byte[] array = allocate.array();
        switch (this.c ? 'U' : '7') {
            default:
                int i3 = (f + 100) - 1;
                d = i3 % 128;
                switch (i3 % 2 != 0 ? '8' : 'E') {
                    case 'E':
                        switch (!c(array, this.b)) {
                            case false:
                                break;
                            default:
                                byte[] bArr = new byte[0];
                                int i4 = (d + 102) - 1;
                                f = i4 % 128;
                                int i5 = i4 % 2;
                                return bArr;
                        }
                    default:
                        c(array, this.b);
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            case '7':
                return array;
        }
    }

    public final byte[] e() {
        int i = d;
        int i2 = (i ^ 67) + ((i & 67) << 1);
        f = i2 % 128;
        int i3 = i2 % 2;
        ByteBuffer allocate = ByteBuffer.allocate(this.e << 1);
        allocate.put(this.a).put(this.b);
        byte[] array = allocate.array();
        int i4 = d + 71;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return array;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:41:0x006c. Please report as an issue. */
    private static boolean b(byte[] bArr, byte[] bArr2) {
        int i = f;
        int i2 = (i & 67) + (i | 67);
        d = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? '5' : '+') {
            case Opcodes.SALOAD /* 53 */:
                obj.hashCode();
                throw null;
            default:
                if (bArr != null) {
                    switch (bArr2 == null) {
                        case false:
                            switch (bArr.length == bArr2.length) {
                                case false:
                                    int i3 = i + 79;
                                    int i4 = i3 % 128;
                                    d = i4;
                                    int i5 = i3 % 2;
                                    int i6 = i4 + 89;
                                    f = i6 % 128;
                                    switch (i6 % 2 == 0 ? (char) 27 : (char) 22) {
                                        case 27:
                                            throw null;
                                        default:
                                            return false;
                                    }
                                default:
                                    int i7 = (i ^ Opcodes.LSHL) + ((i & Opcodes.LSHL) << 1);
                                    d = i7 % 128;
                                    switch (i7 % 2 != 0 ? 'U' : ',') {
                                    }
                                    int i8 = 0;
                                    while (i8 < bArr.length) {
                                        int i9 = f;
                                        int i10 = (i9 & 85) + (i9 | 85);
                                        d = i10 % 128;
                                        switch (i10 % 2 == 0) {
                                            case false:
                                                bArr[i8] = (byte) (bArr[i8] | bArr2[i8]);
                                                i8 += 79;
                                                break;
                                            default:
                                                byte b = bArr[i8];
                                                byte b2 = bArr2[i8];
                                                bArr[i8] = (byte) ((b | b2) & (~(b & b2)));
                                                i8 = (i8 & 1) + (i8 | 1);
                                                break;
                                        }
                                    }
                                    return true;
                            }
                    }
                }
                int i11 = ((i | 99) << 1) - (i ^ 99);
                d = i11 % 128;
                switch (i11 % 2 != 0 ? (char) 31 : (char) 24) {
                    case 24:
                        return false;
                    default:
                        throw null;
                }
        }
    }

    private static boolean c(byte[] bArr, byte[] bArr2) {
        int i = d;
        int i2 = (i ^ 77) + ((i & 77) << 1);
        f = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 3 : '.') {
            case '.':
                return b(bArr, bArr2);
            default:
                b(bArr, bArr2);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static void a(byte[] bArr) {
        int i = d + 79;
        f = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                if (bArr == null) {
                    return;
                }
                Arrays.fill(bArr, (byte) 0);
                int i2 = (d + 58) - 1;
                f = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static void c(byte[][] r6) {
        /*
            int r0 = o.bv.a.d
            r1 = r0 ^ 121(0x79, float:1.7E-43)
            r0 = r0 & 121(0x79, float:1.7E-43)
            r2 = 1
            int r0 = r0 << r2
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.bv.a.f = r0
            int r1 = r1 % 2
            r0 = 0
            if (r1 != 0) goto L14
            r1 = r2
            goto L15
        L14:
            r1 = r0
        L15:
            switch(r1) {
                case 1: goto L1b;
                default: goto L18;
            }
        L18:
            if (r6 != 0) goto L24
        L1a:
            goto L23
        L1b:
            r1 = 46
            int r1 = r1 / r0
            if (r6 != 0) goto L24
            goto L1a
        L21:
            r6 = move-exception
            throw r6
        L23:
            return
        L24:
            int r1 = r6.length
            r3 = r0
        L26:
            if (r3 >= r1) goto L2a
            r4 = r2
            goto L2b
        L2a:
            r4 = r0
        L2b:
            switch(r4) {
                case 0: goto L3f;
                default: goto L2e;
            }
        L2e:
            int r4 = o.bv.a.d
            r5 = r4 | 15
            int r5 = r5 << r2
            r4 = r4 ^ 15
            int r5 = r5 - r4
            int r4 = r5 % 128
            o.bv.a.f = r4
            int r5 = r5 % 2
            if (r5 != 0) goto L50
            goto L4e
        L3f:
            int r6 = o.bv.a.f
            r0 = r6 & 33
            r6 = r6 | 33
            int r0 = r0 + r6
            int r6 = r0 % 128
            o.bv.a.d = r6
            int r0 = r0 % 2
            return
        L4e:
            r4 = r0
            goto L51
        L50:
            r4 = r2
        L51:
            switch(r4) {
                case 0: goto L65;
                default: goto L54;
            }
        L54:
            r4 = r6[r3]
            a(r4)
            r4 = r3 ^ (-46)
            r3 = r3 & (-46)
            int r3 = r3 << r2
            int r4 = r4 + r3
            r3 = r4 | 47
            int r3 = r3 << r2
            r4 = r4 ^ 47
            goto L75
        L65:
            r4 = r6[r3]
            a(r4)
            r4 = r3 | 36
            int r4 = r4 << r2
            r3 = r3 ^ 36
            int r4 = r4 - r3
            r3 = r4 | 57
            int r3 = r3 << r2
            r4 = r4 ^ 57
        L75:
            int r3 = r3 - r4
            int r4 = o.bv.a.f
            r5 = r4 & 59
            r4 = r4 | 59
            int r5 = r5 + r4
            int r4 = r5 % 128
            o.bv.a.d = r4
            int r5 = r5 % 2
            if (r5 == 0) goto L87
            r4 = r2
            goto L88
        L87:
            r4 = r0
        L88:
            switch(r4) {
                case 0: goto L8b;
                default: goto L8b;
            }
        L8b:
            goto L26
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.a.c(byte[][]):void");
    }
}
