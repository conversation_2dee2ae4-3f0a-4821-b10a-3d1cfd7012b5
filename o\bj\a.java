package o.bj;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.work.WorkRequest;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeoutException;
import o.d.a$31117$c;
import o.ee.g;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bj\a.smali */
public final class a implements a$31117$c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static final a d;
    private static int f;
    private static byte[] g;
    private static int h;
    private static int i;
    private static short[] j;
    private static int n;
    private int b = 0;
    private final Object e = new Object();
    private boolean a = false;

    static void c() {
        g = new byte[]{113, -120, 123, -105, 112, 119, -127, -89, 86, -117, -122, -109, -59, -15, -88, 11, -63, -60, -39, 26, -53, -6, 40, 31, 82, 54, 39, 86, 24, 6, -63, 121, 2, 5, 42, -13, 121, 54, 43, 80, 39, -26, 20, 84, 26, 108, 6, 82, -7, 88, 2, 5, 10, 71, 24, 39, 41, 38, -120, 21, -79, 43, -7, 63, -77, 74, 109, 35, 62, 91, -100, 45, PSSSigner.TRAILER_IMPLICIT, 65, 46, 87, 33, 65, 13, 100, 3, 93, 64, 85, 18, 67, 50, -6, 68, -75, -10, -26, 68, 77, 75, -9, 90, 23, 65, -2, 75, 71, 90, -19, -10, 79, -4, 88, -9, -8, 70, 40, 25, 76, 73, 84, -19, -10, -112, -112, -112, -112, -112, -112};
        i = 909053598;
        f = -1279189494;
        c = -1734330442;
    }

    static void init$0() {
        $$a = new byte[]{96, 104, -93, 9};
        $$b = Opcodes.DREM;
    }

    static void init$1() {
        $$d = new byte[]{79, 74, -126, -127};
        $$e = 0;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = 1 - r9
            byte[] r0 = o.bj.a.$$a
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r8 = r8 * 3
            int r8 = 98 - r8
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L1a
            r8 = r7
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L37
        L1a:
            r3 = r2
            r6 = r9
            r9 = r8
        L1d:
            r8 = r6
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2d
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2d:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L37:
            int r7 = r7 + r10
            int r8 = r8 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bj.a.l(short, byte, byte, java.lang.Object[]):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bj.a.$$d
            int r8 = r8 * 3
            int r8 = 4 - r8
            int r6 = r6 * 2
            int r6 = 110 - r6
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1b:
            r3 = r2
            r5 = r8
            r8 = r6
            r6 = r5
        L1f:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r9
            r9 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L37:
            int r6 = -r6
            int r8 = r8 + r6
            int r6 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bj.a.m(int, int, byte, java.lang.Object[]):void");
    }

    static {
        init$1();
        $10 = 0;
        $11 = 1;
        init$0();
        h = 0;
        n = 1;
        c();
        Process.getGidForName("");
        ViewConfiguration.getScrollBarSize();
        ViewConfiguration.getFadingEdgeLength();
        View.getDefaultSize(0, 0);
        ViewConfiguration.getFadingEdgeLength();
        d = new a();
        int i2 = n + 15;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    public static a a() {
        int i2 = h;
        int i3 = i2 + 39;
        n = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                a aVar = d;
                int i4 = i2 + Opcodes.LSUB;
                n = i4 % 128;
                int i5 = i4 % 2;
                return aVar;
            default:
                throw null;
        }
    }

    private a() {
    }

    public final int c(Context context) throws TimeoutException {
        g.c();
        Object[] objArr = new Object[1];
        k((byte) ((Process.myTid() >> 22) + 26), 1366345435 + TextUtils.indexOf((CharSequence) "", '0', 0), (short) (4 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (-1) - ExpandableListView.getPackedPositionType(0L), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 2047995832, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((byte) ((ViewConfiguration.getTouchSlop() >> 8) - 12), 1366345446 - View.MeasureSpec.getMode(0), (short) ((-96) - ExpandableListView.getPackedPositionType(0L)), (KeyEvent.getMaxKeyCode() >> 16) - 3, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 2047995854, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        synchronized (this.e) {
            if (this.a) {
                return this.b;
            }
            g.c();
            Object[] objArr3 = new Object[1];
            k((byte) (27 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1366345434, (short) (4 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (-1) - TextUtils.indexOf("", "", 0), TextUtils.getOffsetAfter("", 0) + 2047995832, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + Opcodes.FDIV), 1366345457 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) ((-40) - TextUtils.lastIndexOf("", '0', 0, 0)), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 21, 2047995853 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            try {
                Object[] objArr5 = {context, this};
                Object obj = o.e.a.s.get(2126540919);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 10, (char) (ViewConfiguration.getEdgeSlop() >> 16), 64 - TextUtils.lastIndexOf("", '0', 0, 0));
                    byte b = (byte) 0;
                    byte b2 = b;
                    Object[] objArr6 = new Object[1];
                    l(b, b2, b2, objArr6);
                    obj = cls.getMethod((String) objArr6[0], Context.class, a$31117$c.class);
                    o.e.a.s.put(2126540919, obj);
                }
                ((Method) obj).invoke(null, objArr5);
                if (!this.a) {
                    try {
                        this.e.wait(WorkRequest.DEFAULT_BACKOFF_DELAY_MILLIS);
                        g.c();
                        Object[] objArr7 = new Object[1];
                        k((byte) (26 - TextUtils.getOffsetAfter("", 0)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1366345434, (short) (KeyEvent.getDeadChar(0, 0) + 4), TextUtils.getTrimmedLength("") - 1, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 2047995833, objArr7);
                        String intern3 = ((String) objArr7[0]).intern();
                        Object[] objArr8 = new Object[1];
                        k((byte) (((Process.getThreadPriority(0) + 20) >> 6) + 64), 1366345489 - Process.getGidForName(""), (short) ((-110) - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), ExpandableListView.getPackedPositionChild(0L) + 5, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 2047995852, objArr8);
                        g.d(intern3, ((String) objArr8[0]).intern());
                    } catch (InterruptedException e) {
                        throw new TimeoutException();
                    }
                }
                return this.b;
            } catch (Throwable th) {
                Throwable cause = th.getCause();
                if (cause != null) {
                    throw cause;
                }
                throw th;
            }
        }
    }

    public final int e() {
        int i2 = n + 37;
        h = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((byte) (27 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 1366345434 - View.MeasureSpec.getSize(0), (short) (4 - (ViewConfiguration.getTouchSlop() >> 8)), -(ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), View.MeasureSpec.getMode(0) + 2047995832, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((byte) (TextUtils.indexOf("", "", 0) + 22), 1366345507 - (Process.myTid() >> 22), (short) (70 - (ViewConfiguration.getWindowTouchSlop() >> 8)), 1 - ExpandableListView.getPackedPositionGroup(0L), TextUtils.lastIndexOf("", '0', 0, 0) + 2047995854, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = this.b;
        int i5 = n + 89;
        h = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 30 : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return i4;
            default:
                int i6 = 13 / 0;
                return i4;
        }
    }

    @Override // o.d.a$31117$c
    public final void d(int i2, int i3) {
        g.c();
        Object[] objArr = new Object[1];
        k((byte) (AndroidCharacter.getMirror('0') - 22), 1366345433 + (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) (4 - View.getDefaultSize(0, 0)), (-1) - TextUtils.getTrimmedLength(""), 2047995833 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k((byte) ((-71) - (ViewConfiguration.getScrollDefaultDelay() >> 16)), 1366345521 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) (Color.green(0) - 32), AndroidCharacter.getMirror('0') - 30, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 2047995861, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(i3).toString());
        synchronized (this.e) {
            this.a = true;
            this.b = i3;
            this.e.notifyAll();
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:123:0x0214, code lost:
    
        if (r4 != false) goto L74;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x02d1, code lost:
    
        r3 = r7;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:57:0x01f0. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 952
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bj.a.k(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
