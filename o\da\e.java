package o.da;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import o.a.j;
import o.ct.b;
import o.e.a;
import o.fc.c;
import o.fc.d;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\da\e.smali */
public final class e implements b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static char[] b;
    private static boolean c;
    private static int d;
    private static int e;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        h = 1;
        d();
        Process.myTid();
        int i = h + 11;
        d = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        b = new char[]{61621, 61624, 61618, 61617, 61638, 61613, 61634, 61619, 61607, 61635, 61598, 61637, 61625, 61595, 61596, 61628};
        c = true;
        a = true;
        e = 782102862;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 4
            int r9 = 1 - r9
            int r8 = r8 * 3
            int r8 = 4 - r8
            byte[] r0 = o.da.e.$$a
            int r7 = r7 + 117
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L17:
            r3 = r2
        L18:
            r6 = r9
            r9 = r7
            r7 = r6
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r8 = r8 + 1
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.da.e.g(int, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 10, -33, 93};
        $$b = 218;
    }

    @Override // o.ct.b
    public final d b(o.eg.b bVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        Object obj = null;
        f(null, 127 - TextUtils.indexOf("", ""), null, "\u0085\u0084\u0083\u0082\u0081", objArr);
        short shortValue = bVar.k(((String) objArr[0]).intern()).shortValue();
        Object[] objArr2 = new Object[1];
        f(null, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 127, null, "\u0088\u0087\u0086", objArr2);
        int intValue = bVar.i(((String) objArr2[0]).intern()).intValue();
        o.fh.b bVar2 = new o.fh.b(true, c.b, shortValue);
        bVar2.b(0);
        bVar2.a(1);
        bVar2.c(intValue);
        int i = d + 13;
        h = i % 128;
        switch (i % 2 != 0 ? '\\' : (char) 1) {
            case 1:
                obj.hashCode();
                throw null;
            default:
                return bVar2;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v23, types: [byte[]] */
    private static void f(String str, int i, int[] iArr, String str2, Object[] objArr) {
        int i2;
        char[] cArr;
        int i3;
        ?? r1 = str2;
        switch (r1 != 0 ? 'c' : '_') {
            case Opcodes.DADD /* 99 */:
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        char[] charArray = str != null ? str.toCharArray() : str;
        j jVar = new j();
        char[] cArr2 = b;
        long j = 0;
        int i4 = 2;
        int i5 = 1;
        int i6 = 0;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i7 = 0;
            while (true) {
                switch (i7 < length ? (char) 27 : '@') {
                    case '@':
                        cArr2 = cArr3;
                        break;
                    default:
                        int i8 = $11 + 87;
                        $10 = i8 % 128;
                        if (i8 % i4 != 0) {
                            try {
                                Object[] objArr2 = new Object[i5];
                                objArr2[i6] = Integer.valueOf(cArr2[i7]);
                                Object obj = a.s.get(1085633688);
                                if (obj == null) {
                                    Class cls = (Class) a.c(((byte) KeyEvent.getModifierMetaStateMask()) + 12, (char) ((ExpandableListView.getPackedPositionForChild(i6, i6) > j ? 1 : (ExpandableListView.getPackedPositionForChild(i6, i6) == j ? 0 : -1)) + 1), TextUtils.indexOf("", "") + 493);
                                    byte length2 = (byte) $$a.length;
                                    byte b2 = (byte) (length2 - 4);
                                    Object[] objArr3 = new Object[1];
                                    g(length2, b2, b2, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    a.s.put(1085633688, obj);
                                }
                                cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i7 += 0;
                                j = 0;
                                i4 = 2;
                                i5 = 1;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } else {
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr2[i7])};
                                Object obj2 = a.s.get(1085633688);
                                if (obj2 == null) {
                                    Class cls2 = (Class) a.c(View.resolveSizeAndState(0, 0, 0) + 11, (char) (ViewConfiguration.getPressedStateDuration() >> 16), 493 - ((Process.getThreadPriority(0) + 20) >> 6));
                                    byte length3 = (byte) $$a.length;
                                    byte b3 = (byte) (length3 - 4);
                                    Object[] objArr5 = new Object[1];
                                    g(length3, b3, b3, objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    a.s.put(1085633688, obj2);
                                }
                                cArr3[i7] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                i7++;
                                j = 0;
                                i4 = 2;
                                i5 = 1;
                                i6 = 0;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
            }
        }
        try {
            Object[] objArr6 = {Integer.valueOf(e)};
            Object obj3 = a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls3 = (Class) a.c(10 - ExpandableListView.getPackedPositionType(0L), (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 8855), TextUtils.indexOf("", "") + 324);
                byte b4 = (byte) 1;
                byte b5 = (byte) (b4 - 1);
                Object[] objArr7 = new Object[1];
                g(b4, b5, b5, objArr7);
                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
            if (a) {
                int i9 = $11 + 11;
                $10 = i9 % 128;
                int i10 = i9 % 2;
                jVar.e = bArr.length;
                char[] cArr4 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr4[jVar.c] = (char) (cArr2[bArr[(jVar.e - 1) - jVar.c] + i] - intValue);
                    try {
                        Object[] objArr8 = {jVar, jVar};
                        Object obj4 = a.s.get(745816316);
                        if (obj4 == null) {
                            Class cls4 = (Class) a.c(Color.red(0) + 10, (char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 207);
                            byte b6 = (byte) 0;
                            byte b7 = b6;
                            Object[] objArr9 = new Object[1];
                            g(b6, b7, b7, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            a.s.put(745816316, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr[0] = new String(cArr4);
                return;
            }
            if (!c) {
                jVar.e = iArr.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    int i11 = $10 + 27;
                    $11 = i11 % 128;
                    if (i11 % 2 == 0) {
                        cArr5[jVar.c] = (char) (cArr2[iArr[(jVar.e << 0) >> jVar.c] + i] / intValue);
                        i2 = jVar.c * 0;
                    } else {
                        cArr5[jVar.c] = (char) (cArr2[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                        i2 = jVar.c + 1;
                    }
                    jVar.c = i2;
                }
                objArr[0] = new String(cArr5);
                return;
            }
            int i12 = $10 + 21;
            $11 = i12 % 128;
            if (i12 % 2 == 0) {
                jVar.e = charArray.length;
                cArr = new char[jVar.e];
                i3 = 1;
            } else {
                jVar.e = charArray.length;
                cArr = new char[jVar.e];
                i3 = 0;
            }
            jVar.c = i3;
            while (jVar.c < jVar.e) {
                cArr[jVar.c] = (char) (cArr2[charArray[(jVar.e - 1) - jVar.c] - i] - intValue);
                try {
                    Object[] objArr10 = {jVar, jVar};
                    Object obj5 = a.s.get(745816316);
                    if (obj5 == null) {
                        Class cls5 = (Class) a.c(9 - TextUtils.lastIndexOf("", '0'), (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 206);
                        byte b8 = (byte) 0;
                        byte b9 = b8;
                        Object[] objArr11 = new Object[1];
                        g(b8, b9, b9, objArr11);
                        obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                        a.s.put(745816316, obj5);
                    }
                    ((Method) obj5).invoke(null, objArr10);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            String str3 = new String(cArr);
            int i13 = $11 + Opcodes.DSUB;
            $10 = i13 % 128;
            int i14 = i13 % 2;
            objArr[0] = str3;
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
