package o.z;

import android.content.Context;
import android.view.View;
import android.widget.ExpandableListView;
import o.cf.i;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\a.smali */
public final class a extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        e();
        View.getDefaultSize(0, 0);
        int i = e + 69;
        b = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        d = 7805648481584979L;
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = 18;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(short r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 4 - r8
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.z.a.$$a
            int r9 = r9 * 3
            int r9 = r9 + 68
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1a
            r9 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r7
            goto L36
        L1a:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1f:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L2e
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2e:
            r3 = r0[r9]
            r6 = r10
            r10 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r7 = r7 + r8
            int r9 = r9 + 1
            r8 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.z.a.x(short, int, int, java.lang.Object[]):void");
    }

    a(Context context, boolean z) {
        super(context, 26, Boolean.valueOf(z));
    }

    @Override // o.cf.i
    public final void b() throws d, o.bt.d, o.bn.c, o.bp.d {
        int i = b + 47;
        e = i % 128;
        int i2 = i % 2;
        h();
        t();
        o();
        m();
        l();
        n();
        int i3 = e + 81;
        b = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.cf.i
    public final String c() {
        int i = e + 41;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        v("鳄乆鳲觲䮜\ud92a쒰辽苆ꯋ\ue6e2귆ꂓ䶛胗䨚왬汮ꈀ栫\ue433ุ䱷", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = e + 19;
        b = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x002c. Please report as an issue. */
    private static void v(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 412
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.z.a.v(java.lang.String, int, java.lang.Object[]):void");
    }
}
