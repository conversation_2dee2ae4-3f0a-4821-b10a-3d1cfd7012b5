package org.bouncycastle.jcajce.provider.util;

import org.bouncycastle.jcajce.provider.config.ConfigurableProvider;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\jcajce\provider\util\AlgorithmProvider.smali */
public abstract class AlgorithmProvider {
    public abstract void configure(ConfigurableProvider configurableProvider);
}
