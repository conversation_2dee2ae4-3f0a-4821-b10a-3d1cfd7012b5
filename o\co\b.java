package o.co;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.eg.d;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\co\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long c;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        d = 1;
        c = 621618770763357287L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void e(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 4
            byte[] r0 = o.co.b.$$a
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r7 = r7 * 3
            int r7 = 71 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L39
        L1c:
            r3 = r2
            r5 = r8
            r8 = r7
        L1f:
            r7 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2f
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2f:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L39:
            int r6 = -r6
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.b.e(byte, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{59, -48, 125, -69};
        $$b = Opcodes.GETSTATIC;
    }

    public static a d(o.eg.b bVar) throws d, i {
        int i = a + 95;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        b("諽务摝誔蹽铏", ViewConfiguration.getEdgeSlop() >> 16, objArr);
        String r = bVar.r(((String) objArr[0]).intern());
        try {
            Object[] objArr2 = new Object[1];
            b("䮔\uf1b5ꮈ䯠\u2d74ኈ䩚\udc17", ViewConfiguration.getLongPressTimeout() >> 16, objArr2);
            e b = e.b(bVar.r(((String) objArr2[0]).intern()));
            Object[] objArr3 = new Object[1];
            b("ﺾ栆맣ﻊ듛ë찵婩", TextUtils.getOffsetBefore("", 0), objArr3);
            String c2 = bVar.c(((String) objArr3[0]).intern(), (String) null);
            Object[] objArr4 = new Object[1];
            b("ⷦ幕攮ⶂ芌\udc2a찰婹強", ViewConfiguration.getPressedStateDuration() >> 16, objArr4);
            String c3 = bVar.c(((String) objArr4[0]).intern(), (String) null);
            Object[] objArr5 = new Object[1];
            b("ట谋\u19ad౻僒ꂩ\ue5bd珴绍", TextUtils.getOffsetAfter("", 0), objArr5);
            a aVar = new a(r, b, c2, c3, bVar.c(((String) objArr5[0]).intern(), (String) null));
            int i3 = d + 57;
            a = i3 % 128;
            int i4 = i3 % 2;
            return aVar;
        } catch (IllegalArgumentException e) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr6 = new Object[1];
            b("\uf053\ue419ꔼ\uf01a㣏\u1c3a梬ﻥ苟ꯨ褈憄ᗀ☕㩩ጴ꣓턴띜虄㮾䱄↸६친･勌뱐䅪榑쿩\u2fe4푉\ue4e1", View.resolveSizeAndState(0, 0, 0), objArr6);
            StringBuilder append = sb.append(((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            b("䮔\uf1b5ꮈ䯠\u2d74ኈ䩚\udc17", (-1) - TextUtils.lastIndexOf("", '0', 0, 0), objArr7);
            throw new i(append.append(bVar.r(((String) objArr7[0]).intern())).toString());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:47:0x0023, code lost:
    
        if (r13 != 0) goto L17;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v2, types: [char[]] */
    /* JADX WARN: Type inference failed for: r13v3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void b(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.b.b(java.lang.String, int, java.lang.Object[]):void");
    }
}
