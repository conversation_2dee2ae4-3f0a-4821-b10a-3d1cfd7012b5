package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\a.smali */
public abstract class a {
    public static final String a = w.c();
    public static final String b = w.b();

    public static final void a(String str) {
        if (str == null) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.KEY_NULL, null, 2, null);
        }
        if (str.length() > 100 || str.length() == 0) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.KEY_INCORRECT_LENGTH, null, 2, null);
        }
        if (!y.b(str)) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.KEY_INCORRECT_FORMAT, null, 2, null);
        }
    }

    public static final void b(String str) {
        if (str == null) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.VALUE_NULL, null, 2, null);
        }
        String SEPARATOR_KEY_VALUE_PAIRS = a;
        Intrinsics.checkNotNullExpressionValue(SEPARATOR_KEY_VALUE_PAIRS, "SEPARATOR_KEY_VALUE_PAIRS");
        if (!StringsKt.contains$default((CharSequence) str, (CharSequence) SEPARATOR_KEY_VALUE_PAIRS, false, 2, (Object) null)) {
            String SEPARATOR_KEY_VALUE = b;
            Intrinsics.checkNotNullExpressionValue(SEPARATOR_KEY_VALUE, "SEPARATOR_KEY_VALUE");
            if (!StringsKt.contains$default((CharSequence) str, (CharSequence) SEPARATOR_KEY_VALUE, false, 2, (Object) null)) {
                return;
            }
        }
        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.VALUE_INCORRECT_FORMAT, null, 2, null);
    }

    public static final void c(String str) {
        if (str == null) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_NAME_NULL, null, 2, null);
        }
        if (!y.b(StringsKt.replace$default(StringsKt.replace$default(StringsKt.replace$default(str, '-', 'x', false, 4, (Object) null), '_', 'x', false, 4, (Object) null), '.', 'x', false, 4, (Object) null))) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_NAME_INCORRECT_FORMAT, null, 2, null);
        }
        if (str.length() == 0 || str.length() > 100) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_NAME_INCORRECT_LENGTH, null, 2, null);
        }
    }
}
