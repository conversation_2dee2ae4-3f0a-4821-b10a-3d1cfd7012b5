package o.ei;

import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.Product;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ei\a.smali */
public final class a implements o.ee.d<Product>, b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final a a;
    public static final a b;
    public static final a d;
    private static int f;
    private static char[] g;
    private static boolean h;
    private static final /* synthetic */ a[] i;
    private static int k;
    private static int l;

    /* renamed from: o, reason: collision with root package name */
    private static boolean f60o;
    private final Product c;
    private final String e;
    private final String j;

    static void b() {
        g = new char[]{61929, 61708, 61701, 61712, 61938, 61715, 61702, 61717, 61703, 61718, 61707, 61943, 61716, 61705, 61927, 61710, 61697, 61719, 61890, 61901, 61714, 61912, 61893, 61700, 61930, 61706, 61900, 61722, 61921, 61926};
        h = true;
        f60o = true;
        f = 782102946;
    }

    static void init$0() {
        $$a = new byte[]{77, -11, 68, UtilitiesSDKConstants.SRP_LABEL_MAC};
        $$b = 255;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(short r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r0 = o.ei.a.$$a
            int r7 = r7 + 117
            int r9 = r9 * 4
            int r9 = r9 + 1
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1c:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            int r7 = r7 + 1
            if (r5 != r9) goto L2d
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2d:
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L35:
            int r9 = -r9
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.a.n(short, int, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ a[] h() {
        int i2 = k + 23;
        l = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return new a[]{d, a, b};
            default:
                a[] aVarArr = new a[3];
                aVarArr[0] = d;
                aVarArr[0] = a;
                aVarArr[2] = b;
                return aVarArr;
        }
    }

    public static a valueOf(String str) {
        int i2 = l + 27;
        k = i2 % 128;
        int i3 = i2 % 2;
        a aVar = (a) Enum.valueOf(a.class, str);
        int i4 = l + 31;
        k = i4 % 128;
        int i5 = i4 % 2;
        return aVar;
    }

    public static a[] values() {
        int i2 = l + 83;
        k = i2 % 128;
        int i3 = i2 % 2;
        a[] aVarArr = (a[]) i.clone();
        int i4 = l + 47;
        k = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return aVarArr;
            default:
                int i5 = 54 / 0;
                return aVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Product a() {
        int i2 = l + Opcodes.LNEG;
        k = i2 % 128;
        int i3 = i2 % 2;
        Product d2 = d();
        int i4 = l + 17;
        k = i4 % 128;
        int i5 = i4 % 2;
        return d2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        k = 1;
        b();
        ViewConfiguration.getEdgeSlop();
        Object[] objArr = new Object[1];
        m(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "\u0083\u0089\u0099", objArr);
        String intern = ((String) objArr[0]).intern();
        Product product = Product.Hce;
        Object[] objArr2 = new Object[1];
        m(null, 126 - Process.getGidForName(""), null, "\u0083\u0089\u009a", objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        m(null, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 127, null, "\u0083\u0089\u008e\u008d\u0084\u0083\u008c\u0088\u0087\u0095\u009d\u008a\u0092\u0086\u0099\u0095\u0086\u0090\u0083\u008a\u0082\u009d\u009b\u0087\u0083\u0092\u0086\u0095\u009c\u0083\u009b\u0095\u0086\u0090\u0083\u008a\u0082\u0091\u009b\u0084\u0098", objArr3);
        d = new a(intern, 0, product, intern2, ((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        m(null, 127 - Color.blue(0), null, "\u0091\u0089\u008c", objArr4);
        String intern3 = ((String) objArr4[0]).intern();
        Product product2 = Product.Sca;
        Object[] objArr5 = new Object[1];
        m(null, 127 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), null, "\u0091\u0089\u0092", objArr5);
        a = new a(intern3, 1, product2, ((String) objArr5[0]).intern(), null);
        Object[] objArr6 = new Object[1];
        m(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "\u0087\u0084\u0091\u008f\u0090\u0091\u008a\u008e\u008b\u008e\u009e", objArr6);
        String intern4 = ((String) objArr6[0]).intern();
        Product product3 = Product.DigitalCard;
        Object[] objArr7 = new Object[1];
        m(null, 127 - TextUtils.getOffsetAfter("", 0), null, "\u0087\u0084\u0091\u008f\u0090\u0091\u008a\u008e\u008b\u008e\u0087", objArr7);
        b = new a(intern4, 2, product3, ((String) objArr7[0]).intern(), null);
        i = h();
        int i2 = k + 23;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    private a(String str, int i2, Product product, String str2, String str3) {
        this.c = product;
        this.e = str2;
        this.j = str3;
    }

    public final Product d() {
        int i2 = k + 47;
        l = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c;
        }
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = k;
        int i3 = i2 + 25;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? 'O' : (char) 26) {
            case 26:
                String str = this.e;
                int i4 = i2 + 11;
                l = i4 % 128;
                int i5 = i4 % 2;
                return str;
            default:
                throw null;
        }
    }

    public final Class<?> c() {
        int i2 = l + Opcodes.DDIV;
        k = i2 % 128;
        int i3 = i2 % 2;
        String str = this.j;
        Class<?> cls = null;
        switch (str != null ? Typography.dollar : (char) 29) {
            default:
                try {
                    cls = Class.forName(str);
                    int i4 = l + Opcodes.DSUB;
                    k = i4 % 128;
                    if (i4 % 2 != 0) {
                    }
                } catch (ClassNotFoundException e) {
                    o.ee.g.c();
                    Object[] objArr = new Object[1];
                    m(null, (Process.myPid() >> 22) + 127, null, "\u008a\u0089\u0088\u0087\u0086\u0084\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    m(null, 127 - View.MeasureSpec.makeMeasureSpec(0, 0), null, "\u0087\u0082\u0088\u0086\u0098\u0093\u008a\u0086\u0082\u0093\u0092\u0097\u0093\u0096\u0093\u0092\u0092\u0091\u0090\u0089\u0093\u0083\u0089\u008e\u008d\u0084\u0083\u0092\u0093\u0094\u0093\u0092\u0097\u0093\u0096\u0093\u008a\u0089\u0088\u0087\u0086\u0084\u0095\u0093\u0094\u0093\u0092\u0092\u0091\u0090\u008f\u0083\u0089\u008e\u008d\u0084\u0083\u008c\u008a\u0083\u008b", objArr2);
                    o.ee.g.e(intern, String.format(((String) objArr2[0]).intern(), this, this.j));
                    return null;
                }
            case 29:
                return cls;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean c(java.util.List<o.ei.e> r4) {
        /*
            r3 = this;
            java.util.Iterator r4 = r4.iterator()
        L6:
            boolean r0 = r4.hasNext()
            if (r0 == 0) goto Lf
            r0 = 62
            goto L11
        Lf:
            r0 = 49
        L11:
            r1 = 0
            switch(r0) {
                case 62: goto L16;
                default: goto L15;
            }
        L15:
            goto L42
        L16:
            java.lang.Object r0 = r4.next()
            o.ei.e r0 = (o.ei.e) r0
            o.ei.a r0 = r0.a()
            r2 = 1
            if (r0 != r3) goto L25
            r0 = r2
            goto L26
        L25:
            r0 = r1
        L26:
            switch(r0) {
                case 1: goto L2a;
                default: goto L29;
            }
        L29:
            goto L6
        L2a:
            int r4 = o.ei.a.k
            int r4 = r4 + 81
            int r0 = r4 % 128
            o.ei.a.l = r0
            int r4 = r4 % 2
            if (r4 == 0) goto L38
            goto L39
        L38:
            r1 = r2
        L39:
            switch(r1) {
                case 1: goto L3d;
                default: goto L3c;
            }
        L3c:
            goto L3e
        L3d:
            return r2
        L3e:
            r4 = 0
            throw r4     // Catch: java.lang.Throwable -> L40
        L40:
            r4 = move-exception
            throw r4
        L42:
            int r4 = o.ei.a.l
            int r4 = r4 + 99
            int r0 = r4 % 128
            o.ei.a.k = r0
            int r4 = r4 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.a.c(java.util.List):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.ei.a e(java.lang.String r7) {
        /*
            o.ei.a[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L8:
            if (r3 >= r1) goto Lc
            r4 = r2
            goto Ld
        Lc:
            r4 = 1
        Ld:
            r5 = 0
            switch(r4) {
                case 0: goto L12;
                default: goto L11;
            }
        L11:
            goto L3b
        L12:
            int r4 = o.ei.a.l
            int r4 = r4 + 9
            int r6 = r4 % 128
            o.ei.a.k = r6
            int r4 = r4 % 2
            if (r4 == 0) goto L2f
            r4 = r0[r3]
            java.lang.String r5 = r4.toString()
            boolean r5 = r5.equals(r7)
            if (r5 == 0) goto L2c
        L2b:
            return r4
        L2c:
            int r3 = r3 + 1
            goto L8
        L2f:
            r0 = r0[r3]
            java.lang.String r0 = r0.toString()
            r0.equals(r7)
            throw r5     // Catch: java.lang.Throwable -> L39
        L39:
            r7 = move-exception
            throw r7
        L3b:
            int r7 = o.ei.a.k
            int r7 = r7 + 113
            int r0 = r7 % 128
            o.ei.a.l = r0
            int r7 = r7 % 2
            if (r7 != 0) goto L48
            return r5
        L48:
            r5.hashCode()     // Catch: java.lang.Throwable -> L4c
            throw r5     // Catch: java.lang.Throwable -> L4c
        L4c:
            r7 = move-exception
            throw r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.a.e(java.lang.String):o.ei.a");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 762
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.a.m(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
