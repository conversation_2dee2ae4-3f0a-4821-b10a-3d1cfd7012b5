package o.bb;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bb\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final e A;
    private static e B;
    public static final e C;
    public static final e D;
    private static char[] E;
    private static e F;
    private static e G;
    private static long H;
    private static final /* synthetic */ e[] I;
    private static char J;
    private static int K;
    private static char L;
    private static char M;
    private static char N;
    private static int P;
    public static final e a;
    public static final e b;
    public static final e c;
    public static final e d;
    public static final e e;
    public static final e f;
    public static final e g;
    public static final e h;
    public static final e i;
    public static final e j;
    public static final e k;
    public static final e l;
    public static final e m;
    public static final e n;

    /* renamed from: o, reason: collision with root package name */
    public static final e f40o;
    public static final e p;
    public static final e q;
    public static final e r;
    public static final e s;
    public static final e t;
    public static final e u;
    public static final e v;
    public static final e w;
    public static final e x;
    public static final e y;
    public static final e z;

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void R(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = 105 - r6
            int r7 = r7 * 4
            int r7 = 3 - r7
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.bb.e.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.e.R(int, short, short, java.lang.Object[]):void");
    }

    static void d() {
        E = new char[]{30352, 44682, 50865, 65247, 5838, 20222, 26115, 2470, 53682, 47519, 33260, 27113, 12744, 6455, 57652, 51480, 37231, 11421, 62605, 40100, 42181, 19650, 5350, 15374, 50183, 60464, 46144, 23643, 49939, 6926, 29497, 19281, 41795, 64353, 54144, 11160, 929, 23512, 46047, 35811, 57883, 14874, 4653, 27221, 17022, 39545, 62099, 51879, 8892, 31453, 21243, 43751, 33029, 34360, 24108, 13835, 3708, 59001, 48725, 38565, 28349, 18070, 7911, 63226, 52957, 11397, 62608, 40098, 42180, 19652, 5363, 11402, 62605, 40096, 42186, 19653, 5346, 15378, 50192, 60472, 46173, 23633, 11402, 62615, 40096, 42184, 19674, 5368, 15384, 50179, 60477, 46158, 23617, 25726, 35685, 21366, 15192, 819, 60220, 45851, 39915, 25577, 19407, 5048, 64426, 50059, 43647, 11400, 62602, 40113, 42179, 19668, 5353, 15385, 50202, 60474, 46158, 23617, 25726, 3486, 54676, 64936, 34256, 44541, 30192, 7432, 9518, 52515, 11418, 62618, 40102, 42206, 19651, 5346, 15378, 50202, 60471, 46171, 23632, 25705, 3463, 54678, 64958, 34246, 62258, 11049, 17181, 31613, 37754, 52057, 58302, 7091, 13189, 27633, 33784, 48064, 53793, 2607, 8711, 23144, 29257, 43587, 49851, 64134, 4746, 19175, 25286, 39619, 45357, 59669, 260, 14711, 11417, 62602, 40118, 42179, 19662, 5348, 15372, 50177, 60477, 46160, 23617, 25716, 3486, 54675, 64952, 34261, 44512, 30204, 7424, 9524, 52518, 38214, 48481, 17791, 28316, 14011, 11405, 62614, 40098, 42178, 19653, 5350, 15361, 50188, 60474, 46158, 23623, 25727, 3486, 54661, 64952, 34259, 44512, 30193, 11405, 62614, 40098, 42178, 19653, 5350, 15361, 50188, 60474, 46158, 23623, 25727, 3486, 54675, 64952, 34255, 44524, 30187, 7424, 9524, 52532, 38212, 48482, 17790, 28294, 14012, 56993, 59098, 36597, 22270, 32286, 1596, 11869, 63056, 40558, 42894, 20383};
        H = 3407027761797592287L;
        J = (char) 3757;
        M = (char) 6988;
        N = (char) 16178;
        L = (char) 18883;
    }

    static void init$0() {
        $$a = new byte[]{37, -65, 15, -4};
        $$b = Opcodes.D2I;
    }

    private e(String str, int i2) {
    }

    private static /* synthetic */ e[] b() {
        int i2 = P + 7;
        int i3 = i2 % 128;
        K = i3;
        int i4 = i2 % 2;
        e[] eVarArr = {e, a, d, B, c, b, f, i, h, j, g, k, n, m, f40o, l, F, r, G, q, s, p, t, x, v, y, u, w, C, A, z, D};
        int i5 = i3 + 109;
        P = i5 % 128;
        switch (i5 % 2 == 0 ? '\r' : 'a') {
            case '\r':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return eVarArr;
        }
    }

    public static e valueOf(String str) {
        int i2 = P + 27;
        K = i2 % 128;
        int i3 = i2 % 2;
        e eVar = (e) Enum.valueOf(e.class, str);
        int i4 = P + 33;
        K = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    public static e[] values() {
        int i2 = P + 79;
        K = i2 % 128;
        int i3 = i2 % 2;
        e[] eVarArr = (e[]) I.clone();
        int i4 = K + 97;
        P = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 0 : '\r') {
            case '\r':
                return eVarArr;
            default:
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        K = 0;
        P = 1;
        d();
        Object[] objArr = new Object[1];
        O((char) (Color.argb(0, 0, 0, 0) + 23066), ViewConfiguration.getMaximumDrawingCacheSize() >> 24, 6 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
        e = new e(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        O((char) (9517 - TextUtils.indexOf((CharSequence) "", '0')), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 7, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 10, objArr2);
        a = new e(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        O((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 17 - TextUtils.indexOf("", ""), 11 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
        d = new e(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        O((char) (61337 - Color.red(0)), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 28, Process.getGidForName("") + 26, objArr4);
        B = new e(((String) objArr4[0]).intern(), 3);
        Object[] objArr5 = new Object[1];
        Q("も⧱䦛灈\ue99c\ue74e䮇䨟\uedea择\u2d2f\udb1f量⋨ﶻꊢ뱵\ue274", 17 - KeyEvent.keyCodeFromString(""), objArr5);
        c = new e(((String) objArr5[0]).intern(), 4);
        Object[] objArr6 = new Object[1];
        O((char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 43681), ((Process.getThreadPriority(0) + 20) >> 6) + 53, Color.red(0) + 12, objArr6);
        b = new e(((String) objArr6[0]).intern(), 5);
        Object[] objArr7 = new Object[1];
        Q("怣忮㓕팳", 4 - KeyEvent.normalizeMetaState(0), objArr7);
        f = new e(((String) objArr7[0]).intern(), 6);
        Object[] objArr8 = new Object[1];
        Q("왽䄄⽾뢆꣰壘\uda10穱\udc14ޚ⽾뢆힕ᑟ", (ViewConfiguration.getTapTimeout() >> 16) + 13, objArr8);
        i = new e(((String) objArr8[0]).intern(), 7);
        Object[] objArr9 = new Object[1];
        O((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 66, View.MeasureSpec.makeMeasureSpec(0, 0) + 6, objArr9);
        h = new e(((String) objArr9[0]).intern(), 8);
        Object[] objArr10 = new Object[1];
        Q("輬肻⦺ﭾ噱㵻ﴉ갭\ude6fﵗ禀ᑨ掬顓踄\uebdb聃\u08d0", (ViewConfiguration.getJumpTapTimeout() >> 16) + 18, objArr10);
        j = new e(((String) objArr10[0]).intern(), 9);
        Object[] objArr11 = new Object[1];
        O((char) (ViewConfiguration.getFadingEdgeLength() >> 16), 71 - (ViewConfiguration.getPressedStateDuration() >> 16), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 11, objArr11);
        g = new e(((String) objArr11[0]).intern(), 10);
        Object[] objArr12 = new Object[1];
        Q("㘐㨰뮼蕸퓥Ꮂ꣰壘맪ꎀ뱿镂剣훂䝟鼸黿\uf391迺燊ଛ٫ݿ턠\uef24筁ଛ٫赞췙", 29 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr12);
        k = new e(((String) objArr12[0]).intern(), 11);
        Object[] objArr13 = new Object[1];
        Q("왽䄄⽾뢆꣰壘迺燊ᾕ\ue010영ㅪ", ExpandableListView.getPackedPositionChild(0L) + 12, objArr13);
        n = new e(((String) objArr13[0]).intern(), 12);
        Object[] objArr14 = new Object[1];
        O((char) TextUtils.getOffsetAfter("", 0), TextUtils.lastIndexOf("", '0', 0, 0) + 83, AndroidCharacter.getMirror('0') - '$', objArr14);
        m = new e(((String) objArr14[0]).intern(), 13);
        Object[] objArr15 = new Object[1];
        O((char) (43001 - Color.green(0)), ((Process.getThreadPriority(0) + 20) >> 6) + 94, 13 - View.resolveSizeAndState(0, 0, 0), objArr15);
        f40o = new e(((String) objArr15[0]).intern(), 14);
        Object[] objArr16 = new Object[1];
        Q("\ue2d5昢\uef24筁㘐㨰꣰壘\udf09\ue0adڌ攰䝟鼸蓥툌뮼蕸㦆`\uef24筁ଛ٫", 24 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr16);
        l = new e(((String) objArr16[0]).intern(), 15);
        Object[] objArr17 = new Object[1];
        Q("䵫ᛚ㻔켠\ue725穀⪮ᕿ\ue2d5昢\uef24筁㘐㨰\uef24筁ଛ٫迺燊\uf7ca岕剐噟", 22 - ExpandableListView.getPackedPositionChild(0L), objArr17);
        F = new e(((String) objArr17[0]).intern(), 16);
        Object[] objArr18 = new Object[1];
        O((char) ExpandableListView.getPackedPositionType(0L), (ViewConfiguration.getTouchSlop() >> 8) + Opcodes.DMUL, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 21, objArr18);
        r = new e(((String) objArr18[0]).intern(), 17);
        Object[] objArr19 = new Object[1];
        O((char) View.MeasureSpec.getMode(0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 127, 16 - TextUtils.getCapsMode("", 0, 0), objArr19);
        G = new e(((String) objArr19[0]).intern(), 18);
        Object[] objArr20 = new Object[1];
        O((char) (View.resolveSizeAndState(0, 0, 0) + 57279), TextUtils.getOffsetBefore("", 0) + Opcodes.D2F, 28 - Color.blue(0), objArr20);
        q = new e(((String) objArr20[0]).intern(), 19);
        Object[] objArr21 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪뾵猅晦㾅맪ꎀꬅ멓槾償執氊\uf6dd궭읋훷板㹃Ā光", 32 - KeyEvent.getDeadChar(0, 0), objArr21);
        s = new e(((String) objArr21[0]).intern(), 20);
        Object[] objArr22 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪\udd82ｹ⚲禪妭\uf3e8ﾤ\ua97d᪒拿흀\ue21c聃\u08d0", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 26, objArr22);
        p = new e(((String) objArr22[0]).intern(), 21);
        Object[] objArr23 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪뾵猅晦㾅迺燊ᾕ\ue010狴\ue80c뚽ﮓ聃\u08d0", 26 - TextUtils.getCapsMode("", 0, 0), objArr23);
        t = new e(((String) objArr23[0]).intern(), 22);
        Object[] objArr24 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪\udd82ｹ⚲禪妭\uf3e8ﾤ\ua97d뚽ﮓ⟒ꅇ", 24 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr24);
        x = new e(((String) objArr24[0]).intern(), 23);
        Object[] objArr25 = new Object[1];
        O((char) ('0' - AndroidCharacter.getMirror('0')), 172 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 27 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr25);
        v = new e(((String) objArr25[0]).intern(), 24);
        Object[] objArr26 = new Object[1];
        Q("㦆`걲龪⾱陝䦛灈岾ꓧﾤ\ua97dݿ턠䄜\ude3cஇ촛뱵\ue274", 19 - ExpandableListView.getPackedPositionType(0L), objArr26);
        y = new e(((String) objArr26[0]).intern(), 25);
        Object[] objArr27 = new Object[1];
        Q("も⧱䦛灈\ue99c\ue74eణᾌ˞俐槾償\uef24筁\udc14ޚ赞췙", 17 - Color.red(0), objArr27);
        u = new e(((String) objArr27[0]).intern(), 26);
        Object[] objArr28 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪䝟鼸ﱦ߫ွ鲼掬顓剐噟", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 21, objArr28);
        w = new e(((String) objArr28[0]).intern(), 27);
        Object[] objArr29 = new Object[1];
        O((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), Color.green(0) + Opcodes.IFNULL, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 17, objArr29);
        C = new e(((String) objArr29[0]).intern(), 28);
        Object[] objArr30 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪뾵猅晦㾅맪ꎀ壎䦙聃\u08d0\ue2d5昢\uef24筁ଛ٫迺燊ଛ٫叮✮픿\ue714", 36 - TextUtils.getCapsMode("", 0, 0), objArr30);
        A = new e(((String) objArr30[0]).intern(), 29);
        Object[] objArr31 = new Object[1];
        Q("ݿ턠鰮꼍帩㸀Ꞡ柦㦆`걲龪迺燊䵫ᛚ掬顓ﾤ\ua97dĀ光ꄶ㿘憬럕掬顓ﾤ\ua97d叮✮⚲禪萩䄂ﱦ߫踄\uebdb\udf05釮賴䲢矊խ䓲鮮쌸菫", 49 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr31);
        z = new e(((String) objArr31[0]).intern(), 30);
        Object[] objArr32 = new Object[1];
        O((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ViewConfiguration.getTouchSlop() >> 8) + 216, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 36, objArr32);
        D = new e(((String) objArr32[0]).intern(), 31);
        I = b();
        int i2 = K + 61;
        P = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void O(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 730
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.e.O(char, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void Q(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 582
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.e.Q(java.lang.String, int, java.lang.Object[]):void");
    }
}
