package fr.antelop.sdk;

import android.app.Application;
import android.content.Context;
import androidx.lifecycle.LiveData;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import java.util.List;
import o.bv.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\WalletManager.smali */
public final class WalletManager {
    private final f innerWalletManager;

    public WalletManager(Context context, WalletManagerCallback walletManagerCallback, Object obj) throws WalletValidationException {
        this.innerWalletManager = new f(context, walletManagerCallback, obj);
    }

    public final void connect() {
        this.innerWalletManager.g();
    }

    public final void connect(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        this.innerWalletManager.a(customerAuthenticationCredentials, customerAuthenticationCredentials2);
    }

    public final void logout() throws WalletValidationException {
        this.innerWalletManager.f();
    }

    public final void lock(WalletLockReason walletLockReason) throws WalletValidationException {
        this.innerWalletManager.a(walletLockReason);
    }

    public final void delete() {
        this.innerWalletManager.n();
    }

    public final void checkCredentials(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerWalletManager.e(customerAuthenticationCredentials);
    }

    public final void changeCredentials(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        this.innerWalletManager.c(customerAuthenticationCredentials, customerAuthenticationCredentials2);
    }

    public final void disconnect() {
        this.innerWalletManager.h();
    }

    public final LiveData<List<HceTransaction>> listHceTransactions(Application application, int i, int i2) {
        return f.c(application, i, i2);
    }

    public final boolean setCustomerCredentialsForTransaction(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        return this.innerWalletManager.c(customerAuthenticationCredentials);
    }

    public final void cancelOngoingTransaction() throws WalletValidationException {
        this.innerWalletManager.j();
    }

    public final void activateAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerWalletManager.b(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    public final void deactivateAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerWalletManager.a(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    public final void synchronizeAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerWalletManager.e(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    public final void clean() {
        this.innerWalletManager.i();
    }
}
