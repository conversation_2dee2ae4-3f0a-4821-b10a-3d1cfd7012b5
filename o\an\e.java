package o.an;

import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\e.smali */
public final class e implements a<C0025e, d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long c;
    private static long d;
    private static char e;
    private static int h;
    private static int i;
    private final C0025e a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        i = 1;
        a();
        ViewConfiguration.getTouchSlop();
        int i2 = i + Opcodes.LSHL;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'T' : '[') {
            case Opcodes.BASTORE /* 84 */:
                int i3 = 54 / 0;
                break;
        }
    }

    static void a() {
        c = 6644988181972242975L;
        e = (char) 17957;
        b = 161105445;
        d = 2305531596843724084L;
    }

    static void init$0() {
        $$a = new byte[]{110, -66, 47, -83};
        $$b = 75;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(short r5, byte r6, byte r7, java.lang.Object[] r8) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r5 = r5 + 68
            byte[] r0 = o.an.e.$$a
            byte[] r1 = new byte[r7]
            r2 = -1
            int r7 = r7 + r2
            if (r0 != 0) goto L19
            r5 = r6
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L35
        L19:
            r4 = r6
            r6 = r5
            r5 = r4
        L1c:
            int r2 = r2 + 1
            byte r3 = (byte) r6
            r1[r2] = r3
            if (r2 != r7) goto L2c
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2c:
            r3 = r0[r5]
            r4 = r8
            r8 = r7
            r7 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r4
        L35:
            int r6 = r6 + r7
            int r5 = r5 + 1
            r7 = r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.e.j(short, byte, byte, java.lang.Object[]):void");
    }

    @Override // o.an.a
    public final /* synthetic */ d a(o.eg.b bVar) throws o.eg.d {
        int i2 = h + 79;
        i = i2 % 128;
        int i3 = i2 % 2;
        d c2 = c(bVar);
        int i4 = h + 33;
        i = i4 % 128;
        int i5 = i4 % 2;
        return c2;
    }

    public e(C0025e c0025e) {
        this.a = c0025e;
    }

    @Override // o.an.a
    public final void b(o.eg.b bVar) throws o.eg.d {
        int i2 = i + Opcodes.DSUB;
        h = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        f("擖࿆ꕱ鬆撓鵥肞⌫⾻퉣햃頥\uf2b3❒ẞ픭薳籨ꎣȣ䢧녳\uf494缵Ꭲ٩㦃", TextUtils.indexOf((CharSequence) "", '0', 0) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("썡蝚킨㰎쌀ᗾ\uf54c萜蠄嫫ꁝ㼫唒꿮歬爯∕\uf4fb", Process.myTid() >> 22, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f("\uf8ccᒟ큮䘒\uf8bb蘾\uf582︾뎩줫ꂺ䔫溼㰺", View.MeasureSpec.getMode(0), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g(Gravity.getAbsoluteGravity(0, 0), "榝\ud8f3秏羮", (char) (545 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), "ꃲ㶹⁌䘂", "欑ꇕ纃䓠", objArr4);
        bVar.d(intern2, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        g(1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "❤༑樵ḍ䨻ຆ韗\uedb5\ue866\ude50켈쿖풒ᠭ\uea77墍\uef11腎孨侔", (char) TextUtils.getOffsetAfter("", 0), "亘啑ᕴ있", "欑ꇕ纃䓠", objArr5);
        bVar.d(((String) objArr5[0]).intern(), this.a.a);
        switch (this.a.d == null) {
            case false:
                Object[] objArr6 = new Object[1];
                f("ỳ⹉짱韾ẖ볪\uec1e⿓喡\uf3ec뤅铋袁ۧ爤\ud9ccﾚ", ViewConfiguration.getMaximumDrawingCacheSize() >> 24, objArr6);
                bVar.d(((String) objArr6[0]).intern(), this.a.d);
                break;
        }
        int i4 = h + 61;
        i = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x00e9, code lost:
    
        r10 = 1;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.an.e.d c(o.eg.b r18) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 428
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.e.c(o.eg.b):o.an.e$d");
    }

    /* renamed from: o.an.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\e$e.smali */
    public static final class C0025e {
        final String a;
        final String d;

        public C0025e(String str, String str2) {
            this.a = str;
            this.d = str2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\e$d.smali */
    public static final class d {
        private final String a;
        private final String e;
        private static int d = 0;
        private static int b = 1;

        public d(String str, String str2) {
            this.a = str;
            this.e = str2;
        }

        public final String d() {
            int i = d;
            int i2 = (i & 23) + (i | 23);
            b = i2 % 128;
            Object obj = null;
            switch (i2 % 2 == 0) {
                case true:
                    throw null;
                default:
                    String str = this.a;
                    int i3 = (i & 61) + (i | 61);
                    b = i3 % 128;
                    switch (i3 % 2 == 0 ? '5' : '_') {
                        case Opcodes.SALOAD /* 53 */:
                            obj.hashCode();
                            throw null;
                        default:
                            return str;
                    }
            }
        }

        public final String c() {
            int i = b + 29;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            String str = this.e;
            int i4 = ((i2 | 55) << 1) - (i2 ^ 55);
            b = i4 % 128;
            switch (i4 % 2 == 0 ? '[' : 'D') {
                case 'D':
                    return str;
                default:
                    throw null;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 350
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.e.f(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void g(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        switch (str3 == null) {
            case true:
                cArr = str3;
                break;
            default:
                cArr = str3.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        if (str2 != null) {
            cArr2 = str2.toCharArray();
            int i3 = $11 + 29;
            $10 = i3 % 128;
            int i4 = i3 % 2;
        } else {
            cArr2 = str2;
        }
        char[] cArr4 = cArr2;
        char[] charArray = str != null ? str.toCharArray() : str;
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i2));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 11, (char) (20954 - Gravity.getAbsoluteGravity(0, 0)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 344);
                    byte b2 = (byte) 0;
                    Object[] objArr3 = new Object[1];
                    j((byte) 31, b2, b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 207);
                        byte b3 = (byte) 0;
                        Object[] objArr5 = new Object[1];
                        j((byte) 33, b3, b3, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr5[oVar.e % 4] * 32718), Integer.valueOf(cArr6[intValue])};
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 11, (char) (Process.myTid() >> 22), 280 - MotionEvent.axisFromString(""));
                            byte b4 = (byte) 0;
                            Object[] objArr7 = new Object[1];
                            j((byte) 35, b4, b4, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 19, (char) (14687 - (ViewConfiguration.getFadingEdgeLength() >> 16)), 112 - View.MeasureSpec.getMode(0));
                                byte b5 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                j((byte) 38, b5, b5, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ charArray[oVar.e]) ^ (d ^ 6565854932352255525L)) ^ ((int) (b ^ 6565854932352255525L))) ^ ((char) (e ^ 6565854932352255525L)));
                            oVar.e++;
                            int i5 = $11 + 97;
                            $10 = i5 % 128;
                            switch (i5 % 2 != 0) {
                            }
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
