package com.rolster.capacitor.biometric;

import android.app.KeyguardManager;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.security.KeyPairGeneratorSpec;
import android.security.keystore.KeyGenParameterSpec;
import android.security.keystore.StrongBoxUnavailableException;
import android.util.Base64;
import androidx.activity.result.ActivityResult;
import androidx.biometric.BiometricManager;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.android.gms.stats.CodePackage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.i18n.MessageBundle;

@CapacitorPlugin(name = "NativeBiometric")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes3\com\rolster\capacitor\biometric\NativeBiometricPlugin.smali */
public class NativeBiometricPlugin extends Plugin {
    private static final String AES_MODE = "AES/ECB/PKCS7Padding";
    private static final String ANDROID_KEY_STORE = "AndroidKeyStore";
    private static final String ENCRYPTED_KEY = "NativeBiometricKey";
    private static final int FACE_AUTHENTICATION = 4;
    private static final int FINGERPRINT = 3;
    private static final byte[] FIXED_IV = new byte[12];
    private static final int IRIS_AUTHENTICATION = 5;
    private static final int MULTIPLE = 6;
    private static final String NATIVE_BIOMETRIC_SHARED_PREFERENCES = "NativeBiometricSharedPreferences";
    private static final int NONE = 0;
    private static final String RSA_MODE = "RSA/ECB/PKCS1Padding";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private KeyStore keyStore;

    @PluginMethod
    public void isAvailable(PluginCall call) {
        JSObject result = new JSObject();
        boolean useFallback = Boolean.TRUE.equals(call.getBoolean("useFallback", false));
        BiometricManager biometricManager = BiometricManager.from(getContext());
        int canAuthenticateResult = biometricManager.canAuthenticate();
        boolean fallbackAvailable = useFallback && deviceHasCredentials();
        if (useFallback && !fallbackAvailable) {
            canAuthenticateResult = 14;
        }
        boolean isAvailable = canAuthenticateResult == 0 || fallbackAvailable;
        result.put("isAvailable", isAvailable);
        if (!isAvailable) {
            int pluginErrorCode = NativeBiometricActivity.convertToPluginErrorCode(canAuthenticateResult);
            result.put("errorCode", pluginErrorCode);
        }
        result.put("biometricType", getAvailableFeature());
        call.resolve(result);
    }

    @PluginMethod
    public void verifyIdentity(PluginCall call) {
        Intent intent = new Intent(getContext(), (Class<?>) NativeBiometricActivity.class);
        intent.putExtra(MessageBundle.TITLE_ENTRY, call.getString(MessageBundle.TITLE_ENTRY, "Authenticate"));
        if (call.hasOption("subtitle")) {
            intent.putExtra("subtitle", call.getString("subtitle"));
        }
        if (call.hasOption("description")) {
            intent.putExtra("description", call.getString("description"));
        }
        if (call.hasOption("negativeButtonText")) {
            intent.putExtra("negativeButtonText", call.getString("negativeButtonText"));
        }
        if (call.hasOption("maxAttempts")) {
            intent.putExtra("maxAttempts", call.getInt("maxAttempts"));
        }
        boolean useFallback = Boolean.TRUE.equals(call.getBoolean("useFallback", false));
        if (useFallback) {
            useFallback = deviceHasCredentials();
        }
        intent.putExtra("useFallback", useFallback);
        startActivityForResult(call, intent, "verifyResult");
    }

    @PluginMethod
    public void setCredentials(PluginCall call) {
        String username = call.getString("username", null);
        String password = call.getString("password", null);
        String server = call.getString("server", null);
        if (username != null && password != null && server != null) {
            try {
                SharedPreferences.Editor editor = getContext().getSharedPreferences(NATIVE_BIOMETRIC_SHARED_PREFERENCES, 0).edit();
                editor.putString(server + "-username", encryptString(username, server));
                editor.putString(server + "-password", encryptString(password, server));
                editor.apply();
                call.resolve();
                return;
            } catch (IOException | GeneralSecurityException e) {
                call.reject("Failed to save credentials", e);
                e.printStackTrace();
                return;
            }
        }
        call.reject("Missing properties");
    }

    @PluginMethod
    public void getCredentials(PluginCall call) {
        String server = call.getString("server", null);
        SharedPreferences sharedPreferences = getContext().getSharedPreferences(NATIVE_BIOMETRIC_SHARED_PREFERENCES, 0);
        String username = sharedPreferences.getString(server + "-username", null);
        String password = sharedPreferences.getString(server + "-password", null);
        if (server != null) {
            if (username != null && password != null) {
                try {
                    JSObject result = new JSObject();
                    result.put("username", decryptString(username, server));
                    result.put("password", decryptString(password, server));
                    call.resolve(result);
                    return;
                } catch (IOException | GeneralSecurityException e) {
                    call.reject("Failed to get credentials");
                    return;
                }
            }
            call.reject("No credentials found");
            return;
        }
        call.reject("No server name was provided");
    }

    @ActivityCallback
    private void verifyResult(PluginCall call, ActivityResult result) {
        if (result.getResultCode() == -1) {
            Intent data = result.getData();
            if (data != null && data.hasExtra("result")) {
                switch (data.getStringExtra("result")) {
                    case "success":
                        call.resolve();
                        break;
                    case "failed":
                    case "error":
                        call.reject(data.getStringExtra("errorDetails"), data.getStringExtra("errorCode"));
                        break;
                    default:
                        call.reject("Something went wrong.");
                        break;
                }
            }
            return;
        }
        call.reject("Something went wrong.");
    }

    @PluginMethod
    public void deleteCredentials(PluginCall call) {
        String server = call.getString("server", null);
        if (server != null) {
            try {
                getKeyStore().deleteEntry(server);
                SharedPreferences.Editor editor = getContext().getSharedPreferences(NATIVE_BIOMETRIC_SHARED_PREFERENCES, 0).edit();
                editor.clear();
                editor.apply();
                call.resolve();
                return;
            } catch (IOException | KeyStoreException | NoSuchAlgorithmException | CertificateException e) {
                call.reject("Failed to delete", e);
                return;
            }
        }
        call.reject("No server name was provided");
    }

    private String encryptString(String stringToEncrypt, String KEY_ALIAS) throws GeneralSecurityException, IOException {
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(1, getKey(KEY_ALIAS), new GCMParameterSpec(128, FIXED_IV));
        byte[] encodedBytes = cipher.doFinal(stringToEncrypt.getBytes("UTF-8"));
        return Base64.encodeToString(encodedBytes, 0);
    }

    private String decryptString(String stringToDecrypt, String KEY_ALIAS) throws GeneralSecurityException, IOException {
        byte[] encryptedData = Base64.decode(stringToDecrypt, 0);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(2, getKey(KEY_ALIAS), new GCMParameterSpec(128, FIXED_IV));
        byte[] decryptedData = cipher.doFinal(encryptedData);
        return new String(decryptedData, "UTF-8");
    }

    private int getAvailableFeature() {
        int type = 0;
        if (getContext().getPackageManager().hasSystemFeature("android.hardware.fingerprint")) {
            type = 3;
        }
        if (getContext().getPackageManager().hasSystemFeature("android.hardware.biometrics.face")) {
            if (type != 0) {
                return 6;
            }
            type = 4;
        }
        if (getContext().getPackageManager().hasSystemFeature("android.hardware.biometrics.iris")) {
            return type != 0 ? 6 : 5;
        }
        return type;
    }

    private Key generateKey(String KEY_ALIAS) throws GeneralSecurityException, IOException {
        try {
            Key key = generateKey(KEY_ALIAS, true);
            return key;
        } catch (StrongBoxUnavailableException e) {
            Key key2 = generateKey(KEY_ALIAS, false);
            return key2;
        }
    }

    private Key generateKey(String KEY_ALIAS, boolean isStrongBoxBacked) throws GeneralSecurityException, IOException, StrongBoxUnavailableException {
        KeyGenerator generator = KeyGenerator.getInstance("AES", ANDROID_KEY_STORE);
        KeyGenParameterSpec.Builder paramBuilder = new KeyGenParameterSpec.Builder(KEY_ALIAS, 3).setBlockModes(CodePackage.GCM).setEncryptionPaddings("NoPadding").setRandomizedEncryptionRequired(false);
        if (Build.VERSION.SDK_INT >= 28) {
            paramBuilder.setUnlockedDeviceRequired(true);
            paramBuilder.setIsStrongBoxBacked(isStrongBoxBacked);
        }
        generator.init(paramBuilder.build());
        return generator.generateKey();
    }

    private Key getKey(String KEY_ALIAS) throws GeneralSecurityException, IOException {
        KeyStore.SecretKeyEntry secretKeyEntry = (KeyStore.SecretKeyEntry) getKeyStore().getEntry(KEY_ALIAS, null);
        if (secretKeyEntry != null) {
            return secretKeyEntry.getSecretKey();
        }
        return generateKey(KEY_ALIAS);
    }

    private KeyStore getKeyStore() throws KeyStoreException, CertificateException, NoSuchAlgorithmException, IOException {
        if (this.keyStore == null) {
            KeyStore keyStore = KeyStore.getInstance(ANDROID_KEY_STORE);
            this.keyStore = keyStore;
            keyStore.load(null);
        }
        return this.keyStore;
    }

    private Key getAESKey(String KEY_ALIAS) throws CertificateException, NoSuchPaddingException, InvalidKeyException, NoSuchAlgorithmException, KeyStoreException, NoSuchProviderException, UnrecoverableEntryException, IOException, InvalidAlgorithmParameterException {
        SharedPreferences sharedPreferences = getContext().getSharedPreferences("", 0);
        String encryptedKeyB64 = sharedPreferences.getString(ENCRYPTED_KEY, null);
        if (encryptedKeyB64 == null) {
            byte[] key = new byte[16];
            SecureRandom secureRandom = new SecureRandom();
            secureRandom.nextBytes(key);
            byte[] encryptedKey = rsaEncrypt(key, KEY_ALIAS);
            String encryptedKeyB642 = Base64.encodeToString(encryptedKey, 0);
            SharedPreferences.Editor edit = sharedPreferences.edit();
            edit.putString(ENCRYPTED_KEY, encryptedKeyB642);
            edit.apply();
            return new SecretKeySpec(key, "AES");
        }
        byte[] encryptedKey2 = Base64.decode(encryptedKeyB64, 0);
        return new SecretKeySpec(rsaDecrypt(encryptedKey2, KEY_ALIAS), "AES");
    }

    private KeyStore.PrivateKeyEntry getPrivateKeyEntry(String KEY_ALIAS) throws NoSuchProviderException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, CertificateException, KeyStoreException, IOException, UnrecoverableEntryException {
        KeyStore.PrivateKeyEntry privateKeyEntry = (KeyStore.PrivateKeyEntry) getKeyStore().getEntry(KEY_ALIAS, null);
        if (privateKeyEntry == null) {
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA", ANDROID_KEY_STORE);
            keyPairGenerator.initialize(new KeyPairGeneratorSpec.Builder(getContext()).setAlias(KEY_ALIAS).build());
            keyPairGenerator.generateKeyPair();
        }
        return privateKeyEntry;
    }

    private byte[] rsaEncrypt(byte[] secret, String KEY_ALIAS) throws CertificateException, NoSuchAlgorithmException, KeyStoreException, IOException, UnrecoverableEntryException, NoSuchProviderException, NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException {
        KeyStore.PrivateKeyEntry privateKeyEntry = getPrivateKeyEntry(KEY_ALIAS);
        Cipher inputCipher = Cipher.getInstance(RSA_MODE, "AndroidOpenSSL");
        inputCipher.init(1, privateKeyEntry.getCertificate().getPublicKey());
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        CipherOutputStream cipherOutputStream = new CipherOutputStream(outputStream, inputCipher);
        cipherOutputStream.write(secret);
        cipherOutputStream.close();
        return outputStream.toByteArray();
    }

    private byte[] rsaDecrypt(byte[] encrypted, String KEY_ALIAS) throws UnrecoverableEntryException, NoSuchAlgorithmException, KeyStoreException, NoSuchProviderException, NoSuchPaddingException, InvalidKeyException, IOException, CertificateException, InvalidAlgorithmParameterException {
        KeyStore.PrivateKeyEntry privateKeyEntry = getPrivateKeyEntry(KEY_ALIAS);
        Cipher output = Cipher.getInstance(RSA_MODE, "AndroidOpenSSL");
        output.init(2, privateKeyEntry.getPrivateKey());
        CipherInputStream cipherInputStream = new CipherInputStream(new ByteArrayInputStream(encrypted), output);
        ArrayList<Byte> values = new ArrayList<>();
        while (true) {
            int nextByte = cipherInputStream.read();
            if (nextByte == -1) {
                break;
            }
            values.add(Byte.valueOf((byte) nextByte));
        }
        byte[] bytes = new byte[values.size()];
        for (int i = 0; i < bytes.length; i++) {
            bytes[i] = values.get(i).byteValue();
        }
        return bytes;
    }

    private boolean deviceHasCredentials() {
        KeyguardManager keyguardManager = (KeyguardManager) getActivity().getSystemService("keyguard");
        return keyguardManager.isDeviceSecure();
    }
}
