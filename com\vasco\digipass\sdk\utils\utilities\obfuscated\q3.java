package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\q3.smali */
public class q3 extends u {
    r C;
    x L;
    r R;
    BigInteger b;
    x u0;
    p3 x;

    private q3(e0 e0Var) {
        this.b = BigInteger.valueOf(0L);
        int i = 0;
        if (e0Var.a(0) instanceof j0) {
            j0 j0Var = (j0) e0Var.a(0);
            if (!j0Var.k() || j0Var.j() != 0) {
                throw new IllegalArgumentException("object parse error");
            }
            this.b = r.a((Object) j0Var.a()).i();
            i = 1;
        }
        this.x = p3.a(e0Var.a(i));
        int i2 = i + 1;
        this.C = r.a(e0Var.a(i2));
        int i3 = i2 + 1;
        this.L = x.a(e0Var.a(i3));
        int i4 = i3 + 1;
        this.R = r.a(e0Var.a(i4));
        this.u0 = x.a(e0Var.a(i4 + 1));
    }

    public static q3 a(Object obj) {
        if (obj instanceof q3) {
            return (q3) obj;
        }
        if (obj != null) {
            return new q3(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.C.i();
    }

    public byte[] f() {
        return Arrays.clone(this.L.h());
    }

    public p3 g() {
        return this.x;
    }

    public BigInteger getN() {
        return this.R.i();
    }

    public byte[] h() {
        return Arrays.clone(this.u0.h());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(6);
        if (this.b.compareTo(BigInteger.valueOf(0L)) != 0) {
            iVar.a(new m2(true, 0, new r(this.b)));
        }
        iVar.a(this.x);
        iVar.a(this.C);
        iVar.a(this.L);
        iVar.a(this.R);
        iVar.a(this.u0);
        return new j2(iVar);
    }
}
