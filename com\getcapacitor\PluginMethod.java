package com.getcapacitor;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\PluginMethod.smali */
public @interface PluginMethod {
    public static final String RETURN_CALLBACK = "callback";
    public static final String RETURN_NONE = "none";
    public static final String RETURN_PROMISE = "promise";

    String returnType() default "promise";
}
