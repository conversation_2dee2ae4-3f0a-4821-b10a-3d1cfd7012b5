package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP128R1FieldElement.smali */
public class SecP128R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF"));
    protected int[] a;

    public SecP128R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP128R1FieldElement");
        }
        this.a = SecP128R1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = s5.a();
        SecP128R1Field.add(this.a, ((SecP128R1FieldElement) eCFieldElement).a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = s5.a();
        SecP128R1Field.addOne(this.a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = s5.a();
        SecP128R1Field.inv(((SecP128R1FieldElement) eCFieldElement).a, a);
        SecP128R1Field.multiply(a, this.a, a);
        return new SecP128R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP128R1FieldElement) {
            return s5.a(this.a, ((SecP128R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP128R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 4);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = s5.a();
        SecP128R1Field.inv(this.a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return s5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return s5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = s5.a();
        SecP128R1Field.multiply(this.a, ((SecP128R1FieldElement) eCFieldElement).a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = s5.a();
        SecP128R1Field.negate(this.a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (s5.b(iArr) || s5.a(iArr)) {
            return this;
        }
        int[] a = s5.a();
        SecP128R1Field.square(iArr, a);
        SecP128R1Field.multiply(a, iArr, a);
        int[] a2 = s5.a();
        SecP128R1Field.squareN(a, 2, a2);
        SecP128R1Field.multiply(a2, a, a2);
        int[] a3 = s5.a();
        SecP128R1Field.squareN(a2, 4, a3);
        SecP128R1Field.multiply(a3, a2, a3);
        SecP128R1Field.squareN(a3, 2, a2);
        SecP128R1Field.multiply(a2, a, a2);
        SecP128R1Field.squareN(a2, 10, a);
        SecP128R1Field.multiply(a, a2, a);
        SecP128R1Field.squareN(a, 10, a3);
        SecP128R1Field.multiply(a3, a2, a3);
        SecP128R1Field.square(a3, a2);
        SecP128R1Field.multiply(a2, iArr, a2);
        SecP128R1Field.squareN(a2, 95, a2);
        SecP128R1Field.square(a2, a3);
        if (s5.a(iArr, a3)) {
            return new SecP128R1FieldElement(a2);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = s5.a();
        SecP128R1Field.square(this.a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = s5.a();
        SecP128R1Field.subtract(this.a, ((SecP128R1FieldElement) eCFieldElement).a, a);
        return new SecP128R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return s5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return s5.c(this.a);
    }

    public SecP128R1FieldElement() {
        this.a = s5.a();
    }

    protected SecP128R1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
