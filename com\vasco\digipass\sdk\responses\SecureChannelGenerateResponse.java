package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.models.SecureChannelMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\SecureChannelGenerateResponse.smali */
public class SecureChannelGenerateResponse extends DigipassResponse {
    private SecureChannelMessage c;

    public SecureChannelGenerateResponse(int i) {
        super(i);
    }

    public SecureChannelMessage getSecureChannelMessage() {
        return this.c;
    }

    public SecureChannelGenerateResponse(int i, Throwable th) {
        super(i, th);
    }

    public SecureChannelGenerateResponse(int i, SecureChannelMessage secureChannelMessage) {
        super(i);
        this.c = secureChannelMessage;
    }
}
