package o.ad;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.l;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ad\a.smali */
public final class a extends o.h.a {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        b();
        int i = a + 99;
        c = i % 128;
        int i2 = i % 2;
    }

    static void b() {
        e = new char[]{50932, 50854, 50857, 50863, 50853, 50832, 50834, 50850, 50859, 50856, 50851, 50876, 50852, 50857, 50857, 50854, 50839, 50838, 50876, 50852, 50852, 50852, 50859, 50857, 50858, 50858, 50842, 50861, 50849, 50853, 50849, 50859, 50833};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r6 = r6 + 66
            byte[] r0 = o.ad.a.$$d
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r8) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.a.h(short, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{3, 65, Tnaf.POW_2_WIDTH, 100};
        $$e = 3;
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public a() {
        /*
            r5 = this;
            r0 = 33
            r1 = 0
            int[] r0 = new int[]{r1, r0, r1, r1}
            r2 = 1
            java.lang.Object[] r3 = new java.lang.Object[r2]
            java.lang.String r4 = "\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000"
            g(r4, r0, r2, r3)
            r0 = r3[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            o.i.i r1 = o.i.i.a
            r5.<init>(r0, r1, r2)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.a.<init>():void");
    }

    private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            int i2 = $10 + 11;
            $11 = i2 % 128;
            int i3 = i2 % 2;
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i4 = 0;
        int i5 = iArr[0];
        int i6 = iArr[1];
        int i7 = iArr[2];
        int i8 = iArr[3];
        char[] cArr2 = e;
        switch (cArr2 != null) {
            case true:
                int length = cArr2.length;
                char[] cArr3 = new char[length];
                int i9 = 0;
                while (true) {
                    switch (i9 < length ? '^' : Typography.amp) {
                        case Opcodes.DUP2_X2 /* 94 */:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i4] = Integer.valueOf(cArr2[i9]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    cArr = cArr2;
                                } else {
                                    Class cls = (Class) o.e.a.c(View.MeasureSpec.getSize(i4) + 11, (char) (Process.myTid() >> 22), 43 - Color.argb(i4, i4, i4, i4));
                                    byte b = (byte) ($$e - 4);
                                    cArr = cArr2;
                                    Object[] objArr3 = new Object[1];
                                    h((byte) 54, b, (byte) (b + 1), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr3[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i9++;
                                cArr2 = cArr;
                                i4 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr2 = cArr3;
                            break;
                    }
                }
        }
        char[] cArr4 = new char[i6];
        System.arraycopy(cArr2, i5, cArr4, 0, i6);
        if (bArr2 != null) {
            int i10 = $10 + 63;
            $11 = i10 % 128;
            int i11 = i10 % 2;
            char[] cArr5 = new char[i6];
            lVar.d = 0;
            char c2 = 0;
            while (lVar.d < i6) {
                int i12 = $11 + 77;
                $10 = i12 % 128;
                int i13 = i12 % 2;
                switch (bArr2[lVar.d] == 1 ? '_' : ',') {
                    case ',':
                        int i14 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                            Object obj2 = o.e.a.s.get(804049217);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(Color.red(0) + 10, (char) TextUtils.getTrimmedLength(""), 207 - TextUtils.getTrimmedLength(""));
                                byte b2 = (byte) ($$e - 4);
                                Object[] objArr5 = new Object[1];
                                h((byte) 56, b2, (byte) (b2 + 1), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(804049217, obj2);
                            }
                            cArr5[i14] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        int i15 = lVar.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                            Object obj3 = o.e.a.s.get(2016040108);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0'), (char) (ViewConfiguration.getPressedStateDuration() >> 16), ImageFormat.getBitsPerPixel(0) + 449);
                                byte b3 = (byte) ($$e - 4);
                                Object[] objArr7 = new Object[1];
                                h((byte) 53, b3, (byte) (b3 + 1), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2016040108, obj3);
                            }
                            cArr5[i15] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                }
                c2 = cArr5[lVar.d];
                try {
                    Object[] objArr8 = {lVar, lVar};
                    Object obj4 = o.e.a.s.get(-2112603350);
                    if (obj4 == null) {
                        Class cls4 = (Class) o.e.a.c(12 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (char) ((Process.getThreadPriority(0) + 20) >> 6), 259 - (ViewConfiguration.getScrollDefaultDelay() >> 16));
                        byte b4 = (byte) ($$e - 3);
                        byte b5 = (byte) (b4 - 1);
                        Object[] objArr9 = new Object[1];
                        h(b4, b5, (byte) (b5 + 1), objArr9);
                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                        o.e.a.s.put(-2112603350, obj4);
                    }
                    ((Method) obj4).invoke(null, objArr8);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            cArr4 = cArr5;
        }
        if (i8 > 0) {
            char[] cArr6 = new char[i6];
            i = 0;
            System.arraycopy(cArr4, 0, cArr6, 0, i6);
            int i16 = i6 - i8;
            System.arraycopy(cArr6, 0, cArr4, i16, i8);
            System.arraycopy(cArr6, i8, cArr4, 0, i16);
        } else {
            i = 0;
        }
        if (z) {
            char[] cArr7 = new char[i6];
            lVar.d = i;
            while (lVar.d < i6) {
                cArr7[lVar.d] = cArr4[(i6 - lVar.d) - 1];
                lVar.d++;
            }
            cArr4 = cArr7;
        }
        if (i7 > 0) {
            int i17 = 0;
            while (true) {
                lVar.d = i17;
                switch (lVar.d < i6) {
                    case false:
                        break;
                    default:
                        cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                        i17 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr4);
    }
}
