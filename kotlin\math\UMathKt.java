package kotlin.math;

import kotlin.Metadata;
import kotlin.comparisons.UComparisonsKt;

/* compiled from: UMath.kt */
@Metadata(d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a#\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0001H\u0087\bø\u0001\u0000¢\u0006\u0004\b\u0004\u0010\u0005\u001a#\u0010\u0000\u001a\u00020\u00062\u0006\u0010\u0002\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u0006H\u0087\bø\u0001\u0000¢\u0006\u0004\b\u0007\u0010\b\u001a#\u0010\t\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u0001H\u0087\bø\u0001\u0000¢\u0006\u0004\b\n\u0010\u0005\u001a#\u0010\t\u001a\u00020\u00062\u0006\u0010\u0002\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u0006H\u0087\bø\u0001\u0000¢\u0006\u0004\b\u000b\u0010\b\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\f"}, d2 = {"max", "Lkotlin/UInt;", "a", "b", "max-J1ME1BU", "(II)I", "Lkotlin/ULong;", "max-eb3DHEI", "(JJ)J", "min", "min-J1ME1BU", "min-eb3DHEI", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\math\UMathKt.smali */
public final class UMathKt {
    /* renamed from: min-J1ME1BU, reason: not valid java name */
    private static final int m1450minJ1ME1BU(int a, int b) {
        return UComparisonsKt.m1431minOfJ1ME1BU(a, b);
    }

    /* renamed from: min-eb3DHEI, reason: not valid java name */
    private static final long m1451mineb3DHEI(long a, long b) {
        return UComparisonsKt.m1439minOfeb3DHEI(a, b);
    }

    /* renamed from: max-J1ME1BU, reason: not valid java name */
    private static final int m1448maxJ1ME1BU(int a, int b) {
        return UComparisonsKt.m1419maxOfJ1ME1BU(a, b);
    }

    /* renamed from: max-eb3DHEI, reason: not valid java name */
    private static final long m1449maxeb3DHEI(long a, long b) {
        return UComparisonsKt.m1427maxOfeb3DHEI(a, b);
    }
}
