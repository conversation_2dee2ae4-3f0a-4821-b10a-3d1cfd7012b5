package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfn.smali */
final class zzfn implements zzfu {
    private final zzfu[] zza;

    zzfn(zzfu... zzfuVarArr) {
        this.zza = zzfuVarArr;
    }

    public final zzft zzb(Class cls) {
        zzfu[] zzfuVarArr = this.zza;
        for (int i = 0; i < 2; i++) {
            zzfu zzfuVar = zzfuVarArr[i];
            if (zzfuVar.zzc(cls)) {
                return zzfuVar.zzb(cls);
            }
        }
        throw new UnsupportedOperationException("No factory is available for message type: ".concat(String.valueOf(cls.getName())));
    }

    public final boolean zzc(Class cls) {
        zzfu[] zzfuVarArr = this.zza;
        for (int i = 0; i < 2; i++) {
            if (zzfuVarArr[i].zzc(cls)) {
                return true;
            }
        }
        return false;
    }
}
