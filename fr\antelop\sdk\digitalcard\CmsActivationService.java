package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import o.bv.c;
import o.er.l;
import o.g.b;
import o.p.g;
import o.v.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\CmsActivationService.smali */
public final class CmsActivationService {
    private final l innerCmsActivationService;

    public CmsActivationService(l lVar) {
        this.innerCmsActivationService = lVar;
    }

    public final boolean isPinCodeRequired() {
        return this.innerCmsActivationService.c();
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerCmsActivationService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final SecureCmsActivation getSecureCmsActivation() throws WalletValidationException {
        return new SecureCmsActivation(this.innerCmsActivationService.a());
    }

    public final void activate(Context context, SecurePinInput securePinInput, final OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        final e a = this.innerCmsActivationService.a();
        a.b(context, securePinInput, new g() { // from class: fr.antelop.sdk.digitalcard.CmsActivationService.1
            @Override // o.p.g
            public void onProcessSuccess() {
                operationCallback.onSuccess(Boolean.valueOf(a.a()));
            }

            @Override // o.p.g
            public void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
            }

            @Override // o.p.g
            public void onError(c cVar) {
                operationCallback.onError(cVar.d());
            }

            @Override // o.p.g
            public void onCustomerCredentialsInvalid(b bVar) {
            }

            @Override // o.p.g
            public void onProcessStart() {
            }

            @Override // o.p.g
            public void onAuthenticationDeclined() {
            }

            @Override // o.p.g
            public void abortPrompt() {
            }
        });
    }
}
