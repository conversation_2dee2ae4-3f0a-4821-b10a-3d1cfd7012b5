package kotlinx.coroutines.flow;

import kotlin.Metadata;

/* compiled from: Context.kt */
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b`\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u00012\b\u0012\u0004\u0012\u0002H\u00010\u0002¨\u0006\u0003"}, d2 = {"Lkotlinx/coroutines/flow/CancellableFlow;", "T", "Lkotlinx/coroutines/flow/Flow;", "kotlinx-coroutines-core"}, k = 1, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\flow\CancellableFlow.smali */
public interface CancellableFlow<T> extends Flow<T> {
}
