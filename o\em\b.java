package o.em;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import java.util.List;
import kotlin.text.Typography;
import o.ag.a;
import o.du.i;
import o.ee.o;
import o.em.d;
import o.er.t;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\b.smali */
public final class b extends d<t> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static short[] a;
    private static int b;
    private static int c;
    private static byte[] d;
    private static int e;
    private static int f;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        i();
        KeyEvent.normalizeMetaState(0);
        AudioTrack.getMaxVolume();
        ViewConfiguration.getPressedStateDuration();
        TypedValue.complexToFloat(0);
        ViewConfiguration.getZoomControlsTimeout();
        int i = h + 5;
        f = i % 128;
        switch (i % 2 != 0 ? 'S' : '!') {
            case Opcodes.AASTORE /* 83 */:
                int i2 = 95 / 0;
                break;
        }
    }

    static void i() {
        d = new byte[]{13, 58, 53, -116, -109, -107, -93, 100, -121, -104, -102, -103, 15, 46, 66, 41, 50, -100, -65, 104, -118, -102, -123, 63, -28, -25, -10, -26, -9, 5, -33, -16, -17, 28, -64, -53, 17, 27, -44, -25, -8, -6, 25, 50, 79, 104, 77, 102, 123, 77, 49, 20, -2, -27, Tnaf.POW_2_WIDTH, -17, 85, 26, -18, 4, -17, 31, 7, -18, 77, -83, 26, 1, Tnaf.POW_2_WIDTH, 12, 25, 18, 27, 26, 7, 94, -17, -64, 20, 0, 22, 27, 20, 30, 77, -89, 0, 28, 73, -88, 5, 68, -89, 10, 20, 27, 6, 5};
        e = 909053685;
        c = -1803673148;
        b = 1086031797;
    }

    static void init$0() {
        $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
        $$b = 104;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = r9 + 108
            byte[] r0 = o.em.b.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = -r7
            int r7 = r7 + r10
            int r9 = r9 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.b.j(int, int, int, java.lang.Object[]):void");
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ long a() {
        int i = h + 75;
        f = i % 128;
        int i2 = i % 2;
        long a2 = super.a();
        int i3 = f + 63;
        h = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 51 / 0;
                return a2;
            default:
                return a2;
        }
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ void b() {
        int i = f + 31;
        h = i % 128;
        int i2 = i % 2;
        super.b();
        int i3 = f + Opcodes.DSUB;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 14 : '_') {
            case 14:
                throw null;
            default:
                return;
        }
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ List<t> d() {
        int i = h + Opcodes.LSHL;
        f = i % 128;
        int i2 = i % 2;
        List<t> d2 = super.d();
        int i3 = f + Opcodes.DNEG;
        h = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return d2;
            default:
                int i4 = 94 / 0;
                return d2;
        }
    }

    @Override // o.em.d
    protected final /* synthetic */ o.eg.b d(t tVar) throws o.eg.d {
        int i = h + 53;
        f = i % 128;
        int i2 = i % 2;
        o.eg.b a2 = a(tVar);
        int i3 = f + 71;
        h = i3 % 128;
        int i4 = i3 % 2;
        return a2;
    }

    @Override // o.em.d
    protected final /* synthetic */ t e(o.eg.b bVar) throws o.eg.d {
        int i = h + 49;
        f = i % 128;
        int i2 = i % 2;
        t a2 = a(bVar);
        int i3 = f + 87;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? '*' : '/') {
            case '/':
                return a2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static o.eg.b a(t tVar) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        g((byte) Color.green(0), (-1989445925) + (ViewConfiguration.getLongPressTimeout() >> 16), (short) (81 - (ViewConfiguration.getScrollBarSize() >> 8)), View.getDefaultSize(0, 0) - 102, TextUtils.lastIndexOf("", '0', 0) + 1571737878, objArr);
        bVar.d(((String) objArr[0]).intern(), tVar.e());
        Object[] objArr2 = new Object[1];
        g((byte) View.MeasureSpec.getMode(0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1989445923, (short) ((-15) - TextUtils.lastIndexOf("", '0', 0, 0)), (-103) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 1571737888, objArr2);
        bVar.d(((String) objArr2[0]).intern(), o.a(tVar.c()));
        Object[] objArr3 = new Object[1];
        g((byte) (ViewConfiguration.getKeyRepeatDelay() >> 16), (-1989445914) - TextUtils.indexOf((CharSequence) "", '0'), (short) (58 - TextUtils.getOffsetAfter("", 0)), (-102) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), View.combineMeasuredStates(0, 0) + 1571737882, objArr3);
        bVar.d(((String) objArr3[0]).intern(), tVar.b());
        Object[] objArr4 = new Object[1];
        g((byte) (Process.myTid() >> 22), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1989445909, (short) (KeyEvent.normalizeMetaState(0) - 18), (-103) - MotionEvent.axisFromString(""), TextUtils.getOffsetBefore("", 0) + 1571737880, objArr4);
        bVar.d(((String) objArr4[0]).intern(), tVar.a());
        int i = f + 63;
        h = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    private static t a(o.eg.b bVar) throws o.eg.d {
        i iVar;
        Object[] objArr = new Object[1];
        g((byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), (-1989445925) + (Process.myPid() >> 22), (short) (TextUtils.lastIndexOf("", '0') + 82), (-102) - (ViewConfiguration.getKeyRepeatDelay() >> 16), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 1571737877, objArr);
        String r = bVar.r(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g((byte) ((-1) - TextUtils.lastIndexOf("", '0')), View.resolveSizeAndState(0, 0, 0) - 1989445923, (short) ((-14) - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), Color.rgb(0, 0, 0) + 16777114, 1571737888 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr2);
        t.d[] dVarArr = (t.d[]) bVar.c(t.d.class, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g((byte) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (-1989445914) + (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (short) (58 - TextUtils.indexOf("", "", 0)), (-102) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 1571737882 + (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr3);
        String q = bVar.q(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        g((byte) TextUtils.getOffsetBefore("", 0), (-1989445909) + (ViewConfiguration.getLongPressTimeout() >> 16), (short) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 19), (-102) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), View.getDefaultSize(0, 0) + 1571737880, objArr4);
        String q2 = bVar.q(((String) objArr4[0]).intern());
        if (q2 == null) {
            iVar = null;
        } else {
            iVar = new i(q2);
            int i = f + 21;
            h = i % 128;
            int i2 = i % 2;
        }
        t tVar = new t(r, dVarArr, q, iVar);
        int i3 = h + Opcodes.DDIV;
        f = i3 % 128;
        switch (i3 % 2 != 0 ? '\'' : (char) 0) {
            case 0:
                return tVar;
            default:
                throw null;
        }
    }

    public final void d(Context context, String str, final d.e<t> eVar) throws WalletValidationException {
        int i = f + 49;
        h = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        g((byte) ExpandableListView.getPackedPositionType(0L), (-1989445901) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (short) ((Process.myPid() >> 22) - 110), (-102) - (ViewConfiguration.getTouchSlop() >> 8), 1571737856 + (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((byte) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-1989445882) - (ViewConfiguration.getTouchSlop() >> 8), (short) (23 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), (-103) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), 1571737886 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!o.ei.c.c().q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            g((byte) Drawable.resolveOpacity(0, 0), TextUtils.indexOf((CharSequence) "", '0') - 1989445874, (short) (TextUtils.indexOf("", "", 0) - 117), (-102) - ((Process.getThreadPriority(0) + 20) >> 6), 1554960643 - Color.rgb(0, 0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g((byte) TextUtils.getTrimmedLength(""), (-1989445869) - (ViewConfiguration.getKeyRepeatDelay() >> 16), (short) (117 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 101, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1571737858, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.ag.a(context, new a.d() { // from class: o.em.b.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int c;
            private static int e;
            private static int f;
            private static byte[] h;
            private static short[] i;
            private static int j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                j = 0;
                f = 1;
                h = new byte[]{74, -17, 18, -31, 17, -30, 48, -54, -5, 26, 7, -53, -10, 28, 6, -33, 18, -29, -27, 4, 101, -50, -24, -74, -50, -8, -28, -2, -29, 22, -39, -16, -7, 0, -39, -2, 4, -82, -4, -2, -10, -4, -23, -53, -1, -25, -18, -33, -28, -11, -9, 22, -37, -22, 25, -44, -6, 42, -50, -24, -77, -16, -23, -50, -25, -4, -50};
                e = 909053610;
                c = 1181913831;
                a = 1796535387;
            }

            static void init$0() {
                $$a = new byte[]{43, -103, 93, -106};
                $$b = 215;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0039). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void k(byte r7, byte r8, int r9, java.lang.Object[] r10) {
                /*
                    int r9 = r9 * 4
                    int r9 = 3 - r9
                    int r7 = r7 * 2
                    int r7 = 1 - r7
                    int r8 = r8 * 2
                    int r8 = r8 + 108
                    byte[] r0 = o.em.b.AnonymousClass3.$$a
                    byte[] r1 = new byte[r7]
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r8 = r7
                    r3 = r1
                    r5 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    goto L39
                L1a:
                    r3 = r2
                    r6 = r9
                    r9 = r8
                L1d:
                    r8 = r6
                    byte r4 = (byte) r9
                    int r5 = r3 + 1
                    r1[r3] = r4
                    if (r5 != r7) goto L2d
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2d:
                    int r8 = r8 + 1
                    r3 = r0[r8]
                    r6 = r8
                    r8 = r7
                    r7 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    r9 = r6
                L39:
                    int r7 = -r7
                    int r7 = r7 + r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r5
                    r6 = r9
                    r9 = r7
                    r7 = r8
                    goto L1d
                */
                throw new UnsupportedOperationException("Method not decompiled: o.em.b.AnonymousClass3.k(byte, byte, int, java.lang.Object[]):void");
            }

            @Override // o.ag.a.d
            public final void c(List<t> list) {
                b.this.a(list, new Date().getTime());
                eVar.e(list);
                int i3 = f + 31;
                j = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return;
                    default:
                        throw null;
                }
            }

            @Override // o.ag.a.d
            public final void c(o.bb.d dVar) {
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                g((byte) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (-1564211915) - View.MeasureSpec.getSize(0), (short) (View.getDefaultSize(0, 0) - 121), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 58, (-1885183012) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                g((byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), (-1564211894) - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (short) ((ViewConfiguration.getKeyRepeatDelay() >> 16) - 107), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 59, KeyEvent.getDeadChar(0, 0) - 1885182981, objArr6);
                o.ee.g.d(intern3, sb.append(((String) objArr6[0]).intern()).append(dVar.e()).toString());
                eVar.e(o.bv.c.c(dVar).d());
                int i3 = f + 21;
                j = i3 % 128;
                int i4 = i3 % 2;
            }

            /* JADX WARN: Code restructure failed: missing block: B:82:0x02a9, code lost:
            
                r3 = r7;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
                /*
                    Method dump skipped, instructions count: 852
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.em.b.AnonymousClass3.g(byte, int, short, int, int, java.lang.Object[]):void");
            }
        }, o.ei.c.c()).c(str);
        int i3 = h + 47;
        f = i3 % 128;
        switch (i3 % 2 != 0 ? Typography.greater : '6') {
            case '>':
                int i4 = 93 / 0;
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:71:0x0297, code lost:
    
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 822
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.b.g(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
