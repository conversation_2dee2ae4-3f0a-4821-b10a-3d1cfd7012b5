package o.et;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\g.smali */
public class g extends c {
    private static int a = 0;
    private static int d = 1;

    @Override // o.el.d
    public final /* synthetic */ o.ey.e a(String str) {
        int i = d + Opcodes.DNEG;
        a = i % 128;
        switch (i % 2 != 0 ? '\t' : ',') {
            case ',':
                o.fa.a c = c(str);
                int i2 = (a + 18) - 1;
                d = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        throw null;
                    default:
                        return c;
                }
            default:
                c(str);
                throw null;
        }
    }

    public g(String str, String str2, int i, String str3) {
        super(str, o.dp.b.b, str2, i, str3);
    }

    @Override // o.et.c
    protected final c c(String str, String str2, int i, String str3) {
        g gVar = new g(str, str2, i, str3);
        int i2 = a + 27;
        d = i2 % 128;
        int i3 = i2 % 2;
        return gVar;
    }

    private o.fa.a c(String str) {
        o.fa.a aVar = new o.fa.a(n(), str, false);
        int i = a;
        int i2 = ((i | 89) << 1) - (i ^ 89);
        d = i2 % 128;
        switch (i2 % 2 == 0 ? ' ' : (char) 6) {
            case 6:
                return aVar;
            default:
                throw null;
        }
    }

    @Override // o.et.c
    public final byte[] E() {
        int i = a;
        int i2 = (i ^ Opcodes.LREM) + ((i & Opcodes.LREM) << 1);
        int i3 = i2 % 128;
        d = i3;
        int i4 = i2 % 2;
        int i5 = i3 + 77;
        a = i5 % 128;
        boolean z = i5 % 2 == 0;
        Object obj = null;
        switch (z) {
            case true:
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.et.c
    public EmvApplicationType e() {
        int i = (a + Opcodes.IREM) - 1;
        d = i % 128;
        switch (i % 2 == 0 ? 'D' : '\'') {
            case 'D':
                EmvApplicationType emvApplicationType = EmvApplicationType.HceIssuerPure;
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return EmvApplicationType.HceIssuerPure;
        }
    }
}
