package org.bouncycastle.crypto.engines;

import androidx.core.view.InputDeviceCompat;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.BlockCipher;
import org.bouncycastle.crypto.CipherParameters;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.OutputLengthException;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;
import org.bouncycastle.util.Integers;
import org.bouncycastle.util.Pack;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\crypto\engines\TwofishEngine.smali */
public final class TwofishEngine implements BlockCipher {
    private static final int BLOCK_SIZE = 16;
    private static final int GF256_FDBK = 361;
    private static final int GF256_FDBK_2 = 180;
    private static final int GF256_FDBK_4 = 90;
    private static final int INPUT_WHITEN = 0;
    private static final int MAX_KEY_BITS = 256;
    private static final int MAX_ROUNDS = 16;
    private static final int OUTPUT_WHITEN = 4;
    private static final byte[][] P = {new byte[]{-87, 103, -77, -24, 4, -3, -93, 118, -102, -110, ByteCompanionObject.MIN_VALUE, 120, -28, -35, -47, 56, 13, -58, 53, -104, 24, -9, -20, 108, 67, 117, 55, 38, -6, 19, -108, 72, -14, -48, -117, 48, -124, 84, -33, 35, 25, 91, Base64.padSymbol, 89, -13, -82, -94, -126, 99, 1, -125, 46, -39, 81, -101, 124, -90, -21, -91, -66, 22, 12, -29, 97, -64, -116, 58, -11, 115, 44, 37, 11, -69, 78, -119, 107, 83, 106, -76, -15, -31, -26, -67, 69, -30, -12, -74, 102, -52, -107, 3, 86, -44, 28, 30, -41, -5, -61, -114, -75, -23, -49, -65, -70, -22, 119, 57, -81, 51, -55, 98, 113, -127, 121, 9, -83, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -51, -7, -40, -27, -59, -71, 77, 68, 8, -122, -25, -95, 29, -86, -19, 6, 112, UtilitiesSDKConstants.SRP_LABEL_MAC, -46, 65, 123, -96, 17, 49, -62, 39, -112, 32, -10, 96, -1, -106, 92, -79, -85, -98, -100, 82, 27, 95, -109, 10, -17, -111, -123, 73, -18, 45, 79, -113, 59, 71, -121, 109, 70, -42, 62, 105, 100, 42, -50, -53, 47, -4, -105, 5, 122, -84, ByteCompanionObject.MAX_VALUE, -43, 26, 75, 14, -89, 90, 40, 20, 63, 41, -120, 60, 76, 2, -72, -38, UtilitiesSDKConstants.SRP_LABEL_ENC, 23, 85, 31, -118, 125, 87, -57, -115, 116, -73, -60, -97, 114, 126, 21, 34, 18, 88, 7, -103, 52, 110, 80, -34, 104, 101, PSSSigner.TRAILER_IMPLICIT, -37, -8, -56, -88, 43, 64, -36, -2, 50, -92, -54, Tnaf.POW_2_WIDTH, 33, -16, -45, 93, 15, 0, 111, -99, 54, 66, 74, 94, -63, -32}, new byte[]{117, -13, -58, -12, -37, 123, -5, -56, 74, -45, -26, 107, 69, 125, -24, 75, -42, 50, -40, -3, 55, 113, -15, -31, 48, 15, -8, 27, -121, -6, 6, 63, 94, -70, -82, 91, -118, 0, PSSSigner.TRAILER_IMPLICIT, -99, 109, -63, -79, 14, ByteCompanionObject.MIN_VALUE, 93, -46, -43, -96, -124, 7, 20, -75, -112, 44, -93, UtilitiesSDKConstants.SRP_LABEL_MAC, 115, 76, 84, -110, 116, 54, 81, 56, UtilitiesSDKConstants.SRP_LABEL_ENC, -67, 90, -4, 96, 98, -106, 108, 66, -9, Tnaf.POW_2_WIDTH, 124, 40, 39, -116, 19, -107, -100, -57, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 70, 59, 112, -54, -29, -123, -53, 17, -48, -109, -72, -90, -125, 32, -1, -97, 119, -61, -52, 3, 111, 8, -65, 64, -25, 43, -30, 121, 12, -86, -126, 65, 58, -22, -71, -28, -102, -92, -105, 126, -38, 122, 23, 102, -108, -95, 29, Base64.padSymbol, -16, -34, -77, 11, 114, -89, 28, -17, -47, 83, 62, -113, 51, 38, 95, -20, 118, 42, 73, -127, -120, -18, 33, -60, 26, -21, -39, -59, 57, -103, -51, -83, 49, -117, 1, 24, 35, -35, 31, 78, 45, -7, 72, 79, -14, 101, -114, 120, 92, 88, 25, -115, -27, -104, 87, 103, ByteCompanionObject.MAX_VALUE, 5, 100, -81, 99, -74, -2, -11, -73, 60, -91, -50, -23, 104, 68, -32, 77, 67, 105, 41, 46, -84, 21, 89, -88, 10, -98, 110, 71, -33, 52, 53, 106, -49, -36, 34, -55, -64, -101, -119, -44, -19, -85, 18, -94, 13, 82, -69, 2, 47, -87, -41, 97, 30, -76, 80, 4, -10, -62, 22, 37, -122, 86, 85, 9, -66, -111}};
    private static final int P_00 = 1;
    private static final int P_01 = 0;
    private static final int P_02 = 0;
    private static final int P_03 = 1;
    private static final int P_04 = 1;
    private static final int P_10 = 0;
    private static final int P_11 = 0;
    private static final int P_12 = 1;
    private static final int P_13 = 1;
    private static final int P_14 = 0;
    private static final int P_20 = 1;
    private static final int P_21 = 1;
    private static final int P_22 = 0;
    private static final int P_23 = 0;
    private static final int P_24 = 0;
    private static final int P_30 = 0;
    private static final int P_31 = 1;
    private static final int P_32 = 1;
    private static final int P_33 = 0;
    private static final int P_34 = 1;
    private static final int ROUNDS = 16;
    private static final int ROUND_SUBKEYS = 8;
    private static final int RS_GF_FDBK = 333;
    private static final int SK_BUMP = 16843009;
    private static final int SK_ROTL = 9;
    private static final int SK_STEP = 33686018;
    private static final int TOTAL_SUBKEYS = 40;
    private int[] gSBox;
    private int[] gSubKeys;
    private boolean encrypting = false;
    private int[] gMDS0 = new int[256];
    private int[] gMDS1 = new int[256];
    private int[] gMDS2 = new int[256];
    private int[] gMDS3 = new int[256];
    private int k64Cnt = 0;
    private byte[] workingKey = null;

    public TwofishEngine() {
        int[] iArr = new int[2];
        int[] iArr2 = new int[2];
        int[] iArr3 = new int[2];
        for (int i = 0; i < 256; i++) {
            byte[][] bArr = P;
            int i2 = bArr[0][i] & 255;
            iArr[0] = i2;
            iArr2[0] = Mx_X(i2) & 255;
            iArr3[0] = Mx_Y(i2) & 255;
            int i3 = bArr[1][i] & 255;
            iArr[1] = i3;
            iArr2[1] = Mx_X(i3) & 255;
            int Mx_Y = Mx_Y(i3) & 255;
            iArr3[1] = Mx_Y;
            this.gMDS0[i] = (Mx_Y << 24) | iArr[1] | (iArr2[1] << 8) | (Mx_Y << 16);
            int[] iArr4 = this.gMDS1;
            int i4 = iArr3[0];
            iArr4[i] = i4 | (i4 << 8) | (iArr2[0] << 16) | (iArr[0] << 24);
            int[] iArr5 = this.gMDS2;
            int i5 = iArr2[1];
            int i6 = iArr3[1];
            iArr5[i] = (iArr[1] << 16) | i5 | (i6 << 8) | (i6 << 24);
            int[] iArr6 = this.gMDS3;
            int i7 = iArr2[0];
            iArr6[i] = (i7 << 24) | (iArr[0] << 8) | i7 | (iArr3[0] << 16);
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    private int F32(int i, int[] iArr) {
        int i2;
        int i3;
        int b0 = b0(i);
        int b1 = b1(i);
        int b2 = b2(i);
        int b3 = b3(i);
        int i4 = iArr[0];
        int i5 = iArr[1];
        int i6 = iArr[2];
        int i7 = iArr[3];
        switch (3 & this.k64Cnt) {
            case 0:
                byte[][] bArr = P;
                b0 = (bArr[1][b0] & 255) ^ b0(i7);
                b1 = (bArr[0][b1] & 255) ^ b1(i7);
                b2 = (bArr[0][b2] & 255) ^ b2(i7);
                b3 = (bArr[1][b3] & 255) ^ b3(i7);
                byte[][] bArr2 = P;
                b0 = (bArr2[1][b0] & 255) ^ b0(i6);
                b1 = (bArr2[1][b1] & 255) ^ b1(i6);
                b2 = (bArr2[0][b2] & 255) ^ b2(i6);
                b3 = (bArr2[0][b3] & 255) ^ b3(i6);
                int[] iArr2 = this.gMDS0;
                byte[][] bArr3 = P;
                byte[] bArr4 = bArr3[0];
                i2 = (iArr2[(bArr4[(bArr4[b0] & 255) ^ b0(i5)] & 255) ^ b0(i4)] ^ this.gMDS1[(bArr3[0][(bArr3[1][b1] & 255) ^ b1(i5)] & 255) ^ b1(i4)]) ^ this.gMDS2[(bArr3[1][(bArr3[0][b2] & 255) ^ b2(i5)] & 255) ^ b2(i4)];
                int[] iArr3 = this.gMDS3;
                byte[] bArr5 = bArr3[1];
                i3 = iArr3[(bArr5[(bArr5[b3] & 255) ^ b3(i5)] & 255) ^ b3(i4)];
                return i2 ^ i3;
            case 1:
                int[] iArr4 = this.gMDS0;
                byte[][] bArr6 = P;
                i2 = (iArr4[(bArr6[0][b0] & 255) ^ b0(i4)] ^ this.gMDS1[(bArr6[0][b1] & 255) ^ b1(i4)]) ^ this.gMDS2[(bArr6[1][b2] & 255) ^ b2(i4)];
                i3 = this.gMDS3[(bArr6[1][b3] & 255) ^ b3(i4)];
                return i2 ^ i3;
            case 2:
                int[] iArr22 = this.gMDS0;
                byte[][] bArr32 = P;
                byte[] bArr42 = bArr32[0];
                i2 = (iArr22[(bArr42[(bArr42[b0] & 255) ^ b0(i5)] & 255) ^ b0(i4)] ^ this.gMDS1[(bArr32[0][(bArr32[1][b1] & 255) ^ b1(i5)] & 255) ^ b1(i4)]) ^ this.gMDS2[(bArr32[1][(bArr32[0][b2] & 255) ^ b2(i5)] & 255) ^ b2(i4)];
                int[] iArr32 = this.gMDS3;
                byte[] bArr52 = bArr32[1];
                i3 = iArr32[(bArr52[(bArr52[b3] & 255) ^ b3(i5)] & 255) ^ b3(i4)];
                return i2 ^ i3;
            case 3:
                byte[][] bArr22 = P;
                b0 = (bArr22[1][b0] & 255) ^ b0(i6);
                b1 = (bArr22[1][b1] & 255) ^ b1(i6);
                b2 = (bArr22[0][b2] & 255) ^ b2(i6);
                b3 = (bArr22[0][b3] & 255) ^ b3(i6);
                int[] iArr222 = this.gMDS0;
                byte[][] bArr322 = P;
                byte[] bArr422 = bArr322[0];
                i2 = (iArr222[(bArr422[(bArr422[b0] & 255) ^ b0(i5)] & 255) ^ b0(i4)] ^ this.gMDS1[(bArr322[0][(bArr322[1][b1] & 255) ^ b1(i5)] & 255) ^ b1(i4)]) ^ this.gMDS2[(bArr322[1][(bArr322[0][b2] & 255) ^ b2(i5)] & 255) ^ b2(i4)];
                int[] iArr322 = this.gMDS3;
                byte[] bArr522 = bArr322[1];
                i3 = iArr322[(bArr522[(bArr522[b3] & 255) ^ b3(i5)] & 255) ^ b3(i4)];
                return i2 ^ i3;
            default:
                return 0;
        }
    }

    private int Fe32_0(int i) {
        int[] iArr = this.gSBox;
        return iArr[(((i >>> 24) & 255) * 2) + InputDeviceCompat.SOURCE_DPAD] ^ ((iArr[((i & 255) * 2) + 0] ^ iArr[(((i >>> 8) & 255) * 2) + 1]) ^ iArr[(((i >>> 16) & 255) * 2) + 512]);
    }

    private int Fe32_3(int i) {
        int[] iArr = this.gSBox;
        return iArr[(((i >>> 16) & 255) * 2) + InputDeviceCompat.SOURCE_DPAD] ^ ((iArr[(((i >>> 24) & 255) * 2) + 0] ^ iArr[((i & 255) * 2) + 1]) ^ iArr[(((i >>> 8) & 255) * 2) + 512]);
    }

    private int LFSR1(int i) {
        return ((i & 1) != 0 ? 180 : 0) ^ (i >> 1);
    }

    private int LFSR2(int i) {
        return ((i >> 2) ^ ((i & 2) != 0 ? 180 : 0)) ^ ((i & 1) != 0 ? 90 : 0);
    }

    private int Mx_X(int i) {
        return i ^ LFSR2(i);
    }

    private int Mx_Y(int i) {
        return LFSR2(i) ^ (LFSR1(i) ^ i);
    }

    private int RS_MDS_Encode(int i, int i2) {
        for (int i3 = 0; i3 < 4; i3++) {
            i2 = RS_rem(i2);
        }
        int i4 = i ^ i2;
        for (int i5 = 0; i5 < 4; i5++) {
            i4 = RS_rem(i4);
        }
        return i4;
    }

    private int RS_rem(int i) {
        int i2 = (i >>> 24) & 255;
        int i3 = ((i2 << 1) ^ ((i2 & 128) != 0 ? RS_GF_FDBK : 0)) & 255;
        int i4 = ((i2 >>> 1) ^ ((i2 & 1) != 0 ? Opcodes.IF_ACMPNE : 0)) ^ i3;
        return ((((i << 8) ^ (i4 << 24)) ^ (i3 << 16)) ^ (i4 << 8)) ^ i2;
    }

    private int b0(int i) {
        return i & 255;
    }

    private int b1(int i) {
        return (i >>> 8) & 255;
    }

    private int b2(int i) {
        return (i >>> 16) & 255;
    }

    private int b3(int i) {
        return (i >>> 24) & 255;
    }

    private void decryptBlock(byte[] bArr, int i, byte[] bArr2, int i2) {
        int littleEndianToInt = Pack.littleEndianToInt(bArr, i) ^ this.gSubKeys[4];
        int littleEndianToInt2 = Pack.littleEndianToInt(bArr, i + 4) ^ this.gSubKeys[5];
        int littleEndianToInt3 = Pack.littleEndianToInt(bArr, i + 8) ^ this.gSubKeys[6];
        int littleEndianToInt4 = Pack.littleEndianToInt(bArr, i + 12) ^ this.gSubKeys[7];
        int i3 = 39;
        int i4 = 0;
        while (i4 < 16) {
            int Fe32_0 = Fe32_0(littleEndianToInt);
            int Fe32_3 = Fe32_3(littleEndianToInt2);
            int i5 = i3 - 1;
            int i6 = littleEndianToInt4 ^ (((Fe32_3 * 2) + Fe32_0) + this.gSubKeys[i3]);
            int i7 = Fe32_0 + Fe32_3;
            int i8 = i5 - 1;
            littleEndianToInt3 = Integers.rotateLeft(littleEndianToInt3, 1) ^ (i7 + this.gSubKeys[i5]);
            littleEndianToInt4 = Integers.rotateRight(i6, 1);
            int Fe32_02 = Fe32_0(littleEndianToInt3);
            int Fe32_32 = Fe32_3(littleEndianToInt4);
            int i9 = i8 - 1;
            int i10 = littleEndianToInt2 ^ (((Fe32_32 * 2) + Fe32_02) + this.gSubKeys[i8]);
            littleEndianToInt = Integers.rotateLeft(littleEndianToInt, 1) ^ ((Fe32_02 + Fe32_32) + this.gSubKeys[i9]);
            littleEndianToInt2 = Integers.rotateRight(i10, 1);
            i4 += 2;
            i3 = i9 - 1;
        }
        Pack.intToLittleEndian(this.gSubKeys[0] ^ littleEndianToInt3, bArr2, i2);
        Pack.intToLittleEndian(littleEndianToInt4 ^ this.gSubKeys[1], bArr2, i2 + 4);
        Pack.intToLittleEndian(this.gSubKeys[2] ^ littleEndianToInt, bArr2, i2 + 8);
        Pack.intToLittleEndian(this.gSubKeys[3] ^ littleEndianToInt2, bArr2, i2 + 12);
    }

    private void encryptBlock(byte[] bArr, int i, byte[] bArr2, int i2) {
        int i3 = 0;
        int littleEndianToInt = Pack.littleEndianToInt(bArr, i) ^ this.gSubKeys[0];
        int littleEndianToInt2 = Pack.littleEndianToInt(bArr, i + 4) ^ this.gSubKeys[1];
        int littleEndianToInt3 = Pack.littleEndianToInt(bArr, i + 8) ^ this.gSubKeys[2];
        int littleEndianToInt4 = Pack.littleEndianToInt(bArr, i + 12) ^ this.gSubKeys[3];
        int i4 = 8;
        while (i3 < 16) {
            int Fe32_0 = Fe32_0(littleEndianToInt);
            int Fe32_3 = Fe32_3(littleEndianToInt2);
            int i5 = i4 + 1;
            littleEndianToInt3 = Integers.rotateRight(littleEndianToInt3 ^ ((Fe32_0 + Fe32_3) + this.gSubKeys[i4]), 1);
            int i6 = Fe32_0 + (Fe32_3 * 2);
            int i7 = i5 + 1;
            littleEndianToInt4 = Integers.rotateLeft(littleEndianToInt4, 1) ^ (i6 + this.gSubKeys[i5]);
            int Fe32_02 = Fe32_0(littleEndianToInt3);
            int Fe32_32 = Fe32_3(littleEndianToInt4);
            int i8 = i7 + 1;
            littleEndianToInt = Integers.rotateRight(littleEndianToInt ^ ((Fe32_02 + Fe32_32) + this.gSubKeys[i7]), 1);
            littleEndianToInt2 = Integers.rotateLeft(littleEndianToInt2, 1) ^ ((Fe32_02 + (Fe32_32 * 2)) + this.gSubKeys[i8]);
            i3 += 2;
            i4 = i8 + 1;
        }
        Pack.intToLittleEndian(this.gSubKeys[4] ^ littleEndianToInt3, bArr2, i2);
        Pack.intToLittleEndian(littleEndianToInt4 ^ this.gSubKeys[5], bArr2, i2 + 4);
        Pack.intToLittleEndian(this.gSubKeys[6] ^ littleEndianToInt, bArr2, i2 + 8);
        Pack.intToLittleEndian(this.gSubKeys[7] ^ littleEndianToInt2, bArr2, i2 + 12);
    }

    private void setKey(byte[] bArr) {
        int b0;
        int b1;
        int b2;
        int b3;
        int i;
        int i2;
        int i3;
        int i4;
        int[] iArr = new int[4];
        int[] iArr2 = new int[4];
        int[] iArr3 = new int[4];
        this.gSubKeys = new int[40];
        for (int i5 = 0; i5 < this.k64Cnt; i5++) {
            int i6 = i5 * 8;
            iArr[i5] = Pack.littleEndianToInt(bArr, i6);
            int littleEndianToInt = Pack.littleEndianToInt(bArr, i6 + 4);
            iArr2[i5] = littleEndianToInt;
            iArr3[(this.k64Cnt - 1) - i5] = RS_MDS_Encode(iArr[i5], littleEndianToInt);
        }
        for (int i7 = 0; i7 < 20; i7++) {
            int i8 = SK_STEP * i7;
            int F32 = F32(i8, iArr);
            int rotateLeft = Integers.rotateLeft(F32(i8 + 16843009, iArr2), 8);
            int i9 = F32 + rotateLeft;
            int[] iArr4 = this.gSubKeys;
            int i10 = i7 * 2;
            iArr4[i10] = i9;
            int i11 = i9 + rotateLeft;
            iArr4[i10 + 1] = (i11 << 9) | (i11 >>> 23);
        }
        int i12 = iArr3[0];
        int i13 = iArr3[1];
        int i14 = iArr3[2];
        int i15 = iArr3[3];
        this.gSBox = new int[1024];
        for (int i16 = 0; i16 < 256; i16++) {
            switch (this.k64Cnt & 3) {
                case 0:
                    byte[][] bArr2 = P;
                    b0 = (bArr2[1][i16] & 255) ^ b0(i15);
                    b1 = (bArr2[0][i16] & 255) ^ b1(i15);
                    b2 = (bArr2[0][i16] & 255) ^ b2(i15);
                    b3 = (bArr2[1][i16] & 255) ^ b3(i15);
                    byte[][] bArr3 = P;
                    i2 = (bArr3[1][b0] & 255) ^ b0(i14);
                    i3 = (bArr3[1][b1] & 255) ^ b1(i14);
                    i4 = (bArr3[0][b2] & 255) ^ b2(i14);
                    i = (bArr3[0][b3] & 255) ^ b3(i14);
                    int[] iArr5 = this.gSBox;
                    int i17 = i16 * 2;
                    int[] iArr6 = this.gMDS0;
                    byte[][] bArr4 = P;
                    byte[] bArr5 = bArr4[0];
                    iArr5[i17] = iArr6[(bArr5[(bArr5[i2] & 255) ^ b0(i13)] & 255) ^ b0(i12)];
                    this.gSBox[i17 + 1] = this.gMDS1[(bArr4[0][(bArr4[1][i3] & 255) ^ b1(i13)] & 255) ^ b1(i12)];
                    this.gSBox[i17 + 512] = this.gMDS2[(bArr4[1][(bArr4[0][i4] & 255) ^ b2(i13)] & 255) ^ b2(i12)];
                    int[] iArr7 = this.gSBox;
                    int i18 = i17 + InputDeviceCompat.SOURCE_DPAD;
                    int[] iArr8 = this.gMDS3;
                    byte[] bArr6 = bArr4[1];
                    iArr7[i18] = iArr8[(bArr6[(bArr6[i] & 255) ^ b3(i13)] & 255) ^ b3(i12)];
                    break;
                case 1:
                    int[] iArr9 = this.gSBox;
                    int i19 = i16 * 2;
                    int[] iArr10 = this.gMDS0;
                    byte[][] bArr7 = P;
                    iArr9[i19] = iArr10[(bArr7[0][i16] & 255) ^ b0(i12)];
                    this.gSBox[i19 + 1] = this.gMDS1[(bArr7[0][i16] & 255) ^ b1(i12)];
                    this.gSBox[i19 + 512] = this.gMDS2[(bArr7[1][i16] & 255) ^ b2(i12)];
                    this.gSBox[i19 + InputDeviceCompat.SOURCE_DPAD] = this.gMDS3[(bArr7[1][i16] & 255) ^ b3(i12)];
                    break;
                case 2:
                    i = i16;
                    i2 = i;
                    i3 = i2;
                    i4 = i3;
                    int[] iArr52 = this.gSBox;
                    int i172 = i16 * 2;
                    int[] iArr62 = this.gMDS0;
                    byte[][] bArr42 = P;
                    byte[] bArr52 = bArr42[0];
                    iArr52[i172] = iArr62[(bArr52[(bArr52[i2] & 255) ^ b0(i13)] & 255) ^ b0(i12)];
                    this.gSBox[i172 + 1] = this.gMDS1[(bArr42[0][(bArr42[1][i3] & 255) ^ b1(i13)] & 255) ^ b1(i12)];
                    this.gSBox[i172 + 512] = this.gMDS2[(bArr42[1][(bArr42[0][i4] & 255) ^ b2(i13)] & 255) ^ b2(i12)];
                    int[] iArr72 = this.gSBox;
                    int i182 = i172 + InputDeviceCompat.SOURCE_DPAD;
                    int[] iArr82 = this.gMDS3;
                    byte[] bArr62 = bArr42[1];
                    iArr72[i182] = iArr82[(bArr62[(bArr62[i] & 255) ^ b3(i13)] & 255) ^ b3(i12)];
                    break;
                case 3:
                    b3 = i16;
                    b0 = b3;
                    b1 = b0;
                    b2 = b1;
                    byte[][] bArr32 = P;
                    i2 = (bArr32[1][b0] & 255) ^ b0(i14);
                    i3 = (bArr32[1][b1] & 255) ^ b1(i14);
                    i4 = (bArr32[0][b2] & 255) ^ b2(i14);
                    i = (bArr32[0][b3] & 255) ^ b3(i14);
                    int[] iArr522 = this.gSBox;
                    int i1722 = i16 * 2;
                    int[] iArr622 = this.gMDS0;
                    byte[][] bArr422 = P;
                    byte[] bArr522 = bArr422[0];
                    iArr522[i1722] = iArr622[(bArr522[(bArr522[i2] & 255) ^ b0(i13)] & 255) ^ b0(i12)];
                    this.gSBox[i1722 + 1] = this.gMDS1[(bArr422[0][(bArr422[1][i3] & 255) ^ b1(i13)] & 255) ^ b1(i12)];
                    this.gSBox[i1722 + 512] = this.gMDS2[(bArr422[1][(bArr422[0][i4] & 255) ^ b2(i13)] & 255) ^ b2(i12)];
                    int[] iArr722 = this.gSBox;
                    int i1822 = i1722 + InputDeviceCompat.SOURCE_DPAD;
                    int[] iArr822 = this.gMDS3;
                    byte[] bArr622 = bArr422[1];
                    iArr722[i1822] = iArr822[(bArr622[(bArr622[i] & 255) ^ b3(i13)] & 255) ^ b3(i12)];
                    break;
            }
        }
    }

    @Override // org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return "Twofish";
    }

    @Override // org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return 16;
    }

    @Override // org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) {
        if (!(cipherParameters instanceof KeyParameter)) {
            throw new IllegalArgumentException("invalid parameter passed to Twofish init - " + cipherParameters.getClass().getName());
        }
        this.encrypting = z;
        byte[] key = ((KeyParameter) cipherParameters).getKey();
        this.workingKey = key;
        switch (key.length * 8) {
            case 128:
            case 192:
            case 256:
                this.k64Cnt = key.length / 8;
                setKey(key);
                return;
            default:
                throw new IllegalArgumentException("Key length not 128/192/256 bits.");
        }
    }

    @Override // org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i, byte[] bArr2, int i2) {
        if (this.workingKey == null) {
            throw new IllegalStateException("Twofish not initialised");
        }
        if (i + 16 > bArr.length) {
            throw new DataLengthException("input buffer too short");
        }
        if (i2 + 16 > bArr2.length) {
            throw new OutputLengthException("output buffer too short");
        }
        if (this.encrypting) {
            encryptBlock(bArr, i, bArr2, i2);
            return 16;
        }
        decryptBlock(bArr, i, bArr2, i2);
        return 16;
    }

    @Override // org.bouncycastle.crypto.BlockCipher
    public void reset() {
        byte[] bArr = this.workingKey;
        if (bArr != null) {
            setKey(bArr);
        }
    }
}
