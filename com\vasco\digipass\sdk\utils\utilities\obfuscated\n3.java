package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\n3.smali */
public class n3 extends j3 {
    private static final BigInteger d = BigInteger.valueOf(1);
    private static final BigInteger e = BigInteger.valueOf(2);
    private BigInteger c;

    public n3(BigInteger bigInteger, l3 l3Var) {
        super(false, l3Var);
        this.c = a(bigInteger, l3Var);
    }

    private BigInteger a(BigInteger bigInteger, l3 l3Var) {
        if (l3Var == null) {
            return bigInteger;
        }
        BigInteger bigInteger2 = e;
        if (bigInteger2.compareTo(bigInteger) > 0 || l3Var.b().subtract(bigInteger2).compareTo(bigInteger) < 0 || !d.equals(bigInteger.modPow(l3Var.c(), l3Var.b()))) {
            throw new IllegalArgumentException("y value does not appear to be in correct group");
        }
        return bigInteger;
    }
}
