package com.rolster.capacitor.device;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\device\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\device\R$id.smali */
    public static final class id {
        public static int webview = 0x7f0a016f;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\device\R$layout.smali */
    public static final class layout {
        public static int bridge_layout_main = 0x7f0d0025;

        private layout() {
        }
    }

    private R() {
    }
}
