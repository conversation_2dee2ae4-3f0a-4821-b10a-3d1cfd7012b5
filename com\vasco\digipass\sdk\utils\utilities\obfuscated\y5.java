package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y5.smali */
public abstract class y5 {
    public static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        u5.c(iArr, iArr2, iArr3);
        u5.b(iArr, 6, iArr2, 6, iArr3, 12);
        int a = u5.a(iArr3, 6, iArr3, 12);
        int a2 = a + u5.a(iArr3, 18, iArr3, 12, u5.a(iArr3, 0, iArr3, 6, 0) + a);
        int[] a3 = u5.a();
        int[] a4 = u5.a();
        boolean z = u5.a(iArr, 6, iArr, 0, a3, 0) != u5.a(iArr2, 6, iArr2, 0, a4, 0);
        int[] c = u5.c();
        u5.c(a3, a4, c);
        c6.a(24, a2 + (z ? c6.a(12, c, 0, iArr3, 6) : c6.c(12, c, 0, iArr3, 6)), iArr3, 18);
    }

    public static void a(int[] iArr, int[] iArr2) {
        u5.c(iArr, iArr2);
        u5.d(iArr, 6, iArr2, 12);
        int a = u5.a(iArr2, 6, iArr2, 12);
        int a2 = a + u5.a(iArr2, 18, iArr2, 12, u5.a(iArr2, 0, iArr2, 6, 0) + a);
        int[] a3 = u5.a();
        u5.a(iArr, 6, iArr, 0, a3, 0);
        int[] c = u5.c();
        u5.c(a3, c);
        c6.a(24, a2 + c6.c(12, c, 0, iArr2, 6), iArr2, 18);
    }
}
