package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.SharedPreferences;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001:\u0001\u0006B\u0019\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0002¢\u0006\u0004\b\u0012\u0010\u0013J\u001a\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0003\u001a\u00020\u00022\b\u0010\u0004\u001a\u0004\u0018\u00010\u0002H\u0002J\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0003\u001a\u00020\u0002H\u0082\u0002J\u0012\u0010\t\u001a\u00020\b2\b\u0010\u0007\u001a\u0004\u0018\u00010\u0002H\u0002J\u0010\u0010\u000b\u001a\u00020\u00052\b\u0010\n\u001a\u0004\u0018\u00010\u0002J\u0010\u0010\r\u001a\u00020\u00052\b\u0010\f\u001a\u0004\u0018\u00010\u0002R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u00028F¢\u0006\u0006\u001a\u0004\b\u000b\u0010\u000eR\u0013\u0010\f\u001a\u0004\u0018\u00010\u00028F¢\u0006\u0006\u001a\u0004\b\r\u0010\u000e¨\u0006\u0014"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/o;", "", "", "key", "value", "", "a", "encryptionKey", "", "d", "imei", "b", "serial", "c", "()Ljava/lang/String;", "iMEI", "Landroid/content/SharedPreferences;", "sharedPreferences", "<init>", "(Landroid/content/SharedPreferences;Ljava/lang/String;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\o.smali */
public final class o {
    public static final a g;
    private static final String h;
    private final SharedPreferences a;
    private final String b;
    private final String c;
    private final String d;
    private final int e;
    private final byte[] f;

    @Metadata(bv = {}, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\t\u0010\nR\u0014\u0010\u0005\u001a\u00020\u00028BX\u0082\u0004¢\u0006\u0006\u001a\u0004\b\u0003\u0010\u0004R\u0017\u0010\u0006\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0006\u0010\u0007\u001a\u0004\b\b\u0010\u0004¨\u0006\u000b"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/o$a;", "", "", "b", "()Ljava/lang/String;", "generateSharedPreferencesFileName", "FILE_NAME", "Ljava/lang/String;", "a", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\o$a.smali */
    public static final class a {
        private a() {
        }

        public /* synthetic */ a(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        /* JADX INFO: Access modifiers changed from: private */
        public final String b() {
            byte[] bArr = new byte[21];
            int[] iArr = {Opcodes.IF_ICMPLE, Opcodes.MONITOREXIT, Opcodes.INVOKEDYNAMIC, Opcodes.JSR, Opcodes.MULTIANEWARRAY, Opcodes.INVOKEVIRTUAL, Opcodes.MONITOREXIT, Opcodes.GETFIELD, Opcodes.IFEQ, Opcodes.INVOKEDYNAMIC, 203, Opcodes.ARRAYLENGTH, Opcodes.INVOKESTATIC, Opcodes.INVOKEDYNAMIC, Opcodes.DCMPL, Opcodes.ARRAYLENGTH, Opcodes.MONITOREXIT, Opcodes.INVOKEINTERFACE, Opcodes.ARRAYLENGTH, Opcodes.MONITOREXIT, 188};
            for (int i = 0; i < 21; i++) {
                bArr[i] = (byte) ((iArr[i] - 55) - 30);
            }
            return new String(bArr, Charsets.UTF_8);
        }

        public final String a() {
            return o.h;
        }
    }

    static {
        a aVar = new a(null);
        g = aVar;
        h = aVar.b();
    }

    public o(SharedPreferences sharedPreferences, String str) {
        Intrinsics.checkNotNullParameter(sharedPreferences, "sharedPreferences");
        this.a = sharedPreferences;
        this.b = str;
        this.c = "w5oxWeVfJcQvzU4IhZyszQ";
        this.d = "Jj61U8yBQiwrifsukk9qgw";
        this.e = 32;
        this.f = new byte[]{-22, 109, -105, 38, 74, -47, -116, 28, -104, 110, 79, 11, -75, -83, 88, 14};
    }

    /* JADX WARN: Code restructure failed: missing block: B:4:0x000d, code lost:
    
        if (r5 == null) goto L6;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private final byte[] d(java.lang.String r5) throws com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException {
        /*
            r4 = this;
            java.lang.String r0 = "this as java.lang.String).getBytes(charset)"
            if (r5 == 0) goto Lf
            java.nio.charset.Charset r1 = kotlin.text.Charsets.UTF_8
            byte[] r5 = r5.getBytes(r1)
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r5, r0)
            if (r5 != 0) goto L1a
        Lf:
            java.nio.charset.Charset r5 = kotlin.text.Charsets.UTF_8
            java.lang.String r1 = ""
            byte[] r5 = r1.getBytes(r5)
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r5, r0)
        L1a:
            byte[] r0 = r4.f
            int r1 = r4.e
            r2 = 3
            r3 = 1000(0x3e8, float:1.401E-42)
            com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse r5 = com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK.deriveKey(r2, r5, r0, r3, r1)
            int r0 = r5.getReturnCode()
            if (r0 != 0) goto L35
            byte[] r5 = r5.getOutputData()
            java.lang.String r0 = "response.outputData"
            kotlin.jvm.internal.Intrinsics.checkNotNullExpressionValue(r5, r0)
            return r5
        L35:
            com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException r0 = new com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException
            int r5 = r5.getReturnCode()
            r1 = 2
            r2 = 0
            r0.<init>(r5, r2, r1, r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.utils.devicebinding.obfuscated.o.d(java.lang.String):byte[]");
    }

    public final void b(String imei) throws DeviceBindingSDKException {
        a(this.c, imei);
    }

    public final void c(String serial) throws DeviceBindingSDKException {
        a(this.d, serial);
    }

    private final void a(String key, String value) throws DeviceBindingSDKException {
        if (value != null) {
            try {
                if (this.a.getString(key, null) != null) {
                    return;
                }
                byte[] d = d(this.b);
                byte[] bArr = this.f;
                byte[] bytes = value.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
                UtilitiesSDKCryptoResponse encrypt = UtilitiesSDK.encrypt((byte) 3, (byte) 4, d, bArr, bytes);
                if (encrypt.getReturnCode() != 0) {
                    throw new DeviceBindingSDKException(encrypt.getReturnCode(), null, 2, null);
                }
                this.a.edit().putString(key, p.a(encrypt.getOutputData())).apply();
            } catch (Exception e) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.LEGACY_ENCRYPTION_ERROR, e);
            }
        }
    }

    public final String b() throws DeviceBindingSDKException {
        return a(this.c);
    }

    public final String c() throws DeviceBindingSDKException {
        return a(this.d);
    }

    private final String a(String key) throws DeviceBindingSDKException {
        try {
            String string = this.a.getString(key, null);
            if (string == null) {
                return null;
            }
            UtilitiesSDKCryptoResponse decrypt = UtilitiesSDK.decrypt((byte) 3, (byte) 4, d(this.b), this.f, p.a(string));
            if (decrypt.getReturnCode() == 0) {
                byte[] outputData = decrypt.getOutputData();
                Intrinsics.checkNotNullExpressionValue(outputData, "cipheredDataResponse.outputData");
                return new String(outputData, Charsets.UTF_8);
            }
            throw new DeviceBindingSDKException(decrypt.getReturnCode(), null, 2, null);
        } catch (Exception e) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.LEGACY_DECRYPTION_ERROR, e);
        }
    }
}
