package com.google.android.material.shape;

import android.graphics.RectF;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\material\shape\AbsoluteCornerSize.smali */
public final class AbsoluteCornerSize implements CornerSize {
    private final float size;

    public AbsoluteCornerSize(float size) {
        this.size = size;
    }

    @Override // com.google.android.material.shape.CornerSize
    public float getCornerSize(RectF bounds) {
        return this.size;
    }

    public float getCornerSize() {
        return this.size;
    }

    public boolean equals(Object o2) {
        if (this == o2) {
            return true;
        }
        if (!(o2 instanceof AbsoluteCornerSize)) {
            return false;
        }
        AbsoluteCornerSize that = (AbsoluteCornerSize) o2;
        return this.size == that.size;
    }

    public int hashCode() {
        Object[] hashedFields = {Float.valueOf(this.size)};
        return Arrays.hashCode(hashedFields);
    }
}
