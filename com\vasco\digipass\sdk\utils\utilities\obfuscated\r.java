package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;
import java.math.BigInteger;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r.smali */
public class r extends b0 {
    static final o0 C = new a(r.class, 2);
    private final byte[] b;
    private final int x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return r.b(f2Var.h());
        }
    }

    public r(long j) {
        this.b = BigInteger.valueOf(j).toByteArray();
        this.x = 0;
    }

    public static r a(Object obj) {
        if (obj == null || (obj instanceof r)) {
            return (r) obj;
        }
        if (!(obj instanceof byte[])) {
            throw new IllegalArgumentException("illegal object in getInstance: " + obj.getClass().getName());
        }
        try {
            return (r) C.a((byte[]) obj);
        } catch (Exception e) {
            throw new IllegalArgumentException("encoding error in getInstance: " + e.toString());
        }
    }

    static r b(byte[] bArr) {
        return new r(bArr, false);
    }

    static boolean c(byte[] bArr) {
        int length = bArr.length;
        if (length == 0) {
            return true;
        }
        if (length != 1) {
            return bArr[0] == (bArr[1] >> 7) && !r6.b("bc.org.bouncycastle.asn1.allow_unsafe_integer");
        }
        return false;
    }

    static int d(byte[] bArr) {
        int length = bArr.length - 1;
        int i = 0;
        while (i < length) {
            int i2 = i + 1;
            if (bArr[i] != (bArr[i2] >> 7)) {
                break;
            }
            i = i2;
        }
        return i;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    public BigInteger h() {
        return new BigInteger(1, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public BigInteger i() {
        return new BigInteger(this.b);
    }

    public int j() {
        byte[] bArr = this.b;
        int length = bArr.length;
        int i = this.x;
        int i2 = length - i;
        if (i2 > 4 || (i2 == 4 && (bArr[i] & ByteCompanionObject.MIN_VALUE) != 0)) {
            throw new ArithmeticException("ASN.1 Integer out of positive int range");
        }
        return a(bArr, i, 255);
    }

    public int k() {
        byte[] bArr = this.b;
        int length = bArr.length;
        int i = this.x;
        if (length - i <= 4) {
            return a(bArr, i, -1);
        }
        throw new ArithmeticException("ASN.1 Integer out of int range");
    }

    public String toString() {
        return i().toString();
    }

    public r(BigInteger bigInteger) {
        this.b = bigInteger.toByteArray();
        this.x = 0;
    }

    r(byte[] bArr, boolean z) {
        if (!c(bArr)) {
            this.b = z ? Arrays.clone(bArr) : bArr;
            this.x = d(bArr);
            return;
        }
        throw new IllegalArgumentException("malformed integer");
    }

    public boolean a(int i) {
        byte[] bArr = this.b;
        int length = bArr.length;
        int i2 = this.x;
        return length - i2 <= 4 && a(bArr, i2, -1) == i;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 2, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (b0Var instanceof r) {
            return Arrays.areEqual(this.b, ((r) b0Var).b);
        }
        return false;
    }

    static int a(byte[] bArr, int i, int i2) {
        int length = bArr.length;
        int max = Math.max(i, length - 4);
        int i3 = i2 & bArr[max];
        while (true) {
            max++;
            if (max >= length) {
                return i3;
            }
            i3 = (i3 << 8) | (bArr[max] & 255);
        }
    }
}
