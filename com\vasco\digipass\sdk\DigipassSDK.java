package com.vasco.digipass.sdk;

import com.vasco.digipass.sdk.models.SecureChannelMessage;
import com.vasco.digipass.sdk.obfuscated.a;
import com.vasco.digipass.sdk.obfuscated.c;
import com.vasco.digipass.sdk.obfuscated.j;
import com.vasco.digipass.sdk.obfuscated.k;
import com.vasco.digipass.sdk.obfuscated.l;
import com.vasco.digipass.sdk.obfuscated.m;
import com.vasco.digipass.sdk.obfuscated.n;
import com.vasco.digipass.sdk.obfuscated.q;
import com.vasco.digipass.sdk.responses.ActivationResponse;
import com.vasco.digipass.sdk.responses.CryptoResponse;
import com.vasco.digipass.sdk.responses.DigipassPropertiesResponse;
import com.vasco.digipass.sdk.responses.GenerateDerivationCodeResponse;
import com.vasco.digipass.sdk.responses.GenerationResponse;
import com.vasco.digipass.sdk.responses.GenericResponse;
import com.vasco.digipass.sdk.responses.ManageApplicationResponse;
import com.vasco.digipass.sdk.responses.MultiDeviceLicenseActivationResponse;
import com.vasco.digipass.sdk.responses.SecureChannelDecryptionResponse;
import com.vasco.digipass.sdk.responses.SecureChannelGenerateResponse;
import com.vasco.digipass.sdk.responses.SecureChannelParseResponse;
import com.vasco.digipass.sdk.responses.ValidationResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\DigipassSDK.smali */
public final class DigipassSDK {
    public static final String VERSION = "4.34.0";

    private DigipassSDK() {
    }

    private static CryptoResponse a(byte b, byte[] bArr) {
        UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash(b, bArr);
        int returnCode = hash.getReturnCode();
        return new CryptoResponse(returnCode != -4205 ? returnCode != -4201 ? returnCode != 0 ? DigipassSDKReturnCodes.UNKNOWN_ERROR : 0 : DigipassSDKReturnCodes.CRYPTO_MECANISM_INVALID : DigipassSDKReturnCodes.INPUT_DATA_NULL, hash.getOutputData());
    }

    @Deprecated
    public static ActivationResponse activateOffline(String str, String str2, String str3, String str4, CharSequence charSequence, CharSequence charSequence2, byte[] bArr) {
        return activateOfflineWithFingerprint(str, str2, str3, str4, charSequence, charSequence2, null, bArr);
    }

    @Deprecated
    public static ActivationResponse activateOfflineDPPlusWithFingerprint(String str, String str2, String str3, String str4, CharSequence charSequence, CharSequence charSequence2, String str5, byte[] bArr) {
        byte[] a = charSequence2 != null ? c.a(charSequence2) : null;
        ActivationResponse a2 = a.a(str, str2, str3, str4, charSequence, a, charSequence2 != null ? charSequence2.length() : 0, null, false, true, str5, bArr);
        q.g(a);
        return a2;
    }

    @Deprecated
    public static ActivationResponse activateOfflineDPPlusWithKey(String str, String str2, String str3, String str4, CharSequence charSequence, byte[] bArr, byte[] bArr2) {
        return a.a(str, str2, str3, str4, charSequence, null, 0, bArr, true, true, null, bArr2);
    }

    public static ActivationResponse activateOfflineWithFingerprint(String str, String str2, String str3, String str4, CharSequence charSequence, CharSequence charSequence2, String str5, byte[] bArr) {
        byte[] a = charSequence2 != null ? c.a(charSequence2) : null;
        ActivationResponse activateOfflineWithFingerprintAndBytePassword = activateOfflineWithFingerprintAndBytePassword(str, str2, str3, str4, charSequence, a, charSequence2 != null ? charSequence2.length() : 0, str5, bArr);
        q.g(a);
        return activateOfflineWithFingerprintAndBytePassword;
    }

    public static ActivationResponse activateOfflineWithFingerprintAndBytePassword(String str, String str2, String str3, String str4, CharSequence charSequence, byte[] bArr, int i, String str5, byte[] bArr2) {
        return a.a(str, str2, str3, str4, charSequence, bArr, i, null, false, false, str5, bArr2);
    }

    public static ActivationResponse activateOfflineWithKey(String str, String str2, String str3, String str4, CharSequence charSequence, byte[] bArr, byte[] bArr2) {
        return a.a(str, str2, str3, str4, charSequence, null, 0, bArr, true, false, null, bArr2);
    }

    @Deprecated
    public static ActivationResponse activateOnline(String str, String str2, CharSequence charSequence, String str3, CharSequence charSequence2, byte[] bArr) {
        return activateOnlineWithFingerprint(str, str2, charSequence, str3, charSequence2, null, bArr);
    }

    @Deprecated
    public static ActivationResponse activateOnlineDPPlus(String str, String str2, CharSequence charSequence, String str3, CharSequence charSequence2, byte[] bArr) {
        return activateOnlineDPPlusWithFingerprint(str, str2, charSequence, str3, charSequence2, null, bArr);
    }

    @Deprecated
    public static ActivationResponse activateOnlineDPPlusWithFingerprint(String str, String str2, CharSequence charSequence, String str3, CharSequence charSequence2, String str4, byte[] bArr) {
        byte[] a = charSequence2 != null ? c.a(charSequence2) : null;
        ActivationResponse a2 = a.a(str, str2, charSequence, str3, a, charSequence2 != null ? charSequence2.length() : 0, null, false, true, str4, bArr);
        q.g(a);
        return a2;
    }

    @Deprecated
    public static ActivationResponse activateOnlineDPPlusWithKey(String str, String str2, CharSequence charSequence, String str3, byte[] bArr, byte[] bArr2) {
        return a.a(str, str2, charSequence, str3, null, 0, bArr, true, true, null, bArr2);
    }

    public static ActivationResponse activateOnlineWithFingerprint(String str, String str2, CharSequence charSequence, String str3, CharSequence charSequence2, String str4, byte[] bArr) {
        byte[] a = charSequence2 != null ? c.a(charSequence2) : null;
        ActivationResponse activateOnlineWithFingerprintAndBytePassword = activateOnlineWithFingerprintAndBytePassword(str, str2, charSequence, str3, a, charSequence2 != null ? charSequence2.length() : 0, str4, bArr);
        q.g(a);
        return activateOnlineWithFingerprintAndBytePassword;
    }

    public static ActivationResponse activateOnlineWithFingerprintAndBytePassword(String str, String str2, CharSequence charSequence, String str3, byte[] bArr, int i, String str4, byte[] bArr2) {
        return a.a(str, str2, charSequence, str3, bArr, i, null, false, false, str4, bArr2);
    }

    public static ActivationResponse activateOnlineWithKey(String str, String str2, CharSequence charSequence, String str3, byte[] bArr, byte[] bArr2) {
        return a.a(str, str2, charSequence, str3, null, 0, bArr, true, false, null, bArr2);
    }

    public static GenericResponse changeBytePasswordWithFingerprint(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, byte[] bArr4, int i2, String str) {
        return l.a(bArr, bArr2, bArr3, i, bArr4, i2, str);
    }

    public static GenericResponse changeEncryptionKey(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4) {
        return l.a(bArr, bArr2, bArr3, bArr4);
    }

    @Deprecated
    public static GenericResponse changePassword(byte[] bArr, byte[] bArr2, String str, String str2) {
        return changePasswordWithFingerprint(bArr, bArr2, str, str2, null);
    }

    public static GenericResponse changePasswordWithFingerprint(byte[] bArr, byte[] bArr2, CharSequence charSequence, CharSequence charSequence2, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        int length = charSequence != null ? charSequence.length() : 0;
        byte[] a2 = charSequence2 != null ? c.a(charSequence2) : null;
        GenericResponse changeBytePasswordWithFingerprint = changeBytePasswordWithFingerprint(bArr, bArr2, a, length, a2, charSequence2 != null ? charSequence2.length() : 0, str);
        q.g(a);
        q.g(a2);
        return changeBytePasswordWithFingerprint;
    }

    public static long computeClientServerTimeShiftFromServerTime(long j) {
        return j.a(j);
    }

    public static CryptoResponse decrypt(byte b, byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int i;
        UtilitiesSDKCryptoResponse decrypt = UtilitiesSDK.decrypt(b, b2, bArr, bArr2, bArr3);
        int returnCode = decrypt.getReturnCode();
        if (returnCode != 0) {
            switch (returnCode) {
                case UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH /* -4207 */:
                    i = DigipassSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH /* -4206 */:
                    i = DigipassSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.INPUT_DATA_NULL /* -4205 */:
                    i = DigipassSDKReturnCodes.INPUT_DATA_NULL;
                    break;
                case UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH /* -4204 */:
                    i = DigipassSDKReturnCodes.KEY_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.KEY_NULL /* -4203 */:
                    i = DigipassSDKReturnCodes.KEY_NULL;
                    break;
                case UtilitiesSDKReturnCodes.CRYPTO_MODE_INVALID /* -4202 */:
                    i = DigipassSDKReturnCodes.CRYPTO_MODE_INVALID;
                    break;
                case UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID /* -4201 */:
                    i = DigipassSDKReturnCodes.CRYPTO_MECANISM_INVALID;
                    break;
                default:
                    i = DigipassSDKReturnCodes.UNKNOWN_ERROR;
                    break;
            }
        } else {
            i = 0;
        }
        return new CryptoResponse(i, decrypt.getOutputData());
    }

    public static SecureChannelDecryptionResponse decryptSecureChannelMessageBody(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, String str) {
        return m.b(bArr, bArr2, secureChannelMessage, str);
    }

    public static ManageApplicationResponse disableApplication(byte[] bArr, byte[] bArr2, int i) {
        return j.a(bArr, bArr2, i, false);
    }

    public static ManageApplicationResponse enableApplication(byte[] bArr, byte[] bArr2, int i) {
        return j.a(bArr, bArr2, i, true);
    }

    public static CryptoResponse encrypt(byte b, byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int i;
        UtilitiesSDKCryptoResponse encrypt = UtilitiesSDK.encrypt(b, b2, bArr, bArr2, bArr3);
        int returnCode = encrypt.getReturnCode();
        if (returnCode != 0) {
            switch (returnCode) {
                case UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH /* -4207 */:
                    i = DigipassSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH /* -4206 */:
                    i = DigipassSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.INPUT_DATA_NULL /* -4205 */:
                    i = DigipassSDKReturnCodes.INPUT_DATA_NULL;
                    break;
                case UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH /* -4204 */:
                    i = DigipassSDKReturnCodes.KEY_INCORRECT_LENGTH;
                    break;
                case UtilitiesSDKReturnCodes.KEY_NULL /* -4203 */:
                    i = DigipassSDKReturnCodes.KEY_NULL;
                    break;
                case UtilitiesSDKReturnCodes.CRYPTO_MODE_INVALID /* -4202 */:
                    i = DigipassSDKReturnCodes.CRYPTO_MODE_INVALID;
                    break;
                case UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID /* -4201 */:
                    i = DigipassSDKReturnCodes.CRYPTO_MECANISM_INVALID;
                    break;
                default:
                    i = DigipassSDKReturnCodes.UNKNOWN_ERROR;
                    break;
            }
        } else {
            i = 0;
        }
        return new CryptoResponse(i, encrypt.getOutputData());
    }

    public static GenerateDerivationCodeResponse generateDerivationCode(byte[] bArr, byte[] bArr2, CharSequence charSequence, long j, int i, String str, String str2) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerateDerivationCodeResponse generateDerivationCodeWithBytePassword = generateDerivationCodeWithBytePassword(bArr, bArr2, a, charSequence != null ? charSequence.length() : 0, j, i, str, str2);
        q.g(a);
        return generateDerivationCodeWithBytePassword;
    }

    public static GenerateDerivationCodeResponse generateDerivationCodeWithBytePassword(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, long j, int i2, String str, String str2) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, str2, str);
    }

    public static GenerateDerivationCodeResponse generateDerivationCodeWithKey(byte[] bArr, byte[] bArr2, byte[] bArr3, long j, int i, String str, String str2) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, str2, str);
    }

    public static GenerationResponse generateResponseFromChallenge(byte[] bArr, byte[] bArr2, String str, CharSequence charSequence, long j, int i, String str2) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateResponseFromChallengeWithBytePassword = generateResponseFromChallengeWithBytePassword(bArr, bArr2, str, a, charSequence != null ? charSequence.length() : 0, j, i, str2);
        q.g(a);
        return generateResponseFromChallengeWithBytePassword;
    }

    public static GenerationResponse generateResponseFromChallengeES(byte[] bArr, byte[] bArr2, String str, CharSequence charSequence, long j, int i, String str2, String str3) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse a2 = k.a(bArr, bArr2, j, i, a, charSequence != null ? charSequence.length() : 0, null, false, str, true, null, false, str3, str2, true, (byte) 0, false);
        q.g(a);
        return a2;
    }

    public static GenerationResponse generateResponseFromChallengeESWithKey(byte[] bArr, byte[] bArr2, String str, byte[] bArr3, long j, int i, String str2, String str3) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, str, true, null, false, str3, str2, true, (byte) 0, false);
    }

    public static GenerationResponse generateResponseFromChallengeWithBytePassword(byte[] bArr, byte[] bArr2, String str, byte[] bArr3, int i, long j, int i2, String str2) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, str, true, null, false, str2, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateResponseFromChallengeWithKey(byte[] bArr, byte[] bArr2, String str, byte[] bArr3, long j, int i, String str2) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, str, true, null, false, str2, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateResponseFromChallengeWithScore(byte[] bArr, byte[] bArr2, String str, CharSequence charSequence, long j, int i, String str2, byte b) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateResponseFromChallengeWithScoreAndBytePassword = generateResponseFromChallengeWithScoreAndBytePassword(bArr, bArr2, str, a, charSequence != null ? charSequence.length() : 0, j, i, str2, b);
        q.g(a);
        return generateResponseFromChallengeWithScoreAndBytePassword;
    }

    public static GenerationResponse generateResponseFromChallengeWithScoreAndBytePassword(byte[] bArr, byte[] bArr2, String str, byte[] bArr3, int i, long j, int i2, String str2, byte b) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, str, true, null, false, str2, null, false, b, true);
    }

    public static GenerationResponse generateResponseOnly(byte[] bArr, byte[] bArr2, CharSequence charSequence, long j, int i, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateResponseOnlyWithBytePassword = generateResponseOnlyWithBytePassword(bArr, bArr2, a, charSequence != null ? charSequence.length() : 0, j, i, str);
        q.g(a);
        return generateResponseOnlyWithBytePassword;
    }

    public static GenerationResponse generateResponseOnlyES(byte[] bArr, byte[] bArr2, CharSequence charSequence, long j, int i, String str, String str2) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse a2 = k.a(bArr, bArr2, j, i, a, charSequence != null ? charSequence.length() : 0, null, false, null, false, null, false, str2, str, true, (byte) 0, false);
        q.g(a);
        return a2;
    }

    public static GenerationResponse generateResponseOnlyESWithKey(byte[] bArr, byte[] bArr2, byte[] bArr3, long j, int i, String str, String str2) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, null, false, null, false, str2, str, true, (byte) 0, false);
    }

    public static GenerationResponse generateResponseOnlyWithBytePassword(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, long j, int i2, String str) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, null, false, null, false, str, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateResponseOnlyWithKey(byte[] bArr, byte[] bArr2, byte[] bArr3, long j, int i, String str) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, null, false, null, false, str, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateResponseOnlyWithScore(byte[] bArr, byte[] bArr2, CharSequence charSequence, long j, int i, String str, byte b) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateResponseOnlyWithScoreAndBytePassword = generateResponseOnlyWithScoreAndBytePassword(bArr, bArr2, a, charSequence != null ? charSequence.length() : 0, j, i, str, b);
        q.g(a);
        return generateResponseOnlyWithScoreAndBytePassword;
    }

    public static GenerationResponse generateResponseOnlyWithScoreAndBytePassword(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, long j, int i2, String str, byte b) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, null, false, null, false, str, null, false, b, true);
    }

    public static SecureChannelGenerateResponse generateSecureChannelInformationMessage(byte[] bArr, byte[] bArr2, String str, byte b, String str2) {
        return m.a(bArr, bArr2, str, b, str2);
    }

    public static GenerationResponse generateSignature(byte[] bArr, byte[] bArr2, String[] strArr, CharSequence charSequence, long j, int i, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateSignatureWithBytePassword = generateSignatureWithBytePassword(bArr, bArr2, strArr, a, charSequence != null ? charSequence.length() : 0, j, i, str);
        q.g(a);
        return generateSignatureWithBytePassword;
    }

    public static GenerationResponse generateSignatureES(byte[] bArr, byte[] bArr2, String[] strArr, CharSequence charSequence, long j, int i, String str, String str2) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse a2 = k.a(bArr, bArr2, j, i, a, charSequence != null ? charSequence.length() : 0, null, false, null, false, strArr, true, str2, str, true, (byte) 0, false);
        q.g(a);
        return a2;
    }

    public static GenerationResponse generateSignatureESWithKey(byte[] bArr, byte[] bArr2, String[] strArr, byte[] bArr3, long j, int i, String str, String str2) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, null, false, strArr, true, str2, str, true, (byte) 0, false);
    }

    public static GenerationResponse generateSignatureFromSecureChannelMessage(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, CharSequence charSequence, long j, int i, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateSignatureFromSecureChannelMessageWithBytePassword = generateSignatureFromSecureChannelMessageWithBytePassword(bArr, bArr2, secureChannelMessage, a, charSequence != null ? charSequence.length() : 0, j, i, str);
        q.g(a);
        return generateSignatureFromSecureChannelMessageWithBytePassword;
    }

    public static GenerationResponse generateSignatureFromSecureChannelMessageWithBytePassword(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, int i, long j, int i2, String str) {
        return m.a(bArr, bArr2, secureChannelMessage, bArr3, i, null, false, j, i2, str, (byte) 0, false);
    }

    public static GenerationResponse generateSignatureFromSecureChannelMessageWithKey(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, long j, int i, String str) {
        return m.a(bArr, bArr2, secureChannelMessage, null, 0, bArr3, true, j, i, str, (byte) 0, false);
    }

    public static GenerationResponse generateSignatureFromSecureChannelMessageWithScore(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, CharSequence charSequence, long j, int i, String str, byte b) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateSignatureFromSecureChannelMessageWithScoreAndBytePassword = generateSignatureFromSecureChannelMessageWithScoreAndBytePassword(bArr, bArr2, secureChannelMessage, a, charSequence != null ? charSequence.length() : 0, j, i, str, b);
        q.g(a);
        return generateSignatureFromSecureChannelMessageWithScoreAndBytePassword;
    }

    public static GenerationResponse generateSignatureFromSecureChannelMessageWithScoreAndBytePassword(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, int i, long j, int i2, String str, byte b) {
        return m.a(bArr, bArr2, secureChannelMessage, bArr3, i, null, false, j, i2, str, b, true);
    }

    public static GenerationResponse generateSignatureWithBytePassword(byte[] bArr, byte[] bArr2, String[] strArr, byte[] bArr3, int i, long j, int i2, String str) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, null, false, strArr, true, str, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateSignatureWithKey(byte[] bArr, byte[] bArr2, String[] strArr, byte[] bArr3, long j, int i, String str) {
        return k.a(bArr, bArr2, j, i, null, 0, bArr3, true, null, false, strArr, true, str, null, false, (byte) 0, false);
    }

    public static GenerationResponse generateSignatureWithScore(byte[] bArr, byte[] bArr2, String[] strArr, CharSequence charSequence, long j, int i, String str, byte b) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        GenerationResponse generateSignatureWithScoreAndBytePassword = generateSignatureWithScoreAndBytePassword(bArr, bArr2, strArr, a, charSequence != null ? charSequence.length() : 0, j, i, str, b);
        q.g(a);
        return generateSignatureWithScoreAndBytePassword;
    }

    public static GenerationResponse generateSignatureWithScoreAndBytePassword(byte[] bArr, byte[] bArr2, String[] strArr, byte[] bArr3, int i, long j, int i2, String str, byte b) {
        return k.a(bArr, bArr2, j, i2, bArr3, i, null, false, null, false, strArr, true, str, null, false, b, true);
    }

    public static DigipassPropertiesResponse getDigipassProperties(byte[] bArr, byte[] bArr2) {
        return j.a(bArr, bArr2, (String) null);
    }

    public static DigipassPropertiesResponse getDigipassPropertiesFromXFAD(String str) {
        return j.a((byte[]) null, (byte[]) null, str);
    }

    public static String getMessageForReturnCode(int i) {
        return j.a(i);
    }

    @Deprecated
    public static CryptoResponse hash(byte b, byte[] bArr) {
        return a(b, bArr);
    }

    public static boolean isBytePasswordWeak(byte[] bArr, int i) {
        return q.b(bArr, i);
    }

    public static boolean isPasswordWeak(CharSequence charSequence) {
        return UtilitiesSDK.isPasswordWeak(charSequence);
    }

    public static ActivationResponse multiDeviceActivateInstance(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, CharSequence charSequence, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        ActivationResponse multiDeviceActivateInstanceWithBytePassword = multiDeviceActivateInstanceWithBytePassword(bArr, bArr2, secureChannelMessage, a, charSequence != null ? charSequence.length() : 0, str);
        q.g(a);
        return multiDeviceActivateInstanceWithBytePassword;
    }

    public static ActivationResponse multiDeviceActivateInstanceWithBytePassword(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, int i, String str) {
        return m.a(bArr, bArr2, secureChannelMessage, bArr3, i, null, false, str);
    }

    public static ActivationResponse multiDeviceActivateInstanceWithKey(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, String str) {
        return m.a(bArr, bArr2, secureChannelMessage, null, 0, bArr3, true, str);
    }

    public static MultiDeviceLicenseActivationResponse multiDeviceActivateLicense(SecureChannelMessage secureChannelMessage, String str, String str2, byte b, long j) {
        return m.a(secureChannelMessage, str, str2, b, j);
    }

    public static SecureChannelParseResponse parseSecureChannelMessage(String str) {
        return n.a(str);
    }

    public static CryptoResponse sha256Hash(byte[] bArr) {
        return a((byte) 3, bArr);
    }

    public static ValidationResponse validateBytePasswordWithFingerprint(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, String str) {
        return l.a(bArr, bArr2, bArr3, i, str);
    }

    public static ValidationResponse validatePassword(byte[] bArr, byte[] bArr2, CharSequence charSequence) {
        return validatePasswordWithFingerprint(bArr, bArr2, charSequence, null);
    }

    public static ValidationResponse validatePasswordWithFingerprint(byte[] bArr, byte[] bArr2, CharSequence charSequence, String str) {
        byte[] a = charSequence != null ? c.a(charSequence) : null;
        ValidationResponse validateBytePasswordWithFingerprint = validateBytePasswordWithFingerprint(bArr, bArr2, a, charSequence != null ? charSequence.length() : 0, str);
        q.g(a);
        return validateBytePasswordWithFingerprint;
    }
}
