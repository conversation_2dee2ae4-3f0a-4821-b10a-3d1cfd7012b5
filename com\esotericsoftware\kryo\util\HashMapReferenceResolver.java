package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.ReferenceResolver;
import java.util.ArrayList;
import java.util.IdentityHashMap;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\HashMapReferenceResolver.smali */
public class HashMapReferenceResolver implements ReferenceResolver {
    protected Kryo kryo;
    protected final IdentityHashMap<Object, Integer> writtenObjects = new IdentityHashMap<>();
    protected final ArrayList readObjects = new ArrayList();

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setKryo(Kryo kryo) {
        this.kryo = kryo;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int addWrittenObject(Object object) {
        int id = this.writtenObjects.size();
        this.writtenObjects.put(object, Integer.valueOf(id));
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int getWrittenId(Object object) {
        Integer id = this.writtenObjects.get(object);
        if (id == null) {
            return -1;
        }
        return id.intValue();
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int nextReadId(Class type) {
        int id = this.readObjects.size();
        this.readObjects.add(null);
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setReadObject(int id, Object object) {
        this.readObjects.set(id, object);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public Object getReadObject(Class type, int id) {
        return this.readObjects.get(id);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void reset() {
        this.readObjects.clear();
        this.writtenObjects.clear();
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public boolean useReferences(Class type) {
        return (Util.isWrapperClass(type) || Util.isEnum(type)) ? false : true;
    }
}
