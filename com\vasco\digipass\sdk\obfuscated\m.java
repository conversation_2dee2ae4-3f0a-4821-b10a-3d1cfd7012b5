package com.vasco.digipass.sdk.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.models.SecureChannelMessage;
import com.vasco.digipass.sdk.obfuscated.n;
import com.vasco.digipass.sdk.responses.GenerationResponse;
import com.vasco.digipass.sdk.responses.SecureChannelDecryptionResponse;
import com.vasco.digipass.sdk.responses.SecureChannelGenerateResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.wbc.Serializer;
import com.vasco.digipass.sdk.utils.utilities.wbc.WBCTable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\m.smali */
public final class m implements DigipassSDKConstants {
    private static final byte[] a = {71, -37, -99, 29};
    private static final byte[] b = {14, -1, -100, 28};
    private static final byte[] c = {-83, -5, 120, 116};
    private static final byte[] d = {4, -35, -62, 73};

    private m() {
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x0024, code lost:
    
        if (r4 == null) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x002e, code lost:
    
        if (a(r17.getBytes(), r4) == false) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0038, code lost:
    
        throw new com.vasco.digipass.sdk.obfuscated.h(com.vasco.digipass.sdk.DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_STATIC_VECTOR_INCONSISTENT);
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static com.vasco.digipass.sdk.responses.MultiDeviceLicenseActivationResponse a(com.vasco.digipass.sdk.models.SecureChannelMessage r16, java.lang.String r17, java.lang.String r18, byte r19, long r20) {
        /*
            Method dump skipped, instructions count: 309
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.obfuscated.m.a(com.vasco.digipass.sdk.models.SecureChannelMessage, java.lang.String, java.lang.String, byte, long):com.vasco.digipass.sdk.responses.MultiDeviceLicenseActivationResponse");
    }

    public static SecureChannelDecryptionResponse b(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, String str) {
        if (secureChannelMessage != null) {
            try {
                byte b2 = secureChannelMessage.messageType;
                if (b2 == 0 || b2 == 1) {
                    throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_TYPE);
                }
            } catch (h e) {
                return new SecureChannelDecryptionResponse(e.a());
            } catch (Exception e2) {
                return new SecureChannelDecryptionResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
            }
        }
        e a2 = a(bArr, bArr2, secureChannelMessage, str, true);
        if (!a2.e()) {
            throw new h(DigipassSDKReturnCodes.STATUS_INVALID);
        }
        SecureChannelDecryptionResponse secureChannelDecryptionResponse = new SecureChannelDecryptionResponse(0, secureChannelMessage.body);
        a2.f();
        return secureChannelDecryptionResponse;
    }

    static String c() {
        byte[] bArr = new byte[32];
        int[] iArr = {Opcodes.FMUL, 97, Opcodes.FNEG, 97, Opcodes.ISHL, 46, 109, Opcodes.LMUL, 99, 114, Opcodes.DDIV, Opcodes.LSUB, 100, Opcodes.LMUL, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 46, 109, Opcodes.LMUL, 100, 108, Opcodes.LSUB, Opcodes.INEG, 46, 77, 73, 68, 108, Opcodes.LSUB, Opcodes.INEG};
        for (int i = 0; i < 32; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    private static byte[] b(byte[] bArr) {
        WBCTable deserializeResource;
        synchronized (m.class) {
            deserializeResource = new Serializer().deserializeResource("/encrypt_platform_key_tables.dat");
        }
        return UtilitiesSDK.encryptAESBlock(deserializeResource, bArr).getOutputData();
    }

    static String b() {
        byte[] bArr = new byte[16];
        int[] iArr = {48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48, 48};
        for (int i = 0; i < 16; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    /* JADX WARN: Removed duplicated region for block: B:29:0x0121  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static com.vasco.digipass.sdk.responses.ActivationResponse a(byte[] r15, byte[] r16, com.vasco.digipass.sdk.models.SecureChannelMessage r17, byte[] r18, int r19, byte[] r20, boolean r21, java.lang.String r22) {
        /*
            Method dump skipped, instructions count: 293
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.obfuscated.m.a(byte[], byte[], com.vasco.digipass.sdk.models.SecureChannelMessage, byte[], int, byte[], boolean, java.lang.String):com.vasco.digipass.sdk.responses.ActivationResponse");
    }

    public static GenerationResponse a(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, byte[] bArr3, int i, byte[] bArr4, boolean z, long j, int i2, String str, byte b2, boolean z2) {
        try {
            l.a(bArr3, i);
            a(bArr, bArr2, secureChannelMessage, str);
            byte b3 = secureChannelMessage.messageType;
            if (b3 != 3 && b3 != 1) {
                throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_TYPE);
            }
            if (secureChannelMessage.protectionType != 0) {
                UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, q.a(secureChannelMessage.rawData));
                if (hash.getReturnCode() == 0) {
                    byte[] outputData = hash.getOutputData();
                    String[] strArr = new String[4];
                    byte[] bArr5 = new byte[8];
                    for (int i3 = 0; i3 < 4; i3++) {
                        System.arraycopy(outputData, i3 * 8, bArr5, 0, 8);
                        strArr[i3] = q.a(bArr5);
                    }
                    return k.a(bArr, bArr2, j, i2, bArr3, i, bArr4, z, null, false, strArr, true, str, null, false, b2, z2);
                }
                throw new Exception("" + hash.getReturnCode());
            }
            throw new h(DigipassSDKReturnCodes.PROTECTION_TYPE_NOT_SUPPORTED);
        } catch (h e) {
            return new GenerationResponse(e.a());
        } catch (Exception e2) {
            return new GenerationResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    public static SecureChannelGenerateResponse a(byte[] bArr, byte[] bArr2, String str, byte b2, String str2) {
        try {
            if (!q.d(str2)) {
                if (b2 != 0 && b2 != 1) {
                    throw new h(DigipassSDKReturnCodes.PROTECTION_TYPE_NOT_SUPPORTED);
                }
                if (!q.d(str)) {
                    if (q.c(str)) {
                        if (str.length() % 2 == 0 && str.length() <= 1024) {
                            e b3 = p.b(bArr, bArr2);
                            if (b3.d.v) {
                                String a2 = q.a(UtilitiesSDK.generateRandomByteArray(8).getOutputData());
                                SecureChannelMessage secureChannelMessage = new SecureChannelMessage();
                                secureChannelMessage.protocolVersion = (byte) 0;
                                secureChannelMessage.messageType = DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE;
                                secureChannelMessage.serialNumber = b3.c.b;
                                secureChannelMessage.nonce = a2;
                                secureChannelMessage.body = str;
                                secureChannelMessage.encrypted = false;
                                secureChannelMessage.authenticationTag = null;
                                if (b2 == 0) {
                                    secureChannelMessage.protectionType = (byte) 0;
                                } else {
                                    secureChannelMessage.protectionType = (byte) 1;
                                    secureChannelMessage.authenticationTag = b();
                                    byte[] b4 = o.b(b3, str2);
                                    byte[] a3 = o.a(b4, f.e);
                                    secureChannelMessage.body = o.a(true, secureChannelMessage, a3);
                                    secureChannelMessage.encrypted = true;
                                    q.g(a3);
                                    secureChannelMessage.rawData = o.a(secureChannelMessage);
                                    byte[] a4 = o.a(b4, f.f);
                                    q.g(b4);
                                    secureChannelMessage.authenticationTag = o.a(secureChannelMessage, a4);
                                    q.g(a4);
                                }
                                secureChannelMessage.rawData = o.a(secureChannelMessage);
                                return new SecureChannelGenerateResponse(0, secureChannelMessage);
                            }
                            throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_DISABLED);
                        }
                        throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_LENGTH);
                    }
                    throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_FORMAT);
                }
                throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_NULL_OR_EMPTY);
            }
            throw new h(DigipassSDKReturnCodes.PLATFORM_FINGERPRINT_NOT_DEFINED);
        } catch (h e) {
            return new SecureChannelGenerateResponse(e.a());
        } catch (Exception e2) {
            return new SecureChannelGenerateResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static void a(e eVar, n.b bVar) {
        byte[] bArr = new byte[3];
        System.arraycopy(UtilitiesSDK.encrypt((byte) 3, (byte) 1, eVar.d.p, null, new byte[16]).getOutputData(), 0, bArr, 0, 3);
        if (!Arrays.areEqual(bArr, bVar.b)) {
            throw new h(DigipassSDKReturnCodes.PLATFORM_ACTIVATION_KEY_INVALID);
        }
    }

    private static byte a(byte b2) {
        try {
            try {
                Class.forName(a());
                return b2 == 2 ? (byte) 9 : (byte) 7;
            } catch (Exception e) {
                return (byte) 1;
            }
        } catch (ClassNotFoundException e2) {
            try {
                Class.forName(c());
                return (byte) 15;
            } catch (ClassNotFoundException e3) {
                return (byte) 1;
            }
        }
    }

    private static e a(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, String str, boolean z) {
        byte[] c2;
        byte[] bArr3;
        byte[] bArr4;
        e a2 = a(bArr, bArr2, secureChannelMessage, str);
        if (z && !a2.d.v) {
            throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_DISABLED);
        }
        if (z) {
            c2 = o.b(a2, str);
            bArr3 = f.d;
            bArr4 = f.c;
        } else {
            c2 = o.c(a2, str);
            bArr3 = f.b;
            bArr4 = f.a;
        }
        byte[] a3 = o.a(c2, bArr3);
        o.b(secureChannelMessage, a3);
        q.g(a3);
        byte[] a4 = o.a(c2, bArr4);
        q.g(c2);
        secureChannelMessage.body = o.a(false, secureChannelMessage, a4);
        secureChannelMessage.encrypted = false;
        q.g(a4);
        return a2;
    }

    private static e a(byte[] bArr, byte[] bArr2, SecureChannelMessage secureChannelMessage, String str) {
        if (secureChannelMessage != null) {
            if (!q.d(str)) {
                e b2 = p.b(bArr, bArr2);
                i iVar = b2.c;
                if (iVar.t) {
                    if (iVar.b.equals(secureChannelMessage.serialNumber)) {
                        return b2;
                    }
                    throw new h(DigipassSDKReturnCodes.LICENSE_INCORRECT);
                }
                throw new h(DigipassSDKReturnCodes.MULTI_DEVICE_ACTIVATION_DISABLED);
            }
            throw new h(DigipassSDKReturnCodes.PLATFORM_FINGERPRINT_NOT_DEFINED);
        }
        throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL);
    }

    private static byte[] a(byte[] bArr) {
        byte[] bArr2 = new byte[16];
        System.arraycopy(a, 0, bArr2, 0, 4);
        System.arraycopy(b, 0, bArr2, 4, 4);
        System.arraycopy(c, 0, bArr2, 8, 4);
        System.arraycopy(d, 0, bArr2, 12, 4);
        byte[] a2 = o.a(true, bArr2, bArr, (byte) 1);
        q.g(bArr2);
        return a2;
    }

    static boolean a(byte[] bArr, byte[] bArr2) {
        if (bArr.length != bArr2.length) {
            return false;
        }
        for (int i = 0; i < bArr.length; i++) {
            if (bArr[i] != bArr2[i] && (i < 6 || i > 8)) {
                return false;
            }
        }
        return true;
    }

    static String a() {
        byte[] bArr = new byte[20];
        int[] iArr = {97, Opcodes.FDIV, 100, 114, Opcodes.DDIV, Opcodes.LMUL, 100, 46, 97, Opcodes.IREM, Opcodes.IREM, 46, 65, 99, Opcodes.INEG, Opcodes.LMUL, Opcodes.FNEG, Opcodes.LMUL, Opcodes.INEG, Opcodes.LSHL};
        for (int i = 0; i < 20; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }
}
