package o.fk;

import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\b.smali */
public final class b {
    private static int f = 0;
    private static int g = 1;
    private boolean b;
    private boolean c;
    private boolean d;
    private boolean e;
    private final List<o.el.d> a = new ArrayList();
    private final List<a> j = new ArrayList();

    /* renamed from: o.fk.b$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\b$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] b;
        private static int c;
        private static int e;

        static {
            c = 0;
            e = 1;
            int[] iArr = new int[o.el.b.values().length];
            b = iArr;
            try {
                iArr[o.el.b.e.ordinal()] = 1;
                int i = c;
                int i2 = (i ^ 87) + ((i & 87) << 1);
                e = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[o.el.b.d.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                b[o.el.b.c.ordinal()] = 3;
                int i4 = e;
                int i5 = (i4 ^ 67) + ((i4 & 67) << 1);
                c = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                b[o.el.b.b.ordinal()] = 4;
                int i7 = e;
                int i8 = (i7 ^ 89) + ((i7 & 89) << 1);
                c = i8 % 128;
                if (i8 % 2 == 0) {
                }
            } catch (NoSuchFieldError e5) {
            }
            try {
                b[o.el.b.a.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                b[o.el.b.g.ordinal()] = 6;
                int i9 = e;
                int i10 = ((i9 | 11) << 1) - (i9 ^ 11);
                c = i10 % 128;
                switch (i10 % 2 != 0) {
                    case false:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            } catch (NoSuchFieldError e7) {
            }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:19:0x003c. Please report as an issue. */
    public final void d(o.el.b bVar, o.el.d dVar) {
        int i = g + 27;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                switch (AnonymousClass5.b[bVar.ordinal()]) {
                    case 1:
                        this.a.add(dVar);
                        int i2 = f;
                        int i3 = (i2 & Opcodes.LMUL) + (i2 | Opcodes.LMUL);
                        g = i3 % 128;
                        int i4 = i3 % 2;
                        return;
                    case 2:
                        this.c = true;
                        int i5 = g;
                        int i6 = ((i5 | 73) << 1) - (i5 ^ 73);
                        f = i6 % 128;
                        int i7 = i6 % 2;
                        return;
                    case 3:
                        int i8 = (g + Opcodes.ISHL) - 1;
                        f = i8 % 128;
                        int i9 = i8 % 2;
                        return;
                    case 4:
                        this.e = true;
                        return;
                    case 5:
                        this.e = true;
                        int i10 = (f + 20) - 1;
                        g = i10 % 128;
                        switch (i10 % 2 == 0 ? (char) 25 : ':') {
                        }
                }
                int i11 = (f + 94) - 1;
                g = i11 % 128;
                int i12 = i11 % 2;
                return;
            default:
                int i13 = AnonymousClass5.b[bVar.ordinal()];
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void b(a aVar) {
        int i = g;
        int i2 = (i ^ 79) + ((i & 79) << 1);
        f = i2 % 128;
        int i3 = i2 % 2;
        this.j.add(aVar);
        int i4 = (f + 98) - 1;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    public final List<a> d() {
        int i = f;
        int i2 = (i + 60) - 1;
        g = i2 % 128;
        int i3 = i2 % 2;
        List<a> list = this.j;
        int i4 = i + 65;
        g = i4 % 128;
        int i5 = i4 % 2;
        return list;
    }

    public final void e() {
        int i = f;
        int i2 = ((i | 57) << 1) - (i ^ 57);
        g = i2 % 128;
        int i3 = i2 % 2;
        this.d = true;
        int i4 = i + 1;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    public final void b() {
        int i = g + Opcodes.DSUB;
        f = i % 128;
        switch (i % 2 != 0) {
        }
        this.b = true;
    }

    public final boolean a() {
        int i = g;
        int i2 = i + 93;
        f = i2 % 128;
        int i3 = i2 % 2;
        switch (!this.e) {
            case true:
                int i4 = (i ^ 47) + ((i & 47) << 1);
                f = i4 % 128;
                int i5 = i4 % 2;
                switch (this.c) {
                    case false:
                        int i6 = ((i | 27) << 1) - (i ^ 27);
                        int i7 = i6 % 128;
                        f = i7;
                        int i8 = i6 % 2;
                        switch (!this.d ? 61 : 69) {
                            case true:
                                break;
                            default:
                                int i9 = ((i7 | 89) << 1) - (i7 ^ 89);
                                g = i9 % 128;
                                int i10 = i9 % 2;
                                switch (this.b ? 'M' : '=') {
                                    case 'M':
                                        break;
                                    default:
                                        return false;
                                }
                        }
                }
        }
        int i11 = f;
        int i12 = (i11 ^ 29) + ((i11 & 29) << 1);
        g = i12 % 128;
        switch (i12 % 2 != 0 ? 'V' : 'E') {
            case Opcodes.SASTORE /* 86 */:
                return true;
            default:
                throw null;
        }
    }

    public final boolean c() {
        int i = g;
        int i2 = ((i | 5) << 1) - (i ^ 5);
        f = i2 % 128;
        int i3 = i2 % 2;
        switch (this.e) {
            case false:
                int i4 = (i + 18) - 1;
                f = i4 % 128;
                int i5 = i4 % 2;
                switch (this.c ? 'Z' : (char) 0) {
                    case 0:
                        int i6 = (i ^ 61) + ((i & 61) << 1);
                        f = i6 % 128;
                        switch (i6 % 2 == 0) {
                            case false:
                                throw null;
                            default:
                                return false;
                        }
                }
        }
        int i7 = f;
        int i8 = (i7 ^ 89) + ((i7 & 89) << 1);
        g = i8 % 128;
        int i9 = i8 % 2;
        return true;
    }

    public final boolean g() {
        int i = g;
        int i2 = (i ^ 61) + ((i & 61) << 1);
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 24 : (char) 4) {
            case 24:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final boolean f() {
        int i = (f + 82) - 1;
        g = i % 128;
        switch (i % 2 == 0 ? 'O' : '9') {
            case Opcodes.IASTORE /* 79 */:
                throw null;
            default:
                return this.e;
        }
    }

    public final List<o.el.d> i() {
        int i = g;
        int i2 = ((i | Opcodes.LSUB) << 1) - (i ^ Opcodes.LSUB);
        f = i2 % 128;
        int i3 = i2 % 2;
        List<o.el.d> list = this.a;
        int i4 = (i ^ 109) + ((i & 109) << 1);
        f = i4 % 128;
        int i5 = i4 % 2;
        return list;
    }
}
