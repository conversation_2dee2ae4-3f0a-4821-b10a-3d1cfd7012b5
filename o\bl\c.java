package o.bl;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.j;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\c.smali */
public final class c implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static boolean c;
    private static char[] d;
    private static boolean e;
    private static int f;
    private static char g;
    private static char h;
    private static char i;
    private static char j;
    private static int m;
    private final o.eg.b b;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        m = 1;
        j();
        TextUtils.lastIndexOf("", '0', 0);
        Color.alpha(0);
        Color.green(0);
        TextUtils.getTrimmedLength("");
        SystemClock.uptimeMillis();
        TextUtils.lastIndexOf("", '0', 0, 0);
        TextUtils.lastIndexOf("", '0', 0);
        Color.green(0);
        PointF.length(0.0f, 0.0f);
        Color.blue(0);
        ViewConfiguration.getMinimumFlingVelocity();
        ViewConfiguration.getScrollFriction();
        int i2 = m + 1;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                break;
            default:
                int i3 = 46 / 0;
                break;
        }
    }

    static void init$0() {
        $$a = new byte[]{108, 119, -51, 110};
        $$b = 83;
    }

    static void j() {
        d = new char[]{61929, 61940, 61950, 61933, 61942, 61947, 61946, 61931, 61951, 61934, 61943, 61935, 61932, 61937, 61939, 61949, 61944, 61876, 61936, 61699, 61897, 61900, 61903, 61914, 61948, 61696, 61866, 61877, 61698, 61938, 61941, 61928, 61905, 61911, 61697, 61874, 61873, 61906};
        c = true;
        e = true;
        a = 782102922;
        j = (char) 29206;
        g = (char) 19277;
        h = (char) 3565;
        i = (char) 24568;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r5, short r6, short r7, java.lang.Object[] r8) {
        /*
            byte[] r0 = o.bl.c.$$a
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r5 = 121 - r5
            byte[] r1 = new byte[r6]
            r2 = -1
            int r6 = r6 + r2
            if (r0 != 0) goto L19
            r5 = r6
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L37
        L19:
            r4 = r7
            r7 = r5
            r5 = r4
        L1c:
            int r2 = r2 + 1
            byte r3 = (byte) r7
            r1[r2] = r3
            if (r2 != r6) goto L2c
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2c:
            r3 = r0[r5]
            r4 = r7
            r7 = r5
            r5 = r6
            r6 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r4
        L37:
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            r4 = r6
            r6 = r5
            r5 = r7
            r7 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.c.n(byte, short, short, java.lang.Object[]):void");
    }

    @Override // o.bl.a
    public final void c(Context context) {
        int i2 = f + 39;
        m = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Not initialized variable reg: 8, insn: 0x0102: MOVE (r6 I:??[OBJECT, ARRAY]) = (r8 I:??[OBJECT, ARRAY]), block:B:34:0x0102 */
    /* JADX WARN: Removed duplicated region for block: B:24:0x00fa A[Catch: IOException -> 0x00c4, TRY_ENTER, TRY_LEAVE, TryCatch #7 {IOException -> 0x00c4, blocks: (B:31:0x00c0, B:24:0x00fa), top: B:2:0x000e }] */
    /* JADX WARN: Removed duplicated region for block: B:37:0x0105 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public c(android.content.Context r13) {
        /*
            Method dump skipped, instructions count: 267
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.c.<init>(android.content.Context):void");
    }

    @Override // o.bl.a
    public final String e() {
        int i2 = f;
        int i3 = i2 + 65;
        m = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                o.eg.b bVar = this.b;
                switch (bVar == null ? '!' : 'N') {
                    case 'N':
                        try {
                            Object[] objArr = new Object[1];
                            l("\uf23c慻舼霻ᮅ௵㷷㫩싱\ue049ἣ\uf643빇롪", 13 - TextUtils.getOffsetBefore("", 0), objArr);
                            return bVar.r(((String) objArr[0]).intern());
                        } catch (o.eg.d e2) {
                            g.c();
                            Object[] objArr2 = new Object[1];
                            k(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr2);
                            String intern = ((String) objArr2[0]).intern();
                            Object[] objArr3 = new Object[1];
                            l("\uf6a6㌇෴\u0ee4뵗\uec0b䖌輸頃稂䔌㩬퉼͋샌뚱꯲\ud840揣篶鷒黹뵗\uec0b䖌輸頃稂䔌㩬퉼͋语ܠ撳铞⍫セෆ熪䚴펑庎흃빇롪", View.MeasureSpec.getSize(0) + 45, objArr3);
                            g.e(intern, ((String) objArr3[0]).intern());
                            return null;
                        }
                    default:
                        int i4 = i2 + 29;
                        m = i4 % 128;
                        switch (i4 % 2 == 0 ? 'K' : 'D') {
                            case 'K':
                                int i5 = 21 / 0;
                                return null;
                            default:
                                return null;
                        }
                }
            default:
                throw null;
        }
    }

    @Override // o.bl.a
    public final String b() {
        int i2 = m + 19;
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        int i5 = i3 + Opcodes.LUSHR;
        m = i5 % 128;
        int i6 = i5 % 2;
        return null;
    }

    @Override // o.bl.a
    public final o.bi.a a() {
        String intern;
        Object obj;
        o.eg.b bVar = this.b;
        switch (bVar == null ? 'N' : 'B') {
            case 'N':
                int i2 = m + Opcodes.DREM;
                f = i2 % 128;
                int i3 = i2 % 2;
                break;
            default:
                Object[] objArr = new Object[1];
                l("⫠ࡤ\uec9f\ue424ᢏ恷ꏊ嫓", 8 - View.combineMeasuredStates(0, 0), objArr);
                o.eg.b u = bVar.u(((String) objArr[0]).intern());
                if (u == null) {
                    int i4 = f + 93;
                    m = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case true:
                            g.c();
                            Object[] objArr2 = new Object[1];
                            k(null, (ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr2);
                            intern = ((String) objArr2[0]).intern();
                            Object[] objArr3 = new Object[1];
                            k(null, 127 - View.getDefaultSize(0, 0), null, "\u008a\u0084\u0082\u008e\u008d\u0084\u008a\u009b\u0083\u0086\u0082\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u008c\u009b\u0084\u0089\u0081 \u0084\u0091\u008e\u008d\u009b\u009c\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0089\u0081 \u0084\u0091\u008e\u0096\u0083\u0084\u008f", objArr3);
                            obj = objArr3[0];
                            break;
                        default:
                            g.c();
                            Object[] objArr4 = new Object[1];
                            k(null, 80 / (ViewConfiguration.getPressedStateDuration() * 55), null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr4);
                            intern = ((String) objArr4[0]).intern();
                            Object[] objArr5 = new Object[1];
                            k(null, 25 >> View.getDefaultSize(0, 0), null, "\u008a\u0084\u0082\u008e\u008d\u0084\u008a\u009b\u0083\u0086\u0082\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u008c\u009b\u0084\u0089\u0081 \u0084\u0091\u008e\u008d\u009b\u009c\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0089\u0081 \u0084\u0091\u008e\u0096\u0083\u0084\u008f", objArr5);
                            obj = objArr5[0];
                            break;
                    }
                    g.d(intern, ((String) obj).intern());
                    break;
                } else {
                    try {
                        Object[] objArr6 = new Object[1];
                        k(null, 127 - KeyEvent.keyCodeFromString(""), null, "\u008a¡\u0091\u0084\u008a\u0082\u0084\u0089", objArr6);
                        String r = u.r(((String) objArr6[0]).intern());
                        Object[] objArr7 = new Object[1];
                        l("\uf23c慻舼霻ᮅ௵㷷㫩싱\ue049ἣ\uf643빇롪", 13 - (ViewConfiguration.getEdgeSlop() >> 16), objArr7);
                        String r2 = u.r(((String) objArr7[0]).intern());
                        Object[] objArr8 = new Object[1];
                        k(null, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 127, null, "\u008a¡\u0083\u008c\u0084\u0093\u0086\u0091\u0087", objArr8);
                        String r3 = u.r(((String) objArr8[0]).intern());
                        Object[] objArr9 = new Object[1];
                        k(null, ExpandableListView.getPackedPositionGroup(0L) + 127, null, "£\u0084¢\u008e\u0087\u0081", objArr9);
                        break;
                    } catch (o.eg.d e2) {
                        g.c();
                        Object[] objArr10 = new Object[1];
                        k(null, 127 - Color.red(0), null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr10);
                        String intern2 = ((String) objArr10[0]).intern();
                        Object[] objArr11 = new Object[1];
                        k(null, 127 - (ViewConfiguration.getScrollBarSize() >> 8), null, "\u0083\u0081\u009f\u0091\u0086\u008d\u009b\u008a\u008e\u0085\u0081\u0099\u0082\u008e\u009b\u0082\u0081\u009b\u0089\u0081\u009e\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u008c\u009b\u0084\u0089\u0081 \u0084\u0091\u008e\u008d\u009b\u009c\u009b¥¤\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0089\u0081 \u0084\u0091\u008e\u0096\u0083\u0084\u008f", objArr11);
                        g.e(intern2, ((String) objArr11[0]).intern());
                        return null;
                    }
                }
        }
        return null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0024, code lost:
    
        if (r10.b == null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0038, code lost:
    
        r1 = r10.b;
        r6 = new java.lang.Object[1];
        l("⌘货>\uf818", android.view.Gravity.getAbsoluteGravity(0, 0) + 3, r6);
        r1 = r1.u(((java.lang.String) r6[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0056, code lost:
    
        if (r1 != null) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0058, code lost:
    
        r0 = o.bl.c.f + 79;
        o.bl.c.m = r0 % 128;
        r0 = r0 % 2;
        o.ee.g.c();
        r1 = new java.lang.Object[1];
        k(null, android.view.KeyEvent.normalizeMetaState(0) + 127, null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", r1);
        r0 = ((java.lang.String) r1[0]).intern();
        r2 = new java.lang.Object[1];
        l("\uf6a6㌇⢳辫폊᷒鯋뮠ᴾ陸ꊷ齡ꐰ\ueab6㷷㫩싱\ue049嫚\ue9fe\ued72꼺⌘货\uf10a䫿윣ᩨᴾ陸ꊷ齡ꐰ\ueab6㷷㫩싱\ue049嫚\ue9fe⍫セෆ熪\uf2cf多⫠ࡤ唦鈴빇롪", 51 - (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), r2);
        o.ee.g.d(r0, ((java.lang.String) r2[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0094, code lost:
    
        return null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0095, code lost:
    
        r8 = new java.lang.Object[1];
        l("\uf23c慻舼霻ᮅ௵㷷㫩싱\ue049ἣ\uf643빇롪", 13 - android.text.TextUtils.indexOf("", ""), r8);
        r6 = r1.r(((java.lang.String) r8[0]).intern());
        r9 = new java.lang.Object[1];
        k(null, 127 - (android.view.KeyEvent.getMaxKeyCode() >> 16), null, "\u008a¡\u0083\u008c\u0084\u0093\u0086\u0091\u0087", r9);
        r7 = r1.r(((java.lang.String) r9[0]).intern());
        r9 = new java.lang.Object[1];
        l("븹㮝ྯ⧊\u1756畬ꊖ䤏Ҁ❓嬌ﳍ\ue8f3繗샌뚱", 15 - android.text.TextUtils.lastIndexOf("", '0', 0), r9);
        r1 = new o.bi.d(r6, r7, r1.r(((java.lang.String) r9[0]).intern()));
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x00e9, code lost:
    
        r0 = o.bl.c.f + 27;
        o.bl.c.m = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00f3, code lost:
    
        if ((r0 % 2) != 0) goto L31;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00f6, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00f7, code lost:
    
        switch(r3) {
            case 0: goto L49;
            default: goto L33;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00fa, code lost:
    
        return r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00fb, code lost:
    
        r4.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00fe, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0102, code lost:
    
        o.ee.g.c();
        r1 = new java.lang.Object[1];
        k(null, 127 - (android.view.ViewConfiguration.getWindowTouchSlop() >> 8), null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", r1);
        r0 = ((java.lang.String) r1[0]).intern();
        r2 = new java.lang.Object[1];
        k(null, ((byte) android.view.KeyEvent.getModifierMetaStateMask()) + kotlin.jvm.internal.ByteCompanionObject.MIN_VALUE, null, "\u0083\u0081\u009f\u0091\u0086\u008d\u009b\u008a\u008e\u0085\u0081\u0099\u0082\u008e\u009b\u0082\u0081\u009b\u0089\u0081\u009e\u009b\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u008c\u009b\u0089\u009f\u009e\u009b\u009c\u009b¥¤\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0089\u009f¦\u0083\u0084\u008f", r2);
        o.ee.g.e(r0, ((java.lang.String) r2[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x0133, code lost:
    
        return null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0027, code lost:
    
        r1 = r1 + 79;
        o.bl.c.m = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x002f, code lost:
    
        if ((r1 % 2) == 0) goto L45;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0031, code lost:
    
        return null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0032, code lost:
    
        r4.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0035, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x001e, code lost:
    
        if (r10.b == null) goto L16;
     */
    @Override // o.bl.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.bi.d c() {
        /*
            Method dump skipped, instructions count: 322
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.c.c():o.bi.d");
    }

    @Override // o.bl.a
    public final String d() {
        Object obj;
        int i2 = f + 19;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                k(null, (ViewConfiguration.getTouchSlop() >> 8) + 127, null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k(null, 110 - (ViewConfiguration.getTouchSlop() * Opcodes.IUSHR), null, "\u0091\u0084\u008a\u008e\u0099\u0086\u0091\u0098\u0082\u0086\u008e\u0083\u0081\u0091\u0090\u008f\u008e\u008d\u0082\u0086\u0097\u0084\u0085\u008e\u0096\u0087\u0087\u0095", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    private static void k(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        String str3 = str2;
        byte[] bArr = str3;
        if (str3 != null) {
            bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        char[] charArray = str != null ? str.toCharArray() : str;
        j jVar = new j();
        char[] cArr = d;
        int i3 = 8;
        long j2 = 0;
        if (cArr != null) {
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i4 = 0;
            while (true) {
                switch (i4 >= length) {
                    case false:
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr[i4])};
                            Object obj = o.e.a.s.get(1085633688);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getWindowTouchSlop() >> i3), (char) ((-1) - ExpandableListView.getPackedPositionChild(j2)), 493 - Color.blue(0));
                                byte b = (byte) 0;
                                byte b2 = b;
                                Object[] objArr3 = new Object[1];
                                n(b, b2, b2, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1085633688, obj);
                            }
                            cArr2[i4] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i4++;
                            i3 = 8;
                            j2 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        cArr = cArr2;
                        break;
                }
            }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(a)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(TextUtils.getOffsetBefore("", 0) + 10, (char) (8856 - Color.blue(0)), ExpandableListView.getPackedPositionType(0L) + 324);
                byte b3 = (byte) ($$b & 15);
                byte b4 = (byte) (b3 - 3);
                Object[] objArr5 = new Object[1];
                n(b3, b4, b4, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            switch (e ? 'L' : '\b') {
                case '\b':
                    switch (!c) {
                        case false:
                            int i5 = $10 + 53;
                            $11 = i5 % 128;
                            int i6 = i5 % 2;
                            jVar.e = charArray.length;
                            char[] cArr3 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                cArr3[jVar.c] = (char) (cArr[charArray[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                try {
                                    Object[] objArr6 = {jVar, jVar};
                                    Object obj3 = o.e.a.s.get(745816316);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(10 - View.getDefaultSize(0, 0), (char) TextUtils.getOffsetAfter("", 0), 207 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                                        byte length2 = (byte) $$a.length;
                                        byte b5 = (byte) (length2 - 4);
                                        Object[] objArr7 = new Object[1];
                                        n(length2, b5, b5, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                        o.e.a.s.put(745816316, obj3);
                                    }
                                    ((Method) obj3).invoke(null, objArr6);
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                            objArr[0] = new String(cArr3);
                            return;
                        default:
                            jVar.e = iArr.length;
                            char[] cArr4 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                int i7 = $11 + 85;
                                $10 = i7 % 128;
                                int i8 = i7 % 2;
                                cArr4[jVar.c] = (char) (cArr[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                jVar.c++;
                            }
                            objArr[0] = new String(cArr4);
                            return;
                    }
                default:
                    jVar.e = bArr2.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr[bArr2[(jVar.e - 1) - jVar.c] + i2] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 208 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)));
                                byte length3 = (byte) $$a.length;
                                byte b6 = (byte) (length3 - 4);
                                Object[] objArr9 = new Object[1];
                                n(length3, b6, b6, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.c.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
