package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k3.smali */
public class k3 extends u {
    r C;
    r b;
    r x;

    private k3(e0 e0Var) {
        if (e0Var.size() != 3) {
            throw new IllegalArgumentException("Bad sequence size: " + e0Var.size());
        }
        Enumeration j = e0Var.j();
        this.b = r.a(j.nextElement());
        this.x = r.a(j.nextElement());
        this.C = r.a(j.nextElement());
    }

    public static k3 a(Object obj) {
        if (obj instanceof k3) {
            return (k3) obj;
        }
        if (obj != null) {
            return new k3(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.C.h();
    }

    public BigInteger f() {
        return this.b.h();
    }

    public BigInteger g() {
        return this.x.h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(3);
        iVar.a(this.b);
        iVar.a(this.x);
        iVar.a(this.C);
        return new j2(iVar);
    }
}
