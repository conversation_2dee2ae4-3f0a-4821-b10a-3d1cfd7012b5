package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\i6.smali */
public interface i6 {
    public static final w A;
    public static final w A0;
    public static final w A1;
    public static final w B;
    public static final w B0;
    public static final w B1;
    public static final w C;
    public static final w C0;
    public static final w C1;
    public static final w D;
    public static final w D0;
    public static final w D1;
    public static final w E;
    public static final w E0;
    public static final w E1;
    public static final w F;
    public static final w F0;
    public static final w F1;
    public static final w G;
    public static final w G0;
    public static final w G1;
    public static final w H;
    public static final w H0;
    public static final w H1;
    public static final w I;
    public static final w I0;
    public static final w I1;
    public static final w J;
    public static final w J0;
    public static final w J1;
    public static final w K;
    public static final w K0;
    public static final w K1;
    public static final w L;
    public static final w L0;
    public static final w L1;
    public static final w M;
    public static final w M0;
    public static final w M1;
    public static final w N;
    public static final w N0;
    public static final w N1;
    public static final w O;
    public static final w O0;
    public static final w O1;
    public static final w P;
    public static final w P0;
    public static final w P1;
    public static final w Q;
    public static final w Q0;
    public static final w Q1;
    public static final w R;
    public static final w R0;
    public static final w R1;
    public static final w S;
    public static final w S0;
    public static final w S1;
    public static final w T;
    public static final w T0;
    public static final w T1;
    public static final w U;
    public static final w U0;
    public static final w U1;
    public static final w V;
    public static final w V0;
    public static final w V1;
    public static final w W;
    public static final w W0;
    public static final w W1;
    public static final w X;
    public static final w X0;
    public static final w X1;
    public static final w Y;
    public static final w Y0;
    public static final w Z;
    public static final w Z0;
    public static final w a;
    public static final w a0;
    public static final w a1;
    public static final w b;
    public static final w b0;
    public static final w b1;
    public static final w c;
    public static final w c0;
    public static final w c1;
    public static final w d;
    public static final w d0;
    public static final w d1;
    public static final w e;
    public static final w e0;
    public static final w e1;
    public static final w f;
    public static final w f0;
    public static final w f1;
    public static final w g;
    public static final w g0;
    public static final w g1;
    public static final w h;
    public static final w h0;
    public static final w h1;
    public static final w i;
    public static final w i0;
    public static final w i1;
    public static final w j;
    public static final w j0;
    public static final w j1;
    public static final w k;
    public static final w k0;
    public static final w k1;
    public static final w l;
    public static final w l0;
    public static final w l1;
    public static final w m;
    public static final w m0;
    public static final w m1;
    public static final w n;
    public static final w n0;
    public static final w n1;

    /* renamed from: o, reason: collision with root package name */
    public static final w f23o;
    public static final w o0;
    public static final w o1;
    public static final w p;
    public static final w p0;
    public static final w p1;
    public static final w q;
    public static final w q0;
    public static final w q1;
    public static final w r;
    public static final w r0;
    public static final w r1;
    public static final w s;
    public static final w s0;
    public static final w s1;
    public static final w t;
    public static final w t0;
    public static final w t1;
    public static final w u;
    public static final w u0;
    public static final w u1;
    public static final w v;
    public static final w v0;
    public static final w v1;
    public static final w w;
    public static final w w0;
    public static final w w1;
    public static final w x;
    public static final w x0;
    public static final w x1;
    public static final w y;
    public static final w y0;
    public static final w y1;
    public static final w z;
    public static final w z0;
    public static final w z1;

    static {
        w wVar = new w("1.2.840.113549.1.1");
        a = wVar;
        b = wVar.a("1");
        c = wVar.a("2");
        d = wVar.a("3");
        e = wVar.a("4");
        f = wVar.a("5");
        g = wVar.a("6");
        h = wVar.a("7");
        i = wVar.a("8");
        j = wVar.a("9");
        k = wVar.a("10");
        l = wVar.a("11");
        m = wVar.a("12");
        n = wVar.a("13");
        f23o = wVar.a("14");
        p = wVar.a("15");
        q = wVar.a("16");
        w wVar2 = new w("1.2.840.113549.1.3");
        r = wVar2;
        s = wVar2.a("1");
        w wVar3 = new w("1.2.840.113549.1.5");
        t = wVar3;
        u = wVar3.a("1");
        v = wVar3.a("4");
        w = wVar3.a("3");
        x = wVar3.a("6");
        y = wVar3.a("10");
        z = wVar3.a("11");
        A = wVar3.a("12");
        B = wVar3.a("13");
        C = wVar3.a("14");
        w wVar4 = new w("1.2.840.113549.3");
        D = wVar4;
        E = wVar4.a("7");
        F = wVar4.a("2");
        G = wVar4.a("4");
        w wVar5 = new w("1.2.840.113549.2");
        H = wVar5;
        I = wVar5.a("2");
        J = wVar5.a("4");
        K = wVar5.a("5");
        L = wVar5.a("7").j();
        M = wVar5.a("8").j();
        N = wVar5.a("9").j();
        O = wVar5.a("10").j();
        P = wVar5.a("11").j();
        Q = wVar5.a("12").j();
        R = wVar5.a("13").j();
        S = new w("1.2.840.113549.1.7").j();
        T = new w("1.2.840.113549.1.7.1").j();
        U = new w("1.2.840.113549.1.7.2").j();
        V = new w("1.2.840.113549.1.7.3").j();
        W = new w("1.2.840.113549.1.7.4").j();
        X = new w("1.2.840.113549.1.7.5").j();
        Y = new w("1.2.840.113549.1.7.6").j();
        w wVar6 = new w("1.2.840.113549.1.9");
        Z = wVar6;
        a0 = wVar6.a("1").j();
        b0 = wVar6.a("2").j();
        c0 = wVar6.a("3").j();
        d0 = wVar6.a("4").j();
        e0 = wVar6.a("5").j();
        f0 = wVar6.a("6").j();
        g0 = wVar6.a("7").j();
        h0 = wVar6.a("8").j();
        i0 = wVar6.a("9").j();
        j0 = wVar6.a("13").j();
        k0 = wVar6.a("14").j();
        l0 = wVar6.a("15").j();
        w j2 = wVar6.a("16").j();
        m0 = j2;
        n0 = wVar6.a("16.2.46").j();
        o0 = wVar6.a("20").j();
        p0 = wVar6.a("21").j();
        q0 = wVar6.a("22.1");
        w a2 = wVar6.a("22");
        r0 = a2;
        s0 = a2.a("1").j();
        t0 = a2.a("2").j();
        w a3 = wVar6.a("23");
        u0 = a3;
        v0 = a3.a("1").j();
        w0 = wVar6.a("52").j();
        x0 = wVar6.a("15.1");
        y0 = wVar6.a("15.2");
        z0 = wVar6.a("15.3");
        w wVar7 = new w("1.2.840.113549.1.9.16.1");
        A0 = wVar7;
        B0 = wVar7.a("2");
        C0 = wVar7.a("4");
        D0 = wVar7.a("9");
        E0 = wVar7.a("23");
        F0 = wVar7.a("31");
        w a4 = j2.a("3");
        G0 = a4;
        H0 = a4.a("5");
        I0 = a4.a("6");
        J0 = a4.a("7");
        K0 = a4.a("8");
        L0 = a4.a("9");
        M0 = a4.a("10");
        N0 = a4.a("14");
        O0 = a4.a("17");
        P0 = a4.a("18");
        Q0 = a4.a("28");
        R0 = a4.a("29");
        S0 = a4.a("30");
        w wVar8 = new w("1.2.840.113549.1.9.16.6");
        T0 = wVar8;
        U0 = wVar8.a("1");
        V0 = wVar8.a("2");
        W0 = wVar8.a("3");
        X0 = wVar8.a("4");
        Y0 = wVar8.a("5");
        Z0 = wVar8.a("6");
        w wVar9 = new w("1.2.840.113549.1.9.16.2");
        a1 = wVar9;
        b1 = wVar9.a("1");
        c1 = wVar9.a("4");
        d1 = wVar9.a("5");
        e1 = wVar9.a("10");
        f1 = wVar9.a("11");
        g1 = wVar9.a("12");
        h1 = wVar9.a("47");
        i1 = wVar9.a("7");
        j1 = wVar9.a("14");
        w a5 = wVar9.a("15");
        k1 = a5;
        w a6 = wVar9.a("16");
        l1 = a6;
        w a7 = wVar9.a("17");
        m1 = a7;
        n1 = wVar9.a("18");
        w a8 = wVar9.a("19");
        o1 = a8;
        p1 = wVar9.a("20");
        q1 = wVar9.a("21");
        r1 = wVar9.a("22");
        s1 = wVar9.a("23");
        t1 = wVar9.a("24");
        u1 = wVar9.a("25");
        v1 = wVar9.a("26");
        w1 = wVar9.a("27");
        x1 = wVar9.a("37");
        y1 = wVar9.a("38");
        z1 = wVar9.a("54");
        A1 = wVar9.a("43");
        B1 = wVar9.a("40");
        C1 = a5;
        D1 = a6;
        E1 = a7;
        F1 = a8;
        G1 = new w("1.2.840.113549.1.9.16.5.1");
        H1 = new w("1.2.840.113549.1.9.16.5.2");
        w wVar10 = new w("1.2.840.113549.1.12");
        I1 = wVar10;
        w a9 = wVar10.a("10.1");
        J1 = a9;
        K1 = a9.a("1");
        L1 = a9.a("2");
        M1 = a9.a("3");
        N1 = a9.a("4");
        O1 = a9.a("5");
        P1 = a9.a("6");
        w a10 = wVar10.a("1");
        Q1 = a10;
        R1 = a10.a("1");
        S1 = a10.a("2");
        T1 = a10.a("3");
        U1 = a10.a("4");
        V1 = a10.a("5");
        W1 = a10.a("6");
        X1 = a10.a("6");
    }
}
