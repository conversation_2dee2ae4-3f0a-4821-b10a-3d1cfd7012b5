package com.google.android.gms.tapandpay.globalactions;

import android.app.PendingIntent;
import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GlobalActionCard.smali */
public final class GlobalActionCard extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GlobalActionCard> CREATOR = new zzf();
    private int zza;
    private String zzb;
    private Bitmap zzc;
    private String zzd;
    private String zze;
    private String zzf;
    private Bitmap zzg;
    private PendingIntent zzh;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GlobalActionCard$Builder.smali */
    public static final class Builder {
        private final GlobalActionCard zza;

        public Builder() {
            this.zza = new GlobalActionCard(null);
        }

        public GlobalActionCard build() {
            return this.zza;
        }

        public Builder setCardId(String cardId) {
            this.zza.zzb = cardId;
            return this;
        }

        public Builder setCardImage(Bitmap cardImage) {
            this.zza.zzc = cardImage;
            return this;
        }

        public Builder setCardType(int cardType) {
            this.zza.zza = cardType;
            return this;
        }

        public Builder setContentDescription(String contentDescription) {
            this.zza.zzd = contentDescription;
            return this;
        }

        public Builder setDeviceLockedMessageText(String deviceLockedMessageText) {
            this.zza.zzf = deviceLockedMessageText;
            return this;
        }

        public Builder setMessageIcon(Bitmap messageIcon) {
            this.zza.zzg = messageIcon;
            return this;
        }

        public Builder setMessageText(String messageText) {
            this.zza.zze = messageText;
            return this;
        }

        public Builder setPendingIntent(PendingIntent pendingIntent) {
            this.zza.zzh = pendingIntent;
            return this;
        }

        public Builder(GlobalActionCard origin) {
            GlobalActionCard globalActionCard = new GlobalActionCard(null);
            this.zza = globalActionCard;
            globalActionCard.zza = origin.zza;
            globalActionCard.zzb = origin.zzb;
            globalActionCard.zzc = origin.zzc;
            globalActionCard.zzd = origin.zzd;
            globalActionCard.zze = origin.zze;
            globalActionCard.zzf = origin.zzf;
            globalActionCard.zzg = origin.zzg;
            globalActionCard.zzh = origin.zzh;
        }
    }

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    @Retention(RetentionPolicy.SOURCE)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GlobalActionCard$CardType.smali */
    public @interface CardType {
        public static final int CAR_KEY = 8;
        public static final int CTA = 3;
        public static final int GLOBAL_ACTIONS_DISMISSED = 4;
        public static final int PASS = 1;
        public static final int PAYMENT = 2;
        public static final int STUDENT_ID = 6;
        public static final int TILE = 9;
        public static final int TRANSIT = 7;
        public static final int UNKNOWN = 0;
        public static final int VALUABLE = 5;
    }

    private GlobalActionCard() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof GlobalActionCard) {
            GlobalActionCard globalActionCard = (GlobalActionCard) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(globalActionCard.zza)) && Objects.equal(this.zzb, globalActionCard.zzb) && Objects.equal(this.zzc, globalActionCard.zzc) && Objects.equal(this.zzd, globalActionCard.zzd) && Objects.equal(this.zze, globalActionCard.zze) && Objects.equal(this.zzf, globalActionCard.zzf) && Objects.equal(this.zzg, globalActionCard.zzg) && Objects.equal(this.zzh, globalActionCard.zzh)) {
                return true;
            }
        }
        return false;
    }

    public String getCardId() {
        return this.zzb;
    }

    public Bitmap getCardImage() {
        return this.zzc;
    }

    public int getCardType() {
        return this.zza;
    }

    public String getContentDescription() {
        return this.zzd;
    }

    public String getDeviceLockedMessageText() {
        return this.zzf;
    }

    public Bitmap getMessageIcon() {
        return this.zzg;
    }

    public String getMessageText() {
        return this.zze;
    }

    public PendingIntent getPendingIntent() {
        return this.zzh;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), this.zzb, this.zzc, this.zzd, this.zze, this.zzf, this.zzg, this.zzh);
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 1, getCardType());
        SafeParcelWriter.writeString(dest, 2, getCardId(), false);
        SafeParcelWriter.writeParcelable(dest, 3, getCardImage(), flags, false);
        SafeParcelWriter.writeString(dest, 4, getContentDescription(), false);
        SafeParcelWriter.writeString(dest, 5, getMessageText(), false);
        SafeParcelWriter.writeParcelable(dest, 6, getMessageIcon(), flags, false);
        SafeParcelWriter.writeParcelable(dest, 7, getPendingIntent(), flags, false);
        SafeParcelWriter.writeString(dest, 8, getDeviceLockedMessageText(), false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    GlobalActionCard(int i, String str, Bitmap bitmap, String str2, String str3, String str4, Bitmap bitmap2, PendingIntent pendingIntent) {
        this.zza = i;
        this.zzb = str;
        this.zzc = bitmap;
        this.zzd = str2;
        this.zze = str3;
        this.zzf = str4;
        this.zzg = bitmap2;
        this.zzh = pendingIntent;
    }

    /* synthetic */ GlobalActionCard(zze zzeVar) {
    }
}
