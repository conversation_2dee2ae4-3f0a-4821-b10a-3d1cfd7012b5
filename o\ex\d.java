package o.ex;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.j;
import o.el.b;
import o.et.c;
import o.et.f;
import o.ey.e;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ex\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long c;
    private static int d;
    private static char[] e;
    private final List<e> b = new ArrayList();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        d = 1;
        h();
        AndroidCharacter.getMirror('0');
        Color.rgb(0, 0, 0);
        View.combineMeasuredStates(0, 0);
        TextUtils.getCapsMode("", 0, 0);
        ViewConfiguration.getTapTimeout();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        View.getDefaultSize(0, 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getZoomControlsTimeout();
        Gravity.getAbsoluteGravity(0, 0);
        Process.getGidForName("");
        View.resolveSize(0, 0);
        SystemClock.elapsedRealtimeNanos();
        View.resolveSize(0, 0);
        KeyEvent.getDeadChar(0, 0);
        TextUtils.getOffsetBefore("", 0);
        ViewConfiguration.getScrollFriction();
        int i = d + 85;
        a = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ex.d.$$a
            int r6 = 105 - r6
            int r7 = r7 * 4
            int r7 = r7 + 4
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L32:
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.g(int, short, short, java.lang.Object[]):void");
    }

    static void h() {
        char[] cArr = new char[1697];
        ByteBuffer.wrap(",\u009aã\u0015³ÉC\u008b\u0012L\"8òö\u0082¼Qda,1ÓÀQ\u0090\u0000 Õp÷\u0007\u007f×+çò·ªF\u000b\u0016\u009d&\u008dõ\\\u0085vUÿe\u00ad4oÄ&\u0094Ù«\u001c{\u0004\u000bÏÛ\u0087ê~º4Jö\u001aí)\u001dùÑ\u0089\u008fXUh\u00018òÈ¶\u009fl¯6\u007f\u0092\u000e\u001eÞ\tîÁ¾\u0092M\u007f\u001d8-êý»\u008cV\\Úl\u009d#ZóJ\u0083øS½bi2/Âî\u0091U¡\tØ\u00ad\u0017%Gö·»æ|Ö\u000e\u0006ßv¹¥P\u0095\u0005Å\u008a4od?Tù\u0084\u0084óO#\u001f, ã\u001e³ÒC\u0096\u0012D\"5òó\u0082¯Q{a-1ÓÀ\u0017\u0090E Êp¥\u0007q×4ç ·¡F\u0001\u0016Ò&\u008aõ\u000f\u0085lU±\u009d\u0013R\u0093\u0002Dò\u001c£\u009d\u0093\u0081Cw35àôÐ¨\u0080\u0017qÃ!\u009a\u0011RÁg¶ëf§Vs\u00064÷\u0097§C\u0097\u0000DÚ4´ädÔ,\u0085³u\u00ad%\r\u001aÅÊ\u009bº[jY[é\u000b¥û{«)\u0098\u008dHN8\u001aéØÙ\u0082\u0089jy$.ò\u001eüÎ7¿Ïo\u0090_]\u000f\u001eüì¬¹\u009c_L:=\u009fílÝ\t\u0092ÝB\u009b2bâ)Óå\u0083þs4 \u0080Äë\u000bE[\u008a«Öú-Êe\u001a¶jû¹4\u0089vÙ×(!xPH\u0085\u0098ôïn?$\u000fð_ë®Cþ\u0094ÎÙ\u001d\u001amh½µ\u008d¨Ü8,\u007f|ÜCL\u0093Dã\u009b3Í\u0002`R1¢ò,¬ã\u001d³ÍC£\u0012]\"$òó\u0082¯Qba)1\u0087ÀS\u0090\n Âp\u009e\u0007zLÏ\u0083aÓ®#òr\tBA\u0092\u0092âß1\u0010\u0001RQó \u0005ðtÀ¡\u0010ÐgJ·\u0000\u0087Ô×Ú&kv¿FÑ\u0095+åR5¬\u0005ÈTW¤\u0004ô¡8\u0011÷¹§jW'\u0006à6\u0092æC\u0096NEÂu\u0085%\"Ôá\u0084í´fd\u001a\u0013ÚÃ\u009eóF£\u0004R©\u0002528áè\u0091ÞALq\u001e ÀÐ\u008c\u00802¿ão¡\u001f&Ï$þÕ®\u0085^\n\u000e$=¬íg\u009d\"Là|£,ZÜ\u0006\u008bÄ»\u008bk1\u001a¶Êëú(ªfYÉ,¹ã\u0011³ÂC\u008f\u0012H\":òë\u0082æQja-1\u008aÀI\u0090E Íp¤\u0007m×6çã·¢F\u0013\u0016É&\u0081õK\u0085vUåe·4#Ä\u000f\u0094\u0098«J{G\u000bïÛ\u0099ê`º7Jë\u001a®)\u0015ùË\u0089\u008fXNh\u00068³Èà\u009f%¯i\u007f\u0084\u000e\u001eÞ\rîÈ¾\u008aMf\u001d}-àý \u008c\u0013\\ÂlØ#Mó\u0005\u0083áSübu2+Âø\u0091E¡\u0012qÐ\u0001\u0088Ð4à4°ã@¸\u0017{,»ã\u0015³ÚC\u0086\u0012}\"5òæ\u0082«Qda&1\u0087Àq\u0090\u0000 Õp¤\u0007>×tç · F\u0017\u0016Ä&Äõ\\\u00853Uåeø4qÄ/\u0094\u009b«Y{\u0010\u000bÏÛ\u0085ê0º?Jç\u001a¹)\u0011ùÜ\u0089\u0092XDh\f,¢ã\u0015³ÂC\u0091\u009cdSÊ\u0003\u0005óY¢¢\u0092êB92tá»Ñù\u0081Xp® ß\u0010\nÀ{·ág«W\u007f\u0007`öÅ¦\u0007\u0096\u001bE\u009e5ìå9Õ'\u0084·tð$S\u001bÃËË»\u0014kBZï\n¾ú}ª7\u0099ØI@9]è\u009bØÑ\u0088%xk/¿\u001fàÏ\b¾\u0080nÈ^_\u000e]ý£\u00adá\u009d4M~<Úì\u0007ÜT\u0093\u0088CÐ3$ãwÒø\u0082år>!\u009d\u0011ÁÁ\u000e±Z`¤Pì\u0000=ð>§¡\u0097\rGI6\u008fæÖÖh\u0086,uæ%ë\u0015<Ä\u009f´Çd\bTX\u000b¦ûâ«#\u009b<Jï:Jê\u0006Ù\u009c\u0089\u0091y[)/\u0018¯Èø¸+oË_Ó\u000f\u001cÿJ®×\u009eÿN,>`í¶ÝH\u008d\u001b|Æ,\u009a\u001c\u0010÷c8ãh4\u0098lÉíùÑ)\u0007YE\u008a\u0084ºØêg\u001b³Kê{\"«\u0017Ü\u009b\f×<\u0003lD\u009dçÍ3ýp.ª^Ä\u008e\u0014¾\\ïÃ\u001fÝO}pµ ëÐ+\u0000)1\u0082aÞ\u0091\u0003ÁIòý\"1Ra\u0083á³øã\u0012\u0013CD\u0088tÉ¤yÕª\u0005¹5+en\u0096\u008bÆÎöD&\u0015W¶,ºã\u0015³ÉC\u008b\u0012L\"8òö\u0082¼Qdah1ÞÀ\u001a\u0090\u000f ßp¸\u0007p×yçï·¾F\u0006\u0016Í&\u0091õ[\u0085vU«eø 5o\u0081,¬ã\u001d³ÍC£\u0012]\"$òó\u0082¯Qba)1\u0087ÀS\u0090\n Âp¤,ªã\u0011³ÉC\u0086\u0012^,¹ã\u0002³ÒC\u008c\u0012Y\"\u0007òë\u0082§Qua=1\u0080À\u001a\u0090H \u008cpº\u0007q×;çé·§F\u0017\u0016\u009d&\u0097õ[\u00857Uåe\u00ad4pÄj\u0094\u0092«Y{\t\u000bËÛ\u009bêqº/Jç\u001a©)Tù\u0085\u0089ÆùÝ6af¾\u0096âÇ8÷P'½WÈ\u0084\u000e´]äó\u00152Eju¨¥ÄÒ\f\u0002\u00182Ìb\u008a\u0093vÃ±óó nPV\u0080\u0080°Éá\u000e\u0011BA÷~<®rÞ¦\u000eç?\u001fo\u001a\u009fÙÏ\u008cü0,\u00ad\\§\u008dm½)í\u0081\u001dÏJ\u0005zYªãÛ,\u000b8;ûkª\u0098VÈO,¼ã\u0000³ßC\u0083\u0012Y\"1òÜ\u0082©Qoa<1\u0092ÀS\u0090\u000b Ép¥\u0007m×yç\u00ad·ëF\u0011\u0016Ï&\u0081õN\u0085\"Uøe¶4dÄj\u0094\u009b«Y{\u0010\u000b\u008eÛ\u0088êsº/Jë\u001a»)\u0011ù\u009f\u0089\u0085XNh\u00068çÈ»\u009fl¯\"\u007f\u0092\u000eLÞYîÆ¾\u0084M`\u001d}-áý¢\u008c\u0000\\\u0091l\u0099#Só\u001a\u0083ùSµbd2/Âý\u0091Y¡\u0014qÌ\u0001ÍÐ.à\u007f°£@²,¼ã\u0000³ßC\u0083\u0012Y\"1òÜ\u0082©Qoa<1\u0092ÀS\u0090\u000b Ép¥\u0007m×yç\u00ad·ëF\u0011\u0016Ò&\u008aõ[\u00857Uøe¶4fÄ8\u0094Õ«Z{\b\u000bÜÛÉêuº6Jô\u001aí)\u0015ùÏ\u0089\u0096XMh\u00018ðÈ»\u009fq¯%\u007f\u0098\u000ePÞYî\u009a¾ËM7\u001d.-¤ý§\u008c\u0017\\ÂlØ#Aó\u000f\u0083ðS²b'2<Âì\u0091D¡\tqË\u0001\u0088Ðbà:°âç\u001c( x\u007f\u0088#Ùùé\u00919|I\t\u009aÏª\u009cú2\u000bó[«ki»\u0005ÌÍ\u001cÙ,\r|K\u008d±Ýrí*>ûN\u0097\u009eX®\u0016ÿÆ\u000f\u0098_u`ë°¦À}\u0010i!Ôq\u0092\u0081QÑ\fâ¶2sB#\u0093å£èó\t\u0003ZT×d\u0089´$Åê\u0015¶%ru\"\u0086ÜÖ\u009aæ\u00046\u0006G¢ \u0087ï;¿äO¸\u001eb.\nþç\u008e\u0092]Tm\u0007=©Ìh\u009c0¬ò|\u009e\u000bVÛBë\u0096»ÐJ\u001f\u001aï*¬ùu\u0089MYïi\u008e8NÈQ\u0098¯§ww,\u0007¹×òæB¶\u000eFÏ\u0016\u0097%#õí\u0085¹T{d'4ÍÄÁ\u0093_£\u0003s¯\u0002%Ò1âï²±A[\u0011\u0012£\u0006lº<eÌ9\u009dã\u00ad\u008b}f\r\u0013ÞÕî\u0086¾(Oé\u001f±/sÿ\u001f\u0088×XÃh\u00178QÉ«\u0099u©;zô\n\u0098ÚBê\f»ÞKÐ\u001b!$ãôª\u00844T7eÃ5\u0092ÅY\u0095\u0015¦¢v`\u00068×»ç±·FG\u000e\u0010Ë \u0097ð$\u0081êQ¦ah1qÂÎ\u0092\u0088¢LrU\u0003©Ófã4¬¹|±\f_Ü\u0016íÑ½\u009dMP\u001eë.µþq\u008e8_ÀoÅ?\u0006Ï[\u0098\u0097¨z,¼ã\u0000³ßC\u0083\u0012Y\"1òÜ\u0082©Qoa<1\u0092ÀS\u0090\u000b Ép¥\u0007m×yç\u00ad·ëF\u0011\u0016Ò&\u008aõ[\u00857Uøe¶4fÄ8\u0094Õ«K{\u0006\u000bÝÛÉê~º4Jö\u001aí)\u0010ùÖ\u0089\u0095X@h\n8ÿÈ¿\u009fa¯l\u007fÍ\u000e\u001eÞ\u001dîÉ¾\u0098Ms\u001d?-èý¦\u008c\u0018\\ÖlØ#Jó\u001eðp?Ìo\u0013\u009fOÎ\u0095þý.\u0010^e\u008d£½ðí^\u001c\u009fLÇ|\u0005¬iÛ¡\u000bµ;ak'\u009aÛÊ\u001cú^)ÃYû\u0089-¹dè£\u0018ïHZw\u0091§ß×\u000b\u0007J6²f·\u0096tÆ!õ\u009d%\u0000U\n\u0084\u0085´Åä,\u00146C«så£^Ò\u009c\u0002\u00952\bbB\u0091²Áôñ<!fPÞ\u0080Q°\u0014ÿ\u0089/Ê_,\u008fc¾£îë\u001e+M\u009b}\u0097\u00ad\rÝN\f¶<çl+\u009cdËªû\u001a+Dÿ_0ë`:\u0090sÁ¾ñÔ!\tQq\u0082\u008b²Êâh\u0013ºCòs:£uÔ\u008d\u0004Ò4\u001fd\\\u0095îÅ;õ]&¸VÝ\u0086\u0010,\u00adã\u0019³ÈC\u0081\u0012L\"&òû\u0082\u0085Qna&1\u0080ÀO\u0090\b Ép³\u0007Q×+çÄ·¢F\u0001\u0016Þ&\u0085õ]\u00852Uôe¼4SÄ+\u0094\u008c«Q{\u0002\u000bÀÛ\u009dê[º>Jû\u001a¾,\u00adã\u0019³ÈC\u0081\u0012L\"&òû\u0082\u0096Q`a11\u009eÀ_\u0090\u000b Øp¤\u0007U×<çù·¸FR\u0016\u0090&ÄõJ\u0085;Uçeø4bÄ:\u0094\u0085«\u001c{]Æ\u001d\t®Y|©#ø÷È¥\u0018Bh\u001a»Ç\u008b\u009bÛ(*æz²Jl\u009a\u0016í\u008c=Æ\r\u0012]\u0017¬¯ü{Ìv\u001fóo\u008b¿W\u008f\u0003Þ×.\u0081~.Aà\u0091²á<17\u0000ËP\u008b \u0010ð<,®ã\u0015³ÏC¬\u0012H\",òë\u0082\u0095Qha&1\u0094ÀV\u0090\u0000 ùp¤\u0007{×\u0012çå·²F4\u0016Ò&\u0096õ\u007f\u00857Uèeµ4fÄ$\u0094\u0081«\u001c{J\u000b\u008eÛ\u0088ê`º+Jî\u001a¤)\u0017ùÞ\u0089\u0092XHh\u00078ýÈú\u009f?¯l,éã\u0018³ÚC\u0091\u0012\r\":òð\u0082æQca-1\u0096ÀT\u0090E Þp²\u0007m×6çì·½F\u0017\u0016Ù,®ã\u0015³ÏC£\u0012[\"5òö\u0082ªQ`a*1\u009fÀ_\u0090. Ép®\u0007P×,çí·©F\u0017\u0016Ï&Äõ\u0002\u0085vUðe¨4sÄ&\u0094\u009c«_{\u0006\u000bÚÛ\u0080ê\u007fº5J¢\u001a÷)T,ªã\u0018³ÞC\u0081\u0012F\"\u0015òï\u0082¶Qma!1\u0090À[\u0090\u0011 Åp¸\u0007p×\u001fçò·®F\u0001\u0016Õ&¯õJ\u0085/Uße\u00ad4nÄ(\u0094\u0090«N{G\u000b\u0083ÛÉê`º:Jû\u001a )\u0011ùÑ\u0089\u0092X\u0001h\u00038öÈ£\u009f%¯/\u007f\u0098\u000ePÞ\rîÁ¾\u0082M|\u001d8-öýï\u008c\u0018\\Þl\u008c#\u0003ó\u0018\u0083ðS¨bu2'Âì\u0091F¡\u001eqÆ\u0003]Ìï\u009c)lv=±\râÝ\u0018\u00adA~\u009aNÖ\u001egï¬¿æ\u008f2_O(\u0087øèÈ\u0005\u0098Yiö9\"\tXÚ½ªØz(JZ\u001b\u0099ëß»g\u0084¹T°$tô>Å\u0097\u0095Íe\f5W\u0006æÖ&¦ewöGô\u0017\u0001çT°Ò\u0080ØPo!§ñúÁ6\u0091ub\u008b2Ï\u0002\u0001Ò\u0018£ås)Cj\f§Ü½¬\f|DM\u0084\u001d\u0099í\f¾¢\u008eý^ .sÿ\u0091ÏÍ\u009fQo]8\u009a\b=Ø~\u0094\r[¿\u000byû&ªá\u009a²JH:\u0011éÊÙ\u0086\u00897xü(¶\u0018bÈ\u001f¿×o¸_U\u000f\tþ¦®r\u009e\bMí=\u0088íxÝ\n\u008cÉ|\u008f,7\u0013éÃà³$cnRÇ\u0002\u009dò\\¢\u0007\u0091¶Av15à¦Ð¤\u0080Qp\u0004'\u0082\u0017\u0088Ç?¶÷fªVf\u0006%õÛ¥\u009f\u0095QEH4¡äwÔ-\u009båK ;Wë\u000fÚÅ\u008a\u009bz])·\u0019²Éj¹>h\u0093X\u008a\bDø\u0012¯Ý\u009f}O8>ôî®ÞT\u008eY}\u0084-Ç\u001dGÌð¼£lp\\h\u0003Ùó\u0097£I\u0093\u0001B\u008d2|â4Ñô\u0081éql!\u0012\u0010ÙÀ\u008b°\ngáW½\u0007b÷#¦æ\u0096\u0082FX6\u0006å\u008bÕi\u0085<tê".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1697);
        e = cArr;
        c = -935920836895382672L;
    }

    static void init$0() {
        $$a = new byte[]{106, 58, 15, 91};
        $$b = 17;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x007e. Please report as an issue. */
    public final void a(o.eg.e r12) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 282
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.a(o.eg.e):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:24:0x0132, code lost:
    
        if (r8 == false) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0146, code lost:
    
        o.ee.g.c();
        r14 = new java.lang.Object[1];
        f((char) ((android.view.ViewConfiguration.getTouchSlop() >> 8) + 62516), (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)) + 66, android.graphics.Color.alpha(r3) + 17, r14);
        r8 = ((java.lang.String) r14[r3]).intern();
        r3 = new java.lang.Object[1];
        f((char) (1 - (android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1))), (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1)) + 381, 42 - (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), r3);
        o.ee.g.d(r8, ((java.lang.String) r3[0]).intern());
        r10.b();
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x01aa, code lost:
    
        if (r2.contains(r10.a()) != false) goto L31;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x01ac, code lost:
    
        r2.add(r10.a());
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x01b3, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x0144, code lost:
    
        if (r12.e().equals(r13.e()) == false) goto L28;
     */
    /* JADX WARN: Removed duplicated region for block: B:32:0x01ed  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x0240 A[Catch: d -> 0x0316, TryCatch #0 {d -> 0x0316, blocks: (B:13:0x0106, B:15:0x010c, B:20:0x0123, B:23:0x0131, B:25:0x0146, B:27:0x01ac, B:29:0x01b6, B:30:0x01e7, B:33:0x01f7, B:36:0x0212, B:37:0x0215, B:42:0x0230, B:44:0x0237, B:52:0x0240, B:55:0x0249, B:59:0x02d6, B:60:0x0250, B:68:0x0138, B:72:0x02df, B:73:0x0315), top: B:12:0x0106 }] */
    /* JADX WARN: Removed duplicated region for block: B:62:0x02d4  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.List<java.lang.String> c(android.content.Context r22, o.eg.e r23) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 886
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.c(android.content.Context, o.eg.e):java.util.List");
    }

    public final o.eg.e a() throws o.eg.d {
        o.eg.e eVar = new o.eg.e();
        Iterator<e> it = this.b.iterator();
        while (true) {
            switch (it.hasNext()) {
                case true:
                    int i = a + 61;
                    d = i % 128;
                    int i2 = i % 2;
                    eVar.b(it.next().h());
                default:
                    g.c();
                    Object[] objArr = new Object[1];
                    f((char) ((ViewConfiguration.getTouchSlop() >> 8) + 62516), Color.rgb(0, 0, 0) + 16777283, View.getDefaultSize(0, 0) + 17, objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    f((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 600 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), 26 - View.MeasureSpec.getSize(0), objArr2);
                    g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar.a()).toString());
                    int i3 = d + 53;
                    a = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return eVar;
                    }
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.eg.b a(o.ek.b r14) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 382
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.a(o.ek.b):o.eg.b");
    }

    public final void e(Context context, o.el.d dVar, String str, boolean z) {
        Integer b;
        o.ez.d dVar2;
        Object[] objArr = new Object[1];
        f((char) (62515 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), ImageFormat.getBitsPerPixel(0) + 68, 18 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        if (dVar.o()) {
            g.c();
            Locale a2 = j.a();
            Object[] objArr2 = new Object[1];
            f((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 54625), ExpandableListView.getPackedPositionType(0L) + 687, 52 - TextUtils.lastIndexOf("", '0'), objArr2);
            g.d(intern, String.format(a2, ((String) objArr2[0]).intern(), dVar.n(), dVar.s()));
            e c2 = c(dVar.n());
            if (dVar.s() == b.b) {
                if (c2 != null) {
                    g.c();
                    Locale a3 = j.a();
                    Object[] objArr3 = new Object[1];
                    f((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 814, 72 - (Process.myTid() >> 22), objArr3);
                    g.d(intern, String.format(a3, ((String) objArr3[0]).intern(), dVar.n()));
                    g.c();
                    Locale a4 = j.a();
                    Object[] objArr4 = new Object[1];
                    f((char) (56525 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 1129 - (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getJumpTapTimeout() >> 16) + 76, objArr4);
                    g.d(intern, String.format(a4, ((String) objArr4[0]).intern(), c2.d()));
                    c2.i();
                    c2.l();
                    this.b.remove(c2);
                    return;
                }
                return;
            }
            int i = a + 67;
            d = i % 128;
            if (i % 2 == 0) {
                dVar.s();
                b bVar = b.d;
                throw null;
            }
            if (dVar.s() != b.d) {
                if (c2 == null) {
                    int i2 = d + Opcodes.LNEG;
                    a = i2 % 128;
                    int i3 = i2 % 2;
                    g.c();
                    Locale a5 = j.a();
                    Object[] objArr5 = new Object[1];
                    f((char) (Color.red(0) + 36794), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 994, 75 - (KeyEvent.getMaxKeyCode() >> 16), objArr5);
                    g.d(intern, String.format(a5, ((String) objArr5[0]).intern(), dVar.n()));
                    e<? extends o.fc.d> a6 = dVar.a(str);
                    a6.i();
                    this.b.add(a6);
                    return;
                }
                g.c();
                Locale a7 = j.a();
                Object[] objArr6 = new Object[1];
                f((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), 813 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 72 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr6);
                g.d(intern, String.format(a7, ((String) objArr6[0]).intern(), dVar.n()));
                if (!c2.g()) {
                    g.c();
                    Object[] objArr7 = new Object[1];
                    f((char) TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 1069, 61 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr7);
                    g.d(intern, ((String) objArr7[0]).intern());
                    c2.i();
                }
                if (z) {
                    c2.a(context);
                    return;
                }
                return;
            }
            if (c2 == null) {
                g.c();
                Locale a8 = j.a();
                Object[] objArr8 = new Object[1];
                f((char) Color.blue(0), 740 - View.MeasureSpec.getMode(0), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 72, objArr8);
                g.d(intern, String.format(a8, ((String) objArr8[0]).intern(), dVar.n()));
                this.b.add(dVar.a(str));
                return;
            }
            g.c();
            Locale a9 = j.a();
            Object[] objArr9 = new Object[1];
            f((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 813 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 72 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr9);
            g.d(intern, String.format(a9, ((String) objArr9[0]).intern(), dVar.n()));
            switch (c2.g()) {
                case true:
                    g.c();
                    Object[] objArr10 = new Object[1];
                    f((char) (52129 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), View.MeasureSpec.makeMeasureSpec(0, 0) + 885, TextUtils.getCapsMode("", 0, 0) + 56, objArr10);
                    g.d(intern, ((String) objArr10[0]).intern());
                    c2.q();
                    break;
            }
            switch (z ? Typography.dollar : '#') {
                case '#':
                    break;
                default:
                    c2.a(context);
                    int i4 = a + 49;
                    d = i4 % 128;
                    int i5 = i4 % 2;
                    break;
            }
            if (dVar instanceof f) {
                switch (c2 instanceof o.ez.d ? '*' : '/') {
                    case '/':
                        return;
                    default:
                        int i6 = d + 75;
                        a = i6 % 128;
                        if (i6 % 2 != 0) {
                            b = ((f) dVar).b();
                            dVar2 = (o.ez.d) c2;
                            int i7 = 31 / 0;
                            if (b == null) {
                                return;
                            }
                        } else {
                            b = ((f) dVar).b();
                            dVar2 = (o.ez.d) c2;
                            if (b == null) {
                                return;
                            }
                        }
                        if (dVar2.p() == 1) {
                            ((o.ez.d) c2).d(b.intValue());
                            g.c();
                            Object[] objArr11 = new Object[1];
                            f((char) (View.combineMeasuredStates(0, 0) + 3131), (Process.myTid() >> 22) + 941, (-16777163) - Color.rgb(0, 0, 0), objArr11);
                            g.d(intern, ((String) objArr11[0]).intern());
                            return;
                        }
                        return;
                }
            }
        }
    }

    public final void c() {
        int i = a + 45;
        d = i % 128;
        switch (i % 2 == 0 ? '\b' : (char) 31) {
            case 31:
                Iterator<e> it = this.b.iterator();
                int i2 = d + 61;
                a = i2 % 128;
                switch (i2 % 2 != 0) {
                }
                while (true) {
                    switch (it.hasNext() ? Typography.dollar : '`') {
                        case Opcodes.IADD /* 96 */:
                            return;
                        default:
                            it.next().m();
                    }
                }
            default:
                this.b.iterator();
                throw null;
        }
    }

    public final void e(o.dd.e eVar) {
        int i = d + 45;
        a = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 62516), MotionEvent.axisFromString("") + 68, Color.red(0) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (54258 - Color.blue(0)), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 1205, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 24, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Iterator<e> it = this.b.iterator();
        int i3 = a + 11;
        d = i3 % 128;
        switch (i3 % 2 == 0 ? 'N' : (char) 3) {
        }
        while (true) {
            switch (it.hasNext() ? 'H' : (char) 30) {
                case 'H':
                    int i4 = d + 49;
                    a = i4 % 128;
                    int i5 = i4 % 2;
                    it.next().a(eVar);
                default:
                    int i6 = d + 55;
                    a = i6 % 128;
                    int i7 = i6 % 2;
                    return;
            }
        }
    }

    public final void e() {
        int i = d + 89;
        a = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) (Color.alpha(0) + 62516), TextUtils.indexOf("", "", 0) + 67, (ViewConfiguration.getScrollBarSize() >> 8) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), View.MeasureSpec.getSize(0) + 1230, 37 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Iterator<e> it = this.b.iterator();
        int i3 = d + 63;
        a = i3 % 128;
        int i4 = i3 % 2;
        while (true) {
            switch (!it.hasNext()) {
                case false:
                    it.next().n();
                default:
                    return;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void e(o.dd.e r10, java.lang.String r11) {
        /*
            r9 = this;
            java.util.List<o.ey.e> r0 = r9.b
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.ex.d.a
            int r1 = r1 + 81
            int r2 = r1 % 128
            o.ex.d.d = r2
            int r1 = r1 % 2
        L11:
            boolean r1 = r0.hasNext()
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L1c
            r1 = r2
            goto L1d
        L1c:
            r1 = r3
        L1d:
            switch(r1) {
                case 0: goto L2d;
                default: goto L20;
            }
        L20:
            int r1 = o.ex.d.a
            int r1 = r1 + 27
            int r4 = r1 % 128
            o.ex.d.d = r4
            int r1 = r1 % 2
            if (r1 != 0) goto L38
            goto L38
        L2d:
            int r10 = o.ex.d.d
            int r10 = r10 + 123
            int r11 = r10 % 128
            o.ex.d.a = r11
            int r10 = r10 % 2
            return
        L38:
            java.lang.Object r1 = r0.next()
            o.ey.e r1 = (o.ey.e) r1
            java.lang.String r4 = r1.a()
            boolean r4 = r4.equals(r11)
            if (r4 == 0) goto L11
            o.ee.g.c()
            int r4 = android.view.ViewConfiguration.getMaximumDrawingCacheSize()
            int r4 = r4 >> 24
            r5 = 62516(0xf434, float:8.7604E-41)
            int r4 = r4 + r5
            char r4 = (char) r4
            java.lang.String r5 = ""
            int r5 = android.text.TextUtils.getCapsMode(r5, r3, r3)
            int r5 = 67 - r5
            int r6 = android.graphics.drawable.Drawable.resolveOpacity(r3, r3)
            int r6 = 17 - r6
            java.lang.Object[] r7 = new java.lang.Object[r2]
            f(r4, r5, r6, r7)
            r4 = r7[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            int r6 = android.view.View.MeasureSpec.getMode(r3)
            char r6 = (char) r6
            int r7 = android.graphics.Color.green(r3)
            int r7 = r7 + 1267
            int r8 = android.view.View.resolveSizeAndState(r3, r3, r3)
            int r8 = r8 + 31
            java.lang.Object[] r2 = new java.lang.Object[r2]
            f(r6, r7, r8, r2)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r2 = r5.append(r2)
            java.lang.String r3 = r1.d()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            o.ee.g.d(r4, r2)
            r1.c(r10)
            goto L11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.e(o.dd.e, java.lang.String):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void b() {
        /*
            r7 = this;
            int r0 = o.ex.d.d
            int r0 = r0 + 59
            int r1 = r0 % 128
            o.ex.d.a = r1
            int r0 = r0 % 2
            o.ee.g.c()
            r0 = 62516(0xf434, float:8.7604E-41)
            r1 = 0
            int r2 = android.graphics.Color.argb(r1, r1, r1, r1)
            int r0 = r0 - r2
            char r0 = (char) r0
            int r2 = android.view.ViewConfiguration.getPressedStateDuration()
            int r2 = r2 >> 16
            int r2 = r2 + 67
            int r3 = android.view.View.MeasureSpec.getMode(r1)
            int r3 = r3 + 17
            r4 = 1
            java.lang.Object[] r5 = new java.lang.Object[r4]
            f(r0, r2, r3, r5)
            r0 = r5[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            long r2 = android.os.SystemClock.elapsedRealtime()
            r5 = 0
            int r2 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            r3 = 60083(0xeab3, float:8.4194E-41)
            int r3 = r3 - r2
            char r2 = (char) r3
            int r3 = android.view.KeyEvent.getDeadChar(r1, r1)
            int r3 = r3 + 1298
            int r5 = android.view.ViewConfiguration.getTapTimeout()
            int r5 = r5 >> 16
            int r5 = 37 - r5
            java.lang.Object[] r4 = new java.lang.Object[r4]
            f(r2, r3, r5, r4)
            r2 = r4[r1]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            o.ee.g.d(r0, r2)
            java.util.List<o.ey.e> r0 = r7.b
            java.util.Iterator r0 = r0.iterator()
        L64:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L6d
            r2 = 46
            goto L6e
        L6d:
            r2 = r1
        L6e:
            switch(r2) {
                case 0: goto L7e;
                default: goto L71;
            }
        L71:
            int r2 = o.ex.d.a
            int r2 = r2 + 113
            int r3 = r2 % 128
            o.ex.d.d = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L84
            goto L84
        L7e:
            java.util.List<o.ey.e> r0 = r7.b
            r0.clear()
            return
        L84:
            java.lang.Object r2 = r0.next()
            o.ey.e r2 = (o.ey.e) r2
            r2.l()
            goto L64
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.b():void");
    }

    public final o.fc.e b(c cVar) {
        String g = cVar.g();
        e c2 = c(g);
        switch (c2 != null ? Typography.amp : '\\') {
            case '&':
                int i = a + Opcodes.DMUL;
                d = i % 128;
                int i2 = i % 2;
                o.fc.e k = c2.k();
                int i3 = d + 83;
                a = i3 % 128;
                int i4 = i3 % 2;
                return k;
            default:
                g.c();
                Object[] objArr = new Object[1];
                f((char) (62516 - KeyEvent.keyCodeFromString("")), Process.getGidForName("") + 68, 18 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                f((char) View.MeasureSpec.getSize(0), 1335 - View.MeasureSpec.getSize(0), 47 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr2);
                StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(g);
                Object[] objArr3 = new Object[1];
                f((char) Color.blue(0), 1381 - ExpandableListView.getPackedPositionType(0L), 21 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr3);
                g.e(intern, append.append(((String) objArr3[0]).intern()).toString());
                return null;
        }
    }

    public final short a(c cVar) {
        String e2;
        switch (!cVar.k()) {
            case false:
                int i = a + 29;
                d = i % 128;
                char c2 = i % 2 == 0 ? 'T' : (char) 17;
                String n = cVar.n();
                switch (c2) {
                    case Opcodes.BASTORE /* 84 */:
                        c.e(n);
                        throw null;
                    default:
                        e2 = c.e(n);
                        break;
                }
            default:
                e2 = cVar.n();
                int i2 = d + 19;
                a = i2 % 128;
                int i3 = i2 % 2;
                break;
        }
        e c3 = c(e2);
        if (c3 != null) {
            return c3.o();
        }
        g.c();
        Object[] objArr = new Object[1];
        f((char) (62516 - (ViewConfiguration.getPressedStateDuration() >> 16)), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 67, (ViewConfiguration.getTouchSlop() >> 8) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f((char) KeyEvent.keyCodeFromString(""), AndroidCharacter.getMirror('0') + 1354, 39 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(e2);
        Object[] objArr3 = new Object[1];
        f((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.getCapsMode("", 0, 0) + 1381, Gravity.getAbsoluteGravity(0, 0) + 21, objArr3);
        g.e(intern, append.append(((String) objArr3[0]).intern()).toString());
        return (short) 0;
    }

    public final boolean b(String str) {
        int i = d + Opcodes.LMUL;
        a = i % 128;
        int i2 = i % 2;
        e c2 = c(str);
        switch (c2 == null ? '9' : '\t') {
            case '9':
                g.c();
                Object[] objArr = new Object[1];
                f((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 62517), 68 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), View.MeasureSpec.makeMeasureSpec(0, 0) + 17, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((char) View.resolveSizeAndState(0, 0, 0), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 1440, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 68, objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                return false;
            default:
                switch (c2.j() == null) {
                    case true:
                        int i3 = a + 87;
                        d = i3 % 128;
                        int i4 = i3 % 2;
                        g.c();
                        Object[] objArr3 = new Object[1];
                        f((char) (KeyEvent.normalizeMetaState(0) + 62516), TextUtils.getOffsetAfter("", 0) + 67, KeyEvent.keyCodeFromString("") + 17, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 47272), 1585 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), Drawable.resolveOpacity(0, 0) + Opcodes.LREM, objArr4);
                        g.d(intern2, ((String) objArr4[0]).intern());
                        return false;
                    default:
                        return c2.c(c2.j().b());
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:38:0x0027, code lost:
    
        continue;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.Date d() {
        /*
            r6 = this;
            int r0 = o.ex.d.d
            int r0 = r0 + 69
            int r1 = r0 % 128
            o.ex.d.a = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 18
            goto L11
        Lf:
            r0 = 45
        L11:
            r1 = 0
            switch(r0) {
                case 18: goto L1e;
                default: goto L16;
            }
        L16:
            java.util.List<o.ey.e> r0 = r6.b
            java.util.Iterator r0 = r0.iterator()
            r2 = r1
            goto L27
        L1e:
            java.util.List<o.ey.e> r0 = r6.b
            r0.iterator()
            throw r1     // Catch: java.lang.Throwable -> L25
        L25:
            r0 = move-exception
            throw r0
        L27:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L8a
            int r3 = o.ex.d.a
            int r3 = r3 + 119
            int r4 = r3 % 128
            o.ex.d.d = r4
            int r3 = r3 % 2
            java.lang.Object r3 = r0.next()
            o.ey.e r3 = (o.ey.e) r3
            o.fj.c r3 = r3.j()
            if (r3 == 0) goto L89
            int r4 = o.ex.d.d
            int r4 = r4 + 3
            int r5 = r4 % 128
            o.ex.d.a = r5
            int r4 = r4 % 2
            java.util.Date r3 = r3.a()
            if (r3 == 0) goto L56
            r4 = 84
            goto L58
        L56:
            r4 = 51
        L58:
            switch(r4) {
                case 84: goto L5c;
                default: goto L5b;
            }
        L5b:
            goto L89
        L5c:
            int r4 = o.ex.d.a
            int r4 = r4 + 5
            int r5 = r4 % 128
            o.ex.d.d = r5
            int r4 = r4 % 2
            if (r4 == 0) goto L83
            if (r2 == 0) goto L76
            boolean r4 = r3.before(r2)
            if (r4 == 0) goto L72
            r4 = 1
            goto L73
        L72:
            r4 = 0
        L73:
            switch(r4) {
                case 0: goto L5b;
                default: goto L77;
            }
        L76:
        L77:
            int r2 = o.ex.d.a
            int r2 = r2 + 115
            int r4 = r2 % 128
            o.ex.d.d = r4
            int r2 = r2 % 2
            r2 = r3
            goto L89
        L83:
            r1.hashCode()     // Catch: java.lang.Throwable -> L87
            throw r1     // Catch: java.lang.Throwable -> L87
        L87:
            r0 = move-exception
            throw r0
        L89:
            goto L27
        L8a:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.d():java.util.Date");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private o.ey.e c(java.lang.String r4) {
        /*
            r3 = this;
            java.util.List<o.ey.e> r0 = r3.b
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.ex.d.d
            int r1 = r1 + 75
            int r2 = r1 % 128
            o.ex.d.a = r2
            int r1 = r1 % 2
        L11:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L1a
            r1 = 47
            goto L1c
        L1a:
            r1 = 46
        L1c:
            switch(r1) {
                case 47: goto L21;
                default: goto L1f;
            }
        L1f:
            r4 = 0
            return r4
        L21:
            java.lang.Object r1 = r0.next()
            o.ey.e r1 = (o.ey.e) r1
            java.lang.String r2 = r1.d()
            boolean r2 = r2.equals(r4)
            if (r2 == 0) goto L33
            r2 = 0
            goto L34
        L33:
            r2 = 1
        L34:
            switch(r2) {
                case 1: goto L38;
                default: goto L37;
            }
        L37:
            return r1
        L38:
            int r1 = o.ex.d.d
            int r1 = r1 + 85
            int r2 = r1 % 128
            o.ex.d.a = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L11
            goto L11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.c(java.lang.String):o.ey.e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 986
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ex.d.f(char, int, int, java.lang.Object[]):void");
    }
}
