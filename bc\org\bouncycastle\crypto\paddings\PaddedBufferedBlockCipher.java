package bc.org.bouncycastle.crypto.paddings;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.InvalidCipherTextException;
import bc.org.bouncycastle.crypto.a;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k6;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\paddings\PaddedBufferedBlockCipher.smali */
public class PaddedBufferedBlockCipher extends a {

    /* renamed from: o, reason: collision with root package name */
    h1 f9o;

    public PaddedBufferedBlockCipher(BlockCipher blockCipher, h1 h1Var) {
        this.k = blockCipher;
        this.f9o = h1Var;
        this.h = new byte[blockCipher.getBlockSize()];
        this.i = 0;
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int doFinal(byte[] bArr, int i) throws DataLengthException, IllegalStateException, InvalidCipherTextException {
        int i2;
        int blockSize = this.k.getBlockSize();
        if (this.j) {
            if (this.i != blockSize) {
                i2 = 0;
            } else {
                if ((blockSize * 2) + i > bArr.length) {
                    reset();
                    throw new g6("output buffer too short");
                }
                i2 = this.k.processBlock(this.h, 0, bArr, i);
                this.i = 0;
            }
            this.f9o.addPadding(this.h, this.i);
            return i2 + this.k.processBlock(this.h, 0, bArr, i + i2);
        }
        if (this.i != blockSize) {
            reset();
            throw new DataLengthException("last block incomplete in decryption");
        }
        BlockCipher blockCipher = this.k;
        byte[] bArr2 = this.h;
        int processBlock = blockCipher.processBlock(bArr2, 0, bArr2, 0);
        this.i = 0;
        try {
            int padCount = processBlock - this.f9o.padCount(this.h);
            System.arraycopy(this.h, 0, bArr, i, padCount);
            return padCount;
        } finally {
            reset();
        }
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int getOutputSize(int i) {
        int i2 = i + this.i;
        byte[] bArr = this.h;
        int length = i2 % bArr.length;
        return length == 0 ? this.j ? i2 + bArr.length : i2 : (i2 - length) + bArr.length;
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int getUpdateOutputSize(int i) {
        int i2 = i + this.i;
        byte[] bArr = this.h;
        int length = i2 % bArr.length;
        return length == 0 ? Math.max(0, i2 - bArr.length) : i2 - length;
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        this.j = z;
        reset();
        if (!(cipherParameters instanceof k6)) {
            this.f9o.init(null);
            this.k.init(z, cipherParameters);
        } else {
            k6 k6Var = (k6) cipherParameters;
            this.f9o.init(k6Var.b());
            this.k.init(z, k6Var.a());
        }
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int processByte(byte b, byte[] bArr, int i) throws DataLengthException, IllegalStateException {
        int i2 = this.i;
        byte[] bArr2 = this.h;
        int i3 = 0;
        if (i2 == bArr2.length) {
            int processBlock = this.k.processBlock(bArr2, 0, bArr, i);
            this.i = 0;
            i3 = processBlock;
        }
        byte[] bArr3 = this.h;
        int i4 = this.i;
        this.i = i4 + 1;
        bArr3[i4] = b;
        return i3;
    }

    @Override // bc.org.bouncycastle.crypto.a, bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int processBytes(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException, IllegalStateException {
        if (i2 < 0) {
            throw new IllegalArgumentException("Can't have a negative input length!");
        }
        int blockSize = getBlockSize();
        int updateOutputSize = getUpdateOutputSize(i2);
        if (updateOutputSize > 0 && updateOutputSize + i3 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        byte[] bArr3 = this.h;
        int length = bArr3.length;
        int i4 = this.i;
        int i5 = length - i4;
        int i6 = 0;
        if (i2 > i5) {
            System.arraycopy(bArr, i, bArr3, i4, i5);
            int processBlock = this.k.processBlock(this.h, 0, bArr2, i3) + 0;
            this.i = 0;
            i2 -= i5;
            i += i5;
            i6 = processBlock;
            while (i2 > this.h.length) {
                i6 += this.k.processBlock(bArr, i, bArr2, i3 + i6);
                i2 -= blockSize;
                i += blockSize;
            }
        }
        System.arraycopy(bArr, i, this.h, this.i, i2);
        this.i += i2;
        return i6;
    }

    public PaddedBufferedBlockCipher(BlockCipher blockCipher) {
        this(blockCipher, new PKCS7Padding());
    }
}
