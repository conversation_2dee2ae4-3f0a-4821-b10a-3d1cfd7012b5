package com.capacitorjs.plugins.localnotifications;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.graphics.Color;
import android.media.AudioAttributes;
import android.net.Uri;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.Logger;
import com.getcapacitor.PluginCall;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\capacitorjs\plugins\localnotifications\NotificationChannelManager.smali */
public class NotificationChannelManager {
    private Context context;
    private NotificationManager notificationManager;
    private static String CHANNEL_ID = "id";
    private static String CHANNEL_NAME = "name";
    private static String CHANNEL_DESCRIPTION = "description";
    private static String CHANNEL_IMPORTANCE = "importance";
    private static String CHANNEL_VISIBILITY = "visibility";
    private static String CHANNEL_SOUND = "sound";
    private static String CHANNEL_VIBRATE = "vibration";
    private static String CHANNEL_USE_LIGHTS = "lights";
    private static String CHANNEL_LIGHT_COLOR = "lightColor";

    public NotificationChannelManager(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService("notification");
    }

    public NotificationChannelManager(Context context, NotificationManager manager) {
        this.context = context;
        this.notificationManager = manager;
    }

    public void createChannel(PluginCall call) {
        JSObject channel = new JSObject();
        if (call.getString(CHANNEL_ID) != null) {
            String str = CHANNEL_ID;
            channel.put(str, call.getString(str));
            if (call.getString(CHANNEL_NAME) != null) {
                String str2 = CHANNEL_NAME;
                channel.put(str2, call.getString(str2));
                String str3 = CHANNEL_IMPORTANCE;
                channel.put(str3, (Object) call.getInt(str3, 3));
                String str4 = CHANNEL_DESCRIPTION;
                channel.put(str4, call.getString(str4, ""));
                String str5 = CHANNEL_VISIBILITY;
                channel.put(str5, (Object) call.getInt(str5, 1));
                String str6 = CHANNEL_SOUND;
                channel.put(str6, call.getString(str6, null));
                String str7 = CHANNEL_VIBRATE;
                channel.put(str7, (Object) call.getBoolean(str7, false));
                String str8 = CHANNEL_USE_LIGHTS;
                channel.put(str8, (Object) call.getBoolean(str8, false));
                String str9 = CHANNEL_LIGHT_COLOR;
                channel.put(str9, call.getString(str9, null));
                createChannel(channel);
                call.resolve();
                return;
            }
            call.reject("Channel missing name");
            return;
        }
        call.reject("Channel missing identifier");
    }

    public void createChannel(JSObject channel) {
        NotificationChannel notificationChannel = new NotificationChannel(channel.getString(CHANNEL_ID), channel.getString(CHANNEL_NAME), channel.getInteger(CHANNEL_IMPORTANCE).intValue());
        notificationChannel.setDescription(channel.getString(CHANNEL_DESCRIPTION));
        notificationChannel.setLockscreenVisibility(channel.getInteger(CHANNEL_VISIBILITY).intValue());
        notificationChannel.enableVibration(channel.getBool(CHANNEL_VIBRATE).booleanValue());
        notificationChannel.enableLights(channel.getBool(CHANNEL_USE_LIGHTS).booleanValue());
        String lightColor = channel.getString(CHANNEL_LIGHT_COLOR);
        if (lightColor != null) {
            try {
                notificationChannel.setLightColor(Color.parseColor(lightColor));
            } catch (IllegalArgumentException e) {
                Logger.error(Logger.tags("NotificationChannel"), "Invalid color provided for light color.", null);
            }
        }
        String sound = channel.getString(CHANNEL_SOUND, null);
        if (sound != null && !sound.isEmpty()) {
            if (sound.contains(".")) {
                sound = sound.substring(0, sound.lastIndexOf(46));
            }
            AudioAttributes audioAttributes = new AudioAttributes.Builder().setContentType(4).setUsage(5).build();
            Uri soundUri = Uri.parse("android.resource://" + this.context.getPackageName() + "/raw/" + sound);
            notificationChannel.setSound(soundUri, audioAttributes);
        }
        this.notificationManager.createNotificationChannel(notificationChannel);
    }

    public void deleteChannel(PluginCall call) {
        String channelId = call.getString("id");
        this.notificationManager.deleteNotificationChannel(channelId);
        call.resolve();
    }

    public void listChannels(PluginCall pluginCall) {
        List<NotificationChannel> notificationChannels = this.notificationManager.getNotificationChannels();
        JSArray channels = new JSArray();
        for (NotificationChannel notificationChannel : notificationChannels) {
            JSObject channel = new JSObject();
            channel.put(CHANNEL_ID, notificationChannel.getId());
            channel.put(CHANNEL_NAME, (Object) notificationChannel.getName());
            channel.put(CHANNEL_DESCRIPTION, notificationChannel.getDescription());
            channel.put(CHANNEL_IMPORTANCE, notificationChannel.getImportance());
            channel.put(CHANNEL_VISIBILITY, notificationChannel.getLockscreenVisibility());
            channel.put(CHANNEL_SOUND, (Object) notificationChannel.getSound());
            channel.put(CHANNEL_VIBRATE, notificationChannel.shouldVibrate());
            channel.put(CHANNEL_USE_LIGHTS, notificationChannel.shouldShowLights());
            channel.put(CHANNEL_LIGHT_COLOR, String.format("#%06X", Integer.valueOf(16777215 & notificationChannel.getLightColor())));
            Logger.debug(Logger.tags("NotificationChannel"), "visibility " + notificationChannel.getLockscreenVisibility());
            Logger.debug(Logger.tags("NotificationChannel"), "importance " + notificationChannel.getImportance());
            channels.put(channel);
        }
        JSObject result = new JSObject();
        result.put("channels", (Object) channels);
        pluginCall.resolve(result);
    }
}
