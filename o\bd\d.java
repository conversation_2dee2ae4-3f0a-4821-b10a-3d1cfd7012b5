package o.bd;

import android.content.Context;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Set;
import kotlin.text.Typography;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.dc.h;
import o.ee.g;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bd\d.smali */
public final class d extends o.y.b<a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static boolean c;
    private static boolean e;
    private static int f;
    private static int j;
    h d;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bd\d$a.smali */
    public interface a {
        void c();

        void e(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        j = 1;
        k();
        TextUtils.indexOf("", "");
        View.MeasureSpec.getMode(0);
        PointF.length(0.0f, 0.0f);
        int i = j + Opcodes.LSHR;
        f = i % 128;
        switch (i % 2 != 0 ? (char) 25 : 'O') {
            case Opcodes.IASTORE /* 79 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{96, 104, -93, 9};
        $$e = Opcodes.RETURN;
    }

    static void k() {
        a = new char[]{61637, 61672, 61665, 61639, 61670, 61673, 61660, 61663, 61658, 61652, 61671, 61674, 61626, 61664, 61657, 61597, 61596, 61589, 61600, 61638, 61675, 61662, 61592, 61669};
        c = true;
        e = true;
        b = 782102901;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r6 = r6 + 4
            int r8 = 121 - r8
            byte[] r0 = o.bd.d.$$d
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            goto L37
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            int r4 = r3 + 1
            if (r3 != r8) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L37:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bd.d.m(byte, byte, int, java.lang.Object[]):void");
    }

    public d(Context context, a aVar, c cVar) {
        super(context, aVar, cVar, e.j);
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        b bVar = new b(this);
        int i = f + Opcodes.LUSHR;
        j = i % 128;
        switch (i % 2 == 0 ? Typography.quote : (char) 0) {
            case '\"':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bVar;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i = f + 43;
        j = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        l(null, KeyEvent.getDeadChar(0, 0) + 127, null, "\u008f\u008b\u008a\u008e\u008e\u0085\u008d\u008c\u008b\u0085\u0087\u0086\u008a\u0089\u0087\u0088\u0087\u0086\u0085\u0084\u0083\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = j + 73;
        f = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    public final void b(h hVar) {
        int i = j + 49;
        f = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        l(null, 126 - TextUtils.lastIndexOf("", '0', 0), null, "\u008f\u008b\u008a\u008e\u008e\u0085\u008d\u008c\u008b\u0085\u0087\u0086\u008a\u0089\u0087\u0088\u0087\u0086\u0085\u0084\u0083\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(null, TextUtils.indexOf("", "", 0, 0) + 127, null, "\u008c\u0097\u0092\u008b\u0087\u0096\u0087\u0095\u0094\u008b\u0085\u0087\u0086\u008a\u0089\u0087\u0088\u0087\u0086\u0085\u008b\u0092\u0093\u0092\u0091\u0090\u008c\u008b\u0085\u0087\u0086\u008a\u0089\u0087\u0088\u0087\u0086\u0085\u0084\u0083\u0083\u0082\u0081\u0085\u008f", objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), hVar.name()));
        this.d = hVar;
        c();
        int i3 = j + 35;
        f = i3 % 128;
        switch (i3 % 2 != 0 ? '*' : 'Z') {
            case 'Z':
                return;
            default:
                throw null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bd\d$b.smali */
    public static final class b extends o.y.c<d> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static char a;
        private static char b;
        private static char c;
        private static char d;
        private static int h;
        private static long i;
        private static int j;
        private Set<o.dc.d> e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            j = 1;
            a = (char) 41553;
            d = (char) 37967;
            b = (char) 53869;
            c = (char) 35258;
            i = -7394156192501338255L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0030  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0028  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0030 -> B:4:0x003a). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(int r7, short r8, short r9, java.lang.Object[] r10) {
            /*
                int r8 = r8 * 4
                int r8 = 1 - r8
                int r7 = r7 * 2
                int r7 = 3 - r7
                int r9 = r9 * 3
                int r9 = r9 + 68
                byte[] r0 = o.bd.d.b.$$d
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L1b
                r9 = r8
                r3 = r1
                r4 = r2
                r8 = r7
                r1 = r0
                r0 = r10
                r10 = r9
                goto L3a
            L1b:
                r3 = r2
                r6 = r9
                r9 = r8
                r8 = r6
            L1f:
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                int r7 = r7 + 1
                if (r4 != r9) goto L30
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L30:
                r3 = r0[r7]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r6
            L3a:
                int r7 = r7 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r6 = r8
                r8 = r7
                r7 = r6
                goto L1f
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bd.d.b.C(int, short, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{38, -75, -91, -62};
            $$e = 79;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i2 = h + 79;
            j = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    int i3 = 10 / 0;
                    break;
            }
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i2 = j + Opcodes.LREM;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    o.cf.d b2 = b(context);
                    int i3 = h + 31;
                    j = i3 % 128;
                    switch (i3 % 2 == 0 ? '\f' : '5') {
                        case Opcodes.SALOAD /* 53 */:
                            return b2;
                        default:
                            int i4 = 90 / 0;
                            return b2;
                    }
                default:
                    b(context);
                    throw null;
            }
        }

        b(d dVar) {
            super(dVar, false);
            this.e = null;
        }

        @Override // o.y.c
        public final String l() {
            int i2 = j + 15;
            h = i2 % 128;
            int i3 = i2 % 2;
            Object[] objArr = new Object[1];
            w("\uf372ꊺ\ua97d難ꤑ媐釬觉嬙轭\ue65e䰫釬觉喇\uef06ꎿ棷", 18 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            int i4 = h + Opcodes.LSHL;
            j = i4 % 128;
            int i5 = i4 % 2;
            return intern;
        }

        private static o.cf.d b(Context context) {
            Object[] objArr = new Object[1];
            w("冪䤂\udff3犷▄蜰᧚삙ꏴ\uf566࠘䄕潕躈豛埘逑\uf28d颕䑽", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 18, objArr);
            o.cf.d dVar = new o.cf.d(context, 25, ((String) objArr[0]).intern());
            int i2 = h + 65;
            j = i2 % 128;
            switch (i2 % 2 == 0 ? 'Z' : 'V') {
                case Opcodes.SASTORE /* 86 */:
                    return dVar;
                default:
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            /*
                Method dump skipped, instructions count: 330
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bd.d.b.m():o.eg.b");
        }

        @Override // o.y.c
        public final j n() {
            int i2 = h;
            int i3 = i2 + 99;
            j = i3 % 128;
            int i4 = i3 % 2;
            int i5 = i2 + 43;
            j = i5 % 128;
            int i6 = i5 % 2;
            return null;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i2 = j;
            int i3 = i2 + 29;
            h = i3 % 128;
            int i4 = i3 % 2;
            int i5 = i2 + 79;
            h = i5 % 128;
            Object obj = null;
            switch (i5 % 2 == 0) {
                case false:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:16:0x00bd, code lost:
        
            o.ee.g.c();
            r4 = new java.lang.Object[1];
            B("\ue5c2\ue5b2ഢ혒ꋭ号褼嬟쳁靛鄣飜岎込喒킞\ud98f䥘ዌ\u17ee\u1f47ࠐ퍰唋尴쯘醭鐄鶻蔳庽\udbf4튙䐴Ἢ\u1ae8ၫ߭\udc47塤凖욢骛齸雒聑寉\ude94푏䌃᠈ᰕᔐ˚웠ꍀ䫻ﶟ蟭", (android.widget.ExpandableListView.getPackedPositionForGroup(0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForGroup(0) == 0 ? 0 : -1)), r4);
            o.ee.g.d(r5, ((java.lang.String) r4[0]).intern());
            java.util.Collections.sort(r6, new o.bd.d.b.AnonymousClass4(r11));
            o.ee.g.c();
            r3 = new java.lang.StringBuilder();
            r9 = new java.lang.Object[1];
            B("謱譁뻌쫸ᄃ⫫闖㗬缯讱\ue83f\uf62f▒㱒䥸ꦂ라襁ฦ滲熴믾쾚Ⱇ㋇砶赇\ued18\uf348㛝䉗ꋨ", android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16, r9);
            r12 = r3.append(((java.lang.String) r9[0]).intern()).append(r12.d());
            r4 = new java.lang.Object[1];
            B("뛥뛅랇芑ᡇ\ue050\udda2࠴癇쏟⊏쯴\uef2e㔭ć挻誣\uf3e2䘆ꑽ䱣늽蟻\ue6b6ཝ", android.text.TextUtils.indexOf("", "", 0, 0), r4);
            r12 = r12.append(((java.lang.String) r4[0]).intern()).append(((o.bd.d) e()).d.name());
            r4 = new java.lang.Object[1];
            w("\udf95\uf5c9\uf575\uf870荩⠡獧竑", 7 - (android.os.Process.myTid() >> 22), r4);
            o.ee.g.d(r5, r12.append(((java.lang.String) r4[0]).intern()).toString());
            r12 = r6.iterator();
         */
        /* JADX WARN: Code restructure failed: missing block: B:18:0x015b, code lost:
        
            if (r12.hasNext() == false) goto L18;
         */
        /* JADX WARN: Code restructure failed: missing block: B:19:0x015d, code lost:
        
            r0 = '\'';
         */
        /* JADX WARN: Code restructure failed: missing block: B:20:0x0161, code lost:
        
            switch(r0) {
                case 39: goto L21;
                default: goto L46;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:21:0x0165, code lost:
        
            o.dc.a.c(g(), (o.eg.b) r12.next());
         */
        /* JADX WARN: Code restructure failed: missing block: B:24:0x0173, code lost:
        
            r12 = o.bd.d.b.h + 37;
            o.bd.d.b.j = r12 % 128;
         */
        /* JADX WARN: Code restructure failed: missing block: B:25:0x017d, code lost:
        
            if ((r12 % 2) != 0) goto L30;
         */
        /* JADX WARN: Code restructure failed: missing block: B:28:0x0181, code lost:
        
            r12 = 76 / 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:29:0x0182, code lost:
        
            return;
         */
        /* JADX WARN: Code restructure failed: missing block: B:34:0x0185, code lost:
        
            return;
         */
        /* JADX WARN: Code restructure failed: missing block: B:35:0x0160, code lost:
        
            r0 = 'c';
         */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void c(o.eg.b r12) throws o.eg.d {
            /*
                Method dump skipped, instructions count: 528
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bd.d.b.c(o.eg.b):void");
        }

        @Override // o.y.c
        public final void q() {
            int i2 = h + 51;
            int i3 = i2 % 128;
            j = i3;
            int i4 = i2 % 2;
            switch (this.e != null) {
                case false:
                    break;
                default:
                    int i5 = i3 + 61;
                    h = i5 % 128;
                    boolean z = i5 % 2 != 0;
                    o.dc.c.e(g(), this.e);
                    switch (z) {
                        case false:
                            break;
                        default:
                            int i6 = 17 / 0;
                            break;
                    }
            }
            i().b(g());
            int i7 = j + 57;
            h = i7 % 128;
            int i8 = i7 % 2;
        }

        @Override // o.y.c
        public final void t() {
            int i2 = j + 37;
            h = i2 % 128;
            int i3 = i2 % 2;
            i().b(g());
            int i4 = h + 93;
            j = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i2 = j + 21;
            h = i2 % 128;
            int i3 = i2 % 2;
            ((d) e()).j().c();
            int i4 = h + Opcodes.LSHL;
            j = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i2 = j + 1;
            h = i2 % 128;
            int i3 = i2 % 2;
            ((d) e()).j().e(dVar);
            int i4 = h + 51;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    return;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0011. Please report as an issue. */
        @Override // o.y.c
        public final boolean r() {
            int i2 = h;
            int i3 = i2 + 53;
            j = i3 % 128;
            switch (i3 % 2 == 0) {
            }
            int i4 = i2 + Opcodes.LREM;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return false;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Failed to find 'out' block for switch in B:11:0x0031. Please report as an issue. */
        private static void w(java.lang.String r25, int r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 612
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bd.d.b.w(java.lang.String, int, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
        /* JADX WARN: Type inference failed for: r13v1 */
        /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
        private static void B(java.lang.String r13, int r14, java.lang.Object[] r15) {
            /*
                Method dump skipped, instructions count: 354
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bd.d.b.B(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r20, int r21, int[] r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 790
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bd.d.l(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
