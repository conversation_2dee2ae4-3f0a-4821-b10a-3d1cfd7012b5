package com.rolster.capacitor.update;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\update\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\update\R$id.smali */
    public static final class id {
        public static int webview = 0x7f0a016f;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\update\R$layout.smali */
    public static final class layout {
        public static int bridge_layout_main = 0x7f0d0025;

        private layout() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\update\R$string.smali */
    public static final class string {
        public static int my_string = 0x7f120129;

        private string() {
        }
    }

    private R() {
    }
}
