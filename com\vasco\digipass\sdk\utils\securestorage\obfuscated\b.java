package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import androidx.biometric.BiometricPrompt;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\b.smali */
public final class b extends BiometricPrompt.AuthenticationCallback {
    public final d a;
    public final SecretKey b;
    public final byte[] c;

    public b(d biometricSigningCompletedCallback, <PERSON><PERSON>ey biometricSigningKey, byte[] storageEncryptionKey) {
        Intrinsics.checkNotNullParameter(biometricSigningCompletedCallback, "biometricSigningCompletedCallback");
        Intrinsics.checkNotNullParameter(biometricSigningKey, "biometricSigningKey");
        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
        this.a = biometricSigningCompletedCallback;
        this.b = biometricSigningKey;
        this.c = storageEncryptionKey;
    }

    @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
    public final void onAuthenticationError(int i, CharSequence errString) {
        Intrinsics.checkNotNullParameter(errString, "errString");
        super.onAuthenticationError(i, errString);
        this.a.a(new SecureStorageSDKException(SecureStorageSDKErrorCodes.BIOMETRIC_AUTHENTICATION_ERROR, null, 2, null));
    }

    @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
    public final void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult result) {
        Intrinsics.checkNotNullParameter(result, "result");
        super.onAuthenticationSucceeded(result);
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(this.b);
        byte[] signedStorageEncryptionKey = mac.doFinal(this.c);
        d dVar = this.a;
        Intrinsics.checkNotNullExpressionValue(signedStorageEncryptionKey, "signedStorageEncryptionKey");
        dVar.a(signedStorageEncryptionKey);
    }
}
