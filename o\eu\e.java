package o.eu;

import com.esotericsoftware.asm.Opcodes;
import o.ey.a;
import o.fc.c;
import o.fg.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eu\e.smali */
public final class e extends o.ey.d<a, d> {
    private static int b = 0;
    private static int e = 1;

    @Override // o.ey.a
    public final /* synthetic */ o.fc.d b(c cVar, short s) {
        int i = b + Opcodes.LSHR;
        e = i % 128;
        int i2 = i % 2;
        a c = c(false, cVar, s);
        int i3 = b;
        int i4 = ((i3 | 47) << 1) - (i3 ^ 47);
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return c;
            default:
                throw null;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ o.ey.e e(String str, String str2, boolean z) {
        int i = b + 59;
        e = i % 128;
        int i2 = i % 2;
        d c = c(str, str2, z);
        int i3 = b;
        int i4 = ((i3 | 29) << 1) - (i3 ^ 29);
        e = i4 % 128;
        int i5 = i4 % 2;
        return c;
    }

    @Override // o.ey.a
    public final a.d d() {
        int i = (b + 90) - 1;
        e = i % 128;
        int i2 = i % 2;
        a.d dVar = a.d.a;
        int i3 = b;
        int i4 = (i3 & 51) + (i3 | 51);
        e = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return dVar;
            default:
                throw null;
        }
    }

    private static d c(String str, String str2, boolean z) {
        d dVar = new d(str, str2, z);
        int i = e + 17;
        b = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    private static o.fg.a c(boolean z, c cVar, short s) {
        o.fg.a aVar = new o.fg.a(false, cVar, s);
        int i = e;
        int i2 = (i & 97) + (i | 97);
        b = i2 % 128;
        int i3 = i2 % 2;
        return aVar;
    }
}
