package o.fc;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.b;
import o.a.j;
import o.e.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fc\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c b;
    public static final c c;
    public static final c d;
    public static final c e;
    private static boolean f;
    private static int h;
    private static char[] i;
    private static final /* synthetic */ c[] j;
    private static int k;
    private static boolean l;
    private static int m;
    private static char[] n;

    /* renamed from: o, reason: collision with root package name */
    private static long f84o;
    private final String g;

    static void b() {
        i = new char[]{61794, 61809, 61577, 61821, 61813, 61820, 61574, 61746, 61823, 61575, 61573, 61815, 61571, 61808, 61568, 61822, 61572, 61814, 61768, 61797, 61777, 61817, 61782, 61783, 61799, 61579};
        f = true;
        l = true;
        h = 782102802;
        n = new char[]{50861, 15050, 15957, 13279, 14157, 10448, 11343, 8669, 9563, 9968, 6761, 8186, 4973, 8846, 57065, 55926, 55292, 54126, 52467, 51308, 50683, 49522, 49876, 65088, 64462, 63321, 61651, 60502, 59867, 58547, 6345, 7259, 4566, 5449, 2761, 3649, 972, 1874, 1275, 14461, 15846, 12666, 14063, 10877, 12273, 9081, 8329, 9227, 22929, 23829, 21135, 22045, 19345, 11420, 53446, 54356, 55769, 56698, 49866, 50767, 52185, 53064, 52470, 61541, 62961, 63868, 65270, 57970, 59339, 60264, 59548, 60445, 37272, 38173, 11420, 53478, 54388, 55801, 56678, 49894, 50798, 52195, 53117, 52436, 61522, 62921, 63829, 65216, 57938, 59358, 60246, 59571, 60464, 37300, 38197, 39584, 40485, 37949, 26695, 27861, 24920, 26055, 31303, 32463, 29506, 30684, 29813, 18675, 19816, 16884, 18017, 23283, 24447, 21495, 20481, 21662, 10519, 11670, 8715, 9879, 15106};
        f84o = 3530581672910180533L;
    }

    static void init$0() {
        $$a = new byte[]{94, -116, 51, -9};
        $$b = Opcodes.LRETURN;
    }

    private static void r(int i2, byte b2, int i3, Object[] objArr) {
        int i4 = i2 + 102;
        int i5 = 1 - (i3 * 4);
        int i6 = b2 + 4;
        byte[] bArr = $$a;
        byte[] bArr2 = new byte[i5];
        int i7 = -1;
        int i8 = i5 - 1;
        if (bArr == null) {
            i4 += i6;
            i6 = i6;
            i8 = i8;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = -1;
        }
        while (true) {
            int i9 = i7 + 1;
            bArr2[i9] = (byte) i4;
            int i10 = i6 + 1;
            if (i9 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i4 += bArr[i10];
            i6 = i10;
            i8 = i8;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = i9;
        }
    }

    private static /* synthetic */ c[] e() {
        int i2 = k + 73;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                c[] cVarArr = new c[3];
                cVarArr[1] = b;
                cVarArr[0] = a;
                cVarArr[5] = c;
                cVarArr[3] = e;
                cVarArr[3] = d;
                return cVarArr;
            default:
                return new c[]{b, a, c, e, d};
        }
    }

    public static c valueOf(String str) {
        int i2 = k + 19;
        m = i2 % 128;
        char c2 = i2 % 2 != 0 ? Typography.greater : '\t';
        c cVar = (c) Enum.valueOf(c.class, str);
        switch (c2) {
            default:
                int i3 = 71 / 0;
            case '\t':
                return cVar;
        }
    }

    public static c[] values() {
        int i2 = m + 67;
        k = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 22 : (char) 27) {
            case 22:
                throw null;
            default:
                c[] cVarArr = (c[]) j.clone();
                int i3 = k + 85;
                m = i3 % 128;
                int i4 = i3 % 2;
                return cVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        k = 1;
        b();
        Object[] objArr = new Object[1];
        p(null, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 127, null, "\u0085\u0091\u0096\u0087\u008c\u0095\u0092\u0085\u008a\u008b\u0086\u0094", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) (59954 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), ViewConfiguration.getScrollBarFadeDuration() >> 16, 13 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr2);
        b = new c(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        p(null, Color.red(0) + 127, null, "\u0092\u0085\u0092\u008f\u0082\u008c\u008a\u0096\u0097\u0092\u0085\u008a\u008b\u0086\u0094", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        q((char) (3602 - ExpandableListView.getPackedPositionGroup(0L)), (ViewConfiguration.getWindowTouchSlop() >> 8) + 13, 17 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr4);
        a = new c(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        p(null, 128 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), null, "\u008a\u008a\u0085\u008c\u008c\u008b\u0099\u008a\u008a\u0085\u0090\u0087\u008c\u0082\u0087\u0086\u008d\u0098\u0092\u0085\u008a\u0094", objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        q((char) (51295 - AndroidCharacter.getMirror('0')), 30 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 23, objArr6);
        c = new c(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        q((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), View.MeasureSpec.getMode(0) + 53, 21 - (ViewConfiguration.getTouchSlop() >> 8), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        q((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), 74 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 24 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr8);
        e = new c(intern4, 3, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        p(null, (ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u0086\u009a\u008d\u0086\u0089\u0086\u0094\u008a\u008a\u0085\u0090\u0087\u008c\u0082\u0087\u0086\u008d\u0098\u0092\u0085\u008a\u0094", objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        q((char) (TextUtils.getCapsMode("", 0, 0) + 47265), TextUtils.indexOf("", "") + 97, 23 - MotionEvent.axisFromString(""), objArr10);
        d = new c(intern5, 4, ((String) objArr10[0]).intern());
        j = e();
        int i2 = k + 17;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    private c(String str, int i2, String str2) {
        this.g = str2;
    }

    public final String c() {
        int i2 = k;
        int i3 = i2 + 71;
        m = i3 % 128;
        Object obj = null;
        switch (i3 % 2 != 0 ? 'V' : (char) 30) {
            case 30:
                String str = this.g;
                int i4 = i2 + Opcodes.LSUB;
                m = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return str;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0060  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static o.fc.c a(java.lang.String r8) {
        /*
            int r0 = o.fc.c.k
            r1 = 1
            int r0 = r0 + r1
            int r2 = r0 % 128
            o.fc.c.m = r2
            int r0 = r0 % 2
            r2 = 0
            if (r0 == 0) goto L14
            o.fc.c[] r0 = values()
            int r3 = r0.length
            r4 = r1
            goto L1a
        L14:
            o.fc.c[] r0 = values()
            int r3 = r0.length
            r4 = r2
        L1a:
        L1b:
            if (r4 >= r3) goto L68
            int r5 = o.fc.c.m
            int r5 = r5 + 25
            int r6 = r5 % 128
            o.fc.c.k = r6
            int r5 = r5 % 2
            if (r5 != 0) goto L3e
            r5 = r0[r4]
            java.lang.String r6 = r5.g
            boolean r6 = r8.equals(r6)
            r7 = 4
            int r7 = r7 / r2
            if (r6 == 0) goto L37
            r6 = r2
            goto L38
        L37:
            r6 = r1
        L38:
            switch(r6) {
                case 0: goto L50;
                default: goto L3b;
            }
        L3b:
            goto L51
        L3c:
            r8 = move-exception
            throw r8
        L3e:
            r5 = r0[r4]
            java.lang.String r6 = r5.g
            boolean r6 = r8.equals(r6)
            if (r6 == 0) goto L4b
            r6 = 87
            goto L4d
        L4b:
            r6 = 71
        L4d:
            switch(r6) {
                case 71: goto L51;
                default: goto L50;
            }
        L50:
            goto L54
        L51:
            int r4 = r4 + 1
            goto L1b
        L54:
            int r8 = o.fc.c.m
            int r8 = r8 + 15
            int r0 = r8 % 128
            o.fc.c.k = r0
            int r8 = r8 % 2
            if (r8 != 0) goto L66
            r8 = 83
            int r8 = r8 / r2
            goto L66
        L64:
            r8 = move-exception
            throw r8
        L66:
            return r5
        L68:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            int r4 = android.graphics.Color.alpha(r2)
            int r4 = r4 + 127
            java.lang.Object[] r1 = new java.lang.Object[r1]
            r5 = 0
            java.lang.String r6 = "\u0088\u0093\u0088\u0092\u0085\u0091\u0090\u008d\u008a\u0085\u008f\u0088\u0085\u008e\u0088\u0087\u008d\u0086\u0086\u0082\u008c\u0088\u008a\u008b\u0087\u0082\u0087\u008a\u0088\u0083\u0085\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            p(r5, r4, r5, r6, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r1 = r3.append(r1)
            java.lang.StringBuilder r8 = r1.append(r8)
            java.lang.String r8 = r8.toString()
            r0.<init>(r8)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fc.c.a(java.lang.String):o.fc.c");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v21, types: [byte[]] */
    private static void p(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        int length;
        char[] cArr2;
        ?? r1 = str2;
        switch (r1 != 0 ? '/' : ')') {
            case '/':
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        switch (str != null ? 'a' : (char) 27) {
            case 27:
                cArr = str;
                break;
            default:
                int i3 = $11 + Opcodes.DMUL;
                $10 = i3 % 128;
                switch (i3 % 2 != 0 ? 'E' : 'b') {
                    case Opcodes.FADD /* 98 */:
                        cArr = str.toCharArray();
                        break;
                    default:
                        str.toCharArray();
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
        char[] cArr3 = cArr;
        j jVar = new j();
        char[] cArr4 = i;
        long j2 = 0;
        int i4 = -1;
        if (cArr4 != null) {
            int i5 = $10 + 11;
            $11 = i5 % 128;
            switch (i5 % 2 != 0) {
                case false:
                    length = cArr4.length;
                    cArr2 = new char[length];
                    break;
                default:
                    length = cArr4.length;
                    cArr2 = new char[length];
                    break;
            }
            int i6 = 0;
            while (i6 < length) {
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr4[i6])};
                    Object obj2 = a.s.get(1085633688);
                    if (obj2 == null) {
                        Class cls = (Class) a.c((Process.myPid() >> 22) + 11, (char) (ExpandableListView.getPackedPositionForGroup(0) > j2 ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == j2 ? 0 : -1)), 541 - AndroidCharacter.getMirror('0'));
                        byte b2 = (byte) i4;
                        Object[] objArr3 = new Object[1];
                        r((byte) 19, b2, (byte) (b2 + 1), objArr3);
                        obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        a.s.put(1085633688, obj2);
                    }
                    cArr2[i6] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                    i6++;
                    j2 = 0;
                    i4 = -1;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr4 = cArr2;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(h)};
            Object obj3 = a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls2 = (Class) a.c(Drawable.resolveOpacity(0, 0) + 10, (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 8855), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 323);
                byte b3 = (byte) (-1);
                Object[] objArr5 = new Object[1];
                r((byte) 16, b3, (byte) (b3 + 1), objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
            int i7 = 15;
            switch (l ? (char) 14 : ';') {
                case 14:
                    jVar.e = bArr.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr4[bArr[(jVar.e - 1) - jVar.c] + i2] - intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj4 = a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls3 = (Class) a.c((ViewConfiguration.getEdgeSlop() >> 16) + 10, (char) (ViewConfiguration.getTouchSlop() >> 8), TextUtils.getOffsetAfter("", 0) + 207);
                                byte b4 = (byte) (-1);
                                Object[] objArr7 = new Object[1];
                                r((byte) 15, b4, (byte) (b4 + 1), objArr7);
                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
                default:
                    switch (f ? '@' : '-') {
                        case '-':
                            jVar.e = iArr.length;
                            char[] cArr6 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                int i8 = $11 + Opcodes.DMUL;
                                $10 = i8 % 128;
                                int i9 = i8 % 2;
                                cArr6[jVar.c] = (char) (cArr4[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                jVar.c++;
                            }
                            objArr[0] = new String(cArr6);
                            return;
                        default:
                            int i10 = $11 + Opcodes.LSUB;
                            $10 = i10 % 128;
                            int i11 = i10 % 2;
                            jVar.e = cArr3.length;
                            char[] cArr7 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                cArr7[jVar.c] = (char) (cArr4[cArr3[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                try {
                                    Object[] objArr8 = {jVar, jVar};
                                    Object obj5 = a.s.get(745816316);
                                    if (obj5 == null) {
                                        Class cls4 = (Class) a.c(Color.alpha(0) + 10, (char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 206 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)));
                                        byte b5 = (byte) (-1);
                                        Object[] objArr9 = new Object[1];
                                        r((byte) i7, b5, (byte) (b5 + 1), objArr9);
                                        obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                        a.s.put(745816316, obj5);
                                    }
                                    ((Method) obj5).invoke(null, objArr8);
                                    i7 = 15;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            objArr[0] = new String(cArr7);
                            return;
                    }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    private static void q(char c2, int i2, int i3, Object[] objArr) {
        b bVar = new b();
        long[] jArr = new long[i3];
        bVar.c = 0;
        while (bVar.c < i3) {
            int i4 = bVar.c;
            try {
                Object[] objArr2 = {Integer.valueOf(n[i2 + bVar.c])};
                Object obj = a.s.get(-1667701760);
                if (obj == null) {
                    Class cls = (Class) a.c(Color.rgb(0, 0, 0) + 16777226, (char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 8856), 324 - (Process.myTid() >> 22));
                    byte b2 = (byte) 3;
                    byte b3 = (byte) (b2 - 4);
                    Object[] objArr3 = new Object[1];
                    r(b2, b3, (byte) (b3 + 1), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                    a.s.put(-1667701760, obj);
                }
                try {
                    Object[] objArr4 = {Long.valueOf(((Long) ((Method) obj).invoke(null, objArr2)).longValue()), Long.valueOf(bVar.c), Long.valueOf(f84o), Integer.valueOf(c2)};
                    Object obj2 = a.s.get(-1772233796);
                    if (obj2 == null) {
                        Class cls2 = (Class) a.c(19 - Color.argb(0, 0, 0, 0), (char) (15094 - ExpandableListView.getPackedPositionChild(0L)), 188 - (ViewConfiguration.getKeyRepeatTimeout() >> 16));
                        byte b4 = (byte) 2;
                        byte b5 = (byte) (b4 - 3);
                        Object[] objArr5 = new Object[1];
                        r(b4, b5, (byte) (b5 + 1), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Long.TYPE, Long.TYPE, Long.TYPE, Integer.TYPE);
                        a.s.put(-1772233796, obj2);
                    }
                    jArr[i4] = ((Long) ((Method) obj2).invoke(null, objArr4)).longValue();
                    try {
                        Object[] objArr6 = {bVar, bVar};
                        Object obj3 = a.s.get(128511541);
                        if (obj3 == null) {
                            Class cls3 = (Class) a.c(11 - (Process.myPid() >> 22), (char) (46213 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 437 - Color.blue(0));
                            byte b6 = (byte) 0;
                            byte b7 = (byte) (b6 - 1);
                            Object[] objArr7 = new Object[1];
                            r(b6, b7, (byte) (b7 + 1), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            a.s.put(128511541, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            } catch (Throwable th3) {
                Throwable cause3 = th3.getCause();
                if (cause3 == null) {
                    throw th3;
                }
                throw cause3;
            }
        }
        char[] cArr = new char[i3];
        bVar.c = 0;
        while (true) {
            if (bVar.c >= i3) {
                String str = new String(cArr);
                int i5 = $11 + 31;
                $10 = i5 % 128;
                switch (i5 % 2 == 0 ? ';' : (char) 21) {
                    case ';':
                        objArr[0] = str;
                        return;
                    default:
                        Object obj4 = null;
                        obj4.hashCode();
                        throw null;
                }
            }
            int i6 = $10 + 21;
            $11 = i6 % 128;
            switch (i6 % 2 == 0 ? 'D' : '9') {
                case '9':
                    cArr[bVar.c] = (char) jArr[bVar.c];
                    try {
                        Object[] objArr8 = {bVar, bVar};
                        Object obj5 = a.s.get(128511541);
                        if (obj5 == null) {
                            Class cls4 = (Class) a.c(11 - View.MeasureSpec.getSize(0), (char) (46212 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (ViewConfiguration.getEdgeSlop() >> 16) + 437);
                            byte b8 = (byte) 0;
                            byte b9 = (byte) (b8 - 1);
                            Object[] objArr9 = new Object[1];
                            r(b8, b9, (byte) (b9 + 1), objArr9);
                            obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            a.s.put(128511541, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr8);
                        break;
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                default:
                    cArr[bVar.c] = (char) jArr[bVar.c];
                    try {
                        Object[] objArr10 = {bVar, bVar};
                        Object obj6 = a.s.get(128511541);
                        if (obj6 == null) {
                            Class cls5 = (Class) a.c((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 10, (char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 46211), 437 - ExpandableListView.getPackedPositionGroup(0L));
                            byte b10 = (byte) 0;
                            byte b11 = (byte) (b10 - 1);
                            Object[] objArr11 = new Object[1];
                            r(b10, b11, (byte) (b11 + 1), objArr11);
                            obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                            a.s.put(128511541, obj6);
                        }
                        ((Method) obj6).invoke(null, objArr10);
                        int i7 = 22 / 0;
                        break;
                    } catch (Throwable th5) {
                        Throwable cause5 = th5.getCause();
                        if (cause5 == null) {
                            throw th5;
                        }
                        throw cause5;
                    }
            }
        }
    }
}
