package o.ay;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.UUID;
import kotlin.text.Typography;
import o.av.b;
import o.cf.i;
import o.de.f;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ay\c.smali */
public final class c extends o.av.b {
    private static char a;
    private static char c;
    private static char e;
    private static char h;
    private static int i;
    String d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int g = 1;

    static {
        i = 0;
        n();
        TextUtils.lastIndexOf("", '0', 0, 0);
        Color.green(0);
        int i2 = g + Opcodes.LREM;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 27 : '+') {
            case 27:
                throw null;
            default:
                return;
        }
    }

    static void n() {
        a = (char) 46472;
        c = (char) 62609;
        h = (char) 1794;
        e = (char) 53564;
    }

    public c(Context context, b.InterfaceC0029b interfaceC0029b, o.ei.c cVar, f fVar) {
        super(context, interfaceC0029b, cVar, fVar);
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i2 = g + 71;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                r("⮛웲嗾諭㌸禑畬漐돖䨨锅蜞妺讥\ue4f1㖇槆\u2fda躇愬ᾇ䘧", Drawable.resolveOpacity(0, 0) + 21, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                r("⮛웲嗾諭㌸禑畬漐돖䨨锅蜞妺讥\ue4f1㖇槆\u2fda躇愬ᾇ䘧", 42 >>> Drawable.resolveOpacity(0, 1), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        new a(e());
        String a2 = a.a(e());
        if (a2 != null) {
            this.d = a2;
            d dVar = new d(this);
            int i2 = i + 85;
            g = i2 % 128;
            int i3 = i2 % 2;
            return dVar;
        }
        d().a().e(e(), g(), f.m);
        return new b(this);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ay\c$b.smali */
    public static final class b extends o.y.a<c> {
        private static int c = 0;
        private static int d = 1;

        @Override // o.y.a
        public final void j() {
            int i = c;
            int i2 = ((i | 35) << 1) - (i ^ 35);
            d = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    return;
                default:
                    throw null;
            }
        }

        b(c cVar) {
            super(cVar, false);
        }

        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 93;
            c = i % 128;
            int i2 = i % 2;
            e().j().b(dVar, i());
            int i3 = (d + 68) - 1;
            c = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = c;
            int i2 = ((i | 47) << 1) - (i ^ 47);
            d = i2 % 128;
            int i3 = i2 % 2;
            e().j().b(dVar, i());
            int i4 = d + Opcodes.LSHR;
            c = i4 % 128;
            int i5 = i4 % 2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ay\c$d.smali */
    public static final class d extends b.a<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static long a;
        private static char[] b;
        private static int c;
        private static long d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            c = 0;
            e = 1;
            b = new char[]{21645, 37560, 55452, 1774, 19688, 35549, 61502, 15891, 25611, 41542, 59476, 54859, 7596, 23448, 33227, 53221, 13790, 29489, 11515, 60137, 41183, 32446, 13474, 62088, 34933, 18008, 7235, 55858, 36888, 44544, 26084, 9178, 63923, 47009, 19851, 2935, 49508, 52426, 2815, 16603, 40617, 54447, 4762, 26745, 42580, 64588, 14849, 28691, 19980, 34283, 50143, 6555, 22444, 44423, 60272, 8549, 32577, 46386, 11451, 60091, 41110, 32505, 13552, 62153, 34871, 17953, 7189, 12861, 62499, 48667, 24678, 10844, 60497, 38581, 22663, 641, 50412, 36575, 45250, 31530, 15657, 59255, 43386, 21333, 5563, 57274, 33185, 19453, 3527, 14283, Typography.leftDoubleQuote, 58896, 44077, 29256, 14426, 65118, 33946, 19133, 4277, 54997, 40160, 41716, 26896, 12042, 62798, 11449, 60076, 41090, 32508, 13556, 62152, 34854, 17932, 7249, 55926, 36942, 44621, 26033, 9101, 63978, 47092, 19865, 2932, 49527};
            d = -7023238001783608610L;
            a = -7241591270796924644L;
        }

        private static void C(int i, int i2, short s, Object[] objArr) {
            int i3 = 3 - (i * 4);
            int i4 = 105 - s;
            int i5 = 1 - (i2 * 4);
            byte[] bArr = $$d;
            byte[] bArr2 = new byte[i5];
            int i6 = -1;
            int i7 = i5 - 1;
            if (bArr == null) {
                i4 = i7 + (-i3);
                i3 = i3;
                i7 = i7;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i6 = -1;
            }
            while (true) {
                int i8 = i6 + 1;
                int i9 = i3 + 1;
                bArr2[i8] = (byte) i4;
                if (i8 == i7) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                i4 += -bArr[i9];
                i3 = i9;
                i7 = i7;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i6 = i8;
            }
        }

        static void init$0() {
            $$d = new byte[]{21, -84, -91, -118};
            $$e = 90;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = e + 45;
            c = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = c + 41;
            e = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = e + 49;
            c = i % 128;
            char c2 = i % 2 != 0 ? (char) 1 : '@';
            o.cf.d a2 = a(context);
            switch (c2) {
                default:
                    int i2 = 61 / 0;
                case '@':
                    return a2;
            }
        }

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        d(c cVar) {
            super(cVar);
            ViewConfiguration.getKeyRepeatDelay();
            Color.green(0);
            TextUtils.lastIndexOf("", '0');
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = e + 39;
            c = i % 128;
            switch (i % 2 != 0 ? 'c' : '%') {
                case Opcodes.DADD /* 99 */:
                    Object[] objArr = new Object[1];
                    w("\udf91沺\udfe1䔋츪淆鷃ჹ磴밆朸㓃釧ᬵ㰲\udbf3⫐爳", -TextUtils.lastIndexOf("", '@'), objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w("\udf91沺\udfe1䔋츪淆鷃ჹ磴밆朸㓃釧ᬵ㰲\udbf3⫐爳", -TextUtils.lastIndexOf("", '0'), objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        private static o.cf.d a(Context context) {
            Object[] objArr = new Object[1];
            B((char) View.MeasureSpec.getMode(0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 18, 19 - View.MeasureSpec.getSize(0), objArr);
            o.cf.d dVar = new o.cf.d(context, 19, ((String) objArr[0]).intern());
            int i = e + 39;
            c = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            g.c();
            Object[] objArr = new Object[1];
            B((char) (57427 - (Process.myPid() >> 22)), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 36, 21 - (Process.myPid() >> 22), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            w("丒凓乵硵붵♗\uee5d孻\ue96c腽ᒻ罋Z♩侱遻뭛佇ꛗ⤻툂鑒Ǝ䉶൜㵅磺鮈ꐳ䖶폴㳊\udf36\ueab1\u0af4喎瘗㎔斛\ueeb4鄗墙\udcc3ު젙", -((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            g.c();
            Object[] objArr3 = new Object[1];
            B((char) (57428 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), ((byte) KeyEvent.getModifierMetaStateMask()) + 38, TextUtils.lastIndexOf("", '0', 0, 0) + 22, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            w("撫珕擌婳䉵\ue65aᆝ魶쏕ꍻ\ueb7b뽆⫣ѯ끱偶釢流夗\ue936\uf8bb뙫﹏艪⟤Ἂ蜯宁躌枨ⰺﲔ\uf596좭\uf529閂岥ᆑ驛⺻뮦窎⌎", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1, objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            o.eg.b bVar = new o.eg.b();
            Object[] objArr5 = new Object[1];
            w("艩᮲舝㈞à㧷匍䓛┋쬨꧱惧찀氜\uf2f6迨眨Գᯂ㛄Ḽ\ude2f볘巇", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr5);
            bVar.d(((String) objArr5[0]).intern(), ((c) e()).d);
            Object[] objArr6 = new Object[1];
            B((char) Color.argb(0, 0, 0, 0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 59, View.getDefaultSize(0, 0) + 9, objArr6);
            bVar.d(((String) objArr6[0]).intern(), UUID.randomUUID().toString());
            g.c();
            Object[] objArr7 = new Object[1];
            B((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 57426), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 36, TextUtils.getOffsetAfter("", 0) + 21, objArr7);
            String intern3 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            w("\uec99އ\uecfe⸡滕\uf35e㴽蹲䯧휩쟛ꩂꋑ瀽鳑䕲᧐ᤓ疷ﰲ炉숹틯靮꿖歘ꮏ亅ھᏺ\u009a\ue990綤볿\ud989肆풗旃뛻㮩㎐\u0edbྺ튫檕랜\ue4a7跒셩傡", View.getDefaultSize(0, 0) + 1, objArr8);
            g.d(intern3, ((String) objArr8[0]).intern());
            o.eg.b c2 = c.c(((c) e()).d);
            o.eg.b bVar2 = new o.eg.b();
            Object[] objArr9 = new Object[1];
            w("恣❱怎໖࿈㺐尫䎪윿\uf7ccꛖ枉⸒僙\ufddc袤锩㧒ᓠㆳﰲ", 1 - KeyEvent.getDeadChar(0, 0), objArr9);
            bVar2.d(((String) objArr9[0]).intern(), bVar);
            Object[] objArr10 = new Object[1];
            B((char) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 7833), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 66, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 22, objArr10);
            bVar2.d(((String) objArr10[0]).intern(), c2);
            Object[] objArr11 = new Object[1];
            B((char) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 3233), 91 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 15, objArr11);
            bVar2.d(((String) objArr11[0]).intern(), ((c) e()).d);
            g.c();
            Object[] objArr12 = new Object[1];
            B((char) (57426 - ImageFormat.getBitsPerPixel(0)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 36, 21 - View.MeasureSpec.getSize(0), objArr12);
            String intern4 = ((String) objArr12[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr13 = new Object[1];
            B((char) Color.green(0), 105 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 19 - View.resolveSize(0, 0), objArr13);
            g.d(intern4, sb.append(((String) objArr13[0]).intern()).append(bVar2.b()).toString());
            int i = e + 95;
            c = i % 128;
            int i2 = i % 2;
            return bVar2;
        }

        @Override // o.y.c
        public final boolean e(int i) {
            int i2 = e;
            int i3 = i2 + 55;
            c = i3 % 128;
            int i4 = i3 % 2;
            switch (i == 2 ? 'T' : 'Q') {
                case Opcodes.FASTORE /* 81 */:
                    int i5 = i2 + 55;
                    c = i5 % 128;
                    int i6 = i5 % 2;
                    return false;
                default:
                    int i7 = i2 + Opcodes.LSUB;
                    c = i7 % 128;
                    int i8 = i7 % 2;
                    return true;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            Object obj = null;
            switch (h().a().a() == null ? Typography.less : (char) 28) {
                case 28:
                    break;
                default:
                    int i = c + 71;
                    e = i % 128;
                    if (i % 2 == 0) {
                        h().a().e(g(), f(), f.m);
                        obj.hashCode();
                        throw null;
                    }
                    h().a().e(g(), f(), f.m);
                    break;
            }
            if (((c) e()).d != null) {
                new a(g());
                a.b(g(), ((c) e()).d);
            }
            int i2 = c + 11;
            e = i2 % 128;
            switch (i2 % 2 == 0 ? 'P' : (char) 20) {
                case 20:
                    return;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:19:0x002d, code lost:
        
            if (h().d() == o.bb.a.h) goto L20;
         */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void t() {
            /*
                r4 = this;
                int r0 = o.ay.c.d.c
                int r0 = r0 + 61
                int r1 = r0 % 128
                o.ay.c.d.e = r1
                int r0 = r0 % 2
                r1 = 0
                if (r0 != 0) goto Lf
                r0 = 1
                goto L10
            Lf:
                r0 = r1
            L10:
                switch(r0) {
                    case 1: goto L20;
                    default: goto L13;
                }
            L13:
                o.bb.d r0 = r4.h()
                o.bb.a r0 = r0.d()
                o.bb.a r1 = o.bb.a.h
                if (r0 != r1) goto L35
                goto L32
            L20:
                o.bb.d r0 = r4.h()
                o.bb.a r0 = r0.d()
                o.bb.a r2 = o.bb.a.h
                r3 = 43
                int r3 = r3 / r1
                if (r0 != r2) goto L3a
                goto L3b
            L30:
                r0 = move-exception
                throw r0
            L32:
                r0 = 56
                goto L37
            L35:
                r0 = 47
            L37:
                switch(r0) {
                    case 56: goto L3b;
                    default: goto L3a;
                }
            L3a:
                goto L5d
            L3b:
                o.y.b r0 = r4.e()
                o.ay.c r0 = (o.ay.c) r0
                java.lang.String r0 = r0.d
                if (r0 == 0) goto L3a
                o.ay.a r0 = new o.ay.a
                android.content.Context r1 = r4.g()
                r0.<init>(r1)
                android.content.Context r0 = r4.g()
                o.y.b r1 = r4.e()
                o.ay.c r1 = (o.ay.c) r1
                java.lang.String r1 = r1.d
                o.ay.a.c(r0, r1)
            L5d:
                int r0 = o.ay.c.d.e
                int r0 = r0 + 41
                int r1 = r0 % 128
                o.ay.c.d.c = r1
                int r0 = r0 % 2
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ay.c.d.t():void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void B(char r22, int r23, int r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 970
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ay.c.d.B(char, int, int, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 380
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ay.c.d.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    @Override // o.av.b
    public final boolean k() {
        int i2 = g;
        int i3 = i2 + 81;
        i = i3 % 128;
        int i4 = i3 % 2;
        int i5 = i2 + 37;
        i = i5 % 128;
        int i6 = i5 % 2;
        return true;
    }

    public static o.eg.b c(String str) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        r("眗\ue41a尹舶럪\uf21a弖斛ꉇ⤠Ⓠ薹⊖彀쁸嘓妺讥䉀揢", Color.alpha(0) + 20, objArr);
        bVar.d(((String) objArr[0]).intern(), str);
        Object[] objArr2 = new Object[1];
        r("櫄얗ꉇ⤠큎鯈䅭压ᾇ䘧", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 9, objArr2);
        bVar.d(((String) objArr2[0]).intern(), UUID.randomUUID().toString());
        Object[] objArr3 = new Object[1];
        r("櫄얗毱쭰Ս죂", 6 - TextUtils.indexOf("", ""), objArr3);
        String intern = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        r("\ue962賦紿쌇曔쓮䑻\udc9a", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 7, objArr4);
        bVar.d(intern, ((String) objArr4[0]).intern());
        int i2 = g + 19;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? '4' : (char) 4) {
            case 4:
                return bVar;
            default:
                int i3 = 89 / 0;
                return bVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void r(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 574
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ay.c.r(java.lang.String, int, java.lang.Object[]):void");
    }
}
