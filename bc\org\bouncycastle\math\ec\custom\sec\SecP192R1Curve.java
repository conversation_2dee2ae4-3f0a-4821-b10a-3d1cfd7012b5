package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.AbstractECLookupTable;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECLookupTable;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192R1Curve.smali */
public class SecP192R1Curve extends ECCurve.AbstractFp {
    protected SecP192R1Point i;
    public static final BigInteger q = SecP192R1FieldElement.Q;
    private static final ECFieldElement[] j = {new SecP192R1FieldElement(ECConstants.ONE)};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192R1Curve$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ int[] b;

        a(int i, int[] iArr) {
            this.a = i;
            this.b = iArr;
        }

        private ECPoint a(int[] iArr, int[] iArr2) {
            return SecP192R1Curve.this.a(new SecP192R1FieldElement(iArr), new SecP192R1FieldElement(iArr2), SecP192R1Curve.j);
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            int[] a = u5.a();
            int[] a2 = u5.a();
            int i2 = 0;
            for (int i3 = 0; i3 < this.a; i3++) {
                int i4 = ((i3 ^ i) - 1) >> 31;
                for (int i5 = 0; i5 < 6; i5++) {
                    int i6 = a[i5];
                    int[] iArr = this.b;
                    a[i5] = i6 ^ (iArr[i2 + i5] & i4);
                    a2[i5] = a2[i5] ^ (iArr[(i2 + 6) + i5] & i4);
                }
                i2 += 12;
            }
            return a(a, a2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            int[] a = u5.a();
            int[] a2 = u5.a();
            int i2 = i * 6 * 2;
            for (int i3 = 0; i3 < 6; i3++) {
                int[] iArr = this.b;
                a[i3] = iArr[i2 + i3];
                a2[i3] = iArr[i2 + 6 + i3];
            }
            return a(a, a2);
        }
    }

    public SecP192R1Curve() {
        super(q);
        this.i = new SecP192R1Point(this, null, null);
        this.b = fromBigInteger(new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC")));
        this.c = fromBigInteger(new BigInteger(1, z4.a("64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1")));
        this.d = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831"));
        this.e = BigInteger.valueOf(1L);
        this.f = 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECCurve a() {
        return new SecP192R1Curve();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        int[] iArr = new int[i2 * 6 * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            u5.b(((SecP192R1FieldElement) eCPoint.getRawXCoord()).a, 0, iArr, i3);
            int i5 = i3 + 6;
            u5.b(((SecP192R1FieldElement) eCPoint.getRawYCoord()).a, 0, iArr, i5);
            i3 = i5 + 6;
        }
        return new a(i2, iArr);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement fromBigInteger(BigInteger bigInteger) {
        return new SecP192R1FieldElement(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public int getFieldSize() {
        return q.bitLength();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECPoint getInfinity() {
        return this.i;
    }

    public BigInteger getQ() {
        return q;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
        int[] a2 = u5.a();
        SecP192R1Field.random(secureRandom, a2);
        return new SecP192R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
        int[] a2 = u5.a();
        SecP192R1Field.randomMult(secureRandom, a2);
        return new SecP192R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public boolean supportsCoordinateSystem(int i) {
        return i == 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return new SecP192R1Point(this, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        return new SecP192R1Point(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
