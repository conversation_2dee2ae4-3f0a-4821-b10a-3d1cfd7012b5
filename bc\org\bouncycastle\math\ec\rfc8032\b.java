package bc.org.bouncycastle.math.ec.rfc8032;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\b.smali */
abstract class b {
    private static final int[] a = {1559614445, 1477600026, -1560830762, 350157278, 0, 0, 0, 268435456};
    private static final int[] b = {-1424848535, -487721339, 580428573, 1745064566, -770181698, 1036971123, 461123738, -1582065343, 1268693629, -889041821, -731974758, 43769659, 0, 0, 0, 16777216};

    static boolean a(byte[] bArr, int[] iArr) {
        b(bArr, iArr);
        return !w5.c(iArr, a);
    }

    static void b(byte[] bArr, int[] iArr) {
        a.a(bArr, 0, iArr, 0, 8);
    }

    static byte[] b(byte[] bArr) {
        long c = a.c(bArr, 49) & 4294967295L;
        long c2 = a.c(bArr, 56) & 4294967295L;
        long j = bArr[63] & 255;
        long b2 = ((a.b(bArr, 60) << 4) & 4294967295L) + (c2 >> 28);
        long j2 = c2 & 268435455;
        long c3 = (a.c(bArr, 28) & 4294967295L) - (b2 * (-50998291));
        long b3 = (((a.b(bArr, 32) << 4) & 4294967295L) - (j * (-50998291))) - (b2 * 19280294);
        long c4 = ((a.c(bArr, 42) & 4294967295L) - (j * (-6428113))) - (b2 * 5343);
        long b4 = ((((a.b(bArr, 39) << 4) & 4294967295L) - (j * 127719000)) - (b2 * (-6428113))) - (j2 * 5343);
        long b5 = ((a.b(bArr, 53) << 4) & 4294967295L) + (c >> 28);
        long j3 = c & 268435455;
        long c5 = ((((a.c(bArr, 35) & 4294967295L) - (j * 19280294)) - (b2 * 127719000)) - (j2 * (-6428113))) - (b5 * 5343);
        long b6 = ((((a.b(bArr, 25) << 4) & 4294967295L) - (j2 * (-50998291))) - (b5 * 19280294)) - (j3 * 127719000);
        long j4 = ((b3 - (j2 * 127719000)) - (b5 * (-6428113))) - (j3 * 5343);
        long b7 = (((a.b(bArr, 46) << 4) & 4294967295L) - (j * 5343)) + (c4 >> 28);
        long j5 = (c4 & 268435455) + (b4 >> 28);
        long b8 = ((a.b(bArr, 11) << 4) & 4294967295L) - (j5 * (-50998291));
        long c6 = ((a.c(bArr, 14) & 4294967295L) - (b7 * (-50998291))) - (j5 * 19280294);
        long b9 = ((((a.b(bArr, 18) << 4) & 4294967295L) - (j3 * (-50998291))) - (b7 * 19280294)) - (j5 * 127719000);
        long c7 = ((((a.c(bArr, 21) & 4294967295L) - (b5 * (-50998291))) - (j3 * 19280294)) - (b7 * 127719000)) - (j5 * (-6428113));
        long j6 = (b6 - (b7 * (-6428113))) - (j5 * 5343);
        long j7 = (b4 & 268435455) + (c5 >> 28);
        long j8 = c5 & 268435455;
        long c8 = (a.c(bArr, 7) & 4294967295L) - (j7 * (-50998291));
        long j9 = b8 - (j7 * 19280294);
        long j10 = c6 - (j7 * 127719000);
        long j11 = b9 - (j7 * (-6428113));
        long j12 = c7 - (j7 * 5343);
        long j13 = j8 + (j4 >> 28);
        long j14 = j4 & 268435455;
        long b10 = ((a.b(bArr, 4) << 4) & 4294967295L) - (j13 * (-50998291));
        long j15 = c8 - (j13 * 19280294);
        long j16 = j9 - (j13 * 127719000);
        long j17 = j10 - (j13 * (-6428113));
        long j18 = j11 - (j13 * 5343);
        long j19 = ((((c3 - (j2 * 19280294)) - (b5 * 127719000)) - (j3 * (-6428113))) - (b7 * 5343)) + (j6 >> 28);
        long j20 = j19 & 268435455;
        long j21 = j20 >>> 27;
        long j22 = j14 + (j19 >> 28) + j21;
        long c9 = (a.c(bArr, 0) & 4294967295L) - (j22 * (-50998291));
        long j23 = (b10 - (j22 * 19280294)) + (c9 >> 28);
        long j24 = c9 & 268435455;
        long j25 = (j15 - (j22 * 127719000)) + (j23 >> 28);
        long j26 = (j16 - (j22 * (-6428113))) + (j25 >> 28);
        long j27 = (j17 - (j22 * 5343)) + (j26 >> 28);
        long j28 = j18 + (j27 >> 28);
        long j29 = j27 & 268435455;
        long j30 = j12 + (j28 >> 28);
        long j31 = (j6 & 268435455) + (j30 >> 28);
        long j32 = j20 + (j31 >> 28);
        long j33 = (j32 >> 28) - j21;
        long j34 = j24 + (j33 & (-50998291));
        long j35 = (j23 & 268435455) + (j33 & 19280294) + (j34 >> 28);
        long j36 = (j25 & 268435455) + (j33 & 127719000) + (j35 >> 28);
        long j37 = (j26 & 268435455) + (j33 & (-6428113)) + (j36 >> 28);
        long j38 = j29 + (j33 & 5343) + (j37 >> 28);
        long j39 = (j28 & 268435455) + (j38 >> 28);
        long j40 = (j30 & 268435455) + (j39 >> 28);
        long j41 = (j31 & 268435455) + (j40 >> 28);
        byte[] bArr2 = new byte[32];
        a.a((j34 & 268435455) | ((j35 & 268435455) << 28), bArr2, 0);
        a.a(((j37 & 268435455) << 28) | (j36 & 268435455), bArr2, 7);
        a.a((j38 & 268435455) | ((j39 & 268435455) << 28), bArr2, 14);
        a.a((j40 & 268435455) | ((j41 & 268435455) << 28), bArr2, 21);
        a.b((int) ((j32 & 268435455) + (j41 >> 28)), bArr2, 28);
        return bArr2;
    }

    static void a(int i, byte[] bArr) {
        e.a(a, i, bArr);
    }

    static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] iArr4 = new int[12];
        w5.d(iArr, iArr2, iArr4);
        if (iArr2[3] < 0) {
            w5.a(a, 0, iArr4, 4, 0);
            w5.b(iArr, 0, iArr4, 4, 0);
        }
        byte[] bArr = new byte[48];
        a.a(iArr4, 0, 12, bArr, 0);
        b(a(bArr), iArr3);
    }

    static byte[] a(byte[] bArr) {
        long b2 = (a.b(bArr, 32) << 4) & 4294967295L;
        long c = a.c(bArr, 35) & 4294967295L;
        long b3 = (a.b(bArr, 39) << 4) & 4294967295L;
        long c2 = a.c(bArr, 42) & 4294967295L;
        long a2 = ((a.a(bArr, 46) << 4) & 4294967295L) + (c2 >> 28);
        long j = (c2 & 268435455) + (b3 >> 28);
        long c3 = ((a.c(bArr, 21) & 4294967295L) - (a2 * 127719000)) - (j * (-6428113));
        long b4 = (((a.b(bArr, 25) << 4) & 4294967295L) - (a2 * (-6428113))) - (j * 5343);
        long j2 = (b3 & 268435455) + (c >> 28);
        long b5 = (((a.b(bArr, 11) << 4) & 4294967295L) - (j * (-50998291))) - (j2 * 19280294);
        long c4 = (((a.c(bArr, 14) & 4294967295L) - (a2 * (-50998291))) - (j * 19280294)) - (j2 * 127719000);
        long b6 = ((((a.b(bArr, 18) << 4) & 4294967295L) - (a2 * 19280294)) - (j * 127719000)) - (j2 * (-6428113));
        long j3 = (c & 268435455) + (b2 >> 28);
        long b7 = ((a.b(bArr, 4) << 4) & 4294967295L) - (j3 * (-50998291));
        long c5 = ((a.c(bArr, 7) & 4294967295L) - (j2 * (-50998291))) - (j3 * 19280294);
        long j4 = b5 - (j3 * 127719000);
        long j5 = c4 - (j3 * (-6428113));
        long j6 = b6 - (j3 * 5343);
        long c6 = ((a.c(bArr, 28) & 4294967295L) - (a2 * 5343)) + (b4 >> 28);
        long j7 = (b2 & 268435455) + (c6 >> 28);
        long j8 = c6 & 268435455;
        long j9 = j8 >>> 27;
        long j10 = j7 + j9;
        long c7 = (a.c(bArr, 0) & 4294967295L) - (j10 * (-50998291));
        long j11 = (b7 - (j10 * 19280294)) + (c7 >> 28);
        long j12 = (c5 - (j10 * 127719000)) + (j11 >> 28);
        long j13 = (j4 - (j10 * (-6428113))) + (j12 >> 28);
        long j14 = (j5 - (j10 * 5343)) + (j13 >> 28);
        long j15 = j6 + (j14 >> 28);
        long j16 = (c3 - (j2 * 5343)) + (j15 >> 28);
        long j17 = (b4 & 268435455) + (j16 >> 28);
        long j18 = j8 + (j17 >> 28);
        long j19 = (j18 >> 28) - j9;
        long j20 = (c7 & 268435455) + (j19 & (-50998291));
        long j21 = (j11 & 268435455) + (j19 & 19280294) + (j20 >> 28);
        long j22 = (j12 & 268435455) + (j19 & 127719000) + (j21 >> 28);
        long j23 = (j13 & 268435455) + (j19 & (-6428113)) + (j22 >> 28);
        long j24 = (j14 & 268435455) + (j19 & 5343) + (j23 >> 28);
        long j25 = (j15 & 268435455) + (j24 >> 28);
        long j26 = (j16 & 268435455) + (j25 >> 28);
        long j27 = (j17 & 268435455) + (j26 >> 28);
        byte[] bArr2 = new byte[64];
        a.a(((j21 & 268435455) << 28) | (j20 & 268435455), bArr2, 0);
        a.a((j22 & 268435455) | ((j23 & 268435455) << 28), bArr2, 7);
        a.a(((j25 & 268435455) << 28) | (j24 & 268435455), bArr2, 14);
        a.a(((j27 & 268435455) << 28) | (j26 & 268435455), bArr2, 21);
        a.b((int) ((j18 & 268435455) + (j27 >> 28)), bArr2, 28);
        return bArr2;
    }

    static void a(int i, int[] iArr) {
        c6.a(8, (~iArr[0]) & 1, a, iArr);
        c6.d(8, iArr, 1);
    }

    static void b(int[] iArr, int[] iArr2, int[] iArr3) {
        int i;
        int[] iArr4 = new int[16];
        System.arraycopy(b, 0, iArr4, 0, 16);
        int[] iArr5 = new int[16];
        w5.d(iArr, iArr5);
        iArr5[0] = iArr5[0] + 1;
        int[] iArr6 = new int[16];
        int[] iArr7 = a;
        w5.c(iArr7, iArr, iArr6);
        int[] iArr8 = new int[4];
        System.arraycopy(iArr7, 0, iArr8, 0, 4);
        int[] iArr9 = new int[4];
        System.arraycopy(iArr, 0, iArr9, 0, 4);
        int[] iArr10 = new int[4];
        iArr10[0] = 1;
        int i2 = 15;
        int[] iArr11 = iArr8;
        int[] iArr12 = iArr9;
        int b2 = d.b(15, iArr5);
        int[] iArr13 = iArr10;
        int[] iArr14 = new int[4];
        while (b2 > 254) {
            int a2 = d.a(i2, iArr6) - b2;
            int i3 = (~(a2 >> 31)) & a2;
            if (iArr6[i2] < 0) {
                d.a(i2, i3, iArr4, iArr5, iArr6);
                i = b2;
                d.a(3, i3, iArr11, iArr14, iArr12, iArr13);
            } else {
                i = b2;
                d.b(i2, i3, iArr4, iArr5, iArr6);
                d.b(3, i3, iArr11, iArr14, iArr12, iArr13);
            }
            if (!d.a(i2, iArr4, iArr5)) {
                b2 = i;
            } else {
                i2 = i >>> 5;
                b2 = d.b(i2, iArr4);
                int[] iArr15 = iArr13;
                iArr13 = iArr14;
                iArr14 = iArr15;
                int[] iArr16 = iArr5;
                iArr5 = iArr4;
                iArr4 = iArr16;
                int[] iArr17 = iArr11;
                iArr11 = iArr12;
                iArr12 = iArr17;
            }
        }
        System.arraycopy(iArr12, 0, iArr2, 0, 4);
        System.arraycopy(iArr13, 0, iArr3, 0, 4);
    }
}
