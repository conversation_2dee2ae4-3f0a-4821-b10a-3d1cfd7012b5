package kotlin.jvm.functions;

import kotlin.Function;
import kotlin.Metadata;

/* compiled from: Functions.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u000e\bf\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u0000*\u0006\b\u0001\u0010\u0002 \u0000*\u0006\b\u0002\u0010\u0003 \u0000*\u0006\b\u0003\u0010\u0004 \u0000*\u0006\b\u0004\u0010\u0005 \u0000*\u0006\b\u0005\u0010\u0006 \u0000*\u0006\b\u0006\u0010\u0007 \u0000*\u0006\b\u0007\u0010\b \u0000*\u0006\b\b\u0010\t \u0000*\u0006\b\t\u0010\n \u0000*\u0006\b\n\u0010\u000b \u0000*\u0006\b\u000b\u0010\f \u00012\b\u0012\u0004\u0012\u0002H\f0\rJf\u0010\u000e\u001a\u00028\u000b2\u0006\u0010\u000f\u001a\u00028\u00002\u0006\u0010\u0010\u001a\u00028\u00012\u0006\u0010\u0011\u001a\u00028\u00022\u0006\u0010\u0012\u001a\u00028\u00032\u0006\u0010\u0013\u001a\u00028\u00042\u0006\u0010\u0014\u001a\u00028\u00052\u0006\u0010\u0015\u001a\u00028\u00062\u0006\u0010\u0016\u001a\u00028\u00072\u0006\u0010\u0017\u001a\u00028\b2\u0006\u0010\u0018\u001a\u00028\t2\u0006\u0010\u0019\u001a\u00028\nH¦\u0002¢\u0006\u0002\u0010\u001a¨\u0006\u001b"}, d2 = {"Lkotlin/jvm/functions/Function11;", "P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "P9", "P10", "P11", "R", "Lkotlin/Function;", "invoke", "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9", "p10", "p11", "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\functions\Function11.smali */
public interface Function11<P1, P2, P3, P4, P5, P6, P7, P8, P9, P10, P11, R> extends Function<R> {
    R invoke(P1 p1, P2 p2, P3 p3, P4 p4, P5 p5, P6 p6, P7 p7, P8 p8, P9 p9, P10 p10, P11 p11);
}
