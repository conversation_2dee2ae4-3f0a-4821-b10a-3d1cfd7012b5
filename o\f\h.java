package o.f;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\h.smali */
public final class h extends e {
    private static int e = 0;
    private static int a = 1;

    public h() {
        super(e.d.c, new Date());
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = e;
        int i2 = (i ^ 3) + ((i & 3) << 1);
        a = i2 % 128;
        boolean z = i2 % 2 == 0;
        o.i.f fVar = o.i.f.f;
        switch (z) {
            case true:
                int i3 = 8 / 0;
            default:
                return fVar;
        }
    }

    @Override // o.f.e
    public final String c() {
        int i = a;
        int i2 = (i + Opcodes.INEG) - 1;
        e = i2 % 128;
        int i3 = i2 % 2;
        int i4 = (i + 108) - 1;
        e = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.f.e
    public final byte[] d() {
        int i = e;
        int i2 = (i ^ 65) + ((i & 65) << 1);
        int i3 = i2 % 128;
        a = i3;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case false:
                int i4 = (i3 + 76) - 1;
                e = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        return null;
                    default:
                        int i5 = 5 / 0;
                        return null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = a;
        int i2 = i + 19;
        e = i2 % 128;
        int i3 = i2 % 2;
        int i4 = (i + 20) - 1;
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 20 / 0;
                return null;
            default:
                return null;
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = e;
        int i2 = (i ^ Opcodes.LMUL) + ((i & Opcodes.LMUL) << 1);
        int i3 = i2 % 128;
        a = i3;
        int i4 = i2 % 2;
        int i5 = (i3 + 82) - 1;
        e = i5 % 128;
        int i6 = i5 % 2;
        return null;
    }

    @Override // o.f.e
    public final String j() {
        int i = a;
        int i2 = (i + 8) - 1;
        e = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? '8' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                int i3 = (i + Opcodes.ISHR) - 1;
                e = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        int i4 = 80 / 0;
                        return null;
                    default:
                        return null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }
}
