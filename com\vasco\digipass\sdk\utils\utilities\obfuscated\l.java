package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l.smali */
public abstract class l extends b0 {
    static final o0 u0 = new a(l.class, 8);
    b0 C;
    int L;
    b0 R;
    w b;
    r x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(e0 e0Var) {
            return e0Var.l();
        }
    }

    l(e0 e0Var) {
        int i = 0;
        b0 a2 = a(e0Var, 0);
        if (a2 instanceof w) {
            this.b = (w) a2;
            a2 = a(e0Var, 1);
            i = 1;
        }
        if (a2 instanceof r) {
            this.x = (r) a2;
            i++;
            a2 = a(e0Var, i);
        }
        if (!(a2 instanceof j0)) {
            this.C = a2;
            i++;
            a2 = a(e0Var, i);
        }
        if (e0Var.size() != i + 1) {
            throw new IllegalArgumentException("input sequence too large");
        }
        if (!(a2 instanceof j0)) {
            throw new IllegalArgumentException("No tagged object found in sequence. Structure doesn't seem to be of type External");
        }
        j0 j0Var = (j0) a2;
        this.L = a(j0Var.j());
        this.R = a(j0Var);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        return h().a(z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return true;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return new x1(this.b, this.x, this.C, this.L, this.R);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new a3(this.b, this.x, this.C, this.L, this.R);
    }

    abstract e0 h();

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return (((f6.a(this.b) ^ f6.a(this.x)) ^ f6.a(this.C)) ^ this.L) ^ this.R.hashCode();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 40);
        h().a(zVar, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (this == b0Var) {
            return true;
        }
        if (!(b0Var instanceof l)) {
            return false;
        }
        l lVar = (l) b0Var;
        return f6.a(this.b, lVar.b) && f6.a(this.x, lVar.x) && f6.a(this.C, lVar.C) && this.L == lVar.L && this.R.b(lVar.R);
    }

    private static int a(int i) {
        if (i < 0 || i > 2) {
            throw new IllegalArgumentException("invalid encoding value: " + i);
        }
        return i;
    }

    private static b0 a(int i, b0 b0Var) {
        if (i != 1) {
            return i != 2 ? b0Var : d.x.a(b0Var);
        }
        return x.x.a(b0Var);
    }

    private static b0 a(j0 j0Var) {
        int i = j0Var.i();
        int j = j0Var.j();
        if (128 != i) {
            throw new IllegalArgumentException("invalid tag: " + p0.a(i, j));
        }
        if (j == 0) {
            return j0Var.h().toASN1Primitive();
        }
        if (j == 1) {
            return x.a(j0Var, false);
        }
        if (j == 2) {
            return d.a(j0Var, false);
        }
        throw new IllegalArgumentException("invalid tag: " + p0.a(i, j));
    }

    l(w wVar, r rVar, b0 b0Var, int i, b0 b0Var2) {
        this.b = wVar;
        this.x = rVar;
        this.C = b0Var;
        this.L = a(i);
        this.R = a(i, b0Var2);
    }

    private static b0 a(e0 e0Var, int i) {
        if (e0Var.size() > i) {
            return e0Var.a(i).toASN1Primitive();
        }
        throw new IllegalArgumentException("too few objects in input sequence");
    }
}
