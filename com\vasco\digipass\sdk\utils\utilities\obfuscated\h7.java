package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.Digest;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\h7.smali */
public class h7 {
    protected BigInteger a;
    protected BigInteger b;
    protected Digest c;

    public void a(BigInteger bigInteger, BigInteger bigInteger2, Digest digest) {
        this.a = bigInteger;
        this.b = bigInteger2;
        this.c = digest;
    }

    public BigInteger a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        return this.b.modPow(g7.calculateX(this.c, this.a, bArr, bArr2, bArr3), this.a);
    }
}
