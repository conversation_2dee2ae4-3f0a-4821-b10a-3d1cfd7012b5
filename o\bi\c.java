package o.bi;

import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\c.smali */
public final class c extends Exception {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int e;
    private final b c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        b();
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        int i = e + 99;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void b() {
        b = -1197846136338657060L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r0 = o.bi.c.$$a
            int r8 = r8 * 3
            int r8 = 71 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L30
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r8 = r8 + 1
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.c.f(short, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{66, 0, -113, Tnaf.POW_2_WIDTH};
        $$b = 207;
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    c(o.bi.c.b r5) {
        /*
            r4 = this;
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r1 = r5.c
            java.lang.StringBuilder r0 = r0.append(r1)
            r1 = 0
            float r2 = android.graphics.PointF.length(r1, r1)
            int r1 = (r2 > r1 ? 1 : (r2 == r1 ? 0 : -1))
            r2 = 1
            int r1 = 1 - r1
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r3 = "Э⮷Ѝ믚ꃶᚑ䐁ﯞ\ue0e2嚌葛㯈肟₡隤ꛬ쑧箰惖횮ѳ믢ꃀᚲ䑤ﯺ\ue0bc囝萌㮓₿"
            d(r3, r1, r2)
            r1 = 0
            r1 = r2[r1]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r0 = r0.append(r1)
            java.lang.String r0 = r0.toString()
            r4.<init>(r0)
            r4.c = r5
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.c.<init>(o.bi.c$b):void");
    }

    /* renamed from: o.bi.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b = 0;
        static final /* synthetic */ int[] c;
        private static int d;

        static {
            d = 1;
            int[] iArr = new int[b.values().length];
            c = iArr;
            try {
                iArr[b.e.ordinal()] = 1;
                int i = b + 87;
                d = i % 128;
                if (i % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                c[b.a.ordinal()] = 2;
                int i2 = b;
                int i3 = (i2 ^ 77) + ((i2 & 77) << 1);
                d = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    public final o.bb.a a() {
        int i = e + 33;
        a = i % 128;
        int i2 = i % 2;
        switch (AnonymousClass4.c[this.c.ordinal()]) {
            case 1:
                return o.bb.a.u;
            case 2:
                o.bb.a aVar = o.bb.a.A;
                int i3 = e + 7;
                a = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return aVar;
                }
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                d("Ⴋ壢ჾ좏潥䑿僗袈⽷ѯ郖䢝伆\uef38쑹\uf40e탮ࢩ꽙葆Ⴁ죱", TextUtils.getOffsetAfter("", 0) + 1, objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(this.c.name()).toString());
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\c$b.smali */
    public static final class b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final b a;
        private static char[] b;
        private static final /* synthetic */ b[] d;
        public static final b e;
        private static int f;
        private static int h;
        final String c;

        static void b() {
            b = new char[]{50819, 50814, 50694, 50692, 50696, 50700, 50800, 50696, 50692, 50698, 50692, 50811, 50812, 50941, 50853, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50937, 50843, 50921, 50835, 50878, 50873, 50848, 50854, 50832, 50843, 50852, 50709, 50811, 50796, 50702, 50718, 50710, 50733, 50732};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0031). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void i(int r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = 122 - r7
                byte[] r0 = o.bi.c.b.$$a
                int r8 = r8 * 2
                int r8 = 1 - r8
                int r6 = r6 + 4
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L15
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L31
            L15:
                r3 = r2
            L16:
                byte r4 = (byte) r7
                r1[r3] = r4
                int r3 = r3 + 1
                int r6 = r6 + 1
                if (r3 != r8) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L31:
                int r7 = -r7
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L16
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bi.c.b.i(int, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{105, 1, -115, -23};
            $$b = Opcodes.D2F;
        }

        private static /* synthetic */ b[] c() {
            int i = f;
            int i2 = i + Opcodes.LSHR;
            h = i2 % 128;
            int i3 = i2 % 2;
            b[] bVarArr = {e, a};
            int i4 = i + 23;
            h = i4 % 128;
            int i5 = i4 % 2;
            return bVarArr;
        }

        public static b valueOf(String str) {
            int i = f + 63;
            h = i % 128;
            int i2 = i % 2;
            b bVar = (b) Enum.valueOf(b.class, str);
            int i3 = f + 35;
            h = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 26 : ']') {
                case 26:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return bVar;
            }
        }

        public static b[] values() {
            int i = h + 15;
            f = i % 128;
            int i2 = i % 2;
            b[] bVarArr = (b[]) d.clone();
            int i3 = h + 1;
            f = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    int i4 = 97 / 0;
                    return bVarArr;
                default:
                    return bVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            f = 1;
            b();
            Object[] objArr = new Object[1];
            g("\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{0, 13, 91, 0}, false, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g("\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{13, 14, 0, 0}, false, objArr2);
            e = new b(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            g("\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{27, 8, 0, 0}, false, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g("\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000", new int[]{35, 9, 109, 5}, false, objArr4);
            a = new b(intern2, 1, ((String) objArr4[0]).intern());
            d = c();
            int i = h + 83;
            f = i % 128;
            switch (i % 2 == 0 ? '=' : 'A') {
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    int i2 = 44 / 0;
                    return;
                default:
                    return;
            }
        }

        private b(String str, int i, String str2) {
            this.c = str2;
        }

        private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
            int i;
            int i2;
            char[] cArr;
            char[] cArr2;
            int i3;
            String str2 = str;
            byte[] bArr = str2;
            if (str2 != null) {
                bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            }
            byte[] bArr2 = bArr;
            l lVar = new l();
            int i4 = 0;
            int i5 = iArr[0];
            int i6 = iArr[1];
            int i7 = iArr[2];
            int i8 = iArr[3];
            char[] cArr3 = b;
            if (cArr3 != null) {
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i9 = 0;
                while (true) {
                    switch (i9 < length ? 'L' : '/') {
                        case Base64.mimeLineLength /* 76 */:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i4] = Integer.valueOf(cArr3[i9]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    cArr2 = cArr3;
                                    i3 = length;
                                } else {
                                    Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) TextUtils.indexOf("", "", i4), 43 - View.resolveSizeAndState(i4, i4, i4));
                                    byte b2 = (byte) (-$$a[1]);
                                    byte b3 = (byte) (b2 + 3);
                                    cArr2 = cArr3;
                                    i3 = length;
                                    Object[] objArr3 = new Object[1];
                                    i(b2, b3, (byte) (b3 - 2), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i9++;
                                cArr3 = cArr2;
                                length = i3;
                                i4 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr3 = cArr4;
                            break;
                    }
                }
            }
            char[] cArr5 = new char[i6];
            System.arraycopy(cArr3, i5, cArr5, 0, i6);
            if (bArr2 != null) {
                int i10 = $11 + Opcodes.DSUB;
                $10 = i10 % 128;
                if (i10 % 2 != 0) {
                    cArr = new char[i6];
                    i2 = 0;
                } else {
                    i2 = 0;
                    cArr = new char[i6];
                }
                lVar.d = i2;
                char c = 0;
                while (true) {
                    switch (lVar.d < i6) {
                        case false:
                            cArr5 = cArr;
                            break;
                        default:
                            if (bArr2[lVar.d] == 1) {
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = o.e.a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(TextUtils.getCapsMode("", 0, 0) + 11, (char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1), 449 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)));
                                        byte b4 = (byte) (-$$a[1]);
                                        byte b5 = (byte) (b4 + 4);
                                        Object[] objArr5 = new Object[1];
                                        i(b4, b5, (byte) (b5 - 3), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj2);
                                    }
                                    cArr[i11] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i12 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c)};
                                    Object obj3 = o.e.a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(KeyEvent.normalizeMetaState(0) + 10, (char) TextUtils.getCapsMode("", 0, 0), ExpandableListView.getPackedPositionType(0L) + 207);
                                        byte b6 = (byte) (-$$a[1]);
                                        byte b7 = (byte) (b6 + 1);
                                        Object[] objArr7 = new Object[1];
                                        i(b6, b7, b7, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj3);
                                    }
                                    cArr[i12] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c = cArr[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj4 = o.e.a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 11, (char) View.MeasureSpec.getMode(0), 259 - (ViewConfiguration.getScrollDefaultDelay() >> 16));
                                    byte b8 = $$a[1];
                                    byte b9 = (byte) (-b8);
                                    Object[] objArr9 = new Object[1];
                                    i(b9, (byte) (b9 & 56), (byte) (b8 - 1), objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                    }
                }
            }
            if (i8 > 0) {
                int i13 = $10 + Opcodes.DMUL;
                $11 = i13 % 128;
                switch (i13 % 2 == 0 ? '8' : (char) 0) {
                    case '8':
                        char[] cArr6 = new char[i6];
                        i = 0;
                        System.arraycopy(cArr5, 1, cArr6, 0, i6);
                        System.arraycopy(cArr6, 0, cArr5, i6 + i8, i8);
                        System.arraycopy(cArr6, i8, cArr5, 0, i6 * i8);
                        break;
                    default:
                        i = 0;
                        char[] cArr7 = new char[i6];
                        System.arraycopy(cArr5, 0, cArr7, 0, i6);
                        int i14 = i6 - i8;
                        System.arraycopy(cArr7, 0, cArr5, i14, i8);
                        System.arraycopy(cArr7, i8, cArr5, 0, i14);
                        break;
                }
            } else {
                i = 0;
            }
            if (z) {
                char[] cArr8 = new char[i6];
                lVar.d = i;
                while (lVar.d < i6) {
                    cArr8[lVar.d] = cArr5[(i6 - lVar.d) - 1];
                    lVar.d++;
                }
                cArr5 = cArr8;
            }
            switch (i7 > 0 ? '\\' : 'V') {
                case Opcodes.SASTORE /* 86 */:
                    break;
                default:
                    int i15 = $10 + 73;
                    $11 = i15 % 128;
                    if (i15 % 2 == 0) {
                        lVar.d = 1;
                    } else {
                        lVar.d = 0;
                    }
                    while (lVar.d < i6) {
                        cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                        lVar.d++;
                    }
                    break;
            }
            objArr[0] = new String(cArr5);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x0038  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void d(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 372
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.c.d(java.lang.String, int, java.lang.Object[]):void");
    }
}
