package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP160R2Field.smali */
public class SecP160R2Field {
    static final int[] a = {-21389, -2, -1, -1, -1};
    private static final int[] b = {457489321, 42778, 1, 0, 0, -42778, -3, -1, -1, -1};
    private static final int[] c = {-457489321, -42779, -2, -1, -1, 42777, 2};

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (t5.a(iArr, iArr2, iArr3) != 0 || (iArr3[4] == -1 && t5.b(iArr3, a))) {
            c6.a(5, 21389, iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.a(10, iArr, iArr2, iArr3) != 0 || (iArr3[9] == -1 && c6.d(10, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(10, iArr3, iArr4.length);
            }
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(5, iArr, iArr2) != 0 || (iArr2[4] == -1 && t5.b(iArr2, a))) {
            c6.a(5, 21389, iArr2);
        }
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = t5.a(bigInteger);
        if (a2[4] == -1) {
            int[] iArr = a;
            if (t5.b(a2, iArr)) {
                t5.d(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(5, iArr, 0, iArr2);
        } else {
            c6.d(5, iArr2, t5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 5; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] b2 = t5.b();
        t5.c(iArr, iArr2, b2);
        reduce(b2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (t5.d(iArr, iArr2, iArr3) != 0 || (iArr3[9] == -1 && c6.d(10, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(10, iArr3, iArr4.length);
            }
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            t5.e(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            t5.e(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[20];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 5);
        } while (c6.f(5, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        if (t5.a(21389, t5.a(21389, iArr, 5, iArr, 0, iArr2, 0), iArr2, 0) != 0 || (iArr2[4] == -1 && t5.b(iArr2, a))) {
            c6.a(5, 21389, iArr2);
        }
    }

    public static void reduce32(int i, int[] iArr) {
        if ((i == 0 || t5.a(21389, i, iArr, 0) == 0) && !(iArr[4] == -1 && t5.b(iArr, a))) {
            return;
        }
        c6.a(5, 21389, iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] b2 = t5.b();
        t5.c(iArr, b2);
        reduce(b2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] b2 = t5.b();
        t5.c(iArr, b2);
        reduce(b2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            t5.c(iArr2, b2);
            reduce(b2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (t5.e(iArr, iArr2, iArr3) != 0) {
            c6.c(5, 21389, iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(10, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            if (c6.g(iArr4.length, iArr4, iArr3) != 0) {
                c6.a(10, iArr3, iArr4.length);
            }
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(5, iArr, 0, iArr2) != 0 || (iArr2[4] == -1 && t5.b(iArr2, a))) {
            c6.a(5, 21389, iArr2);
        }
    }
}
