package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k6.smali */
public class k6 implements CipherParameters {
    private SecureRandom a;
    private CipherParameters b;

    public CipherParameters a() {
        return this.b;
    }

    public SecureRandom b() {
        return this.a;
    }
}
