package o.ds;

import android.graphics.Color;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static char[] c;
    private static char d;
    private static b e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        e();
        SystemClock.currentThreadTimeMillis();
        Color.argb(0, 0, 0, 0);
        int i = a + 33;
        b = i % 128;
        switch (i % 2 != 0 ? 'R' : (char) 19) {
            case 19:
                break;
            default:
                int i2 = 27 / 0;
                break;
        }
    }

    static void e() {
        c = new char[]{30498, 30566, 30509, 30570, 30510, 30555, 30530, 30517, 30591, 30589, 30571, 30556, 30511, 30568, 30582, 30588, 30508, 30587, 30572, 30560, 30561, 30540, 30584, 30586, 30574};
        d = (char) 17040;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 69
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r0 = o.ds.c.$$a
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L19:
            r3 = r2
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ds.c.g(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{60, -43, Tnaf.POW_2_WIDTH, 118};
        $$b = Opcodes.IFNE;
    }

    /* renamed from: o.ds.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] d;

        static {
            a = 0;
            c = 1;
            int[] iArr = new int[f.values().length];
            d = iArr;
            try {
                iArr[f.c.ordinal()] = 1;
                int i = c;
                int i2 = (i ^ 71) + ((i & 71) << 1);
                a = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                d[f.e.ordinal()] = 2;
                int i4 = a;
                int i5 = (i4 ^ 35) + ((i4 & 35) << 1);
                c = i5 % 128;
                if (i5 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[f.a.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    public static void b(f fVar) {
        g.c();
        Object[] objArr = new Object[1];
        f(32 - Color.red(0), "\u0006\u0005\u0014\u0015\u0013\u0014\u0013\u0012\u0004\u0010\u0015\n\u0013\u0016\u0007\u0013\u0018\u0010\u0000\u000f\u0002\u0010\u0004\u0010\u0015\u0005\u0014\u0015\u0017\u000e\u0004\b", (byte) (TextUtils.getTrimmedLength("") + 60), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f((-16777168) - Color.rgb(0, 0, 0), "\u0003\r\u000e\u0014\u0012\u0002\u0011\u0016\u000e\u0004\u0000\u0014\u0017\u0013\u0010\u0002\u000f\u0018\n\u0011\u0013\u0016\u0007\u0013\r\u0011\u000f\u0018\u000b\u0000\u0010\u0002\u000f\u0018\n\u0002\n\u0016\u0002\u0017\n\u0007\r\t\u0002\r\f\u0011", (byte) ((ViewConfiguration.getEdgeSlop() >> 16) + Opcodes.INEG), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(fVar).toString());
        switch (AnonymousClass4.d[fVar.ordinal()]) {
            case 1:
                e = new a();
                return;
            case 2:
                e = new e();
                return;
            case 3:
                e = new d();
                int i = a + 27;
                b = i % 128;
                int i2 = i % 2;
                break;
        }
        int i3 = a + Opcodes.LMUL;
        b = i3 % 128;
        int i4 = i3 % 2;
    }

    public static f c() {
        b bVar = e;
        switch (bVar == null) {
            case false:
                return bVar.d();
            default:
                int i = b;
                int i2 = i + 45;
                a = i2 % 128;
                if (i2 % 2 == 0) {
                }
                int i3 = i + 7;
                a = i3 % 128;
                int i4 = i3 % 2;
                return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r25, java.lang.String r26, byte r27, java.lang.Object[] r28) {
        /*
            Method dump skipped, instructions count: 1080
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ds.c.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
