package o.et;

import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\f.smali */
public final class f extends c {
    private static int[] a;
    private static int d = 1;
    private static int e;
    private Integer c;

    static {
        e = 0;
        d();
        TextUtils.indexOf((CharSequence) "", '0');
        int i = d + 81;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        a = new int[]{1841850477, -1043259099, -1349706152, -1024483694, -1210670369, 1870102752, -1355999077, 578682514, -1407897691, -1260923675, 2020961421, 1666822738, 685616438, -689558966, -842052613, 1856267964, -1450204969, -1103873597};
    }

    @Override // o.el.d
    public final /* synthetic */ o.ey.e a(String str) {
        int i = e + 93;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                c(str);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                o.ez.d c = c(str);
                int i2 = d + Opcodes.LREM;
                e = i2 % 128;
                int i3 = i2 % 2;
                return c;
        }
    }

    public f(String str, String str2, int i, String str3) {
        super(str, o.dp.b.h, str2, i, str3);
    }

    @Override // o.et.c
    protected final c c(String str, String str2, int i, String str3) {
        f fVar = new f(str, str2, i, str3);
        int i2 = e + 85;
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return fVar;
        }
    }

    private o.ez.d c(String str) {
        o.ez.d dVar = new o.ez.d(n(), str, false);
        Integer num = this.c;
        switch (num != null ? (char) 21 : '0') {
            case '0':
                break;
            default:
                int i = d + 75;
                e = i % 128;
                if (i % 2 != 0) {
                }
                dVar.d(num.intValue());
                break;
        }
        int i2 = d + Opcodes.LREM;
        e = i2 % 128;
        int i3 = i2 % 2;
        return dVar;
    }

    public final Integer b() {
        int i = e;
        int i2 = i + 9;
        d = i2 % 128;
        int i3 = i2 % 2;
        Integer num = this.c;
        int i4 = i + 41;
        d = i4 % 128;
        switch (i4 % 2 == 0 ? 'K' : Typography.greater) {
            case 'K':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return num;
        }
    }

    public final void c(Integer num) {
        int i = d + Opcodes.DNEG;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        this.c = num;
        int i4 = i2 + 19;
        d = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    @Override // o.et.c
    public final EmvApplicationType e() {
        EmvApplicationType emvApplicationType;
        int i = e + 37;
        d = i % 128;
        switch (i % 2 == 0) {
            case false:
                emvApplicationType = EmvApplicationType.HceVts;
                break;
            default:
                emvApplicationType = EmvApplicationType.HceVts;
                int i2 = 92 / 0;
                break;
        }
        int i3 = e + 55;
        d = i3 % 128;
        int i4 = i3 % 2;
        return emvApplicationType;
    }

    @Override // o.et.c
    public final byte[] E() {
        int i = d;
        int i2 = i + 31;
        e = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? ':' : (char) 28) {
            case Opcodes.ASTORE /* 58 */:
                obj.hashCode();
                throw null;
            default:
                int i3 = i + 11;
                e = i3 % 128;
                int i4 = i3 % 2;
                return null;
        }
    }
}
