package com.vasco.digipass.sdk.utils.utilities.srp;

import bc.org.bouncycastle.crypto.CryptoException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f7;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\srp\SRP6VascoServer.smali */
public class SRP6VascoServer extends f7 {
    private boolean n = false;

    private static void a(BigInteger bigInteger) throws UtilitiesSDKException {
        if (bigInteger == null || BigInteger.ZERO.equals(bigInteger)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.PASSWORD_VERIFIER_NULL_OR_EMPTY);
        }
        if (f1.a(bigInteger).length > 256) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.PASSWORD_VERIFIER_INCORRECT_LENGTH);
        }
    }

    public UtilitiesSDKCryptoResponse calculateHashedSecret(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
        try {
            if (!this.n) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SRP_SERVER_NOT_INITIALIZED);
            }
            SRP6VascoUtils.b(bigInteger, this.a);
            a(bigInteger2, this.a);
            SRP6VascoUtils.a(bigInteger3, this.a);
            this.g = bigInteger2;
            this.h = bigInteger;
            calculateSecret(bigInteger3);
            return new UtilitiesSDKCryptoResponse(0, SRP6VascoUtils.a(this.e, this.j));
        } catch (CryptoException e) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        } catch (UtilitiesSDKException e2) {
            return new UtilitiesSDKCryptoResponse(e2.getReturnErrorCode());
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.f7
    public BigInteger generateServerCredentials() {
        if (this.n) {
            return super.generateServerCredentials();
        }
        return null;
    }

    public BigInteger getPrivB() {
        return this.g;
    }

    public void init(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, byte b, SecureRandom secureRandom) throws UtilitiesSDKException {
        try {
            SRP6VascoUtils.c(bigInteger);
            SRP6VascoUtils.b(bigInteger2);
            a(bigInteger3);
            SRP6VascoUtils.a(b);
            if (secureRandom == null) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_RANDOM_NULL_OR_EMPTY);
            }
            super.init(bigInteger, bigInteger2, bigInteger3, SRP6VascoUtils.b(b), secureRandom);
            this.n = true;
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public boolean verifyClientEvidenceMessage(byte[] bArr, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr2, byte[] bArr3, byte[] bArr4) throws UtilitiesSDKException {
        try {
            if (!this.n) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SRP_SERVER_NOT_INITIALIZED);
            }
            SRP6VascoUtils.a(bArr);
            SRP6VascoUtils.a(bigInteger, this.a);
            SRP6VascoUtils.b(bigInteger2, this.a);
            SRP6VascoUtils.c(bArr2);
            SRP6VascoUtils.e(bArr3);
            SRP6VascoUtils.b(bArr4);
            UtilitiesSDKCryptoResponse calculateClientEvidenceMessage = SRP6VascoUtils.calculateClientEvidenceMessage(SRP6VascoUtils.a(this.e), this.a, this.b, bigInteger, bigInteger2, bArr2, bArr4, bArr3);
            int returnCode = calculateClientEvidenceMessage.getReturnCode();
            if (returnCode == 0) {
                return Arrays.equals(bArr, calculateClientEvidenceMessage.getOutputData());
            }
            throw new UtilitiesSDKException(returnCode);
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    private static void a(BigInteger bigInteger, BigInteger bigInteger2) throws UtilitiesSDKException {
        if (bigInteger != null) {
            BigInteger bigInteger3 = BigInteger.ZERO;
            if (!bigInteger3.equals(bigInteger) && !bigInteger.mod(bigInteger2).equals(bigInteger3)) {
                if (f1.a(bigInteger).length > 256) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_PRIVATE_KEY_INCORRECT_LENGTH);
                }
                return;
            }
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_PRIVATE_KEY_INVALID);
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_PRIVATE_KEY_NULL_OR_EMPTY);
    }
}
