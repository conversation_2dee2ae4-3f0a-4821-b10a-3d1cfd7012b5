package o.ec;

import com.esotericsoftware.asm.Opcodes;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import kotlin.io.encoding.Base64;
import o.ee.j;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ec\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int d;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        a = 1;
        e();
        int i = a + 91;
        d = i % 128;
        switch (i % 2 != 0 ? '+' : 'Y') {
            case '+':
                throw null;
            default:
                return;
        }
    }

    static void e() {
        e = new char[]{50863, 50701, 50792, 50787, 50902, 50936, 50942, 50820, 50857, 50848, 50941, 50838, 50847, 50824, 50928, 50943, 50859, 50857, 50855, 50878, 50849, 50836, 50928, 50846, 50875, 50851, 50848, 50878, 50849, 50857, 50849, 50845, 50826, 50854, 50854, 50856, 50850, 50878, 50849, 50859, 50853, 50843, 50933, 50879, 50848, 50849, 50855, 50863, 50941, 50838, 50847, 50826, 50828, 50830, 50943, 50859, 50853, 50859, 50849, 50878, 50903, 50929, 50821, 50932, 50854, 50855, 50851, 50853, 50843, 50837, 50848, 50855, 50841};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = 122 - r9
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r0 = o.ec.e.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L33
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L33:
            int r7 = r7 + r10
            int r9 = r9 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ec.e.g(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{Base64.padSymbol, -59, -56, Tnaf.POW_2_WIDTH};
        $$b = Opcodes.GOTO;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:24:0x0068  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] c(byte[] r5, java.lang.String r6) {
        /*
            int r0 = r6.hashCode()
            r1 = 4
            r2 = 1
            r3 = 0
            switch(r0) {
                case -903629273: goto L3a;
                case 3528965: goto Lc;
                default: goto Lb;
            }
        Lb:
            goto L68
        Lc:
            r0 = 92
            r4 = 3
            int[] r0 = new int[]{r3, r1, r0, r4}
            java.lang.Object[] r1 = new java.lang.Object[r2]
            java.lang.String r4 = "\u0000\u0001\u0000\u0000"
            f(r4, r0, r3, r1)
            r0 = r1[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r6 = r6.equals(r0)
            if (r6 == 0) goto L2a
            r6 = r2
            goto L2b
        L2a:
            r6 = r3
        L2b:
            switch(r6) {
                case 1: goto L2f;
                default: goto L2e;
            }
        L2e:
            goto L68
        L2f:
            int r6 = o.ec.e.a
            int r6 = r6 + r2
            int r0 = r6 % 128
            o.ec.e.d = r0
            int r6 = r6 % 2
            r2 = r3
            goto L69
        L3a:
            r0 = 6
            int[] r0 = new int[]{r1, r0, r3, r0}
            java.lang.Object[] r1 = new java.lang.Object[r2]
            java.lang.String r4 = "\u0000\u0001\u0001\u0001\u0001\u0001"
            f(r4, r0, r2, r1)
            r0 = r1[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r6 = r6.equals(r0)
            if (r6 == 0) goto L57
            r6 = 96
            goto L59
        L57:
            r6 = 31
        L59:
            switch(r6) {
                case 31: goto L68;
                default: goto L5c;
            }
        L5c:
            int r6 = o.ec.e.a
            int r6 = r6 + 105
            int r0 = r6 % 128
            o.ec.e.d = r0
            int r6 = r6 % 2
            goto L69
        L68:
            r2 = -1
        L69:
            switch(r2) {
                case 0: goto L7e;
                case 1: goto L6f;
                default: goto L6c;
            }
        L6c:
            byte[] r5 = new byte[r3]
            goto L83
        L6f:
            byte[] r5 = a(r5)
            int r6 = o.ec.e.d
            int r6 = r6 + 65
            int r0 = r6 % 128
            o.ec.e.a = r0
            int r6 = r6 % 2
            return r5
        L7e:
            byte[] r5 = c(r5)
            return r5
        L83:
            int r6 = o.ec.e.d
            int r6 = r6 + 39
            int r0 = r6 % 128
            o.ec.e.a = r0
            int r6 = r6 % 2
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ec.e.c(byte[], java.lang.String):byte[]");
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0047, code lost:
    
        r7 = new java.lang.Object[1];
        f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{15, 27, 0, 21}, true, r7);
        r3 = java.lang.Class.forName(((java.lang.String) r7[0]).intern());
        r6 = new java.lang.Object[1];
        f("\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{42, 6, 0, 2}, true, r6);
        r3.getMethod(((java.lang.String) r6[0]).intern(), byte[].class).invoke(r2, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x008d, code lost:
    
        return r2.digest();
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x008e, code lost:
    
        r9 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x008f, code lost:
    
        r1 = r9.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0093, code lost:
    
        if (r1 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0095, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0096, code lost:
    
        throw r9;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0040, code lost:
    
        r5 = r5 + 99;
        o.ec.e.d = r5 % 128;
        r5 = r5 % 2;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] c(byte[] r9) {
        /*
            int r0 = o.ec.e.a
            int r0 = r0 + 25
            int r1 = r0 % 128
            o.ec.e.d = r1
            r1 = 2
            int r0 = r0 % r1
            r0 = 0
            java.lang.String r2 = "\u0001\u0001\u0001\u0000\u0000"
            r3 = 10
            r4 = 5
            r5 = 14
            int[] r3 = new int[]{r3, r4, r5, r0}     // Catch: java.security.NoSuchAlgorithmException -> L97
            r4 = 1
            java.lang.Object[] r5 = new java.lang.Object[r4]     // Catch: java.security.NoSuchAlgorithmException -> L97
            f(r2, r3, r0, r5)     // Catch: java.security.NoSuchAlgorithmException -> L97
            r2 = r5[r0]     // Catch: java.security.NoSuchAlgorithmException -> L97
            java.lang.String r2 = (java.lang.String) r2     // Catch: java.security.NoSuchAlgorithmException -> L97
            java.lang.String r2 = r2.intern()     // Catch: java.security.NoSuchAlgorithmException -> L97
            java.security.MessageDigest r2 = java.security.MessageDigest.getInstance(r2)     // Catch: java.security.NoSuchAlgorithmException -> L97
            r2.reset()     // Catch: java.security.NoSuchAlgorithmException -> L97
            int r3 = o.ec.e.d
            int r3 = r3 + 95
            int r5 = r3 % 128
            o.ec.e.a = r5
            int r3 = r3 % r1
            r6 = 42
            if (r3 != 0) goto L3c
            r3 = 26
            goto L3d
        L3c:
            r3 = r6
        L3d:
            switch(r3) {
                case 26: goto L40;
                default: goto L40;
            }
        L40:
            int r5 = r5 + 99
            int r3 = r5 % 128
            o.ec.e.d = r3
            int r5 = r5 % r1
            java.lang.Object[] r9 = new java.lang.Object[]{r9}     // Catch: java.lang.Throwable -> L8e
            java.lang.String r3 = "\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001"
            r5 = 15
            r7 = 27
            r8 = 21
            int[] r5 = new int[]{r5, r7, r0, r8}     // Catch: java.lang.Throwable -> L8e
            java.lang.Object[] r7 = new java.lang.Object[r4]     // Catch: java.lang.Throwable -> L8e
            f(r3, r5, r4, r7)     // Catch: java.lang.Throwable -> L8e
            r3 = r7[r0]     // Catch: java.lang.Throwable -> L8e
            java.lang.String r3 = (java.lang.String) r3     // Catch: java.lang.Throwable -> L8e
            java.lang.String r3 = r3.intern()     // Catch: java.lang.Throwable -> L8e
            java.lang.Class r3 = java.lang.Class.forName(r3)     // Catch: java.lang.Throwable -> L8e
            java.lang.String r5 = "\u0000\u0001\u0000\u0001\u0001\u0001"
            r7 = 6
            int[] r1 = new int[]{r6, r7, r0, r1}     // Catch: java.lang.Throwable -> L8e
            java.lang.Object[] r6 = new java.lang.Object[r4]     // Catch: java.lang.Throwable -> L8e
            f(r5, r1, r4, r6)     // Catch: java.lang.Throwable -> L8e
            r1 = r6[r0]     // Catch: java.lang.Throwable -> L8e
            java.lang.String r1 = (java.lang.String) r1     // Catch: java.lang.Throwable -> L8e
            java.lang.String r1 = r1.intern()     // Catch: java.lang.Throwable -> L8e
            java.lang.Class[] r4 = new java.lang.Class[r4]     // Catch: java.lang.Throwable -> L8e
            java.lang.Class<byte[]> r5 = byte[].class
            r4[r0] = r5     // Catch: java.lang.Throwable -> L8e
            java.lang.reflect.Method r1 = r3.getMethod(r1, r4)     // Catch: java.lang.Throwable -> L8e
            r1.invoke(r2, r9)     // Catch: java.lang.Throwable -> L8e
            byte[] r9 = r2.digest()     // Catch: java.security.NoSuchAlgorithmException -> L97
            return r9
        L8e:
            r9 = move-exception
            java.lang.Throwable r1 = r9.getCause()     // Catch: java.security.NoSuchAlgorithmException -> L97
            if (r1 == 0) goto L96
            throw r1     // Catch: java.security.NoSuchAlgorithmException -> L97
        L96:
            throw r9     // Catch: java.security.NoSuchAlgorithmException -> L97
        L97:
            r9 = move-exception
            byte[] r9 = new byte[r0]
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ec.e.c(byte[]):byte[]");
    }

    public static byte[] d(byte[] bArr) {
        int i = d + 75;
        a = i % 128;
        int i2 = i % 2;
        try {
            Object[] objArr = new Object[1];
            f("\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{48, 6, 14, 0}, false, objArr);
            MessageDigest messageDigest = MessageDigest.getInstance(((String) objArr[0]).intern());
            messageDigest.reset();
            int i3 = a + Opcodes.LMUL;
            d = i3 % 128;
            int i4 = i3 % 2;
            try {
                Object[] objArr2 = new Object[1];
                f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{15, 27, 0, 21}, true, objArr2);
                Class<?> cls = Class.forName(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{42, 6, 0, 2}, true, objArr3);
                cls.getMethod(((String) objArr3[0]).intern(), byte[].class).invoke(messageDigest, bArr);
                try {
                    Object[] objArr4 = new Object[1];
                    f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{15, 27, 0, 21}, true, objArr4);
                    Class<?> cls2 = Class.forName(((String) objArr4[0]).intern());
                    Object[] objArr5 = new Object[1];
                    f("\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{54, 6, 0, 0}, false, objArr5);
                    return (byte[]) cls2.getMethod(((String) objArr5[0]).intern(), byte[].class).invoke(messageDigest, bArr);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 != null) {
                    throw cause2;
                }
                throw th2;
            }
        } catch (NoSuchAlgorithmException e2) {
            return new byte[0];
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x0060, code lost:
    
        r6 = new java.lang.Object[1];
        f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{15, 27, 0, 21}, true, r6);
        r4 = java.lang.Class.forName(((java.lang.String) r6[0]).intern());
        r6 = new java.lang.Object[1];
        f("\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{42, 6, 0, 2}, true, r6);
        r4.getMethod(((java.lang.String) r6[0]).intern(), byte[].class).invoke(r0, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00a7, code lost:
    
        return r0.digest();
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00a8, code lost:
    
        r9 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00a9, code lost:
    
        r0 = r9.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00ad, code lost:
    
        if (r0 != null) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00af, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00b0, code lost:
    
        throw r9;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] a(byte[] r9) {
        /*
            int r0 = o.ec.e.d
            int r0 = r0 + 53
            int r1 = r0 % 128
            o.ec.e.a = r1
            r1 = 2
            int r0 = r0 % r1
            r2 = 1
            r3 = 0
            if (r0 != 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r3
        L11:
            r4 = 14
            r5 = 48
            java.lang.String r6 = "\u0001\u0001\u0001\u0001\u0001\u0001"
            r7 = 6
            switch(r0) {
                case 0: goto L1c;
                default: goto L1b;
            }
        L1b:
            goto L38
        L1c:
            int[] r0 = new int[]{r5, r7, r4, r3}     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.Object[] r4 = new java.lang.Object[r2]     // Catch: java.security.NoSuchAlgorithmException -> L35
            f(r6, r0, r3, r4)     // Catch: java.security.NoSuchAlgorithmException -> L35
            r0 = r4[r3]     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.String r0 = (java.lang.String) r0     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.String r0 = r0.intern()     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.security.MessageDigest r0 = java.security.MessageDigest.getInstance(r0)     // Catch: java.security.NoSuchAlgorithmException -> L35
        L31:
            r0.reset()     // Catch: java.security.NoSuchAlgorithmException -> L35
            goto L4e
        L35:
            r9 = move-exception
            goto Lb1
        L38:
            int[] r0 = new int[]{r5, r7, r4, r3}     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.Object[] r4 = new java.lang.Object[r2]     // Catch: java.security.NoSuchAlgorithmException -> L35
            f(r6, r0, r3, r4)     // Catch: java.security.NoSuchAlgorithmException -> L35
            r0 = r4[r3]     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.String r0 = (java.lang.String) r0     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.lang.String r0 = r0.intern()     // Catch: java.security.NoSuchAlgorithmException -> L35
            java.security.MessageDigest r0 = java.security.MessageDigest.getInstance(r0)     // Catch: java.security.NoSuchAlgorithmException -> L35
            goto L31
        L4e:
            int r4 = o.ec.e.d
            int r4 = r4 + 37
            int r5 = r4 % 128
            o.ec.e.a = r5
            int r4 = r4 % r1
            if (r4 != 0) goto L5b
            r4 = r3
            goto L5c
        L5b:
            r4 = r2
        L5c:
            switch(r4) {
                case 1: goto L5f;
                default: goto L60;
            }
        L5f:
        L60:
            java.lang.Object[] r9 = new java.lang.Object[]{r9}     // Catch: java.lang.Throwable -> La8
            java.lang.String r4 = "\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001"
            r5 = 15
            r6 = 27
            r8 = 21
            int[] r5 = new int[]{r5, r6, r3, r8}     // Catch: java.lang.Throwable -> La8
            java.lang.Object[] r6 = new java.lang.Object[r2]     // Catch: java.lang.Throwable -> La8
            f(r4, r5, r2, r6)     // Catch: java.lang.Throwable -> La8
            r4 = r6[r3]     // Catch: java.lang.Throwable -> La8
            java.lang.String r4 = (java.lang.String) r4     // Catch: java.lang.Throwable -> La8
            java.lang.String r4 = r4.intern()     // Catch: java.lang.Throwable -> La8
            java.lang.Class r4 = java.lang.Class.forName(r4)     // Catch: java.lang.Throwable -> La8
            java.lang.String r5 = "\u0000\u0001\u0000\u0001\u0001\u0001"
            r6 = 42
            int[] r1 = new int[]{r6, r7, r3, r1}     // Catch: java.lang.Throwable -> La8
            java.lang.Object[] r6 = new java.lang.Object[r2]     // Catch: java.lang.Throwable -> La8
            f(r5, r1, r2, r6)     // Catch: java.lang.Throwable -> La8
            r1 = r6[r3]     // Catch: java.lang.Throwable -> La8
            java.lang.String r1 = (java.lang.String) r1     // Catch: java.lang.Throwable -> La8
            java.lang.String r1 = r1.intern()     // Catch: java.lang.Throwable -> La8
            java.lang.Class[] r2 = new java.lang.Class[r2]     // Catch: java.lang.Throwable -> La8
            java.lang.Class<byte[]> r5 = byte[].class
            r2[r3] = r5     // Catch: java.lang.Throwable -> La8
            java.lang.reflect.Method r1 = r4.getMethod(r1, r2)     // Catch: java.lang.Throwable -> La8
            r1.invoke(r0, r9)     // Catch: java.lang.Throwable -> La8
            byte[] r9 = r0.digest()     // Catch: java.security.NoSuchAlgorithmException -> L35
            return r9
        La8:
            r9 = move-exception
            java.lang.Throwable r0 = r9.getCause()     // Catch: java.security.NoSuchAlgorithmException -> L35
            if (r0 == 0) goto Lb0
            throw r0     // Catch: java.security.NoSuchAlgorithmException -> L35
        Lb0:
            throw r9     // Catch: java.security.NoSuchAlgorithmException -> L35
        Lb1:
            byte[] r9 = new byte[r3]
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ec.e.a(byte[]):byte[]");
    }

    public static byte[] c(String str) {
        int i = d + Opcodes.LSHR;
        a = i % 128;
        switch (i % 2 == 0 ? (char) 20 : (char) 21) {
            case 20:
                a(str.getBytes(j.c()));
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                byte[] a2 = a(str.getBytes(j.c()));
                int i2 = d + 55;
                a = i2 % 128;
                int i3 = i2 % 2;
                return a2;
        }
    }

    public static byte[] e(byte[] bArr) {
        try {
            Object[] objArr = new Object[1];
            f("\u0001\u0001\u0001", new int[]{60, 3, 0, 0}, true, objArr);
            MessageDigest messageDigest = MessageDigest.getInstance(((String) objArr[0]).intern());
            messageDigest.reset();
            int i = a + 31;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.DNEG;
            a = i4 % 128;
            int i5 = i4 % 2;
            try {
                Object[] objArr2 = new Object[1];
                f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{15, 27, 0, 21}, true, objArr2);
                Class<?> cls = Class.forName(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{42, 6, 0, 2}, true, objArr3);
                cls.getMethod(((String) objArr3[0]).intern(), byte[].class).invoke(messageDigest, bArr);
                return messageDigest.digest();
            } catch (Throwable th) {
                Throwable cause = th.getCause();
                if (cause != null) {
                    throw cause;
                }
                throw th;
            }
        } catch (NoSuchAlgorithmException e2) {
            return new byte[0];
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:88:0x010e, code lost:
    
        if (r0[r6.d] == 1) goto L56;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:101:0x02d3. Please report as an issue. */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v29, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 850
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ec.e.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
