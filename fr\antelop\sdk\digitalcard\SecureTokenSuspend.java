package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.p.e;
import o.p.i;
import o.v.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureTokenSuspend.smali */
public final class SecureTokenSuspend implements CustomerAuthenticatedProcess {
    private final o innerTokenSuspendProcess;

    public SecureTokenSuspend(o oVar) {
        this.innerTokenSuspendProcess = oVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerTokenSuspendProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.ee.o.d(this.innerTokenSuspendProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerTokenSuspendProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerTokenSuspendProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenSuspendProcess.e(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerTokenSuspendProcess));
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenSuspendProcess.e(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerTokenSuspendProcess));
    }
}
