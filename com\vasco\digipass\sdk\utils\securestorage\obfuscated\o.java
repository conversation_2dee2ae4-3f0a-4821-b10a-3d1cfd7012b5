package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.securestorage.initialization.SecureStorageInitCallback;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\o.smali */
public final class o implements h {
    public final /* synthetic */ SecureStorageInitCallback a;
    public final /* synthetic */ com.vasco.digipass.sdk.utils.securestorage.a b;

    public o(SecureStorageInitCallback secureStorageInitCallback, com.vasco.digipass.sdk.utils.securestorage.a aVar) {
        this.a = secureStorageInitCallback;
        this.b = aVar;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.h
    public final void a(l lVar) {
        if (lVar != null) {
            this.b.a(lVar);
        }
        this.a.onInitSuccess(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.h
    public final void onInitFailed(SecureStorageSDKException secureStorageSDKException) {
        Intrinsics.checkNotNullParameter(secureStorageSDKException, "secureStorageSDKException");
        this.a.onInitFailed(secureStorageSDKException);
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.h
    public final void a() {
        this.a.onInitFailed(new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_CORRUPTED_KEY_CORRUPTED, null, 2, null));
    }
}
