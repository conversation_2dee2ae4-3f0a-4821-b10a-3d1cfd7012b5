package kotlinx.coroutines.flow.internal;

import kotlin.Metadata;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

/* compiled from: AbstractSharedFlow.kt */
@Metadata(d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\"&\u0010\u0000\u001a\u0010\u0012\f\u0012\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00020\u00018\u0000X\u0081\u0004¢\u0006\n\n\u0002\u0010\u0006\u0012\u0004\b\u0004\u0010\u0005¨\u0006\u0007"}, d2 = {"EMPTY_RESUMES", "", "Lkotlin/coroutines/Continuation;", "", "getEMPTY_RESUMES$annotations", "()V", "[Lkotlin/coroutines/Continuation;", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\flow\internal\AbstractSharedFlowKt.smali */
public final class AbstractSharedFlowKt {
    public static final Continuation<Unit>[] EMPTY_RESUMES = new Continuation[0];

    public static /* synthetic */ void getEMPTY_RESUMES$annotations() {
    }
}
