package androidx.room;

import kotlin.Metadata;

/* compiled from: EntityUpsertionAdapter.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082T¢\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0082T¢\u0006\u0002\n\u0000¨\u0006\u0003"}, d2 = {"ErrorCode", "", "ErrorMsg", "room-runtime_release"}, k = 2, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\room\EntityUpsertionAdapterKt.smali */
public final class EntityUpsertionAdapterKt {
    private static final String ErrorCode = "1555";
    private static final String ErrorMsg = "unique";
}
