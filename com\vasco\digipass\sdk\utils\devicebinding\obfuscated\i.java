package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.security.keystore.KeyPermanentlyInvalidatedException;
import androidx.biometric.BiometricPrompt;
import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingBiometricAuthenticationCallback;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingWithBiometrics;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0001\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ(\u0010\n\u001a\u00020\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u0006H\u0016¨\u0006\u000f"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/i;", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingWithBiometrics;", "", "salt", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;", "deviceBindingBiometricAuthenticationCallback", "", "invalidateByBiometricEnrollment", "fallbackOnDeviceCredential", "", "fingerprint", "Landroidx/fragment/app/FragmentActivity;", "activity", "<init>", "(Landroidx/fragment/app/FragmentActivity;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\i.smali */
public final class i implements DeviceBindingWithBiometrics {
    private final FragmentActivity a;
    private final String b;

    public i(FragmentActivity activity) {
        Intrinsics.checkNotNullParameter(activity, "activity");
        this.a = activity;
        this.b = "OneSpan_DeviceBinding_SE_userAuthEnabled_";
    }

    @Override // com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingWithBiometrics
    public void fingerprint(String salt, DeviceBindingBiometricAuthenticationCallback deviceBindingBiometricAuthenticationCallback, boolean invalidateByBiometricEnrollment, boolean fallbackOnDeviceCredential) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        Intrinsics.checkNotNullParameter(deviceBindingBiometricAuthenticationCallback, "deviceBindingBiometricAuthenticationCallback");
        if (salt.length() == 0) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_NULL, null, 2, null);
        }
        if (salt.length() < 64) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_TOO_SHORT, null, 2, null);
        }
        c cVar = c.a;
        int a = cVar.a(this.a);
        if (a != 0) {
            cVar.a(this.a, a);
        }
        try {
            m mVar = m.a;
            SecretKey a2 = mVar.a(this.a, mVar.a(salt, this.b), invalidateByBiometricEnrollment, true);
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(a2);
            cVar.a(salt, this.a, deviceBindingBiometricAuthenticationCallback, new BiometricPrompt.CryptoObject(mac), fallbackOnDeviceCredential);
        } catch (KeyPermanentlyInvalidatedException e) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.BIOMETRIC_KEY_INVALIDATED, e);
        } catch (DeviceBindingSDKException e2) {
            throw e2;
        } catch (SecurityException e3) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.PERMISSION_DENIED, null, 2, null);
        } catch (Exception e4) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, e4);
        }
    }
}
