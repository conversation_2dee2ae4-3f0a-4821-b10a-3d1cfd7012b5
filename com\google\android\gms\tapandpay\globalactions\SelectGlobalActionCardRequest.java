package com.google.android.gms.tapandpay.globalactions;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\SelectGlobalActionCardRequest.smali */
public final class SelectGlobalActionCardRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<SelectGlobalActionCardRequest> CREATOR = new zzh();
    private int zza;
    private String zzb;
    private int zzc;

    private SelectGlobalActionCardRequest() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof SelectGlobalActionCardRequest) {
            SelectGlobalActionCardRequest selectGlobalActionCardRequest = (SelectGlobalActionCardRequest) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(selectGlobalActionCardRequest.zza)) && Objects.equal(this.zzb, selectGlobalActionCardRequest.zzb) && Objects.equal(Integer.valueOf(this.zzc), Integer.valueOf(selectGlobalActionCardRequest.zzc))) {
                return true;
            }
        }
        return false;
    }

    public String getCardId() {
        return this.zzb;
    }

    public int getCardType() {
        return this.zza;
    }

    public int getSelectionTimeoutMs() {
        return this.zzc;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), this.zzb, Integer.valueOf(this.zzc));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 2, getCardType());
        SafeParcelWriter.writeString(dest, 3, getCardId(), false);
        SafeParcelWriter.writeInt(dest, 4, getSelectionTimeoutMs());
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    SelectGlobalActionCardRequest(int i, String str, int i2) {
        this.zza = i;
        this.zzb = str;
        this.zzc = i2;
    }

    /* synthetic */ SelectGlobalActionCardRequest(zzg zzgVar) {
    }
}
