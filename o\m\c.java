package o.m;

import android.app.Activity;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Build;
import android.os.CancellationSignal;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.biometric.BiometricPrompt;
import androidx.fragment.app.FragmentActivity;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.R;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.Executor;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.a.n;
import o.ee.g;
import o.f.d;
import o.f.e;
import o.f.j;
import o.i.o;
import o.m.a;
import o.m.c;
import o.o.b;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\c.smali */
public final class c extends o.o.c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final int a;
    private static final int b;
    private static final int c;
    private static final int d;
    private static final int e;
    private static int j;
    private static long k;
    private static int m;

    /* renamed from: o, reason: collision with root package name */
    private static int f92o;
    private final String f;
    private final o g;
    private final String h;
    private final String i;

    static void g() {
        j = 874635458;
        k = -5661223985284849119L;
    }

    static void init$0() {
        $$a = new byte[]{26, 103, -21, 32};
        $$b = 6;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            byte[] r0 = o.m.c.$$a
            int r8 = 109 - r8
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L15:
            r3 = r2
        L16:
            byte r4 = (byte) r8
            int r6 = r6 + 1
            r1[r3] = r4
            if (r3 != r7) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r7 = r7 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.m.c.p(byte, byte, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        f92o = 1;
        g();
        KeyEvent.getDeadChar(0, 0);
        d = R.string.antelopScreenUnlockPromptName;
        e = R.string.antelopScreenUnlockPromptDefaultTitle;
        b = R.string.antelopScreenUnlockPromptDefaultSubtitle;
        c = R.string.antelopScreenUnlockPromptSubmitButtonLabel;
        a = R.drawable.antelopScreenUnlockPromptIcon;
        int i = f92o + 29;
        m = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    @Override // o.o.c
    public final boolean c() {
        switch (1) {
            case 1:
                return false;
            default:
                int i = f92o;
                int i2 = i + 63;
                m = i2 % 128;
                if (i2 % 2 != 0) {
                }
                int i3 = i + 63;
                m = i3 % 128;
                int i4 = i3 % 2;
                return true;
        }
    }

    private static String e(Context context) {
        String string;
        int i = f92o + 27;
        m = i % 128;
        char c2 = i % 2 != 0 ? '.' : 'B';
        Resources resources = context.getResources();
        switch (c2) {
            case '.':
                string = resources.getString(d);
                int i2 = 16 / 0;
                break;
            default:
                string = resources.getString(d);
                break;
        }
        int i3 = m + 27;
        f92o = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return string;
        }
    }

    private static String d(Context context) {
        int i = m + 63;
        f92o = i % 128;
        boolean z = i % 2 != 0;
        Resources resources = context.getResources();
        switch (z) {
            case false:
                resources.getString(e);
                throw null;
            default:
                String string = resources.getString(e);
                int i2 = f92o + 109;
                m = i2 % 128;
                int i3 = i2 % 2;
                return string;
        }
    }

    private static String b(Context context) {
        String string;
        int i = m + 7;
        f92o = i % 128;
        boolean z = i % 2 != 0;
        Resources resources = context.getResources();
        switch (z) {
            case false:
                string = resources.getString(b);
                int i2 = 40 / 0;
                break;
            default:
                string = resources.getString(b);
                break;
        }
        int i3 = m + 23;
        f92o = i3 % 128;
        switch (i3 % 2 == 0 ? '\'' : '-') {
            case '-':
                return string;
            default:
                throw null;
        }
    }

    private static String c(Context context) {
        int i = m + 35;
        f92o = i % 128;
        boolean z = i % 2 != 0;
        Resources resources = context.getResources();
        switch (z) {
            case true:
                return resources.getString(c);
            default:
                resources.getString(c);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public c(Context context, String str, String str2, o oVar) {
        super(e(context), a);
        if (str == null) {
            this.f = o.ee.o.e((CharSequence) d(context));
        } else {
            this.f = o.ee.o.e((CharSequence) str);
        }
        if (str2 == null) {
            this.h = o.ee.o.e((CharSequence) b(context));
        } else {
            this.h = o.ee.o.e((CharSequence) str2);
        }
        this.i = o.ee.o.e((CharSequence) c(context));
        this.g = oVar;
    }

    public final String j() {
        int i = f92o + Opcodes.DDIV;
        m = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.f;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String i() {
        int i = f92o + Opcodes.LNEG;
        int i2 = i % 128;
        m = i2;
        int i3 = i % 2;
        String str = this.h;
        int i4 = i2 + 79;
        f92o = i4 % 128;
        switch (i4 % 2 == 0 ? '^' : Typography.dollar) {
            case '$':
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String f() {
        int i = m;
        int i2 = i + 13;
        f92o = i2 % 128;
        int i3 = i2 % 2;
        String str = this.i;
        int i4 = i + 43;
        f92o = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* renamed from: o.m.c$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\c$1.smali */
    final class AnonymousClass1 implements b {
        private static int d = 0;
        private static int j = 1;
        private /* synthetic */ Handler b;
        private /* synthetic */ b c;
        private /* synthetic */ HandlerThread e;

        AnonymousClass1(Handler handler, b bVar, HandlerThread handlerThread) {
            this.b = handler;
            this.c = bVar;
            this.e = handlerThread;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void e(b bVar, e eVar, o.o.c cVar) {
            int i = d;
            int i2 = (i ^ 41) + ((i & 41) << 1);
            j = i2 % 128;
            char c = i2 % 2 == 0 ? '?' : 'A';
            bVar.b(eVar, cVar);
            switch (c) {
                case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                    int i3 = j + 57;
                    d = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            throw null;
                        default:
                            return;
                    }
                default:
                    throw null;
            }
        }

        @Override // o.o.b
        public final void b(final e eVar, final o.o.c cVar) {
            int i = j;
            int i2 = ((i | 43) << 1) - (i ^ 43);
            d = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    Handler handler = this.b;
                    final b bVar = this.c;
                    handler.post(new Runnable() { // from class: o.m.c$1$$ExternalSyntheticLambda1
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass1.e(b.this, eVar, cVar);
                        }
                    });
                    this.e.quitSafely();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    Handler handler2 = this.b;
                    final b bVar2 = this.c;
                    handler2.post(new Runnable() { // from class: o.m.c$1$$ExternalSyntheticLambda1
                        @Override // java.lang.Runnable
                        public final void run() {
                            c.AnonymousClass1.e(b.this, eVar, cVar);
                        }
                    });
                    this.e.quitSafely();
                    int i3 = j;
                    int i4 = (i3 & 57) + (i3 | 57);
                    d = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case true:
                            int i5 = 24 / 0;
                            return;
                        default:
                            return;
                    }
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void c(b bVar, o.o.e eVar, o.o.c cVar) {
            int i = j + 81;
            d = i % 128;
            int i2 = i % 2;
            bVar.c(eVar, cVar);
            int i3 = (j + 72) - 1;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // o.o.b
        public final void c(final o.o.e eVar, final o.o.c cVar) {
            int i = d + 89;
            j = i % 128;
            int i2 = i % 2;
            Handler handler = this.b;
            final b bVar = this.c;
            handler.post(new Runnable() { // from class: o.m.c$1$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() {
                    c.AnonymousClass1.c(b.this, eVar, cVar);
                }
            });
            this.e.quitSafely();
            int i3 = j;
            int i4 = ((i3 | 43) << 1) - (i3 ^ 43);
            d = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static /* synthetic */ void d(b bVar, o.o.c cVar) {
            int i = j;
            int i2 = ((i | 57) << 1) - (i ^ 57);
            d = i2 % 128;
            int i3 = i2 % 2;
            bVar.e(cVar);
            int i4 = d;
            int i5 = (i4 ^ 87) + ((i4 & 87) << 1);
            j = i5 % 128;
            int i6 = i5 % 2;
        }

        @Override // o.o.b
        public final void e(final o.o.c cVar) {
            int i = j;
            int i2 = ((i | 91) << 1) - (i ^ 91);
            d = i2 % 128;
            int i3 = i2 % 2;
            Handler handler = this.b;
            final b bVar = this.c;
            handler.post(new Runnable() { // from class: o.m.c$1$$ExternalSyntheticLambda2
                @Override // java.lang.Runnable
                public final void run() {
                    c.AnonymousClass1.d(b.this, cVar);
                }
            });
            this.e.quitSafely();
            int i4 = j;
            int i5 = ((i4 | 9) << 1) - (i4 ^ 9);
            d = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    int i6 = 5 / 0;
                    return;
                default:
                    return;
            }
        }
    }

    @Override // o.o.c
    public final void d(FragmentActivity fragmentActivity, int i, CancellationSignal cancellationSignal, b bVar) {
        Object[] objArr = new Object[1];
        l(6 - TextUtils.indexOf((CharSequence) "", '0', 0), "\ufffa\u000e￼\n\ufff6\u0007\ufff9\u0000", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 8, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 295, false, objArr);
        KeyguardManager keyguardManager = (KeyguardManager) fragmentActivity.getSystemService(((String) objArr[0]).intern());
        if (keyguardManager == null) {
            g.c();
            Object[] objArr2 = new Object[1];
            n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", MotionEvent.axisFromString("") + 1, objArr2);
            String intern = ((String) objArr2[0]).intern();
            Object[] objArr3 = new Object[1];
            l(View.MeasureSpec.getSize(0) + 15, "\t\u0016\t\u0014\u0003￡\u000e\uffef\u0019\u0001\f\u0010\u0013\t\u0004\u0012\u0005\u0007\u0001\u000e\u0001￭\u0004\u0012\u0001\u0015\u0007\u0019\u0005￫\uffc0\f\f\u0015￮\uffc0ￍ\uffc0\u0019\u0014", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 39, 285 - View.MeasureSpec.getMode(0), true, objArr3);
            g.e(intern, ((String) objArr3[0]).intern());
            bVar.c(o.o.e.b, this);
            return;
        }
        Object[] objArr4 = new Object[1];
        n("ꪘ\uaaf9䫆丄끺ℌ웏\uda08\ue856ⰹ\u0b8c壥봻勛嘒塪क껞\ue23d푩蔔㫧縳䁔ᄁ뛵ਬﱟ\ued2d˶蘴框礳黱ሼ\ue44e\uf537\uea86깠ိ䅏暍㩈谣", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1, objArr4);
        HandlerThread handlerThread = new HandlerThread(((String) objArr4[0]).intern());
        handlerThread.start();
        o.ee.b bVar2 = new o.ee.b(Looper.myLooper());
        int i2 = f92o + Opcodes.DMUL;
        m = i2 % 128;
        int i3 = i2 % 2;
        boolean isDeviceLocked = keyguardManager.isDeviceLocked();
        int i4 = f92o + 75;
        m = i4 % 128;
        int i5 = i4 % 2;
        g.c();
        Object[] objArr5 = new Object[1];
        n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr5);
        String intern2 = ((String) objArr5[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr6 = new Object[1];
        n("⭙⬽\u09ffݩ잰ꃍ藿鍫ꭨ宇䋦⼺㳇ᇇὩ⾤裘\uedf8꭫ꎬӀ禖㜗㟀部\uf5cd䌒讌泬䇐콃ᾓ\uf8f4\udd8e孁鎍瓫ꦤ\ue73f柮삕▲猫ﮨ岅놩Ｉ俻⢔ඪୢ쎧", ViewConfiguration.getMaximumFlingVelocity() >> 16, objArr6);
        g.d(intern2, sb.append(((String) objArr6[0]).intern()).append(isDeviceLocked).toString());
        AnonymousClass1 anonymousClass1 = new AnonymousClass1(bVar2, bVar, handlerThread);
        switch (!isDeviceLocked ? ' ' : '6') {
            case ' ':
                if (Build.VERSION.SDK_INT < 30) {
                    a(fragmentActivity, i, anonymousClass1, new o.ee.b(handlerThread.getLooper()));
                    return;
                }
                e(fragmentActivity, cancellationSignal, anonymousClass1, new o.ee.b(handlerThread.getLooper()));
                int i6 = f92o + 81;
                m = i6 % 128;
                switch (i6 % 2 != 0 ? '5' : (char) 3) {
                    case Opcodes.SALOAD /* 53 */:
                        throw null;
                    default:
                        return;
                }
            default:
                b(fragmentActivity, anonymousClass1, new o.ee.b(handlerThread.getLooper()));
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void c(BiometricPrompt biometricPrompt, FragmentActivity fragmentActivity) {
        int i = m + 65;
        f92o = i % 128;
        int i2 = i % 2;
        biometricPrompt.cancelAuthentication();
        fragmentActivity.finish();
        int i3 = m + Opcodes.DSUB;
        f92o = i3 % 128;
        int i4 = i3 % 2;
    }

    private void e(final FragmentActivity fragmentActivity, CancellationSignal cancellationSignal, final b bVar, final Handler handler) {
        g.c();
        Object[] objArr = new Object[1];
        n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", ViewConfiguration.getEdgeSlop() >> 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 29, "��\n\ufffb￫￼�\u0003\ufffb\u0007\u0004\u0006￭\u0005\u0007\n\uffde\u0003\ufffb\u0007\u0004\u0006￭\f\u000b�\r\t�\n\u0006", 31 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 292 - ExpandableListView.getPackedPositionChild(0L), true, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Objects.requireNonNull(handler);
        Executor executor = new Executor() { // from class: o.m.c$$ExternalSyntheticLambda0
            @Override // java.util.concurrent.Executor
            public final void execute(Runnable runnable) {
                handler.post(runnable);
            }
        };
        BiometricPrompt.PromptInfo build = new BiometricPrompt.PromptInfo.Builder().setTitle(this.f).setSubtitle(this.h).setAllowedAuthenticators(32783).build();
        final BiometricPrompt biometricPrompt = new BiometricPrompt(fragmentActivity, executor, new BiometricPrompt.AuthenticationCallback() { // from class: o.m.c.5
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static long a;
            private static int d;
            private static int e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                e = 0;
                d = 1;
                a = 2545212557644913633L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r6, short r7, byte r8, java.lang.Object[] r9) {
                /*
                    int r8 = r8 * 3
                    int r8 = r8 + 68
                    int r6 = r6 + 4
                    byte[] r0 = o.m.c.AnonymousClass5.$$a
                    int r7 = r7 * 3
                    int r7 = r7 + 1
                    byte[] r1 = new byte[r7]
                    int r7 = r7 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1b
                    r8 = r7
                    r3 = r1
                    r4 = r2
                    r7 = r6
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L36
                L1b:
                    r3 = r2
                L1c:
                    int r6 = r6 + 1
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    int r4 = r3 + 1
                    if (r3 != r7) goto L2d
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2d:
                    r3 = r0[r6]
                    r5 = r7
                    r7 = r6
                    r6 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r5
                L36:
                    int r8 = r8 + r6
                    r6 = r7
                    r7 = r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1c
                */
                throw new UnsupportedOperationException("Method not decompiled: o.m.c.AnonymousClass5.g(int, short, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{116, -79, 3, -53};
                $$b = Opcodes.IF_ACMPEQ;
            }

            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public final void onAuthenticationFailed() {
                int i = e + 47;
                d = i % 128;
                switch (i % 2 != 0) {
                    case false:
                        int i2 = 12 / 0;
                        break;
                }
            }

            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public final void onAuthenticationError(int i, CharSequence charSequence) {
                o.o.e eVar;
                g.c();
                Object[] objArr3 = new Object[1];
                f("鰠鱩፯ꒊ☡\uf0aa蠿銘聚\u318f\uf7fe줾䖵ﵴ芚封\udaa6橵\uef93\uef3f⾫읨碪爊벝㑛얍ԙᆄꅙ嚱ꠚ暜Ṑꎿ㬝ﮔ誨ೋ컪䣈\ue7bb駃凡\udde0咥", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("\uf3ae\uf3c2틺飯㰋鼵䦥껦䇻ෂ\ueddf팟⨱㳦뻺䘁땦ꮮ펢\uf512䀠ۺ䓏栱팞\uf5d6隆Ἱ縗惈櫋눡ए\udfcc鿌℘鐜䬩サ퓗", MotionEvent.axisFromString("") + 1, objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                switch (i) {
                    case 1:
                    case 2:
                    case 4:
                    case 5:
                    case 8:
                    case 12:
                        eVar = o.o.e.e;
                        break;
                    case 3:
                        eVar = o.o.e.d;
                        break;
                    case 6:
                    default:
                        eVar = o.o.e.j;
                        int i2 = d + 39;
                        e = i2 % 128;
                        if (i2 % 2 == 0) {
                            break;
                        } else {
                            break;
                        }
                    case 7:
                    case 9:
                        eVar = o.o.e.c;
                        break;
                    case 10:
                        eVar = o.o.e.a;
                        break;
                    case 11:
                    case 13:
                    case 14:
                        eVar = o.o.e.j;
                        break;
                }
                bVar.c(eVar, c.this);
                int i3 = d + 3;
                e = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public final void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult authenticationResult) {
                int i = e + 57;
                d = i % 128;
                int i2 = i % 2;
                g.c();
                Object[] objArr3 = new Object[1];
                f("鰠鱩፯ꒊ☡\uf0aa蠿銘聚\u318f\uf7fe줾䖵ﵴ芚封\udaa6橵\uef93\uef3f⾫읨碪爊벝㑛얍ԙᆄꅙ嚱ꠚ暜Ṑꎿ㬝ﮔ誨ೋ컪䣈\ue7bb駃凡\udde0咥", ViewConfiguration.getScrollBarSize() >> 8, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("赹贕蟋싡᱈\ue1e2Ე\uf4e8ᓊ埌출\uf35c哦槗\ue4f4時쮱ﺟ覬핑㻷友ề䡲귉ꃧꏒ㽺À㗹ヅ鉢矘諽엂ō\ueaccḉ檷\uf483妤猆ﾹ殊", 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                c.this.e(bVar);
                int i3 = d + 87;
                e = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return;
                    default:
                        int i4 = 12 / 0;
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
                /*
                    Method dump skipped, instructions count: 368
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.m.c.AnonymousClass5.f(java.lang.String, int, java.lang.Object[]):void");
            }
        });
        cancellationSignal.setOnCancelListener(new CancellationSignal.OnCancelListener() { // from class: o.m.c$$ExternalSyntheticLambda1
            @Override // android.os.CancellationSignal.OnCancelListener
            public final void onCancel() {
                c.c(BiometricPrompt.this, fragmentActivity);
            }
        });
        biometricPrompt.authenticate(build);
        int i = m + 47;
        f92o = i % 128;
        switch (i % 2 == 0 ? 'I' : '0') {
            case 'I':
                int i2 = 83 / 0;
                return;
            default:
                return;
        }
    }

    private void a(FragmentActivity fragmentActivity, int i, b bVar, Handler handler) {
        g.c();
        Object[] objArr = new Object[1];
        n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", TextUtils.indexOf("", "", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("짎즼郯꺝讎䉓ᳱ㪐㉴ឦ\ueb10持\ude50裶뚓授橍瓔ʆ\uef8c\ue643\ue0ff麢箷特況\ueaaf잶蹺\ud8e9暿厹ᩣ䓗\uf2ba\udf80陡ェ仜⯚∂볢\uda89랓븒⢻囉υ쨅钺ꋝ迍䘉Ê㻪ᯩ툷貅諩柶渰碎", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        fragmentActivity.getSupportFragmentManager().beginTransaction().replace(i, new a(this, false, b(bVar, handler))).addToBackStack(null).commit();
        int i2 = m + 79;
        f92o = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 14 : '#') {
            case '#':
                return;
            default:
                throw null;
        }
    }

    /* renamed from: o.m.c$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\c$3.smali */
    final class AnonymousClass3 extends KeyguardManager.KeyguardDismissCallback {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static long e;
        private static int j;
        private /* synthetic */ Handler b;
        private /* synthetic */ b d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            j = 1;
            e = 2684745690380492225L;
        }

        private static void g(short s, int i, int i2, Object[] objArr) {
            byte[] bArr = $$a;
            int i3 = 71 - (i2 * 3);
            int i4 = (i * 4) + 1;
            int i5 = s + 4;
            byte[] bArr2 = new byte[i4];
            int i6 = -1;
            int i7 = i4 - 1;
            if (bArr == null) {
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i6 = -1;
                i3 = i7 + i3;
                i7 = i7;
            }
            while (true) {
                i5++;
                int i8 = i6 + 1;
                bArr2[i8] = (byte) i3;
                if (i8 == i7) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b = bArr[i5];
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i6 = i8;
                i3 = b + i3;
                i7 = i7;
            }
        }

        static void init$0() {
            $$a = new byte[]{43, -103, 93, -106};
            $$b = 77;
        }

        AnonymousClass3(b bVar, Handler handler) {
            this.d = bVar;
            this.b = handler;
        }

        @Override // android.app.KeyguardManager.KeyguardDismissCallback
        public final void onDismissError() {
            int i = j + 69;
            a = i % 128;
            int i2 = i % 2;
            g.c();
            Object[] objArr = new Object[1];
            f("\ueaff\ueab6㮻麎Ἳꛋꓜ전黵뜎\u0a0b莤ɪ₠螞ࠛ럹尡㌗ﲥ㭴졼ꢮ慐곂䗏␉헃偛\uf14d况婀엃櫄촻컇䥋\ue67c祏데\uf297Ꮿ\uf6c7⟻昿轱", 1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("\ue846\ue834ば먴ﺲ괋聹⦝鱛볮⺹戋ß⭶ꌸ\ue9be땝埾ពᴺ㧟쎒谐胆깬丙Í㐂劶慠甛믳읧愞\ue990⽖䯵\ueda6巀勵\uf00cᠲ퉿", Gravity.getAbsoluteGravity(0, 0), objArr2);
            g.e(intern, ((String) objArr2[0]).intern());
            this.d.c(o.o.e.e, c.this);
            int i3 = a + 29;
            j = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    throw null;
                default:
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void c(b bVar) {
            int i = j + 65;
            a = i % 128;
            int i2 = i % 2;
            c.this.e(bVar);
            int i3 = j + Opcodes.LNEG;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.KeyguardManager.KeyguardDismissCallback
        public final void onDismissSucceeded() {
            int i = j + 59;
            a = i % 128;
            int i2 = i % 2;
            g.c();
            Object[] objArr = new Object[1];
            f("\ueaff\ueab6㮻麎Ἳꛋꓜ전黵뜎\u0a0b莤ɪ₠螞ࠛ럹尡㌗ﲥ㭴졼ꢮ慐곂䗏␉헃偛\uf14d况婀엃櫄촻컇䥋\ue67c祏데\uf297Ꮿ\uf6c7⟻昿轱", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("馓駡踫㓀徒ፐຍ袽\ued8eʵꁍ쌫焊锭ⷌ䢞쒈\ue9a5饢밚䠊緉ˤ⇦\udfb9\uf042踹锢⍣䓑ﯯ\u1ad3뚲\udf45杤蹶㨠叽팢\uf3d2臈ꙥ岜杊ᕇ㫻젅", View.MeasureSpec.getMode(0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            Handler handler = this.b;
            final b bVar = this.d;
            handler.post(new Runnable() { // from class: o.m.c$3$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() {
                    c.AnonymousClass3.this.c(bVar);
                }
            });
            int i3 = j + Opcodes.LNEG;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.KeyguardManager.KeyguardDismissCallback
        public final void onDismissCancelled() {
            int i = a + 33;
            j = i % 128;
            int i2 = i % 2;
            g.c();
            Object[] objArr = new Object[1];
            f("\ueaff\ueab6㮻麎Ἳꛋꓜ전黵뜎\u0a0b莤ɪ₠螞ࠛ럹尡㌗ﲥ㭴졼ꢮ慐곂䗏␉헃偛\uf14d况婀엃櫄촻컇䥋\ue67c祏데\uf297Ꮿ\uf6c7⟻昿轱", TextUtils.indexOf("", "", 0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("⓮⒜︨鮾ﬠ捓ꇳⰏ僳状༳枙챷\ue52e芲\uec2c秵馦㘜ᢨ\uf577්궚蕔拄聁ⅇ㆐鸞㓒咑빡\u0bcf꽆젚⫄蝝⏾籌坴㲸홦\uf3e2쏱꠲䫸杻", TextUtils.indexOf("", ""), objArr2);
            g.e(intern, ((String) objArr2[0]).intern());
            this.d.c(o.o.e.a, c.this);
            int i3 = a + 37;
            j = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 25 : 'M') {
                case 'M':
                    return;
                default:
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(java.lang.String r18, int r19, java.lang.Object[] r20) {
            /*
                Method dump skipped, instructions count: 362
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.m.c.AnonymousClass3.f(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    private void b(Activity activity, b bVar, Handler handler) {
        int i = m + Opcodes.LNEG;
        f92o = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("齓鼡ඝ\ue991퍖ᓎ膃綜꼆佾개㯙裍ᖄ\uf19f㭐㳐\ue9a6䖊띔냞綔\ud9af⍠ⓠ\uf1b5궬齘\ud8e0䖺↵୶䳵", View.getDefaultSize(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 7, "\ufffa\u000e￼\n\ufff6\u0007\ufff9\u0000", TextUtils.indexOf((CharSequence) "", '0', 0) + 9, 296 - (ViewConfiguration.getKeyRepeatDelay() >> 16), false, objArr3);
        KeyguardManager keyguardManager = (KeyguardManager) activity.getSystemService(((String) objArr3[0]).intern());
        switch (keyguardManager == null ? '=' : ';') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                int i3 = f92o + 1;
                m = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr4 = new Object[1];
                n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", KeyEvent.keyCodeFromString(""), objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                n("닪늘ዧ臶鉸㥷黹ᗻ끼๐쑻竷ꕴ૾駸穾ᅩ\uf6dcⷭ\uf67a鵧拮뇈扎ख़\ueecf엋\ude76\uf559嫀䧒䩘慌욚\udd92옕\ued44늷憫㈡奲㺁\uf5aa긼씽ꪧ禶ᨯ넦ᚗ趾阻㴫芅ᆂȟ", View.MeasureSpec.getSize(0), objArr5);
                g.e(intern2, ((String) objArr5[0]).intern());
                bVar.c(o.o.e.e, this);
                break;
            default:
                keyguardManager.requestDismissKeyguard(activity, new AnonymousClass3(bVar, handler));
                break;
        }
    }

    private void c(FragmentActivity fragmentActivity, int i, b bVar, Handler handler) {
        g.c();
        Object[] objArr = new Object[1];
        n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", TextUtils.indexOf("", "", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("ᤎ\u197c\ue994촣\ue4a9銓斊央䬏碁袮దຐ\uf18d픭ಯ몍ද愸肫㚃馝ﴝᒟꊽᖼ褞ꢧ庽ꆳԇ㲉쪨㶊鄅낉䚾䧐ⵦ䒼\uf29b얙륶\ud8f5滋减㕡泤\u1acf\uedc7셭\ue0a4雈秣嵓瓛˻\uf5fc\ue954ࣀ", Gravity.getAbsoluteGravity(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        fragmentActivity.getSupportFragmentManager().beginTransaction().replace(i, new a(this, true, b(bVar, handler))).addToBackStack(null).commit();
        int i2 = f92o + 89;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    final void e(b bVar) {
        try {
            byte[] d2 = this.g.e().d();
            g.c();
            Object[] objArr = new Object[1];
            n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", 1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            n("뮄믦ᬱ祐ⶵ〘霏\ued43릺놴㳅씣감̱慇얽᠅］핍䦲鑄歉䤹\udd9d9\ue70f㵣憈ﰤ匀녠\uf586栢콜╲禔\ue427뭧餜跪偏", TextUtils.indexOf("", "", 0, 0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            bVar.b(new j(e.d.c, new Date(), new d(d2)), this);
            int i = m + 43;
            f92o = i % 128;
            switch (i % 2 == 0 ? Typography.less : (char) 15) {
                case '<':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        } catch (o.r.b e2) {
            g.c();
            Object[] objArr3 = new Object[1];
            n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", (-1) - ExpandableListView.getPackedPositionChild(0L), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            n("曦暄ट抂ᥧ\ued7a蔡\uf691ꮔ蕦✗\uf1f1煲ᄟ窕\uf16f앧\ued13캟絠䤦祧勫\ue94e\udd46\uf521⚦啓⅂䄳ꪴ셕딎\udd25㺻䵚㥊ꥏ芋뤯贻╁ᛑ┺ᄦ녎髒鄵攩ഒ滗ᴲ\ue932饫", (-1) - TextUtils.indexOf((CharSequence) "", '0', 0), objArr4);
            g.a(intern2, ((String) objArr4[0]).intern(), e2);
            bVar.c(o.o.e.j, this);
        } catch (o.r.d e3) {
            g.c();
            Object[] objArr5 = new Object[1];
            n("蓿蒶醽ॾ\u1c8bཱུᶈ鵻㌭肔䳬\uf414鍪覦ᅮ\uf4ab❹疧ꕧ碕ꭴ\ue1ba㥞\ueca0㽂涉䵹傳썛\ud98b셅쒰坃䖂啋䢷\udb4bㇺ\ue93f변漗뷩紷\u20cb\uf33f⧷", KeyEvent.normalizeMetaState(0), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            n("䲁䳣렠复\uf1de윝㐞촞᪫淟Ი᥈嬕ꀠ䄚᧖\uef00尬\uf510闙捁졘楤Ƿ\uf721䐞ᴩ뷪ଥ\uf00c鄻⧬齩氚Դꗣጭᡰ뤄冖Ꝝ鑾ⵞ춃㭁qꅝ禌低밭啘\uf58b썕⡔줤懨圹ꑈ絿ᶿ\ueb63倅\uf17a覭罽찍敽ֿ\uf375碽ᦁ녜ލ\uf4b4趏ⵛ鮅悠Ɛ\ud902⿓᳭떉啄ꎍ袚⦧셹㟹Ґ\udda9絮䮹낊冰\ue922\udfa7Ⲃ얨攪厲\ud8e0秔ᄂ\ue7d6哯\uedd8负篕", (-1) - TextUtils.lastIndexOf("", '0', 0), objArr6);
            g.e(intern3, ((String) objArr6[0]).intern());
            bVar.c(o.o.e.b, this);
        }
    }

    /* renamed from: o.m.c$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\c$2.smali */
    final class AnonymousClass2 implements a.c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        private static int b;
        private static int i;
        private /* synthetic */ b c;
        private /* synthetic */ Handler e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            i = 1;
            a = new char[]{50935, 50879, 50851, 50851, 50877, 50860, 50834, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50836, 50854, 50852, 50851, 50838, 50839, 50858, 50852, 50848, 50848, 50860, 50860, 50852, 50856, 50854, 50855, 50838, 50863, 50854, 50852, 50851, 50838, 51180, 51157, 51181, 51159, 51161, 51176, 50740, 51178, 51161, 51182, 51181, 51183, 50747, 51155, 51163, 51183, 51154, 51180, 51141, 51180, 51157, 51157, 51176, 51163, 51147, 51176, 51183, 51176, 51176, 50741, 51155, 51163, 51183, 51154, 51180, 51141, 51157, 51176, 51173, 51163, 51157, 51147, 51180, 51183, 50718, 50733, 50718, 51155, 51163, 51161, 51160, 51154, 51154, 51161, 50747, 51178, 50844, 50770, 50770, 50794, 50780, 50757, 50769, 50799, 50768, 50778, 50773, 50768, 50870, 50833, 50833, 50864, 50793, 50779, 50783, 50775, 50799, 50788, 50796, 50782, 50778, 50798, 50798, 50770, 50768, 50752, 50757, 50768, 50771, 50775, 50771, 50799, 50771, 50775, 50780, 50780, 50797, 50796, 50774, 50770, 50778, 50778, 50798, 50798, 50770, 50768, 50752, 50754, 50793, 50793, 50771, 50797, 50782, 50783, 50770, 50775, 50939, 50851, 50851, 50853, 50849, 50849, 50857, 50838, 50838, 50855, 50854, 50856, 50852, 50860, 50860, 50848, 50848, 50852, 50858, 50842, 50836, 50851, 50851, 50853, 50855, 50832, 50833, 50852, 50857, 50855, 50852, 50852, 50876, 50838, 50847, 50859, 50849, 50858, 50860, 50863, 50858, 50824, 50923, 50923, 50826, 50851, 50861, 50833, 50857, 50849, 50878, 50854, 50832, 50860, 50848, 50848, 50852, 50858, 50842, 50836};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void g(int r6, int r7, int r8, java.lang.Object[] r9) {
            /*
                int r7 = 122 - r7
                int r6 = r6 * 3
                int r6 = 3 - r6
                int r8 = r8 * 2
                int r8 = 1 - r8
                byte[] r0 = o.m.c.AnonymousClass2.$$a
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r4 = r7
                r3 = r2
                r7 = r6
                goto L2e
            L17:
                r3 = r2
            L18:
                int r6 = r6 + 1
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r8) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r7
                r7 = r6
                r6 = r5
            L2e:
                int r4 = -r4
                int r6 = r6 + r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.m.c.AnonymousClass2.g(int, int, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{Base64.padSymbol, 89, 45, -101};
            $$b = Opcodes.DRETURN;
        }

        AnonymousClass2(b bVar, Handler handler) {
            this.c = bVar;
            this.e = handler;
        }

        @Override // o.m.a.c
        public final void a(o.o.e eVar) {
            int i2 = b + 7;
            i = i2 % 128;
            int i3 = i2 % 2;
            g.c();
            Object[] objArr = new Object[1];
            f("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 42, 0, 0}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(null, new int[]{42, 56, Opcodes.PUTSTATIC, 25}, true, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            this.c.c(eVar, c.this);
            int i4 = i + 73;
            b = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // o.m.a.c
        public final void a() {
            String intern;
            Object obj;
            int i2 = b + 35;
            i = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    g.c();
                    Object[] objArr = new Object[1];
                    f("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 42, 0, 0}, true, objArr);
                    intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    f("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000", new int[]{98, 60, 54, 38}, false, objArr2);
                    obj = objArr2[0];
                    break;
                default:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    f("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 42, 0, 0}, true, objArr3);
                    intern = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    f("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000", new int[]{98, 60, 54, 38}, true, objArr4);
                    obj = objArr4[0];
                    break;
            }
            g.d(intern, ((String) obj).intern());
            this.c.c(o.o.e.a, c.this);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void b(b bVar) {
            int i2 = b + 49;
            i = i2 % 128;
            int i3 = i2 % 2;
            c.this.e(bVar);
            int i4 = b + 7;
            i = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // o.m.a.c
        public final void d() {
            int i2 = b + 41;
            i = i2 % 128;
            int i3 = i2 % 2;
            g.c();
            Object[] objArr = new Object[1];
            f("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 42, 0, 0}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000", new int[]{Opcodes.IFLE, 60, 0, 7}, false, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            Handler handler = this.e;
            final b bVar = this.c;
            handler.post(new Runnable() { // from class: o.m.c$2$$ExternalSyntheticLambda0
                @Override // java.lang.Runnable
                public final void run() {
                    c.AnonymousClass2.this.b(bVar);
                }
            });
            int i4 = b + 43;
            i = i4 % 128;
            int i5 = i4 % 2;
        }

        private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
            int i2;
            char[] cArr;
            int i3;
            byte[] bArr;
            l lVar;
            char c;
            String str2 = str;
            int i4 = $10 + 61;
            int i5 = i4 % 128;
            $11 = i5;
            int i6 = 2;
            int i7 = i4 % 2;
            byte[] bArr2 = str2;
            if (str2 != null) {
                int i8 = i5 + 21;
                $10 = i8 % 128;
                if (i8 % 2 != 0) {
                    str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    throw null;
                }
                bArr2 = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            }
            byte[] bArr3 = bArr2;
            l lVar2 = new l();
            char c2 = 0;
            int i9 = iArr[0];
            int i10 = iArr[1];
            int i11 = iArr[2];
            int i12 = iArr[3];
            char[] cArr2 = a;
            char c3 = '0';
            switch (cArr2 == null) {
                case true:
                    break;
                default:
                    int length = cArr2.length;
                    char[] cArr3 = new char[length];
                    int i13 = 0;
                    while (true) {
                        switch (i13 < length ? c2 : (char) 1) {
                            case 1:
                                cArr2 = cArr3;
                                break;
                            default:
                                try {
                                    Object[] objArr2 = {Integer.valueOf(cArr2[i13])};
                                    Object obj = o.e.a.s.get(1951085128);
                                    if (obj != null) {
                                        bArr = bArr3;
                                        lVar = lVar2;
                                        c = 0;
                                    } else {
                                        Class cls = (Class) o.e.a.c(11 - (Process.myPid() >> 22), (char) (ViewConfiguration.getTouchSlop() >> 8), 42 - TextUtils.indexOf((CharSequence) "", '0'));
                                        byte b2 = (byte) 0;
                                        byte b3 = (byte) (b2 + 2);
                                        bArr = bArr3;
                                        lVar = lVar2;
                                        Object[] objArr3 = new Object[1];
                                        g(b2, b3, (byte) (b3 - 2), objArr3);
                                        c = 0;
                                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                        o.e.a.s.put(1951085128, obj);
                                    }
                                    cArr3[i13] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                    i13++;
                                    c2 = c;
                                    bArr3 = bArr;
                                    lVar2 = lVar;
                                    i6 = 2;
                                    c3 = '0';
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                        }
                    }
            }
            char[] cArr4 = new char[i10];
            System.arraycopy(cArr2, i9, cArr4, c2, i10);
            if (bArr3 != null) {
                int i14 = $10 + 1;
                $11 = i14 % 128;
                int i15 = i14 % i6;
                char[] cArr5 = new char[i10];
                lVar2.d = c2;
                char c4 = c2;
                while (lVar2.d < i10) {
                    if (bArr3[lVar2.d] == 1) {
                        int i16 = lVar2.d;
                        char c5 = cArr4[lVar2.d];
                        try {
                            Object[] objArr4 = new Object[i6];
                            objArr4[1] = Integer.valueOf(c4);
                            objArr4[c2] = Integer.valueOf(c5);
                            Object obj2 = o.e.a.s.get(2016040108);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(11 - (ViewConfiguration.getJumpTapTimeout() >> 16), (char) ((-1) - TextUtils.indexOf("", c3, c2)), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 448);
                                byte b4 = (byte) c2;
                                byte b5 = (byte) (b4 + 3);
                                Object[] objArr5 = new Object[1];
                                g(b4, b5, (byte) (b5 - 3), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2016040108, obj2);
                            }
                            cArr5[i16] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } else {
                        int i17 = lVar2.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr4[lVar2.d]), Integer.valueOf(c4)};
                            Object obj3 = o.e.a.s.get(804049217);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(Color.red(0) + 10, (char) (Process.getGidForName("") + 1), 207 - View.MeasureSpec.getMode(0));
                                byte b6 = (byte) 0;
                                byte b7 = b6;
                                Object[] objArr7 = new Object[1];
                                g(b6, b7, b7, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(804049217, obj3);
                            }
                            cArr5[i17] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    c4 = cArr5[lVar2.d];
                    try {
                        Object[] objArr8 = {lVar2, lVar2};
                        Object obj4 = o.e.a.s.get(-2112603350);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(';' - AndroidCharacter.getMirror('0'), (char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 259);
                            byte b8 = (byte) 0;
                            Object[] objArr9 = new Object[1];
                            g(b8, (byte) (b8 | 56), b8, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(-2112603350, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                        i6 = 2;
                        c2 = 0;
                        c3 = '0';
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                cArr4 = cArr5;
            }
            switch (i12 > 0 ? (char) 3 : (char) 17) {
                case 3:
                    char[] cArr6 = new char[i10];
                    i2 = 0;
                    System.arraycopy(cArr4, 0, cArr6, 0, i10);
                    int i18 = i10 - i12;
                    System.arraycopy(cArr6, 0, cArr4, i18, i12);
                    System.arraycopy(cArr6, i12, cArr4, 0, i18);
                    break;
                default:
                    i2 = 0;
                    break;
            }
            if (z) {
                char[] cArr7 = new char[i10];
                lVar2.d = i2;
                while (lVar2.d < i10) {
                    int i19 = $11 + 99;
                    $10 = i19 % 128;
                    switch (i19 % 2 != 0 ? ':' : '\t') {
                        case '\t':
                            cArr7[lVar2.d] = cArr4[(i10 - lVar2.d) - 1];
                            i3 = lVar2.d + 1;
                            break;
                        default:
                            cArr7[lVar2.d] = cArr4[(i10 >>> lVar2.d) << 1];
                            i3 = lVar2.d >> 0;
                            break;
                    }
                    lVar2.d = i3;
                }
                cArr = cArr7;
            } else {
                cArr = cArr4;
            }
            switch (i11 > 0 ? '+' : (char) 4) {
                case 4:
                    break;
                default:
                    int i20 = $11 + 65;
                    $10 = i20 % 128;
                    int i21 = i20 % 2;
                    int i22 = 0;
                    while (true) {
                        lVar2.d = i22;
                        switch (lVar2.d < i10 ? (char) 17 : '.') {
                            case '.':
                                break;
                            default:
                                cArr[lVar2.d] = (char) (cArr[lVar2.d] - iArr[2]);
                                i22 = lVar2.d + 1;
                        }
                    }
                    break;
            }
            objArr[0] = new String(cArr);
        }
    }

    private a.c b(b bVar, Handler handler) {
        AnonymousClass2 anonymousClass2 = new AnonymousClass2(bVar, handler);
        int i = f92o + Opcodes.DNEG;
        m = i % 128;
        switch (i % 2 == 0) {
            case true:
                return anonymousClass2;
            default:
                throw null;
        }
    }

    @Override // o.o.c
    public final boolean a(o.o.c cVar) {
        int i = f92o + 19;
        int i2 = i % 128;
        m = i2;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                switch (cVar == null ? '?' : '6') {
                    case '?':
                        int i3 = i2 + 91;
                        f92o = i3 % 128;
                        int i4 = i3 % 2;
                        return true;
                    default:
                        return false;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(int r19, java.lang.String r20, int r21, int r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 532
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.m.c.l(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }

    private static void n(String str, int i, Object[] objArr) {
        int i2 = $10 + 33;
        $11 = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                char[] charArray = str != null ? str.toCharArray() : str;
                n nVar = new n();
                char[] b2 = n.b(k ^ 8632603938177761503L, charArray, i);
                int i3 = 4;
                nVar.c = 4;
                while (nVar.c < b2.length) {
                    int i4 = $11 + 39;
                    $10 = i4 % 128;
                    int i5 = i4 % 2;
                    nVar.e = nVar.c - i3;
                    int i6 = nVar.c;
                    try {
                        Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % i3]), Long.valueOf(nVar.e), Long.valueOf(k)};
                        Object obj = o.e.a.s.get(-1945790373);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(ExpandableListView.getPackedPositionGroup(0L) + 11, (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.lastIndexOf("", '0', 0) + 44);
                            byte b3 = (byte) (-1);
                            byte b4 = (byte) (b3 + 1);
                            Object[] objArr3 = new Object[1];
                            p(b3, b4, (byte) (b4 | 41), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                            o.e.a.s.put(-1945790373, obj);
                        }
                        b2[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        try {
                            Object[] objArr4 = {nVar, nVar};
                            Object obj2 = o.e.a.s.get(-341518981);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(10 - (Process.myPid() >> 22), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 249 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                byte b5 = (byte) (-1);
                                byte b6 = (byte) (b5 + 1);
                                Object[] objArr5 = new Object[1];
                                p(b5, b6, (byte) (b6 | 38), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                o.e.a.s.put(-341518981, obj2);
                            }
                            ((Method) obj2).invoke(null, objArr4);
                            i3 = 4;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(b2, 4, b2.length - 4);
                return;
        }
    }
}
