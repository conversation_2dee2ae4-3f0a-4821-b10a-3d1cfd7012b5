package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.util.Util;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers.smali */
public final class ImmutableCollectionsSerializers {
    public static void addDefaultSerializers(Kryo kryo) {
        if (Util.isClassAvailable("java.util.ImmutableCollections")) {
            JdkImmutableListSerializer.addDefaultSerializers(kryo);
            JdkImmutableMapSerializer.addDefaultSerializers(kryo);
            JdkImmutableSetSerializer.addDefaultSerializers(kryo);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers$JdkImmutableListSerializer.smali */
    static class JdkImmutableListSerializer extends CollectionSerializer<List<Object>> {
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends List<Object>>) cls);
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Collection read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends List<Object>>) cls);
        }

        private JdkImmutableListSerializer() {
            setElementsCanBeNull(false);
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public List<Object> create(Kryo kryo, Input input, Class<? extends List<Object>> type, int size) {
            return new ArrayList(size);
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public List<Object> createCopy(Kryo kryo, List<Object> original) {
            return new ArrayList(original.size());
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public List<Object> read(Kryo kryo, Input input, Class<? extends List<Object>> type) {
            List<Object> list = (List) super.read(kryo, input, (Class) type);
            if (list == null) {
                return null;
            }
            return ImmutableCollectionsSerializers$JdkImmutableListSerializer$$ExternalSyntheticBackport0.m(list.toArray());
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public List<Object> copy(Kryo kryo, List<Object> original) {
            List<Object> copy = (List) super.copy(kryo, (Kryo) original);
            return ImmutableCollectionsSerializers$JdkImmutableListSerializer$$ExternalSyntheticBackport3.m(copy);
        }

        static void addDefaultSerializers(Kryo kryo) {
            List m;
            List m2;
            List m3;
            JdkImmutableListSerializer serializer = new JdkImmutableListSerializer();
            kryo.addDefaultSerializer(Collections.emptyList().getClass(), serializer);
            m = ImmutableCollectionsSerializers$JdkImmutableListSerializer$$ExternalSyntheticBackport0.m(new Object[]{1});
            kryo.addDefaultSerializer(m.getClass(), serializer);
            m2 = ImmutableCollectionsSerializers$JdkImmutableListSerializer$$ExternalSyntheticBackport0.m(new Object[]{1, 2, 3, 4});
            kryo.addDefaultSerializer(m2.getClass(), serializer);
            m3 = ImmutableCollectionsSerializers$JdkImmutableListSerializer$$ExternalSyntheticBackport0.m(new Object[]{1, 2, 3, 4});
            kryo.addDefaultSerializer(m3.subList(0, 2).getClass(), serializer);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers$JdkImmutableMapSerializer.smali */
    static class JdkImmutableMapSerializer extends MapSerializer<Map<Object, Object>> {
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer, com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Map<Object, Object>>) cls);
        }

        private JdkImmutableMapSerializer() {
            setKeysCanBeNull(false);
            setValuesCanBeNull(false);
        }

        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        protected Map<Object, Object> create(Kryo kryo, Input input, Class<? extends Map<Object, Object>> type, int size) {
            return new HashMap();
        }

        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        protected Map<Object, Object> createCopy(Kryo kryo, Map<Object, Object> original) {
            return new HashMap();
        }

        @Override // com.esotericsoftware.kryo.serializers.MapSerializer, com.esotericsoftware.kryo.Serializer
        public Map<Object, Object> read(Kryo kryo, Input input, Class<? extends Map<Object, Object>> type) {
            Map<Object, Object> map = super.read(kryo, input, type);
            if (map == null) {
                return null;
            }
            return ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport0.m(map);
        }

        @Override // com.esotericsoftware.kryo.serializers.MapSerializer, com.esotericsoftware.kryo.Serializer
        public Map<Object, Object> copy(Kryo kryo, Map<Object, Object> original) {
            Map<Object, Object> copy = super.copy(kryo, (Kryo) original);
            return ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport0.m(copy);
        }

        static void addDefaultSerializers(Kryo kryo) {
            Map m;
            Map m2;
            JdkImmutableMapSerializer serializer = new JdkImmutableMapSerializer();
            kryo.addDefaultSerializer(Collections.emptyMap().getClass(), serializer);
            m = ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport3.m(new Map.Entry[]{new AbstractMap.SimpleEntry(1, 2)});
            kryo.addDefaultSerializer(m.getClass(), serializer);
            m2 = ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport3.m(new Map.Entry[]{new AbstractMap.SimpleEntry(1, 2), new AbstractMap.SimpleEntry(3, 4)});
            kryo.addDefaultSerializer(m2.getClass(), serializer);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers$JdkImmutableSetSerializer.smali */
    static class JdkImmutableSetSerializer extends CollectionSerializer<Set<Object>> {
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Set<Object>>) cls);
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Collection read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Set<Object>>) cls);
        }

        private JdkImmutableSetSerializer() {
            setElementsCanBeNull(false);
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public Set<Object> create(Kryo kryo, Input input, Class<? extends Set<Object>> type, int size) {
            return new HashSet();
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public Set<Object> createCopy(Kryo kryo, Set<Object> original) {
            return new HashSet();
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public Set<Object> read(Kryo kryo, Input input, Class<? extends Set<Object>> type) {
            Set<Object> set = (Set) super.read(kryo, input, (Class) type);
            if (set == null) {
                return null;
            }
            return ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport0.m(set.toArray());
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public Set<Object> copy(Kryo kryo, Set<Object> original) {
            Set<Object> copy = (Set) super.copy(kryo, (Kryo) original);
            return ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport3.m(copy);
        }

        static void addDefaultSerializers(Kryo kryo) {
            Set m;
            Set m2;
            JdkImmutableSetSerializer serializer = new JdkImmutableSetSerializer();
            kryo.addDefaultSerializer(Collections.emptySet().getClass(), serializer);
            m = ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport0.m(new Object[]{1});
            kryo.addDefaultSerializer(m.getClass(), serializer);
            m2 = ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport0.m(new Object[]{1, 2, 3, 4});
            kryo.addDefaultSerializer(m2.getClass(), serializer);
        }
    }
}
