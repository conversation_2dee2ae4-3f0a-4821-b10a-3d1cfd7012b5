package com.vasco.digipass.sdk.utils.utilities.wbc;

import cz.muni.fi.xklinex.whiteboxAES.AES;
import cz.muni.fi.xklinex.whiteboxAES.T1Box;
import cz.muni.fi.xklinex.whiteboxAES.T2Box;
import cz.muni.fi.xklinex.whiteboxAES.T3Box;
import cz.muni.fi.xklinex.whiteboxAES.XORCascade;
import cz.muni.fi.xklinex.whiteboxAES.XORCascadeState;
import cz.muni.fi.xklinex.whiteboxAES.generator.ExternalBijections;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\wbc\WBCTable.smali */
public class WBCTable implements Serializable {
    public static final int BYTES = 16;
    public static final int ROUNDS = 10;
    public static final int T1BOXES = 2;
    private static final long serialVersionUID = -8735138231669345519L;
    protected ExternalBijections extc;
    protected T1Box[][] t1;
    protected T2Box[][] t2;
    protected T3Box[][] t3;
    protected XORCascade[][] xor;
    protected XORCascadeState[] xorState;

    public WBCTable() {
        this.extc = new ExternalBijections();
        this.t2 = (T2Box[][]) Array.newInstance((Class<?>) T2Box.class, 10, 16);
        this.t3 = (T3Box[][]) Array.newInstance((Class<?>) T3Box.class, 10, 16);
        this.xorState = new XORCascadeState[2];
        this.xor = (XORCascade[][]) Array.newInstance((Class<?>) XORCascade.class, 10, 8);
        this.t1 = (T1Box[][]) Array.newInstance((Class<?>) T1Box.class, 2, 16);
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        WBCTable wBCTable = (WBCTable) obj;
        return Objects.equals(this.extc, wBCTable.extc) && Arrays.deepEquals(this.t2, wBCTable.t2) && Arrays.deepEquals(this.t3, wBCTable.t3) && Arrays.deepEquals(this.xorState, wBCTable.xorState) && Arrays.deepEquals(this.xor, wBCTable.xor) && Arrays.deepEquals(this.t1, wBCTable.t1);
    }

    ExternalBijections getExternalBijections() {
        return this.extc;
    }

    public T1Box[][] getT1() {
        return this.t1;
    }

    public T2Box[][] getT2() {
        return this.t2;
    }

    public T3Box[][] getT3() {
        return this.t3;
    }

    public XORCascade[][] getXor() {
        return this.xor;
    }

    public XORCascadeState[] getXorState() {
        return this.xorState;
    }

    public int hashCode() {
        return (((((((((Objects.hash(this.extc) * 31) + Arrays.deepHashCode(this.t2)) * 31) + Arrays.deepHashCode(this.t3)) * 31) + Arrays.deepHashCode(this.xorState)) * 31) + Arrays.deepHashCode(this.xor)) * 31) + Arrays.deepHashCode(this.t1);
    }

    void populateTable(AES aes) {
        this.t1 = aes.getT1();
        this.xorState = aes.getXorState();
        this.t2 = aes.getT2();
        this.t3 = aes.getT3();
        this.xor = aes.getXor();
    }

    void setExternalBijections(ExternalBijections externalBijections) {
        this.extc = externalBijections;
    }

    public WBCTable(AES aes) {
        this.extc = new ExternalBijections();
        this.t2 = (T2Box[][]) Array.newInstance((Class<?>) T2Box.class, 10, 16);
        this.t3 = (T3Box[][]) Array.newInstance((Class<?>) T3Box.class, 10, 16);
        this.xorState = new XORCascadeState[2];
        this.xor = (XORCascade[][]) Array.newInstance((Class<?>) XORCascade.class, 10, 8);
        this.t1 = (T1Box[][]) Array.newInstance((Class<?>) T1Box.class, 2, 16);
        this.t1 = aes.getT1();
        this.xorState = aes.getXorState();
        this.t2 = aes.getT2();
        this.t3 = aes.getT3();
        this.xor = aes.getXor();
    }
}
