package o.cc;

import android.view.KeyEvent;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cc\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int c;
    private static int f;
    private byte[] b;
    private int d;
    private int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        f = 1;
        b();
        KeyEvent.getModifierMetaStateMask();
        int i = f + 57;
        c = i % 128;
        switch (i % 2 != 0 ? '_' : '\t') {
            case Opcodes.SWAP /* 95 */:
                throw null;
            default:
                return;
        }
    }

    static void b() {
        a = new int[]{-2076675374, -1035645958, -955969186, 596937524, 1485104805, 360858998, -1314015150, -1192539700, -884953848, -1655395270, 1866048837, 87458162, 1799339336, -2067360717, -1560468044, 489175373, 519431733, 2142471416};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x003a). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r0 = o.cc.a.$$a
            int r6 = r6 + 115
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r6 = r8
            r3 = r1
            r4 = r2
            r8 = r7
            r1 = r0
            r0 = r9
            r9 = r6
            goto L3a
        L1b:
            r3 = r2
            r5 = r8
            r8 = r6
            r6 = r5
        L1f:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r6 = r6 + 1
            if (r3 != r7) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L3a:
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.a.h(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{52, 109, 93, 87};
        $$b = 216;
    }

    public a(int i, int i2, byte[] bArr) {
        this.e = i;
        this.d = i2;
        this.b = bArr;
    }

    public final int c() {
        int i = c + 39;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                return this.e;
            default:
                int i2 = 24 / 0;
                return this.e;
        }
    }

    public final int e() {
        int i = c + 45;
        int i2 = i % 128;
        f = i2;
        switch (i % 2 != 0) {
            case true:
                int i3 = this.d;
                int i4 = i2 + 5;
                c = i4 % 128;
                int i5 = i4 % 2;
                return i3;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final byte[] d() {
        int i = f + 65;
        c = i % 128;
        switch (i % 2 != 0 ? 'L' : (char) 7) {
            case 7:
                return this.b;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.lang.String a() {
        /*
            r7 = this;
            int r0 = o.cc.a.f
            int r0 = r0 + 107
            int r1 = r0 % 128
            o.cc.a.c = r1
            int r0 = r0 % 2
            byte[] r0 = r7.b
            java.util.ArrayList r0 = o.ej.d.b(r0)
            java.util.Iterator r0 = r0.iterator()
        L14:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L1d
            r1 = 66
            goto L1f
        L1d:
            r1 = 84
        L1f:
            switch(r1) {
                case 84: goto L2f;
                default: goto L22;
            }
        L22:
            int r1 = o.cc.a.f
            int r1 = r1 + 31
            int r2 = r1 % 128
            o.cc.a.c = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L31
            goto L31
        L2f:
            r0 = 0
            return r0
        L31:
            java.lang.Object r1 = r0.next()
            o.ej.d r1 = (o.ej.d) r1
            java.lang.String r2 = r1.a()
            r3 = 1354275965(0x50b89c7d, float:2.47781069E10)
            r4 = 2010031811(0x77cea6c3, float:8.382777E33)
            int[] r3 = new int[]{r3, r4}
            r4 = 0
            int r4 = android.widget.ExpandableListView.getPackedPositionChild(r4)
            r5 = 1
            int r4 = 1 - r4
            java.lang.Object[] r6 = new java.lang.Object[r5]
            g(r3, r4, r6)
            r3 = 0
            r4 = r6[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            boolean r2 = r2.equals(r4)
            if (r2 == 0) goto Lcd
            o.ee.g.c()
            r0 = -988651951(0xffffffffc5125e51, float:-2341.8948)
            r2 = -1846069507(0xffffffff91f736fd, float:-3.9003618E-28)
            r4 = 725270770(0x2b3ac0f2, float:6.634824E-13)
            r6 = -1034505000(0xffffffffc256b4d8, float:-53.676605)
            int[] r0 = new int[]{r4, r6, r0, r2}
            java.lang.String r2 = ""
            int r2 = android.text.TextUtils.indexOf(r2, r2)
            int r2 = r2 + 6
            java.lang.Object[] r4 = new java.lang.Object[r5]
            g(r0, r2, r4)
            r0 = r4[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            r4 = 14
            int[] r4 = new int[r4]
            r4 = {x00d6: FILL_ARRAY_DATA , data: [-333713049, -1973629887, 2025119691, -631665066, 1839411240, 1762956113, -1733036168, 1296401909, -636204720, -103831934, -2044610661, 1484175156, -1351147597, 803694304} // fill-array
            int r6 = android.view.View.combineMeasuredStates(r3, r3)
            int r6 = r6 + 26
            java.lang.Object[] r5 = new java.lang.Object[r5]
            g(r4, r6, r5)
            r3 = r5[r3]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r3 = r1.d()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.String r2 = r2.toString()
            o.ee.g.d(r0, r2)
            java.lang.String r0 = r1.d()
            java.util.Locale r1 = o.ee.j.a()
            java.lang.String r0 = r0.toLowerCase(r1)
            java.lang.String r0 = o.ee.o.d(r0)
            return r0
        Lcd:
            goto L14
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.a.a():java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 846
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cc.a.g(int[], int, java.lang.Object[]):void");
    }
}
