package o.bi;

import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\d.smali */
public final class d {
    private static int c = 0;
    private static int e = 1;
    private final String a;
    private final String b;
    private final String d;

    public d(String str, String str2, String str3) {
        this.a = str;
        this.d = str2;
        this.b = str3;
    }

    public final String a() {
        int i = c;
        int i2 = (i + 32) - 1;
        e = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 3 : '/') {
            case '/':
                String str = this.a;
                int i3 = (i ^ 35) + ((i & 35) << 1);
                e = i3 % 128;
                int i4 = i3 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String e() {
        int i = e;
        int i2 = i + 11;
        c = i2 % 128;
        int i3 = i2 % 2;
        String str = this.d;
        int i4 = (i ^ 57) + ((i & 57) << 1);
        c = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String d() {
        int i = c + 91;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        String str = this.b;
        int i4 = (i2 & 39) + (i2 | 39);
        c = i4 % 128;
        switch (i4 % 2 != 0 ? Typography.dollar : '3') {
            case '$':
                throw null;
            default:
                return str;
        }
    }

    public final boolean equals(Object obj) {
        int i = e;
        int i2 = (i + 86) - 1;
        c = i2 % 128;
        if (i2 % 2 != 0) {
            throw null;
        }
        switch (this != obj) {
            case true:
                if (obj != null) {
                    switch (getClass() != obj.getClass()) {
                        case false:
                            d dVar = (d) obj;
                            switch (this.a.equals(dVar.a)) {
                                case false:
                                    break;
                                default:
                                    switch (this.d.equals(dVar.d) ? 'I' : 'Q') {
                                        case Opcodes.FASTORE /* 81 */:
                                            break;
                                        default:
                                            int i3 = c;
                                            int i4 = (i3 ^ 63) + ((i3 & 63) << 1);
                                            e = i4 % 128;
                                            int i5 = i4 % 2;
                                            switch (this.b.equals(dVar.b)) {
                                                case false:
                                                    break;
                                                default:
                                                    int i6 = c;
                                                    int i7 = (i6 ^ 91) + ((i6 & 91) << 1);
                                                    int i8 = i7 % 128;
                                                    e = i8;
                                                    if (i7 % 2 == 0) {
                                                    }
                                                    int i9 = (i8 & 3) + (i8 | 3);
                                                    c = i9 % 128;
                                                    int i10 = i9 % 2;
                                                    return true;
                                            }
                                    }
                            }
                            int i11 = e;
                            int i12 = (i11 ^ 109) + ((i11 & 109) << 1);
                            c = i12 % 128;
                            if (i12 % 2 == 0) {
                                return false;
                            }
                            throw null;
                    }
                }
                int i13 = c;
                int i14 = (i13 ^ Opcodes.DSUB) + ((i13 & Opcodes.DSUB) << 1);
                e = i14 % 128;
                int i15 = i14 % 2;
                return false;
            default:
                int i16 = (i & 25) + (i | 25);
                c = i16 % 128;
                switch (i16 % 2 != 0 ? '4' : 'Y') {
                    case '4':
                        return false;
                    default:
                        return true;
                }
        }
    }
}
