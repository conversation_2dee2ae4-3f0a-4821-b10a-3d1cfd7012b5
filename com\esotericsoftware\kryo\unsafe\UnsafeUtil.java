package com.esotericsoftware.kryo.unsafe;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import sun.misc.Unsafe;
import sun.nio.ch.DirectBuffer;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\unsafe\UnsafeUtil.smali */
public class UnsafeUtil {
    public static final long booleanArrayBaseOffset;
    public static final long byteArrayBaseOffset;
    public static final long charArrayBaseOffset;
    private static Method cleanMethod;
    private static Method cleanerMethod;
    private static Constructor<? extends ByteBuffer> directByteBufferConstructor;
    public static final long doubleArrayBaseOffset;
    public static final long floatArrayBaseOffset;
    public static final long intArrayBaseOffset;
    public static final long longArrayBaseOffset;
    public static final long shortArrayBaseOffset;
    public static final Unsafe unsafe;

    static {
        Unsafe tempUnsafe;
        Unsafe tempUnsafe2;
        Unsafe tempUnsafe3;
        long tempByteArrayBaseOffset;
        long tempByteArrayBaseOffset2 = 0;
        long tempFloatArrayBaseOffset = 0;
        long tempDoubleArrayBaseOffset = 0;
        long tempIntArrayBaseOffset = 0;
        long tempLongArrayBaseOffset = 0;
        long tempShortArrayBaseOffset = 0;
        long tempCharArrayBaseOffset = 0;
        long tempBooleanArrayBaseOffset = 0;
        try {
            if (!Util.isAndroid) {
                Field field = Unsafe.class.getDeclaredField("theUnsafe");
                field.setAccessible(true);
                tempUnsafe = (Unsafe) field.get(null);
                try {
                    tempByteArrayBaseOffset2 = tempUnsafe.arrayBaseOffset(byte[].class);
                    try {
                        tempByteArrayBaseOffset = tempUnsafe.arrayBaseOffset(char[].class);
                    } catch (Exception e) {
                        ex = e;
                    }
                    try {
                        tempShortArrayBaseOffset = tempUnsafe.arrayBaseOffset(short[].class);
                        tempIntArrayBaseOffset = tempUnsafe.arrayBaseOffset(int[].class);
                        tempFloatArrayBaseOffset = tempUnsafe.arrayBaseOffset(float[].class);
                        tempLongArrayBaseOffset = tempUnsafe.arrayBaseOffset(long[].class);
                        tempDoubleArrayBaseOffset = tempUnsafe.arrayBaseOffset(double[].class);
                        tempCharArrayBaseOffset = tempByteArrayBaseOffset;
                        tempBooleanArrayBaseOffset = tempUnsafe.arrayBaseOffset(boolean[].class);
                        tempUnsafe3 = tempUnsafe;
                        tempByteArrayBaseOffset2 = tempByteArrayBaseOffset2;
                    } catch (Exception e2) {
                        ex = e2;
                        tempCharArrayBaseOffset = tempByteArrayBaseOffset;
                        tempByteArrayBaseOffset2 = tempByteArrayBaseOffset2;
                        if (Log.DEBUG) {
                            tempUnsafe2 = tempUnsafe;
                            Log.debug("kryo", "Unsafe is not available.", ex);
                        } else {
                            tempUnsafe2 = tempUnsafe;
                        }
                        tempUnsafe3 = tempUnsafe2;
                        byteArrayBaseOffset = tempByteArrayBaseOffset2;
                        charArrayBaseOffset = tempCharArrayBaseOffset;
                        shortArrayBaseOffset = tempShortArrayBaseOffset;
                        intArrayBaseOffset = tempIntArrayBaseOffset;
                        floatArrayBaseOffset = tempFloatArrayBaseOffset;
                        longArrayBaseOffset = tempLongArrayBaseOffset;
                        doubleArrayBaseOffset = tempDoubleArrayBaseOffset;
                        booleanArrayBaseOffset = tempBooleanArrayBaseOffset;
                        unsafe = tempUnsafe3;
                        ByteBuffer buffer = ByteBuffer.allocateDirect(1);
                        Constructor declaredConstructor = buffer.getClass().getDeclaredConstructor(Long.TYPE, Integer.TYPE);
                        directByteBufferConstructor = declaredConstructor;
                        declaredConstructor.setAccessible(true);
                        Method method = DirectBuffer.class.getMethod("cleaner", new Class[0]);
                        cleanerMethod = method;
                        method.setAccessible(true);
                        cleanMethod = cleanerMethod.getReturnType().getMethod("clean", new Class[0]);
                    }
                } catch (Exception e3) {
                    ex = e3;
                }
            } else {
                if (Log.DEBUG) {
                    Log.debug("kryo", "Unsafe is not available on Android.");
                }
                tempUnsafe3 = null;
            }
        } catch (Exception e4) {
            ex = e4;
            tempUnsafe = null;
        }
        byteArrayBaseOffset = tempByteArrayBaseOffset2;
        charArrayBaseOffset = tempCharArrayBaseOffset;
        shortArrayBaseOffset = tempShortArrayBaseOffset;
        intArrayBaseOffset = tempIntArrayBaseOffset;
        floatArrayBaseOffset = tempFloatArrayBaseOffset;
        longArrayBaseOffset = tempLongArrayBaseOffset;
        doubleArrayBaseOffset = tempDoubleArrayBaseOffset;
        booleanArrayBaseOffset = tempBooleanArrayBaseOffset;
        unsafe = tempUnsafe3;
        ByteBuffer buffer2 = ByteBuffer.allocateDirect(1);
        try {
            Constructor declaredConstructor2 = buffer2.getClass().getDeclaredConstructor(Long.TYPE, Integer.TYPE);
            directByteBufferConstructor = declaredConstructor2;
            declaredConstructor2.setAccessible(true);
        } catch (Exception ex) {
            if (Log.DEBUG) {
                Log.debug("kryo", "No direct ByteBuffer constructor is available.", ex);
            }
            directByteBufferConstructor = null;
        }
        try {
            Method method2 = DirectBuffer.class.getMethod("cleaner", new Class[0]);
            cleanerMethod = method2;
            method2.setAccessible(true);
            cleanMethod = cleanerMethod.getReturnType().getMethod("clean", new Class[0]);
        } catch (Exception ex2) {
            if (Log.DEBUG) {
                Log.debug("kryo", "No direct ByteBuffer clean method is available.", ex2);
            }
            cleanerMethod = null;
        }
    }

    public static ByteBuffer newDirectBuffer(long address, int size) {
        Constructor<? extends ByteBuffer> constructor = directByteBufferConstructor;
        if (constructor == null) {
            throw new UnsupportedOperationException("No direct ByteBuffer constructor is available.");
        }
        try {
            return constructor.newInstance(Long.valueOf(address), Integer.valueOf(size));
        } catch (Exception ex) {
            throw new KryoException("Error creating a ByteBuffer at address: " + address, ex);
        }
    }

    public static boolean isNewDirectBufferAvailable() {
        return directByteBufferConstructor != null;
    }

    public static void dispose(ByteBuffer buffer) {
        Method method;
        if ((buffer instanceof DirectBuffer) && (method = cleanerMethod) != null) {
            try {
                cleanMethod.invoke(method.invoke(buffer, new Object[0]), new Object[0]);
            } catch (Throwable th) {
            }
        }
    }
}
