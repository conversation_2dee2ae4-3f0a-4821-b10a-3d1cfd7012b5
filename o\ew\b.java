package o.ew;

import android.content.Context;
import android.graphics.Color;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import o.a.l;
import o.ay.c;
import o.cs.d;
import o.ee.g;
import o.eg.e;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ew\b.smali */
public final class b extends o.ey.b<o.fi.a> {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static int e;
    private String d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        t();
        int i = a + 43;
        e = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void E(byte r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 66
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r6 = r6 + 4
            byte[] r0 = o.ew.b.$$g
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L17:
            r3 = r2
        L18:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ew.b.E(byte, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$g = new byte[]{91, -13, 111, 42};
        $$h = 39;
    }

    static void t() {
        b = new char[]{50939, 50853, 50857, 50849, 50863, 50838, 50855, 50851, 50853, 50852, 50854, 50851, 50848, 50839, 50836, 50849, 50878, 50855, 50857, 50859, 50826, 50756, 50760, 50752, 50767, 50767, 50780, 50782, 50781, 50754, 50754, 50752, 50754, 50764, 50865, 50752, 50776, 50780, 50780, 50864, 50878, 50758, 50758, 50943, 50856, 50856, 50838, 50838, 50848, 50878, 50848, 50854, 50860, 50860, 50852, 50853, 50848, 50876, 50849, 50857, 50853, 50852, 50854, 50932, 50873, 50873, 50855, 50855, 50862, 50862, 50850, 50859, 50856, 50851, 50876, 50852, 50857, 50857, 50854, 50839, 50837, 50851, 50849, 50851, 50854, 50863, 50855, 50877, 50858, 50852, 50878, 50858, 50693, 50699, 50693, 50703, 50698, 50691, 50694, 50811, 50806, 50689, 50808, 50801, 50691, 50699, 50699, 50717, 50695, 50808, 50803, 50692, 50700, 50808, 50938, 50855, 50858, 50855, 50853, 50838, 50838, 50852, 50852, 50851, 50851, 50857, 50831, 50923, 50923, 50831, 50859, 50851, 50854, 50863, 50852, 50854, 50859, 50854, 50855, 50830, 50824, 50853, 50850, 50875, 50820, 50826, 50855, 50830, 50831, 50848, 50876, 50827, 50829, 50853, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50927, 50817, 50876, 50851, 50836, 50836, 50876, 50838, 50837, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50834, 50860};
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ct.b c() {
        int i = a + 7;
        e = i % 128;
        int i2 = i % 2;
        d x = x();
        int i3 = a + 73;
        e = i3 % 128;
        int i4 = i3 % 2;
        return x;
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ey.a e() {
        int i = a + 53;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                u();
                throw null;
            default:
                return u();
        }
    }

    public b(String str, String str2, boolean z) {
        super(str, str2, z);
    }

    private static a u() {
        a aVar = new a();
        int i = a + 93;
        e = i % 128;
        int i2 = i % 2;
        return aVar;
    }

    private static d x() {
        d dVar = new d();
        int i = e + 47;
        a = i % 128;
        switch (i % 2 != 0) {
            case false:
                return dVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String r() {
        int i = a + 39;
        int i2 = i % 128;
        e = i2;
        Object obj = null;
        switch (i % 2 == 0 ? 'H' : ',') {
            case ',':
                String str = this.d;
                int i3 = i2 + 87;
                a = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return str;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void d(String str) {
        int i = e + Opcodes.LSUB;
        a = i % 128;
        char c = i % 2 != 0 ? '[' : 'X';
        this.d = str;
        switch (c) {
            case Opcodes.POP2 /* 88 */:
                return;
            default:
                throw null;
        }
    }

    @Override // o.ey.b, o.ey.e
    public final o.eg.b b(o.ek.b bVar) throws o.eg.d {
        int i = a + 63;
        e = i % 128;
        int i2 = i % 2;
        o.eg.b b2 = super.b(bVar);
        Object[] objArr = new Object[1];
        D("\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{0, 20, 0, 0}, false, objArr);
        b2.d(((String) objArr[0]).intern(), y());
        Object[] objArr2 = new Object[1];
        D("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{20, 23, 33, 0}, false, objArr2);
        b2.d(((String) objArr2[0]).intern(), c.c(this.d));
        int i3 = e + 29;
        a = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return b2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private o.eg.b y() throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        D("\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{43, 20, 0, 15}, true, objArr);
        bVar.d(((String) objArr[0]).intern(), this.d);
        e eVar = new e();
        if (j() != null) {
            int i = a + 33;
            e = i % 128;
            Object obj = null;
            if (i % 2 == 0) {
                f();
                obj.hashCode();
                throw null;
            }
            if (f() != null) {
                Iterator it = f().iterator();
                int i2 = a + 93;
                e = i2 % 128;
                int i3 = i2 % 2;
                while (true) {
                    switch (!it.hasNext()) {
                        case false:
                            int i4 = e + 19;
                            a = i4 % 128;
                            switch (i4 % 2 == 0) {
                                case false:
                                    eVar.b(((o.fi.a) it.next()).d(j().d()));
                                    obj.hashCode();
                                    throw null;
                                default:
                                    eVar.b(((o.fi.a) it.next()).d(j().d()));
                            }
                    }
                }
            }
        }
        Object[] objArr2 = new Object[1];
        D("\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{63, 28, 0, 0}, true, objArr2);
        bVar.d(((String) objArr2[0]).intern(), eVar);
        return bVar;
    }

    @Override // o.ey.e
    public final void a(Context context) {
        g.c();
        Object[] objArr = new Object[1];
        D("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{91, 23, 93, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        D("\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000", new int[]{114, 70, 0, 52}, false, objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), d()));
        List<T> f = f();
        if (f != 0) {
            int i = a + 77;
            e = i % 128;
            switch (i % 2 == 0) {
                case true:
                    f.iterator();
                    throw null;
                default:
                    Iterator it = f.iterator();
                    int i2 = e + 35;
                    a = i2 % 128;
                    switch (i2 % 2 != 0 ? 'T' : 'F') {
                    }
                    while (it.hasNext()) {
                        ((o.fc.d) it.next()).e(d(), new o.dd.e(context));
                        int i3 = e + 25;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                    }
                    break;
            }
        }
        c((List) null);
    }

    private static void D(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i;
        String str2 = str;
        int i2 = 2;
        byte[] bArr = str2;
        if (str2 != null) {
            int i3 = $10 + 45;
            $11 = i3 % 128;
            if (i3 % 2 == 0) {
                str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                throw null;
            }
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i4 = 0;
        int i5 = iArr[0];
        int i6 = 1;
        int i7 = iArr[1];
        int i8 = iArr[2];
        int i9 = iArr[3];
        char[] cArr2 = b;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i10 = 0;
            while (true) {
                switch (i10 < length ? i4 : i6) {
                    case 0:
                        int i11 = $10 + 3;
                        $11 = i11 % 128;
                        int i12 = i11 % i2;
                        try {
                            Object[] objArr2 = new Object[i6];
                            objArr2[i4] = Integer.valueOf(cArr2[i10]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr = cArr2;
                                i = length;
                            } else {
                                Class cls = (Class) o.e.a.c((-16777205) - Color.rgb(i4, i4, i4), (char) TextUtils.getTrimmedLength(""), 43 - View.getDefaultSize(i4, i4));
                                byte b2 = (byte) (-1);
                                cArr = cArr2;
                                i = length;
                                Object[] objArr3 = new Object[1];
                                E(b2, (byte) (b2 & 54), (byte) i4, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr3[i10] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i10++;
                            cArr2 = cArr;
                            length = i;
                            i2 = 2;
                            i4 = 0;
                            i6 = 1;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        cArr2 = cArr3;
                        break;
                }
            }
        }
        char[] cArr4 = new char[i7];
        System.arraycopy(cArr2, i5, cArr4, 0, i7);
        switch (bArr2 != null ? '(' : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                break;
            default:
                char[] cArr5 = new char[i7];
                lVar.d = 0;
                char c = 0;
                while (true) {
                    switch (lVar.d < i7 ? (char) 3 : ' ') {
                        case 3:
                            if (bArr2[lVar.d] == 1) {
                                int i13 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = o.e.a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(11 - TextUtils.getOffsetAfter("", 0), (char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 448);
                                        byte b3 = (byte) (-1);
                                        Object[] objArr5 = new Object[1];
                                        E(b3, (byte) (b3 & 53), (byte) 0, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj2);
                                    }
                                    cArr5[i13] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i14 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj3 = o.e.a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(10 - View.resolveSize(0, 0), (char) ExpandableListView.getPackedPositionGroup(0L), KeyEvent.getDeadChar(0, 0) + 207);
                                        byte b4 = (byte) (-1);
                                        Object[] objArr7 = new Object[1];
                                        E(b4, (byte) (b4 & 56), (byte) 0, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj3);
                                    }
                                    cArr5[i14] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c = cArr5[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj4 = o.e.a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getKeyRepeatDelay() >> 16), (char) View.resolveSizeAndState(0, 0, 0), 260 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)));
                                    byte b5 = (byte) (-1);
                                    byte b6 = (byte) (b5 + 1);
                                    Object[] objArr9 = new Object[1];
                                    E(b5, b6, b6, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        default:
                            cArr4 = cArr5;
                            break;
                    }
                }
        }
        switch (i9 > 0) {
            case false:
                break;
            default:
                int i15 = $11 + 85;
                $10 = i15 % 128;
                int i16 = i15 % 2;
                char[] cArr6 = new char[i7];
                System.arraycopy(cArr4, 0, cArr6, 0, i7);
                int i17 = i7 - i9;
                System.arraycopy(cArr6, 0, cArr4, i17, i9);
                System.arraycopy(cArr6, i9, cArr4, 0, i17);
                int i18 = $10 + 51;
                $11 = i18 % 128;
                if (i18 % 2 != 0) {
                    break;
                } else {
                    break;
                }
        }
        if (z) {
            int i19 = $10 + Opcodes.DNEG;
            $11 = i19 % 128;
            int i20 = i19 % 2;
            char[] cArr7 = new char[i7];
            int i21 = 0;
            while (true) {
                lVar.d = i21;
                if (lVar.d < i7) {
                    cArr7[lVar.d] = cArr4[(i7 - lVar.d) - 1];
                    i21 = lVar.d + 1;
                } else {
                    cArr4 = cArr7;
                }
            }
        }
        if (i8 > 0) {
            int i22 = $10 + 67;
            $11 = i22 % 128;
            int i23 = i22 % 2;
            int i24 = 0;
            while (true) {
                lVar.d = i24;
                if (lVar.d < i7) {
                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                    i24 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr4);
    }
}
