package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCardPushToIssuerNfcWallet.smali */
public final class SecureCardPushToIssuerNfcWallet implements CustomerAuthenticatedProcess {
    private final a innerSecureCardPushToIssuerNfcWallet;

    public SecureCardPushToIssuerNfcWallet(a aVar) {
        this.innerSecureCardPushToIssuerNfcWallet = aVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureCardPushToIssuerNfcWallet.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureCardPushToIssuerNfcWallet.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureCardPushToIssuerNfcWallet.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureCardPushToIssuerNfcWallet.k();
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureCardPushToIssuerNfcWallet.b(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureCardPushToIssuerNfcWallet));
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureCardPushToIssuerNfcWallet.b(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureCardPushToIssuerNfcWallet));
    }

    public final void requireTermsAndConditionsApproval(boolean z) {
        this.innerSecureCardPushToIssuerNfcWallet.b(z);
    }

    public final String getMessage() {
        return null;
    }
}
