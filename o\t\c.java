package o.t;

import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import kotlin.text.Typography;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\t\c.smali */
public final class c {
    private static int c;
    private static int e = 1;
    private static final Map<f, Date> d = new HashMap();

    static {
        c = 0;
        int i = e + 45;
        c = i % 128;
        switch (i % 2 != 0 ? (char) 30 : (char) 7) {
            case 7:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0031. Please report as an issue. */
    private synchronized void b(f fVar) {
        int i = c + 87;
        e = i % 128;
        int i2 = i % 2;
        if (c(fVar)) {
            d.put(fVar, new Date());
            int i3 = c;
            int i4 = (i3 ^ Opcodes.LSHL) + ((i3 & Opcodes.LSHL) << 1);
            e = i4 % 128;
            switch (i4 % 2 == 0 ? '=' : 'K') {
            }
        }
    }

    public final synchronized void b(List<f> list) {
        int i = e + 73;
        c = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                Iterator<f> it = list.iterator();
                int i2 = e;
                int i3 = (i2 & 29) + (i2 | 29);
                c = i3 % 128;
                int i4 = i3 % 2;
                while (true) {
                    switch (!it.hasNext()) {
                        case false:
                            int i5 = e;
                            int i6 = ((i5 | 51) << 1) - (i5 ^ 51);
                            c = i6 % 128;
                            switch (i6 % 2 != 0 ? '4' : '5') {
                                case '4':
                                    b(it.next());
                                    try {
                                        obj.hashCode();
                                        throw null;
                                    } catch (Throwable th) {
                                        throw th;
                                    }
                                default:
                                    b(it.next());
                            }
                        default:
                            int i7 = c;
                            int i8 = ((i7 | 11) << 1) - (i7 ^ 11);
                            e = i8 % 128;
                            switch (i8 % 2 == 0 ? Typography.greater : ')') {
                                case ')':
                                    break;
                                default:
                                    try {
                                        obj.hashCode();
                                        throw null;
                                    } catch (Throwable th2) {
                                        throw th2;
                                    }
                            }
                    }
                }
            default:
                list.iterator();
                try {
                    throw null;
                } catch (Throwable th3) {
                    throw th3;
                }
        }
    }

    public final synchronized List<f> e(int i) {
        ArrayList arrayList = new ArrayList();
        switch (i < 0 ? (char) 31 : (char) 22) {
            case 31:
                int i2 = c;
                int i3 = ((i2 | 89) << 1) - (i2 ^ 89);
                e = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        int i4 = 93 / 0;
                        return arrayList;
                    default:
                        return arrayList;
                }
            default:
                Date date = new Date(System.currentTimeMillis() - (i * 1000));
                Iterator<Map.Entry<f, Date>> it = d.entrySet().iterator();
                int i5 = (e + 96) - 1;
                c = i5 % 128;
                switch (i5 % 2 != 0 ? '9' : '-') {
                }
                while (true) {
                    switch (it.hasNext() ? '9' : 'P') {
                        case '9':
                            int i6 = e + Opcodes.DDIV;
                            c = i6 % 128;
                            switch (i6 % 2 != 0 ? (char) 22 : (char) 28) {
                                case 22:
                                    it.next().getValue().after(date);
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                                default:
                                    Map.Entry<f, Date> next = it.next();
                                    switch (!next.getValue().after(date)) {
                                        case false:
                                            arrayList.add(next.getKey());
                                            int i7 = c;
                                            int i8 = ((i7 | 59) << 1) - (i7 ^ 59);
                                            e = i8 % 128;
                                            int i9 = i8 % 2;
                                            break;
                                    }
                                    int i10 = c;
                                    int i11 = (i10 & 13) + (i10 | 13);
                                    e = i11 % 128;
                                    int i12 = i11 % 2;
                                    break;
                            }
                        default:
                            return arrayList;
                    }
                }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:27:0x007e. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0054  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static boolean c(o.i.f r6) {
        /*
            int r0 = o.t.c.e
            r1 = r0 | 97
            r2 = 1
            int r1 = r1 << r2
            r0 = r0 ^ 97
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.t.c.c = r0
            r0 = 2
            int r1 = r1 % r0
            if (r1 == 0) goto L14
            r1 = 98
            goto L16
        L14:
            r1 = 86
        L16:
            r3 = 3
            r4 = 0
            switch(r1) {
                case 98: goto L30;
                default: goto L1b;
            }
        L1b:
            o.i.f[] r1 = new o.i.f[r3]
            o.i.f r3 = o.i.f.c
            r1[r4] = r3
            o.i.f r3 = o.i.f.f
            r1[r2] = r3
            o.i.f r3 = o.i.f.d
            r1[r0] = r3
            boolean r6 = o.ee.o.a.c(r6, r1)
            if (r6 != 0) goto L4e
            goto L4b
        L30:
            o.i.f[] r1 = new o.i.f[r3]
            o.i.f r5 = o.i.f.c
            r1[r2] = r5
            o.i.f r5 = o.i.f.f
            r1[r2] = r5
            o.i.f r5 = o.i.f.d
            r1[r3] = r5
            boolean r6 = o.ee.o.a.c(r6, r1)
            if (r6 != 0) goto L46
            r6 = r2
            goto L47
        L46:
            r6 = r4
        L47:
            switch(r6) {
                case 1: goto L53;
                default: goto L4a;
            }
        L4a:
            goto L54
        L4b:
            r6 = 75
            goto L50
        L4e:
            r6 = 54
        L50:
            switch(r6) {
                case 54: goto L54;
                default: goto L53;
            }
        L53:
            goto L72
        L54:
            int r6 = o.t.c.e
            r1 = r6 & 85
            r6 = r6 | 85
            int r1 = r1 + r6
            int r6 = r1 % 128
            o.t.c.c = r6
            int r1 = r1 % r0
            if (r1 == 0) goto L65
            r6 = 56
            goto L67
        L65:
            r6 = 61
        L67:
            switch(r6) {
                case 61: goto L6b;
                default: goto L6a;
            }
        L6a:
            goto L6c
        L6b:
            return r4
        L6c:
            r6 = 65
            int r6 = r6 / r4
            return r4
        L70:
            r6 = move-exception
            throw r6
        L72:
            int r6 = o.t.c.c
            int r1 = r6 + 73
            int r3 = r1 % 128
            o.t.c.e = r3
            int r1 = r1 % r0
            if (r1 != 0) goto L7e
            r4 = r2
        L7e:
            switch(r4) {
                case 1: goto L81;
                default: goto L81;
            }
        L81:
            int r6 = r6 + 117
            int r1 = r6 % 128
            o.t.c.e = r1
            int r6 = r6 % r0
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.t.c.c(o.i.f):boolean");
    }
}
