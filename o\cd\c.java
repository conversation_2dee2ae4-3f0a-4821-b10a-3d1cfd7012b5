package o.cd;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.WalletNotificationServiceCallback;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import java.util.Date;
import java.util.List;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cd\c.smali */
public final class c implements WalletNotificationServiceCallback {
    private static int d = 0;
    private static int c = 1;

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onCardsUpdated(Context context) {
        int i = c;
        int i2 = (i & 89) + (i | 89);
        d = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 23 : '8') {
            case 23:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onCountersUpdated(Context context) {
        int i = c + 87;
        d = i % 128;
        int i2 = i % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onCustomerCredentialsReset(Context context) {
        int i = c + Opcodes.DMUL;
        d = i % 128;
        switch (i % 2 != 0 ? 'A' : (char) 0) {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                int i2 = 71 / 0;
                break;
        }
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onEmvApplicationActivationRequired(Context context, String str, List<EmvApplicationActivationMethod> list) {
        int i = d;
        int i2 = (i ^ 27) + ((i & 27) << 1);
        c = i2 % 128;
        switch (i2 % 2 == 0 ? 'E' : '`') {
            case 'E':
                int i3 = 64 / 0;
                break;
        }
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onEmvApplicationCredentialsUpdated(Context context) {
        int i = (d + Opcodes.IUSHR) - 1;
        c = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 78 / 0;
                break;
        }
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onLogout(Context context) {
        int i = c;
        int i2 = ((i | 33) << 1) - (i ^ 33);
        d = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onLostEligibility(Context context) {
        int i = (d + 96) - 1;
        c = i % 128;
        int i2 = i % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onSettingsUpdated(Context context) {
        int i = c + 89;
        d = i % 128;
        int i2 = i % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onSunsetScheduled(Context context, Date date) {
        int i = (c + 8) - 1;
        d = i % 128;
        switch (i % 2 != 0 ? Typography.greater : (char) 11) {
            case 11:
                return;
            default:
                throw null;
        }
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onWalletDeleted(Context context) {
        int i = d;
        int i2 = (i & 55) + (i | 55);
        c = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onWalletLoaded(Context context) {
        int i = c;
        int i2 = ((i | Opcodes.LNEG) << 1) - (i ^ Opcodes.LNEG);
        d = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onWalletLocked(Context context, WalletLockReason walletLockReason) {
        int i = d;
        int i2 = (i & 27) + (i | 27);
        c = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public final void onWalletUnlocked(Context context) {
        int i = c + 87;
        d = i % 128;
        switch (i % 2 != 0 ? 'V' : 'R') {
            case Opcodes.DASTORE /* 82 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }
}
