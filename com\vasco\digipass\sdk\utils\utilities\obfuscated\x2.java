package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x2.smali */
public class x2 {
    private byte[] a;
    private int b;

    public x2(byte[] bArr, int i) {
        this.a = Arrays.clone(bArr);
        this.b = i;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof x2)) {
            return false;
        }
        x2 x2Var = (x2) obj;
        if (x2Var.b != this.b) {
            return false;
        }
        return Arrays.areEqual(this.a, x2Var.a);
    }

    public int hashCode() {
        return this.b ^ Arrays.hashCode(this.a);
    }
}
