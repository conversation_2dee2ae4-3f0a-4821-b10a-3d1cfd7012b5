package o.fa;

import com.esotericsoftware.asm.Opcodes;
import o.ey.a;
import o.ey.d;
import o.fe.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fa\c.smali */
public final class c extends d<e, a> {
    private static int b = 0;
    private static int e = 1;

    @Override // o.ey.a
    public final /* synthetic */ o.fc.d b(o.fc.c cVar, short s) {
        int i = (e + 74) - 1;
        b = i % 128;
        int i2 = i % 2;
        e e2 = e(false, cVar, s);
        int i3 = e;
        int i4 = (i3 & 89) + (i3 | 89);
        b = i4 % 128;
        int i5 = i4 % 2;
        return e2;
    }

    @Override // o.ey.a
    public final /* synthetic */ o.ey.e e(String str, String str2, boolean z) {
        int i = b + Opcodes.LMUL;
        e = i % 128;
        int i2 = i % 2;
        a a = a(str, str2, z);
        int i3 = b;
        int i4 = ((i3 | 85) << 1) - (i3 ^ 85);
        e = i4 % 128;
        int i5 = i4 % 2;
        return a;
    }

    @Override // o.ey.a
    public final a.d d() {
        int i = b;
        int i2 = (i ^ 51) + ((i & 51) << 1);
        e = i2 % 128;
        int i3 = i2 % 2;
        a.d dVar = a.d.e;
        int i4 = e;
        int i5 = (i4 ^ 77) + ((i4 & 77) << 1);
        b = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                return dVar;
            default:
                throw null;
        }
    }

    private static a a(String str, String str2, boolean z) {
        a aVar = new a(str, str2, z);
        int i = (b + 6) - 1;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                return aVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static e e(boolean z, o.fc.c cVar, short s) {
        e eVar = new e(false, cVar, s);
        int i = b + 29;
        e = i % 128;
        int i2 = i % 2;
        return eVar;
    }
}
