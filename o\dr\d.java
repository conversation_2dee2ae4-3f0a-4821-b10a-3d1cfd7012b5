package o.dr;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.transaction.hce.TransactionType;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\d.smali */
public final class d implements o.ee.a<TransactionType> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final d a;
    public static final d b;
    public static final d c;
    public static final d d;
    public static final d e;
    private static final /* synthetic */ d[] f;
    private static int h;
    private static long i;
    private static int j;
    private final int g;

    static void e() {
        i = -1009160027267617722L;
    }

    static void init$0() {
        $$a = new byte[]{126, -85, 96, -58};
        $$b = 100;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dr.d.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r7 = r7 * 4
            int r7 = 4 - r7
            int r9 = r9 * 3
            int r9 = r9 + 68
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r7 = r7 + 1
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.d.l(int, byte, short, java.lang.Object[]):void");
    }

    private static /* synthetic */ d[] d() {
        d[] dVarArr;
        int i2 = j + 35;
        int i3 = i2 % 128;
        h = i3;
        switch (i2 % 2 == 0) {
            case true:
                dVarArr = new d[]{e, c, d, a, b};
                break;
            default:
                dVarArr = new d[]{c, e};
                dVarArr[3] = d;
                dVarArr[4] = a;
                dVarArr[2] = b;
                break;
        }
        int i4 = i3 + 75;
        j = i4 % 128;
        int i5 = i4 % 2;
        return dVarArr;
    }

    public static d valueOf(String str) {
        int i2 = h + 69;
        j = i2 % 128;
        int i3 = i2 % 2;
        d dVar = (d) Enum.valueOf(d.class, str);
        int i4 = j + 3;
        h = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    public static d[] values() {
        d[] dVarArr;
        int i2 = h + Opcodes.LMUL;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                dVarArr = (d[]) f.clone();
                break;
            default:
                dVarArr = (d[]) f.clone();
                int i3 = 35 / 0;
                break;
        }
        int i4 = h + 47;
        j = i4 % 128;
        int i5 = i4 % 2;
        return dVarArr;
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = j + 89;
        h = i2 % 128;
        int i3 = i2 % 2;
        TransactionType b2 = b();
        int i4 = j + Opcodes.LNEG;
        h = i4 % 128;
        int i5 = i4 % 2;
        return b2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        e();
        Object[] objArr = new Object[1];
        k("ꑬꐹ뇋崼궂瓛ﺇ㬢ᙧ쎶♺", TextUtils.getCapsMode("", 0, 0) + 1, objArr);
        e = new d(((String) objArr[0]).intern(), 0, 1);
        Object[] objArr2 = new Object[1];
        k("㫽㪭\ueaa1ٍ쪡Ꮱ옘ΰ裱飑䅄篺", TextUtils.indexOf("", "") + 1, objArr2);
        c = new d(((String) objArr2[0]).intern(), 1, 2);
        Object[] objArr3 = new Object[1];
        k("\u089d\u08cf睸鮄曶뾢ᑍ퇳몗ԝ", (ViewConfiguration.getEdgeSlop() >> 16) + 1, objArr3);
        d = new d(((String) objArr3[0]).intern(), 2, 3);
        Object[] objArr4 = new Object[1];
        k("춐췑쥌▁唛豤삁", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr4);
        a = new d(((String) objArr4[0]).intern(), 3, 4);
        Object[] objArr5 = new Object[1];
        k("뻷뺴嵎놶⣖\uf197꿏橬\u0cd1⼪ꌣረ", 1 - TextUtils.getOffsetBefore("", 0), objArr5);
        b = new d(((String) objArr5[0]).intern(), 4, 5);
        f = d();
        int i2 = h + 83;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    private d(String str, int i2, int i3) {
        this.g = i3;
    }

    public final int c() {
        int i2 = h + 79;
        int i3 = i2 % 128;
        j = i3;
        switch (i2 % 2 == 0 ? 'I' : '?') {
            case '?':
                int i4 = this.g;
                int i5 = i3 + 83;
                h = i5 % 128;
                int i6 = i5 % 2;
                return i4;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static d a(int i2) {
        d[] values;
        int length;
        int i3 = j + Opcodes.LSHL;
        h = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                values = values();
                length = values.length;
                break;
            default:
                values = values();
                length = values.length;
                break;
        }
        int i4 = 0;
        while (true) {
            Object obj = null;
            if (i4 >= length) {
                return null;
            }
            d dVar = values[i4];
            if (dVar.g == i2) {
                int i5 = j;
                int i6 = i5 + 41;
                h = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        int i7 = i5 + Opcodes.DREM;
                        h = i7 % 128;
                        if (i7 % 2 == 0) {
                            return dVar;
                        }
                        obj.hashCode();
                        throw null;
                }
            }
            i4++;
            int i8 = j + 85;
            h = i8 % 128;
            int i9 = i8 % 2;
        }
    }

    /* renamed from: o.dr.d$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\d$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        static final /* synthetic */ int[] c;
        private static int d;
        private static int e;

        static {
            d = 0;
            e = 1;
            int[] iArr = new int[d.values().length];
            c = iArr;
            try {
                iArr[d.e.ordinal()] = 1;
                int i = e;
                int i2 = ((i | 109) << 1) - (i ^ 109);
                d = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[d.c.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[d.d.ordinal()] = 3;
                int i4 = e;
                int i5 = (i4 & 77) + (i4 | 77);
                d = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                c[d.a.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                c[d.b.ordinal()] = 5;
                int i7 = d;
                int i8 = (i7 ^ Opcodes.LMUL) + ((i7 & Opcodes.LMUL) << 1);
                e = i8 % 128;
                switch (i8 % 2 == 0 ? '3' : 'D') {
                    case 'D':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    public final TransactionType b() {
        switch (AnonymousClass4.c[ordinal()]) {
            case 1:
                TransactionType transactionType = TransactionType.Unknown;
                int i2 = h + Opcodes.DMUL;
                j = i2 % 128;
                int i3 = i2 % 2;
                return transactionType;
            case 2:
                TransactionType transactionType2 = TransactionType.Purchase;
                int i4 = j + 67;
                h = i4 % 128;
                switch (i4 % 2 != 0 ? Typography.less : '.') {
                    case '.':
                        return transactionType2;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            case 3:
                return TransactionType.Refund;
            case 4:
                return TransactionType.ATM;
            case 5:
                return TransactionType.CashBack;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                k("閐闅⍄쾳ᒇ췐ꋌ板➄儫齲ἤ\uf13d麶⥝䶺苝찚\ue4ac믭尺禺", -TextUtils.lastIndexOf("", '0', 0), objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.d.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
