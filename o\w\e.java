package o.w;

import android.graphics.PointF;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import o.ei.a;
import o.ei.c;
import o.i.i;
import o.p.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\w\e.smali */
public abstract class e extends h<o.dl.e> {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static int h;
    private static int[] i;
    private static int l;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        l = 1;
        z();
        ViewConfiguration.getScrollBarSize();
        int i2 = l + 53;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int i3 = 38 / 0;
                break;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void I(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 4
            byte[] r0 = o.w.e.$$g
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = r7 + 115
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1c:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = -r8
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.w.e.I(int, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$g = new byte[]{46, 74, -29, UtilitiesSDKConstants.SRP_LABEL_MAC};
        $$h = 215;
    }

    static void z() {
        i = new int[]{863913463, -273538149, -461529055, -677542517, -311304966, 1393120458, 1296232988, 1952685655, 2021273628, -808726213, 551963116, -1349033743, 1771992962, 1331192973, -2046941242, 1320881888, 952823437, -726888108};
    }

    @Override // o.p.h
    public final /* synthetic */ o.dl.e r() {
        int i2 = l + 59;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                o.dl.e a = a();
                int i3 = h + 71;
                l = i3 % 128;
                int i4 = i3 % 2;
                return a;
            default:
                a();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public e(String str, boolean z) {
        super(i.d, str, false);
    }

    @Override // o.p.h
    public final void d(c cVar) throws WalletValidationException {
        int i2 = h + 79;
        l = i2 % 128;
        int i3 = i2 % 2;
        if (cVar.e().e().b(a.a)) {
            int i4 = h + 87;
            l = i4 % 128;
            int i5 = i4 % 2;
        } else {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
            Object[] objArr = new Object[1];
            H(new int[]{-1895597401, 2017169847, 1531606311, -1304129892}, 7 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            H(new int[]{1294904574, -1520139427, -693691703, 2026037356, -1895597401, 2017169847, 843057987, -398308851, -326321402, -291829969, -1175950014, 1900773351, 150834821, -986859140, 372781372, 340605647, -379985896, -974688495, -1247368305, -267547982, 1570367268, 67034505}, 43 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
    }

    @Override // o.p.h
    public final int a(c cVar) {
        int i2 = h + Opcodes.LSHR;
        l = i2 % 128;
        int i3 = i2 % 2;
        int a = cVar.e().c().a();
        int i4 = l + 61;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return a;
        }
    }

    private o.dl.e a() {
        boolean z = this.b;
        return new o.dl.e(!this.d);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void H(int[] r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 868
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.w.e.H(int[], int, java.lang.Object[]):void");
    }
}
