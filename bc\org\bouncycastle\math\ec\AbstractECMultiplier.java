package bc.org.bouncycastle.math.ec;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\AbstractECMultiplier.smali */
public abstract class AbstractECMultiplier implements ECMultiplier {
    protected ECPoint a(ECPoint eCPoint) {
        return ECAlgorithms.a(eCPoint);
    }

    protected abstract ECPoint a(ECPoint eCPoint, BigInteger bigInteger);

    @Override // bc.org.bouncycastle.math.ec.ECMultiplier
    public ECPoint multiply(ECPoint eCPoint, BigInteger bigInteger) {
        int signum = bigInteger.signum();
        if (signum == 0 || eCPoint.isInfinity()) {
            return eCPoint.getCurve().getInfinity();
        }
        ECPoint a = a(eCPoint, bigInteger.abs());
        if (signum <= 0) {
            a = a.negate();
        }
        return a(a);
    }
}
