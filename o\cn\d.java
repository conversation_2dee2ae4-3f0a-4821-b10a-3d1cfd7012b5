package o.cn;

import o.cq.e;
import o.et.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cn\d.smali */
public final class d extends e<i> {
    private static int a = 0;
    private static int c = 1;

    @Override // o.cq.e
    public final /* synthetic */ i a(String str, String str2, int i, String str3) {
        int i2 = (c + 4) - 1;
        a = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 16 : ';') {
            case 16:
                e(str, str2, i, str3);
                throw null;
            default:
                return e(str, str2, i, str3);
        }
    }

    private static i e(String str, String str2, int i, String str3) {
        i iVar = new i(str, str2, i, str3);
        int i2 = a;
        int i3 = ((i2 | 85) << 1) - (i2 ^ 85);
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return iVar;
        }
    }
}
