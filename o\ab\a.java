package o.ab;

import android.content.Context;
import android.graphics.PointF;
import android.os.Process;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.text.Typography;
import o.bb.d;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ab\a.smali */
public final class a extends o.y.b<b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static int d;
    private static long e;
    String b;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ab\a$b.smali */
    public interface b {
        void a(d dVar);

        void b();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        n();
        PointF.length(0.0f, 0.0f);
        Process.getElapsedCpuTime();
        int i = c + 93;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{18, UtilitiesSDKConstants.SRP_LABEL_MAC, -55, -33};
        $$e = 91;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = r9 + 4
            byte[] r0 = o.ab.a.$$d
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = 114 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L1a:
            r3 = r2
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r7 = r7 + r8
            int r8 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ab.a.l(int, int, int, java.lang.Object[]):void");
    }

    static void n() {
        e = 1270968930752498853L;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = d + 71;
        c = i % 128;
        int i2 = i % 2;
        AsyncTaskC0015a m = m();
        int i3 = d + Opcodes.LMUL;
        c = i3 % 128;
        int i4 = i3 % 2;
        return m;
    }

    public a(Context context, b bVar, c cVar) {
        super(context, bVar, cVar, e.n);
    }

    public final void e(String str) {
        g.c();
        Object[] objArr = new Object[1];
        k("䩱향疳閏㖕喙\uf5c8ᗧ뗯헌痤闝㔤唩\uf532ᔀ딁", KeyEvent.keyCodeFromString("") + 40949, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("䩑\ufb3f⢻广迍㲩戟鎓셞盙ꞵ픆᪩䠹煉⺽射跲㍋恪醥윓璻멂\ueb28ᢄ丒￦ⵜ剤菙ㄮ", MotionEvent.axisFromString("") + 45414, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        this.b = str;
        c();
        int i = d + 69;
        c = i % 128;
        int i2 = i % 2;
    }

    private AsyncTaskC0015a m() {
        AsyncTaskC0015a asyncTaskC0015a = new AsyncTaskC0015a(this);
        int i = d + 19;
        c = i % 128;
        int i2 = i % 2;
        return asyncTaskC0015a;
    }

    @Override // o.y.b
    public final String a() {
        int i = d + 25;
        c = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k("䩱향疳閏㖕喙\uf5c8ᗧ뗯헌痤闝㔤唩\uf532ᔀ딁", 40949 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = d + 35;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return intern;
        }
    }

    /* renamed from: o.ab.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ab\a$a.smali */
    static final class AsyncTaskC0015a extends o.y.c<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static long b;
        private static int d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            d = 1;
            b = 7290165515975433896L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(int r6, short r7, byte r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 4
                int r6 = 1 - r6
                int r7 = r7 * 3
                int r7 = r7 + 4
                int r8 = r8 * 3
                int r8 = 71 - r8
                byte[] r0 = o.ab.a.AsyncTaskC0015a.$$d
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r4 = r8
                r3 = r2
                r8 = r7
                r7 = r6
                goto L30
            L1a:
                r3 = r2
            L1b:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r6) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r8
                r8 = r5
            L30:
                int r6 = r6 + r4
                int r8 = r8 + 1
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r5
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ab.a.AsyncTaskC0015a.B(int, short, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{114, -113, -41, 111};
            $$e = 26;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = a + 15;
            d = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = a + 71;
            d = i % 128;
            switch (i % 2 != 0) {
                case true:
                    break;
                default:
                    int i2 = 37 / 0;
                    break;
            }
        }

        AsyncTaskC0015a(a aVar) {
            super(aVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = a + 5;
            d = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("\u2e5e螳ꄢ⸺良妡ᶠ\u196f埶\udf85鞫靏\udd94嗸", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 1, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = d + 77;
            a = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("흗ꉶ\uddf9흤\uebd7簴愠炃꺳靖\ueb0bﺧⓚ火浪擳뫻\uee4e\uf749\ue31a〒枥", -Process.getGidForName(""), objArr);
            o.cf.d dVar = new o.cf.d(context, 22, ((String) objArr[0]).intern());
            int i = a + 11;
            d = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("ㄵ߭ۑㅖ쟳\ud9fb멍峲䢠忚", 1 - (ViewConfiguration.getEdgeSlop() >> 16), objArr);
            bVar.d(((String) objArr[0]).intern(), ((a) e()).b);
            int i = a + 35;
            d = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        @Override // o.y.c
        public final j n() {
            int i = a;
            int i2 = i + 31;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 47;
            d = i4 % 128;
            Object obj = null;
            switch (i4 % 2 == 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a;
            int i2 = i + 55;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 63;
            d = i4 % 128;
            Object obj = null;
            switch (i4 % 2 == 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:20:0x001c, code lost:
        
            if (r6 == 5002) goto L19;
         */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final boolean e(int r6) {
            /*
                r5 = this;
                int r0 = o.ab.a.AsyncTaskC0015a.d
                int r1 = r0 + 5
                int r2 = r1 % 128
                o.ab.a.AsyncTaskC0015a.a = r2
                int r1 = r1 % 2
                r2 = 1
                r3 = 0
                if (r1 == 0) goto L11
                r1 = r3
                goto L12
            L11:
                r1 = r2
            L12:
                switch(r1) {
                    case 1: goto L1a;
                    default: goto L15;
                }
            L15:
                r1 = 6789(0x1a85, float:9.513E-42)
                if (r6 != r1) goto L22
                goto L1f
            L1a:
                r1 = 5002(0x138a, float:7.009E-42)
                if (r6 != r1) goto L28
            L1e:
                goto L2d
            L1f:
                r1 = 52
                goto L24
            L22:
                r1 = 32
            L24:
                switch(r1) {
                    case 32: goto L28;
                    default: goto L27;
                }
            L27:
                goto L1e
            L28:
                boolean r6 = super.e(r6)
                return r6
            L2d:
                int r0 = r0 + 65
                int r6 = r0 % 128
                o.ab.a.AsyncTaskC0015a.a = r6
                int r0 = r0 % 2
                java.lang.String r6 = "ꍯ퐰ꖑꌀ拽\u0a31ᤚ離\udac1谊錿矙傒٭ᕅ\ued8f캉顯轼橗䑪ᆴƬ\ue04d쉐\ueb80뮋鹡硧淬㷦ᐂ\uf60f\ue76b됂鋸淽礂⸾ࣜ\uebaa\uf323ꁟ蚹憛畚\uda63㽥齿컷峓땇ᕐ䂙훛㌡錭\udae6䣮ꥌआ尼썇⟣胾혈䔠\uddd1㻜꡴"
                java.lang.String r1 = "\ueafe\udab7浨\ueabaኗҥ퇪覗鍖芁寡\u07b7ᤴࣼ\udd8d鷥蜇雑䞋ᨀ෪"
                if (r0 == 0) goto L6a
                o.ee.g.c()
                int r0 = android.view.View.resolveSizeAndState(r3, r2, r2)
                int r0 = r3 << r0
                java.lang.Object[] r4 = new java.lang.Object[r2]
                w(r1, r0, r4)
                r0 = r4[r3]
                java.lang.String r0 = (java.lang.String) r0
                java.lang.String r0 = r0.intern()
                int r1 = android.view.ViewConfiguration.getKeyRepeatTimeout()
                int r1 = r1 % 61
                int r1 = r3 % r1
                java.lang.Object[] r2 = new java.lang.Object[r2]
                w(r6, r1, r2)
                r6 = r2[r3]
                java.lang.String r6 = (java.lang.String) r6
                java.lang.String r6 = r6.intern()
                o.ee.g.d(r0, r6)
                return r3
            L6a:
                o.ee.g.c()
                int r0 = android.view.View.resolveSizeAndState(r3, r3, r3)
                int r0 = 1 - r0
                java.lang.Object[] r4 = new java.lang.Object[r2]
                w(r1, r0, r4)
                r0 = r4[r3]
                java.lang.String r0 = (java.lang.String) r0
                java.lang.String r0 = r0.intern()
                int r1 = android.view.ViewConfiguration.getKeyRepeatTimeout()
                int r1 = r1 >> 16
                int r1 = r1 + r2
                java.lang.Object[] r4 = new java.lang.Object[r2]
                w(r6, r1, r4)
                r6 = r4[r3]
                java.lang.String r6 = (java.lang.String) r6
                java.lang.String r6 = r6.intern()
                o.ee.g.d(r0, r6)
                return r2
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ab.a.AsyncTaskC0015a.e(int):boolean");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = a + 35;
            d = i % 128;
            switch (i % 2 == 0 ? '.' : 'K') {
                case '.':
                    f().e(g(), ((a) e()).b);
                    int i2 = 97 / 0;
                    return;
                default:
                    f().e(g(), ((a) e()).b);
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(d dVar) {
            int i = d + Opcodes.DSUB;
            a = i % 128;
            int i2 = i % 2;
            ((a) e()).j().b();
            int i3 = d + 77;
            a = i3 % 128;
            switch (i3 % 2 != 0 ? Typography.amp : (char) 20) {
                case 20:
                    return;
                default:
                    int i4 = 47 / 0;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(d dVar) {
            int i = d + 73;
            a = i % 128;
            int i2 = i % 2;
            ((a) e()).j().a(dVar);
            int i3 = a + Opcodes.LNEG;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 362
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ab.a.AsyncTaskC0015a.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 512
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ab.a.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
