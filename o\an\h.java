package o.an;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import java.lang.reflect.Method;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.l;
import o.a.n;
import o.an.a;
import o.ee.g;
import o.ep.a;
import o.ep.b;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\h.smali */
public final class h<T extends o.ep.b> implements a<c, d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static long d;
    private static int e;
    private final T c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        e();
        ViewConfiguration.getScrollBarFadeDuration();
        int i = b + 71;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        d = -4627586734903719426L;
        a = new char[]{50941, 50863, 50857, 50838, 50838, 50854, 50878, 50848, 50849, 50878, 50833, 50847, 50855, 50855, 50934, 50849, 50859, 50849, 50853, 50849, 50857, 50859, 50873, 50855, 50912, 50833, 50836, 50847, 50838, 50834, 50845, 50840, 50835, 50847, 50818, 50870, 50720, 50727, 50750, 50746, 50745, 50720, 50707, 50710, 50749, 50732, 50714, 50749, 50751, 50724, 50730, 50711, 50708, 50873, 50725, 50722, 50723, 50725, 50733, 50707, 50722, 50745, 50751, 50748, 50750, 50722, 51175, 51169, 51197, 51152, 50709, 50728, 50709, 51152, 51174, 51179, 51178, 51173, 51174, 51152, 51143, 51181, 51158, 51169, 51152, 51155, 50709, 50751, 50709, 51153, 51156, 51178, 51177, 51196, 51156, 51141, 51179, 51178, 51180, 51174, 51180, 51171, 51178, 51175, 51141, 51156, 50878, 50706, 50729, 50727, 50733, 50733, 50725};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.an.h.$$a
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r7 = r7 * 4
            int r7 = 4 - r7
            int r8 = 122 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L17:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r8]
        L2c:
            int r7 = r7 + r4
            int r8 = r8 + 1
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.h.h(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{112, 25, 2, PSSSigner.TRAILER_IMPLICIT};
        $$b = 96;
    }

    @Override // o.an.a
    public final /* synthetic */ d a(o.eg.b bVar) throws o.eg.d {
        int i = e + 61;
        b = i % 128;
        int i2 = i % 2;
        d d2 = d(bVar);
        int i3 = b + 73;
        e = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    public h(T t) {
        this.c = t;
    }

    @Override // o.an.a
    public final void b(o.eg.b bVar) throws o.eg.d, a.C0022a {
        g.c();
        Object[] objArr = new Object[1];
        f("襘褋䣆鏳꼸Ɔ궩汍ǜ락讟琨쀱鏖\udac2倬\ue4bf띎ﹼ㲕ᬭ\udac6◻\u193e㾿﹇䥥\ue5ad", (Process.myTid() >> 22) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g("\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{0, 14, 0, 0}, false, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        final CountDownLatch countDownLatch = new CountDownLatch(2);
        final AtomicReference atomicReference = new AtomicReference();
        final AtomicReference atomicReference2 = new AtomicReference();
        final AtomicReference atomicReference3 = new AtomicReference();
        this.c.b(new a.InterfaceC0042a<String>() { // from class: o.an.h.2
            private static int d = 0;
            private static int i = 1;

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(String str) {
                int i2 = (i + Opcodes.IUSHR) - 1;
                d = i2 % 128;
                int i3 = i2 % 2;
                c(str);
                int i4 = (d + 58) - 1;
                i = i4 % 128;
                switch (i4 % 2 == 0 ? (char) 5 : Typography.less) {
                    case '<':
                        return;
                    default:
                        throw null;
                }
            }

            private void c(String str) {
                int i2 = d + 95;
                i = i2 % 128;
                int i3 = i2 % 2;
                atomicReference2.set(str);
                countDownLatch.countDown();
                int i4 = i + 67;
                d = i4 % 128;
                int i5 = i4 % 2;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i2 = d;
                int i3 = (i2 & 53) + (i2 | 53);
                i = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        atomicReference.set(cVar);
                        countDownLatch.countDown();
                        throw null;
                    default:
                        atomicReference.set(cVar);
                        countDownLatch.countDown();
                        int i4 = d + 85;
                        i = i4 % 128;
                        int i5 = i4 % 2;
                        return;
                }
            }
        });
        this.c.a(new a.InterfaceC0042a<String>() { // from class: o.an.h.3
            private static int c = 0;
            private static int i = 1;

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(String str) {
                int i2 = i + 87;
                c = i2 % 128;
                int i3 = i2 % 2;
                c(str);
                int i4 = i;
                int i5 = (i4 ^ 15) + ((i4 & 15) << 1);
                c = i5 % 128;
                int i6 = i5 % 2;
            }

            private void c(String str) {
                int i2 = (c + 22) - 1;
                i = i2 % 128;
                int i3 = i2 % 2;
                atomicReference3.set(str);
                countDownLatch.countDown();
                int i4 = i;
                int i5 = ((i4 | 13) << 1) - (i4 ^ 13);
                c = i5 % 128;
                int i6 = i5 % 2;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i2 = c;
                int i3 = (i2 ^ 77) + ((i2 & 77) << 1);
                i = i3 % 128;
                switch (i3 % 2 == 0 ? 'N' : 'D') {
                    case 'N':
                        atomicReference.set(cVar);
                        countDownLatch.countDown();
                        throw null;
                    default:
                        atomicReference.set(cVar);
                        countDownLatch.countDown();
                        int i4 = (i + Opcodes.IREM) - 1;
                        c = i4 % 128;
                        int i5 = i4 % 2;
                        return;
                }
            }
        });
        try {
            countDownLatch.await();
            String str = (String) atomicReference2.get();
            String str2 = (String) atomicReference3.get();
            if (atomicReference.get() != null) {
                throw new a.C0022a((o.bv.c) atomicReference.get());
            }
            switch (str != null ? '[' : (char) 28) {
                case Opcodes.DUP_X2 /* 91 */:
                    int i = e + 55;
                    b = i % 128;
                    int i2 = i % 2;
                    if (str2 != null) {
                        c cVar = new c(str, str2);
                        Object[] objArr3 = new Object[1];
                        g("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{14, 10, 0, 0}, false, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        g("\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{24, 11, 8, 8}, false, objArr4);
                        bVar.d(intern2, ((String) objArr4[0]).intern());
                        Object[] objArr5 = new Object[1];
                        f("\u31ef㆜ᠲᆚ\udd8b兲ᔞ㲹莵㔔郎ڛ碆쌢墨⊑小\ue7b3簞一ꎪ訰Ꞃ殚蜲꺣", -((byte) KeyEvent.getModifierMetaStateMask()), objArr5);
                        bVar.d(((String) objArr5[0]).intern(), cVar.e());
                        Object[] objArr6 = new Object[1];
                        g("\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001", new int[]{35, 18, Opcodes.LXOR, 0}, false, objArr6);
                        bVar.d(((String) objArr6[0]).intern(), cVar.b());
                        int i3 = b + 85;
                        e = i3 % 128;
                        int i4 = i3 % 2;
                        return;
                    }
                    break;
            }
            throw new a.C0022a(new o.bv.c(AntelopErrorCode.InternalError));
        } catch (InterruptedException e2) {
            throw new a.C0022a(new o.bv.c(AntelopErrorCode.InternalError));
        }
    }

    private static d d(o.eg.b bVar) throws o.eg.d {
        g.c();
        Object[] objArr = new Object[1];
        f("襘褋䣆鏳꼸Ɔ궩汍ǜ락讟琨쀱鏖\udac2倬\ue4bf띎ﹼ㲕ᬭ\udac6◻\u193e㾿﹇䥥\ue5ad", TextUtils.indexOf("", "") + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{53, 13, Opcodes.LXOR, 0}, false, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f("虀蘳൬ᝦ뉙䐬ꊱ⧧蕉㏨雾楉켩홼幆䵚\uebb8\uf2f3竦⇶ᐢ齲ꅭѓェ믰췰\uf8d0崈恴\ue86a\udf5e禳\u0cf0ᓳ", (KeyEvent.getMaxKeyCode() >> 16) + 1, objArr3);
        String r = bVar.r(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        f("\uedc3\uedb0씣顬½豣줲\ue1a8\u0a43볢␚\udbad꒪ḳ텝ﾩ耤㪫\uf5e3錒羡圽\u2e67뚷嬳玳䋧", 1 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
        String r2 = bVar.r(((String) objArr4[0]).intern());
        g.c();
        Object[] objArr5 = new Object[1];
        f("襘褋䣆鏳꼸Ɔ궩汍ǜ락讟琨쀱鏖\udac2倬\ue4bf띎ﹼ㲕ᬭ\udac6◻\u193e㾿﹇䥥\ue5ad", Color.red(0) + 1, objArr5);
        String intern2 = ((String) objArr5[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr6 = new Object[1];
        g(null, new int[]{66, 40, Opcodes.INVOKESTATIC, 20}, true, objArr6);
        StringBuilder append = sb.append(((String) objArr6[0]).intern()).append(r);
        Object[] objArr7 = new Object[1];
        f("○◫\ud95eه\ue1e4遐Ġﶞ鐥⊆앺㫳沓ȋ伀Ữ䠮⚙殎牮럻䭛끗", View.resolveSize(0, 0) + 1, objArr7);
        g.d(intern2, append.append(((String) objArr7[0]).intern()).append(r2).toString());
        Object[] objArr8 = new Object[1];
        g("\u0001\u0001\u0000\u0000\u0000\u0001\u0000", new int[]{Opcodes.FMUL, 7, Opcodes.DCMPL, 0}, false, objArr8);
        d dVar = new d(r, r2, ((String) objArr8[0]).intern());
        int i = b + Opcodes.LREM;
        e = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\h$c.smali */
    public static final class c {
        private final String b;
        private final String c;
        private static int e = 0;
        private static int a = 1;

        public c(String str, String str2) {
            this.c = str;
            this.b = str2;
        }

        public final String e() {
            int i = e;
            int i2 = (i & 19) + (i | 19);
            a = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    throw null;
                default:
                    return this.c;
            }
        }

        public final String b() {
            int i = a;
            int i2 = ((i | 15) << 1) - (i ^ 15);
            e = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    String str = this.b;
                    int i3 = (i & 13) + (i | 13);
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    return str;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\h$d.smali */
    public static final class d {
        private final String a;
        private final String b;
        private final String c;
        private static int e = 0;
        private static int d = 1;

        public d(String str, String str2, String str3) {
            this.a = str;
            this.b = str2;
            this.c = str3;
        }

        public final String b() {
            int i = d;
            int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
            int i3 = i2 % 128;
            e = i3;
            Object obj = null;
            switch (i2 % 2 == 0) {
                case true:
                    String str = this.a;
                    int i4 = ((i3 | 49) << 1) - (i3 ^ 49);
                    d = i4 % 128;
                    switch (i4 % 2 == 0) {
                        case false:
                            return str;
                        default:
                            obj.hashCode();
                            throw null;
                    }
                default:
                    throw null;
            }
        }

        public final String c() {
            int i = d;
            int i2 = (i & 43) + (i | 43);
            e = i2 % 128;
            switch (i2 % 2 != 0 ? '^' : (char) 19) {
                case Opcodes.DUP2_X2 /* 94 */:
                    int i3 = 22 / 0;
                    return this.b;
                default:
                    return this.b;
            }
        }

        public final String a() {
            int i = d;
            int i2 = i + 91;
            e = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    throw null;
                default:
                    String str = this.c;
                    int i3 = (i & 53) + (i | 53);
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    return str;
            }
        }
    }

    private static void f(String str, int i, Object[] objArr) {
        char[] charArray;
        switch (str == null) {
            case false:
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        n nVar = new n();
        char[] b2 = n.b(d ^ 8632603938177761503L, charArray, i);
        nVar.c = 4;
        int i2 = $11 + Opcodes.LUSHR;
        $10 = i2 % 128;
        switch (i2 % 2 == 0) {
        }
        while (nVar.c < b2.length) {
            int i3 = $10 + Opcodes.DMUL;
            $11 = i3 % 128;
            int i4 = i3 % 2;
            nVar.e = nVar.c - 4;
            int i5 = nVar.c;
            try {
                Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % 4]), Long.valueOf(nVar.e), Long.valueOf(d)};
                Object obj = o.e.a.s.get(-1945790373);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(Drawable.resolveOpacity(0, 0) + 11, (char) ((-1) - Process.getGidForName("")), 42 - ExpandableListView.getPackedPositionChild(0L));
                    byte b3 = (byte) 0;
                    byte b4 = b3;
                    Object[] objArr3 = new Object[1];
                    h(b3, b4, (byte) (b4 | 54), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                    o.e.a.s.put(-1945790373, obj);
                }
                b2[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {nVar, nVar};
                    Object obj2 = o.e.a.s.get(-341518981);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(Process.getGidForName("") + 11, (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.getDefaultSize(0, 0) + 249);
                        byte b5 = (byte) 0;
                        byte b6 = b5;
                        Object[] objArr5 = new Object[1];
                        h(b5, b6, (byte) (b6 | 51), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-341518981, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        objArr[0] = new String(b2, 4, b2.length - 4);
    }

    private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i;
        char[] cArr2;
        String str2 = str;
        char c2 = 2;
        byte[] bArr = str2;
        if (str2 != null) {
            int i2 = $10 + 43;
            $11 = i2 % 128;
            int i3 = i2 % 2;
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i4 = 0;
        int i5 = iArr[0];
        int i6 = 1;
        int i7 = iArr[1];
        int i8 = iArr[2];
        int i9 = iArr[3];
        char[] cArr3 = a;
        switch (cArr3 != null ? (char) 14 : '\t') {
            case '\t':
                break;
            default:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i10 = 0;
                while (true) {
                    switch (i10 < length ? i6 : i4) {
                        case 1:
                            try {
                                Object[] objArr2 = new Object[i6];
                                objArr2[i4] = Integer.valueOf(cArr3[i10]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    cArr2 = cArr3;
                                } else {
                                    Class cls = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 11, (char) (ViewConfiguration.getJumpTapTimeout() >> 16), 43 - View.getDefaultSize(i4, i4));
                                    byte b2 = (byte) i4;
                                    cArr2 = cArr3;
                                    Object[] objArr3 = new Object[1];
                                    h(b2, b2, $$a[c2], objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr4[i10] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i10++;
                                cArr3 = cArr2;
                                c2 = 2;
                                i4 = 0;
                                i6 = 1;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr3 = cArr4;
                            break;
                    }
                }
        }
        char[] cArr5 = new char[i7];
        System.arraycopy(cArr3, i5, cArr5, 0, i7);
        if (bArr2 != null) {
            char[] cArr6 = new char[i7];
            lVar.d = 0;
            char c3 = 0;
            while (lVar.d < i7) {
                switch (bArr2[lVar.d] == 1 ? 'L' : 'b') {
                    case Base64.mimeLineLength /* 76 */:
                        int i11 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c3)};
                            Object obj2 = o.e.a.s.get(2016040108);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getFadingEdgeLength() >> 16) + 11, (char) (MotionEvent.axisFromString("") + 1), TextUtils.lastIndexOf("", '0', 0) + 449);
                                byte b3 = (byte) 0;
                                byte b4 = b3;
                                Object[] objArr5 = new Object[1];
                                h(b3, b4, (byte) (b4 + 3), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2016040108, obj2);
                            }
                            cArr6[i11] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        int i12 = lVar.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c3)};
                            Object obj3 = o.e.a.s.get(804049217);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 11, (char) View.combineMeasuredStates(0, 0), KeyEvent.keyCodeFromString("") + 207);
                                byte b5 = (byte) 0;
                                byte b6 = b5;
                                Object[] objArr7 = new Object[1];
                                h(b5, b6, b6, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(804049217, obj3);
                            }
                            cArr6[i12] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                }
                c3 = cArr6[lVar.d];
                try {
                    Object[] objArr8 = {lVar, lVar};
                    Object obj4 = o.e.a.s.get(-2112603350);
                    if (obj4 == null) {
                        Class cls4 = (Class) o.e.a.c(11 - View.resolveSize(0, 0), (char) KeyEvent.keyCodeFromString(""), View.resolveSize(0, 0) + 259);
                        byte b7 = (byte) 0;
                        byte b8 = b7;
                        Object[] objArr9 = new Object[1];
                        h(b7, b8, (byte) (b8 | 56), objArr9);
                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                        o.e.a.s.put(-2112603350, obj4);
                    }
                    ((Method) obj4).invoke(null, objArr8);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            int i13 = $10 + 59;
            $11 = i13 % 128;
            int i14 = i13 % 2;
            cArr5 = cArr6;
        }
        if (i9 > 0) {
            char[] cArr7 = new char[i7];
            System.arraycopy(cArr5, 0, cArr7, 0, i7);
            int i15 = i7 - i9;
            System.arraycopy(cArr7, 0, cArr5, i15, i9);
            System.arraycopy(cArr7, i9, cArr5, 0, i15);
        }
        if (z) {
            int i16 = $11 + 93;
            $10 = i16 % 128;
            switch (i16 % 2 != 0 ? (char) 25 : 'V') {
                case Opcodes.SASTORE /* 86 */:
                    cArr = new char[i7];
                    i = 0;
                    break;
                default:
                    i = 0;
                    cArr = new char[i7];
                    break;
            }
            lVar.d = i;
            while (lVar.d < i7) {
                cArr[lVar.d] = cArr5[(i7 - lVar.d) - 1];
                lVar.d++;
            }
            cArr5 = cArr;
        }
        if (i8 > 0) {
            int i17 = $10 + Opcodes.DMUL;
            $11 = i17 % 128;
            int i18 = i17 % 2 == 0 ? 1 : 0;
            while (true) {
                lVar.d = i18;
                if (lVar.d < i7) {
                    cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                    i18 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr5);
    }
}
