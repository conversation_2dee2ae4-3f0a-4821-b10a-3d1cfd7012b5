package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.math.ec.WNafUtil;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4.smali */
public class a4 {
    static e8 a = new a();
    static e8 b = new b();
    static e8 c = new c();
    static e8 d = new d();
    static e8 e = new e();
    static e8 f = new f();
    static e8 g = new g();
    static e8 h = new h();
    static final Hashtable i = new Hashtable();
    static final Hashtable j = new Hashtable();
    static final Hashtable k = new Hashtable();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4$a.smali */
    class a extends e8 {
        a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a4.b(new ECCurve.Fp(a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD97"), a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD94"), a4.b("A6"), a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6C611070995AD10045841B09B761B893"), ECConstants.ONE, true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a4.b(c, ECConstants.ONE, a4.b("8D91E471E0989CDA27DF505A453F2B7635294F2DDF23E3B122ACC99C9E9F1E14")), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4$c.smali */
    class c extends e8 {
        c() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a4.b(new ECCurve.Fp(a4.b("9B9F605F5A858107AB1EC85E6B41C8AACF846E86789051D37998F7B9022D759B"), a4.b("9B9F605F5A858107AB1EC85E6B41C8AACF846E86789051D37998F7B9022D7598"), a4.b("805A"), a4.b("9B9F605F5A858107AB1EC85E6B41C8AA582CA3511EDDFB74F02F3A6598980BB9"), ECConstants.ONE, true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a4.b(c, ECConstants.ZERO, a4.b("41ECE55743711A8C3CBF3783CD08C0EE4D4DC440D4641A8F366E550DFDB3BB67")), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4$d.smali */
    class d extends e8 {
        d() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a4.b(new ECCurve.Fp(a4.b("9B9F605F5A858107AB1EC85E6B41C8AACF846E86789051D37998F7B9022D759B"), a4.b("9B9F605F5A858107AB1EC85E6B41C8AACF846E86789051D37998F7B9022D7598"), a4.b("805A"), a4.b("9B9F605F5A858107AB1EC85E6B41C8AA582CA3511EDDFB74F02F3A6598980BB9"), ECConstants.ONE, true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a4.b(c, ECConstants.ZERO, a4.b("41ECE55743711A8C3CBF3783CD08C0EE4D4DC440D4641A8F366E550DFDB3BB67")), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4$e.smali */
    class e extends e8 {
        e() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a4.b(new ECCurve.Fp(a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD97"), a4.b("C2173F1513981673AF4892C23035A27CE25E2013BF95AA33B22C656F277E7335"), a4.b("295F9BAE7428ED9CCC20E7C359A9D41A22FCCD9108E17BF7BA9337A6F8AE9513"), a4.b("400000000000000000000000000000000FD8CDDFC87B6635C115AF556C360C67"), ECConstants.FOUR, true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a4.b(c, a4.b("91E38443A5E82C0D880923425712B2BB658B9196932E02C78B2582FE742DAA28"), a4.b("32879423AB1A0375895786C4BB46E9565FDE0B5344766740AF268ADB32322E5C")), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a4$f.smali */
    class f extends e8 {
        f() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a4.b(new ECCurve.Fp(a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDC7"), a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFDC4"), a4.b("E8C2505DEDFC86DDC1BD0B2B6667F1DA34B82574761CB0E879BD081CFD0B6265EE3CB090F30D27614CB4574010DA90DD862EF9D4EBEE4761503190785A71C760"), a4.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF27E69532F48D89116FF22B8D4E0560609B4B38ABFAD2B85DCACDB1411F10B275"), ECConstants.ONE, true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a4.b(c, ECConstants.THREE, a4.b("7503CFE87A836AE3A61B8816E25450E6CE5E1C93ACF1ABC1778064FDCBEFA921DF1626BE4FD036E93D75E6A50E3A41E98028FE5FC235F5B889A589CB5215F2A4")), c.getOrder(), c.getCofactor(), null);
        }
    }

    static {
        a("GostR3410-2001-CryptoPro-A", o1.x, a);
        a("GostR3410-2001-CryptoPro-B", o1.y, b);
        a("GostR3410-2001-CryptoPro-C", o1.z, c);
        a("GostR3410-2001-CryptoPro-XchA", o1.A, a);
        a("GostR3410-2001-CryptoPro-XchB", o1.B, d);
        a("Tc26-Gost-3410-12-256-paramSetA", y6.o, e);
        a("Tc26-Gost-3410-12-512-paramSetA", y6.q, f);
        a("Tc26-Gost-3410-12-512-paramSetB", y6.r, g);
        a("Tc26-Gost-3410-12-512-paramSetC", y6.s, h);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, BigInteger bigInteger, BigInteger bigInteger2) {
        ECPoint createPoint = eCCurve.createPoint(bigInteger, bigInteger2);
        WNafUtil.configureBasepoint(createPoint);
        return new f8(createPoint, false);
    }

    public static e8 c(String str) {
        w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return a(e2);
    }

    public static X9ECParameters d(String str) {
        w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return b(e2);
    }

    public static w e(String str) {
        return (w) i.get(str);
    }

    static void a(String str, w wVar, e8 e8Var) {
        i.put(str, wVar);
        k.put(wVar, str);
        j.put(wVar, e8Var);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BigInteger b(String str) {
        return new BigInteger(1, z4.a(str));
    }

    public static X9ECParameters b(w wVar) {
        e8 a2 = a(wVar);
        if (a2 == null) {
            return null;
        }
        return a2.d();
    }

    public static e8 a(w wVar) {
        return (e8) j.get(wVar);
    }

    public static Enumeration a() {
        return k.elements();
    }
}
