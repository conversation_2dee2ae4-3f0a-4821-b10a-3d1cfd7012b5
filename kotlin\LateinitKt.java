package kotlin;

import kotlin.jvm.internal.Intrinsics;
import kotlin.reflect.KProperty0;

/* compiled from: Lateinit.kt */
@Metadata(d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0004\"#\u0010\u0000\u001a\u00020\u0001*\u0006\u0012\u0002\b\u00030\u00028Æ\u0002X\u0087\u0004¢\u0006\f\u0012\u0004\b\u0003\u0010\u0004\u001a\u0004\b\u0000\u0010\u0005¨\u0006\u0006"}, d2 = {"isInitialized", "", "Lkotlin/reflect/KProperty0;", "isInitialized$annotations", "(Lkotlin/reflect/KProperty0;)V", "(<PERSON><PERSON><PERSON>/reflect/KProperty0;)Z", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\LateinitKt.smali */
public final class LateinitKt {
    public static /* synthetic */ void isInitialized$annotations(KProperty0 kProperty0) {
    }

    private static final boolean isInitialized(KProperty0<?> kProperty0) {
        Intrinsics.checkNotNullParameter(kProperty0, "<this>");
        throw new NotImplementedError("Implementation is intrinsic");
    }
}
