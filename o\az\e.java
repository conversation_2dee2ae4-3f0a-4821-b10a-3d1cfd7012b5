package o.az;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.security.keystore.KeyGenParameterSpec;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.ProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.util.Locale;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int f;
    private static int h;
    private static long j;
    private boolean a;
    private KeyStore c;
    private PrivateKey d;
    private byte[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        a();
        TextUtils.lastIndexOf("", '0');
        ExpandableListView.getPackedPositionChild(0L);
        View.combineMeasuredStates(0, 0);
        TextUtils.lastIndexOf("", '0');
        SystemClock.currentThreadTimeMillis();
        View.MeasureSpec.getMode(0);
        TextUtils.indexOf((CharSequence) "", '0');
        Drawable.resolveOpacity(0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        TextUtils.lastIndexOf("", '0');
        View.MeasureSpec.getMode(0);
        TextUtils.lastIndexOf("", '0');
        int i = f + 55;
        h = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        char[] cArr = new char[1738];
        ByteBuffer.wrap(",\u0088/¼*\u0091%â ç#Æ>99:4\u00167a2Y\rH\tª\u0004\u0087\u0007\u008f\u0002ì\u001dß\u0018Ö\u001b!\u0016\u000e\u0011\u0003\u0005Õ\u0006Ä\u0003ù\f\u008a\u009e\n\u009d\u0003\u0098!\u0097\t\u0092U\u0091x, /±*\u008c%ÿ ±#\u008a>m964\u00177l2g\rB\t±\u0004£\u0007\u0094\u0002í\u001dÎ\u0018\u009f\u001b.\u0016\u000e\u0011\bl'o_jVfºa\u0080|\u0083\u007fþzÓuÒp9ë\u0097è\u0086í»âÈç\u0086ä½ùZþ\nó!ð\u0018õGÊbÎ\u0095Ã\u0092À³ÅÄÚêßáÜ\u001cÑ;Öf«[¨\u007f\u00ad}¡Î¦ª»§¸Ï½ù²ö·\u000f´&\u0089[\u008eL\rZ\u000eK\u000bv\u0004\u0005\u0001K\u0002p\u001f\u0097\u0018Â\u0015æ\u0016\u008c\u0013\u009c,µ(T%_&b#Y<<90:Ë7±0äM\u009bN÷K¼GP@p\u0019\u0017\u001a\u001a\u001f;\u0010S\u0015[\u0016n\u000b\u008a\f\u009d\u0001ò\u0002À\u0007Û8æ<\u00031\u001f237\b(i-q.\u0097#³$®YÃZô_ý4R7C2~=\r8C;x&\u009f!Ê,î/\u0084*Ç\u0015¹\u0011R\u001cL\u001f}\u001aQ\u00054\u00008\u0003Ã\u000e¹\tìt\u0093wÿr´~Xyxå³æ¢ã\u009fììé¢ê\u0099÷~ð2ý\u000fþoûcÄ\\À¦Í\u00adÎ\u0080Ë÷Ô\u009aÑÇÒ3ß\u0001Ø\u0011¥`¦Q£R¯¯T3W$R\u0018]gXv[IF§A\u0088L\u0089OòJùuÜq/|=\u007f\u0006zye{`Rc°,\u00ad/º*\u0086%ù è#×>99\u00164\u00177l2g\rB\t±\u0004£\u0007\u0098\u0002ç\u001då\u0018Ì\u001b.\u0016K\u0011\\l'o]jAf¶a\u0099|\u009c\u007fÿzÄuÅp}s\rNfIkD\u0005@¢C¿^\u008eYùTúWØR#\u00ad<¨\u0001«d¦s,¥/¬*\u008e%« á#Õ>\"9%4\u00107k2p\rI\tá\u0004¹\u0007\u0092\u0002÷\u001d\u0089\u0018Ö\u001b+\u0016\u0002\u0011\u0005lnoLj_f°a\u0095|\u0090\u007fÿ4r7e2Y=&87;\b&æ!É,È/³*¸\u0015\u009d\u0011n\u001c|\u001fG\u001a8\u0005:\u0000\u0013\u0003ñ\u000e\u0094\t\u0083tøw\u009cr\u0099~jy\\d\ng(b\rm\u0003hÇkÒVµQ²\\\u0083Xd[zFQA<L+O-Jõµó*j)},\b#6&.%\u00038ö?ÿ2À1§4ø\u000b\u009d\u000fi\u0002c\u0001\u0010\u0004+\u001b\u001c\u001e\u001b\u001dû\u0010Ò\u0017Õj¤i\u0087ÀßÃÈÆôÉ\u008bÌ\u009aÏ¥ÒKÕdØeÛ\u001eÞ\u0015á0åÃèÑëêî\u0095ñ\u0097ô¾÷\\ú9ý.\u0080U\u00831\u00864\u008aÇ\u008dñ\u0090§\u0093\u008c\u0096½\u0099¦\u009c]\u009fh¢\u000b¥\u0019¨2¬Ý¯ï²æµ\u0094-û.ì+\u0099$²!£\"\u0098?c8v5U6'3,\f\u0003\b½\u0005ï\u0006À\u0003«\u001c\u0094\u0019Ã\u001a|\u0017O\u0010Dm(n\u0005k\u0006gë`Ô,\u009b/\u008c*¤%¤ Ô#ä>\u000f9|4)7D2V\rh\tð\u0004\u0087\u0007\u009c\u0002ç\u001dÍ\u0018Ö\u001b+\u0016\f,£/¾*\u0093%ê é#\u0089>.9!4\u00007\u007f2a\rT\tï\u0004\u0094\u0007\u0094\u0002ó\u001dÁ\u0018Ú\u001b7,®/º*\u0091%Â ÿ#Ô>9924\u00177l2pcß`ÂeÑj\u0090o\u008dl´qSÑ¸Ò¯×\u0093ØìÝýÞÂÃ,Ä\u0003É\u0002ÊyÏrðWô¤ù¶ú\u008dÿòàðåÙæ;ë^ìI\u00912\u0092\\\u0097G\u009b¸\u009c\u009b\u0081À\u0082ê\u0087Ñ\u0088Á\u008d:\u008e\u000f³l´~¹U½º¾ä£\u0081¤í©åªÏ¯?P3U\u001dVr[w\\D@ºE\u0085FÊKªL\u009e,\u00ad/º*\u0086%ù è#×>99\u00164\u00177l2g\rB\t±\u0004£\u0007\u0098\u0002ç\u001då\u0018Ì\u001b.\u0016K\u0011\\l'oHjKfºa\u008a|\u0085\u007fïzÈuØp3sCN~IwDL@§C´^ÇYéTöWÚR=\u00ad,¨\u000b«u¦~¡S½¤¸É»\u009b¶ä±ß\u008cÐ,»/º*\u008b%î æ#â>#904\u000b7v2e\rO\t¤\u0004³\u0007±\u0002ð\u001dÂ\u0018\u009f\u001b\u007f\u0016KèÚëÎîÿá\u009aä\u0097ç²úMýBðAó\bö\nÉ\nÍÛÀÀÃûÆ\u008eÙ\u00adÜ¿ßXÒqÕb¨8«<®>¢ý¥ú¸è»\u009d,®/º*\u008b%î ã#Æ>996457|2~\r~\t¯\u0004´\u0007\u008f\u0002ú\u001dÙ\u0018Ë\u001b,\u0016\u0005\u0011\u0016lLoHjJf\u0089a\u008e|\u009c\u007féz\u0081u\u009ap}s\u0013N{IpDS@¢Cµ^\u0082YÿT³W×R \u00ad!¨[«h¦y¡T½·¸\u0080»\u009e¶é±Â\u008cË\u008f\"\u008a\t,\u009b/\u008c*¤)d*]/m \u0015%\u0012&\";Å<ô1ð2\u009a7ª\b£\fB\u0001I\u0002t,\u008c/\u009c*§,\u0099/\u0094*¦%Ø  #÷>,974\u001d7f2{\r\\-¯.¶+\u0096$é!ú\"Ï?:8\u00185\r6cÑ\u0099Ò\u008d×¼ØÙÝÔÞñÃ\u000eÄ\u0001É\u0002ÊKÏIðIô\u0098ù\u0083ú¸ÿÍàîåüæ\u001bë2ì!\u0091{\u0092\u007f\u0097}\u009b¾\u009c¹\u0081«\u0082Þ\u0087¶\u0088\u00ad\u008dJ\u008e1³F´K¹w½\u008c¾\u0092£¹¤Õ©Ê,®/º*\u008b%î ã#Æ>996457|2~\r~\t¯\u0004´\u0007\u008f\u0002ú\u001dÙ\u0018Ë\u001b,\u0016\u0005\u0011\u0016lLoHjJf\u0089a\u008e|\u009c\u007féz\u0081u\u009ap}s\rNlIhD\u0005@ªC¥^\u0093YèTþWÉR;\u00adu¨\u0014«g¦7¡V½¦¸\u0090»ß¶â±Î\u008cß\u008f\"\u008a\u001f\u0085\u0012\u0080m\u0083F\u009eZ\u009aµ\u0095Á\u0090\u0080\u0093ôî×é!ä0ç\u0010â\u007fý1øFô¹÷§ò\u009cÍüÈáËÚÆ5Á>Ü\u0012ßm,\u008a/\u0091*Ø%ç â#Ì>`984\t¦\u009e¥\u008a »¯ÞªÓ©ö´\t³\u0006¾\u0005½L¸N\u0087N\u0083\u009f\u008e\u0084\u008d¿\u0088Ê\u0097é\u0092û\u0091\u001c\u009c5\u009b&æ|åxàzì¹ë¾ö¬õÙð±ÿªúMù8Ä\\ÃVÎ5Ê\u008bÉ\u0080Ô¾ÓÏÞ\u0083ÝúØ\n'\u0006\"(!T,T+~7\u00952¬1£<Ù;â\u0006¡\u0005\u0014\u0000/\u000f&\nH\tk\u0014`\u0010\u008f}ä~ð{Ût\u0084qµr\u008eohh}eVf!c\u000f\\\u0004XéUñVÞSªL¨I\u0090JvGb@^=?>\u0013;\u00107õ0Ì-Ü.°+\u009f$\u0098!T\"A\u001f\"\u0018<\u0015\u0001\u0011¡\u0012¶\u000f\u008d\b¢\u0005«\u0006\u0081\u0003jümù\u0011ú<÷5ð\u0012ìçé\u0083êÓçªà\u0095Ý\u0098ÞeÛNÔWÑ4ÒEÏ\u001cËôÄÙÁÉÂ¾¿\u008f¸jµv¶N³5¬>©M¥ä¦ñ£Ò\u009c¬\u0099±Ó\u009eÐ\u008aÕ¡ÚþßÏÜôÁ\u0012Æ\u0007Ë,È[Íuò~ö\u0093û\u008bø¤ýÐâÒçêä\fé\u0018î$\u0093E\u0090i\u0095j\u0099\u008f\u009e¶\u0083¦\u0080Ê\u0085å\u008aâ\u008f.\u008c;±X¶F»{¿Û¼Ì¡÷¦Ó«Ö¨å\u00ad\u0013REW(TTYU^yB\u009aG¿D¦IÖNúsõp\u0012u}z \u007fA|~ale\u0085jño¡lÈ\u0011ç\u0016\u001a\u001b\u0007\u00180\u001d_,«/ª*\u008c%ç õ#æ>.9'4\u00107y2t\rO\t¨\u0004¸\u0007\u0093\u0002Ñ\u001dÌ\u0018Î\u001b0\u0016\u000e\u0011\u0002lso\rj\u001efùa\u008c|\u0090\u007fézÕuÞp;s\nNjI~DQ@®Cñ^ÂYéT³WßR=\u00ad:¨\u0016«!¦t¡U½¢¸\u0080»\u0091¶¥±\u0091\u008c\u0091\u008fb\u008a\u001e¸ö»â¾É±\u0096´§·\u009cªz\u00ado D£3¦\u001d\u0099\u0016\u009dû\u0090ã\u0093Ì\u0096¸\u0089º\u008c\u0082\u008fd\u0082p\u0085Lø-û\u0001þ\u0002òçõÞèÎë¢î\u008dá\u008aäFçSÚ0Ý.Ð\u0013Ô³×¤Ê\u009fÍ¶ÀªÃ\u008fÆy9b<W?y2*5\u000b)ø,Þ/Ã\"¸%Ó\u0018\u008a\u001bz\u001eG\u0011_\u0014(\u0017\u0011\n\u0004\u000eà\u0001Ø\u0004Û\u0007 zÛ}rpos\\v:i',®/º*\u0091%Î ÿ#Ä>\"974\u001c7k2E\rN\t£\u0004»\u0007\u0094\u0002à\u001dâ\u0018Ú\u001b<\u0016(\u0011\u0014luoYjZf¿a\u0086|\u0096\u007fúzÕuÒp\u001es\u000bNhIvDK@ëCü^ÇYîTöWËR;\u00ad<¨\u001d«h¦t¡\\½·¸\u008c»ß¶æ±Ã\u008cÐ\u008f.\u008a\u0003\u0085S\u0080i\u0083]\u009eP\u009a«\u0095\u0080\u0090\u0085\u0093øîÇéiäeçE,¯/³*\u0090%ø ù,¯/³*\u0090%ø ù#\u0087>`9s4\t7}2z\rM\t¨\u0004³\u0007\u0098\u0002ñ\u001d\u0089\u0018Ñ\u001b*\u0016\u001f\u0011QlnoCjZf\u00ada\u0086|\u0094\u007f÷zÈuÍp8s\u0007,¯/³*\u0090%ø ù#\u0087>`9s4\u00127j2l\rH\tµ\u0004¸\u0007\u008f\u0002æ\u001d\u0089\u0018Ð\u001b0\u0016\u001f\u0011QlhoKj\u0013f¬a\u009c|\u0090,¢/º*\u009c%ø å#È>?964Y7`2`\rO\tá\u0004¸\u0007\u009b\u0002£\u001dÜ\u0018Ì\u001b ¢^¡J¤a«+®\u0013\u00ad>°Ë·Âºý¹\u009a¼®\u0083®\u0087H\u008a\u0007\u0089 \u008cS\u0093:\u0096'\u0095Ð\u0098ø\u009fêâ\u009eá³ä¤è\tïzò}ñ\u0002ô\"û3þÈýýÀ\u009aÇ\u008aÊõÎTÍGÐ7×\u0016Ú\u0006Ù0Ü\u009f#Õ&ê%\u0098(\u0095/í3\t69ØüÛèÞÃÑ\u0089Ô±×\u009cÊiÍ`À_Ã8Æ\fù\fýêð¥ó\u0082öñé\u0090ì\u0088ïnâ\u0019åS\u00984\u009b\u0016\u009e\u0013\u0092«\u0095Ô\u0088Ô\u008bé\u008e\u009d\u0081\u008a\u0084{\u0087\u0011º+½?°\u0012´ê·æªÛ\u00ad« á£Ã¦sYh\\\t_0R U\u001dIåLÒOËB¾E\u009ax\u0082{a~Zq\b,®/º*\u0091%Û ã#Î>;924\r7j2^\r^\t¸\u0004÷\u0007Ð\u0002£\u001dÂ\u0018Ú\u001b<\u0016K\u0011\u0001lfoDjAfùa\u0086|\u0086\u007f»zÏuØp)sCNyImD@@¸C´^\u0089YùT³W\u0091R!\u00ad:¨[«q¦e¡T½µ¸\u0088»\u008b¶à±\u008b\u008cÚ\u008f\"\u008a\u0014\u0085Z\u0093Î\u0090Ú\u0095ñ\u009a»\u009f\u0083\u009c®\u0081[\u0086R\u008bm\u0088\n\u008d>²>¶Ø»\u0097¸°½Ã¢¢§º¤\\©+®aÓ\u0006Ð$Õ!Ù\u0099ÞæÃæÀÛÅ±Ê¥ÏXÌpñ\fö\u0011û1ÿ\u008büÐáéæ\u0089ëÓè¬í\\\u0012T\u0017y\u0014\r\u0019\u0012ÕëÖéÓ\u008fÜ®Ù»Ú\u0097ÇlÀ{ÍRÎ;Ë\u007fô\u001eðëýçþÜû«ä\u0082á\u008bâiïLèP\u0095/\u0096\u0000\u0093\u0015\u009fï\u0098Ê\u0085Ã\u0086¦\u0083º\u008c\u0080\u0089q\u008aF·?°>½\u0005¹Ðºå§Ñ ¬\u00ad±®\u0098«yTtQQR&_6X\n²\u0085±\u0091´¼»\u0088¾Ù½æ \u0013§\u0015.\u0086-\u0092(¹'æ\"×!ì<\u0017;\u00026!5S0X\u000fw\u000b¥\u0006\u008c\u0005¾\u0000\u008b\u001f¬\u001a·\u0019\b\u0014-\u0013:n]m|hkd\u0085c¢~¹}\u0093xåwìr\u001eqkLOKXFyBÃA\u0089\\½[ÀVÈUôP\t¯\t,®/º*\u0091%Î ÿ#Ä>?9*4\t7{2p\r_\t\u008d\u0004¤\u0007\u0096\u0002£\u001d\u0084\u0018\u009f\u001b)\u0016\u0018\u0011\u001al'o\u0005jQf¸a\u009c|\u0090\u007f\u00adz\u0095u\u009ep}sYN),º/«*\u008a%ù ô#â>#904\u000b7v2e\rO\t¤\u0004³\u0007±\u0002ð\u001dÂ\u0018\u009f\u001bh\u0016K\u0011\u001dltoFj\u0013fña\u008d|\u0094\u007fèzÄu\u0081pisJN)I%D\u0005,¯/³*\u0090%ø ù#â>#904\u000b7v2e\rO\t¤\u0004³\u0007±\u0002ð\u001dÂ,¯/³*\u0090%ø ù#ì>(9*4*7{2z\rI\t¤".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1738);
        b = cArr;
        j = -8060295440837038113L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = 105 - r7
            int r9 = r9 * 2
            int r9 = 1 - r9
            int r8 = r8 + 4
            byte[] r0 = o.az.e.$$a
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L30
        L15:
            r3 = r2
        L16:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L25
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L25:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L30:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.e.i(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 46;
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x016d  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x0173 A[RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(android.content.Context r14) throws o.az.e.c {
        /*
            Method dump skipped, instructions count: 474
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.e.d(android.content.Context):void");
    }

    private boolean d() {
        int i = f;
        int i2 = i + 23;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                boolean z = this.a;
                int i3 = i + Opcodes.LSUB;
                h = i3 % 128;
                int i4 = i3 % 2;
                return z;
            default:
                throw null;
        }
    }

    final boolean c() {
        if (this.a) {
            int i = f + 91;
            int i2 = i % 128;
            h = i2;
            if (i % 2 != 0) {
                throw null;
            }
            if (this.e != null) {
                switch (this.d == null) {
                    case true:
                        break;
                    default:
                        int i3 = i2 + 57;
                        f = i3 % 128;
                        switch (i3 % 2 == 0 ? (char) 7 : '?') {
                            case 7:
                                return false;
                            default:
                                return true;
                        }
                }
            }
        }
        return false;
    }

    final String b() throws c {
        int i = h + Opcodes.LSHR;
        f = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) ('0' - AndroidCharacter.getMirror('0')), ViewConfiguration.getTapTimeout() >> 16, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        g((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 30879), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 196, (Process.myTid() >> 22) + 19, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (!this.a) {
            g.c();
            Object[] objArr3 = new Object[1];
            g((char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), 216 - (ViewConfiguration.getTapTimeout() >> 16), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 45, objArr3);
            g.e(intern, ((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            g((char) ((Process.getThreadPriority(0) + 20) >> 6), View.MeasureSpec.getSize(0) + 262, 28 - KeyEvent.keyCodeFromString(""), objArr4);
            throw new c(((String) objArr4[0]).intern());
        }
        if (this.d == null) {
            g.c();
            Object[] objArr5 = new Object[1];
            g((char) (6367 - View.combineMeasuredStates(0, 0)), 291 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 44 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr5);
            g.e(intern, ((String) objArr5[0]).intern());
            Object[] objArr6 = new Object[1];
            g((char) (TextUtils.getTrimmedLength("") + 1741), ((byte) KeyEvent.getModifierMetaStateMask()) + 334, TextUtils.lastIndexOf("", '0', 0) + 24, objArr6);
            throw new c(((String) objArr6[0]).intern());
        }
        if (this.e == null) {
            g.c();
            Object[] objArr7 = new Object[1];
            g((char) (60530 - Drawable.resolveOpacity(0, 0)), (KeyEvent.getMaxKeyCode() >> 16) + 356, (ViewConfiguration.getTouchSlop() >> 8) + 39, objArr7);
            g.e(intern, ((String) objArr7[0]).intern());
            Object[] objArr8 = new Object[1];
            g((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 348), 395 - View.getDefaultSize(0, 0), Gravity.getAbsoluteGravity(0, 0) + 26, objArr8);
            throw new c(((String) objArr8[0]).intern());
        }
        try {
            Object[] objArr9 = new Object[1];
            g((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), TextUtils.lastIndexOf("", '0', 0) + 422, ExpandableListView.getPackedPositionChild(0L) + 21, objArr9);
            String intern2 = ((String) objArr9[0]).intern();
            int i3 = h + 93;
            f = i3 % 128;
            int i4 = i3 % 2;
            try {
                Object[] objArr10 = new Object[1];
                g((char) View.getDefaultSize(0, 0), 441 - TextUtils.indexOf("", ""), 19 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr10);
                Class<?> cls = Class.forName(((String) objArr10[0]).intern());
                Object[] objArr11 = new Object[1];
                g((char) (Process.myTid() >> 22), KeyEvent.getDeadChar(0, 0) + 460, KeyEvent.getDeadChar(0, 0) + 11, objArr11);
                Cipher cipher = (Cipher) cls.getMethod(((String) objArr11[0]).intern(), String.class).invoke(null, intern2);
                cipher.init(2, this.d);
                byte[] bArr = this.e;
                int i5 = f + 67;
                h = i5 % 128;
                int i6 = i5 % 2;
                try {
                    Object[] objArr12 = new Object[1];
                    g((char) View.MeasureSpec.makeMeasureSpec(0, 0), 441 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 19 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr12);
                    Class<?> cls2 = Class.forName(((String) objArr12[0]).intern());
                    Object[] objArr13 = new Object[1];
                    g((char) (20338 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), Color.blue(0) + 471, 7 - View.resolveSize(0, 0), objArr13);
                    byte[] bArr2 = (byte[]) cls2.getMethod(((String) objArr13[0]).intern(), byte[].class).invoke(cipher, bArr);
                    g.c();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr14 = new Object[1];
                    g((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 64789), 478 - Color.argb(0, 0, 0, 0), 52 - Color.alpha(0), objArr14);
                    g.d(intern, sb.append(((String) objArr14[0]).intern()).append(o.dk.b.e(bArr2)).toString());
                    String encodeToString = Base64.encodeToString(bArr2, 2);
                    int i7 = f + 85;
                    h = i7 % 128;
                    int i8 = i7 % 2;
                    return encodeToString;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 != null) {
                    throw cause2;
                }
                throw th2;
            }
        } catch (InvalidKeyException | NoSuchAlgorithmException | BadPaddingException | IllegalBlockSizeException | NoSuchPaddingException e) {
            g.c();
            Object[] objArr15 = new Object[1];
            g((char) ((-1) - MotionEvent.axisFromString("")), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 529, 53 - (Process.myTid() >> 22), objArr15);
            g.a(intern, ((String) objArr15[0]).intern(), e);
            throw new c(e.getMessage());
        }
    }

    final void c(Context context, String str) {
        g.c();
        Object[] objArr = new Object[1];
        g((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), KeyEvent.getDeadChar(0, 0), 21 - View.getDefaultSize(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g((char) TextUtils.getCapsMode("", 0, 0), TextUtils.indexOf("", "") + 583, 19 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        b(context, str);
        this.e = Base64.decode(str, 2);
        int i = h + Opcodes.DMUL;
        f = i % 128;
        int i2 = i % 2;
    }

    final void e(Context context) throws c {
        KeyPair generateKeyPair;
        Object[] objArr = new Object[1];
        g((char) (ViewConfiguration.getTapTimeout() >> 16), ViewConfiguration.getWindowTouchSlop() >> 8, 21 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        g((char) (50292 - (ViewConfiguration.getFadingEdgeLength() >> 16)), TextUtils.indexOf((CharSequence) "", '0') + 604, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 27, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (!this.a) {
            g.c();
            Object[] objArr3 = new Object[1];
            g((char) Color.argb(0, 0, 0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 631, 55 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr3);
            g.e(intern, ((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            g((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (Process.myTid() >> 22) + 262, ((byte) KeyEvent.getModifierMetaStateMask()) + 29, objArr4);
            throw new c(((String) objArr4[0]).intern());
        }
        try {
            Object[] objArr5 = new Object[1];
            g((char) (Process.myTid() >> 22), 686 - (ViewConfiguration.getTapTimeout() >> 16), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 3, objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            g((char) (1516 - View.getDefaultSize(0, 0)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 688, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 14, objArr6);
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(intern2, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            g((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 45743), 26 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 6, objArr7);
            KeyGenParameterSpec.Builder builder = new KeyGenParameterSpec.Builder(((String) objArr7[0]).intern(), 3);
            Object[] objArr8 = new Object[1];
            g((char) Gravity.getAbsoluteGravity(0, 0), 704 - (ViewConfiguration.getWindowTouchSlop() >> 8), 3 - View.resolveSizeAndState(0, 0, 0), objArr8);
            KeyGenParameterSpec.Builder blockModes = builder.setBlockModes(((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            g((char) TextUtils.indexOf("", "", 0, 0), 708 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), View.getDefaultSize(0, 0) + 12, objArr9);
            KeyGenParameterSpec.Builder keySize = blockModes.setEncryptionPaddings(((String) objArr9[0]).intern()).setKeySize(2048);
            Object[] objArr10 = new Object[1];
            g((char) (263 - Drawable.resolveOpacity(0, 0)), TextUtils.indexOf((CharSequence) "", '0', 0) + 720, 10 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr10);
            KeyGenParameterSpec.Builder attestationChallenge = keySize.setAttestationChallenge(((String) objArr10[0]).intern().getBytes(StandardCharsets.UTF_8));
            switch (Build.VERSION.SDK_INT >= 31) {
                case false:
                    break;
                default:
                    int i = h + 17;
                    f = i % 128;
                    int i2 = i % 2;
                    attestationChallenge.setDevicePropertiesAttestationIncluded(true);
                    break;
            }
            keyPairGenerator.initialize(attestationChallenge.build());
            try {
                generateKeyPair = keyPairGenerator.generateKeyPair();
            } catch (ProviderException e) {
                g.c();
                Object[] objArr11 = new Object[1];
                g((char) (TextUtils.lastIndexOf("", '0') + 64824), 729 - (ViewConfiguration.getTouchSlop() >> 8), 39 - MotionEvent.axisFromString(""), objArr11);
                g.a(intern, ((String) objArr11[0]).intern(), e);
                g.c();
                Object[] objArr12 = new Object[1];
                g((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), Color.rgb(0, 0, 0) + 16777985, 80 - (ViewConfiguration.getTouchSlop() >> 8), objArr12);
                g.d(intern, ((String) objArr12[0]).intern());
                attestationChallenge.setAttestationChallenge(null);
                if (Build.VERSION.SDK_INT >= 31) {
                    attestationChallenge.setDevicePropertiesAttestationIncluded(false);
                }
                keyPairGenerator.initialize(attestationChallenge.build());
                try {
                    generateKeyPair = keyPairGenerator.generateKeyPair();
                    int i3 = h + 9;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                } catch (ProviderException e2) {
                    g.c();
                    Object[] objArr13 = new Object[1];
                    g((char) (64823 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), 729 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 40, objArr13);
                    g.a(intern, ((String) objArr13[0]).intern(), e);
                    throw new c(e.getMessage());
                }
            }
            g.c();
            Object[] objArr14 = new Object[1];
            g((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 35376), 858 - ((Process.getThreadPriority(0) + 20) >> 6), TextUtils.lastIndexOf("", '0', 0) + 61, objArr14);
            g.d(intern, ((String) objArr14[0]).intern());
            this.d = generateKeyPair.getPrivate();
        } catch (InvalidAlgorithmParameterException | NoSuchAlgorithmException | NoSuchProviderException e3) {
            g.c();
            Object[] objArr15 = new Object[1];
            g((char) (64823 - TextUtils.getOffsetBefore("", 0)), 729 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 39, objArr15);
            g.a(intern, ((String) objArr15[0]).intern(), e3);
            throw new c(e3.getMessage());
        }
    }

    public final o.eg.e e() throws c {
        int length;
        int i;
        int i2;
        Object[] objArr = new Object[1];
        g((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), 1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        if (!d()) {
            Object[] objArr2 = new Object[1];
            g((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), Color.rgb(0, 0, 0) + 16777478, View.resolveSize(0, 0) + 28, objArr2);
            throw new c(((String) objArr2[0]).intern());
        }
        try {
            KeyStore keyStore = this.c;
            Object[] objArr3 = new Object[1];
            g((char) (45743 - Color.argb(0, 0, 0, 0)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 24, 6 - TextUtils.getTrimmedLength(""), objArr3);
            Certificate[] certificateChain = keyStore.getCertificateChain(((String) objArr3[0]).intern());
            switch (certificateChain == null ? 'K' : ')') {
                case ')':
                    o.eg.e eVar = new o.eg.e();
                    try {
                        length = certificateChain.length;
                        i = 0;
                        i2 = 0;
                    } catch (CertificateEncodingException | o.eg.d e) {
                        g.c();
                        Object[] objArr4 = new Object[1];
                        g((char) (37976 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 1115 - TextUtils.indexOf((CharSequence) "", '0', 0), 70 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr4);
                        g.e(intern, ((String) objArr4[0]).intern());
                        throw new c(e.getMessage());
                    }
                    while (true) {
                        switch (i < length ? 'H' : 'X') {
                            case 'H':
                                String encodeToString = Base64.encodeToString(certificateChain[i].getEncoded(), 10);
                                eVar.e(i2, encodeToString);
                                g.c();
                                Locale locale = Locale.getDefault();
                                Object[] objArr5 = new Object[1];
                                g((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 1061 - View.getDefaultSize(0, 0), Color.blue(0) + 55, objArr5);
                                g.d(intern, String.format(locale, ((String) objArr5[0]).intern(), Integer.valueOf(i2), encodeToString));
                                i2++;
                                i++;
                                int i3 = f + 21;
                                h = i3 % 128;
                                switch (i3 % 2 != 0 ? 'N' : '\r') {
                                }
                            default:
                                g.c();
                                StringBuilder sb = new StringBuilder();
                                Object[] objArr6 = new Object[1];
                                g((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 1186 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 67, objArr6);
                                g.d(intern, sb.append(((String) objArr6[0]).intern()).append(eVar).toString());
                                int i4 = f + 75;
                                h = i4 % 128;
                                int i5 = i4 % 2;
                                return eVar;
                        }
                        g.c();
                        Object[] objArr42 = new Object[1];
                        g((char) (37976 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 1115 - TextUtils.indexOf((CharSequence) "", '0', 0), 70 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr42);
                        g.e(intern, ((String) objArr42[0]).intern());
                        throw new c(e.getMessage());
                    }
                default:
                    g.c();
                    Object[] objArr7 = new Object[1];
                    g((char) (65329 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 992 - ((byte) KeyEvent.getModifierMetaStateMask()), TextUtils.getTrimmedLength("") + 68, objArr7);
                    g.d(intern, ((String) objArr7[0]).intern());
                    return null;
            }
        } catch (KeyStoreException e2) {
            g.c();
            Object[] objArr8 = new Object[1];
            g((char) (20810 - Color.green(0)), ExpandableListView.getPackedPositionChild(0L) + 919, (ViewConfiguration.getJumpTapTimeout() >> 16) + 75, objArr8);
            g.a(intern, ((String) objArr8[0]).intern(), e2);
            throw new c(e2.getMessage());
        }
    }

    public final void c(Context context) throws c {
        int i = h + 55;
        f = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((char) (ExpandableListView.getPackedPositionChild(0L) + 1), MotionEvent.axisFromString("") + 1, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), TextUtils.lastIndexOf("", '0', 0, 0) + 1253, 5 - (ViewConfiguration.getScrollBarSize() >> 8), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (!this.a) {
            g.c();
            Object[] objArr3 = new Object[1];
            g((char) KeyEvent.getDeadChar(0, 0), TextUtils.indexOf("", "", 0, 0), 21 - TextUtils.indexOf("", "", 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 1257 - Gravity.getAbsoluteGravity(0, 0), 32 - View.resolveSizeAndState(0, 0, 0), objArr4);
            g.e(intern2, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            g((char) TextUtils.getOffsetAfter("", 0), 262 - (ViewConfiguration.getScrollDefaultDelay() >> 16), 28 - (ViewConfiguration.getTouchSlop() >> 8), objArr5);
            throw new c(((String) objArr5[0]).intern());
        }
        try {
            a(context);
            a(this.c);
            int i3 = f + 11;
            h = i3 % 128;
            int i4 = i3 % 2;
        } catch (KeyStoreException e) {
            g.c();
            Object[] objArr6 = new Object[1];
            g((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), 21 - ((Process.getThreadPriority(0) + 20) >> 6), objArr6);
            String intern3 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            g((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), (-16775927) - Color.rgb(0, 0, 0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 27, objArr7);
            g.a(intern3, ((String) objArr7[0]).intern(), e);
            Object[] objArr8 = new Object[1];
            g((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 1317 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), 19 - TextUtils.getOffsetAfter("", 0), objArr8);
            throw new c(((String) objArr8[0]).intern());
        }
    }

    private static KeyStore j() throws KeyStoreException, CertificateException, NoSuchAlgorithmException, IOException {
        int i = f + 91;
        h = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 1515), 689 - (ViewConfiguration.getTapTimeout() >> 16), 15 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr);
        KeyStore keyStore = KeyStore.getInstance(((String) objArr[0]).intern());
        keyStore.load(null);
        int i3 = h + 27;
        f = i3 % 128;
        int i4 = i3 % 2;
        return keyStore;
    }

    private static PrivateKey d(KeyStore keyStore, String str) throws KeyStoreException, CertificateException, NoSuchAlgorithmException, IOException, NoSuchPaddingException, InvalidKeyException, UnrecoverableKeyException {
        g.c();
        Object[] objArr = new Object[1];
        g((char) (ViewConfiguration.getEdgeSlop() >> 16), View.getDefaultSize(0, 0), 21 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 36592), 1335 - ((Process.getThreadPriority(0) + 20) >> 6), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 49, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        switch (keyStore.getCertificate(str) == null ? 'V' : (char) 7) {
            case Opcodes.SASTORE /* 86 */:
                g.c();
                Object[] objArr3 = new Object[1];
                g((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), ViewConfiguration.getKeyRepeatTimeout() >> 16, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 22, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                g((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 62546), ImageFormat.getBitsPerPixel(0) + 1385, 57 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                return null;
            default:
                Key key = keyStore.getKey(str, null);
                switch (key == null ? (char) 24 : '(') {
                    case 24:
                        int i = h + Opcodes.LMUL;
                        f = i % 128;
                        int i2 = i % 2;
                        g.c();
                        Object[] objArr5 = new Object[1];
                        g((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.getDefaultSize(0, 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 21, objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        g((char) TextUtils.indexOf("", ""), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1440, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 55, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        int i3 = f + 15;
                        h = i3 % 128;
                        switch (i3 % 2 != 0 ? '\f' : 'P') {
                            case '\f':
                                throw null;
                            default:
                                return null;
                        }
                    default:
                        PrivateKey privateKey = (PrivateKey) key;
                        Object[] objArr7 = new Object[1];
                        g((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), View.MeasureSpec.getSize(0) + 421, Drawable.resolveOpacity(0, 0) + 20, objArr7);
                        try {
                            Object[] objArr8 = {((String) objArr7[0]).intern()};
                            Object[] objArr9 = new Object[1];
                            g((char) TextUtils.getOffsetBefore("", 0), 440 - MotionEvent.axisFromString(""), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, objArr9);
                            Class<?> cls = Class.forName(((String) objArr9[0]).intern());
                            Object[] objArr10 = new Object[1];
                            g((char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 460 - (Process.myPid() >> 22), 11 - Drawable.resolveOpacity(0, 0), objArr10);
                            ((Cipher) cls.getMethod(((String) objArr10[0]).intern(), String.class).invoke(null, objArr8)).init(2, privateKey);
                            g.c();
                            Object[] objArr11 = new Object[1];
                            g((char) (ViewConfiguration.getWindowTouchSlop() >> 8), (-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 20, objArr11);
                            String intern4 = ((String) objArr11[0]).intern();
                            Object[] objArr12 = new Object[1];
                            g((char) (48992 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (KeyEvent.getMaxKeyCode() >> 16) + 1496, 47 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr12);
                            g.d(intern4, ((String) objArr12[0]).intern());
                            return privateKey;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause != null) {
                                throw cause;
                            }
                            throw th;
                        }
                }
        }
    }

    private static byte[] b(Context context) {
        Object[] objArr = new Object[1];
        g((char) (63813 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), Color.rgb(0, 0, 0) + 16778758, 47 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        g((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 40492), 1589 - ExpandableListView.getPackedPositionType(0L), 8 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        String string = sharedPreferences.getString(((String) objArr2[0]).intern(), "");
        switch (string.isEmpty()) {
            case true:
                int i = h + 63;
                f = i % 128;
                int i2 = i % 2;
                g.c();
                Object[] objArr3 = new Object[1];
                g((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), ViewConfiguration.getEdgeSlop() >> 16, 21 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
                String intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                g((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 552), 1597 - (ViewConfiguration.getTouchSlop() >> 8), 42 - TextUtils.lastIndexOf("", '0'), objArr4);
                g.d(intern, ((String) objArr4[0]).intern());
                int i3 = f + 109;
                h = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return null;
                }
            default:
                g.c();
                Object[] objArr5 = new Object[1];
                g((char) ((-1) - Process.getGidForName("")), TextUtils.indexOf((CharSequence) "", '0', 0) + 1, (ViewConfiguration.getEdgeSlop() >> 16) + 21, objArr5);
                String intern2 = ((String) objArr5[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                g((char) (Process.myTid() >> 22), View.resolveSize(0, 0) + 1640, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 34, objArr6);
                g.d(intern2, sb.append(((String) objArr6[0]).intern()).append(string).toString());
                return Base64.decode(string, 2);
        }
    }

    private static void b(Context context, String str) {
        g.c();
        Object[] objArr = new Object[1];
        g((char) Color.blue(0), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 21 - View.MeasureSpec.getMode(0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g((char) TextUtils.getCapsMode("", 0, 0), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 1672, Color.red(0) + 35, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        Object[] objArr3 = new Object[1];
        g((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 63812), 1542 - (ViewConfiguration.getJumpTapTimeout() >> 16), KeyEvent.keyCodeFromString("") + 47, objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        g((char) (40493 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), 1589 - ((Process.getThreadPriority(0) + 20) >> 6), 8 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr4);
        edit.putString(((String) objArr4[0]).intern(), str).commit();
        int i = f + 67;
        h = i % 128;
        switch (i % 2 != 0 ? (char) 23 : (char) 16) {
            case 23:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private static void a(Context context) {
        int i = f + 1;
        h = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((char) View.resolveSizeAndState(0, 0, 0), ViewConfiguration.getDoubleTapTimeout() >> 16, 20 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) ExpandableListView.getPackedPositionType(0L), 1708 - (ViewConfiguration.getTapTimeout() >> 16), TextUtils.indexOf("", "", 0, 0) + 17, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g((char) (63813 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1542, TextUtils.getOffsetBefore("", 0) + 47, objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        g((char) (40493 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), 1589 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 8 - TextUtils.indexOf("", ""), objArr4);
        edit.putString(((String) objArr4[0]).intern(), "").commit();
        int i3 = f + 21;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? 'W' : '#') {
            case Opcodes.POP /* 87 */:
                throw null;
            default:
                return;
        }
    }

    private static void a(KeyStore keyStore) throws KeyStoreException {
        int i = f + 37;
        h = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), Color.alpha(0), 21 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 1724 - TextUtils.lastIndexOf("", '0', 0, 0), 13 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g((char) (45743 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), 25 - KeyEvent.keyCodeFromString(""), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 6, objArr3);
        keyStore.deleteEntry(((String) objArr3[0]).intern());
        int i3 = f + 91;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\e$c.smali */
    public static final class c extends Exception {
        c(String str) {
            super(str);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 742
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.e.g(char, int, int, java.lang.Object[]):void");
    }
}
