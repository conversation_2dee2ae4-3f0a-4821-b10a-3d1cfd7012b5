package com.vasco.digipass.sdk.obfuscated;

import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.models.SecureChannelMessage;
import com.vasco.digipass.sdk.responses.SecureChannelParseResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelParseResponse;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\n.smali */
public final class n implements DigipassSDKConstants {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\n$a.smali */
    public static class a {
        public byte b;
        public byte d;
        public boolean f;
        public final byte[] a = new byte[4];
        public final byte[] c = new byte[16];
        public final byte[] e = new byte[16];
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\n$b.smali */
    public static class b {
        public final byte[] a = new byte[16];
        public final byte[] b = new byte[3];
        public byte[] c;
        public byte[] d;
    }

    public static SecureChannelParseResponse a(String str) {
        UtilitiesSDKSecureChannelParseResponse parseSecureChannelMessage = UtilitiesSDK.parseSecureChannelMessage(str);
        if (parseSecureChannelMessage.getReturnCode() != 0) {
            switch (parseSecureChannelMessage.getReturnCode()) {
                case UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH /* -4215 */:
                    return new SecureChannelParseResponse(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH);
                case UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT /* -4214 */:
                    return new SecureChannelParseResponse(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
                case UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL /* -4213 */:
                    return new SecureChannelParseResponse(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL);
                default:
                    return new SecureChannelParseResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, parseSecureChannelMessage.getCause());
            }
        }
        UtilitiesSDKSecureChannelMessage message = parseSecureChannelMessage.getMessage();
        SecureChannelMessage secureChannelMessage = new SecureChannelMessage();
        secureChannelMessage.authenticationTag = message.authenticationTag;
        secureChannelMessage.body = message.body;
        secureChannelMessage.encrypted = message.encrypted;
        secureChannelMessage.messageType = message.messageType;
        secureChannelMessage.nonce = message.nonce;
        secureChannelMessage.protectionType = message.protectionType;
        secureChannelMessage.protocolVersion = message.protocolVersion;
        secureChannelMessage.rawData = message.rawData;
        secureChannelMessage.serialNumber = message.serialNumber;
        return new SecureChannelParseResponse(0, secureChannelMessage);
    }

    public static b b(SecureChannelMessage secureChannelMessage) {
        b bVar = new b();
        byte[] a2 = q.a(secureChannelMessage.body);
        byte b2 = a2[0];
        byte[] bArr = bVar.a;
        System.arraycopy(a2, 17, bArr, 0, bArr.length);
        byte[] bArr2 = bVar.b;
        System.arraycopy(a2, 36, bArr2, 0, bArr2.length);
        if (b2 == 1) {
            int length = a2.length - 39;
            byte[] bArr3 = new byte[length];
            bVar.c = bArr3;
            System.arraycopy(a2, 39, bArr3, 0, length);
        } else if (b2 == 2) {
            int length2 = a2.length - 39;
            byte[] bArr4 = new byte[length2];
            bVar.d = bArr4;
            System.arraycopy(a2, 39, bArr4, 0, length2);
        }
        return bVar;
    }

    public static a a(SecureChannelMessage secureChannelMessage) {
        a aVar = new a();
        byte[] a2 = q.a(secureChannelMessage.body);
        byte b2 = a2[0];
        byte[] bArr = aVar.a;
        System.arraycopy(a2, 1, bArr, 0, bArr.length);
        aVar.b = a2[5];
        byte[] bArr2 = aVar.c;
        System.arraycopy(a2, 6, bArr2, 0, bArr2.length);
        if (b2 == 0) {
            aVar.f = true;
            aVar.d = a2[22];
            byte[] bArr3 = aVar.e;
            System.arraycopy(a2, 23, bArr3, 0, bArr3.length);
        }
        return aVar;
    }
}
