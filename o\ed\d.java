package o.ed;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.view.accessibility.AccessibilityEventCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import fr.antelop.sdk.ui.LocaleManager;
import kotlin.text.Typography;
import o.ee.g;
import o.n.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\d.smali */
public final class d extends AppCompatActivity {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static e a;
    static c c;
    private static int f;
    private static int g;
    private static long i;
    private String b;
    private String d;
    private String e;
    private a h;
    private boolean j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        d();
        ViewConfiguration.getFadingEdgeLength();
        int i2 = g + 99;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 23 : Typography.less) {
            case '<':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void d() {
        i = -4203503988872914220L;
    }

    static void init$0() {
        $$a = new byte[]{32, 0, 62, 110};
        $$b = Opcodes.D2I;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 68
            int r9 = r9 * 2
            int r9 = 1 - r9
            byte[] r0 = o.ed.d.$$a
            int r8 = r8 * 3
            int r8 = 4 - r8
            byte[] r1 = new byte[r9]
            int r9 = r9 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L38
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L38:
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ed.d.l(int, int, short, java.lang.Object[]):void");
    }

    static void b(Context context, c cVar, e eVar) {
        c = cVar;
        a = eVar;
        Intent intent = new Intent(context, (Class<?>) d.class);
        intent.addFlags(AccessibilityEventCompat.TYPE_VIEW_TARGETED_BY_SCROLL);
        switch (context instanceof Activity) {
            case true:
                break;
            default:
                int i2 = f + 73;
                g = i2 % 128;
                boolean z = i2 % 2 == 0;
                intent.addFlags(268435456);
                switch (z) {
                    case false:
                        break;
                    default:
                        int i3 = 88 / 0;
                        break;
                }
                int i4 = g + 13;
                f = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
        context.startActivity(intent);
    }

    @Override // androidx.appcompat.app.AppCompatActivity, android.app.Activity, android.view.ContextThemeWrapper, android.content.ContextWrapper
    protected final void attachBaseContext(Context context) {
        int i2 = g + 13;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? 'W' : 'T') {
            case Opcodes.BASTORE /* 84 */:
                super.attachBaseContext(LocaleManager.getInstance().getLocalizedContext(context));
                return;
            default:
                super.attachBaseContext(LocaleManager.getInstance().getLocalizedContext(context));
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(View view) {
        int i2 = g + 11;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                finish();
                c.onDisplayCancelled();
                throw null;
            default:
                finish();
                c.onDisplayCancelled();
                int i3 = f + 21;
                g = i3 % 128;
                int i4 = i3 % 2;
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:52:0x01d3. Please report as an issue. */
    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected final void onCreate(Bundle bundle) {
        int i2;
        int i3 = f + 19;
        g = i3 % 128;
        int i4 = i3 % 2;
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
        super.onCreate(bundle);
        Object obj = null;
        switch (c == null ? '.' : '-') {
            case '-':
                setTheme(R.style.antelopSecurePinInputThemeInternal);
                if (a.d() != null) {
                    int i5 = g + Opcodes.LUSHR;
                    f = i5 % 128;
                    if (i5 % 2 != 0) {
                        a.d().onActivityStart(getApplicationContext());
                        obj.hashCode();
                        throw null;
                    }
                    a.d().onActivityStart(getApplicationContext());
                }
                Resources.Theme theme = getTheme();
                TypedValue typedValue = new TypedValue();
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_defaultTitle, typedValue, true);
                this.b = getResources().getString(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_defaultSubTitle, typedValue, true);
                this.d = getResources().getString(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_pinsNotMatchingErrorDescription, typedValue, true);
                this.e = getResources().getString(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_randomizeKeyboard, typedValue, true);
                this.j = getResources().getBoolean(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_showCloseButton, typedValue, true);
                boolean z = getResources().getBoolean(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_pinSize, typedValue, true);
                int integer = getResources().getInteger(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_colorPrimary, typedValue, true);
                int color = getResources().getColor(typedValue.resourceId, theme);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_colorSecondary, typedValue, true);
                switch (1) {
                    case 0:
                        int color2 = getResources().getColor(typedValue.resourceId);
                        int i6 = f + Opcodes.LSHL;
                        g = i6 % 128;
                        int i7 = i6 % 2;
                        i2 = color2;
                        break;
                    default:
                        i2 = getResources().getColor(typedValue.resourceId, theme);
                        break;
                }
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_bulletIcon, typedValue, true);
                int i8 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_deleteIcon, typedValue, true);
                int i9 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_alphaStyle, typedValue, true);
                int i10 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_displayAlpha, typedValue, true);
                boolean z2 = getResources().getBoolean(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_digitStyle, typedValue, true);
                int i11 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_backgroundStyle, typedValue, true);
                int i12 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_closeIcon, typedValue, true);
                int i13 = typedValue.resourceId;
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_enableOverlayProtection, typedValue, true);
                boolean z3 = getResources().getBoolean(typedValue.resourceId);
                theme.resolveAttribute(R.attr.antelopSecurePinInputThemeInternal_overlayWarningMessage, typedValue, true);
                String string = getResources().getString(typedValue.resourceId);
                setContentView(R.layout.antelop_pin_prompt_fragment);
                findViewById(R.id.antelop_pin_prompt_root).setBackgroundResource(R.color.antelopSecurePinInputColorBackground);
                o.n.a aVar = (o.n.a) findViewById(R.id.antelop_pin_prompt_keypad);
                if (z3) {
                    int i14 = g + 1;
                    f = i14 % 128;
                    if (i14 % 2 != 0) {
                        aVar.enableOverlayProtection(string);
                        int i15 = 73 / 0;
                    } else {
                        aVar.enableOverlayProtection(string);
                    }
                }
                aVar.initializeView(new a.InterfaceC0045a() { // from class: o.ed.d.4
                    private static int e = 0;
                    private static int d = 1;

                    @Override // o.n.a.InterfaceC0045a
                    public final void onExtraButtonPressed() {
                        int i16 = e;
                        int i17 = ((i16 | 49) << 1) - (i16 ^ 49);
                        d = i17 % 128;
                        int i18 = i17 % 2;
                    }

                    @Override // o.n.a.InterfaceC0045a
                    public final void onKeyPressed() {
                        int i16 = (e + 56) - 1;
                        d = i16 % 128;
                        int i17 = i16 % 2;
                    }

                    @Override // o.n.a.InterfaceC0045a
                    public final void onPasscodeEntryDone(byte[] bArr) {
                        int i16 = d;
                        int i17 = (i16 & 3) + (i16 | 3);
                        e = i17 % 128;
                        switch (i17 % 2 != 0 ? ' ' : (char) 26) {
                            case 26:
                                switch (d.c.b(bArr, d.this)) {
                                }
                                int i18 = d;
                                int i19 = (i18 ^ 71) + ((i18 & 71) << 1);
                                e = i19 % 128;
                                int i20 = i19 % 2;
                            default:
                                int i21 = 1 / 0;
                                switch (d.c.b(bArr, d.this) ? false : true) {
                                }
                                int i182 = d;
                                int i192 = (i182 ^ 71) + ((i182 & 71) << 1);
                                e = i192 % 128;
                                int i202 = i192 % 2;
                        }
                        int i22 = e;
                        int i23 = ((i22 | 41) << 1) - (i22 ^ 41);
                        d = i23 % 128;
                        int i24 = i23 % 2;
                        d.this.finish();
                        d.c.onDisplaySuccess();
                        int i25 = e;
                        int i26 = ((i25 | Opcodes.DNEG) << 1) - (i25 ^ Opcodes.DNEG);
                        d = i26 % 128;
                        int i27 = i26 % 2;
                        int i1822 = d;
                        int i1922 = (i1822 ^ 71) + ((i1822 & 71) << 1);
                        e = i1922 % 128;
                        int i2022 = i1922 % 2;
                    }
                }, new a.d(integer, i8, color, i2, i11, i10, i12, i9, 0, z2));
                if (z) {
                    ImageView imageView = (ImageView) findViewById(R.id.antelop_pin_prompt_cancel);
                    imageView.setImageResource(i13);
                    imageView.setVisibility(0);
                    imageView.setOnClickListener(new View.OnClickListener() { // from class: o.ed.d$$ExternalSyntheticLambda0
                        @Override // android.view.View.OnClickListener
                        public final void onClick(View view) {
                            d.this.b(view);
                        }
                    });
                    int i16 = f + Opcodes.DDIV;
                    g = i16 % 128;
                    switch (i16 % 2 != 0) {
                    }
                }
                e();
                this.h = new a();
                return;
            default:
                int i17 = g + 67;
                f = i17 % 128;
                switch (i17 % 2 == 0) {
                    case true:
                        finish();
                        return;
                    default:
                        finish();
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final void a(e eVar) {
        int i2 = f + Opcodes.DDIV;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 0 : '6') {
            case Opcodes.ISTORE /* 54 */:
                a = eVar;
                e();
                break;
            default:
                a = eVar;
                e();
                int i3 = 87 / 0;
                break;
        }
        int i4 = f + Opcodes.DSUB;
        g = i4 % 128;
        switch (i4 % 2 == 0 ? '9' : 'M') {
            case '9':
                throw null;
            default:
                return;
        }
    }

    private void e() {
        String str;
        String str2;
        String str3;
        switch (a.a() == null) {
            case true:
                str = this.b;
                break;
            default:
                int i2 = g + 23;
                f = i2 % 128;
                if (i2 % 2 != 0) {
                    a.a();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                str = a.a();
                break;
        }
        switch (a.e() != null ? 'R' : (char) 24) {
            case 24:
                str2 = this.d;
                break;
            default:
                str2 = a.e();
                break;
        }
        switch (a.b() != null ? '+' : '9') {
            case '9':
                str3 = this.e;
                break;
            default:
                int i3 = f + 77;
                g = i3 % 128;
                if (i3 % 2 == 0) {
                }
                str3 = a.b();
                break;
        }
        ((TextView) findViewById(R.id.antelop_pin_prompt_title)).setText(str);
        ((TextView) findViewById(R.id.antelop_pin_prompt_subtitle)).setText(str2);
        TextView textView = (TextView) findViewById(R.id.antelop_pin_prompt_description);
        switch (a.c() ? (char) 16 : (char) 5) {
            case 5:
                textView.setText("");
                textView.setVisibility(8);
                break;
            default:
                textView.setText(str3);
                textView.setVisibility(0);
                break;
        }
        b();
    }

    private void b() {
        int i2 = g + 29;
        f = i2 % 128;
        int i3 = i2 % 2;
        o.n.a aVar = (o.n.a) findViewById(R.id.antelop_pin_prompt_keypad);
        switch (this.j ? (char) 28 : (char) 1) {
            case 28:
                int i4 = g + Opcodes.LSHL;
                f = i4 % 128;
                boolean z = i4 % 2 != 0;
                aVar.scramble();
                switch (z) {
                    case true:
                        throw null;
                }
        }
        aVar.resetPasscode();
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public final void onBackPressed() {
        int i2 = f + Opcodes.DMUL;
        g = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k("隓\uf329雃鋼楋Ꚅ↳ﻚ\uf17d\uefc1໋\ud9fe䚮餃雼燈깢⇒︧\ue907㙗짻䙌腹鸻冷", (ViewConfiguration.getTapTimeout() >> 16) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("氃껈汬\uf5e9㒭솽讁ь겔䗁槻現밾쓘\uf1f4\udbdd哣", (ViewConfiguration.getLongPressTimeout() >> 16) + 1, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        super.onBackPressed();
        c.onDisplayCancelled();
        int i4 = g + Opcodes.LUSHR;
        f = i4 % 128;
        switch (i4 % 2 != 0 ? '1' : '-') {
            case '1':
                throw null;
            default:
                return;
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onDestroy() {
        switch (a.d() != null) {
            case true:
                int i2 = g + 99;
                f = i2 % 128;
                int i3 = i2 % 2;
                a.d().onActivityStop();
                int i4 = g + 17;
                f = i4 % 128;
                if (i4 % 2 == 0) {
                    break;
                }
                break;
        }
        super.onDestroy();
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    protected final void onNewIntent(Intent intent) {
        int i2 = f + 79;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? 'J' : '`') {
            case Opcodes.IADD /* 96 */:
                super.onNewIntent(intent);
                int i3 = g + 7;
                f = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return;
                    default:
                        int i4 = 55 / 0;
                        return;
                }
            default:
                super.onNewIntent(intent);
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 382
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ed.d.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
