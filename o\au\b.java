package o.au;

import android.content.Context;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.proxy.AuthApiStatusCodes;
import java.lang.reflect.Method;
import o.a.l;
import o.cf.i;
import o.cf.j;
import o.co.a;
import o.ee.g;
import o.ei.c;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\au\b.smali */
public final class b extends o.y.d<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int c;
    private static int e;
    a b;
    o.el.d d;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\au\b$e.smali */
    public interface e {
        void c();

        void d(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        n();
        int i = c + 77;
        e = i % 128;
        switch (i % 2 != 0 ? 'E' : (char) 25) {
            case 'E':
                int i2 = 82 / 0;
                break;
        }
    }

    static void init$0() {
        $$d = new byte[]{70, -116, 4, 37};
        $$e = 236;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = 122 - r8
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r0 = o.au.b.$$d
            int r7 = r7 * 4
            int r7 = 3 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L30
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r4 = -r4
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.au.b.m(int, short, short, java.lang.Object[]):void");
    }

    static void n() {
        a = new char[]{50941, 50855, 50851, 50849, 50851, 50837, 50836, 50852, 50857, 50841, 50836, 50851, 50848, 50858, 50858, 50852, 50838, 50838, 50854, 50878, 50848, 50849, 50878, 50839, 50847, 50854, 50851, 50850, 50937, 50852, 50826, 50918, 50918, 50826, 50851, 50875, 50861, 50832, 50872, 50816, 50922, 50844, 50819, 50918, 50918, 50819, 50869, 50876, 50850, 50816, 50818, 50874, 50816, 50934, 50835, 50876, 50878, 50877, 50851, 50826, 50943, 50943, 50922, 50844, 50877, 50851, 50856, 50861, 50877, 50869, 50879, 50876, 50869, 50834, 50842, 50877, 50878, 50873, 50877, 50850, 50878, 50876, 50878, 50832, 50835, 50779, 51179, 51179, 51171, 51173, 51178, 51171, 51160, 51136, 51179, 51172, 51175, 51179, 51176, 51172, 51178, 51172, 51166, 51161, 51177, 51154};
    }

    static /* synthetic */ void c(b bVar, o.el.d dVar) {
        int i = e + 73;
        c = i % 128;
        char c2 = i % 2 == 0 ? '@' : 'a';
        bVar.c(dVar);
        switch (c2) {
            case Opcodes.LADD /* 97 */:
                return;
            default:
                throw null;
        }
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = e + 57;
        c = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 93 / 0;
                return l();
            default:
                return l();
        }
    }

    public b(Context context, e eVar, c cVar) {
        super(context, eVar, cVar, o.bb.e.l);
    }

    public final void e(o.el.d dVar, a aVar) {
        int i = e + 69;
        c = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{0, 28, 0, 16}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{28, 57, 5, 36}, false, objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), dVar.n(), aVar.d()));
        this.d = dVar;
        this.b = aVar;
        c();
        int i3 = c + 63;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? '7' : '\r') {
            case '7':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private d l() {
        d dVar = new d(this);
        int i = c + Opcodes.DMUL;
        e = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = c + 83;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                k("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{0, 28, 0, 16}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{0, 28, 0, 16}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = c + 31;
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\au\b$d.smali */
    static final class d extends o.y.c<b> {
        private static int $10 = 0;
        private static int $11 = 1;
        private static int a = 0;
        private static int g = 1;
        private static char e = 61695;
        private static char b = 24246;
        private static char c = 672;
        private static char d = 25285;

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = a + 93;
            g = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = g + 81;
            a = i % 128;
            switch (i % 2 != 0 ? '%' : '7') {
                case '7':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        d(b bVar) {
            super(bVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = a + Opcodes.LNEG;
            g = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("\uee8f끥蚲喵\uf73aᘎ淡㽕舴沌ଐ\uf875퇪ৌ퇵\ue59a\ufde0Ԗ푍㛇玓镮", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 20, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = g + 89;
            a = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return intern;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("뉩ᗅצ뉉愔诵쪮釜革蝍Ρ䪚껂鲴옝\ueee1枆Ⅎ↼囕", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, objArr);
            o.cf.d dVar = new o.cf.d(context, 23, ((String) objArr[0]).intern());
            int i = a + Opcodes.LUSHR;
            g = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("冷\udd31襒횀켮쥊毙\uee50昺姉鼚峰\ue680鿻彭点", 16 - ((Process.getThreadPriority(0) + 20) >> 6), objArr);
            bVar.d(((String) objArr[0]).intern(), ((b) e()).d.n());
            Object[] objArr2 = new Object[1];
            w("筰҃⦃˓銀䮇ⓤ\ue52f嬹Ⴖ푍㛇彭点", 14 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((b) e()).b.d());
            Object[] objArr3 = new Object[1];
            w("⇋繊驎ꖒ\uf73aᘎ쌏\uda32约谄ᱦ衐毙\uee50仒㮃", 16 - View.MeasureSpec.getMode(0), objArr3);
            bVar.d(((String) objArr3[0]).intern(), ((b) e()).d.r());
            int i = g + 55;
            a = i % 128;
            switch (i % 2 != 0 ? ',' : (char) 14) {
                case 14:
                    return bVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = g + 67;
            int i2 = i % 128;
            a = i2;
            switch (i % 2 != 0 ? (char) 28 : (char) 23) {
                case 28:
                    throw null;
                default:
                    int i3 = i2 + 59;
                    g = i3 % 128;
                    int i4 = i3 % 2;
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = g;
            int i2 = i + 67;
            a = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 35;
            a = i4 % 128;
            Object obj = null;
            switch (i4 % 2 == 0) {
                case true:
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            switch (i) {
                case AuthApiStatusCodes.AUTH_API_SERVER_ERROR /* 3003 */:
                    return o.bb.a.au;
                case AuthApiStatusCodes.AUTH_TOKEN_ERROR /* 3004 */:
                    o.bb.a aVar = o.bb.a.aw;
                    int i2 = a + 41;
                    g = i2 % 128;
                    switch (i2 % 2 != 0) {
                        case false:
                            int i3 = 86 / 0;
                            return aVar;
                        default:
                            return aVar;
                    }
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i);
                    int i4 = a + 43;
                    g = i4 % 128;
                    int i5 = i4 % 2;
                    return c2;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = g + 19;
            a = i % 128;
            int i2 = i % 2;
            f().a().i().c(((b) e()).d, ((b) e()).b);
            f().d(g());
            int i3 = g + 37;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = a + 7;
            g = i % 128;
            switch (i % 2 == 0 ? '\n' : (char) 23) {
                case 23:
                    switch (AnonymousClass5.b[h().d().ordinal()]) {
                        case 1:
                        case 2:
                            b.c((b) e(), ((b) e()).d);
                            int i2 = g + 109;
                            a = i2 % 128;
                            int i3 = i2 % 2;
                            return;
                        case 3:
                            f().c(g(), ((b) e()).d.u());
                            return;
                        case 4:
                            f().e(g(), ((b) e()).d.u());
                            return;
                        default:
                            super.t();
                            return;
                    }
                default:
                    int i4 = AnonymousClass5.b[h().d().ordinal()];
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = g + 33;
            a = i % 128;
            Object obj = null;
            switch (i % 2 == 0) {
                case false:
                    ((b) e()).j().c();
                    throw null;
                default:
                    ((b) e()).j().c();
                    int i2 = a + 45;
                    g = i2 % 128;
                    switch (i2 % 2 == 0 ? '\b' : (char) 6) {
                        case 6:
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = a + 83;
            g = i % 128;
            int i2 = i % 2;
            ((b) e()).j().d(dVar);
            int i3 = g + 19;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r24, int r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 576
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.au.b.d.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.au.b$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\au\b$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] b;
        private static int c;
        private static int d = 1;

        static {
            c = 0;
            int[] iArr = new int[o.bb.a.values().length];
            b = iArr;
            try {
                iArr[o.bb.a.au.ordinal()] = 1;
                int i = (d + 114) - 1;
                c = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                b[o.bb.a.aw.ordinal()] = 2;
                int i3 = d;
                int i4 = (i3 & 61) + (i3 | 61);
                c = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[o.bb.a.ay.ordinal()] = 3;
                int i5 = d;
                int i6 = (i5 ^ 81) + ((i5 & 81) << 1);
                c = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                b[o.bb.a.az.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    private static void k(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        String str2 = str;
        int i = 0;
        byte[] bArr = str2;
        if (str2 != null) {
            int i2 = $11 + 87;
            $10 = i2 % 128;
            if (i2 % 2 != 0) {
                int i3 = 76 / 0;
                bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            } else {
                bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            }
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i4 = iArr[0];
        int i5 = iArr[1];
        int i6 = iArr[2];
        int i7 = iArr[3];
        char[] cArr3 = a;
        if (cArr3 != null) {
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int i8 = 0;
            while (true) {
                switch (i8 < length ? 1 : i) {
                    case 1:
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i] = Integer.valueOf(cArr3[i8]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr2 = cArr3;
                            } else {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 11, (char) (ViewConfiguration.getEdgeSlop() >> 16), 43 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                                byte b = (byte) i;
                                byte b2 = b;
                                cArr2 = cArr3;
                                Object[] objArr3 = new Object[1];
                                m(b, b2, (byte) (b2 + 2), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr4[i8] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i8++;
                            cArr3 = cArr2;
                            i = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        cArr3 = cArr4;
                        break;
                }
            }
        }
        char[] cArr5 = new char[i5];
        System.arraycopy(cArr3, i4, cArr5, 0, i5);
        switch (bArr2 != null) {
            case true:
                char[] cArr6 = new char[i5];
                lVar.d = 0;
                char c2 = 0;
                while (true) {
                    switch (lVar.d < i5 ? 'O' : (char) 29) {
                        case Opcodes.IASTORE /* 79 */:
                            if (bArr2[lVar.d] == 1) {
                                int i9 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                    Object obj2 = o.e.a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(11 - (ViewConfiguration.getEdgeSlop() >> 16), (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 449 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)));
                                        byte b3 = (byte) 0;
                                        byte b4 = b3;
                                        Object[] objArr5 = new Object[1];
                                        m(b3, b4, (byte) (b4 + 3), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj2);
                                    }
                                    cArr6[i9] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i10 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                    Object obj3 = o.e.a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(10 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) View.getDefaultSize(0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 207);
                                        byte b5 = (byte) 0;
                                        byte b6 = b5;
                                        Object[] objArr7 = new Object[1];
                                        m(b5, b6, b6, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj3);
                                    }
                                    cArr6[i10] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c2 = cArr6[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj4 = o.e.a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 12, (char) KeyEvent.getDeadChar(0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 259);
                                    byte b7 = (byte) 0;
                                    byte b8 = b7;
                                    Object[] objArr9 = new Object[1];
                                    m(b7, b8, (byte) (b8 | 56), objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        default:
                            int i11 = $11 + 17;
                            $10 = i11 % 128;
                            int i12 = i11 % 2;
                            cArr5 = cArr6;
                            break;
                    }
                }
        }
        switch (i7 > 0 ? (char) 0 : '9') {
            case '9':
                break;
            default:
                char[] cArr7 = new char[i5];
                System.arraycopy(cArr5, 0, cArr7, 0, i5);
                int i13 = i5 - i7;
                System.arraycopy(cArr7, 0, cArr5, i13, i7);
                System.arraycopy(cArr7, i7, cArr5, 0, i13);
                break;
        }
        switch (z ? 'T' : 'C') {
            case 'C':
                break;
            default:
                int i14 = $11 + 59;
                $10 = i14 % 128;
                if (i14 % 2 != 0) {
                    cArr = new char[i5];
                    lVar.d = 1;
                } else {
                    cArr = new char[i5];
                    lVar.d = 0;
                }
                while (lVar.d < i5) {
                    int i15 = $11 + Opcodes.LSHL;
                    $10 = i15 % 128;
                    int i16 = i15 % 2;
                    cArr[lVar.d] = cArr5[(i5 - lVar.d) - 1];
                    lVar.d++;
                }
                cArr5 = cArr;
                break;
        }
        if (i6 > 0) {
            lVar.d = 0;
            while (lVar.d < i5) {
                cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                lVar.d++;
            }
        }
        String str3 = new String(cArr5);
        int i17 = $11 + Opcodes.LNEG;
        $10 = i17 % 128;
        if (i17 % 2 != 0) {
            throw null;
        }
        objArr[0] = str3;
    }
}
