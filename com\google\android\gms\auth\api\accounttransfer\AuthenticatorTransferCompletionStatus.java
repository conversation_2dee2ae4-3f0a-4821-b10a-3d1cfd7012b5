package com.google.android.gms.auth.api.accounttransfer;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
@Retention(RetentionPolicy.SOURCE)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\auth\api\accounttransfer\AuthenticatorTransferCompletionStatus.smali */
public @interface AuthenticatorTransferCompletionStatus {
    public static final int COMPLETED_FAILURE = 2;
    public static final int COMPLETED_SUCCESS = 1;
}
