package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.ao.e;
import o.eo.j;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\q.smali */
public final class q extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static byte[] l;
    private static int m;

    /* renamed from: o, reason: collision with root package name */
    private static int f112o;
    private static int r;
    private static int s;
    private static short[] t;
    private final o.eo.j h;
    private final boolean i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        s = 1;
        a();
        Process.getElapsedCpuTime();
        TextUtils.indexOf("", "", 0, 0);
        KeyEvent.getModifierMetaStateMask();
        MotionEvent.axisFromString("");
        ViewConfiguration.getEdgeSlop();
        int i = s + Opcodes.LREM;
        r = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void a() {
        l = new byte[]{-62, -52, 58, 55, -24, 37, Base64.padSymbol, -34, -47, 24, -60, 35, -50, -51, -58, -36, 38, 40, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -46, -37, 4, -51, -34, -34, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 40, 53, -53, -40, 32, -43, 43, 50, -7, 45, 43, -44, -44, 55, 56, -15, 45, -53, 45, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -40, 3, -7, -43, -37, 52, -40, 52, -57, 43, -47, 38, 3, -25, -23, -27, 19, 26, -59, 5, 30, -28, 85, -83, -92, 83, 90, 69, -74, 90, 84, -94, -81, 112, -67, -91, 70, 73, ByteCompanionObject.MIN_VALUE, 92, -69, 86, 85, 94, 68, -112, -112, -112, -112};
        k = 909053651;
        m = -732398948;
        f112o = -579891015;
    }

    static void init$0() {
        $$d = new byte[]{48, 67, 97, 27};
        $$e = 218;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r8 = r8 * 2
            int r8 = 110 - r8
            int r6 = r6 * 3
            int r6 = 3 - r6
            byte[] r0 = o.v.q.$$d
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1d
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L39
        L1d:
            r3 = r2
        L1e:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            int r6 = r6 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L39:
            int r8 = r8 + r6
            r6 = r7
            r7 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.q.v(short, byte, short, java.lang.Object[]):void");
    }

    static /* synthetic */ o.p.g a(q qVar) {
        int i = s + 87;
        r = i % 128;
        int i2 = i % 2;
        o.p.g l2 = qVar.l();
        int i3 = s + 89;
        r = i3 % 128;
        switch (i3 % 2 != 0 ? '^' : '\b') {
            case Opcodes.DUP2_X2 /* 94 */:
                throw null;
            default:
                return l2;
        }
    }

    static /* synthetic */ o.p.g b(q qVar) {
        int i = r + 75;
        s = i % 128;
        switch (i % 2 == 0) {
            case false:
                return qVar.l();
            default:
                qVar.l();
                throw null;
        }
    }

    static /* synthetic */ o.p.g c(q qVar) {
        int i = r + 75;
        s = i % 128;
        int i2 = i % 2;
        o.p.g l2 = qVar.l();
        int i3 = s + Opcodes.LMUL;
        r = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 22 : '^') {
            case Opcodes.DUP2_X2 /* 94 */:
                return l2;
            default:
                int i4 = 92 / 0;
                return l2;
        }
    }

    static /* synthetic */ o.p.g d(q qVar) {
        int i = r + 9;
        s = i % 128;
        int i2 = i % 2;
        o.p.g l2 = qVar.l();
        int i3 = s + 77;
        r = i3 % 128;
        int i4 = i3 % 2;
        return l2;
    }

    static /* synthetic */ void e(q qVar) {
        int i = s + 57;
        r = i % 128;
        int i2 = i % 2;
        qVar.n();
        int i3 = r + Opcodes.LMUL;
        s = i3 % 128;
        switch (i3 % 2 == 0 ? '\n' : '?') {
            case '?':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g i(q qVar) {
        int i = r + 83;
        s = i % 128;
        int i2 = i % 2;
        o.p.g l2 = qVar.l();
        int i3 = s + 65;
        r = i3 % 128;
        int i4 = i3 % 2;
        return l2;
    }

    public q(String str, o.eo.e eVar, boolean z, o.eo.j jVar) {
        super(str, eVar);
        this.i = z;
        this.h = jVar;
    }

    @Override // o.p.h
    public final String d() {
        int i = s + 65;
        r = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        u((byte) (95 - Color.argb(0, 0, 0, 0)), 348086743 - View.resolveSize(0, 0), (short) View.MeasureSpec.makeMeasureSpec(0, 0), KeyEvent.normalizeMetaState(0) - 50, 495488074 + TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = r + Opcodes.LMUL;
        s = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 58 / 0;
                return intern;
            default:
                return intern;
        }
    }

    @Override // o.p.h
    public final void a(Context context, o.ei.c cVar, o.h.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((byte) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 74), View.getDefaultSize(0, 0) + 348086759, (short) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), Color.alpha(0) - 23, TextUtils.lastIndexOf("", '0', 0, 0) + 495488062, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((byte) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + Opcodes.FNEG), KeyEvent.getDeadChar(0, 0) + 348086802, (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 57, (Process.myTid() >> 22) + 495488102, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.ao.e(context, new e.a() { // from class: o.v.q.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static char[] b;
            private static int d;
            private static int e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                d = 0;
                e = 1;
                b = new char[]{50861, 50790, 50789, 50806, 50811, 50805, 50786, 50788, 50812, 50804, 50701, 50805, 50796, 50798, 50808, 50808, 50806, 50810, 50814, 50793, 50770, 50811, 50805, 50791, 50788, 50808, 50791, 50793, 50809, 50809, 50812, 50814, 50789, 50789, 50804, 50700, 50803, 50811, 50794, 50787, 50800, 50811, 50812, 50804, 50851, 50731, 50707, 50711, 50735, 50715, 50714, 50733, 50707, 50732, 50732, 50716, 50714, 50725, 50700, 50797, 50797, 50702, 50720, 50731, 50707, 50734, 50727, 50710, 50708, 50726, 50720, 50721, 50720, 50731, 50707, 51177, 51163, 51161, 51181, 51182, 51148, 51154, 51179, 51182, 50716, 50707, 50716, 51163, 51182, 51179, 51152, 51159, 51167, 50746, 51154, 51161, 51146, 51163, 51157, 51167, 51154, 51167, 50739, 51154, 51181, 50716, 50707, 50716, 51177};
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r7, short r8, byte r9, java.lang.Object[] r10) {
                /*
                    int r9 = r9 * 2
                    int r9 = r9 + 1
                    int r7 = 122 - r7
                    byte[] r0 = o.v.q.AnonymousClass4.$$a
                    int r8 = r8 + 4
                    byte[] r1 = new byte[r9]
                    r2 = 0
                    if (r0 != 0) goto L17
                    r7 = r9
                    r3 = r1
                    r4 = r2
                    r9 = r8
                    r1 = r0
                    r0 = r10
                    r10 = r7
                    goto L35
                L17:
                    r3 = r2
                    r6 = r9
                    r9 = r7
                    r7 = r6
                L1b:
                    int r4 = r3 + 1
                    byte r5 = (byte) r9
                    r1[r3] = r5
                    int r8 = r8 + 1
                    if (r4 != r7) goto L2c
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2c:
                    r3 = r0[r8]
                    r6 = r9
                    r9 = r8
                    r8 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r10
                    r10 = r6
                L35:
                    int r8 = r8 + r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r6 = r9
                    r9 = r8
                    r8 = r6
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.q.AnonymousClass4.g(int, short, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{49, -72, 33, -42};
                $$b = Opcodes.LSHR;
            }

            @Override // o.ao.e.a
            public final void c() {
                int i = d + 17;
                e = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f("\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000", new int[]{0, 44, 77, 1}, false, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000", new int[]{44, 31, Opcodes.ISHR, 27}, true, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                switch (q.d(q.this) != null ? '6' : '8') {
                    case '8':
                        break;
                    default:
                        q.b(q.this).onProcessSuccess();
                        break;
                }
                int i3 = d + 13;
                e = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.ao.e.a
            public final void c(o.bb.d dVar2) {
                o.bv.c c = o.bv.c.c(dVar2);
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f("\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000", new int[]{0, 44, 77, 1}, false, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                f(null, new int[]{75, 34, Opcodes.RETURN, 9}, true, objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(c).toString());
                switch (q.c(q.this) != null) {
                    case false:
                        break;
                    default:
                        int i = e + 19;
                        d = i % 128;
                        int i2 = i % 2;
                        switch (dVar2.d() == o.bb.a.aA ? '@' : '#') {
                            case '#':
                                q.i(q.this).onError(o.bv.c.c(dVar2));
                                break;
                            default:
                                int i3 = e + 97;
                                d = i3 % 128;
                                if (i3 % 2 != 0) {
                                }
                                q.e(q.this);
                                q.a(q.this).onAuthenticationDeclined();
                                break;
                        }
                }
            }

            private static void f(String str, int[] iArr, boolean z, Object[] objArr3) {
                char[] cArr;
                String str2 = str;
                byte[] bArr = str2;
                if (str2 != null) {
                    bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                }
                byte[] bArr2 = bArr;
                o.a.l lVar = new o.a.l();
                int i = 0;
                int i2 = iArr[0];
                int i3 = iArr[1];
                int i4 = 2;
                int i5 = iArr[2];
                int i6 = iArr[3];
                char[] cArr2 = b;
                switch (cArr2 != null ? 'R' : (char) 26) {
                    case 26:
                        break;
                    default:
                        int length = cArr2.length;
                        char[] cArr3 = new char[length];
                        int i7 = 0;
                        while (i7 < length) {
                            int i8 = $11 + Opcodes.LSHR;
                            $10 = i8 % 128;
                            int i9 = i8 % i4;
                            try {
                                Object[] objArr4 = new Object[1];
                                objArr4[i] = Integer.valueOf(cArr2[i7]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    cArr = cArr2;
                                } else {
                                    Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getJumpTapTimeout() >> 16), (char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (-16777173) - Color.rgb(i, i, i));
                                    byte b2 = (byte) ($$b & 6);
                                    byte b3 = (byte) (b2 - 3);
                                    cArr = cArr2;
                                    Object[] objArr5 = new Object[1];
                                    g(b2, b3, (byte) (b3 + 1), objArr5);
                                    obj = cls.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr4)).charValue();
                                i7++;
                                cArr2 = cArr;
                                i = 0;
                                i4 = 2;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        cArr2 = cArr3;
                        break;
                }
                char[] cArr4 = new char[i3];
                System.arraycopy(cArr2, i2, cArr4, 0, i3);
                switch (bArr2 != null ? 'c' : (char) 22) {
                    case 22:
                        break;
                    default:
                        char[] cArr5 = new char[i3];
                        lVar.d = 0;
                        char c = 0;
                        while (lVar.d < i3) {
                            int i10 = $10 + 11;
                            $11 = i10 % 128;
                            if (i10 % 2 != 0 ? bArr2[lVar.d] != 1 : bArr2[lVar.d] != 1) {
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = o.e.a.s.get(804049217);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), KeyEvent.keyCodeFromString("") + 207);
                                        byte b4 = (byte) 0;
                                        byte b5 = (byte) (b4 - 1);
                                        Object[] objArr7 = new Object[1];
                                        g(b4, b5, (byte) (b5 + 1), objArr7);
                                        obj2 = cls2.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj2);
                                    }
                                    cArr5[i11] = ((Character) ((Method) obj2).invoke(null, objArr6)).charValue();
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i12 = lVar.d;
                                try {
                                    Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj3 = o.e.a.s.get(2016040108);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(11 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (char) (MotionEvent.axisFromString("") + 1), ExpandableListView.getPackedPositionGroup(0L) + 448);
                                        byte b6 = (byte) ($$b & 7);
                                        byte b7 = (byte) (b6 - 4);
                                        Object[] objArr9 = new Object[1];
                                        g(b6, b7, (byte) (b7 + 1), objArr9);
                                        obj3 = cls3.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj3);
                                    }
                                    cArr5[i12] = ((Character) ((Method) obj3).invoke(null, objArr8)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c = cArr5[lVar.d];
                            try {
                                Object[] objArr10 = {lVar, lVar};
                                Object obj4 = o.e.a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(11 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) (ImageFormat.getBitsPerPixel(0) + 1), 259 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                    byte b8 = (byte) (-1);
                                    Object[] objArr11 = new Object[1];
                                    g((byte) ($$b & 188), b8, (byte) (b8 + 1), objArr11);
                                    obj4 = cls4.getMethod((String) objArr11[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr10);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        cArr4 = cArr5;
                        break;
                }
                if (i6 > 0) {
                    char[] cArr6 = new char[i3];
                    System.arraycopy(cArr4, 0, cArr6, 0, i3);
                    int i13 = i3 - i6;
                    System.arraycopy(cArr6, 0, cArr4, i13, i6);
                    System.arraycopy(cArr6, i6, cArr4, 0, i13);
                }
                switch (z) {
                    case true:
                        int i14 = $10 + 69;
                        $11 = i14 % 128;
                        int i15 = i14 % 2;
                        char[] cArr7 = new char[i3];
                        int i16 = 0;
                        while (true) {
                            lVar.d = i16;
                            if (lVar.d >= i3) {
                                int i17 = $11 + Opcodes.LSHL;
                                $10 = i17 % 128;
                                int i18 = i17 % 2;
                                cArr4 = cArr7;
                                break;
                            } else {
                                cArr7[lVar.d] = cArr4[(i3 - lVar.d) - 1];
                                i16 = lVar.d + 1;
                            }
                        }
                }
                if (i5 > 0) {
                    int i19 = 0;
                    while (true) {
                        lVar.d = i19;
                        switch (lVar.d < i3) {
                            case true:
                                int i20 = $10 + Opcodes.DNEG;
                                $11 = i20 % 128;
                                if (i20 % 2 == 0) {
                                    cArr4[lVar.d] = (char) (cArr4[lVar.d] >> iArr[3]);
                                    i19 = lVar.d * 0;
                                } else {
                                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                                    i19 = lVar.d + 1;
                                }
                        }
                    }
                }
                objArr3[0] = new String(cArr4);
            }
        }, cVar).b(dVar, o(), e.c.d, ((d) this).n, this.h);
        int i = r + 39;
        s = i % 128;
        int i2 = i % 2;
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = r;
        int i2 = i + 77;
        s = i2 % 128;
        int i3 = i2 % 2;
        if (!this.i) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            u((byte) ((-57) - ExpandableListView.getPackedPositionGroup(0L)), 348086811 + TextUtils.getOffsetAfter("", 0), (short) TextUtils.getCapsMode("", 0, 0), Color.red(0) - 43, Color.green(0) + 495488074, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        int i4 = i + 61;
        s = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    public final void e(Context context, o.p.g gVar) throws WalletValidationException {
        int i = r + Opcodes.LNEG;
        s = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                this.h.a();
                j.b bVar = j.b.b;
                obj.hashCode();
                throw null;
            default:
                if (this.h.a() != j.b.b) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    u((byte) ((Process.myTid() >> 22) + 95), KeyEvent.getDeadChar(0, 0) + 348086743, (short) (MotionEvent.axisFromString("") + 1), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 51, 495488074 - TextUtils.getOffsetBefore("", 0), objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                d(context, gVar);
                int i2 = s + Opcodes.DDIV;
                r = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:96:0x0333, code lost:
    
        r3 = r8;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1002
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.q.u(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
