package bc.org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\MD5Digest.smali */
public class MD5Digest extends a {
    private int e;
    private int f;
    private int g;
    private int h;
    private int[] i;
    private int j;

    public MD5Digest() {
        this(q1.ANY);
    }

    private int a(int i, int i2) {
        return (i >>> (32 - i2)) | (i << i2);
    }

    private int a(int i, int i2, int i3) {
        return ((~i) & i3) | (i2 & i);
    }

    private void a(MD5Digest mD5Digest) {
        super.a((a) mD5Digest);
        this.e = mD5Digest.e;
        this.f = mD5Digest.f;
        this.g = mD5Digest.g;
        this.h = mD5Digest.h;
        int[] iArr = mD5Digest.i;
        System.arraycopy(iArr, 0, this.i, 0, iArr.length);
        this.j = mD5Digest.j;
    }

    private int b(int i, int i2, int i3) {
        return (i & i3) | (i2 & (~i3));
    }

    private int c(int i, int i2, int i3) {
        return (i ^ i2) ^ i3;
    }

    private int d(int i, int i2, int i3) {
        return (i | (~i3)) ^ i2;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public m5 copy() {
        return new MD5Digest(this);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i) {
        finish();
        j6.b(this.e, bArr, i);
        j6.b(this.f, bArr, i + 4);
        j6.b(this.g, bArr, i + 8);
        j6.b(this.h, bArr, i + 12);
        reset();
        return 16;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public String getAlgorithmName() {
        return "MD5";
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int getDigestSize() {
        return 16;
    }

    public byte[] getEncodedState() {
        int i = (this.j * 4) + 36 + 1;
        byte[] bArr = new byte[i];
        super.a(bArr);
        j6.a(this.e, bArr, 16);
        j6.a(this.f, bArr, 20);
        j6.a(this.g, bArr, 24);
        j6.a(this.h, bArr, 28);
        j6.a(this.j, bArr, 32);
        for (int i2 = 0; i2 != this.j; i2++) {
            j6.a(this.i[i2], bArr, (i2 * 4) + 36);
        }
        bArr[i - 1] = (byte) this.a.ordinal();
        return bArr;
    }

    @Override // bc.org.bouncycastle.crypto.digests.a, bc.org.bouncycastle.crypto.Digest
    public void reset() {
        super.reset();
        this.e = 1732584193;
        this.f = -271733879;
        this.g = -1732584194;
        this.h = 271733878;
        this.j = 0;
        int i = 0;
        while (true) {
            int[] iArr = this.i;
            if (i == iArr.length) {
                return;
            }
            iArr[i] = 0;
            i++;
        }
    }

    public MD5Digest(q1 q1Var) {
        super(q1Var);
        this.i = new int[16];
        t1.a(g.a(this, 64, q1Var));
        reset();
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(byte[] bArr, int i) {
        int[] iArr = this.i;
        int i2 = this.j;
        this.j = i2 + 1;
        iArr[i2] = j6.c(bArr, i);
        if (this.j == 16) {
            a();
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public void reset(m5 m5Var) {
        a((MD5Digest) m5Var);
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(long j) {
        if (this.j > 14) {
            a();
        }
        int[] iArr = this.i;
        iArr[14] = (int) ((-1) & j);
        iArr[15] = (int) (j >>> 32);
    }

    public MD5Digest(byte[] bArr) {
        super(bArr);
        this.i = new int[16];
        this.e = j6.a(bArr, 16);
        this.f = j6.a(bArr, 20);
        this.g = j6.a(bArr, 24);
        this.h = j6.a(bArr, 28);
        this.j = j6.a(bArr, 32);
        for (int i = 0; i != this.j; i++) {
            this.i[i] = j6.a(bArr, (i * 4) + 36);
        }
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a() {
        int i = this.e;
        int i2 = this.f;
        int i3 = this.g;
        int i4 = this.h;
        int a = a(((i + a(i2, i3, i4)) + this.i[0]) - 680876936, 7) + i2;
        int a2 = a(((i4 + a(a, i2, i3)) + this.i[1]) - 389564586, 12) + a;
        int a3 = a(i3 + a(a2, a, i2) + this.i[2] + 606105819, 17) + a2;
        int a4 = a(((i2 + a(a3, a2, a)) + this.i[3]) - 1044525330, 22) + a3;
        int a5 = a(((a + a(a4, a3, a2)) + this.i[4]) - 176418897, 7) + a4;
        int a6 = a(a2 + a(a5, a4, a3) + this.i[5] + 1200080426, 12) + a5;
        int a7 = a(((a3 + a(a6, a5, a4)) + this.i[6]) - 1473231341, 17) + a6;
        int a8 = a(((a4 + a(a7, a6, a5)) + this.i[7]) - 45705983, 22) + a7;
        int a9 = a(a5 + a(a8, a7, a6) + this.i[8] + 1770035416, 7) + a8;
        int a10 = a(((a6 + a(a9, a8, a7)) + this.i[9]) - 1958414417, 12) + a9;
        int a11 = a(((a7 + a(a10, a9, a8)) + this.i[10]) - 42063, 17) + a10;
        int a12 = a(((a8 + a(a11, a10, a9)) + this.i[11]) - 1990404162, 22) + a11;
        int a13 = a(a9 + a(a12, a11, a10) + this.i[12] + 1804603682, 7) + a12;
        int a14 = a(((a10 + a(a13, a12, a11)) + this.i[13]) - 40341101, 12) + a13;
        int a15 = a(((a11 + a(a14, a13, a12)) + this.i[14]) - 1502002290, 17) + a14;
        int a16 = a(a12 + a(a15, a14, a13) + this.i[15] + 1236535329, 22) + a15;
        int a17 = a(((a13 + b(a16, a15, a14)) + this.i[1]) - 165796510, 5) + a16;
        int a18 = a(((a14 + b(a17, a16, a15)) + this.i[6]) - 1069501632, 9) + a17;
        int a19 = a(a15 + b(a18, a17, a16) + this.i[11] + 643717713, 14) + a18;
        int a20 = a(((a16 + b(a19, a18, a17)) + this.i[0]) - 373897302, 20) + a19;
        int a21 = a(((a17 + b(a20, a19, a18)) + this.i[5]) - 701558691, 5) + a20;
        int a22 = a(a18 + b(a21, a20, a19) + this.i[10] + 38016083, 9) + a21;
        int a23 = a(((a19 + b(a22, a21, a20)) + this.i[15]) - 660478335, 14) + a22;
        int a24 = a(((a20 + b(a23, a22, a21)) + this.i[4]) - 405537848, 20) + a23;
        int a25 = a(a21 + b(a24, a23, a22) + this.i[9] + 568446438, 5) + a24;
        int a26 = a(((a22 + b(a25, a24, a23)) + this.i[14]) - 1019803690, 9) + a25;
        int a27 = a(((a23 + b(a26, a25, a24)) + this.i[3]) - 187363961, 14) + a26;
        int a28 = a(a24 + b(a27, a26, a25) + this.i[8] + 1163531501, 20) + a27;
        int a29 = a(((a25 + b(a28, a27, a26)) + this.i[13]) - 1444681467, 5) + a28;
        int a30 = a(((a26 + b(a29, a28, a27)) + this.i[2]) - 51403784, 9) + a29;
        int a31 = a(a27 + b(a30, a29, a28) + this.i[7] + 1735328473, 14) + a30;
        int a32 = a(((a28 + b(a31, a30, a29)) + this.i[12]) - 1926607734, 20) + a31;
        int a33 = a(((a29 + c(a32, a31, a30)) + this.i[5]) - 378558, 4) + a32;
        int a34 = a(((a30 + c(a33, a32, a31)) + this.i[8]) - 2022574463, 11) + a33;
        int a35 = a(a31 + c(a34, a33, a32) + this.i[11] + 1839030562, 16) + a34;
        int a36 = a(((a32 + c(a35, a34, a33)) + this.i[14]) - 35309556, 23) + a35;
        int a37 = a(((a33 + c(a36, a35, a34)) + this.i[1]) - 1530992060, 4) + a36;
        int a38 = a(a34 + c(a37, a36, a35) + this.i[4] + 1272893353, 11) + a37;
        int a39 = a(((a35 + c(a38, a37, a36)) + this.i[7]) - 155497632, 16) + a38;
        int a40 = a(((a36 + c(a39, a38, a37)) + this.i[10]) - 1094730640, 23) + a39;
        int a41 = a(a37 + c(a40, a39, a38) + this.i[13] + 681279174, 4) + a40;
        int a42 = a(((a38 + c(a41, a40, a39)) + this.i[0]) - 358537222, 11) + a41;
        int a43 = a(((a39 + c(a42, a41, a40)) + this.i[3]) - 722521979, 16) + a42;
        int a44 = a(a40 + c(a43, a42, a41) + this.i[6] + 76029189, 23) + a43;
        int a45 = a(((a41 + c(a44, a43, a42)) + this.i[9]) - 640364487, 4) + a44;
        int a46 = a(((a42 + c(a45, a44, a43)) + this.i[12]) - 421815835, 11) + a45;
        int a47 = a(a43 + c(a46, a45, a44) + this.i[15] + 530742520, 16) + a46;
        int a48 = a(((a44 + c(a47, a46, a45)) + this.i[2]) - 995338651, 23) + a47;
        int a49 = a(((a45 + d(a48, a47, a46)) + this.i[0]) - 198630844, 6) + a48;
        int a50 = a(a46 + d(a49, a48, a47) + this.i[7] + 1126891415, 10) + a49;
        int a51 = a(((a47 + d(a50, a49, a48)) + this.i[14]) - 1416354905, 15) + a50;
        int a52 = a(((a48 + d(a51, a50, a49)) + this.i[5]) - 57434055, 21) + a51;
        int a53 = a(a49 + d(a52, a51, a50) + this.i[12] + 1700485571, 6) + a52;
        int a54 = a(((a50 + d(a53, a52, a51)) + this.i[3]) - 1894986606, 10) + a53;
        int a55 = a(((a51 + d(a54, a53, a52)) + this.i[10]) - 1051523, 15) + a54;
        int a56 = a(((a52 + d(a55, a54, a53)) + this.i[1]) - 2054922799, 21) + a55;
        int a57 = a(a53 + d(a56, a55, a54) + this.i[8] + 1873313359, 6) + a56;
        int a58 = a(((a54 + d(a57, a56, a55)) + this.i[15]) - 30611744, 10) + a57;
        int a59 = a(((a55 + d(a58, a57, a56)) + this.i[6]) - 1560198380, 15) + a58;
        int a60 = a(a56 + d(a59, a58, a57) + this.i[13] + 1309151649, 21) + a59;
        int a61 = a(((a57 + d(a60, a59, a58)) + this.i[4]) - 145523070, 6) + a60;
        int a62 = a(((a58 + d(a61, a60, a59)) + this.i[11]) - 1120210379, 10) + a61;
        int a63 = a(a59 + d(a62, a61, a60) + this.i[2] + 718787259, 15) + a62;
        int a64 = a(((a60 + d(a63, a62, a61)) + this.i[9]) - 343485551, 21) + a63;
        this.e += a61;
        this.f += a64;
        this.g += a63;
        this.h += a62;
        this.j = 0;
        int i5 = 0;
        while (true) {
            int[] iArr = this.i;
            if (i5 == iArr.length) {
                return;
            }
            iArr[i5] = 0;
            i5++;
        }
    }

    public MD5Digest(MD5Digest mD5Digest) {
        super(mD5Digest);
        this.i = new int[16];
        a(mD5Digest);
    }
}
