package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p3.smali */
public class p3 extends u {
    private int C;
    private int L;
    private int b;
    private int x;

    private p3(e0 e0Var) {
        this.b = r.a(e0Var.a(0)).j();
        if (e0Var.a(1) instanceof r) {
            this.x = ((r) e0Var.a(1)).j();
        } else {
            if (!(e0Var.a(1) instanceof e0)) {
                throw new IllegalArgumentException("object parse error");
            }
            e0 a = e0.a(e0Var.a(1));
            this.x = r.a(a.a(0)).j();
            this.C = r.a(a.a(1)).j();
            this.L = r.a(a.a(2)).j();
        }
    }

    public static p3 a(Object obj) {
        if (obj instanceof p3) {
            return (p3) obj;
        }
        if (obj != null) {
            return new p3(e0.a(obj));
        }
        return null;
    }

    public int e() {
        return this.x;
    }

    public int f() {
        return this.C;
    }

    public int g() {
        return this.L;
    }

    public int h() {
        return this.b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(new r(this.b));
        if (this.C == 0) {
            iVar.a(new r(this.x));
        } else {
            i iVar2 = new i(3);
            iVar2.a(new r(this.x));
            iVar2.a(new r(this.C));
            iVar2.a(new r(this.L));
            iVar.a(new j2(iVar2));
        }
        return new j2(iVar);
    }
}
