package o.dq;

import android.telephony.cdma.CdmaCellLocation;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.io.File;
import java.net.URI;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dq\c.smali */
public final class c implements d<URI> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    public static final c b;
    private static long c;
    private static int d;
    private static char[] e;

    static void b() {
        e = new char[]{11420, 33562, 29666, 8824, 37416, 17025, 12640, 57818, 20900, '2', 61670, 42833, 5930, 51089, 46709, 26309, 54972, 34091, 30196, 9295, 37935, 17567, 15226, 60410, 23476, 2579, 64252, 43350, 6448, 51590, 47212, 26827};
        c = -3815295051874663576L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 102
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r0 = o.dq.c.$$a
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L30
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r7 = r7 + 1
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dq.c.g(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{113, -79, 113, -105};
        $$b = Opcodes.IXOR;
    }

    @Override // o.dq.d
    public final /* synthetic */ URI a(File file) throws o.du.e {
        int i = d + 73;
        a = i % 128;
        int i2 = i % 2;
        URI b2 = b(file);
        int i3 = a + 17;
        d = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return b2;
            default:
                int i4 = 41 / 0;
                return b2;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        a = 1;
        b();
        b = new c();
        int i = d + 55;
        a = i % 128;
        int i2 = i % 2;
    }

    private static URI b(File file) throws o.du.e {
        int i = a + 27;
        d = i % 128;
        char c2 = i % 2 != 0 ? ')' : 'b';
        URI uri = file.toURI();
        switch (c2) {
            case ')':
                int i2 = 97 / 0;
            default:
                return uri;
        }
    }

    @Override // o.dq.d
    public final Class<URI> a() {
        int i = d + 23;
        a = i % 128;
        switch (i % 2 == 0 ? 'P' : 'I') {
            case 'P':
                throw null;
            default:
                return URI.class;
        }
    }

    public final String toString() {
        Object obj;
        int i = a + 33;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                f((char) (ViewConfiguration.getWindowTouchSlop() >> 8), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 32 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f((char) (ViewConfiguration.getWindowTouchSlop() >> 50), (CdmaCellLocation.convertQuartSecToDecDegrees(1) > 1.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(1) == 1.0d ? 0 : -1)), 23 >> (ViewConfiguration.getLongPressTimeout() << 54), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = d + 23;
        a = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 602
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dq.c.f(char, int, int, java.lang.Object[]):void");
    }
}
