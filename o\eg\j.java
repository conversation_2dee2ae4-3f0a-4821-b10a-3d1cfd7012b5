package o.eg;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.f;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\j.smali */
public final class j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int c;
    private static int d;
    private static int f;
    private static short[] g;
    private static int h;
    private static byte[] i;
    private static int j;
    private int a;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        b = 9014996644581455830L;
        i = new byte[]{80, 6, 9, -8, 0, -2, 12, 67, PSSSigner.TRAILER_IMPLICIT, -1, -15, 19, -13, 5, -4, -5, 13, -15, 6, 25, 47, 0, -4, 14, 7, -8, -86, 69, 6, 8, -22, 10, -4, 5, 2, -12, 8, -1, -32, 85, 126, 77, -102, 109, -111, 104, -100, -108, 106, -53, 35, 109, -105, 102, 104, -106, -35, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 99, 101, -109, 115, -112, -67, 82, ByteCompanionObject.MAX_VALUE, -122, -124, 100, -39, 59, -124, 96, -126, 126, -124, 114, -61, 51, 112, 126, -98, 113, 122, 119, PSSSigner.TRAILER_IMPLICIT, 32, Base64.padSymbol, -54, 58, 62, 114, 47, 90, -7, 5, -6, -15, -75, 13, 25, -25, -13, 72, 11, 5, -27, 10, 1, 12, -57, 46, -26, 17, -2, -17, -65, 66, 1, 15, -19, 13, -5, 2, 5, -13, 15, -8, -25, 42, -34, 125, -127, 97, 114, -97, 97, -119, 117, 51, -36, 99, 49};
        c = 909053636;
        j = 1554413315;
        d = -716491089;
    }

    static void init$0() {
        $$a = new byte[]{38, -75, -91, -62};
        $$b = 49;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.eg.j.$$a
            int r7 = r7 + 4
            int r6 = r6 + 68
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r7 = r7 + 1
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = -r7
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.m(int, byte, int, java.lang.Object[]):void");
    }

    public j(String str) {
        if (str != null) {
            Object[] objArr = new Object[1];
            k("뀿什\ud84a큌嘫", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 1, objArr);
            if (str.startsWith(((String) objArr[0]).intern())) {
                str = str.substring(1);
            }
        }
        this.e = str;
    }

    public final Object c() throws d {
        int i2 = h + 73;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                e();
                throw null;
            default:
                int e = e();
                switch (e) {
                    case -1:
                        Object[] objArr = new Object[1];
                        k("ꁫꀮ삪㟍疅䗔簠\ue486鯳ꃯ凥憹ᡍ飬㷜㷸", (ViewConfiguration.getFadingEdgeLength() >> 16) + 1, objArr);
                        throw b(((String) objArr[0]).intern());
                    case 34:
                    case 39:
                        String c2 = c((char) e);
                        int i3 = h + 89;
                        f = i3 % 128;
                        switch (i3 % 2 != 0) {
                            case true:
                                int i4 = 6 / 0;
                                return c2;
                            default:
                                return c2;
                        }
                    case Opcodes.DUP_X2 /* 91 */:
                        return h();
                    case Opcodes.LSHR /* 123 */:
                        return f();
                    default:
                        this.a--;
                        Object b2 = b();
                        int i5 = f + 7;
                        h = i5 % 128;
                        switch (i5 % 2 != 0) {
                            case true:
                                return b2;
                            default:
                                throw null;
                        }
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private int e() throws o.eg.d {
        /*
            Method dump skipped, instructions count: 280
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.e():int");
    }

    private void a() {
        while (this.a < this.e.length()) {
            char charAt = this.e.charAt(this.a);
            if (charAt != '\r') {
                int i2 = f + 65;
                h = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 31 : (char) 3) {
                    case 31:
                        switch (charAt != '!') {
                            case false:
                                break;
                            default:
                                this.a++;
                        }
                    default:
                        switch (charAt != '\n') {
                            case true:
                                this.a++;
                        }
                        break;
                }
            }
            this.a++;
            int i3 = h + 83;
            f = i3 % 128;
            if (i3 % 2 != 0) {
                int i4 = 97 / 0;
                return;
            }
            return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private java.lang.String c(char r12) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 274
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.c(char):java.lang.String");
    }

    private char d() throws d {
        String str = this.e;
        int i2 = this.a;
        this.a = i2 + 1;
        char charAt = str.charAt(i2);
        Object obj = null;
        switch (charAt) {
            case Opcodes.FADD /* 98 */:
                return '\b';
            case 'f':
                return '\f';
            case Opcodes.FDIV /* 110 */:
                return '\n';
            case 'r':
                return '\r';
            case Opcodes.INEG /* 116 */:
                int i3 = f + Opcodes.DDIV;
                h = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return '\t';
                }
            case Opcodes.LNEG /* 117 */:
                if (this.a + 4 > this.e.length()) {
                    Object[] objArr = new Object[1];
                    k("笋筞Ⱶ\udb12\ueb87\u0feaꝝࡒס\uea94쾾⯅쌢琷ꏞ瞓\uef47倂蟬鍰௺볻嬦뼟㟌飊㽖\udb2e厶쒝፨\ue702", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
                    throw b(((String) objArr[0]).intern());
                }
                String str2 = this.e;
                int i4 = this.a;
                String substring = str2.substring(i4, i4 + 4);
                this.a += 4;
                try {
                    return (char) Integer.parseInt(substring, 16);
                } catch (NumberFormatException e) {
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    l((byte) (8 - (ViewConfiguration.getWindowTouchSlop() >> 8)), 51224 - AndroidCharacter.getMirror('0'), (short) Gravity.getAbsoluteGravity(0, 0), (-86) - TextUtils.lastIndexOf("", '0', 0, 0), TextUtils.indexOf("", "", 0) - 1787394378, objArr2);
                    throw b(sb.append(((String) objArr2[0]).intern()).append(substring).toString());
                }
            default:
                int i5 = h + Opcodes.LUSHR;
                f = i5 % 128;
                switch (i5 % 2 == 0) {
                    case false:
                        obj.hashCode();
                        throw null;
                    default:
                        return charAt;
                }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:38:0x01ea  */
    /* JADX WARN: Removed duplicated region for block: B:43:0x01f1  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private java.lang.Object b() throws o.eg.d {
        /*
            Method dump skipped, instructions count: 578
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.b():java.lang.Object");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private java.lang.String c(java.lang.String r6) {
        /*
            r5 = this;
            int r0 = r5.a
        L3:
            int r1 = r5.a
            java.lang.String r2 = r5.e
            int r2 = r2.length()
            if (r1 >= r2) goto L10
            r1 = 41
            goto L12
        L10:
            r1 = 18
        L12:
            switch(r1) {
                case 18: goto L22;
                default: goto L15;
            }
        L15:
            int r1 = o.eg.j.f
            int r1 = r1 + 85
            int r2 = r1 % 128
            o.eg.j.h = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L3f
            goto L3c
        L22:
            java.lang.String r6 = r5.e
            java.lang.String r6 = r6.substring(r0)
            int r0 = o.eg.j.h
            int r0 = r0 + 47
            int r1 = r0 % 128
            o.eg.j.f = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L35
            return r6
        L35:
            r6 = 0
            r6.hashCode()     // Catch: java.lang.Throwable -> L3a
            throw r6     // Catch: java.lang.Throwable -> L3a
        L3a:
            r6 = move-exception
            throw r6
        L3c:
            r1 = 11
            goto L41
        L3f:
            r1 = 33
        L41:
            r2 = 1
            switch(r1) {
                case 33: goto L52;
                default: goto L45;
            }
        L45:
            java.lang.String r1 = r5.e
            int r3 = r5.a
            char r1 = r1.charAt(r3)
            r3 = 63
            if (r1 == r3) goto L61
            goto L5f
        L52:
            java.lang.String r1 = r5.e
            int r3 = r5.a
            char r1 = r1.charAt(r3)
            r3 = 13
            if (r1 == r3) goto L8c
        L5e:
            goto L66
        L5f:
            r3 = 0
            goto L62
        L61:
            r3 = r2
        L62:
            switch(r3) {
                case 0: goto L5e;
                default: goto L65;
            }
        L65:
            goto L8c
        L66:
            r3 = 10
            if (r1 == r3) goto L6d
            r3 = 90
            goto L6f
        L6d:
            r3 = 93
        L6f:
            switch(r3) {
                case 90: goto L73;
                default: goto L72;
            }
        L72:
            goto L8c
        L73:
            int r3 = o.eg.j.f
            int r3 = r3 + 95
            int r4 = r3 % 128
            o.eg.j.h = r4
            int r3 = r3 % 2
            int r1 = r6.indexOf(r1)
            r3 = -1
            if (r1 == r3) goto L85
            goto L8c
        L85:
            int r1 = r5.a
            int r1 = r1 + r2
            r5.a = r1
            goto L3
        L8c:
            java.lang.String r6 = r5.e
            int r1 = r5.a
            java.lang.String r6 = r6.substring(r0, r1)
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.c(java.lang.String):java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0032. Please report as an issue. */
    private o.eg.b f() throws o.eg.d {
        /*
            Method dump skipped, instructions count: 422
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.f():o.eg.b");
    }

    private e h() throws d {
        e eVar = new e();
        boolean z = false;
        while (true) {
            switch (e()) {
                case -1:
                    Object[] objArr = new Object[1];
                    l((byte) (110 - ((Process.getThreadPriority(0) + 20) >> 6)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 479971376, (short) (ViewConfiguration.getKeyRepeatDelay() >> 16), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 85, (-1787394366) - KeyEvent.normalizeMetaState(0), objArr);
                    throw b(((String) objArr[0]).intern());
                case 44:
                case 59:
                    eVar.b((Object) null);
                    z = true;
                    break;
                case Opcodes.DUP2_X1 /* 93 */:
                    switch (z ? 'S' : '4') {
                        case Opcodes.AASTORE /* 83 */:
                            eVar.b((Object) null);
                        default:
                            return eVar;
                    }
                default:
                    this.a--;
                    eVar.b(c());
                    switch (e()) {
                        case 44:
                        case 59:
                            int i2 = h + 67;
                            f = i2 % 128;
                            int i3 = i2 % 2;
                            z = true;
                            break;
                        case Opcodes.DUP2_X1 /* 93 */:
                            int i4 = f + 83;
                            h = i4 % 128;
                            int i5 = i4 % 2;
                            return eVar;
                        default:
                            Object[] objArr2 = new Object[1];
                            l((byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + Opcodes.FDIV), TextUtils.lastIndexOf("", '0') + 479971377, (short) (ImageFormat.getBitsPerPixel(0) + 1), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 86, (-1787394365) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr2);
                            throw b(((String) objArr2[0]).intern());
                    }
            }
        }
    }

    private d b(String str) {
        d dVar = new d(new StringBuilder().append(str).append(this).toString());
        int i2 = h + Opcodes.DDIV;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 16 : '^') {
            case 16:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return dVar;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        l((byte) ((-32) - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 479971394 - View.getDefaultSize(0, 0), (short) Color.alpha(0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 86, (-1787394419) - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.a);
        Object[] objArr2 = new Object[1];
        k("歲歒랝䃻觌ᯄ枸\ufeff", View.MeasureSpec.makeMeasureSpec(0, 0) + 1, objArr2);
        String obj = append.append(((String) objArr2[0]).intern()).append(this.e).toString();
        int i2 = h + 21;
        f = i2 % 128;
        int i3 = i2 % 2;
        return obj;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 368
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.j.k(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void l(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        int i5;
        boolean z;
        char c2;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(c)};
            Object obj = o.e.a.s.get(-2120899312);
            int i6 = -1;
            if (obj == null) {
                Class cls = (Class) o.e.a.c((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 10, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 65);
                byte b3 = (byte) (-1);
                Object[] objArr3 = new Object[1];
                m((byte) 40, b3, (byte) (b3 + 1), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z2 = intValue == -1;
            switch (!z2) {
                case true:
                    break;
                default:
                    byte[] bArr = i;
                    if (bArr != null) {
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i7 = 0;
                        while (true) {
                            switch (i7 >= length) {
                                case true:
                                    bArr = bArr2;
                                    break;
                                default:
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(bArr[i7])};
                                        Object obj2 = o.e.a.s.get(494867332);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(18 - ImageFormat.getBitsPerPixel(0), (char) (16425 - Color.red(0)), 150 - Color.green(0));
                                            byte b4 = (byte) i6;
                                            Object[] objArr5 = new Object[1];
                                            m((byte) 42, b4, (byte) (b4 + 1), objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                            o.e.a.s.put(494867332, obj2);
                                        }
                                        bArr2[i7] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                        i7++;
                                        i6 = -1;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                            }
                        }
                    }
                    if (bArr == null) {
                        intValue = (short) (((short) (g[i2 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                        break;
                    } else {
                        byte[] bArr3 = i;
                        try {
                            Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(d)};
                            Object obj3 = o.e.a.s.get(-2120899312);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), 65 - TextUtils.getCapsMode("", 0, 0));
                                byte b5 = (byte) (-1);
                                Object[] objArr7 = new Object[1];
                                m((byte) 40, b5, (byte) (b5 + 1), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-2120899312, obj3);
                            }
                            intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
            }
            switch (intValue > 0) {
                case true:
                    int i8 = ((i2 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                    if (z2) {
                        i5 = 1;
                    } else {
                        int i9 = $10 + 85;
                        $11 = i9 % 128;
                        int i10 = i9 % 2;
                        i5 = 0;
                    }
                    fVar.d = i8 + i5;
                    try {
                        Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                        Object obj4 = o.e.a.s.get(160906762);
                        if (obj4 == null) {
                            obj4 = ((Class) o.e.a.c(11 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (char) (Process.myTid() >> 22), 603 - (ViewConfiguration.getScrollBarSize() >> 8))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj4);
                        }
                        ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr4 = i;
                        switch (bArr4 == null) {
                            case true:
                                break;
                            default:
                                int length2 = bArr4.length;
                                byte[] bArr5 = new byte[length2];
                                for (int i11 = 0; i11 < length2; i11++) {
                                    bArr5[i11] = (byte) (bArr4[i11] ^ (-5810760824076169584L));
                                }
                                bArr4 = bArr5;
                                break;
                        }
                        if (bArr4 != null) {
                            int i12 = $10 + 39;
                            $11 = i12 % 128;
                            int i13 = i12 % 2;
                            z = true;
                        } else {
                            z = false;
                        }
                        fVar.c = 1;
                        while (fVar.c < intValue) {
                            if (z) {
                                int i14 = $10 + 59;
                                $11 = i14 % 128;
                                if (i14 % 2 == 0) {
                                    byte[] bArr6 = i;
                                    fVar.d = fVar.d / 1;
                                    c2 = (char) (fVar.b << (((byte) (((byte) (bArr6[r8] - 5810760824076169584L)) * s)) | b2));
                                } else {
                                    byte[] bArr7 = i;
                                    fVar.d = fVar.d - 1;
                                    c2 = (char) (fVar.b + (((byte) (((byte) (bArr7[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                }
                                fVar.e = c2;
                            } else {
                                short[] sArr = g;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                            }
                            sb.append(fVar.e);
                            fVar.b = fVar.e;
                            fVar.c++;
                        }
                        break;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
