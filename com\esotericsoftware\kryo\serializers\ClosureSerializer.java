package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Util;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ClosureSerializer.smali */
public class ClosureSerializer extends Serializer {
    private static Method readResolve;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ClosureSerializer$Closure.smali */
    public static class Closure {
    }

    public ClosureSerializer() {
        if (readResolve == null) {
            try {
                Method declaredMethod = SerializedLambda.class.getDeclaredMethod("readResolve", new Class[0]);
                readResolve = declaredMethod;
                declaredMethod.setAccessible(true);
            } catch (Exception ex) {
                throw new KryoException("Unable to obtain SerializedLambda#readResolve via reflection.", ex);
            }
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, Object object) {
        SerializedLambda serializedLambda = toSerializedLambda(object);
        int count = serializedLambda.getCapturedArgCount();
        output.writeVarInt(count, true);
        for (int i = 0; i < count; i++) {
            kryo.writeClassAndObject(output, serializedLambda.getCapturedArg(i));
        }
        try {
            kryo.writeClass(output, Class.forName(serializedLambda.getCapturingClass().replace('/', '.')));
            output.writeString(serializedLambda.getFunctionalInterfaceClass());
            output.writeString(serializedLambda.getFunctionalInterfaceMethodName());
            output.writeString(serializedLambda.getFunctionalInterfaceMethodSignature());
            output.writeVarInt(serializedLambda.getImplMethodKind(), true);
            output.writeString(serializedLambda.getImplClass());
            output.writeString(serializedLambda.getImplMethodName());
            output.writeString(serializedLambda.getImplMethodSignature());
            output.writeString(serializedLambda.getInstantiatedMethodType());
        } catch (ClassNotFoundException ex) {
            throw new KryoException("Error writing closure.", ex);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object read(Kryo kryo, Input input, Class type) {
        int count = input.readVarInt(true);
        Object[] capturedArgs = new Object[count];
        for (int i = 0; i < count; i++) {
            capturedArgs[i] = kryo.readClassAndObject(input);
        }
        SerializedLambda serializedLambda = new SerializedLambda(kryo.readClass(input).getType(), input.readString(), input.readString(), input.readString(), input.readVarInt(true), input.readString(), input.readString(), input.readString(), input.readString(), capturedArgs);
        try {
            return readResolve.invoke(serializedLambda, new Object[0]);
        } catch (Exception ex) {
            throw new KryoException("Error reading closure.", ex);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object copy(Kryo kryo, Object original) {
        try {
            return readResolve.invoke(toSerializedLambda(original), new Object[0]);
        } catch (Exception ex) {
            throw new KryoException("Error copying closure.", ex);
        }
    }

    private SerializedLambda toSerializedLambda(Object object) {
        try {
            Method writeReplace = object.getClass().getDeclaredMethod("writeReplace", new Class[0]);
            writeReplace.setAccessible(true);
            Object replacement = writeReplace.invoke(object, new Object[0]);
            try {
                return (SerializedLambda) replacement;
            } catch (Exception ex) {
                throw new KryoException("writeReplace must return a SerializedLambda: " + (replacement == null ? null : Util.className(replacement.getClass())), ex);
            }
        } catch (Exception ex2) {
            if (object instanceof Serializable) {
                throw new KryoException("Error serializing closure.", ex2);
            }
            throw new KryoException("Closure must implement java.io.Serializable.", ex2);
        }
    }
}
