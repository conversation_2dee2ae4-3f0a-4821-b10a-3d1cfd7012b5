package o.dc;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.de.f;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\e.smali */
public final class e implements o.b.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static int c;
    private static char d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        a();
        Color.red(0);
        ViewConfiguration.getKeyRepeatTimeout();
        int i = c + 97;
        e = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        d = (char) 15778;
        b = 161105445;
        a = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.dc.e.$$a
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r7 = r7 * 3
            int r7 = 4 - r7
            int r8 = r8 + 99
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r6 = r6 + r4
            int r8 = r8 + 1
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.e.g(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{30, 126, 60, -105};
        $$b = 33;
    }

    @Override // o.b.b
    public final o.b.d c(Context context, o.b.e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        f(ViewConfiguration.getScrollBarSize() >> 8, "⎖\u2fed\ueae0\u09c9߉\ue53b\uecb5㵿祜[十㵝챨\udf53\uea03檚뗢干赩ਢ㣏윞濾藚䬻㮪䲄\ue2ac爖⓬菙헦雋玶", (char) (ImageFormat.getBitsPerPixel(0) + 1), "ﮠᄵ皎侯", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f(1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "ᡱꑠ蜞캿蕈㘩ꖓ윇ꭳӠ嫆旷뜸虇ᝰᚣ秦鶫Ꝭ땝員ꃌᗍ\u31ed砦뵵\ue7b6苾㨋날", (char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 25715), "辖\uf1b7琸㥤", "\u0000\u0000\u0000\u0000", objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(eVar);
        Object[] objArr3 = new Object[1];
        f((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "ޒ趇ﺯ쎠ㅲＯꋔ궫킽徨됭뇑颊튈ట鐃嗎씮謒ㄫ\ue853画ỹꞗ\udd98씾\ue586硎", (char) (42696 - Process.getGidForName("")), "碼祬쥀\ue1a6", "\u0000\u0000\u0000\u0000", objArr3);
        g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
        o.b.d dVar = o.b.d.d;
        int i = c + 55;
        e = i % 128;
        switch (i % 2 != 0 ? 'V' : 'c') {
            case Opcodes.SASTORE /* 86 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return dVar;
        }
    }

    @Override // o.b.b
    public final f b(Context context) {
        switch (new o.bv.e().c(context)) {
            case false:
                g.c();
                Object[] objArr = new Object[1];
                f((-1) - ImageFormat.getBitsPerPixel(0), "⎖\u2fed\ueae0\u09c9߉\ue53b\uecb5㵿祜[十㵝챨\udf53\uea03檚뗢干赩ਢ㣏윞濾藚䬻㮪䲄\ue2ac爖⓬菙헦雋玶", (char) ((-1) - ImageFormat.getBitsPerPixel(0)), "ﮠᄵ皎侯", "\u0000\u0000\u0000\u0000", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((-1) - TextUtils.lastIndexOf("", '0', 0), "쭒ﭻ㛗锓龮ᙁ欮積㫋哦ặᄃ鎭ﳀ霣뻓歇幤\udac6䂕富⟻栝\uefae둊쿑种鏒詋\u05cc覉밾烝\ue161\ue237㝩硚봦穌\uef6b仳傂\uda70믋鼶꛴ᚐ櫔徳꿯瞞䜆犏틘䭚쿎鱾惂觥⥻\uecaa岐泲䰣玐급ⱒ銦䕹\ue977㐛\uebb4", (char) View.resolveSize(0, 0), "뚜㋤萩꾷", "\u0000\u0000\u0000\u0000", objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                return f.g;
            default:
                int i = e + 23;
                c = i % 128;
                if (i % 2 == 0) {
                }
                g.c();
                Object[] objArr3 = new Object[1];
                f(Color.blue(0), "⎖\u2fed\ueae0\u09c9߉\ue53b\uecb5㵿祜[十㵝챨\udf53\uea03檚뗢干赩ਢ㣏윞濾藚䬻㮪䲄\ue2ac爖⓬菙헦雋玶", (char) TextUtils.indexOf("", "", 0, 0), "ﮠᄵ皎侯", "\u0000\u0000\u0000\u0000", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((-1574096556) - TextUtils.getOffsetBefore("", 0), "溵㭈\u20f8ൕ荗鐀䫷\uedac\u2e79着ባ\uf635〗뤠⢚珘\ue15f籧뉙㫘瓮㓻᷑荧⸵뢕氺澒䩰\uf4f3逮༚飫稝트\uf497Ⱋ즹맅ꨆ뛽\ue5ba㷍쀡낇⽮者㕰﨔鐄郱ퟌ뽛딛퇏▖꠳΅墊᧫\u1c4a欅벫ʶ鞖僺湳턃鷖㙯\uf14e糨メ\uec5f䛻묘垟겟薯羨䌜鳚\ue3a6聼㟔䏊땦\uf634\ud8b3䲄唤⮝\ueaf5뤄烦蘃跭", (char) (47858 - ((byte) KeyEvent.getModifierMetaStateMask())), "咅ⴱ\uf3a2﮺", "\u0000\u0000\u0000\u0000", objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                f fVar = f.i;
                int i2 = c + 27;
                e = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 25 : 'B') {
                    case 'B':
                        return fVar;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 672
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.e.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
