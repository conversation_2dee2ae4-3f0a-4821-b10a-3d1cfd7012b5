package o.cz;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.g;
import o.a.m;
import o.i.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static char b;
    private static int c;
    private static char[] d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        e();
        Color.alpha(0);
        TextUtils.getOffsetBefore("", 0);
        int i = c + 81;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        d = new char[]{30560, 30542, 30563, 29840, 30566, 30588, 30574, 30573, 30589, 30591, 30534, 30584, 30575, 30541, 30529, 30540, 30568, 30570, 30517, 30498, 29847, 30531, 30530, 30586, 30538, 30571, 30555, 30582, 30525, 30585, 30579, 30511, 30559, 30569, 30535, 29844, 29843, 30533, 30557, 30583, 30564, 29841, 29845, 30587, 30561, 29846, 30572, 30567, 30562};
        b = (char) 17042;
        a = new int[]{-1577272662, 845192070, -670903014, 1523337505, 1952846184, 773049859, 753513176, 939525326, -199383565, 197875530, -817353741, -1123823663, -514411030, -1312863056, 2138288562, -1942956166, -1759176487, -1769300133};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r0 = o.cz.d.$$a
            int r8 = r8 + 4
            int r6 = r6 + 69
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r8 = r8 + 1
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.d.h(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{106, 33, -117, 89};
        $$b = Opcodes.INEG;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static java.util.HashMap<o.i.f, java.lang.Short> b(o.eg.e r14) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 548
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.d.b(o.eg.e):java.util.HashMap");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.fn.b a(android.content.Context r13, o.eg.e r14, java.math.BigDecimal r15) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 510
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.d.a(android.content.Context, o.eg.e, java.math.BigDecimal):o.fn.b");
    }

    /* renamed from: o.cz.d$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\d$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            a = 1;
            int[] iArr = new int[i.values().length];
            e = iArr;
            try {
                iArr[i.e.ordinal()] = 1;
                int i = b;
                int i2 = ((i | 7) << 1) - (i ^ 7);
                a = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[i.a.ordinal()] = 2;
                int i3 = (b + 2) - 1;
                a = i3 % 128;
                if (i3 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[i.d.ordinal()] = 3;
                int i4 = a + 77;
                b = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[i.b.ordinal()] = 4;
                int i6 = a;
                int i7 = (i6 & 9) + (i6 | 9);
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:63:0x0317 A[Catch: d -> 0x03d6, TRY_LEAVE, TryCatch #0 {d -> 0x03d6, blocks: (B:3:0x000b, B:5:0x0053, B:6:0x0078, B:11:0x00b9, B:12:0x00c3, B:15:0x021e, B:16:0x00c8, B:18:0x00d4, B:21:0x00e0, B:24:0x00f4, B:26:0x0117, B:33:0x0155, B:36:0x016a, B:41:0x015d, B:53:0x01e7, B:54:0x021d, B:56:0x0225, B:59:0x023d, B:61:0x030f, B:63:0x0317, B:78:0x0242, B:80:0x02b3, B:82:0x02da, B:83:0x02ee, B:84:0x030e, B:88:0x039c, B:89:0x03d5), top: B:2:0x000b, inners: #2, #3 }] */
    /* JADX WARN: Removed duplicated region for block: B:66:0x0389  */
    /* JADX WARN: Removed duplicated region for block: B:68:0x0391 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0392  */
    /* JADX WARN: Removed duplicated region for block: B:77:0x038c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.i.g c(o.eg.b r27, java.math.BigDecimal r28) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1338
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.d.c(o.eg.b, java.math.BigDecimal):o.i.g");
    }

    private static void f(int i, String str, byte b2, Object[] objArr) {
        char[] cArr;
        int i2;
        char c2;
        switch (str != null ? '*' : ']') {
            case Opcodes.DUP2_X1 /* 93 */:
                cArr = str;
                break;
            default:
                int i3 = $10 + 21;
                $11 = i3 % 128;
                if (i3 % 2 == 0) {
                }
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = d;
        switch (cArr3 == null) {
            case true:
                break;
            default:
                int i4 = $11;
                int i5 = i4 + 61;
                $10 = i5 % 128;
                int i6 = i5 % 2;
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i7 = i4 + 7;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                for (int i9 = 0; i9 < length; i9++) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr3[i9])};
                        Object obj = o.e.a.s.get(-1401577988);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(17 - (ViewConfiguration.getPressedStateDuration() >> 16), (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 76 - View.combineMeasuredStates(0, 0));
                            byte length2 = (byte) $$a.length;
                            byte b3 = (byte) (length2 - 4);
                            Object[] objArr3 = new Object[1];
                            h(length2, b3, (byte) (b3 - 1), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj);
                        }
                        cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(b)};
            Object obj2 = o.e.a.s.get(-1401577988);
            char c3 = 6;
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - KeyEvent.normalizeMetaState(0), (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), ((Process.getThreadPriority(0) + 20) >> 6) + 76);
                byte length3 = (byte) $$a.length;
                byte b4 = (byte) (length3 - 4);
                Object[] objArr5 = new Object[1];
                h(length3, b4, (byte) (b4 - 1), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i];
            if (i % 2 != 0) {
                i2 = i - 1;
                cArr5[i2] = (char) (cArr2[i2] - b2);
            } else {
                i2 = i;
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (true) {
                    switch (mVar.b < i2 ? '\f' : 'U') {
                        case '\f':
                            mVar.e = cArr2[mVar.b];
                            mVar.a = cArr2[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                cArr5[mVar.b] = (char) (mVar.e - b2);
                                cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                c2 = c3;
                            } else {
                                try {
                                    Object[] objArr6 = new Object[13];
                                    objArr6[12] = mVar;
                                    objArr6[11] = Integer.valueOf(charValue);
                                    objArr6[10] = mVar;
                                    objArr6[9] = mVar;
                                    objArr6[8] = Integer.valueOf(charValue);
                                    objArr6[7] = mVar;
                                    objArr6[c3] = mVar;
                                    objArr6[5] = Integer.valueOf(charValue);
                                    objArr6[4] = mVar;
                                    objArr6[3] = mVar;
                                    objArr6[2] = Integer.valueOf(charValue);
                                    objArr6[1] = mVar;
                                    objArr6[0] = mVar;
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 10, (char) (8856 - (ViewConfiguration.getPressedStateDuration() >> 16)), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 324);
                                        byte b5 = (byte) 0;
                                        byte b6 = b5;
                                        Object[] objArr7 = new Object[1];
                                        h(b5, b6, (byte) (b6 - 1), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        int i10 = $10 + Opcodes.LSHL;
                                        $11 = i10 % 128;
                                        int i11 = i10 % 2;
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 != null) {
                                                c2 = 6;
                                            } else {
                                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 11, (char) KeyEvent.keyCodeFromString(""), 65 - (ViewConfiguration.getKeyRepeatTimeout() >> 16));
                                                byte b7 = (byte) 1;
                                                byte b8 = (byte) (b7 - 1);
                                                Object[] objArr9 = new Object[1];
                                                h(b7, b8, (byte) (b8 - 1), objArr9);
                                                String str2 = (String) objArr9[0];
                                                c2 = 6;
                                                obj4 = cls4.getMethod(str2, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i12 = (mVar.d * charValue) + mVar.h;
                                            cArr5[mVar.b] = cArr3[intValue];
                                            cArr5[mVar.b + 1] = cArr3[i12];
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else {
                                        c2 = 6;
                                        if (mVar.c == mVar.d) {
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i13 = (mVar.c * charValue) + mVar.i;
                                            int i14 = (mVar.d * charValue) + mVar.h;
                                            cArr5[mVar.b] = cArr3[i13];
                                            cArr5[mVar.b + 1] = cArr3[i14];
                                        } else {
                                            int i15 = (mVar.c * charValue) + mVar.h;
                                            int i16 = (mVar.d * charValue) + mVar.i;
                                            cArr5[mVar.b] = cArr3[i15];
                                            cArr5[mVar.b + 1] = cArr3[i16];
                                        }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                            c3 = c2;
                    }
                }
            }
            for (int i17 = 0; i17 < i; i17++) {
                cArr5[i17] = (char) (cArr5[i17] ^ 13722);
            }
            objArr[0] = new String(cArr5);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    private static void g(int[] iArr, int i, Object[] objArr) {
        long j;
        g gVar;
        int i2;
        int i3;
        int[] iArr2 = iArr;
        g gVar2 = new g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr2.length * 2];
        int[] iArr3 = a;
        int i4 = 47;
        int i5 = -1667374059;
        int i6 = 1;
        int i7 = 0;
        switch (iArr3 != null ? '\b' : Typography.amp) {
            case '&':
                break;
            default:
                int length = iArr3.length;
                int[] iArr4 = new int[length];
                int i8 = 0;
                while (i8 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(iArr3[i8])};
                        Object obj = o.e.a.s.get(Integer.valueOf(i5));
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(10 - View.resolveSizeAndState(0, 0, 0), (char) (8856 - KeyEvent.getDeadChar(0, 0)), 324 - (Process.myTid() >> 22));
                            byte b2 = (byte) 0;
                            Object[] objArr3 = new Object[1];
                            h((byte) i4, b2, (byte) (b2 - 1), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1667374059, obj);
                        }
                        iArr4[i8] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                        i8++;
                        i4 = 47;
                        i5 = -1667374059;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                iArr3 = iArr4;
                break;
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = a;
        long j2 = 0;
        switch (iArr6 != null) {
            case false:
                break;
            default:
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i9 = 0;
                while (true) {
                    switch (i9 < length3 ? '6' : (char) 6) {
                        case 6:
                            int i10 = $11 + 81;
                            $10 = i10 % 128;
                            int i11 = i10 % 2;
                            iArr6 = iArr7;
                            break;
                        default:
                            try {
                                Object[] objArr4 = new Object[i6];
                                objArr4[i7] = Integer.valueOf(iArr6[i9]);
                                Object obj2 = o.e.a.s.get(-1667374059);
                                if (obj2 != null) {
                                    gVar = gVar2;
                                    i2 = i6;
                                    i3 = i7;
                                    j = 0;
                                } else {
                                    j = 0;
                                    Class cls2 = (Class) o.e.a.c((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 9, (char) (8856 - KeyEvent.getDeadChar(i7, i7)), 325 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)));
                                    byte b3 = (byte) i7;
                                    gVar = gVar2;
                                    Object[] objArr5 = new Object[1];
                                    h((byte) 47, b3, (byte) (b3 - 1), objArr5);
                                    i2 = 1;
                                    i3 = 0;
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj2);
                                }
                                iArr7[i9] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i9++;
                                i6 = i2;
                                i7 = i3;
                                j2 = j;
                                gVar2 = gVar;
                                iArr2 = iArr;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                    }
                }
        }
        System.arraycopy(iArr6, i7, iArr5, i7, length2);
        gVar2.a = i7;
        while (gVar2.a < iArr2.length) {
            cArr[i7] = (char) (iArr2[gVar2.a] >> 16);
            cArr[i6] = (char) iArr2[gVar2.a];
            cArr[2] = (char) (iArr2[gVar2.a + i6] >> 16);
            cArr[3] = (char) iArr2[gVar2.a + i6];
            gVar2.e = (cArr[i7] << 16) + cArr[i6];
            gVar2.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr5);
            int i12 = i7;
            while (i12 < 16) {
                gVar2.e ^= iArr5[i12];
                int b4 = g.b(gVar2.e);
                try {
                    Object[] objArr6 = new Object[4];
                    objArr6[3] = gVar2;
                    objArr6[2] = gVar2;
                    objArr6[i6] = Integer.valueOf(b4);
                    objArr6[i7] = gVar2;
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        Class cls3 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(i7) > j2 ? 1 : (ExpandableListView.getPackedPositionForGroup(i7) == j2 ? 0 : -1)) + 11, (char) Color.green(i7), 572 - (Process.myPid() >> 22));
                        Class<?>[] clsArr = new Class[4];
                        clsArr[i7] = Object.class;
                        clsArr[i6] = Integer.TYPE;
                        clsArr[2] = Object.class;
                        clsArr[3] = Object.class;
                        obj3 = cls3.getMethod("q", clsArr);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar2.e = gVar2.c;
                    gVar2.c = intValue;
                    i12++;
                    j2 = 0;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i13 = gVar2.e;
            gVar2.e = gVar2.c;
            gVar2.c = i13;
            gVar2.c ^= iArr5[16];
            gVar2.e ^= iArr5[17];
            int i14 = gVar2.e;
            int i15 = gVar2.c;
            cArr[i7] = (char) (gVar2.e >>> 16);
            cArr[i6] = (char) gVar2.e;
            cArr[2] = (char) (gVar2.c >>> 16);
            cArr[3] = (char) gVar2.c;
            g.d(iArr5);
            cArr2[gVar2.a * 2] = cArr[i7];
            cArr2[(gVar2.a * 2) + i6] = cArr[i6];
            cArr2[(gVar2.a * 2) + 2] = cArr[2];
            cArr2[(gVar2.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar2, gVar2};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls4 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(i7) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i7) == 0.0d ? 0 : -1)) + 12, (char) (Gravity.getAbsoluteGravity(i7, i7) + 55183), 515 - (CdmaCellLocation.convertQuartSecToDecDegrees(i7) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i7) == 0.0d ? 0 : -1)));
                    byte b5 = (byte) i7;
                    Object[] objArr8 = new Object[i6];
                    h((byte) 46, b5, (byte) (b5 - 1), objArr8);
                    String str = (String) objArr8[i7];
                    Class<?>[] clsArr2 = new Class[2];
                    clsArr2[i7] = Object.class;
                    clsArr2[i6] = Object.class;
                    obj4 = cls4.getMethod(str, clsArr2);
                    o.e.a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
                j2 = 0;
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str2 = new String(cArr2, i7, i);
        int i16 = $10 + 13;
        $11 = i16 % 128;
        int i17 = i16 % 2;
        objArr[i7] = str2;
    }
}
