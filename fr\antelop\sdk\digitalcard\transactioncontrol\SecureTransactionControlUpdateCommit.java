package fr.antelop.sdk.digitalcard.transactioncontrol;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\SecureTransactionControlUpdateCommit.smali */
public final class SecureTransactionControlUpdateCommit implements CustomerAuthenticatedProcess {
    private final n innerSecureDigitalCardUpdateTransactionControlProcess;

    public SecureTransactionControlUpdateCommit(n nVar) {
        this.innerSecureDigitalCardUpdateTransactionControlProcess = nVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardUpdateTransactionControlProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardUpdateTransactionControlProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardUpdateTransactionControlProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardUpdateTransactionControlProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardUpdateTransactionControlProcess.b(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardUpdateTransactionControlProcess));
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardUpdateTransactionControlProcess.b(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardUpdateTransactionControlProcess));
    }
}
