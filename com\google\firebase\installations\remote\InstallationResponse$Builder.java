package com.google.firebase.installations.remote;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\installations\remote\InstallationResponse$Builder.smali */
public abstract class InstallationResponse$Builder {
    public abstract InstallationResponse build();

    public abstract InstallationResponse$Builder setAuthToken(TokenResult tokenResult);

    public abstract InstallationResponse$Builder setFid(String str);

    public abstract InstallationResponse$Builder setRefreshToken(String str);

    public abstract InstallationResponse$Builder setResponseCode(InstallationResponse$ResponseCode installationResponse$ResponseCode);

    public abstract InstallationResponse$Builder setUri(String str);
}
