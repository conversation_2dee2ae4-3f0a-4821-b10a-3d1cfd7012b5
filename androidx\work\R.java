package androidx.work;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\work\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\work\R$bool.smali */
    public static final class bool {
        public static int enable_system_alarm_service_default = 0x7f050016;
        public static int enable_system_foreground_service_default = 0x7f050017;
        public static int enable_system_job_service_default = 0x7f050018;
        public static int workmanager_test_configuration = 0x7f05001a;

        private bool() {
        }
    }

    private R() {
    }
}
