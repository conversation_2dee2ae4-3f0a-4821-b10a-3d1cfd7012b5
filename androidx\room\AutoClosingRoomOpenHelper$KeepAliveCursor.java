package androidx.room;

import android.content.ContentResolver;
import android.database.CharArrayBuffer;
import android.database.ContentObserver;
import android.database.Cursor;
import android.database.DataSetObserver;
import android.net.Uri;
import android.os.Bundle;
import androidx.sqlite.db.SupportSQLiteCompat;
import java.util.List;
import kotlin.Deprecated;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* JADX INFO: Access modifiers changed from: private */
/* compiled from: AutoClosingRoomOpenHelper.kt */
@Metadata(d1 = {"\u0000\u0090\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\n\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\r\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0002\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0004¢\u0006\u0002\u0010\u0005J\b\u0010\u0006\u001a\u00020\u0007H\u0016J!\u0010\b\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\n2\u000e\u0010\u000b\u001a\n \r*\u0004\u0018\u00010\f0\fH\u0096\u0001J\t\u0010\u000e\u001a\u00020\u0007H\u0097\u0001J\u0019\u0010\u000f\u001a\n \r*\u0004\u0018\u00010\u00100\u00102\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\t\u0010\u0011\u001a\u00020\nH\u0096\u0001J\u0019\u0010\u0012\u001a\u00020\n2\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010\u00130\u0013H\u0096\u0001J\u0019\u0010\u0014\u001a\u00020\n2\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010\u00130\u0013H\u0096\u0001J\u0019\u0010\u0015\u001a\n \r*\u0004\u0018\u00010\u00130\u00132\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J4\u0010\u0016\u001a(\u0012\f\u0012\n \r*\u0004\u0018\u00010\u00130\u0013 \r*\u0014\u0012\u000e\b\u0001\u0012\n \r*\u0004\u0018\u00010\u00130\u0013\u0018\u00010\u00170\u0017H\u0096\u0001¢\u0006\u0002\u0010\u0018J\t\u0010\u0019\u001a\u00020\nH\u0096\u0001J\u0011\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0011\u0010\u001c\u001a\n \r*\u0004\u0018\u00010\u001d0\u001dH\u0096\u0001J\u0011\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0011\u0010 \u001a\u00020\n2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0011\u0010!\u001a\u00020\"2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\b\u0010#\u001a\u00020$H\u0017J\u000e\u0010%\u001a\b\u0012\u0004\u0012\u00020$0&H\u0017J\t\u0010'\u001a\u00020\nH\u0096\u0001J\u0011\u0010(\u001a\u00020)2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0019\u0010*\u001a\n \r*\u0004\u0018\u00010\u00130\u00132\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0011\u0010+\u001a\u00020\n2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\t\u0010,\u001a\u00020-H\u0096\u0001J\t\u0010.\u001a\u00020-H\u0096\u0001J\t\u0010/\u001a\u00020-H\u0096\u0001J\t\u00100\u001a\u00020-H\u0096\u0001J\t\u00101\u001a\u00020-H\u0096\u0001J\t\u00102\u001a\u00020-H\u0096\u0001J\u0011\u00103\u001a\u00020-2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\u0011\u00104\u001a\u00020-2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\t\u00105\u001a\u00020-H\u0096\u0001J\t\u00106\u001a\u00020-H\u0096\u0001J\t\u00107\u001a\u00020-H\u0096\u0001J\u0011\u00108\u001a\u00020-2\u0006\u0010\t\u001a\u00020\nH\u0096\u0001J\t\u00109\u001a\u00020-H\u0096\u0001J\u0019\u0010:\u001a\u00020\u00072\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010;0;H\u0096\u0001J\u0019\u0010<\u001a\u00020\u00072\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010=0=H\u0096\u0001J\t\u0010>\u001a\u00020-H\u0097\u0001J!\u0010?\u001a\n \r*\u0004\u0018\u00010\u001d0\u001d2\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010\u001d0\u001dH\u0096\u0001J\u0010\u0010@\u001a\u00020\u00072\u0006\u0010A\u001a\u00020\u001dH\u0017J)\u0010B\u001a\u00020\u00072\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010C0C2\u000e\u0010\u000b\u001a\n \r*\u0004\u0018\u00010$0$H\u0096\u0001J\u001e\u0010D\u001a\u00020\u00072\u0006\u0010E\u001a\u00020C2\f\u0010F\u001a\b\u0012\u0004\u0012\u00020$0&H\u0017J\u0019\u0010G\u001a\u00020\u00072\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010;0;H\u0096\u0001J\u0019\u0010H\u001a\u00020\u00072\u000e\u0010\t\u001a\n \r*\u0004\u0018\u00010=0=H\u0096\u0001R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006I"}, d2 = {"Landroidx/room/AutoClosingRoomOpenHelper$KeepAliveCursor;", "Landroid/database/Cursor;", "delegate", "autoCloser", "Landroidx/room/AutoCloser;", "(Landroid/database/Cursor;Landroidx/room/AutoCloser;)V", "close", "", "copyStringToBuffer", "p0", "", "p1", "Landroid/database/CharArrayBuffer;", "kotlin.jvm.PlatformType", "deactivate", "getBlob", "", "getColumnCount", "getColumnIndex", "", "getColumnIndexOrThrow", "getColumnName", "getColumnNames", "", "()[Ljava/lang/String;", "getCount", "getDouble", "", "getExtras", "Landroid/os/Bundle;", "getFloat", "", "getInt", "getLong", "", "getNotificationUri", "Landroid/net/Uri;", "getNotificationUris", "", "getPosition", "getShort", "", "getString", "getType", "getWantsAllOnMoveCalls", "", "isAfterLast", "isBeforeFirst", "isClosed", "isFirst", "isLast", "isNull", "move", "moveToFirst", "moveToLast", "moveToNext", "moveToPosition", "moveToPrevious", "registerContentObserver", "Landroid/database/ContentObserver;", "registerDataSetObserver", "Landroid/database/DataSetObserver;", "requery", "respond", "setExtras", "extras", "setNotificationUri", "Landroid/content/ContentResolver;", "setNotificationUris", "cr", "uris", "unregisterContentObserver", "unregisterDataSetObserver", "room-runtime_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\room\AutoClosingRoomOpenHelper$KeepAliveCursor.smali */
public final class AutoClosingRoomOpenHelper$KeepAliveCursor implements Cursor {
    private final AutoCloser autoCloser;
    private final Cursor delegate;

    @Override // android.database.Cursor
    public void copyStringToBuffer(int p0, CharArrayBuffer p1) {
        this.delegate.copyStringToBuffer(p0, p1);
    }

    @Override // android.database.Cursor
    @Deprecated(message = "Deprecated in Java")
    public void deactivate() {
        this.delegate.deactivate();
    }

    @Override // android.database.Cursor
    public byte[] getBlob(int p0) {
        return this.delegate.getBlob(p0);
    }

    @Override // android.database.Cursor
    public int getColumnCount() {
        return this.delegate.getColumnCount();
    }

    @Override // android.database.Cursor
    public int getColumnIndex(String p0) {
        return this.delegate.getColumnIndex(p0);
    }

    @Override // android.database.Cursor
    public int getColumnIndexOrThrow(String p0) {
        return this.delegate.getColumnIndexOrThrow(p0);
    }

    @Override // android.database.Cursor
    public String getColumnName(int p0) {
        return this.delegate.getColumnName(p0);
    }

    @Override // android.database.Cursor
    public String[] getColumnNames() {
        return this.delegate.getColumnNames();
    }

    @Override // android.database.Cursor
    public int getCount() {
        return this.delegate.getCount();
    }

    @Override // android.database.Cursor
    public double getDouble(int p0) {
        return this.delegate.getDouble(p0);
    }

    @Override // android.database.Cursor
    public Bundle getExtras() {
        return this.delegate.getExtras();
    }

    @Override // android.database.Cursor
    public float getFloat(int p0) {
        return this.delegate.getFloat(p0);
    }

    @Override // android.database.Cursor
    public int getInt(int p0) {
        return this.delegate.getInt(p0);
    }

    @Override // android.database.Cursor
    public long getLong(int p0) {
        return this.delegate.getLong(p0);
    }

    @Override // android.database.Cursor
    public int getPosition() {
        return this.delegate.getPosition();
    }

    @Override // android.database.Cursor
    public short getShort(int p0) {
        return this.delegate.getShort(p0);
    }

    @Override // android.database.Cursor
    public String getString(int p0) {
        return this.delegate.getString(p0);
    }

    @Override // android.database.Cursor
    public int getType(int p0) {
        return this.delegate.getType(p0);
    }

    @Override // android.database.Cursor
    public boolean getWantsAllOnMoveCalls() {
        return this.delegate.getWantsAllOnMoveCalls();
    }

    @Override // android.database.Cursor
    public boolean isAfterLast() {
        return this.delegate.isAfterLast();
    }

    @Override // android.database.Cursor
    public boolean isBeforeFirst() {
        return this.delegate.isBeforeFirst();
    }

    @Override // android.database.Cursor
    public boolean isClosed() {
        return this.delegate.isClosed();
    }

    @Override // android.database.Cursor
    public boolean isFirst() {
        return this.delegate.isFirst();
    }

    @Override // android.database.Cursor
    public boolean isLast() {
        return this.delegate.isLast();
    }

    @Override // android.database.Cursor
    public boolean isNull(int p0) {
        return this.delegate.isNull(p0);
    }

    @Override // android.database.Cursor
    public boolean move(int p0) {
        return this.delegate.move(p0);
    }

    @Override // android.database.Cursor
    public boolean moveToFirst() {
        return this.delegate.moveToFirst();
    }

    @Override // android.database.Cursor
    public boolean moveToLast() {
        return this.delegate.moveToLast();
    }

    @Override // android.database.Cursor
    public boolean moveToNext() {
        return this.delegate.moveToNext();
    }

    @Override // android.database.Cursor
    public boolean moveToPosition(int p0) {
        return this.delegate.moveToPosition(p0);
    }

    @Override // android.database.Cursor
    public boolean moveToPrevious() {
        return this.delegate.moveToPrevious();
    }

    @Override // android.database.Cursor
    public void registerContentObserver(ContentObserver p0) {
        this.delegate.registerContentObserver(p0);
    }

    @Override // android.database.Cursor
    public void registerDataSetObserver(DataSetObserver p0) {
        this.delegate.registerDataSetObserver(p0);
    }

    @Override // android.database.Cursor
    @Deprecated(message = "Deprecated in Java")
    public boolean requery() {
        return this.delegate.requery();
    }

    @Override // android.database.Cursor
    public Bundle respond(Bundle p0) {
        return this.delegate.respond(p0);
    }

    @Override // android.database.Cursor
    public void setNotificationUri(ContentResolver p0, Uri p1) {
        this.delegate.setNotificationUri(p0, p1);
    }

    @Override // android.database.Cursor
    public void unregisterContentObserver(ContentObserver p0) {
        this.delegate.unregisterContentObserver(p0);
    }

    @Override // android.database.Cursor
    public void unregisterDataSetObserver(DataSetObserver p0) {
        this.delegate.unregisterDataSetObserver(p0);
    }

    public AutoClosingRoomOpenHelper$KeepAliveCursor(Cursor delegate, AutoCloser autoCloser) {
        Intrinsics.checkNotNullParameter(delegate, "delegate");
        Intrinsics.checkNotNullParameter(autoCloser, "autoCloser");
        this.delegate = delegate;
        this.autoCloser = autoCloser;
    }

    @Override // android.database.Cursor, java.io.Closeable, java.lang.AutoCloseable
    public void close() {
        this.delegate.close();
        this.autoCloser.decrementCountAndScheduleClose();
    }

    @Override // android.database.Cursor
    public void setNotificationUris(ContentResolver cr, List<? extends Uri> uris) {
        Intrinsics.checkNotNullParameter(cr, "cr");
        Intrinsics.checkNotNullParameter(uris, "uris");
        SupportSQLiteCompat.Api29Impl.setNotificationUris(this.delegate, cr, uris);
    }

    @Override // android.database.Cursor
    public Uri getNotificationUri() {
        return SupportSQLiteCompat.Api19Impl.getNotificationUri(this.delegate);
    }

    @Override // android.database.Cursor
    public List<Uri> getNotificationUris() {
        return SupportSQLiteCompat.Api29Impl.getNotificationUris(this.delegate);
    }

    @Override // android.database.Cursor
    public void setExtras(Bundle extras) {
        Intrinsics.checkNotNullParameter(extras, "extras");
        SupportSQLiteCompat.Api23Impl.setExtras(this.delegate, extras);
    }
}
