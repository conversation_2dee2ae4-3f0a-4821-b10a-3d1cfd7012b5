package o.by;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\by\c.smali */
public abstract class c {
    private static int c = 0;
    private static int d = 1;
    private final Context a;
    private final o.ei.c b;
    private final d e;

    public abstract void e(o.h.d dVar) throws WalletValidationException;

    protected c(Context context, d dVar, o.ei.c cVar) {
        this.a = context;
        this.e = dVar;
        this.b = cVar;
    }

    public final Context d() {
        int i = c;
        int i2 = ((i | 97) << 1) - (i ^ 97);
        d = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return this.a;
            default:
                int i3 = 99 / 0;
                return this.a;
        }
    }

    public final d e() {
        int i = d + Opcodes.LSUB;
        int i2 = i % 128;
        c = i2;
        switch (i % 2 != 0 ? '*' : 'A') {
            case '*':
                throw null;
            default:
                d dVar = this.e;
                int i3 = i2 + Opcodes.LSUB;
                d = i3 % 128;
                int i4 = i3 % 2;
                return dVar;
        }
    }

    public final o.ei.c b() {
        o.ei.c cVar;
        int i = c;
        int i2 = ((i | 99) << 1) - (i ^ 99);
        int i3 = i2 % 128;
        d = i3;
        switch (i2 % 2 == 0) {
            case false:
                cVar = this.b;
                break;
            default:
                cVar = this.b;
                int i4 = 1 / 0;
                break;
        }
        int i5 = (i3 + 32) - 1;
        c = i5 % 128;
        int i6 = i5 % 2;
        return cVar;
    }

    public final int hashCode() {
        int hashCode;
        int i = d;
        int i2 = (i & 47) + (i | 47);
        c = i2 % 128;
        switch (i2 % 2 != 0 ? 'D' : '_') {
            case Opcodes.SWAP /* 95 */:
                hashCode = super.hashCode();
                break;
            default:
                hashCode = super.hashCode();
                int i3 = 71 / 0;
                break;
        }
        int i4 = d + 91;
        c = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 27 : '/') {
            case 27:
                int i5 = 77 / 0;
                return hashCode;
            default:
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = d;
        int i2 = (i ^ 53) + ((i & 53) << 1);
        c = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = c;
        int i5 = (i4 & Opcodes.DSUB) + (i4 | Opcodes.DSUB);
        d = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i = d + 77;
        c = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = d;
        int i4 = (i3 & 97) + (i3 | 97);
        c = i4 % 128;
        switch (i4 % 2 != 0 ? 'K' : '(') {
            case 'K':
                int i5 = 83 / 0;
                return obj;
            default:
                return obj;
        }
    }

    protected final void finalize() throws Throwable {
        int i = d + 39;
        c = i % 128;
        boolean z = i % 2 == 0;
        Object obj = null;
        super.finalize();
        switch (z) {
            case true:
                int i2 = d;
                int i3 = (i2 & 91) + (i2 | 91);
                c = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
