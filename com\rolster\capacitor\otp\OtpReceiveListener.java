package com.rolster.capacitor.otp;

import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\rolster\capacitor\otp\OtpReceiveListener.smali */
public interface OtpReceiveListener {
    void onSmsReceivedCancel();

    void onSmsReceivedError(String str);

    void onSmsReceivedSuccess(Intent intent, String str);

    void onSmsReceivedSuccess(String str);

    void onSmsReceivedTimeOut();

    void registerReceiverSms(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter);
}
