package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x6.smali */
public class x6 extends u {
    private BigInteger b;
    private BigInteger x;

    private x6(e0 e0Var) {
        if (e0Var.size() != 2) {
            throw new IllegalArgumentException("Bad sequence size: " + e0Var.size());
        }
        Enumeration j = e0Var.j();
        this.b = r.a(j.nextElement()).h();
        this.x = r.a(j.nextElement()).h();
    }

    public static x6 a(Object obj) {
        if (obj instanceof x6) {
            return (x6) obj;
        }
        if (obj != null) {
            return new x6(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.b;
    }

    public BigInteger f() {
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(new r(e()));
        iVar.a(new r(f()));
        return new j2(iVar);
    }
}
