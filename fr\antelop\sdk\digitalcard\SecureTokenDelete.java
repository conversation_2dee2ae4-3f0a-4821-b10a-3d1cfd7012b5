package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureTokenDelete.smali */
public final class SecureTokenDelete implements CustomerAuthenticatedProcess {
    private final g innerTokenDeleteProcess;

    public SecureTokenDelete(g gVar) {
        this.innerTokenDeleteProcess = gVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerTokenDeleteProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerTokenDeleteProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerTokenDeleteProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerTokenDeleteProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenDeleteProcess.b(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerTokenDeleteProcess));
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenDeleteProcess.b(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerTokenDeleteProcess));
    }
}
