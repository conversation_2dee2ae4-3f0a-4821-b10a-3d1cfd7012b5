package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\CollectionSerializer.smali */
public class CollectionSerializer<T extends Collection> extends Serializer<T> {
    private Class elementClass;
    private Serializer elementSerializer;
    private boolean elementsCanBeNull = true;

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\CollectionSerializer$BindCollection.smali */
    public @interface BindCollection {
        Class elementClass() default Object.class;

        Class<? extends Serializer> elementSerializer() default Serializer.class;

        Class<? extends SerializerFactory> elementSerializerFactory() default SerializerFactory.class;

        boolean elementsCanBeNull() default true;
    }

    public CollectionSerializer() {
        setAcceptsNull(true);
    }

    public void setElementsCanBeNull(boolean elementsCanBeNull) {
        this.elementsCanBeNull = elementsCanBeNull;
    }

    public void setElementClass(Class elementClass) {
        this.elementClass = elementClass;
    }

    public Class getElementClass() {
        return this.elementClass;
    }

    public void setElementClass(Class elementClass, Serializer serializer) {
        this.elementClass = elementClass;
        this.elementSerializer = serializer;
    }

    public void setElementSerializer(Serializer elementSerializer) {
        this.elementSerializer = elementSerializer;
    }

    public Serializer getElementSerializer() {
        return this.elementSerializer;
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T collection) {
        Class genericClass;
        if (collection == null) {
            output.writeByte((byte) 0);
            return;
        }
        int length = collection.size();
        if (length == 0) {
            output.writeByte(1);
            writeHeader(kryo, output, collection);
            return;
        }
        boolean elementsCanBeNull = this.elementsCanBeNull;
        Serializer elementSerializer = this.elementSerializer;
        if (elementSerializer == null && (genericClass = kryo.getGenerics().nextGenericClass()) != null && kryo.isFinal(genericClass)) {
            elementSerializer = kryo.getSerializer(genericClass);
        }
        try {
            if (elementSerializer != null) {
                if (!elementsCanBeNull) {
                    output.writeVarInt(length + 1, true);
                } else {
                    Iterator it = collection.iterator();
                    while (true) {
                        if (!it.hasNext()) {
                            output.writeVarIntFlag(false, length + 1, true);
                            elementsCanBeNull = false;
                            break;
                        } else if (it.next() == null) {
                            output.writeVarIntFlag(true, length + 1, true);
                            break;
                        }
                    }
                }
                writeHeader(kryo, output, collection);
            } else {
                Class elementType = null;
                boolean hasNull = false;
                Iterator it2 = collection.iterator();
                while (true) {
                    if (!it2.hasNext()) {
                        output.writeVarIntFlag(true, length + 1, true);
                        writeHeader(kryo, output, collection);
                        if (elementType == null) {
                            output.writeByte((byte) 0);
                            return;
                        }
                        kryo.writeClass(output, elementType);
                        elementSerializer = kryo.getSerializer(elementType);
                        if (elementsCanBeNull) {
                            output.writeBoolean(hasNull);
                            elementsCanBeNull = hasNull;
                        }
                    } else {
                        Object element = it2.next();
                        if (element == null) {
                            hasNull = true;
                        } else if (elementType == null) {
                            elementType = element.getClass();
                        } else if (element.getClass() != elementType) {
                            output.writeVarIntFlag(false, length + 1, true);
                            writeHeader(kryo, output, collection);
                            break;
                        }
                    }
                }
            }
            if (elementSerializer != null) {
                if (elementsCanBeNull) {
                    Iterator it3 = collection.iterator();
                    while (it3.hasNext()) {
                        kryo.writeObjectOrNull(output, it3.next(), elementSerializer);
                    }
                } else {
                    Iterator it4 = collection.iterator();
                    while (it4.hasNext()) {
                        kryo.writeObject(output, it4.next(), elementSerializer);
                    }
                }
            } else {
                Iterator it5 = collection.iterator();
                while (it5.hasNext()) {
                    kryo.writeClassAndObject(output, it5.next());
                }
            }
        } finally {
            kryo.getGenerics().popGenericType();
        }
    }

    protected void writeHeader(Kryo kryo, Output output, T collection) {
    }

    protected T create(Kryo kryo, Input input, Class<? extends T> type, int size) {
        if (type == ArrayList.class) {
            return new ArrayList(size);
        }
        if (type == HashSet.class) {
            return new HashSet(Math.max(((int) (size / 0.75f)) + 1, 16));
        }
        T t = (T) kryo.newInstance(type);
        if (t instanceof ArrayList) {
            ((ArrayList) t).ensureCapacity(size);
        }
        return t;
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        int length;
        T collection;
        int length2;
        Class genericClass;
        Class elementClass = this.elementClass;
        Serializer elementSerializer = this.elementSerializer;
        if (elementSerializer == null && (genericClass = kryo.getGenerics().nextGenericClass()) != null && kryo.isFinal(genericClass)) {
            elementSerializer = kryo.getSerializer(genericClass);
            elementClass = genericClass;
        }
        try {
            boolean elementsCanBeNull = this.elementsCanBeNull;
            if (elementSerializer != null) {
                if (elementsCanBeNull) {
                    elementsCanBeNull = input.readVarIntFlag();
                    length2 = input.readVarIntFlag(true);
                } else {
                    length2 = input.readVarInt(true);
                }
                if (length2 == 0) {
                    return null;
                }
                length = length2 - 1;
                collection = create(kryo, input, type, length);
                kryo.reference(collection);
                if (length == 0) {
                    return collection;
                }
            } else {
                boolean sameType = input.readVarIntFlag();
                int length3 = input.readVarIntFlag(true);
                if (length3 == 0) {
                    return null;
                }
                length = length3 - 1;
                T collection2 = create(kryo, input, type, length);
                kryo.reference(collection2);
                if (length == 0) {
                    return collection2;
                }
                if (sameType) {
                    Registration registration = kryo.readClass(input);
                    if (registration == null) {
                        for (int i = 0; i < length; i++) {
                            collection2.add(null);
                        }
                        kryo.getGenerics().popGenericType();
                        return collection2;
                    }
                    elementClass = registration.getType();
                    elementSerializer = kryo.getSerializer(elementClass);
                    if (elementsCanBeNull) {
                        elementsCanBeNull = input.readBoolean();
                    }
                    collection = collection2;
                } else {
                    collection = collection2;
                }
            }
            if (elementSerializer == null) {
                for (int i2 = 0; i2 < length; i2++) {
                    collection.add(kryo.readClassAndObject(input));
                }
            } else if (elementsCanBeNull) {
                for (int i3 = 0; i3 < length; i3++) {
                    collection.add(kryo.readObjectOrNull(input, elementClass, elementSerializer));
                }
            } else {
                for (int i4 = 0; i4 < length; i4++) {
                    collection.add(kryo.readObject(input, elementClass, elementSerializer));
                }
            }
            return collection;
        } finally {
            kryo.getGenerics().popGenericType();
        }
    }

    protected T createCopy(Kryo kryo, T original) {
        return (T) kryo.newInstance(original.getClass());
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T copy(Kryo kryo, T original) {
        T copy = createCopy(kryo, original);
        kryo.reference(copy);
        for (Object element : original) {
            copy.add(kryo.copy(element));
        }
        return copy;
    }
}
