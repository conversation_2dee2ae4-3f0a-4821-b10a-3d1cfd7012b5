package com.vasco.digipass.sdk.utils.devicebinding;

import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(bv = {}, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0003\n\u0002\b\u0005\u0018\u0000 \r2\u00060\u0001j\u0002`\u0002:\u0001\u0004B\u001d\b\u0007\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t¢\u0006\u0004\b\u000b\u0010\fR\u0017\u0010\b\u001a\u00020\u00038\u0006¢\u0006\f\n\u0004\b\u0004\u0010\u0005\u001a\u0004\b\u0006\u0010\u0007¨\u0006\u000e"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDKException;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "", "a", "I", "getErrorCode", "()I", "errorCode", "", "cause", "<init>", "(ILjava/lang/Throwable;)V", "Companion", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingSDKException.smali */
public final class DeviceBindingSDKException extends Exception {

    /* renamed from: a, reason: from kotlin metadata */
    private final int errorCode;

    /* JADX WARN: Multi-variable type inference failed */
    public DeviceBindingSDKException(int i) {
        this(i, null, 2, 0 == true ? 1 : 0);
    }

    public /* synthetic */ DeviceBindingSDKException(int i, Throwable th, int i2, DefaultConstructorMarker defaultConstructorMarker) {
        this(i, (i2 & 2) != 0 ? new Throwable() : th);
    }

    public final int getErrorCode() {
        return this.errorCode;
    }

    public DeviceBindingSDKException(int i, Throwable th) {
        super(th);
        this.errorCode = i;
    }
}
