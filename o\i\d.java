package o.i;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import kotlin.io.encoding.Base64;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static d a;
    private static long c;
    private static char[] e;
    private static int i;
    private static int j;
    private final HashMap<f, g> b = new HashMap<>();
    private o.eg.b d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        j();
        MotionEvent.axisFromString("");
        ViewConfiguration.getKeyRepeatDelay();
        KeyEvent.getDeadChar(0, 0);
        ViewConfiguration.getWindowTouchSlop();
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getEdgeSlop();
        ViewConfiguration.getMaximumFlingVelocity();
        Process.getGidForName("");
        TextUtils.lastIndexOf("", '0');
        PointF.length(0.0f, 0.0f);
        AndroidCharacter.getMirror('0');
        TextUtils.getTrimmedLength("");
        ViewConfiguration.getGlobalActionKeyTimeout();
        ViewConfiguration.getScrollBarSize();
        AndroidCharacter.getMirror('0');
        int i2 = i + 95;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '\n' : (char) 20) {
            case '\n':
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$a = new byte[]{3, 85, -79, -50};
        $$b = 234;
    }

    static void j() {
        char[] cArr = new char[1840];
        ByteBuffer.wrap("½qÒÖbcóõ\u0003\u0019\u0090\u008a 1±·ÁûQ^æìva\u0087\u009b\u0017\u0001¤¨4$DAÕòetú\u0098\n\t\u009b¹+\t»PÈÞXséçy\u001d\u008e£\u001e>¯¢?ÜOuÜæl\u0002IW&Ã\u0096d\u0007ð÷\u0012d\u0092Ô\u0007E£5Ç¥h\u0012í\u0082ms\u009aã\u0016P¢À:°A!£\u0091=\u000eÁþ\u0018o¢ß#O\u0005<Ï¬{\u001dü\u008d\bz\u008aê*°2ß¦o\u0001þ\u0095\u000ew\u009d÷-b¼ÆÌ¢\\\rë\u0088{\b\u008aÿ\u001as©Ç9_I$ØÆhX÷¤\u0007p\u0096Ô&\\¶`Å¹U\u001cä\u0092ta\u0083»\u0013G¢Ø2¸BGÑ\u009eadðç\u0000\u0003¯Ü?®O$ÞÏn\u001cýè\ry\u009cÅ,\u001a¼;Ë½[\u0003êôz|\u0089Á\u0019E©'8µH@×\u0096g`ö½\u0006X\u0095Ó%¯µyÄ\u009bTbãâsQ\u0082Ý\u0012\u00ad¢51\u0092A\\Ð¯`w\u008fÙ\u001f\u001c¯\">©NIÝ\u008amrüË\fZ\u009c2+¶»\u0006ßI°Ý\u0000z\u0091îa\fò\u008cB\u0019Ó½£Ù3v\u0084ó\u0014så\u0084u\bÆ¼V$&_·½\u0007#\u0098ßh\u000bù¯I'Ù\u001bªÂ:g\u008bé\u001b\u001aìÀ|\"Í§]Ç-h¾ä\u000e\u0010\u009f\u0088oxÀ¡PÛ X±´\u0001g\u0092\u0093b\u0002ó¾CaÓ@¤Æ4x\u0085\u008f\u0015\u0007æºv>Æ\\WÎ';¸í\b\u001b\u0099Æi#ú¨JÔÚ\u0002«þ;\u001d\u008c\u009d\u001críï}ÑÍ]^º.b¿\u0087\u000fEà£p7ÀTQÀ!f²æ\u0002\b,¼C(ó\u008fb\u001b\u0092ù\u0001y±ì HP,À\u0083w\u0006ç\u0086\u0016q\u0086ý5I¥ÑÕªDHôÖk*\u009bþ\nZºÒ*îY7É\u0092x\u001cèï\u001f5\u008f×>R®2Þ\u009dM\u0011ýål}\u009c\u008d3T£.Ó\u00adBAò\u009eal\u0091æ\u0000\u0005°Ö ¢W3Ç\u0097v(æé\u0015O\u0085É5¾¤6Ô\u008bK\u0007ûåjw\u009a\u0082\t\\¹*)÷X\u0012Èá\u007f}ï\u008b\u001eW\u008e,>¬\u00adCÝÞL`ü°\u0013M\u0083×3²¢tÒ\u008eA\u0002ñü`E\u0090\u009b\u0000£·.'ÌV\u001cÆüutåÁ\u0014G\u0084'4±,¼C(ó\u008fb\u001b\u0092ù\u0001y±ì HP,À\u0091w\u0017ç\u0086\u0016`\u0086ù5W¥ÂÕ\u009aD\u0007ô\u008ekd\u009bé\n\fº\u0092*îY#É\u0085x\u0012èæ\u001f|\u008fÊ>P®fÞ\u0087M\u001dýül:\u009cÀ3]£?\u008aNåÚU}Äé4\u000b§\u008b\u0017\u001e\u0086ºöÞfcÑåAt°\u0092 \u000b\u0093¥\u00030shâõR|Í\u0096=\u001b¬þ\u001c`\u008c\u001cÿÎowÞóN\u0017¹\u008e)8\u0098¢\b\u0094xuëï[\u000eÊÈ:2\u0095¯\u0005Íu\fäçTmÇÑ7\u0014¦¿\u0016#\u0086\u0015ñÁasÐ\u0093@\u001a³¬#&\u0093P\u0002Êr<íì]\u001cÌ\u0084,¼C(ó\u008fb\u001b\u0092ù\u0001y±ì HP,À\u0091w\u0017ç\u0086\u0016`\u0086ù5W¥ÂÕ\u009aD\u0007ô\u008ekd\u009bé\n\fº\u0092*îY\u001fÉ\u008fxSèï\u001fp\u008fÐ>_®)Þ\u008dMXýèlu\u009cß3N£*Ó\u00adB\u0011ò\u009fam\u0091ö\u0000L°Ú  WvÇ\u008dvgæ»\u0015^\u0085Õ5©¤\u007fÔËK\u0002û j{\u009aÃ\tF¹d)µX\u0003Èì\u007fvï\u008b\u001e\\\u008e\">©\u00ad\u0001Ý\u009aL-ü°\u0013P\u0083Ù3¬¢$Ò\u0097A\u001fñ÷`O+®D:ô\u009de\t\u0095ë\u0006k¶þ'ZW>Ç\u0083p\u0005à\u0094\u0011r\u0081ë2E¢ÐÒ\u0088C\u0015ó\u009clv\u009cû\r\u001e½\u0080-ü^ Î\u0084\u007f\fï°\u0018a\u0088Ä9J©9ÙÛJ\u0019úük|\u009bË4G¤3Ô«ESõ\u008dfw\u0096 \u0007\u0012·Õ'õP,À\u008aqiá©\u0012Z\u0082Ê2»£#ÓÜL\u0011ü÷mu\u009dÂ\u000eN¾3.³_\u0011Ïÿx*èÐ\u0019F\u0089\u007f9ºª\u0015Ú\u0089K3ûï\u0014P\u0084Ð4û¥fÕ\u009cF\u0010öøg\u001a\u0097Ê\u0007\u00ad°= \u008cQ\bÁòrwâ\u0092\u0013@\u0083$3³¬\u0013Ü\u0088MdýÏn\n\u009e:\u000e§¿\n/\u0080XiÈ¬yZéÑ\u0019ñ\u008a5:\u0087«\u0002ÛôtpäÎ\u0014¾,¼C(ó\u008fb\u001b\u0092ù\u0001y±ì HP,À\u0091w\u0017ç\u0086\u0016`\u0086ù5W¥ÂÕ\u009aD\u0007ô\u008ekd\u009bé\n\fº\u0092*îY$É\u0090x\u0017èã\u001fa\u008fÁ>\u0017®%Þ\u0086M\u0015ýûlv\u009cÈ3H£*Óº#×LLüæmq\u009d\u009d\u000e/¾²/?_JÏÜxtèø\u0019\u0018\u0089\u009f\u0084oëô[^ÊÉ:%©\u0097\u0019\n\u0088\u0087øòhdßÌO@¾ .'\u009dÇ\rL}9ìÍ\\CÃ©38¢\u009c\u0012\u000b\u0082gñþaNÐ\u0093@'·»'\u0007\u0096\u0098\u0006óvGåÌU.Ä¨4\b\u009b\u0098\u009f½ð;@\u008bÑ\u000e!î²i\u0002Á\u0093uã\"s¢Ä\rT\u009b¥g5á\u0086n\u0016Þf\u008d÷\u000bG\u008eØy(ö¹O,»C=ó\u008db\b\u0092è\u0001o±Ç sP$À¤w\u000bç\u009d\u0016a\u0086ç5r¥ÆÕ¶D\u0006ô¸kx\u009bä\n\\ºË*¡Y\u0012É\u008fx\u001eèò\u001f`\u008fÐ>V®2Þ\u0080M\u0017ýå,»C=ó\u008db\b\u0092è\u0001o±Ç sP$À¤w\u000bç\u009d\u0016a\u0086ç5h¥ØÕ\u008bD\rô\u0088kz\u009bò\nBºÌ*«Y\u0012É\u008fx\u001eèï\u001ft\u008fÊ>SY^6Ø\u0086h\u0017íç\rt\u008aÄ\"U\u0096%ÁµA\u0002î\u0092xc\u0084ó\u0002@\u008dÐ= n1è\u0081m\u001e\u009fî\u0017\u007f§Ï)_N,÷¼j\rû\u009d\nj\u0091ú/K¶Û\u0083«!8½\u0088$\u0019\u008cé'F·Ö\u008a¦~7ü\u0087v\u0014\u0083ä\u0007u´Å8UM\"Ý,¯C*óÅb\u001b\u0092ã\u0001h±Ê RP.À wMç\u0093\u0016k\u0086à5B¥ÚÕ¶D\u0018ô\u0093ki\u009bø\n@ºÖ*¬Y#É\u0081x\u0001èû\u001fJ\u008f×>_®'Þ\u009bM\u001dýïlE\u009cÝ3N£*Ó¸B\u0004ò\u0082af\u0091ü\u0000F°Ñ ´,ªC.ó\u0086b\u0017\u0092ê\u0001n±\u0081 ]P7À½w\u0000ç\u009d\u0016k\u0086ò\u008eÿá{QßÀO0¢£)\u0013\u009d\u0082;òhbøÕCEÝ´$$ò\u0097L\u0007ÐwìæKVÏÉ%9º¨\u0006\u0018\u0090\u0088òûrkÂÚ\u0015J®½ -\u008d\u009c\u001f\f |\u0095ï\u001e,¹C=ó\u0099b\t\u0092ä\u0001o±Û }P.À¾w\u0005ç\u009b\u0016b\u0086´5\n¥\u0096Õ¼D\u0005ô\u008bk~\u009bä\n\fºÍ*«Y\"É\u008fx\u0006èð\u001fv\u008fÁ>D¤öËr{ÖêF\u001a«\u0089 9\u0094¨2ØaHñÿJoÔ\u009e-\u000eû½E-Ù]åÌS|Ûã7\u0013»\u0082\r2\u0097¢¡Ñ}AÆðL`¥\u0097?\u0007\u0099¶\u001d&mV\u0086Å]u·ä:\u0014\u008c»S+t[þÊ\u000ezÛé%\u0019®\u0088\u0001ñ\u007f\u009eù.N¿ÚO\u0019Ü½l\u0019ý\u0089\u008dì\u001dgªÓ:SË¥[\u0013è\u008cx\u001c\b{\u0099Å)X¶îFt×Èg\u0015÷e\u0084µ\u0014G¥Á5+ÂñR\u0003ã\u009csì\u0003K\u0090Õ (±þA\u001aî\u008c~ä\u000eh\u009fÀ/P¼çLlÝÁm\u0003ýw\u008aý\u001aM«¼;6È\u0080X\u001eè(yë\tX\u0096Ú&'·²G\u0015Ô\u0082déô}\u0085Å,»C=ó\u008ab\u001e\u0092Ý\u0001y±Ý MP(À£w\u0017ç\u0097\u0016a\u0086×5H¥ØÕ¿D\u0001ô\u009ck*\u009b°\n\fºÞ*îY2É\u0096x\u001eè¢\u001fv\u008fË>Y® Þ\u0080M\u001fý«ls\u009cÞ3\u001c£<ÓªB\u000eò\u0082af\u0091ö\u0000\u0005°Û ©WvÇ\u009dvaæè\u0015A,»C=ó\u008ab\u001e\u0092Ý\u0001y±Ý MP(À£w\u0017ç\u0097\u0016a\u0086×5H¥ØÕ¿D\u0001ô\u009ck*\u009b°\n\fºÛ*«Y2É\u0092x\nèò\u001fa\u008fÁ>S®fÞ\u008aM\u000eýæl:\u009cÎ3S£!Ó¸B\bò\u0097a#\u0091¨\u0000\u0005,»C=ó\u008ab\u001e\u0092Ý\u0001y±Ý MP(À£w\u0017ç\u0097\u0016a\u0086×5H¥ØÕ¿D\u0001ô\u009ck*\u009b°\n\fºÚ*£Y!É\u0094x\nè¢\u001fq\u008fÁ>T®4Þ\u0090M\býÿl\u007f\u009cÉ3\u001c£,Ó±B\u000fò\u0096aj\u0091õ\u0000P°Æ ¦W\"Ç\u0090vgæõ\u0015\n\u0085\u00905ì¤6Ô\u0089K\u001fûïja\u009aË\t[¹#)÷X\u000fÈý,»C=ó\u008ab\u001e\u0092Ý\u0001y±Ý MP(À£w\u0017ç\u0097\u0016a\u0086×5H¥ØÕ¿D\u0001ô\u009c½\u0019Ò\u008ab.óª\u0003L\u0090Î j±ÓÁ\u0084Q\u0004æ«v=\u0087Á\u0017r¤è4dD,Õ¸e?úË\nI\u009bé,¹C*ó\u008eb\n\u0092ì\u0001n±Ê sP$À¤w\u000bç\u009d\u0016a\u0086Ò5H¥ÄÕ\u008cD\u0018ô\u009fkk\u009bé\nIº\u009f*ãYqÉ\u008dx\u0016èö\u001f}\u008fË>S®#ÞÔMXý®li\u009c\u008d3S£)ÓþB\u0015ò\u0089as\u0091÷\u0000\u0005°\u0091 ´WvÇÄv(æ¾\u0015Yhõ\u0007F·â&nÖ\u0086E\u0000õ¦d*\u0014S\u0084Ã\u0012h}ØÍo\\ü¬\u001c?\u0090\u008f<\u001eºnÐþ\\IéÙyDï+q\u009bÎ\nW¬uÃæsBâÎ\u0012&\u0081 1\u0006 \u0086Ðø,¢C=ó\u0092b\t\u0092ù\u0001s±Ý [P\u0011À¥w\u0001ç\u009e\u0016l\u0086÷5l¥ÓÕ E\u0084*\"\u009a\u0091\u000b\u001dû÷h4ØÐI^9 ©´\u001e\u000e\u008eÚ\u007flïÿ\\[Ì×¼§-\u0001\u009d\u0087\u0002gòÞcAÓÎCµ0- \u0087\u0011\t\u0081ïv=æÊWPÇ<·Á$3\u0094Õ\u0005_õ\u0085¯nÀåpfáÆ\u0011&\u0082¶2\u0004£\u009eÓûCwôÌdP\u0095¸\u0005\u001c¶\u009b&\u001dVrÇöwPè¶\u0018:\u0089\u008d9\u0015©AÚóJBûÒk;\u009c¾\f\u000e½Ù-¥]\u0007ÎÂ~7ï¡\u001f\u0006°Ò »P0ÁÎq\u001eâ®\u00123\u0083\u00863\n£eÔñDVõ¨e!\u0096Ä\u0006\u0012¶w'åWHÈÚx é©\u0019\u0005\u008a\u0098:ëªmÛÁK(ü¸lE\u009d\u0099\ræ½f.É^_Ï«\u007f~\u0090\u0085\u0000\u001d°x!ºQKÂÝr2ã\u0088\u0013U\u0083b4ü¤WÕßE$²\u0004Ý\u0094m<ü°\fH\u009fÆ/O¾ëÎ\u008f^&é°y5\u0088Ç\u0018C«á;GK\u001fÚ¸j-õÅ\u0005J\u0094¯$1´M\u0017àxpÈØYT©¬:\"\u008a«\u001b\u000fkkûÂLTÜÑ-#½§\u000e\u0005\u009e£îû\u007f\\ÏÉP! ®1K\u0081Õ\u0011©bUòñCyÓå$w´\u0090\u0005P\u0095nåÈv\u001fÆ¸W$§\u009a\b\u001e\u0098(è¼yUÉ\u0097Zyªõ;G\u008b\u0080,\u008cC*ó\u0099b\u0015\u0092ÿ\u0001<±Ø VP(À¼w\u0006çÒ\u0016c\u0086ø5R¥ÅÕ±D#ô\u009eks\u009bî\nXºÐ*¼Y4ÉÀx\u0015èí\u001fg\u008f\u0084>t®\u0010Þ¤MX,¨C;ó\u009fb\u0013\u0092û\u0001y±î KP5À¸w\u0006ç\u009c\u0016q\u0086ý5D¥×Õ\u00adD\u0001ô\u0094kd\u009bÐ\nIºË*¦Y>É\u0084x\u0000YI6Í\u0086~\u0017þç\u0001t\u008dÄ:Uÿ%Ãµ^\u0002÷\u0092}c\u0090óU@¯Ð$ \u00181û\u0081\u007f\u001e\u0088î\u0019\u007f¤Ï(_J,Ô¼!\rô\u009d\fj\u0086úeK·Û\u0087«k8ï\u0088\u0007\u0019Ûé8FµÖÏ¦K7 \u0087u\u0014\u008dä\u0016u·ÅuUH\"Ø²l\u0003É\u0093\u001e`®ð:@DÑÐ¡j>°\u008e\u0000\u001fÒï.|µÌÝ\\\u0016-æ½\u001c\n\u008d\u009a/k¶ûÜKIØ®¨|9\u008f\u0089\u0004f¬ö'q,\u001e\u008b®)?ìÏX\\Øì|}ì\r\u0092\u009d\b*¡º-KÒÛNh±øi\u0088\u001c\u0019þ©+6ÓÆYWøç`w\u001c\u0004\u0083\u00943%«µ\u0014BÅÒ}cóóÐ\u0083+\u0010¦ X1\u008cÁhnúþ\u009c\u008e\u000b\u001f¾¯ <ÜÌA]÷í\"}\u0010\n\u0095\u009a;+Ö»HHòØ\u007fh\u0013ù\u008a\u00899\u0016³¦_7ÊÇzT£ä\u009ft\u0004\u0005¤\u0095W\"Á²y,\u009dC0ó\u008ebZ\u0092þ\u0001l±Ê ]P(À¶w\nç\u0097\u0016a\u0086´5F¥ÃÕ\u00adD\u0000ô\u009ekd\u009bé\nEºÜ*¯Y%É\u0089x\u001cèì\u001f5\u008fÉ>R®2Þ\u0081M\u0017ýïl:\u009cÄ3O£oÓ°B\u000eò\u0084a#\u0091ó\u0000F°À ®W Ç\u0098v|æþ\u0015N".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1840);
        e = cArr;
        c = -3459772925850336424L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 102
            byte[] r0 = o.i.d.$$a
            int r9 = r9 * 3
            int r9 = 1 - r9
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L15:
            r3 = r2
        L16:
            int r8 = r8 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r9 = -r9
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.l(int, short, short, java.lang.Object[]):void");
    }

    private d() {
    }

    public static synchronized d c() {
        synchronized (d.class) {
            int i2 = j + 77;
            i = i2 % 128;
            int i3 = i2 % 2;
            if (a == null) {
                a = new d();
            }
            d dVar = a;
            int i4 = j + Opcodes.LUSHR;
            i = i4 % 128;
            switch (i4 % 2 == 0 ? (char) 11 : 'L') {
                case Base64.mimeLineLength /* 76 */:
                    return dVar;
                default:
                    int i5 = 56 / 0;
                    return dVar;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void a(android.content.Context r20, java.util.HashMap<o.i.f, o.i.g> r21) {
        /*
            Method dump skipped, instructions count: 594
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.a(android.content.Context, java.util.HashMap):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean e(o.eg.e r15) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 412
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.e(o.eg.e):boolean");
    }

    public final void e() {
        int i2 = i + 79;
        j = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((char) (37371 - ExpandableListView.getPackedPositionGroup(0L)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 35 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (3960 - Color.blue(0)), View.MeasureSpec.makeMeasureSpec(0, 0) + 659, KeyEvent.keyCodeFromString("") + 14, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        for (g gVar : this.b.values()) {
            try {
                switch (!(gVar instanceof k)) {
                    case true:
                        break;
                    default:
                        ((k) gVar).d();
                        break;
                }
                int i4 = i + 85;
                j = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (a e2) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 37371), TextUtils.getCapsMode("", 0, 0), TextUtils.indexOf((CharSequence) "", '0') + 36, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                k((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 43200), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 672, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 38, objArr4);
                o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e2);
            }
        }
        int i5 = j + 85;
        i = i5 % 128;
        if (i5 % 2 != 0) {
            return;
        }
        Object obj = null;
        obj.hashCode();
        throw null;
    }

    public final g e(f fVar) {
        int i2 = i + 77;
        j = i2 % 128;
        int i3 = i2 % 2;
        g gVar = this.b.get(fVar);
        int i4 = i + Opcodes.DREM;
        j = i4 % 128;
        int i5 = i4 % 2;
        return gVar;
    }

    public final HashMap<f, g> b() {
        int i2 = j;
        int i3 = i2 + 69;
        i = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                HashMap<f, g> hashMap = this.b;
                int i4 = i2 + 83;
                i = i4 % 128;
                int i5 = i4 % 2;
                return hashMap;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.List<o.i.g> a() {
        /*
            r5 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.HashMap<o.i.f, o.i.g> r1 = r5.b
            java.util.Set r1 = r1.entrySet()
            java.util.Iterator r1 = r1.iterator()
        L10:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L19
            r2 = 67
            goto L1b
        L19:
            r2 = 9
        L1b:
            switch(r2) {
                case 9: goto L2b;
                default: goto L1e;
            }
        L1e:
            int r2 = o.i.d.j
            int r2 = r2 + 113
            int r3 = r2 % 128
            o.i.d.i = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L2c
            goto L2c
        L2b:
            return r0
        L2c:
            java.lang.Object r2 = r1.next()
            java.util.Map$Entry r2 = (java.util.Map.Entry) r2
            java.lang.Object r3 = r2.getValue()
            o.i.g r3 = (o.i.g) r3
            o.i.c r3 = r3.j()
            o.i.c r4 = o.i.c.a
            if (r3 != r4) goto L42
            r3 = 0
            goto L43
        L42:
            r3 = 1
        L43:
            switch(r3) {
                case 1: goto L71;
                default: goto L46;
            }
        L46:
            int r3 = o.i.d.i
            int r3 = r3 + 69
            int r4 = r3 % 128
            o.i.d.j = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L55
            r3 = 28
            goto L57
        L55:
            r3 = 51
        L57:
            switch(r3) {
                case 28: goto L64;
                default: goto L5a;
            }
        L5a:
            java.lang.Object r2 = r2.getValue()
            o.i.g r2 = (o.i.g) r2
            r0.add(r2)
            goto L71
        L64:
            java.lang.Object r1 = r2.getValue()
            o.i.g r1 = (o.i.g) r1
            r0.add(r1)
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L6f
        L6f:
            r0 = move-exception
            throw r0
        L71:
            goto L10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.a():java.util.List");
    }

    /* JADX WARN: Removed duplicated region for block: B:4:0x0018  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.Map<fr.antelop.sdk.authentication.CustomerAuthenticationMethodType, fr.antelop.sdk.authentication.CustomerAuthenticationMethod> d() {
        /*
            r5 = this;
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            java.util.HashMap r1 = r5.b()
            java.util.Collection r1 = r1.values()
            java.util.Iterator r1 = r1.iterator()
        L12:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L43
            java.lang.Object r2 = r1.next()
            o.i.g r2 = (o.i.g) r2
            o.i.f r3 = r2.i()
            fr.antelop.sdk.authentication.CustomerAuthenticationMethodType r3 = r3.b()
            fr.antelop.sdk.authentication.CustomerAuthenticationMethod r4 = new fr.antelop.sdk.authentication.CustomerAuthenticationMethod
            r4.<init>(r2)
            r0.put(r3, r4)
            int r2 = o.i.d.j
            int r2 = r2 + 57
            int r3 = r2 % 128
            o.i.d.i = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L3d
            r2 = 86
            goto L3f
        L3d:
            r2 = 85
        L3f:
            switch(r2) {
                case 85: goto L42;
                default: goto L42;
            }
        L42:
            goto L12
        L43:
            int r1 = o.i.d.j
            int r1 = r1 + 119
            int r2 = r1 % 128
            o.i.d.i = r2
            int r1 = r1 % 2
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.d():java.util.Map");
    }

    public final List<g> d(i iVar) {
        ArrayList arrayList = new ArrayList();
        for (g gVar : this.b.values()) {
            int i2 = i + 97;
            j = i2 % 128;
            int i3 = i2 % 2;
            switch (gVar.e(iVar) ? '9' : ';') {
                case ';':
                    break;
                default:
                    int i4 = j + 43;
                    i = i4 % 128;
                    boolean z = i4 % 2 != 0;
                    arrayList.add(gVar);
                    switch (z) {
                        case false:
                            throw null;
                    }
            }
        }
        return arrayList;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.i.g a(o.f.e r5) {
        /*
            r4 = this;
            java.util.HashMap<o.i.f, o.i.g> r0 = r4.b
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        Lb:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L14
            r1 = 9
            goto L16
        L14:
            r1 = 42
        L16:
            switch(r1) {
                case 42: goto L26;
                default: goto L19;
            }
        L19:
            int r1 = o.i.d.i
            int r1 = r1 + 125
            int r2 = r1 % 128
            o.i.d.j = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L28
            goto L28
        L26:
            r5 = 0
            return r5
        L28:
            java.lang.Object r1 = r0.next()
            o.i.g r1 = (o.i.g) r1
            o.i.f r2 = r1.i()
            o.i.f r3 = r5.b()
            boolean r2 = r2.equals(r3)
            if (r2 == 0) goto L3f
            r2 = 91
            goto L41
        L3f:
            r2 = 97
        L41:
            switch(r2) {
                case 91: goto L45;
                default: goto L44;
            }
        L44:
            goto L50
        L45:
            int r5 = o.i.d.i
            int r5 = r5 + 35
            int r0 = r5 % 128
            o.i.d.j = r0
            int r5 = r5 % 2
            return r1
        L50:
            goto Lb
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.a(o.f.e):o.i.g");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private o.eg.e g() throws o.eg.d {
        /*
            r6 = this;
            o.eg.e r0 = new o.eg.e
            r0.<init>()
            java.util.HashMap<o.i.f, o.i.g> r1 = r6.b
            java.util.Collection r1 = r1.values()
            java.util.Iterator r1 = r1.iterator()
        L10:
            boolean r2 = r1.hasNext()
            r3 = 0
            r4 = 1
            if (r2 == 0) goto L1a
            r2 = r3
            goto L1b
        L1a:
            r2 = r4
        L1b:
            switch(r2) {
                case 1: goto L2b;
                default: goto L1e;
            }
        L1e:
            int r2 = o.i.d.j
            int r2 = r2 + 27
            int r5 = r2 % 128
            o.i.d.i = r5
            int r2 = r2 % 2
            if (r2 != 0) goto L37
            goto L36
        L2b:
            int r1 = o.i.d.i
            int r1 = r1 + 57
            int r2 = r1 % 128
            o.i.d.j = r2
            int r1 = r1 % 2
            return r0
        L36:
            goto L38
        L37:
            r3 = r4
        L38:
            switch(r3) {
                case 0: goto L49;
                default: goto L3b;
            }
        L3b:
            java.lang.Object r2 = r1.next()
            o.i.g r2 = (o.i.g) r2
            o.eg.b r2 = r2.k()
            r0.b(r2)
            goto L10
        L49:
            java.lang.Object r1 = r1.next()
            o.i.g r1 = (o.i.g) r1
            o.eg.b r1 = r1.k()
            r0.b(r1)
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L5b
            throw r0     // Catch: java.lang.Throwable -> L5b
        L5b:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.g():o.eg.e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void e(android.content.Context r11, o.ei.c r12) {
        /*
            r10 = this;
            int r0 = o.i.d.j
            int r0 = r0 + 41
            int r1 = r0 % 128
            o.i.d.i = r1
            int r0 = r0 % 2
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getJumpTapTimeout()
            r1 = 16
            int r0 = r0 >> r1
            r2 = 37371(0x91fb, float:5.2368E-41)
            int r0 = r0 + r2
            char r0 = (char) r0
            java.lang.String r2 = ""
            r3 = 48
            int r2 = android.text.TextUtils.lastIndexOf(r2, r3)
            r3 = 1
            int r2 = r2 + r3
            r4 = 0
            int r5 = android.graphics.drawable.Drawable.resolveOpacity(r4, r4)
            int r5 = 35 - r5
            java.lang.Object[] r6 = new java.lang.Object[r3]
            k(r0, r2, r5, r6)
            r0 = r6[r4]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r2 = android.view.ViewConfiguration.getEdgeSlop()
            int r2 = r2 >> r1
            r5 = 45830(0xb306, float:6.4222E-41)
            int r2 = r2 + r5
            char r2 = (char) r2
            r5 = 0
            int r7 = android.widget.ExpandableListView.getPackedPositionChild(r5)
            int r7 = r7 + 712
            long r8 = android.widget.ExpandableListView.getPackedPositionForGroup(r4)
            int r5 = (r8 > r5 ? 1 : (r8 == r5 ? 0 : -1))
            int r5 = 22 - r5
            java.lang.Object[] r6 = new java.lang.Object[r3]
            k(r2, r7, r5, r6)
            r2 = r6[r4]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            o.ee.g.d(r0, r2)
            o.fm.c r12 = r12.d()
            o.fm.d r12 = r12.c()
            java.util.HashMap<o.i.f, o.i.g> r0 = r10.b
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
            int r2 = o.i.d.j
            int r2 = r2 + 121
            int r5 = r2 % 128
            o.i.d.i = r5
            int r2 = r2 % 2
        L7d:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L85
            r2 = r4
            goto L86
        L85:
            r2 = r3
        L86:
            switch(r2) {
                case 0: goto L8a;
                default: goto L89;
            }
        L89:
            return
        L8a:
            int r2 = o.i.d.j
            int r2 = r2 + 51
            int r5 = r2 % 128
            o.i.d.i = r5
            int r2 = r2 % 2
            if (r2 != 0) goto L98
            r2 = r1
            goto L9a
        L98:
            r2 = 36
        L9a:
            switch(r2) {
                case 16: goto Laf;
                default: goto L9d;
            }
        L9d:
            java.lang.Object r2 = r0.next()
            o.i.g r2 = (o.i.g) r2
            o.i.f r5 = r2.i()
            boolean r5 = r12.a(r5)
            r2.a(r11, r5)
            goto L7d
        Laf:
            java.lang.Object r2 = r0.next()
            o.i.g r2 = (o.i.g) r2
            o.i.f r5 = r2.i()
            boolean r5 = r12.a(r5)
            r2.a(r11, r5)
            r2 = 70
            int r2 = r2 / r4
            goto L7d
        Lc4:
            r11 = move-exception
            throw r11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.e(android.content.Context, o.ei.c):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void c(android.content.Context r8, java.util.List<o.i.f> r9) {
        /*
            Method dump skipped, instructions count: 252
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.c(android.content.Context, java.util.List):void");
    }

    public final boolean b(Context context, o.ei.c cVar, o.bb.d dVar) {
        boolean z;
        Iterator<g> it;
        int i2 = j + 21;
        i = i2 % 128;
        int i3 = i2 % 2;
        switch (dVar.d() == o.bb.a.p ? (char) 14 : '(') {
            case 14:
                int i4 = i + Opcodes.LREM;
                j = i4 % 128;
                if (i4 % 2 != 0) {
                }
                return false;
            default:
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((char) (37371 - (ViewConfiguration.getEdgeSlop() >> 16)), View.resolveSize(0, 0), 36 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((char) TextUtils.getTrimmedLength(""), 767 - TextUtils.lastIndexOf("", '0', 0), 31 - Color.red(0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                o.eg.b e2 = e(context);
                o.fm.d c2 = cVar.d().c();
                try {
                    it = this.b.values().iterator();
                    int i5 = i + 55;
                    j = i5 % 128;
                    int i6 = i5 % 2;
                    z = false;
                } catch (o.eg.d e3) {
                    e = e3;
                    z = false;
                }
                while (true) {
                    try {
                    } catch (o.eg.d e4) {
                        e = e4;
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        k((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 37371), 1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 36 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        k((char) (30180 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), (ViewConfiguration.getScrollBarSize() >> 8) + 799, 48 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr4);
                        o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e);
                        e2 = new o.eg.b();
                        b(context, e2);
                        new o.dl.c();
                        o.dl.c.b().a();
                        return z;
                    }
                    switch (!it.hasNext()) {
                        case false:
                            int i7 = j + 81;
                            i = i7 % 128;
                            int i8 = i7 % 2;
                            g next = it.next();
                            z |= next.e(context, c2.a(next.i()), dVar);
                            e2.d(next.i().e(), next.a_());
                        default:
                            b(context, e2);
                            new o.dl.c();
                            o.dl.c.b().a();
                            return z;
                    }
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void b(android.content.Context r6, o.ei.c r7) {
        /*
            r5 = this;
            int r0 = o.i.d.j
            int r0 = r0 + 11
            int r1 = r0 % 128
            o.i.d.i = r1
            int r0 = r0 % 2
            o.eg.b r0 = r5.e(r6)
            o.fm.c r7 = r7.d()
            o.fm.d r7 = r7.c()
            java.util.HashMap<o.i.f, o.i.g> r1 = r5.b
            java.util.Collection r1 = r1.values()
            java.util.Iterator r1 = r1.iterator()
        L21:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L29
            r2 = 0
            goto L2a
        L29:
            r2 = 1
        L2a:
            r3 = 0
            switch(r2) {
                case 0: goto L3b;
                default: goto L2e;
            }
        L2e:
            o.dl.c r6 = new o.dl.c
            r6.<init>()
            o.h.d r6 = o.dl.c.b()
            r6.a()
            goto L91
        L3b:
            int r2 = o.i.d.i
            int r2 = r2 + 109
            int r4 = r2 % 128
            o.i.d.j = r4
            int r2 = r2 % 2
            if (r2 == 0) goto L4a
            r2 = 25
            goto L4c
        L4a:
            r2 = 56
        L4c:
            switch(r2) {
                case 25: goto L6d;
                default: goto L4f;
            }
        L4f:
            java.lang.Object r2 = r1.next()
            o.i.g r2 = (o.i.g) r2
            o.i.f r3 = r2.i()
            boolean r3 = r7.a(r3)
            o.i.f r4 = r2.i()
            java.lang.String r4 = r4.e()
            o.eg.b r4 = r0.u(r4)
            r2.c(r6, r3, r4)
            goto L90
        L6d:
            java.lang.Object r1 = r1.next()
            o.i.g r1 = (o.i.g) r1
            o.i.f r2 = r1.i()
            boolean r7 = r7.a(r2)
            o.i.f r2 = r1.i()
            java.lang.String r2 = r2.e()
            o.eg.b r0 = r0.u(r2)
            r1.c(r6, r7, r0)
            r3.hashCode()     // Catch: java.lang.Throwable -> L8e
            throw r3     // Catch: java.lang.Throwable -> L8e
        L8e:
            r6 = move-exception
            throw r6
        L90:
            goto L21
        L91:
            int r6 = o.i.d.i
            int r6 = r6 + 123
            int r7 = r6 % 128
            o.i.d.j = r7
            int r6 = r6 % 2
            if (r6 != 0) goto L9e
            return
        L9e:
            throw r3     // Catch: java.lang.Throwable -> L9f
        L9f:
            r6 = move-exception
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.b(android.content.Context, o.ei.c):void");
    }

    public final void d(Context context) {
        int i2 = j + 75;
        i = i2 % 128;
        int i3 = i2 % 2;
        this.d = null;
        this.b.clear();
        Object[] objArr = new Object[1];
        k((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 847 - (ViewConfiguration.getEdgeSlop() >> 16), 47 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), 894 - (ViewConfiguration.getScrollBarSize() >> 8), 13 - ImageFormat.getBitsPerPixel(0), objArr2);
        edit.putString(((String) objArr2[0]).intern(), "").commit();
        int i4 = j + 91;
        i = i4 % 128;
        int i5 = i4 % 2;
    }

    final void b(Context context, o.eg.b bVar) {
        Object[] objArr = new Object[1];
        k((char) Color.alpha(0), 847 - View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 47, objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        k((char) (37372 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), ViewConfiguration.getScrollBarSize() >> 8, 35 - TextUtils.getOffsetBefore("", 0), objArr2);
        String intern = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        k((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 41541), TextUtils.indexOf((CharSequence) "", '0', 0) + 909, 33 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr3);
        o.ee.g.d(intern, sb.append(((String) objArr3[0]).intern()).append(bVar.b()).toString());
        switch (bVar.d() == 0 ? '^' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                this.d = bVar;
                String a2 = new o.dd.e(context).a(bVar.b());
                o.ee.g.c();
                Object[] objArr4 = new Object[1];
                k((char) (37371 - (ViewConfiguration.getJumpTapTimeout() >> 16)), ViewConfiguration.getFadingEdgeLength() >> 16, 'S' - AndroidCharacter.getMirror('0'), objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                k((char) (TextUtils.getOffsetAfter("", 0) + 34895), 973 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 46 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr5);
                o.ee.g.d(intern2, ((String) objArr5[0]).intern());
                SharedPreferences.Editor edit = sharedPreferences.edit();
                Object[] objArr6 = new Object[1];
                k((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 894 - Gravity.getAbsoluteGravity(0, 0), KeyEvent.getDeadChar(0, 0) + 14, objArr6);
                edit.putString(((String) objArr6[0]).intern(), a2).commit();
                return;
            default:
                int i2 = i + Opcodes.LSHL;
                j = i2 % 128;
                if (i2 % 2 != 0) {
                }
                o.ee.g.c();
                Object[] objArr7 = new Object[1];
                k((char) (37371 - (Process.myPid() >> 22)), (-1) - ImageFormat.getBitsPerPixel(0), 35 - Drawable.resolveOpacity(0, 0), objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                k((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), 942 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 30 - TextUtils.lastIndexOf("", '0', 0, 0), objArr8);
                o.ee.g.d(intern3, ((String) objArr8[0]).intern());
                SharedPreferences.Editor edit2 = sharedPreferences.edit();
                Object[] objArr9 = new Object[1];
                k((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), ExpandableListView.getPackedPositionChild(0L) + 895, (Process.myPid() >> 22) + 14, objArr9);
                edit2.putString(((String) objArr9[0]).intern(), "").commit();
                int i3 = i + 93;
                j = i3 % 128;
                switch (i3 % 2 != 0 ? (char) 3 : '\n') {
                    case 3:
                        int i4 = 2 / 0;
                        return;
                    default:
                        return;
                }
        }
    }

    final o.eg.b e(Context context) {
        int i2 = j + 71;
        i = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 37371), ViewConfiguration.getDoubleTapTimeout() >> 16, View.MeasureSpec.getSize(0) + 35, objArr);
        String intern = ((String) objArr[0]).intern();
        o.eg.b bVar = this.d;
        switch (bVar != null) {
            case false:
                Object[] objArr2 = new Object[1];
                k((char) View.resolveSizeAndState(0, 0, 0), 847 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 47 - TextUtils.indexOf("", "", 0, 0), objArr2);
                SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr2[0]).intern(), 0);
                Object[] objArr3 = new Object[1];
                k((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 894 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), MotionEvent.axisFromString("") + 15, objArr3);
                String string = sharedPreferences.getString(((String) objArr3[0]).intern(), "");
                if (string.isEmpty()) {
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    k((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 56772), 1018 - (Process.myPid() >> 22), Color.green(0) + 64, objArr4);
                    o.ee.g.d(intern, ((String) objArr4[0]).intern());
                    o.eg.b bVar2 = new o.eg.b();
                    int i4 = j + 61;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                    return bVar2;
                }
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                k((char) View.MeasureSpec.getSize(0), 1082 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 53, objArr5);
                o.ee.g.d(intern, ((String) objArr5[0]).intern());
                String d = new o.dd.e(context).d(string);
                o.ee.g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                k((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), (ViewConfiguration.getPressedStateDuration() >> 16) + 1134, 44 - MotionEvent.axisFromString(""), objArr6);
                o.ee.g.d(intern, sb.append(((String) objArr6[0]).intern()).append(d).toString());
                switch (d == null) {
                    case true:
                        break;
                    default:
                        if (!d.isEmpty()) {
                            try {
                                o.eg.b bVar3 = new o.eg.b(d);
                                this.d = bVar3;
                                return bVar3;
                            } catch (o.eg.d e2) {
                                o.ee.g.c();
                                Object[] objArr7 = new Object[1];
                                k((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getLongPressTimeout() >> 16) + 1244, 19 - View.MeasureSpec.getMode(0), objArr7);
                                o.ee.g.a(intern, ((String) objArr7[0]).intern(), e2);
                                return new o.eg.b();
                            }
                        }
                        break;
                }
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                k((char) (ViewConfiguration.getScrollBarSize() >> 8), 1179 - (KeyEvent.getMaxKeyCode() >> 16), 66 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr8);
                o.ee.g.e(intern, ((String) objArr8[0]).intern());
                return new o.eg.b();
            default:
                return bVar;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:30:0x01ed. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:14:0x015c  */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0164  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x01b0  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x02a8 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:56:0x019f A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:58:0x015f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.eg.e c(android.content.Context r21, boolean r22, java.util.Collection<o.i.g> r23) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 730
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.c(android.content.Context, boolean, java.util.Collection):o.eg.e");
    }

    public final boolean h() {
        Iterator<g> it = this.b.values().iterator();
        while (it.hasNext()) {
            int i2 = i + 99;
            j = i2 % 128;
            Object obj = null;
            if (i2 % 2 != 0) {
                it.next().e(i.a);
                obj.hashCode();
                throw null;
            }
            g next = it.next();
            if (next.e(i.a)) {
                switch (next instanceof n) {
                    case false:
                        continue;
                    default:
                        int i3 = i + 65;
                        j = i3 % 128;
                        if (i3 % 2 != 0) {
                            ((n) next).t();
                            throw null;
                        }
                        switch (!((n) next).t() ? ' ' : '[') {
                            case ' ':
                                o.ee.g.c();
                                Object[] objArr = new Object[1];
                                k((char) (37372 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), (-1) - TextUtils.lastIndexOf("", '0', 0), 35 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                k((char) (33742 - Gravity.getAbsoluteGravity(0, 0)), 1426 - Color.blue(0), ExpandableListView.getPackedPositionChild(0L) + 89, objArr2);
                                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                                return true;
                        }
                }
            }
        }
        int i4 = j + 43;
        i = i4 % 128;
        int i5 = i4 % 2;
        return false;
    }

    public static void a(o.ei.c cVar, boolean z, Collection<g> collection, boolean z2) {
        Object obj;
        long j2;
        o.ee.g.c();
        long j3 = 0;
        Object[] objArr = new Object[1];
        k((char) (Color.rgb(0, 0, 0) + 16814587), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), Color.green(0) + 35, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k((char) (40611 - TextUtils.getCapsMode("", 0, 0)), 1514 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), ExpandableListView.getPackedPositionType(0L) + 24, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z2).toString());
        o.fm.d c2 = cVar.d().c();
        for (g gVar : collection) {
            int i2 = i + 27;
            j = i2 % 128;
            int i3 = i2 % 2;
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            k((char) (37371 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), ViewConfiguration.getKeyRepeatDelay() >> 16, 35 - Drawable.resolveOpacity(0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            char c3 = (char) (15176 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)));
            int defaultSize = 1538 - View.getDefaultSize(0, 0);
            int i4 = (ExpandableListView.getPackedPositionForChild(0, 0) > j3 ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == j3 ? 0 : -1)) + 47;
            Object[] objArr4 = new Object[1];
            k(c3, defaultSize, i4, objArr4);
            String intern3 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[3];
            objArr5[0] = gVar.i();
            objArr5[1] = gVar.o();
            switch (z ? '6' : '!') {
                case '!':
                    Object[] objArr6 = new Object[1];
                    k((char) (16101 - TextUtils.indexOf("", "")), 1347 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getLongPressTimeout() >> 16) + 12, objArr6);
                    obj = objArr6[0];
                    break;
                default:
                    Object[] objArr7 = new Object[1];
                    k((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 17533), 1337 - Drawable.resolveOpacity(0, 0), 10 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr7);
                    obj = objArr7[0];
                    break;
            }
            objArr5[2] = ((String) obj).intern();
            o.ee.g.d(intern2, String.format(intern3, objArr5));
            if (z2) {
                c2.a(gVar.i(), z);
                switch (z) {
                    case false:
                        gVar.d(c.a);
                        try {
                            switch (!(gVar instanceof k)) {
                                case true:
                                    break;
                                default:
                                    ((k) gVar).d();
                                    break;
                            }
                            j3 = 0;
                            break;
                        } catch (a e2) {
                            o.ee.g.c();
                            Object[] objArr8 = new Object[1];
                            k((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 37372), TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getScrollBarSize() >> 8) + 35, objArr8);
                            String intern4 = ((String) objArr8[0]).intern();
                            StringBuilder sb2 = new StringBuilder();
                            Object[] objArr9 = new Object[1];
                            k((char) ('0' - AndroidCharacter.getMirror('0')), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 1584, 33 - TextUtils.lastIndexOf("", '0', 0), objArr9);
                            o.ee.g.e(intern4, sb2.append(((String) objArr9[0]).intern()).append(gVar.i()).toString());
                            j3 = 0;
                            break;
                        }
                    default:
                        gVar.d(c.c);
                        j3 = 0;
                        break;
                }
            } else {
                if (z) {
                    try {
                        if (gVar instanceof k) {
                            ((k) gVar).d();
                        }
                        int i5 = i + 97;
                        j = i5 % 128;
                        int i6 = i5 % 2;
                        j3 = 0;
                    } catch (a e3) {
                        o.ee.g.c();
                        Object[] objArr10 = new Object[1];
                        k((char) (KeyEvent.getDeadChar(0, 0) + 37371), ViewConfiguration.getScrollDefaultDelay() >> 16, Color.rgb(0, 0, 0) + 16777251, objArr10);
                        String intern5 = ((String) objArr10[0]).intern();
                        StringBuilder sb3 = new StringBuilder();
                        j2 = 0;
                        Object[] objArr11 = new Object[1];
                        k((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 1585 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 33 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr11);
                        o.ee.g.e(intern5, sb3.append(((String) objArr11[0]).intern()).append(gVar.i()).toString());
                    }
                } else {
                    j2 = 0;
                }
                j3 = j2;
            }
        }
        int i7 = j + 9;
        i = i7 % 128;
        int i8 = i7 % 2;
    }

    public final f f() {
        f fVar;
        int i2 = i + 57;
        j = i2 % 128;
        int i3 = i2 % 2;
        Set<f> keySet = this.b.keySet();
        switch (keySet.contains(f.e) ? '2' : (char) 28) {
            case '2':
                return f.e;
            default:
                if (!keySet.contains(f.d)) {
                    return f.d;
                }
                int i4 = i + 91;
                j = i4 % 128;
                if (i4 % 2 != 0) {
                    fVar = f.d;
                    int i5 = 22 / 0;
                } else {
                    fVar = f.d;
                }
                int i6 = i + 83;
                j = i6 % 128;
                switch (i6 % 2 != 0 ? '#' : ';') {
                    case ';':
                        return fVar;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 602
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.d.k(char, int, int, java.lang.Object[]):void");
    }
}
