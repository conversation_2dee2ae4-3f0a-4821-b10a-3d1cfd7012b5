package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\o6.smali */
class o6 implements r4 {
    protected final BigInteger a;

    o6(BigInteger bigInteger) {
        this.a = bigInteger;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r4
    public int b() {
        return 1;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r4
    public BigInteger c() {
        return this.a;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj instanceof o6) {
            return this.a.equals(((o6) obj).a);
        }
        return false;
    }

    public int hashCode() {
        return this.a.hashCode();
    }
}
