package com.google.firebase.encoders.proto;

import com.google.firebase.encoders.EncodingException;
import com.google.firebase.encoders.FieldDescriptor;
import com.google.firebase.encoders.ObjectEncoder;
import com.google.firebase.encoders.ObjectEncoderContext;
import com.google.firebase.encoders.ValueEncoder;
import com.google.firebase.encoders.proto.Protobuf;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\encoders\proto\ProtobufDataEncoderContext.smali */
final class ProtobufDataEncoderContext implements ObjectEncoderContext {
    private final ObjectEncoder<Object> fallbackEncoder;
    private final Map<Class<?>, ObjectEncoder<?>> objectEncoders;
    private OutputStream output;
    private final ProtobufValueEncoderContext valueEncoderContext = new ProtobufValueEncoderContext(this);
    private final Map<Class<?>, ValueEncoder<?>> valueEncoders;
    private static final Charset UTF_8 = Charset.forName("UTF-8");
    private static final FieldDescriptor MAP_KEY_DESC = FieldDescriptor.builder("key").withProperty(AtProtobuf.builder().tag(1).build()).build();
    private static final FieldDescriptor MAP_VALUE_DESC = FieldDescriptor.builder("value").withProperty(AtProtobuf.builder().tag(2).build()).build();
    private static final ObjectEncoder<Map.Entry<Object, Object>> DEFAULT_MAP_ENCODER = new ObjectEncoder() { // from class: com.google.firebase.encoders.proto.ProtobufDataEncoderContext$$ExternalSyntheticLambda0
        @Override // com.google.firebase.encoders.Encoder
        public final void encode(Object obj, ObjectEncoderContext objectEncoderContext) {
            ProtobufDataEncoderContext.lambda$static$0((Map.Entry) obj, objectEncoderContext);
        }
    };

    static /* synthetic */ void lambda$static$0(Map.Entry o2, ObjectEncoderContext ctx) throws IOException {
        ctx.add(MAP_KEY_DESC, o2.getKey());
        ctx.add(MAP_VALUE_DESC, o2.getValue());
    }

    ProtobufDataEncoderContext(OutputStream output, Map<Class<?>, ObjectEncoder<?>> objectEncoders, Map<Class<?>, ValueEncoder<?>> valueEncoders, ObjectEncoder<Object> fallbackEncoder) {
        this.output = output;
        this.objectEncoders = objectEncoders;
        this.valueEncoders = valueEncoders;
        this.fallbackEncoder = fallbackEncoder;
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(String name, Object obj) throws IOException {
        return add(FieldDescriptor.of(name), obj);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(String name, double value) throws IOException {
        return add(FieldDescriptor.of(name), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(String name, int value) throws IOException {
        return add(FieldDescriptor.of(name), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(String name, long value) throws IOException {
        return add(FieldDescriptor.of(name), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(String name, boolean value) throws IOException {
        return add(FieldDescriptor.of(name), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, Object obj) throws IOException {
        return add(field, obj, true);
    }

    ObjectEncoderContext add(FieldDescriptor field, Object obj, boolean skipDefault) throws IOException {
        if (obj == null) {
            return this;
        }
        if (obj instanceof CharSequence) {
            CharSequence seq = (CharSequence) obj;
            if (skipDefault && seq.length() == 0) {
                return this;
            }
            int tag = getTag(field);
            writeVarInt32((tag << 3) | 2);
            byte[] bytes = seq.toString().getBytes(UTF_8);
            writeVarInt32(bytes.length);
            this.output.write(bytes);
            return this;
        }
        if (obj instanceof Collection) {
            Collection<Object> collection = (Collection) obj;
            for (Object value : collection) {
                add(field, value, false);
            }
            return this;
        }
        if (obj instanceof Map) {
            Map<Object, Object> map = (Map) obj;
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                doEncode((ObjectEncoder<FieldDescriptor>) DEFAULT_MAP_ENCODER, field, (FieldDescriptor) entry, false);
            }
            return this;
        }
        if (obj instanceof Double) {
            return add(field, ((Double) obj).doubleValue(), skipDefault);
        }
        if (obj instanceof Float) {
            return add(field, ((Float) obj).floatValue(), skipDefault);
        }
        if (obj instanceof Number) {
            return add(field, ((Number) obj).longValue(), skipDefault);
        }
        if (obj instanceof Boolean) {
            return add(field, ((Boolean) obj).booleanValue(), skipDefault);
        }
        if (obj instanceof byte[]) {
            byte[] bytes2 = (byte[]) obj;
            if (skipDefault && bytes2.length == 0) {
                return this;
            }
            int tag2 = getTag(field);
            writeVarInt32((tag2 << 3) | 2);
            writeVarInt32(bytes2.length);
            this.output.write(bytes2);
            return this;
        }
        ObjectEncoder<Object> objectEncoder = this.objectEncoders.get(obj.getClass());
        if (objectEncoder != null) {
            return doEncode((ObjectEncoder<FieldDescriptor>) objectEncoder, field, (FieldDescriptor) obj, skipDefault);
        }
        ValueEncoder<Object> valueEncoder = this.valueEncoders.get(obj.getClass());
        if (valueEncoder != null) {
            return doEncode((ValueEncoder<FieldDescriptor>) valueEncoder, field, (FieldDescriptor) obj, skipDefault);
        }
        if (obj instanceof ProtoEnum) {
            return add(field, ((ProtoEnum) obj).getNumber());
        }
        if (obj instanceof Enum) {
            return add(field, ((Enum) obj).ordinal());
        }
        return doEncode((ObjectEncoder<FieldDescriptor>) this.fallbackEncoder, field, (FieldDescriptor) obj, skipDefault);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, double value) throws IOException {
        return add(field, value, true);
    }

    ObjectEncoderContext add(FieldDescriptor field, double value, boolean skipDefault) throws IOException {
        if (skipDefault && value == 0.0d) {
            return this;
        }
        int tag = getTag(field);
        writeVarInt32((tag << 3) | 1);
        this.output.write(allocateBuffer(8).putDouble(value).array());
        return this;
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, float value) throws IOException {
        return add(field, value, true);
    }

    ObjectEncoderContext add(FieldDescriptor field, float value, boolean skipDefault) throws IOException {
        if (skipDefault && value == 0.0f) {
            return this;
        }
        int tag = getTag(field);
        writeVarInt32((tag << 3) | 5);
        this.output.write(allocateBuffer(4).putFloat(value).array());
        return this;
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ProtobufDataEncoderContext add(FieldDescriptor field, int value) throws IOException {
        return add(field, value, true);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:10:0x0056, code lost:
    
        return r3;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    com.google.firebase.encoders.proto.ProtobufDataEncoderContext add(com.google.firebase.encoders.FieldDescriptor r4, int r5, boolean r6) throws java.io.IOException {
        /*
            r3 = this;
            if (r6 == 0) goto L5
            if (r5 != 0) goto L5
            return r3
        L5:
            com.google.firebase.encoders.proto.Protobuf r0 = getProtobuf(r4)
            int[] r1 = com.google.firebase.encoders.proto.ProtobufDataEncoderContext.AnonymousClass1.$SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding
            com.google.firebase.encoders.proto.Protobuf$IntEncoding r2 = r0.intEncoding()
            int r2 = r2.ordinal()
            r1 = r1[r2]
            switch(r1) {
                case 1: goto L49;
                case 2: goto L37;
                case 3: goto L19;
                default: goto L18;
            }
        L18:
            goto L56
        L19:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r1 = r1 | 5
            r3.writeVarInt32(r1)
            java.io.OutputStream r1 = r3.output
            r2 = 4
            java.nio.ByteBuffer r2 = allocateBuffer(r2)
            java.nio.ByteBuffer r2 = r2.putInt(r5)
            byte[] r2 = r2.array()
            r1.write(r2)
            goto L56
        L37:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r3.writeVarInt32(r1)
            int r1 = r5 << 1
            int r2 = r5 >> 31
            r1 = r1 ^ r2
            r3.writeVarInt32(r1)
            goto L56
        L49:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r3.writeVarInt32(r1)
            r3.writeVarInt32(r5)
        L56:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: com.google.firebase.encoders.proto.ProtobufDataEncoderContext.add(com.google.firebase.encoders.FieldDescriptor, int, boolean):com.google.firebase.encoders.proto.ProtobufDataEncoderContext");
    }

    /* renamed from: com.google.firebase.encoders.proto.ProtobufDataEncoderContext$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\encoders\proto\ProtobufDataEncoderContext$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding;

        static {
            int[] iArr = new int[Protobuf.IntEncoding.values().length];
            $SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding = iArr;
            try {
                iArr[Protobuf.IntEncoding.DEFAULT.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding[Protobuf.IntEncoding.SIGNED.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding[Protobuf.IntEncoding.FIXED.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ProtobufDataEncoderContext add(FieldDescriptor field, long value) throws IOException {
        return add(field, value, true);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x005d, code lost:
    
        return r5;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    com.google.firebase.encoders.proto.ProtobufDataEncoderContext add(com.google.firebase.encoders.FieldDescriptor r6, long r7, boolean r9) throws java.io.IOException {
        /*
            r5 = this;
            if (r9 == 0) goto L9
            r0 = 0
            int r0 = (r7 > r0 ? 1 : (r7 == r0 ? 0 : -1))
            if (r0 != 0) goto L9
            return r5
        L9:
            com.google.firebase.encoders.proto.Protobuf r0 = getProtobuf(r6)
            int[] r1 = com.google.firebase.encoders.proto.ProtobufDataEncoderContext.AnonymousClass1.$SwitchMap$com$google$firebase$encoders$proto$Protobuf$IntEncoding
            com.google.firebase.encoders.proto.Protobuf$IntEncoding r2 = r0.intEncoding()
            int r2 = r2.ordinal()
            r1 = r1[r2]
            r2 = 1
            switch(r1) {
                case 1: goto L50;
                case 2: goto L3c;
                case 3: goto L1e;
                default: goto L1d;
            }
        L1d:
            goto L5d
        L1e:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r1 = r1 | r2
            r5.writeVarInt32(r1)
            java.io.OutputStream r1 = r5.output
            r2 = 8
            java.nio.ByteBuffer r2 = allocateBuffer(r2)
            java.nio.ByteBuffer r2 = r2.putLong(r7)
            byte[] r2 = r2.array()
            r1.write(r2)
            goto L5d
        L3c:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r5.writeVarInt32(r1)
            long r1 = r7 << r2
            r3 = 63
            long r3 = r7 >> r3
            long r1 = r1 ^ r3
            r5.writeVarInt64(r1)
            goto L5d
        L50:
            int r1 = r0.tag()
            int r1 = r1 << 3
            r5.writeVarInt32(r1)
            r5.writeVarInt64(r7)
        L5d:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: com.google.firebase.encoders.proto.ProtobufDataEncoderContext.add(com.google.firebase.encoders.FieldDescriptor, long, boolean):com.google.firebase.encoders.proto.ProtobufDataEncoderContext");
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ProtobufDataEncoderContext add(FieldDescriptor field, boolean value) throws IOException {
        return add(field, value, true);
    }

    ProtobufDataEncoderContext add(FieldDescriptor fieldDescriptor, boolean z, boolean z2) throws IOException {
        return add(fieldDescriptor, z ? 1 : 0, z2);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext inline(Object value) throws IOException {
        return encode(value);
    }

    ProtobufDataEncoderContext encode(Object value) throws IOException {
        if (value == null) {
            return this;
        }
        ObjectEncoder<Object> objectEncoder = this.objectEncoders.get(value.getClass());
        if (objectEncoder != null) {
            objectEncoder.encode(value, this);
            return this;
        }
        throw new EncodingException("No encoder for " + value.getClass());
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext nested(String name) throws IOException {
        return nested(FieldDescriptor.of(name));
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext nested(FieldDescriptor field) throws IOException {
        throw new EncodingException("nested() is not implemented for protobuf encoding.");
    }

    private <T> ProtobufDataEncoderContext doEncode(ObjectEncoder<T> encoder, FieldDescriptor field, T obj, boolean skipDefault) throws IOException {
        long size = determineSize(encoder, obj);
        if (skipDefault && size == 0) {
            return this;
        }
        int tag = getTag(field);
        writeVarInt32((tag << 3) | 2);
        writeVarInt64(size);
        encoder.encode(obj, this);
        return this;
    }

    private <T> long determineSize(ObjectEncoder<T> encoder, T obj) throws IOException {
        LengthCountingOutputStream out = new LengthCountingOutputStream();
        try {
            OutputStream originalStream = this.output;
            this.output = out;
            try {
                encoder.encode(obj, this);
                this.output = originalStream;
                long length = out.getLength();
                out.close();
                return length;
            } catch (Throwable th) {
                this.output = originalStream;
                throw th;
            }
        } catch (Throwable th2) {
            try {
                out.close();
            } catch (Throwable th3) {
                th2.addSuppressed(th3);
            }
            throw th2;
        }
    }

    private <T> ProtobufDataEncoderContext doEncode(ValueEncoder<T> encoder, FieldDescriptor field, T obj, boolean skipDefault) throws IOException {
        this.valueEncoderContext.resetContext(field, skipDefault);
        encoder.encode(obj, this.valueEncoderContext);
        return this;
    }

    private static ByteBuffer allocateBuffer(int length) {
        return ByteBuffer.allocate(length).order(ByteOrder.LITTLE_ENDIAN);
    }

    private static int getTag(FieldDescriptor field) {
        Protobuf protobuf = (Protobuf) field.getProperty(Protobuf.class);
        if (protobuf == null) {
            throw new EncodingException("Field has no @Protobuf config");
        }
        return protobuf.tag();
    }

    private static Protobuf getProtobuf(FieldDescriptor field) {
        Protobuf protobuf = (Protobuf) field.getProperty(Protobuf.class);
        if (protobuf == null) {
            throw new EncodingException("Field has no @Protobuf config");
        }
        return protobuf;
    }

    private void writeVarInt32(int value) throws IOException {
        while ((value & (-128)) != 0) {
            this.output.write((value & 127) | 128);
            value >>>= 7;
        }
        this.output.write(value & 127);
    }

    private void writeVarInt64(long value) throws IOException {
        while (((-128) & value) != 0) {
            this.output.write((((int) value) & 127) | 128);
            value >>>= 7;
        }
        this.output.write(((int) value) & 127);
    }
}
