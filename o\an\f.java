package o.an;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import o.an.a;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\f.smali */
public final class f<TRRequest, TRResponse, TR extends a<TRRequest, TRResponse>> extends o.y.b<b<TRResponse>> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static int h;
    private static int j;
    final TR a;
    String b;
    o.h.d c;
    String d;
    TRResponse e;
    o.ee.h i;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\f$b.smali */
    public interface b<TRResponse> {
        void c(TRResponse trresponse);

        void c(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        m();
        TextUtils.getCapsMode("", 0, 0);
        TypedValue.complexToFloat(0);
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getKeyRepeatTimeout();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ExpandableListView.getPackedPositionForGroup(0);
        int i = h + 67;
        f = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{7, -109, -85, 32};
        $$e = Opcodes.IF_ICMPGT;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = 109 - r7
            int r9 = r9 * 2
            int r9 = 1 - r9
            int r8 = r8 + 4
            byte[] r0 = o.an.f.$$d
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            int r8 = r8 + 1
            if (r4 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r8 = -r8
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.f.l(int, byte, byte, java.lang.Object[]):void");
    }

    static void m() {
        j = 874635266;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = f + 23;
        h = i % 128;
        int i2 = i % 2;
        d<TRRequest, TRResponse> n = n();
        int i3 = h + 31;
        f = i3 % 128;
        int i4 = i3 % 2;
        return n;
    }

    public f(Context context, b<TRResponse> bVar, o.ei.c cVar, TR tr) {
        super(context, bVar, cVar, o.bb.e.v);
        this.a = tr;
    }

    public final void a(o.h.d dVar, String str, String str2, o.ee.h hVar) {
        int i = f + 15;
        h = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(1 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), "￭\u0001\u000b\ufffe\n\n\f￠\u0001\u000f\ufffe￠\u0005\u0010\u0012", 15 - (Process.myPid() >> 22), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + BERTags.FLAGS, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(6 - TextUtils.indexOf((CharSequence) "", '0', 0), "\uffdf\u0004\u000f\u0011￬\u000b\u0000\u0000\u000e�", TextUtils.indexOf("", "", 0, 0) + 10, View.resolveSize(0, 0) + 225, true, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.d = str;
        this.c = dVar;
        this.b = str2;
        this.i = hVar;
        c();
        int i3 = f + 79;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    private d<TRRequest, TRResponse> n() {
        d<TRRequest, TRResponse> dVar = new d<>(this);
        int i = f + 5;
        h = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    @Override // o.y.b
    public final String a() {
        int i = f + 91;
        h = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "￭\u0001\u000b\ufffe\n\n\f￠\u0001\u000f\ufffe￠\u0005\u0010\u0012", 15 - (ViewConfiguration.getWindowTouchSlop() >> 8), 224 - Color.green(0), true, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = h + 69;
        f = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\f$d.smali */
    static final class d<TRRequest, TRResponse> extends o.y.c<f<TRRequest, TRResponse, ?>> {
        private static int $10 = 0;
        private static int $11 = 1;
        private static int c = 0;
        private static int f = 1;
        private static char a = 22010;
        private static char d = 40856;
        private static char e = 34460;
        private static char b = 62808;

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = f + Opcodes.LSUB;
            c = i % 128;
            switch (i % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        d(f<TRRequest, TRResponse, ?> fVar) {
            super(fVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = c + 1;
            f = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("\ufe1c䔇䁿⣙暝䋱쨘董䯢镼\ueeaf覫䓍쪭ᡨ\ueef0䮴\uf865\uec77萅乜\uee3a", ExpandableListView.getPackedPositionGroup(0L) + 22, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = f + 75;
            c = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("ꑭ䯗\uf7e4川茾⟴ࡧ哪佫牯ꥼ篞앪韉\ueff7ዜ钞脪闭喋", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 18, objArr);
            o.cf.d dVar = new o.cf.d(context, 32, ((String) objArr[0]).intern());
            int i = f + Opcodes.DDIV;
            c = i % 128;
            switch (i % 2 != 0 ? 'B' : (char) 3) {
                case 3:
                    return dVar;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("圷粑쨘董碲洅", 6 - Color.blue(0), objArr);
            bVar.d(((String) objArr[0]).intern(), ((f) e()).b);
            try {
                TR tr = ((f) e()).a;
                g();
                tr.b(bVar);
                int i = c + 89;
                f = i % 128;
                if (i % 2 == 0) {
                }
            } catch (a.C0022a e2) {
                if (e2.c().c() == AntelopErrorCode.GooglePayWalletNotAvailable) {
                    h().c(o.bb.a.aD);
                    int i2 = c + Opcodes.LREM;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                } else {
                    h().c(o.bb.a.c);
                }
            }
            switch (((f) e()).i != null) {
                case true:
                    o.eg.b a2 = o.an.d.a(((f) e()).i);
                    Object[] objArr2 = new Object[1];
                    w("ৎ컗\uec77萅ꌻ諴髌궦걱̭깝킻\uec6cⳖ", ImageFormat.getBitsPerPixel(0) + 15, objArr2);
                    bVar.d(((String) objArr2[0]).intern(), a2);
                default:
                    return bVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            j jVar = new j(((f) e()).d, true, ((f) e()).c);
            int i = c + 37;
            f = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = c + Opcodes.DREM;
            int i2 = i % 128;
            f = i2;
            int i3 = i % 2;
            int i4 = i2 + 25;
            c = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = c + Opcodes.LUSHR;
            f = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i);
                    int i4 = c + 91;
                    f = i4 % 128;
                    switch (i4 % 2 == 0 ? 'S' : (char) 3) {
                        case Opcodes.AASTORE /* 83 */:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return c2;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = f + 57;
            c = i % 128;
            Object obj = null;
            switch (i % 2 != 0) {
                case false:
                    ((f) e()).e = (TRResponse) ((f) e()).a.a(bVar);
                    int i2 = c + 59;
                    f = i2 % 128;
                    switch (i2 % 2 == 0) {
                        case true:
                            obj.hashCode();
                            throw null;
                        default:
                            return;
                    }
                default:
                    ((f) e()).e = (TRResponse) ((f) e()).a.a(bVar);
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = c + 67;
            f = i % 128;
            switch (i % 2 == 0 ? (char) 24 : (char) 3) {
                case 24:
                    ((f) e()).j().c((b<TRResponse>) ((f) e()).e);
                    int i2 = 77 / 0;
                    break;
                default:
                    ((f) e()).j().c((b<TRResponse>) ((f) e()).e);
                    break;
            }
            int i3 = f + Opcodes.LSHL;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = c + 23;
            f = i % 128;
            int i2 = i % 2;
            ((f) e()).j().c(dVar);
            int i3 = c + Opcodes.LSHL;
            f = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 612
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.an.f.d.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 524
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.f.k(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
