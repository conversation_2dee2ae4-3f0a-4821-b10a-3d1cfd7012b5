package com.google.android.gms.internal.auth;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfg.smali */
final class zzfg extends zzfk {
    private static final Class zza = Collections.unmodifiableList(Collections.emptyList()).getClass();

    private zzfg() {
        super(null);
    }

    /* synthetic */ zzfg(zzff zzffVar) {
        super(null);
    }

    @Override // com.google.android.gms.internal.auth.zzfk
    final void zza(Object obj, long j) {
        Object unmodifiableList;
        List list = (List) zzhi.zzf(obj, j);
        if (list instanceof zzfe) {
            unmodifiableList = ((zzfe) list).zze();
        } else {
            if (zza.isAssignableFrom(list.getClass())) {
                return;
            }
            if ((list instanceof zzgd) && (list instanceof zzey)) {
                zzey zzeyVar = (zzey) list;
                if (zzeyVar.zzc()) {
                    zzeyVar.zzb();
                    return;
                }
                return;
            }
            unmodifiableList = Collections.unmodifiableList(list);
        }
        zzhi.zzp(obj, j, unmodifiableList);
    }

    @Override // com.google.android.gms.internal.auth.zzfk
    final void zzb(Object obj, Object obj2, long j) {
        List list = (List) zzhi.zzf(obj2, j);
        int size = list.size();
        List list2 = (List) zzhi.zzf(obj, j);
        if (list2.isEmpty()) {
            list2 = list2 instanceof zzfe ? new zzfd(size) : ((list2 instanceof zzgd) && (list2 instanceof zzey)) ? ((zzey) list2).zzd(size) : new ArrayList(size);
            zzhi.zzp(obj, j, list2);
        } else if (zza.isAssignableFrom(list2.getClass())) {
            ArrayList arrayList = new ArrayList(list2.size() + size);
            arrayList.addAll(list2);
            zzhi.zzp(obj, j, arrayList);
            list2 = arrayList;
        } else if (list2 instanceof zzhd) {
            zzfd zzfdVar = new zzfd(list2.size() + size);
            zzfdVar.addAll(zzfdVar.size(), (zzhd) list2);
            zzhi.zzp(obj, j, zzfdVar);
            list2 = zzfdVar;
        } else if ((list2 instanceof zzgd) && (list2 instanceof zzey)) {
            zzey zzeyVar = (zzey) list2;
            if (!zzeyVar.zzc()) {
                list2 = zzeyVar.zzd(list2.size() + size);
                zzhi.zzp(obj, j, list2);
            }
        }
        int size2 = list2.size();
        int size3 = list.size();
        if (size2 > 0 && size3 > 0) {
            list2.addAll(list);
        }
        if (size2 > 0) {
            list = list2;
        }
        zzhi.zzp(obj, j, list);
    }
}
