package o.ca;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicReference;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.i;
import o.a.j;
import o.ab.a;
import o.ac.d;
import o.aw.c;
import o.bb.d;
import o.ee.g;
import o.ei.c;
import o.y.e;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ca\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static int b;
    private static char[] d;
    private static boolean e;
    private static int f;
    private static short[] g;
    private static int h;
    private static byte[] i;
    private static int j;
    private static int k;

    /* renamed from: o, reason: collision with root package name */
    private static int f45o;
    final c c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f45o = 0;
        k = 1;
        b();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        int i2 = f45o + 17;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                break;
            default:
                int i3 = 83 / 0;
                break;
        }
    }

    static void b() {
        d = new char[]{61772, 61806, 61821, 61811, 61794, 61823, 61571, 61810, 61786, 61817, 61808, 61816, 61575, 61792, 61819, 61820, 61804, 61788, 61815, 61570, 61805, 61818, 61814, 61774, 61569};
        a = true;
        e = true;
        b = 782102799;
        i = new byte[]{98, -127, -114, 78, 97, -97, 105, -105, -111, 98, -127, -114, 111, 99, -103, 100, -108, -100, 99, -62, 34, 111, -106, 101, -127, 110, 103, -112, 111, -100, -45, 100, 85, 105, -107, 107, -112, 105, -109, -62, 60, -107, -111, -34, Base64.padSymbol, -102, -39, 60, -97, 105, -112, -101, -102, 98, -127, -114, 78, 97, -125, 108, 99, -97, -99, -111, 104, -109, 100, -93, 94, -111, -98, 96, -108, -100, -125, 126, 98, -127, -114, 78, 97, -125, 108, 99, -65, -107, 111, -106, 101, -101, -107, 102, 111, PSSSigner.TRAILER_IMPLICIT, 79, 102, -67, 94, -106, 107, -99, -127, ByteCompanionObject.MAX_VALUE, ByteCompanionObject.MAX_VALUE, -105, 109, -110, -112, -97, -112, -112, -112, -112, -112, -112};
        h = 909053671;
        f = -1528460707;
        j = -668890505;
    }

    static void init$0() {
        $$a = new byte[]{96, 104, -93, 9};
        $$b = Opcodes.DCMPG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            byte[] r0 = o.ca.a.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = r6 + 108
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L16:
            r3 = r2
        L17:
            r5 = r7
            r7 = r6
            r6 = r5
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            int r6 = r6 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ca.a.n(byte, short, int, java.lang.Object[]):void");
    }

    public a(c cVar) {
        this.c = cVar;
    }

    public final void b(Context context, String str, final b bVar) throws WalletValidationException {
        int i2 = k + 95;
        f45o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        l(null, (ViewConfiguration.getTapTimeout() >> 16) + 127, null, "\u0083\u0088\u008b\u0082\u008a\u0082\u0089\u0088\u0087\u0082\u0084\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((byte) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 301038362, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), Color.red(0) - 109, (ViewConfiguration.getEdgeSlop() >> 16) + 1832221591, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            l(null, Color.argb(0, 0, 0, 0) + 127, null, "\u0087\u008d\u0088\u0087\u008a\u008c\u0081", objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern(), "");
        }
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
            Object[] objArr4 = new Object[1];
            m((byte) ((-1) - TextUtils.lastIndexOf("", '0')), 301038371 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), 65469 - AndroidCharacter.getMirror('0'), TextUtils.indexOf("", "") + 1832221558, objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        if (!this.c.q()) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr5 = new Object[1];
            l(null, 127 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), null, "\u0087\u0088\u008f\u008f\u0082\u008e", objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            m((byte) TextUtils.getCapsMode("", 0, 0), 301038373 - (ViewConfiguration.getEdgeSlop() >> 16), (short) TextUtils.indexOf("", "", 0, 0), (-77) - View.MeasureSpec.makeMeasureSpec(0, 0), 1832221578 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr6);
            throw new WalletValidationException(walletValidationErrorCode3, intern2, ((String) objArr6[0]).intern());
        }
        if (!this.c.a().j().containsKey(str)) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.Unknown;
            Object[] objArr7 = new Object[1];
            m((byte) (ViewConfiguration.getLongPressTimeout() >> 16), 301038370 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), TextUtils.indexOf((CharSequence) "", '0') - 114, 1832221557 - TextUtils.indexOf((CharSequence) "", '0'), objArr7);
            throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr7[0]).intern());
        }
        new o.ab.a(context, new a.b() { // from class: o.ca.a.5
            private static int $10 = 0;
            private static int $11 = 1;
            private static int i = 0;
            private static int j = 1;
            private static char b = 12700;
            private static char d = 13107;
            private static char g = 18380;
            private static char e = 47851;

            @Override // o.ab.a.b
            public final void b() {
                g.c();
                Object[] objArr8 = new Object[1];
                f("뮢庾끭ָꬡ쳲⍴넧砘楑䙚䠿丢蚛\uf556략◷饰", 17 - View.getDefaultSize(0, 0), objArr8);
                String intern3 = ((String) objArr8[0]).intern();
                Object[] objArr9 = new Object[1];
                f("\uda8b춘멜ᾙⰁ楰砘楑뮢庾끭ָ㴘លꝞ㺀벜ቕ\ueda4뗱", AndroidCharacter.getMirror('0') - 29, objArr9);
                g.d(intern3, ((String) objArr9[0]).intern());
                b bVar2 = bVar;
                switch (bVar2 != null ? 'D' : '2') {
                    case 'D':
                        int i4 = i + Opcodes.DMUL;
                        j = i4 % 128;
                        boolean z = i4 % 2 != 0;
                        bVar2.d();
                        switch (z) {
                            case false:
                                int i5 = 61 / 0;
                                break;
                        }
                }
                int i6 = i + 15;
                j = i6 % 128;
                switch (i6 % 2 == 0) {
                    case true:
                        int i7 = 59 / 0;
                        return;
                    default:
                        return;
                }
            }

            @Override // o.ab.a.b
            public final void a(d dVar) {
                boolean z;
                int i4 = i + 93;
                j = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Object[] objArr8 = new Object[1];
                f("뮢庾끭ָꬡ쳲⍴넧砘楑䙚䠿丢蚛\uf556략◷饰", View.getDefaultSize(0, 0) + 17, objArr8);
                String intern3 = ((String) objArr8[0]).intern();
                Object[] objArr9 = new Object[1];
                f("\uda8b춘멜ᾙⰁ楰砘楑뮢庾끭ָ⭔\uf50b\ufb37燸\uecbb嵃䀹檄", (Process.myTid() >> 22) + 19, objArr9);
                g.d(intern3, ((String) objArr9[0]).intern());
                b bVar2 = bVar;
                if (bVar2 == null) {
                    z = false;
                } else {
                    z = true;
                }
                switch (z) {
                    case false:
                        break;
                    default:
                        int i6 = i + 37;
                        j = i6 % 128;
                        boolean z2 = i6 % 2 != 0;
                        bVar2.d(dVar);
                        switch (z2) {
                            case false:
                                int i7 = 6 / 0;
                                break;
                        }
                }
                int i8 = j + 17;
                i = i8 % 128;
                switch (i8 % 2 != 0 ? '@' : '4') {
                    case '4':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:13:0x0065, code lost:
            
                r7 = 58224;
                r9 = r5;
             */
            /* JADX WARN: Code restructure failed: missing block: B:15:0x006d, code lost:
            
                if (r9 >= 16) goto L75;
             */
            /* JADX WARN: Code restructure failed: missing block: B:16:0x006f, code lost:
            
                r11 = r6[r8];
                r12 = r6[r5];
                r16 = r6;
                r5 = (r12 + r7) ^ ((r12 << 4) + ((char) (o.ca.a.AnonymousClass5.d ^ 8439748517800462901L)));
                r6 = r12 >>> 5;
             */
            /* JADX WARN: Code restructure failed: missing block: B:18:0x008d, code lost:
            
                r14 = new java.lang.Object[4];
                r14[3] = java.lang.Integer.valueOf(o.ca.a.AnonymousClass5.g);
                r14[2] = java.lang.Integer.valueOf(r6);
                r14[r8] = java.lang.Integer.valueOf(r5);
                r14[0] = java.lang.Integer.valueOf(r11);
                r5 = o.e.a.s.get(-1512468642);
             */
            /* JADX WARN: Code restructure failed: missing block: B:20:0x00ba, code lost:
            
                if (r5 == null) goto L32;
             */
            /* JADX WARN: Code restructure failed: missing block: B:22:0x00be, code lost:
            
                r1 = (java.lang.Class) o.e.a.c(10 - android.text.TextUtils.lastIndexOf("", '0', 0, 0), (char) (android.os.Process.myTid() >> 22), 603 - android.view.Gravity.getAbsoluteGravity(0, 0));
                r5 = new java.lang.Class[4];
                r5[0] = java.lang.Integer.TYPE;
                r5[r8] = java.lang.Integer.TYPE;
                r5[2] = java.lang.Integer.TYPE;
                r5[3] = java.lang.Integer.TYPE;
                r5 = r1.getMethod("C", r5);
                o.e.a.s.put(-1512468642, r5);
             */
            /* JADX WARN: Code restructure failed: missing block: B:23:0x00fb, code lost:
            
                r1 = ((java.lang.Character) ((java.lang.reflect.Method) r5).invoke(null, r14)).charValue();
             */
            /* JADX WARN: Code restructure failed: missing block: B:24:0x0108, code lost:
            
                r16[r8] = r1;
                r20 = r9;
             */
            /* JADX WARN: Code restructure failed: missing block: B:26:0x0120, code lost:
            
                r9 = new java.lang.Object[]{java.lang.Integer.valueOf(r16[0]), java.lang.Integer.valueOf((r1 + r7) ^ ((r1 << 4) + ((char) (o.ca.a.AnonymousClass5.e ^ 8439748517800462901L)))), java.lang.Integer.valueOf(r1 >>> 5), java.lang.Integer.valueOf(o.ca.a.AnonymousClass5.b)};
                r1 = o.e.a.s.get(-1512468642);
             */
            /* JADX WARN: Code restructure failed: missing block: B:27:0x0149, code lost:
            
                if (r1 == null) goto L39;
             */
            /* JADX WARN: Code restructure failed: missing block: B:30:0x0196, code lost:
            
                r16[0] = ((java.lang.Character) ((java.lang.reflect.Method) r1).invoke(null, r9)).charValue();
                r7 = r7 - 40503;
                r9 = r20 + 1;
                r6 = r16;
                r5 = 0;
                r8 = 1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:31:0x014c, code lost:
            
                r1 = ((java.lang.Class) o.e.a.c(android.view.KeyEvent.normalizeMetaState(0) + 11, (char) android.view.View.MeasureSpec.getSize(0), android.text.TextUtils.getTrimmedLength("") + 603)).getMethod("C", java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
                o.e.a.s.put(-1512468642, r1);
             */
            /* JADX WARN: Code restructure failed: missing block: B:33:0x01a6, code lost:
            
                r0 = move-exception;
             */
            /* JADX WARN: Code restructure failed: missing block: B:34:0x01a7, code lost:
            
                r1 = r0.getCause();
             */
            /* JADX WARN: Code restructure failed: missing block: B:35:0x01ab, code lost:
            
                if (r1 != null) goto L45;
             */
            /* JADX WARN: Code restructure failed: missing block: B:36:0x01ad, code lost:
            
                throw r1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:38:0x01ae, code lost:
            
                throw r0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:40:0x01af, code lost:
            
                r0 = move-exception;
             */
            /* JADX WARN: Code restructure failed: missing block: B:41:0x01b0, code lost:
            
                r1 = r0.getCause();
             */
            /* JADX WARN: Code restructure failed: missing block: B:42:0x01b4, code lost:
            
                if (r1 != null) goto L50;
             */
            /* JADX WARN: Code restructure failed: missing block: B:43:0x01b6, code lost:
            
                throw r1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:44:0x01b7, code lost:
            
                throw r0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:46:0x01b8, code lost:
            
                r16 = r6;
                r4[r3.b] = r16[0];
                r4[r3.b + 1] = r16[1];
             */
            /* JADX WARN: Code restructure failed: missing block: B:48:0x01c9, code lost:
            
                r1 = new java.lang.Object[]{r3, r3};
                r5 = o.e.a.s.get(2062727845);
             */
            /* JADX WARN: Code restructure failed: missing block: B:49:0x01da, code lost:
            
                if (r5 == null) goto L56;
             */
            /* JADX WARN: Code restructure failed: missing block: B:51:0x0214, code lost:
            
                ((java.lang.reflect.Method) r5).invoke(null, r1);
             */
            /* JADX WARN: Code restructure failed: missing block: B:52:0x021a, code lost:
            
                r6 = r16;
                r5 = 0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:53:0x01dd, code lost:
            
                r5 = ((java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getDoubleTapTimeout() >> 16) + 10, (char) (android.text.AndroidCharacter.getMirror('0') + 30677), (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16) + 614)).getMethod("A", java.lang.Object.class, java.lang.Object.class);
                o.e.a.s.put(2062727845, r5);
             */
            /* JADX WARN: Code restructure failed: missing block: B:55:0x0220, code lost:
            
                r0 = move-exception;
             */
            /* JADX WARN: Code restructure failed: missing block: B:56:0x0221, code lost:
            
                r1 = r0.getCause();
             */
            /* JADX WARN: Code restructure failed: missing block: B:57:0x0225, code lost:
            
                if (r1 != null) goto L62;
             */
            /* JADX WARN: Code restructure failed: missing block: B:58:0x0227, code lost:
            
                throw r1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:59:0x0228, code lost:
            
                throw r0;
             */
            /* JADX WARN: Removed duplicated region for block: B:9:0x003b  */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(java.lang.String r21, int r22, java.lang.Object[] r23) {
                /*
                    Method dump skipped, instructions count: 576
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.ca.a.AnonymousClass5.f(java.lang.String, int, java.lang.Object[]):void");
            }
        }, this.c).e(str);
        int i4 = f45o + 21;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void b(final Context context, final o.ac.a aVar, final b bVar, final o.ei.a aVar2) throws WalletValidationException {
        int i2 = k + 29;
        f45o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        l(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "\u0083\u0088\u008b\u0082\u008a\u0082\u0089\u0088\u0087\u0082\u0084\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((byte) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 301038414 - ((Process.getThreadPriority(0) + 20) >> 6), (short) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (-109) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), ((Process.getThreadPriority(0) + 20) >> 6) + 1832221590, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            l(null, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0087\u008d\u0088\u0087\u008a\u008c\u0081", objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (!this.c.q()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            l(null, 127 - View.MeasureSpec.makeMeasureSpec(0, 0), null, "\u0087\u0088\u008f\u008f\u0082\u008e", objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            m((byte) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 301038373 - TextUtils.getOffsetBefore("", 0), (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (KeyEvent.getMaxKeyCode() >> 16) - 77, 1832221579 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr5);
            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
        }
        final AtomicReference atomicReference = new AtomicReference(Boolean.FALSE);
        try {
            new o.ac.d(context, new d.b() { // from class: o.ca.a.2
                public static final byte[] $$a = null;
                public static final int $$b = 0;
                private static int $10;
                private static int $11;
                private static int[] f;
                private static int i;
                private static int j;

                static {
                    init$0();
                    $10 = 0;
                    $11 = 1;
                    i = 0;
                    j = 1;
                    f = new int[]{-586800203, 371962895, 300531893, 222120841, 1841263570, -166421638, -2116949209, 1233273869, 1240617820, -2051155209, 1268756534, 1814761382, -1409939301, -1141457925, 719394157, -2046518342, -1444590049, -949006774};
                }

                static void init$0() {
                    $$a = new byte[]{79, Tnaf.POW_2_WIDTH, 60, 65};
                    $$b = 254;
                }

                /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
                /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
                /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                private static void k(int r6, short r7, int r8, java.lang.Object[] r9) {
                    /*
                        int r6 = 116 - r6
                        int r8 = r8 + 4
                        byte[] r0 = o.ca.a.AnonymousClass2.$$a
                        int r7 = r7 * 2
                        int r7 = 1 - r7
                        byte[] r1 = new byte[r7]
                        int r7 = r7 + (-1)
                        r2 = 0
                        if (r0 != 0) goto L18
                        r6 = r7
                        r3 = r1
                        r4 = r2
                        r1 = r0
                        r0 = r9
                        r9 = r8
                        goto L35
                    L18:
                        r3 = r2
                    L19:
                        byte r4 = (byte) r6
                        r1[r3] = r4
                        int r8 = r8 + 1
                        if (r3 != r7) goto L28
                        java.lang.String r6 = new java.lang.String
                        r6.<init>(r1, r2)
                        r9[r2] = r6
                        return
                    L28:
                        int r3 = r3 + 1
                        r4 = r0[r8]
                        r5 = r8
                        r8 = r6
                        r6 = r7
                        r7 = r4
                        r4 = r3
                        r3 = r1
                        r1 = r0
                        r0 = r9
                        r9 = r5
                    L35:
                        int r7 = r7 + r8
                        r8 = r9
                        r9 = r0
                        r0 = r1
                        r1 = r3
                        r3 = r4
                        r5 = r7
                        r7 = r6
                        r6 = r5
                        goto L19
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.ca.a.AnonymousClass2.k(int, short, int, java.lang.Object[]):void");
                }

                @Override // o.ac.d.b
                public final void a() {
                    int i4 = i + 17;
                    j = i4 % 128;
                    int i5 = i4 % 2;
                    g.c();
                    Object[] objArr6 = new Object[1];
                    g(new int[]{118999165, 342938506, 1407803248, 868034319, -2040389548, -616411991, 1178121412, -386888988, 578241720, -1467580272}, 17 - (KeyEvent.getMaxKeyCode() >> 16), objArr6);
                    String intern3 = ((String) objArr6[0]).intern();
                    Object[] objArr7 = new Object[1];
                    g(new int[]{2059772918, 1546803695, 295980004, 197355967, 118999165, 342938506, 1848301863, 1422612906, -1584593313, -1222356759}, 20 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr7);
                    g.d(intern3, ((String) objArr7[0]).intern());
                    b bVar2 = bVar;
                    switch (bVar2 != null ? '\f' : 'L') {
                        case '\f':
                            bVar2.d();
                            int i6 = j + 67;
                            i = i6 % 128;
                            int i7 = i6 % 2;
                            break;
                    }
                }

                /* JADX WARN: Code restructure failed: missing block: B:4:0x004d, code lost:
                
                    if (r10.d() == o.bb.a.at) goto L13;
                 */
                @Override // o.ac.d.b
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public final void b(o.bb.d r10) {
                    /*
                        Method dump skipped, instructions count: 656
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.ca.a.AnonymousClass2.b(o.bb.d):void");
                }

                private static void g(int[] iArr, int i4, Object[] objArr6) {
                    char[] cArr;
                    int length;
                    int[] iArr2;
                    int i5;
                    char[] cArr2;
                    int i6;
                    o.a.g gVar = new o.a.g();
                    char[] cArr3 = new char[4];
                    char[] cArr4 = new char[iArr.length * 2];
                    int[] iArr3 = f;
                    int i7 = -1667374059;
                    int i8 = 16;
                    int i9 = 1;
                    int i10 = 0;
                    switch (iArr3 != null ? (char) 14 : 'N') {
                        case 14:
                            int length2 = iArr3.length;
                            int[] iArr4 = new int[length2];
                            int i11 = 0;
                            while (true) {
                                switch (i11 < length2 ? (char) 20 : (char) 22) {
                                    case 22:
                                        iArr3 = iArr4;
                                        break;
                                    default:
                                        try {
                                            Object[] objArr7 = new Object[1];
                                            objArr7[i10] = Integer.valueOf(iArr3[i11]);
                                            Object obj = o.e.a.s.get(Integer.valueOf(i7));
                                            if (obj == null) {
                                                Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> i8) + 10, (char) (8856 - Color.blue(i10)), 323 - TextUtils.indexOf((CharSequence) "", '0'));
                                                byte b2 = (byte) i10;
                                                byte b3 = b2;
                                                Object[] objArr8 = new Object[1];
                                                k(b2, b3, (byte) (b3 - 1), objArr8);
                                                obj = cls.getMethod((String) objArr8[0], Integer.TYPE);
                                                o.e.a.s.put(-1667374059, obj);
                                            }
                                            iArr4[i11] = ((Integer) ((Method) obj).invoke(null, objArr7)).intValue();
                                            i11++;
                                            i7 = -1667374059;
                                            i8 = 16;
                                            i10 = 0;
                                        } catch (Throwable th) {
                                            Throwable cause = th.getCause();
                                            if (cause == null) {
                                                throw th;
                                            }
                                            throw cause;
                                        }
                                }
                            }
                    }
                    int length3 = iArr3.length;
                    int[] iArr5 = new int[length3];
                    int[] iArr6 = f;
                    float f2 = 0.0f;
                    if (iArr6 != null) {
                        int i12 = $11 + 5;
                        $10 = i12 % 128;
                        switch (i12 % 2 != 0 ? (char) 6 : (char) 23) {
                            case 23:
                                length = iArr6.length;
                                iArr2 = new int[length];
                                i5 = 0;
                                break;
                            default:
                                length = iArr6.length;
                                iArr2 = new int[length];
                                i5 = 0;
                                break;
                        }
                        while (i5 < length) {
                            int i13 = $11 + 85;
                            $10 = i13 % 128;
                            if (i13 % 2 != 0) {
                                try {
                                    Object[] objArr9 = new Object[i9];
                                    objArr9[0] = Integer.valueOf(iArr6[i5]);
                                    Object obj2 = o.e.a.s.get(-1667374059);
                                    if (obj2 != null) {
                                        cArr2 = cArr4;
                                        i6 = length;
                                    } else {
                                        Class cls2 = (Class) o.e.a.c(11 - (AudioTrack.getMaxVolume() > f2 ? 1 : (AudioTrack.getMaxVolume() == f2 ? 0 : -1)), (char) (8856 - (ViewConfiguration.getJumpTapTimeout() >> 16)), (-16776892) - Color.rgb(0, 0, 0));
                                        byte b4 = (byte) 0;
                                        byte b5 = b4;
                                        cArr2 = cArr4;
                                        i6 = length;
                                        Object[] objArr10 = new Object[1];
                                        k(b4, b5, (byte) (b5 - 1), objArr10);
                                        obj2 = cls2.getMethod((String) objArr10[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj2);
                                    }
                                    iArr2[i5] = ((Integer) ((Method) obj2).invoke(null, objArr9)).intValue();
                                    i5++;
                                    length = i6;
                                    cArr4 = cArr2;
                                    f2 = 0.0f;
                                    i9 = 1;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                char[] cArr5 = cArr4;
                                int i14 = length;
                                try {
                                    Object[] objArr11 = {Integer.valueOf(iArr6[i5])};
                                    Object obj3 = o.e.a.s.get(-1667374059);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(KeyEvent.keyCodeFromString("") + 10, (char) ((KeyEvent.getMaxKeyCode() >> 16) + 8856), 325 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)));
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr12 = new Object[1];
                                        k(b6, b7, (byte) (b7 - 1), objArr12);
                                        obj3 = cls3.getMethod((String) objArr12[0], Integer.TYPE);
                                        o.e.a.s.put(-1667374059, obj3);
                                    }
                                    iArr2[i5] = ((Integer) ((Method) obj3).invoke(null, objArr11)).intValue();
                                    i5++;
                                    length = i14;
                                    cArr4 = cArr5;
                                    f2 = 0.0f;
                                    i9 = 1;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                        }
                        cArr = cArr4;
                        iArr6 = iArr2;
                    } else {
                        cArr = cArr4;
                    }
                    System.arraycopy(iArr6, 0, iArr5, 0, length3);
                    gVar.a = 0;
                    while (gVar.a < iArr.length) {
                        int i15 = $11 + 99;
                        $10 = i15 % 128;
                        int i16 = i15 % 2;
                        cArr3[0] = (char) (iArr[gVar.a] >> 16);
                        cArr3[1] = (char) iArr[gVar.a];
                        cArr3[2] = (char) (iArr[gVar.a + 1] >> 16);
                        cArr3[3] = (char) iArr[gVar.a + 1];
                        gVar.e = (cArr3[0] << 16) + cArr3[1];
                        gVar.c = (cArr3[2] << 16) + cArr3[3];
                        o.a.g.d(iArr5);
                        int i17 = 0;
                        for (int i18 = 16; i17 < i18; i18 = 16) {
                            gVar.e ^= iArr5[i17];
                            try {
                                Object[] objArr13 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                                Object obj4 = o.e.a.s.get(-2036901605);
                                if (obj4 == null) {
                                    obj4 = ((Class) o.e.a.c(View.MeasureSpec.makeMeasureSpec(0, 0) + 11, (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (ViewConfiguration.getTouchSlop() >> 8) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                                    o.e.a.s.put(-2036901605, obj4);
                                }
                                int intValue = ((Integer) ((Method) obj4).invoke(null, objArr13)).intValue();
                                gVar.e = gVar.c;
                                gVar.c = intValue;
                                i17++;
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        int i19 = gVar.e;
                        gVar.e = gVar.c;
                        gVar.c = i19;
                        gVar.c ^= iArr5[16];
                        gVar.e ^= iArr5[17];
                        int i20 = gVar.e;
                        int i21 = gVar.c;
                        cArr3[0] = (char) (gVar.e >>> 16);
                        cArr3[1] = (char) gVar.e;
                        cArr3[2] = (char) (gVar.c >>> 16);
                        cArr3[3] = (char) gVar.c;
                        o.a.g.d(iArr5);
                        cArr[gVar.a * 2] = cArr3[0];
                        cArr[(gVar.a * 2) + 1] = cArr3[1];
                        cArr[(gVar.a * 2) + 2] = cArr3[2];
                        cArr[(gVar.a * 2) + 3] = cArr3[3];
                        try {
                            Object[] objArr14 = {gVar, gVar};
                            Object obj5 = o.e.a.s.get(-331007466);
                            if (obj5 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 12, (char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 55182), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 514);
                                byte b8 = (byte) 1;
                                byte b9 = (byte) (b8 - 1);
                                Object[] objArr15 = new Object[1];
                                k(b8, b9, (byte) (b9 - 1), objArr15);
                                obj5 = cls4.getMethod((String) objArr15[0], Object.class, Object.class);
                                o.e.a.s.put(-331007466, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr14);
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    }
                    objArr6[0] = new String(cArr, 0, i4);
                }
            }, this.c).e(aVar, aVar2);
            int i4 = f45o + Opcodes.LSHL;
            k = i4 % 128;
            int i5 = i4 % 2;
        } catch (e e2) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr6 = new Object[1];
            m((byte) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 301038423 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (short) (ViewConfiguration.getJumpTapTimeout() >> 16), ExpandableListView.getPackedPositionGroup(0L) - 95, 1832221558 - (Process.myPid() >> 22), objArr6);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr6[0]).intern(), e2);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x008e, code lost:
    
        if (r16.c.q() == false) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0090, code lost:
    
        new o.au.b(r17, new o.ca.a.AnonymousClass3(r16), r16.c).e(r18, r19);
        r1 = o.ca.a.k + com.esotericsoftware.asm.Opcodes.LMUL;
        o.ca.a.f45o = r1 % 128;
        r1 = r1 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x00af, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x00b0, code lost:
    
        r2 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r4 = new java.lang.Object[1];
        l(null, 127 - android.view.KeyEvent.getDeadChar(0, 0), null, "\u0087\u0088\u008f\u008f\u0082\u008e", r4);
        r3 = ((java.lang.String) r4[0]).intern();
        r4 = new java.lang.Object[1];
        m((byte) (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 301038373 - (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), (short) (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (-78) - android.os.Process.getGidForName(""), (android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1832221577, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0105, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r2, r3, ((java.lang.String) r4[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0086, code lost:
    
        if (r17 != null) goto L13;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0053, code lost:
    
        if (r17 != null) goto L13;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(android.content.Context r17, o.el.d r18, o.co.a r19, final o.ca.b r20) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 298
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ca.a.d(android.content.Context, o.el.d, o.co.a, o.ca.b):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x008a, code lost:
    
        if (r10.c.q() == false) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x008c, code lost:
    
        new o.ar.b(r11, new o.ca.a.AnonymousClass1(r10), r10.c).a(r12, r13);
        r11 = o.ca.a.k + 99;
        o.ca.a.f45o = r11 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x00a5, code lost:
    
        if ((r11 % 2) == 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x00a7, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00a8, code lost:
    
        switch(r1) {
            case 1: goto L21;
            default: goto L29;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00ac, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00ad, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00b0, code lost:
    
        r12 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r14 = new java.lang.Object[1];
        l(null, android.widget.ExpandableListView.getPackedPositionChild(0) + 128, null, "\u0087\u0088\u008f\u008f\u0082\u008e", r14);
        r13 = ((java.lang.String) r14[0]).intern();
        r14 = new java.lang.Object[1];
        m((byte) android.graphics.Color.red(0), 301038373 - (android.view.ViewConfiguration.getTapTimeout() >> 16), (short) android.graphics.Color.green(0), (android.view.ViewConfiguration.getScrollBarSize() >> 8) - 77, android.text.TextUtils.getOffsetBefore("", 0) + 1832221578, r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0100, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r12, r13, ((java.lang.String) r14[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0081, code lost:
    
        if (r11 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x004d, code lost:
    
        if (r11 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0101, code lost:
    
        r12 = fr.antelop.sdk.exception.WalletValidationErrorCode.Mandatory;
        r14 = new java.lang.Object[1];
        l(null, (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1)) + com.esotericsoftware.asm.Opcodes.IAND, null, "\u0087\u008d\u0088\u0087\u008a\u008c\u0081", r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x011f, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r12, ((java.lang.String) r14[0]).intern(), "");
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(android.content.Context r11, o.el.d r12, java.lang.String r13, final o.ca.b r14) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 300
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ca.a.d(android.content.Context, o.el.d, java.lang.String, o.ca.b):void");
    }

    public final void a(Context context, o.eo.e eVar, boolean z, final b bVar) throws WalletValidationException {
        int i2 = k + Opcodes.DDIV;
        f45o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        Object obj = null;
        l(null, TextUtils.indexOf("", "") + 127, null, "\u0083\u0088\u008b\u0082\u008a\u0082\u0089\u0088\u0087\u0082\u0084\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((byte) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 301038445 - TextUtils.indexOf((CharSequence) "", '0', 0), (short) (ViewConfiguration.getScrollBarSize() >> 8), (-94) - (ViewConfiguration.getDoubleTapTimeout() >> 16), 1832221587 - ExpandableListView.getPackedPositionChild(0L), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            l(null, 127 - (ViewConfiguration.getPressedStateDuration() >> 16), null, "\u0087\u008d\u0088\u0087\u008a\u008c\u0081", objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (!this.c.q()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            l(null, 127 - TextUtils.indexOf("", "", 0, 0), null, "\u0087\u0088\u008f\u008f\u0082\u008e", objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            m((byte) (ViewConfiguration.getTouchSlop() >> 8), KeyEvent.keyCodeFromString("") + 301038373, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (-77) - TextUtils.getOffsetAfter("", 0), 1832221578 - (Process.myTid() >> 22), objArr5);
            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
        }
        new o.aw.c(context, new c.e() { // from class: o.ca.a.4
            private static int $10 = 0;
            private static int $11 = 1;
            private static int j = 0;
            private static int i = 1;
            private static char e = 44780;
            private static char c = 39397;
            private static char g = 25422;
            private static char a = 928;

            /* JADX WARN: Removed duplicated region for block: B:7:0x008e  */
            @Override // o.aw.c.e
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void c() {
                /*
                    r10 = this;
                    int r0 = o.ca.a.AnonymousClass4.i
                    int r0 = r0 + 91
                    int r1 = r0 % 128
                    o.ca.a.AnonymousClass4.j = r1
                    int r0 = r0 % 2
                    r1 = 65
                    java.lang.String r2 = "篶ꓮ밭嬧㦉遡ᶼᕣ\ue9dc㢬뷈볯એ눾\ueba6뮤\ue9dc㢬⽫鯬"
                    java.lang.String r3 = "龎㸄䖈䷱ꅋ儉ᶼᕣ\ue9dc㢬\udefb\udf88䢍䲪藿띣襀ׇ"
                    r4 = 1
                    r5 = 0
                    if (r0 == 0) goto L53
                    o.ee.g.c()
                    long r6 = android.view.ViewConfiguration.getZoomControlsTimeout()
                    r8 = 1
                    int r0 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
                    r6 = 73
                    int r0 = r6 << r0
                    java.lang.Object[] r6 = new java.lang.Object[r4]
                    f(r3, r0, r6)
                    r0 = r6[r5]
                    java.lang.String r0 = (java.lang.String) r0
                    java.lang.String r0 = r0.intern()
                    int r3 = android.view.View.resolveSizeAndState(r4, r5, r5)
                    int r3 = r3 + 62
                    java.lang.Object[] r4 = new java.lang.Object[r4]
                    f(r2, r3, r4)
                    r2 = r4[r5]
                    java.lang.String r2 = (java.lang.String) r2
                    java.lang.String r2 = r2.intern()
                    o.ee.g.d(r0, r2)
                    o.ca.b r0 = r2
                    if (r0 == 0) goto L4d
                    r0 = 17
                    goto L4f
                L4d:
                    r0 = 11
                L4f:
                    switch(r0) {
                        case 17: goto L8e;
                        default: goto L52;
                    }
                L52:
                    goto L8d
                L53:
                    o.ee.g.c()
                    long r6 = android.view.ViewConfiguration.getZoomControlsTimeout()
                    r8 = 0
                    int r0 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
                    int r0 = r0 + 16
                    java.lang.Object[] r6 = new java.lang.Object[r4]
                    f(r3, r0, r6)
                    r0 = r6[r5]
                    java.lang.String r0 = (java.lang.String) r0
                    java.lang.String r0 = r0.intern()
                    int r3 = android.view.View.resolveSizeAndState(r5, r5, r5)
                    r6 = 19
                    int r3 = 19 - r3
                    java.lang.Object[] r4 = new java.lang.Object[r4]
                    f(r2, r3, r4)
                    r2 = r4[r5]
                    java.lang.String r2 = (java.lang.String) r2
                    java.lang.String r2 = r2.intern()
                    o.ee.g.d(r0, r2)
                    o.ca.b r0 = r2
                    if (r0 == 0) goto L8a
                    r6 = r1
                L8a:
                    switch(r6) {
                        case 65: goto L8e;
                        default: goto L8d;
                    }
                L8d:
                    goto Laa
                L8e:
                    int r0 = o.ca.a.AnonymousClass4.j
                    int r0 = r0 + 69
                    int r2 = r0 % 128
                    o.ca.a.AnonymousClass4.i = r2
                    int r0 = r0 % 2
                    if (r0 != 0) goto La5
                    o.ca.b r0 = r2
                    r0.d()
                    r0 = 58
                    int r0 = r0 / r5
                    goto Laa
                La3:
                    r0 = move-exception
                    throw r0
                La5:
                    o.ca.b r0 = r2
                    r0.d()
                Laa:
                    int r0 = o.ca.a.AnonymousClass4.i
                    int r0 = r0 + r1
                    int r1 = r0 % 128
                    o.ca.a.AnonymousClass4.j = r1
                    int r0 = r0 % 2
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.ca.a.AnonymousClass4.c():void");
            }

            @Override // o.aw.c.e
            public final void a(o.bb.d dVar) {
                int i4 = j + 87;
                i = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Object[] objArr6 = new Object[1];
                f("龎㸄䖈䷱ꅋ儉ᶼᕣ\ue9dc㢬\udefb\udf88䢍䲪藿띣襀ׇ", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 16, objArr6);
                String intern3 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                f("篶ꓮ밭嬧㦉遡ᶼᕣ\ue9dc㢬ꤎ誱䈬ﶉ펯\udeab眎ᑈ", TextUtils.indexOf("", "", 0) + 17, objArr7);
                g.d(intern3, ((String) objArr7[0]).intern());
                b bVar2 = bVar;
                switch (bVar2 != null ? (char) 31 : (char) 22) {
                    case 31:
                        int i6 = j + 93;
                        i = i6 % 128;
                        boolean z2 = i6 % 2 != 0;
                        bVar2.d(dVar);
                        switch (z2) {
                            case true:
                                return;
                            default:
                                int i7 = 90 / 0;
                                return;
                        }
                    default:
                        return;
                }
            }

            private static void f(String str, int i4, Object[] objArr6) {
                char[] cArr;
                switch (str != null ? (char) 11 : '\\') {
                    case Opcodes.DUP2 /* 92 */:
                        cArr = str;
                        break;
                    default:
                        int i5 = $10 + 33;
                        $11 = i5 % 128;
                        switch (i5 % 2 == 0 ? (char) 31 : '`') {
                            case Opcodes.IADD /* 96 */:
                                cArr = str.toCharArray();
                                break;
                            default:
                                str.toCharArray();
                                Object obj2 = null;
                                obj2.hashCode();
                                throw null;
                        }
                }
                char[] cArr2 = cArr;
                i iVar = new i();
                char[] cArr3 = new char[cArr2.length];
                int i6 = 0;
                iVar.b = 0;
                char[] cArr4 = new char[2];
                int i7 = $11 + 9;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                while (iVar.b < cArr2.length) {
                    cArr4[i6] = cArr2[iVar.b];
                    cArr4[1] = cArr2[iVar.b + 1];
                    int i9 = 58224;
                    int i10 = i6;
                    while (i10 < 16) {
                        int i11 = $10 + 53;
                        $11 = i11 % 128;
                        int i12 = i11 % 2;
                        char c2 = cArr4[1];
                        char c3 = cArr4[i6];
                        char[] cArr5 = cArr2;
                        int i13 = (c3 + i9) ^ ((c3 << 4) + ((char) (c ^ 8439748517800462901L)));
                        int i14 = c3 >>> 5;
                        try {
                            Object[] objArr7 = new Object[4];
                            objArr7[3] = Integer.valueOf(g);
                            objArr7[2] = Integer.valueOf(i14);
                            objArr7[1] = Integer.valueOf(i13);
                            objArr7[i6] = Integer.valueOf(c2);
                            Object obj3 = o.e.a.s.get(-1512468642);
                            if (obj3 == null) {
                                Class cls = (Class) o.e.a.c(11 - Color.argb(i6, i6, i6, i6), (char) View.getDefaultSize(i6, i6), 603 - KeyEvent.keyCodeFromString(""));
                                Class<?>[] clsArr = new Class[4];
                                clsArr[i6] = Integer.TYPE;
                                clsArr[1] = Integer.TYPE;
                                clsArr[2] = Integer.TYPE;
                                clsArr[3] = Integer.TYPE;
                                obj3 = cls.getMethod("C", clsArr);
                                o.e.a.s.put(-1512468642, obj3);
                            }
                            char charValue = ((Character) ((Method) obj3).invoke(null, objArr7)).charValue();
                            cArr4[1] = charValue;
                            char[] cArr6 = cArr4;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr4[i6]), Integer.valueOf((charValue + i9) ^ ((charValue << 4) + ((char) (a ^ 8439748517800462901L)))), Integer.valueOf(charValue >>> 5), Integer.valueOf(e)};
                                Object obj4 = o.e.a.s.get(-1512468642);
                                if (obj4 == null) {
                                    obj4 = ((Class) o.e.a.c(11 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (char) (ViewConfiguration.getScrollBarSize() >> 8), Process.getGidForName("") + 604)).getMethod("C", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-1512468642, obj4);
                                }
                                cArr6[0] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                i9 -= 40503;
                                i10++;
                                cArr2 = cArr5;
                                cArr4 = cArr6;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    char[] cArr7 = cArr2;
                    char[] cArr8 = cArr4;
                    cArr3[iVar.b] = cArr8[0];
                    cArr3[iVar.b + 1] = cArr8[1];
                    try {
                        Object[] objArr9 = {iVar, iVar};
                        Object obj5 = o.e.a.s.get(2062727845);
                        if (obj5 == null) {
                            obj5 = ((Class) o.e.a.c(10 - View.MeasureSpec.makeMeasureSpec(0, 0), (char) ((ViewConfiguration.getTouchSlop() >> 8) + 30725), 613 - TextUtils.indexOf((CharSequence) "", '0', 0))).getMethod("A", Object.class, Object.class);
                            o.e.a.s.put(2062727845, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr9);
                        int i15 = $11 + 97;
                        $10 = i15 % 128;
                        int i16 = i15 % 2;
                        cArr2 = cArr7;
                        cArr4 = cArr8;
                        i6 = 0;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr6[0] = new String(cArr3, 0, i4);
            }
        }, this.c).e(eVar, z);
        int i4 = k + 13;
        f45o = i4 % 128;
        switch (i4 % 2 != 0 ? 'Y' : '6') {
            case Opcodes.ISTORE /* 54 */:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    private static void l(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] cArr3;
        int length;
        char[] cArr4;
        int i3;
        String str3 = str2;
        char c = 0;
        boolean z = str3 != null;
        int i4 = 2;
        byte[] bArr = str3;
        switch (z) {
            case true:
                byte[] bytes = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                int i5 = $10 + Opcodes.LNEG;
                $11 = i5 % 128;
                int i6 = i5 % 2;
                bArr = bytes;
                break;
        }
        byte[] bArr2 = bArr;
        switch (str != null ? 'T' : '\'') {
            case '\'':
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr5 = cArr;
        j jVar = new j();
        char[] cArr6 = d;
        long j2 = 0;
        switch (cArr6 != null ? '@' : 'Y') {
            case Opcodes.DUP /* 89 */:
                break;
            default:
                int i7 = $11 + Opcodes.LSUB;
                $10 = i7 % 128;
                if (i7 % 2 != 0) {
                    length = cArr6.length;
                    cArr4 = new char[length];
                    i3 = 0;
                } else {
                    length = cArr6.length;
                    cArr4 = new char[length];
                    i3 = 0;
                }
                while (i3 < length) {
                    int i8 = $11 + Opcodes.DNEG;
                    $10 = i8 % 128;
                    switch (i8 % i4 != 0 ? c : (char) 1) {
                        case 1:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[c] = Integer.valueOf(cArr6[i3]);
                                Object obj = o.e.a.s.get(1085633688);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(11 - (Process.myTid() >> 22), (char) ((SystemClock.elapsedRealtimeNanos() > j2 ? 1 : (SystemClock.elapsedRealtimeNanos() == j2 ? 0 : -1)) - 1), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 493);
                                    byte b2 = (byte) (-1);
                                    Object[] objArr3 = new Object[1];
                                    n((byte) 13, b2, (byte) (b2 + 1), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1085633688, obj);
                                }
                                cArr4[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i3++;
                                c = 0;
                                i4 = 2;
                                j2 = 0;
                                break;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr6[i3])};
                                Object obj2 = o.e.a.s.get(1085633688);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(11 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (char) (ViewConfiguration.getScrollBarSize() >> 8), ((byte) KeyEvent.getModifierMetaStateMask()) + 494);
                                    byte b3 = (byte) (-1);
                                    Object[] objArr5 = new Object[1];
                                    n((byte) 13, b3, (byte) (b3 + 1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(1085633688, obj2);
                                }
                                cArr4[i3] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                i3 <<= 1;
                                c = 0;
                                i4 = 2;
                                j2 = 0;
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                    }
                }
                cArr6 = cArr4;
                break;
        }
        try {
            Object[] objArr6 = {Integer.valueOf(b)};
            Object obj3 = o.e.a.s.get(-1667314477);
            char c2 = 3;
            if (obj3 == null) {
                Class cls3 = (Class) o.e.a.c(10 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) (TextUtils.lastIndexOf("", '0', 0, 0) + 8857), 324 - (Process.myTid() >> 22));
                byte b4 = (byte) (-1);
                Object[] objArr7 = new Object[1];
                n((byte) ($$a[3] + 1), b4, (byte) (b4 + 1), objArr7);
                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
            switch (e) {
                case true:
                    int i9 = $11 + 21;
                    $10 = i9 % 128;
                    switch (i9 % 2 != 0) {
                        case false:
                            jVar.e = bArr2.length;
                            cArr2 = new char[jVar.e];
                            jVar.c = 0;
                            break;
                        default:
                            jVar.e = bArr2.length;
                            cArr2 = new char[jVar.e];
                            jVar.c = 1;
                            break;
                    }
                    while (jVar.c < jVar.e) {
                        cArr2[jVar.c] = (char) (cArr6[bArr2[(jVar.e - 1) - jVar.c] + i2] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(11 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (char) (TextUtils.lastIndexOf("", '0', 0) + 1), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 208);
                                byte b5 = (byte) (-1);
                                Object[] objArr9 = new Object[1];
                                n($$a[3], b5, (byte) (b5 + 1), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr[0] = new String(cArr2);
                    return;
                default:
                    if (!a) {
                        jVar.e = iArr.length;
                        char[] cArr7 = new char[jVar.e];
                        jVar.c = 0;
                        while (jVar.c < jVar.e) {
                            int i10 = $11 + 79;
                            $10 = i10 % 128;
                            if (i10 % 2 != 0) {
                                cArr7[jVar.c] = (char) (cArr6[iArr[(jVar.e * 1) - jVar.c] % i2] >>> intValue);
                            } else {
                                cArr7[jVar.c] = (char) (cArr6[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                            }
                            jVar.c++;
                        }
                        objArr[0] = new String(cArr7);
                        return;
                    }
                    int i11 = $10 + 69;
                    $11 = i11 % 128;
                    switch (i11 % 2 == 0) {
                        case true:
                            jVar.e = cArr5.length;
                            cArr3 = new char[jVar.e];
                            jVar.c = 1;
                            break;
                        default:
                            jVar.e = cArr5.length;
                            cArr3 = new char[jVar.e];
                            jVar.c = 0;
                            break;
                    }
                    while (jVar.c < jVar.e) {
                        int i12 = $11 + 87;
                        $10 = i12 % 128;
                        int i13 = i12 % 2;
                        cArr3[jVar.c] = (char) (cArr6[cArr5[(jVar.e - 1) - jVar.c] - i2] - intValue);
                        try {
                            Object[] objArr10 = {jVar, jVar};
                            Object obj5 = o.e.a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 9, (char) ((-1) - MotionEvent.axisFromString("")), 207 - TextUtils.indexOf("", ""));
                                byte b6 = (byte) (-1);
                                Object[] objArr11 = new Object[1];
                                n($$a[c2], b6, (byte) (b6 + 1), objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                            c2 = 3;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    }
                    String str4 = new String(cArr3);
                    int i14 = $11 + 83;
                    $10 = i14 % 128;
                    int i15 = i14 % 2;
                    objArr[0] = str4;
                    return;
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:71:0x021a, code lost:
    
        if (r9 != false) goto L77;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r18, int r19, short r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 938
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ca.a.m(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
