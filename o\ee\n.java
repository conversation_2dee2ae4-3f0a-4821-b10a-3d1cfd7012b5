package o.ee;

import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.Tasks;
import java.util.concurrent.ExecutionException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\n.smali */
public final class n {
    private static int a = 0;
    private static int c = 1;

    public static <T> T c(Task<T> task) {
        T t;
        int i = a;
        int i2 = (i ^ Opcodes.DMUL) + ((i & Opcodes.DMUL) << 1);
        c = i2 % 128;
        try {
            switch (i2 % 2 == 0) {
                case false:
                    t = (T) Tasks.await(task);
                    break;
                default:
                    t = (T) Tasks.await(task);
                    int i3 = 50 / 0;
                    break;
            }
            return t;
        } catch (InterruptedException | ExecutionException e) {
            return null;
        }
    }

    public static <T> T e(com.huawei.hmf.tasks.Task<T> task) {
        int i = a;
        int i2 = ((i | 25) << 1) - (i ^ 25);
        c = i2 % 128;
        try {
            switch (i2 % 2 == 0) {
                case false:
                    return (T) com.huawei.hmf.tasks.Tasks.await(task);
                default:
                    com.huawei.hmf.tasks.Tasks.await(task);
                    throw null;
            }
        } catch (InterruptedException | ExecutionException e) {
            return null;
        }
    }
}
