package o.az;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.m;
import o.az.b;
import o.bv.g;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\d.smali */
public final class d implements b.c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int g;
    private static char[] h;
    private static long i;
    private static char j;
    private static char l;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static int f37o;
    private final o.ei.c a;
    private Context b;
    private b c;
    private final f d;
    private c e;
    private boolean f;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\d$c.smali */
    public interface c {
        void a();

        void a(o.cb.a aVar, o.g.b bVar, o.bb.d dVar);

        void b(o.bb.d dVar);

        void d(o.bb.d dVar, boolean z, g gVar);

        void e();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        n = 0;
        f37o = 1;
        b();
        TextUtils.indexOf((CharSequence) "", '0');
        SystemClock.elapsedRealtimeNanos();
        int i2 = n + 81;
        f37o = i2 % 128;
        switch (i2 % 2 == 0 ? '[' : (char) 29) {
            case Opcodes.DUP_X2 /* 91 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void b() {
        j = (char) 17957;
        g = 1429759502;
        i = 6565854932352255525L;
        h = new char[]{30574, 30537, 30498, 30562, 29844, 29845, 30590, 30563, 29842, 30540, 30572, 29847, 30573, 30511, 30570, 29852, 29855, 30568, 30584, 30517, 30528, 30588, 29841, 30586, 30571, 30503, 30560, 30497, 30564, 29854, 30556, 29840, 30585, 30587, 30567, 30591, 30589, 29846, 30569, 30557, 30536, 30566, 30561, 30529, 30583, 29843, 29853, 30502, 30542};
        l = (char) 17042;
    }

    static void init$0() {
        $$a = new byte[]{15, -30, 44, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
        $$b = 21;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 4
            byte[] r0 = o.az.d.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r9 = 106 - r9
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = r8 + r10
            int r9 = r9 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.d.p(byte, short, short, java.lang.Object[]):void");
    }

    public d(Context context, o.ei.c cVar, c cVar2, f fVar) {
        this.b = context;
        this.a = cVar;
        this.e = cVar2;
        this.d = fVar;
    }

    public final void e(c cVar, Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64768 - KeyEvent.getDeadChar(0, 0)), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 34, "\u001c\u0015\u001d/\u000f\u0013-\r\"(\u001c\u0004\"(\u0015/\u0007\u0002㙡㙡\u0007\u0005\u0007\u001f*+\u0017\n\u0015/\u001c\u0013/\u001e", (byte) (Drawable.resolveOpacity(0, 0) + Opcodes.DMUL), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.e = cVar;
        this.b = context;
        b bVar = this.c;
        switch (bVar != null ? 'U' : ';') {
            case Opcodes.CASTORE /* 85 */:
                int i2 = f37o + 47;
                n = i2 % 128;
                char c2 = i2 % 2 != 0 ? '\r' : '`';
                bVar.c(context);
                switch (c2) {
                    case '\r':
                        throw null;
                }
        }
        int i3 = f37o + 47;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:9:0x0086, code lost:
    
        r0 = false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(o.h.d r9, o.c.a r10, boolean r11, boolean r12) {
        /*
            r8 = this;
            int r0 = o.az.d.n
            int r0 = r0 + 59
            int r1 = r0 % 128
            o.az.d.f37o = r1
            int r0 = r0 % 2
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r0 = r0 >> 16
            r1 = -1502490308(0xffffffffa671d13c, float:-8.3897237E-16)
            int r2 = r1 - r0
            java.lang.String r3 = "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬"
            long r0 = android.os.SystemClock.elapsedRealtime()
            r4 = 0
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            r1 = 64769(0xfd01, float:9.076E-41)
            int r1 = r1 - r0
            char r4 = (char) r1
            java.lang.String r5 = "㲰燑¦曽"
            java.lang.String r6 = "\u0000\u0000\u0000\u0000"
            r0 = 1
            java.lang.Object[] r1 = new java.lang.Object[r0]
            r7 = r1
            k(r2, r3, r4, r5, r6, r7)
            r2 = 0
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            double r3 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r2)
            r5 = 0
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            int r3 = 12 - r3
            int r4 = android.view.ViewConfiguration.getMaximumDrawingCacheSize()
            int r4 = r4 >> 24
            int r4 = r4 + 93
            byte r4 = (byte) r4
            java.lang.Object[] r5 = new java.lang.Object[r0]
            java.lang.String r6 = "\u0019\u001b-\r\"(\u001c\u0004\"(\u0015/"
            m(r3, r6, r4, r5)
            r3 = r5[r2]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            o.ee.g.d(r1, r3)
            android.content.Context r1 = r8.b
            boolean r1 = a(r1)
            if (r1 != 0) goto L6b
            r1 = 84
            goto L6d
        L6b:
            r1 = 49
        L6d:
            switch(r1) {
                case 49: goto L71;
                default: goto L70;
            }
        L70:
            goto L87
        L71:
            int r0 = o.az.d.n
            int r0 = r0 + 41
            int r1 = r0 % 128
            o.az.d.f37o = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L81
            r0 = 50
            goto L83
        L81:
            r0 = 27
        L83:
            switch(r0) {
                case 27: goto L86;
                default: goto L86;
            }
        L86:
            r0 = r2
        L87:
            r8.f = r0
            o.az.b r1 = new o.az.b
            android.content.Context r0 = r8.b
            o.ei.c r2 = r8.a
            o.de.f r3 = r8.d
            r1.<init>(r0, r8, r2, r3)
            r8.c = r1
            boolean r5 = r8.f
            r2 = r9
            r3 = r10
            r4 = r11
            r6 = r12
            r1.a(r2, r3, r4, r5, r6)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.d.d(o.h.d, o.c.a, boolean, boolean):void");
    }

    public final void e() {
        int i2 = n + 97;
        f37o = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k(KeyEvent.normalizeMetaState(0) - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64768 - TextUtils.getOffsetBefore("", 0)), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 22, "\"#㘖㘖\u0010\u001e㘈㘈\u0015\u0000\"/\f\u001f'\"\u0005\u001c(\u001b.\u0015㗓", (byte) ((ViewConfiguration.getTouchSlop() >> 8) + 32), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        b bVar = this.c;
        switch (bVar == null) {
            case true:
                return;
            default:
                int i4 = f37o + Opcodes.LSHL;
                n = i4 % 128;
                boolean z = i4 % 2 == 0;
                bVar.f();
                switch (z) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    private static boolean a(Context context) {
        int i2 = n + 27;
        f37o = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k((-151481715) - (Process.myPid() >> 22), "膩ඪ⪽ฮ\ue720ṣ㻟희⻇\uda27\uf414\ue0a0Ȿꖓ\u0fe7갳ꄀ溿㨀㥄Ⓑ賈濉뚼\u0ee0Ł藰뺵䟂꽝\uf6f6撥ޛ࿐卬魒초瀧잉\uf58d\ued4cԍ\uf32b\uefeeኺ♧䪲", (char) Color.argb(0, 0, 0, 0), "跩\uf892叶糬", "\u0000\u0000\u0000\u0000", objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        m((ViewConfiguration.getFadingEdgeLength() >> 16) + 6, "\u0001\u0004\u0018)\u0003\u0015", (byte) (53 - TextUtils.getOffsetBefore("", 0)), objArr2);
        boolean z = sharedPreferences.getBoolean(((String) objArr2[0]).intern(), false);
        int i4 = n + 45;
        f37o = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 14 : Typography.greater) {
            case '>':
                return z;
            default:
                throw null;
        }
    }

    static void d(Context context) {
        int i2 = f37o + 97;
        n = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k((-168258931) - Color.rgb(0, 0, 0), "膩ඪ⪽ฮ\ue720ṣ㻟희⻇\uda27\uf414\ue0a0Ȿꖓ\u0fe7갳ꄀ溿㨀㥄Ⓑ賈濉뚼\u0ee0Ł藰뺵䟂꽝\uf6f6撥ޛ࿐卬魒초瀧잉\uf58d\ued4cԍ\uf32b\uefeeኺ♧䪲", (char) ((-1) - ImageFormat.getBitsPerPixel(0)), "跩\uf892叶糬", "\u0000\u0000\u0000\u0000", objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        m(6 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "\u0001\u0004\u0018)\u0003\u0015", (byte) (52 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), objArr2);
        edit.putBoolean(((String) objArr2[0]).intern(), true).commit();
        int i4 = n + 71;
        f37o = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    @Override // o.az.b.c
    public final void c(o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k(53612 - AndroidCharacter.getMirror('0'), "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64768 - (Process.myPid() >> 22)), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m(Color.argb(0, 0, 0, 0) + 24, "\u0015/-\r\"(\u001c\u0004\"(\u0015/\u0002\u0001#\r\u0016%\u0012\u00150\f\u0006\t", (byte) (75 - (ViewConfiguration.getWindowTouchSlop() >> 8)), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.e).toString());
        Object obj = null;
        this.c = null;
        this.e.b(dVar);
        int i2 = f37o + 11;
        n = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.az.b.c
    public final void b(o.bb.d dVar, g gVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1502490309, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64767 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((ViewConfiguration.getTapTimeout() >> 16) + 24, "\u0015/-\r\"(\u001c\u0004\"(\u0015/%\u001e㘰㘰\u0015\u001c\u0016\u001a0\f\u0006\t", (byte) (55 - Color.alpha(0)), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.e).toString());
        this.c = null;
        this.e.d(dVar, this.f, gVar);
        this.f = false;
        int i2 = n + 109;
        f37o = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    @Override // o.az.b.c
    public final void e(boolean z, o.j.b bVar, o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((KeyEvent.getMaxKeyCode() >> 16) - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64769 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m(41 - ImageFormat.getBitsPerPixel(0), "\u0015/-\r\"(\u001c\u0004\"(\u0015/\b%\u0011\u0015\u0015\u0000\"(\u0007\u000e\u0019#\u0015\u000e\u0005\u001c\u0011\u0015\u0002\u0001#\r\u0016%\u0012\u00150\f\u0006\t", (byte) (ImageFormat.getBitsPerPixel(0) + Opcodes.LSHL), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.e).toString());
        this.c = null;
        d(z, bVar, dVar);
        int i2 = n + 99;
        f37o = i2 % 128;
        switch (i2 % 2 == 0 ? 'Z' : '[') {
            case 'Z':
                int i3 = 47 / 0;
                return;
            default:
                return;
        }
    }

    /* renamed from: o.az.d$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\d$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int c;

        static {
            b = 0;
            c = 1;
            int[] iArr = new int[o.j.b.values().length];
            a = iArr;
            try {
                iArr[o.j.b.c.ordinal()] = 1;
                int i = b + 47;
                c = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                a[o.j.b.a.ordinal()] = 2;
                int i3 = c;
                int i4 = (i3 ^ 77) + ((i3 & 77) << 1);
                b = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[o.j.b.d.ordinal()] = 3;
                int i5 = b + 53;
                c = i5 % 128;
                if (i5 % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[o.j.b.b.ordinal()] = 4;
                int i6 = (c + 102) - 1;
                b = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    private void d(boolean z, o.j.b bVar, o.bb.d dVar) {
        o.g.b bVar2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64768 - (ViewConfiguration.getJumpTapTimeout() >> 16)), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k(Gravity.getAbsoluteGravity(0, 0), "鴊˷퍨╼딧礋⾙㔗Ჳ麝꒪鴕ࡘ䬤\ue5e9ꊓ㙧笢䢥㭖먾鹳퀬\uf72e\ue4d7烔ᘰ핈\uf49f穝\ue535칻꺊鯾㱼\ue470踇\uf396ﾙ죱흍㍊죐觏\ue39eⓠ愽띱첕\ue58a擼皠ⶶ跑돤쾆밈娘\ud88f뀉\uf460怙\uf51d퇤瞬ඁ䉦䢳", (char) (55700 - KeyEvent.keyCodeFromString("")), "達䄝鑥珙", "\u0000\u0000\u0000\u0000", objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
        o.cb.a aVar = null;
        switch (!z) {
            case false:
                int i2 = f37o + 51;
                n = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        bVar2 = o.g.b.e;
                        int i3 = 52 / 0;
                        break;
                    default:
                        bVar2 = o.g.b.e;
                        break;
                }
            default:
                bVar2 = null;
                break;
        }
        switch (AnonymousClass5.a[bVar.ordinal()]) {
            case 1:
                aVar = o.cb.a.a;
                break;
            case 2:
                aVar = o.cb.a.d;
                break;
            case 3:
                aVar = o.cb.a.b;
                break;
        }
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        k(KeyEvent.keyCodeFromString("") - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (Color.red(0) + 64768), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr4 = new Object[1];
        m((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 67, "$%\u0018\f\u0015\u001c\u001b*\f\u001f'\"\u0005\u001c(\u001b,\u0007#\u000f\u0015\u0011/\u001c#\u0006\u000e\u001c\u0002\u0001#\r\u0016%\u0014\u0007\u0006\t\b&\u0011\u0015\u0015\u0000\"(\u0007\u000e\u001b\u0007#\u000f\u0002\u001b#%\u0011\u0015\b)\u0015\u0007\u0016\u001b0\u0007\u0014\f", (byte) ((ViewConfiguration.getPressedStateDuration() >> 16) + Opcodes.FMUL), objArr4);
        o.ee.g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(aVar).toString());
        this.e.a(aVar, bVar2, dVar);
        int i4 = f37o + 91;
        n = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.az.b.c
    public final void c() {
        int i2 = f37o + 67;
        n = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k((-1502490308) - Drawable.resolveOpacity(0, 0), "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (ExpandableListView.getPackedPositionGroup(0L) + 64768), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(ViewConfiguration.getJumpTapTimeout() >> 16, "쿁\uea28\uf14bṫ\uf18a┦ﺆ盶㍔涰\u1cbb惬㢫抾蚇歱ఛ淥뚸ᣮ͎챞ᆛꑰ퓡칩", (char) (8428 - Color.blue(0)), "㊃ᴶ\uec71\ue120", "\u0000\u0000\u0000\u0000", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.e.e();
        int i4 = n + Opcodes.LSHR;
        f37o = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.az.b.c
    public final void a() {
        int i2 = f37o + 99;
        n = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k(TextUtils.indexOf("", "", 0, 0) - 1502490308, "緥鄣惆臛ቝᅙ뤁啴猳兮丞袴\uf8d8\ued2bꬍ\uf4ee㶬", (char) (64769 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), "㲰燑¦曽", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m(Color.blue(0) + 27, "\u0015/\u001f\u001c\u0016(\"/\f\u001f'\"\u0005\u001c(\u001b0\u000e/#\u001b(-\u000e\u001a.㘙", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 95), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.e.a();
        int i4 = n + 27;
        f37o = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 738
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.d.k(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }

    private static void m(int i2, String str, byte b, Object[] objArr) {
        char[] cArr;
        int i3;
        if (str != null) {
            int i4 = $11 + 77;
            $10 = i4 % 128;
            int i5 = i4 % 2;
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = h;
        int i6 = -1401577988;
        switch (cArr3 != null) {
            case true:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i7 = 0;
                while (true) {
                    switch (i7 < length ? (char) 29 : Typography.greater) {
                        case '>':
                            cArr3 = cArr4;
                            break;
                        default:
                            try {
                                Object[] objArr2 = {Integer.valueOf(cArr3[i7])};
                                Object obj = o.e.a.s.get(Integer.valueOf(i6));
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(17 - ExpandableListView.getPackedPositionType(0L), (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 75 - TextUtils.lastIndexOf("", '0'));
                                    byte b2 = (byte) 0;
                                    byte b3 = b2;
                                    Object[] objArr3 = new Object[1];
                                    p(b2, b3, (byte) (b3 | 33), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(-1401577988, obj);
                                }
                                cArr4[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i7++;
                                i6 = -1401577988;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                    }
                }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(l)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(TextUtils.indexOf("", "") + 17, (char) Drawable.resolveOpacity(0, 0), View.resolveSize(0, 0) + 76);
                byte b4 = (byte) 0;
                byte b5 = b4;
                Object[] objArr5 = new Object[1];
                p(b4, b5, (byte) (b5 | 33), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i2];
            if (i2 % 2 != 0) {
                int i8 = $11;
                int i9 = i8 + 47;
                $10 = i9 % 128;
                int i10 = i9 % 2;
                i3 = i2 - 1;
                cArr5[i3] = (char) (cArr2[i3] - b);
                int i11 = i8 + 95;
                $10 = i11 % 128;
                int i12 = i11 % 2;
            } else {
                i3 = i2;
            }
            if (i3 > 1) {
                mVar.b = 0;
                while (true) {
                    switch (mVar.b < i3) {
                        case false:
                            break;
                        default:
                            mVar.e = cArr2[mVar.b];
                            mVar.a = cArr2[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                cArr5[mVar.b] = (char) (mVar.e - b);
                                cArr5[mVar.b + 1] = (char) (mVar.a - b);
                            } else {
                                try {
                                    Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(TextUtils.indexOf("", "", 0) + 10, (char) ((ViewConfiguration.getScrollBarSize() >> 8) + 8856), ((Process.getThreadPriority(0) + 20) >> 6) + 324);
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr7 = new Object[1];
                                        p(b6, b7, (byte) (b7 | 37), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 == null) {
                                                Class cls4 = (Class) o.e.a.c(Color.green(0) + 11, (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), (Process.myPid() >> 22) + 65);
                                                byte b8 = (byte) 0;
                                                Object[] objArr9 = new Object[1];
                                                p(b8, b8, $$a[3], objArr9);
                                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i13 = (mVar.d * charValue) + mVar.h;
                                            cArr5[mVar.b] = cArr3[intValue];
                                            cArr5[mVar.b + 1] = cArr3[i13];
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else if (mVar.c == mVar.d) {
                                        int i14 = $10 + 85;
                                        $11 = i14 % 128;
                                        int i15 = i14 % 2;
                                        mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                        mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                        int i16 = (mVar.c * charValue) + mVar.i;
                                        int i17 = (mVar.d * charValue) + mVar.h;
                                        cArr5[mVar.b] = cArr3[i16];
                                        cArr5[mVar.b + 1] = cArr3[i17];
                                    } else {
                                        int i18 = (mVar.c * charValue) + mVar.h;
                                        int i19 = (mVar.d * charValue) + mVar.i;
                                        cArr5[mVar.b] = cArr3[i18];
                                        cArr5[mVar.b + 1] = cArr3[i19];
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                    }
                }
            }
            for (int i20 = 0; i20 < i2; i20++) {
                cArr5[i20] = (char) (cArr5[i20] ^ 13722);
            }
            objArr[0] = new String(cArr5);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
