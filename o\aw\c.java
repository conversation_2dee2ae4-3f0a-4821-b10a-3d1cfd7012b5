package o.aw;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.proxy.AuthApiStatusCodes;
import kotlin.text.Typography;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.eo.h;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aw\c.smali */
public final class c extends b<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] d;
    private static int g;
    h b;
    boolean c;
    o.eo.e e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aw\c$e.smali */
    public interface e {
        void a(o.bb.d dVar);

        void c();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        g = 1;
        o();
        int i = g + 57;
        a = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{17, -116, 103, 33};
        $$e = Opcodes.D2L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 66
            byte[] r0 = o.aw.c.$$d
            int r6 = r6 * 4
            int r6 = 4 - r6
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r6 = r6 + 1
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aw.c.l(int, int, short, java.lang.Object[]):void");
    }

    static void o() {
        d = new char[]{50755, 51156, 51159, 51145, 51148, 51166, 50736, 51150, 51157, 51166, 51165, 51157, 51157, 51163, 51157, 51159, 51144, 51150, 51157, 51162, 51164, 51164, 51166, 51146, 51144, 51165, 51161, 51165, 51137, 51161, 51163, 51147, 51147, 50939, 50859, 50854, 50879, 50877, 50876, 50877, 50837, 50847, 50853, 50860, 50857, 50849, 50849};
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        d m;
        int i = a + 71;
        g = i % 128;
        switch (i % 2 != 0) {
            case true:
                m = m();
                break;
            default:
                m = m();
                int i2 = 65 / 0;
                break;
        }
        int i3 = g + 97;
        a = i3 % 128;
        switch (i3 % 2 != 0 ? ']' : '.') {
            case '.':
                return m;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public c(Context context, e eVar, o.ei.c cVar) {
        super(context, eVar, cVar, o.bb.e.k);
    }

    public final void e(o.eo.e eVar, boolean z) {
        int i = g + 35;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 21 : '8') {
            case 21:
                this.e = eVar;
                this.c = z;
                c();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.e = eVar;
                this.c = z;
                c();
                return;
        }
    }

    private d m() {
        d dVar = new d(this);
        int i = g + Opcodes.LMUL;
        a = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = g + Opcodes.DREM;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{0, 33, Opcodes.TABLESWITCH, 23}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{0, 33, Opcodes.TABLESWITCH, 23}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = g + 59;
        a = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 66 / 0;
                return intern;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aw\c$d.smali */
    public static final class d extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static int[] c;
        private static int d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            a = 1;
            c = new int[]{-940145474, 540686204, -1757740468, -1056538897, -187030677, 1750469797, 1689699046, 1629625339, -920660750, -840664802, 1280979064, 1353794627, -1598571850, 299653571, 1189119030, 247399573, 2133733737, 1063038485};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 + 115
                int r6 = r6 * 4
                int r6 = r6 + 1
                int r8 = r8 + 4
                byte[] r0 = o.aw.c.d.$$d
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L33
            L17:
                r3 = r2
            L18:
                int r8 = r8 + 1
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                int r3 = r3 + 1
                r4 = r0[r8]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L33:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aw.c.d.B(byte, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{117, -111, 19, -37};
            $$e = 11;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 95;
            a = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = a + 55;
            d = i % 128;
            int i2 = i % 2;
            o.cf.d e = e(context);
            int i3 = a + 45;
            d = i3 % 128;
            switch (i3 % 2 != 0 ? '*' : (char) 26) {
                case 26:
                    return e;
                default:
                    int i4 = 61 / 0;
                    return e;
            }
        }

        d(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = a + 59;
            d = i % 128;
            switch (i % 2 == 0 ? (char) 16 : ';') {
                case ';':
                    Object[] objArr = new Object[1];
                    w(new int[]{-314350742, -598775265, -1271432552, -1494747017, 1271448343, -2054226598, 77013826, -1466234136}, 70 % TextUtils.getOffsetAfter("", 0), objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{-314350742, -598775265, -1271432552, -1494747017, 1271448343, -2054226598, 77013826, -1466234136}, TextUtils.getOffsetAfter("", 0) + 14, objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        private static o.cf.d e(Context context) {
            Object[] objArr = new Object[1];
            w(new int[]{-863315759, 1081946038, 482910198, -1362094376, 1080019705, 911290045, 762287045, 203578639, -2032656890, -1372977548}, 19 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
            o.cf.d dVar = new o.cf.d(context, 30, ((String) objArr[0]).intern());
            int i = d + 83;
            a = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(new int[]{-2072882892, -2116265917, -527971593, 2017363903}, 6 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
            bVar.d(((String) objArr[0]).intern(), ((c) e()).e.e());
            Object[] objArr2 = new Object[1];
            w(new int[]{869394885, -673523327, 1363454341, -353813732}, View.MeasureSpec.getMode(0) + 8, objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((c) e()).c);
            int i = a + 75;
            d = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        @Override // o.y.c
        public final j n() {
            int i = a + 23;
            int i2 = i % 128;
            d = i2;
            Object obj = null;
            switch (i % 2 != 0 ? (char) 20 : Typography.dollar) {
                case '$':
                    int i3 = i2 + 17;
                    a = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            return null;
                        default:
                            obj.hashCode();
                            throw null;
                    }
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a;
            int i2 = i + 13;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 69;
            d = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = a + 45;
            d = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case AuthApiStatusCodes.AUTH_API_ACCESS_FORBIDDEN /* 3001 */:
                    return o.bb.a.aE;
                case AuthApiStatusCodes.AUTH_API_CLIENT_ERROR /* 3002 */:
                    o.bb.a aVar = o.bb.a.aG;
                    int i4 = a + 11;
                    d = i4 % 128;
                    int i5 = i4 % 2;
                    return aVar;
                case 5002:
                    return o.bb.a.az;
                default:
                    return super.c(i);
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            c cVar = (c) e();
            Object[] objArr = new Object[1];
            w(new int[]{211448363, -670035996, 839134279, 1786666023, 419975280, -1784711226}, 9 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
            cVar.b = (h) bVar.b(h.class, ((String) objArr[0]).intern());
            g.c();
            Object[] objArr2 = new Object[1];
            w(new int[]{-1429173832, 1336235810, -1015779082, 1203102822, 1787627863, -2099744309, 421397118, 394137764, 1765164871, -254699700, 1340838245, 1419352873, -1507540815, -1895762006, -197686094, 1121935872, 2092991533, -1868850763}, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 33, objArr2);
            String intern = ((String) objArr2[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr3 = new Object[1];
            w(new int[]{1311764900, 546881476, -1015779082, 1203102822, -1766231544, 1990738995, -1213400659, -497705020, -1235193014, 1776487714, 672350913, -1243996574, 1166598806, -1610749743, 13149877, -784461665, -1591188011, 2122514079, 740683354, -980940420}, 39 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr3);
            g.d(intern, sb.append(((String) objArr3[0]).intern()).append(((c) e()).b.name()).toString());
            int i = d + 1;
            a = i % 128;
            switch (i % 2 != 0) {
                case false:
                    int i2 = 28 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0097. Please report as an issue. */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            g.c();
            Object[] objArr = new Object[1];
            w(new int[]{-1429173832, 1336235810, -1015779082, 1203102822, 1787627863, -2099744309, 421397118, 394137764, 1765164871, -254699700, 1340838245, 1419352873, -1507540815, -1895762006, -197686094, 1121935872, 2092991533, -1868850763}, Color.rgb(0, 0, 0) + 16777249, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            w(new int[]{-779857576, 177959359, -493069947, -98534261, -46011310, 48186325, 1066584972, 389133850, -1315564354, -872386634, -368278780, -2145438586, -1833518606, -2133758284, 1038000982, -2145482788, 250214032, -1661237476, -1235193014, 1776487714, -478569431, -2032176814, 1787355915, -334359025}, 45 - TextUtils.indexOf("", "", 0, 0), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(((c) e()).b).toString());
            switch (AnonymousClass4.e[((c) e()).b.ordinal()]) {
                case 1:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    w(new int[]{-1429173832, 1336235810, -1015779082, 1203102822, 1787627863, -2099744309, 421397118, 394137764, 1765164871, -254699700, 1340838245, 1419352873, -1507540815, -1895762006, -197686094, 1121935872, 2092991533, -1868850763}, 34 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    w(new int[]{-779857576, 177959359, -493069947, -98534261, -46011310, 48186325, 1066584972, 389133850, -1315564354, -872386634, -2147075702, -2061900719, -433834213, -1681123502, -300592045, -188503229, 1787355915, -334359025}, 33 - (ViewConfiguration.getEdgeSlop() >> 16), objArr4);
                    g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(((c) e()).e.e()).toString());
                    o.fk.d dVar = new o.fk.d();
                    o.fk.b a2 = f().a().a(g(), ((c) e()).e.e());
                    dVar.d(new o.fk.e());
                    dVar.d(a2);
                    i().a(dVar);
                    f().d(g());
                    break;
                case 2:
                    f().e(g(), ((c) e()).e.e());
                    int i = a + 91;
                    d = i % 128;
                    switch (i % 2 != 0 ? '+' : '6') {
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = a + 1;
            d = i % 128;
            boolean z = i % 2 != 0;
            ((c) e()).j().c();
            switch (z) {
                case true:
                    int i2 = 0 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = a + 85;
            d = i % 128;
            int i2 = i % 2;
            ((c) e()).j().a(dVar);
            int i3 = d + Opcodes.DNEG;
            a = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 28 : '=') {
                case 28:
                    throw null;
                default:
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int[] r23, int r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 824
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aw.c.d.w(int[], int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.aw.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aw\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            a = 1;
            int[] iArr = new int[h.values().length];
            e = iArr;
            try {
                iArr[h.a.ordinal()] = 1;
                int i = a + 5;
                b = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[h.d.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 11) + ((i3 & 11) << 1);
                a = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:118:0x02d2  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:120:0x02cc -> B:113:0x02ce). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 822
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aw.c.k(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
