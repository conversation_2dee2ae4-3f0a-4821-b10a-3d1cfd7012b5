package bc.org.bouncycastle.math.ec.custom.djb;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\djb\Curve25519FieldElement.smali */
public class Curve25519FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = w5.c(Curve25519Field.a);
    private static final int[] b = {1242472624, -991028441, -1389370248, 792926214, 1039914919, 726466713, 1338105611, 730014848};
    protected int[] a;

    public Curve25519FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for Curve25519FieldElement");
        }
        this.a = Curve25519Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        Curve25519Field.add(this.a, ((Curve25519FieldElement) eCFieldElement).a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = w5.a();
        Curve25519Field.addOne(this.a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        Curve25519Field.inv(((Curve25519FieldElement) eCFieldElement).a, a);
        Curve25519Field.multiply(a, this.a, a);
        return new Curve25519FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof Curve25519FieldElement) {
            return w5.b(this.a, ((Curve25519FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "Curve25519Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 8);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = w5.a();
        Curve25519Field.inv(this.a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return w5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return w5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        Curve25519Field.multiply(this.a, ((Curve25519FieldElement) eCFieldElement).a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = w5.a();
        Curve25519Field.negate(this.a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (w5.b(iArr) || w5.a(iArr)) {
            return this;
        }
        int[] a = w5.a();
        Curve25519Field.square(iArr, a);
        Curve25519Field.multiply(a, iArr, a);
        Curve25519Field.square(a, a);
        Curve25519Field.multiply(a, iArr, a);
        int[] a2 = w5.a();
        Curve25519Field.square(a, a2);
        Curve25519Field.multiply(a2, iArr, a2);
        int[] a3 = w5.a();
        Curve25519Field.squareN(a2, 3, a3);
        Curve25519Field.multiply(a3, a, a3);
        Curve25519Field.squareN(a3, 4, a);
        Curve25519Field.multiply(a, a2, a);
        Curve25519Field.squareN(a, 4, a3);
        Curve25519Field.multiply(a3, a2, a3);
        Curve25519Field.squareN(a3, 15, a2);
        Curve25519Field.multiply(a2, a3, a2);
        Curve25519Field.squareN(a2, 30, a3);
        Curve25519Field.multiply(a3, a2, a3);
        Curve25519Field.squareN(a3, 60, a2);
        Curve25519Field.multiply(a2, a3, a2);
        Curve25519Field.squareN(a2, 11, a3);
        Curve25519Field.multiply(a3, a, a3);
        Curve25519Field.squareN(a3, Opcodes.ISHL, a);
        Curve25519Field.multiply(a, a2, a);
        Curve25519Field.square(a, a);
        Curve25519Field.square(a, a2);
        if (w5.b(iArr, a2)) {
            return new Curve25519FieldElement(a);
        }
        Curve25519Field.multiply(a, b, a);
        Curve25519Field.square(a, a2);
        if (w5.b(iArr, a2)) {
            return new Curve25519FieldElement(a);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = w5.a();
        Curve25519Field.square(this.a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        Curve25519Field.subtract(this.a, ((Curve25519FieldElement) eCFieldElement).a, a);
        return new Curve25519FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return w5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return w5.c(this.a);
    }

    public Curve25519FieldElement() {
        this.a = w5.a();
    }

    protected Curve25519FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
