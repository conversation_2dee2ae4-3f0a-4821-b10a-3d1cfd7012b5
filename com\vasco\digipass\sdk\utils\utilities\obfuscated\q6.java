package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\q6.smali */
public class q6 extends u {
    private x C;
    private f0 L;
    private d R;
    private r b;
    private s0 x;

    private q6(e0 e0Var) {
        Enumeration j = e0Var.j();
        r a = r.a(j.nextElement());
        this.b = a;
        int a2 = a(a);
        this.x = s0.a(j.nextElement());
        this.C = x.a(j.nextElement());
        int i = -1;
        while (j.hasMoreElements()) {
            j0 j0Var = (j0) j.nextElement();
            int j2 = j0Var.j();
            if (j2 <= i) {
                throw new IllegalArgumentException("invalid optional field in private key info");
            }
            if (j2 == 0) {
                this.L = f0.a(j0Var, false);
            } else {
                if (j2 != 1) {
                    throw new IllegalArgumentException("unknown optional field in private key info");
                }
                if (a2 < 1) {
                    throw new IllegalArgumentException("'publicKey' requires version v2(1) or later");
                }
                this.R = d.a(j0Var, false);
            }
            i = j2;
        }
    }

    public static q6 a(Object obj) {
        if (obj instanceof q6) {
            return (q6) obj;
        }
        if (obj != null) {
            return new q6(e0.a(obj));
        }
        return null;
    }

    public x e() {
        return new f2(this.C.h());
    }

    public s0 f() {
        return this.x;
    }

    public int g() {
        return this.C.i();
    }

    public h h() throws IOException {
        return b0.a(this.C.h());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(5);
        iVar.a(this.b);
        iVar.a(this.x);
        iVar.a(this.C);
        f0 f0Var = this.L;
        if (f0Var != null) {
            iVar.a(new m2(false, 0, f0Var));
        }
        d dVar = this.R;
        if (dVar != null) {
            iVar.a(new m2(false, 1, dVar));
        }
        return new j2(iVar);
    }

    private static int a(r rVar) {
        int k = rVar.k();
        if (k < 0 || k > 1) {
            throw new IllegalArgumentException("invalid version for private key info");
        }
        return k;
    }
}
