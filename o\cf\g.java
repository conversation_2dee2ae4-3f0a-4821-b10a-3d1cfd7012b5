package o.cf;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.view.PointerIconCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.credentials.CredentialsApi;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\g.smali */
public final class g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final f a;
    private static char[] i;
    private static int k;
    private static boolean l;
    private static int m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static boolean f48o;
    private static int r;
    private final f b;
    private final o.eg.b c;
    private final o.eg.b d;
    private final b e;
    private final String f;
    private final String g;
    private final Long h;
    private final h j;

    static void i() {
        i = new char[]{61755, 61769, 61780, 61784, 61748, 61787, 61782, 61783, 61776, 61764, 61785, 61773, 61778, 61770, 61746, 61775, 61774, 61786, 61702, 61713, 61765, 61791, 61724, 61714, 61768, 61771, 61744, 61777, 61737, 61739, 61766, 61745, 61750, 61789, 61741, 61779, 61735};
        f48o = true;
        l = true;
        m = 782103014;
        k = 874635486;
    }

    static void init$0() {
        $$a = new byte[]{9, -87, 124, -45};
        $$b = Opcodes.FNEG;
    }

    private static void s(int i2, short s, short s2, Object[] objArr) {
        int i3 = (i2 * 4) + 4;
        int i4 = 1 - (s2 * 3);
        byte[] bArr = $$a;
        int i5 = s + 107;
        byte[] bArr2 = new byte[i4];
        int i6 = -1;
        int i7 = i4 - 1;
        if (bArr == null) {
            i3++;
            i5 = i3 + i5;
        }
        while (true) {
            int i8 = i5;
            int i9 = i3;
            i6++;
            bArr2[i6] = (byte) i8;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            } else {
                i3 = i9 + 1;
                i5 = i8 + bArr[i9];
            }
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        n = 0;
        r = 1;
        i();
        TextUtils.getCapsMode("", 0, 0);
        a = f.e;
        int i2 = r + 5;
        n = i2 % 128;
        int i3 = i2 % 2;
    }

    private g(b bVar, o.eg.b bVar2, o.eg.b bVar3, f fVar, h hVar, String str, String str2, Long l2) {
        this.e = bVar;
        this.d = bVar2;
        this.c = bVar3;
        this.b = fVar;
        this.j = hVar;
        this.f = str;
        this.g = str2;
        this.h = l2;
    }

    public static g c(o.eg.b bVar) {
        f fVar;
        Object[] objArr = new Object[1];
        p(null, 126 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), null, "\u0082\u0086\u0089\u0088\u0087\u0086\u0082\u0085\u0083\u0082\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        b bVar2 = new b();
        if (bVar == null) {
            o.ee.g.c();
            Object[] objArr2 = new Object[1];
            q(View.getDefaultSize(0, 0) + 11, "\u0011\u0015\u001a\uffc1\u000e\u0006\u0014\u0014\u0002\b\u0006\u0003\u0016\n\r\u0005￭\n\b\t\u0015\uffc1ￎ\uffc1\u0006\u000e", 26 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), TextUtils.getTrimmedLength("") + 256, false, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            bVar2.d(a.g);
            Process.myPid();
            SystemClock.uptimeMillis();
            Color.blue(0);
            return new g(bVar2, new o.eg.b(), new o.eg.b(), a, null, null, null, null);
        }
        o.ee.g.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        p(null, 127 - TextUtils.indexOf("", "", 0), null, "\u0093\u0097\u0093\u0095\u0092\u0095\u008e\u0093\u0096\u0095\u0083\u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr3);
        o.ee.g.d(intern, sb.append(((String) objArr3[0]).intern()).append(o.ee.g.a(bVar.b())).toString());
        Object[] objArr4 = new Object[1];
        p(null, 127 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), null, "\u0086\u008b\u0092\u0095\u0092\u0086", objArr4);
        f d = f.d(bVar.q(((String) objArr4[0]).intern()));
        switch (d == null ? '\\' : (char) 19) {
            case 19:
                fVar = d;
                break;
            default:
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                p(null, 128 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), null, "\u0082\u0089\u0088\u0093\u0092\u008d\u008b\u0095\u0099\u0082\u008e\u0093\u0082\u0091\u0092\u0093\u0090\u0089\u008c\u0092\u0092\u0082\u0086\u0093\u0098\u0082\u0086\u0089\u0088\u0087\u0086\u0082\u0083\u0093\u0083\u0082\u0084\u0083\u0082\u0086\u0093\u0089\u008c\u0093\u008e\u008c\u008d\u0095\u0084\u0089\u008c\u0093\u0083\u0088\u0093\u0092\u0089\u0082\u0086\u0082\u0083\u0087\u0093\u0092\u0088\u0089\u0093\u0086\u008b\u0092\u0095\u0092\u0086\u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr5);
                o.ee.g.d(intern, ((String) objArr5[0]).intern());
                fVar = a;
                break;
        }
        o.ee.g.c();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr6 = new Object[1];
        q((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 9, "ￆ\u0019\u001a\u0007\u001a\u001b\u0019ￆ￠ￆ\b\u001b\u000f\u0012\n\ufff2\u000f\r\u000e\u001aￆￓￆ\u0018\u000b\t\u000b\u000f\u001c\u000b\n", (ViewConfiguration.getFadingEdgeLength() >> 16) + 31, 251 - (ViewConfiguration.getFadingEdgeLength() >> 16), false, objArr6);
        o.ee.g.d(intern, sb2.append(((String) objArr6[0]).intern()).append(fVar.toString()).toString());
        Object[] objArr7 = new Object[1];
        p(null, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 127, null, "\u0082\u008e\u0088\u009a", objArr7);
        if (bVar.b(((String) objArr7[0]).intern())) {
            try {
                Object[] objArr8 = new Object[1];
                q((ViewConfiguration.getScrollBarSize() >> 8) + 6, "￼\n\n\ufff8\ufffe￼\u0004", Color.argb(0, 0, 0, 0) + 7, 265 - ((byte) KeyEvent.getModifierMetaStateMask()), false, objArr8);
                String intern2 = ((String) objArr8[0]).intern();
                Object[] objArr9 = new Object[1];
                p(null, 127 - (ViewConfiguration.getTapTimeout() >> 16), null, "\u0082\u0090\u0095\u0086\u0086\u0082\u009c\u0093\u0088\u009b", objArr9);
                String c = bVar.c(intern2, ((String) objArr9[0]).intern());
                o.ee.g.c();
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr10 = new Object[1];
                p(null, 127 - KeyEvent.keyCodeFromString(""), null, "\u009f\u0093\u0083\u0082\u0084\u0083\u0082\u0086\u0093\u009c\u0088\u0083\u0099\u0093\u008e\u0082\u0089\u0083\u008b\u0092\u0082\u0083\u0093\u0082\u008e\u0088\u009e\u0093\u0083\u0088\u0083\u0083\u009d\u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr10);
                StringBuilder append = sb3.append(((String) objArr10[0]).intern());
                Object[] objArr11 = new Object[1];
                p(null, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0082\u008e\u0088\u009a", objArr11);
                StringBuilder append2 = append.append(bVar.i(((String) objArr11[0]).intern()));
                Object[] objArr12 = new Object[1];
                p(null, 127 - View.MeasureSpec.getSize(0), null, "\u0093\u0094\u0093\u009f", objArr12);
                o.ee.g.e(intern, append2.append(((String) objArr12[0]).intern()).append(c).toString());
                Object[] objArr13 = new Object[1];
                p(null, 127 - TextUtils.getOffsetAfter("", 0), null, "\u0082\u008e\u0088\u009a", objArr13);
                switch (bVar.i(((String) objArr13[0]).intern()).intValue()) {
                    case 1000:
                        bVar2.d(a.m);
                        break;
                    case PointerIconCompat.TYPE_ALIAS /* 1010 */:
                        bVar2.d(a.t);
                        break;
                    case CredentialsApi.CREDENTIAL_PICKER_REQUEST_CODE /* 2000 */:
                        bVar2.d(a.s);
                        break;
                    case 2010:
                        bVar2.d(a.w);
                        int i2 = n + 47;
                        r = i2 % 128;
                        int i3 = i2 % 2;
                        break;
                    case 2020:
                        switch (fVar != f.a) {
                            case false:
                                bVar2.d(a.r);
                                break;
                            default:
                                bVar2.d(a.p);
                                break;
                        }
                    case 2030:
                        bVar2.d(a.v);
                        break;
                    case 2040:
                        bVar2.d(a.x);
                        break;
                    case 9999:
                        bVar2.d(a.q);
                        int i4 = n + 67;
                        r = i4 % 128;
                        if (i4 % 2 == 0) {
                        }
                        break;
                    default:
                        bVar2.d(a.l);
                        break;
                }
            } catch (o.eg.d e) {
                o.ee.g.c();
                Object[] objArr14 = new Object[1];
                p(null, TextUtils.getCapsMode("", 0, 0) + 127, null, "\u0082\u008e\u0088\u009e\u0093\u0083\u0088\u0083\u0083\u009d\u0093\u0090\u0089\u008c\u0086\u0086\u008c \u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr14);
                o.ee.g.a(intern, ((String) objArr14[0]).intern(), e);
                bVar2.d(a.g);
                SystemClock.currentThreadTimeMillis();
                ViewConfiguration.getMaximumFlingVelocity();
                ViewConfiguration.getMaximumDrawingCacheSize();
                return new g(bVar2, new o.eg.b(), new o.eg.b(), a, null, null, null, null);
            }
        }
        Object[] objArr15 = new Object[1];
        q(KeyEvent.getDeadChar(0, 0) + 11, "\u0007\f\n\uffff\uffe7\ufff8\u0010\u0003\u0006\ufff8\ufffb", 11 - (ViewConfiguration.getFadingEdgeLength() >> 16), 266 - View.MeasureSpec.getSize(0), false, objArr15);
        String q = bVar.q(((String) objArr15[0]).intern());
        if (q != null) {
            o.ee.g.c();
            StringBuilder sb4 = new StringBuilder();
            Object[] objArr16 = new Object[1];
            p(null, 127 - TextUtils.indexOf("", ""), null, "\u0093\u0097\u0082\u0086\u0089\u0088\u0087\u0086\u0082\u0083\u0093\u0083\u0082\u0084\u0083\u0082\u0086\u0093\u0089\u008c\u0093\u008e\u0082\u008e\u008b\u008d\u009a\u0089\u008c\u0093\u008e\u0095\u0088\u008d¢\u0095\u0087\u0093\u0091\u0086\u008b¡\u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr16);
            o.ee.g.d(intern, sb4.append(((String) objArr16[0]).intern()).append(q).toString());
        }
        Object[] objArr17 = new Object[1];
        q(8 - (ViewConfiguration.getWindowTouchSlop() >> 8), "�\u000b\u000b\u0001\u0007\u0006￡￼\b\r\u000b\u0000￫", 13 - TextUtils.getTrimmedLength(""), 265 - (Process.myPid() >> 22), false, objArr17);
        String q2 = bVar.q(((String) objArr17[0]).intern());
        if (q2 != null) {
            o.ee.g.c();
            StringBuilder sb5 = new StringBuilder();
            Object[] objArr18 = new Object[1];
            p(null, 127 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), null, "\u0093\u0097\u0082\u0086\u0089\u0088\u0087\u0086\u0082\u0083\u0093\u0083\u0082\u0084\u0083\u0082\u0086\u0093\u0089\u008c\u0093\u008e\u0082\u008e\u008b\u008d\u009a\u0089\u008c\u0093\u008e£\u0093\u0089\u0088\u008c\u0086\u0086\u0082\u0086\u0093\u0091\u0086\u008b¡\u0093\u0094\u0093\u0092\u0091\u0090\u008c\u008f\u008e\u008d\u008c\u008b\u008a", objArr18);
            o.ee.g.d(intern, sb5.append(((String) objArr18[0]).intern()).append(q2).toString());
        }
        if (fVar.equals(f.d)) {
            o.ee.g.c();
            Object[] objArr19 = new Object[1];
            p(null, 127 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), null, "\u008e\u0082\u0092\u0082\u008d\u0082\u008e\u0093\u0086\u0095\u0093\u008e\u0082\u008c\u0099\u008c\u0092\u0088\u0089\u0093\u0092\u0082\u008d\u008d\u0095\u0096", objArr19);
            o.ee.g.d(intern, ((String) objArr19[0]).intern());
            g gVar = new g(bVar2, new o.eg.b(), new o.eg.b(), fVar, null, q, q2, null);
            int i5 = n + 5;
            r = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    return gVar;
                default:
                    throw null;
            }
        }
        if (fVar.equals(f.a)) {
            Object[] objArr20 = new Object[1];
            p(null, 127 - TextUtils.indexOf("", ""), null, "\u0089\u0088\u0086\u0095\u0082\u0085¤\u009a\u0088\u008d", objArr20);
            bVar2.e(o.bg.d.a(bVar.e(((String) objArr20[0]).intern(), (Integer) 9999).intValue()));
            Object[] objArr21 = new Object[1];
            p(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u0082\u0090\u0095\u0086\u0086\u0082 ¤\u009a\u0088\u008d", objArr21);
            bVar.q(((String) objArr21[0]).intern());
            o.ee.g.c();
            StringBuilder sb6 = new StringBuilder();
            Object[] objArr22 = new Object[1];
            q(5 - View.resolveSizeAndState(0, 0, 0), "\f\u0013\u0013\b\u001eￇ￡ￇ\u0015\u0016\u001a\b\f\u0019ￇ\u0012\n\u0016\u0013ￇￔￇ\u000b\f\u0012\n\u0016\u0013ￇ\u001a\bￇ\u000b\f\u0010\r\u0010\u001b\u0016\u0015ￇ\u001b", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 41, 250 - View.MeasureSpec.getMode(0), true, objArr22);
            o.ee.g.d(intern, sb6.append(((String) objArr22[0]).intern()).append(bVar2.b()).toString());
        }
        return new g(bVar2, new o.eg.b(), new o.eg.b(), fVar, null, q, q2, null);
    }

    /* JADX WARN: Removed duplicated region for block: B:71:0x0578  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static o.cf.g a(android.content.Context r29, o.eg.b r30, int r31, java.lang.String r32) {
        /*
            Method dump skipped, instructions count: 1466
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.g.a(android.content.Context, o.eg.b, int, java.lang.String):o.cf.g");
    }

    public final b b() {
        int i2 = r + 77;
        int i3 = i2 % 128;
        n = i3;
        int i4 = i2 % 2;
        b bVar = this.e;
        int i5 = i3 + 3;
        r = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 1 : 'O') {
            case 1:
                throw null;
            default:
                return bVar;
        }
    }

    public final o.eg.b d() {
        int i2 = n + 35;
        r = i2 % 128;
        switch (i2 % 2 == 0 ? '\f' : 'F') {
            case '\f':
                throw null;
            default:
                return this.d;
        }
    }

    public final String a() {
        int i2 = n + 77;
        int i3 = i2 % 128;
        r = i3;
        switch (i2 % 2 == 0 ? (char) 27 : '4') {
            case '4':
                String str = this.f;
                int i4 = i3 + 65;
                n = i4 % 128;
                int i5 = i4 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String c() {
        int i2 = r;
        int i3 = i2 + 1;
        n = i3 % 128;
        int i4 = i3 % 2;
        String str = this.g;
        int i5 = i2 + 45;
        n = i5 % 128;
        switch (i5 % 2 != 0 ? 'S' : '+') {
            case '+':
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final o.eg.b e() {
        int i2 = n + 45;
        int i3 = i2 % 128;
        r = i3;
        int i4 = i2 % 2;
        o.eg.b bVar = this.c;
        int i5 = i3 + 13;
        n = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return bVar;
            default:
                int i6 = 91 / 0;
                return bVar;
        }
    }

    public final f j() {
        int i2 = r + 77;
        n = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 27 / 0;
                return this.b;
            default:
                return this.b;
        }
    }

    public final h h() {
        int i2 = r + 71;
        int i3 = i2 % 128;
        n = i3;
        switch (i2 % 2 == 0) {
            case true:
                h hVar = this.j;
                int i4 = i3 + 15;
                r = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return hVar;
                    default:
                        int i5 = 61 / 0;
                        return hVar;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final Long f() {
        int i2 = n;
        int i3 = i2 + 71;
        r = i3 % 128;
        int i4 = i3 % 2;
        Long l2 = this.h;
        int i5 = i2 + 41;
        r = i5 % 128;
        int i6 = i5 % 2;
        return l2;
    }

    public final int d(String str) {
        int i2 = r + 67;
        n = i2 % 128;
        Object obj = null;
        try {
            switch (i2 % 2 != 0 ? (char) 22 : '`') {
                case 22:
                    this.c.b(str);
                    throw null;
                default:
                    switch (this.c.b(str) ? (char) 23 : '0') {
                        case 23:
                            int i3 = n + 73;
                            r = i3 % 128;
                            int i4 = i3 % 2;
                            o.eg.b v = this.c.v(str);
                            Object[] objArr = new Object[1];
                            p(null, 128 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), null, "\u0082\u008e\u0088\u009e\u0082\u0086\u0089\u0088\u0087\u0086\u0082\u0083", objArr);
                            int intValue = v.e(((String) objArr[0]).intern(), (Integer) (-1)).intValue();
                            int i5 = r + 55;
                            n = i5 % 128;
                            if (i5 % 2 == 0) {
                                return intValue;
                            }
                            obj.hashCode();
                            throw null;
                        default:
                            return -1;
                    }
            }
        } catch (o.eg.d e) {
            return -1;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v20, types: [byte[]] */
    private static void p(java.lang.String r16, int r17, int[] r18, java.lang.String r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 686
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.g.p(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 508
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.g.q(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
