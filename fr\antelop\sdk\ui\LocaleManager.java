package fr.antelop.sdk.ui;

import android.content.Context;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\ui\LocaleManager.smali */
public final class LocaleManager {
    private static final LocaleManager ourInstance = new LocaleManager();
    private Context localizedContext = null;

    public static LocaleManager getInstance() {
        return ourInstance;
    }

    private LocaleManager() {
    }

    public final void applyLocale(Context context) {
        this.localizedContext = context;
    }

    public final Context getLocalizedContext(Context context) {
        Context context2 = this.localizedContext;
        return context2 == null ? context : context2;
    }
}
