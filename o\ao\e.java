package o.ao;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.m;
import o.a.o;
import o.cf.i;
import o.ee.g;
import o.eo.j;
import o.h.d;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ao\e.smali */
public final class e extends b<a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static int g;
    private static int h;
    private static char i;
    private static long j;
    d a;
    o.eo.e b;
    j c;
    String d;
    c e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ao\e$a.smali */
    public interface a {
        void c();

        void c(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        n();
        KeyEvent.getModifierMetaStateMask();
        TypedValue.complexToFloat(0);
        TextUtils.getTrimmedLength("");
        TextUtils.lastIndexOf("", '0', 0, 0);
        int i2 = f + 99;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{73, -116, 106, -1};
        $$e = Opcodes.IXOR;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = 106 - r8
            byte[] r0 = o.ao.e.$$d
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r6
            r5 = r2
            goto L26
        L14:
            r3 = r2
        L15:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L24
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L24:
            r3 = r0[r6]
        L26:
            int r6 = r6 + 1
            int r3 = -r3
            int r8 = r8 + r3
            r3 = r5
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ao.e.m(int, int, byte, java.lang.Object[]):void");
    }

    static void n() {
        i = (char) 17957;
        g = 1844686293;
        j = 6565854932352255525L;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i2 = h + 53;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                AsyncTaskC0026e l = l();
                int i3 = h + 97;
                f = i3 % 128;
                int i4 = i3 % 2;
                return l;
            default:
                l();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public e(Context context, a aVar, o.ei.c cVar) {
        super(context, aVar, cVar, o.bb.e.x);
    }

    public final void b(d dVar, String str, c cVar, o.eo.e eVar, j jVar) {
        g.c();
        Object[] objArr = new Object[1];
        k(1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "ꠣᦩ航쮦똜嫊嗢痀꾊进탽\ude8fཆ䷯҄뚴", (char) (38247 - TextUtils.indexOf("", "")), "噏曉杄꒕", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k(Process.myPid() >> 22, "䚓蘤꿤濵䷗璎盏酴ᛜෲ\ue746ꂕ\udab6\ue916躣Ꮏ矗惚\uefda醪\ue71cን짝挧筬趲\ue5af戃歨泭﴿죭ﱹ㙉ɝĩ", (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), "廄\uf31e뱗\ue0c6", "\u0000\u0000\u0000\u0000", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar.e()).toString());
        this.a = dVar;
        this.d = str;
        this.e = cVar;
        this.b = eVar;
        this.c = jVar;
        c();
        int i2 = f + 49;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    private AsyncTaskC0026e l() {
        AsyncTaskC0026e asyncTaskC0026e = new AsyncTaskC0026e(this);
        int i2 = h + 67;
        f = i2 % 128;
        switch (i2 % 2 == 0 ? 'T' : '=') {
            case Opcodes.BASTORE /* 84 */:
                throw null;
            default:
                return asyncTaskC0026e;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i2 = f + 85;
        h = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k(Drawable.resolveOpacity(0, 0), "ꠣᦩ航쮦똜嫊嗢痀꾊进탽\ude8fཆ䷯҄뚴", (char) (View.MeasureSpec.getMode(0) + 38247), "噏曉杄꒕", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = h + 77;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? '6' : '(') {
            case Opcodes.ISTORE /* 54 */:
                int i5 = 32 / 0;
                return intern;
            default:
                return intern;
        }
    }

    /* renamed from: o.ao.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ao\e$e.smali */
    static final class AsyncTaskC0026e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static long c;
        private static int d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            d = 1;
            c = 3764310348998357110L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(int r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 + 4
                int r7 = r7 * 3
                int r7 = 1 - r7
                int r8 = r8 * 2
                int r8 = r8 + 112
                byte[] r0 = o.ao.e.AsyncTaskC0026e.$$d
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r4 = r8
                r3 = r2
                r8 = r7
                goto L2e
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r6 = r6 + 1
                if (r3 != r7) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r5
            L2e:
                int r7 = r7 + r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ao.e.AsyncTaskC0026e.B(int, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{48, 67, 97, 27};
            $$e = Opcodes.IXOR;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 45;
            a = i % 128;
            switch (i % 2 == 0) {
                case true:
                    break;
                default:
                    int i2 = 53 / 0;
                    break;
            }
        }

        AsyncTaskC0026e(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = d + Opcodes.LSUB;
            a = i % 128;
            switch (i % 2 != 0 ? 'K' : '*') {
                case '*':
                    Object[] objArr = new Object[1];
                    w("皋≺\udf72衰╵\ude72譞⑮텠", (ViewConfiguration.getFadingEdgeLength() >> 16) + 21757, objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w("皋≺\udf72衰╵\ude72譞⑮텠", (ViewConfiguration.getFadingEdgeLength() >> Opcodes.ISHL) * 19390, objArr2);
                    obj = objArr2[0];
                    break;
            }
            String intern = ((String) obj).intern();
            int i2 = a + 25;
            d = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    return intern;
                default:
                    Object obj2 = null;
                    obj2.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("盗碌椏\ud912䥫른⥈饝ম禴\ue989妕짧㧵꧀᧔蠢\uf809", 36848 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
            o.cf.d dVar = new o.cf.d(context, 38, ((String) objArr[0]).intern());
            int i = d + Opcodes.DMUL;
            a = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("皇ꀸ\udbe8\uf2b8ⱽ䜹", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 54973, objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).e.e());
            o.eg.b bVar2 = new o.eg.b();
            Object[] objArr2 = new Object[1];
            w("皏䯭", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 15727, objArr2);
            bVar2.d(((String) objArr2[0]).intern(), ((e) e()).c.e());
            Object[] objArr3 = new Object[1];
            w("皅㐲\uf3fe뺝类㬋", Process.getGidForName("") + 17078, objArr3);
            bVar2.d(((String) objArr3[0]).intern(), ((e) e()).b.e());
            Object[] objArr4 = new Object[1];
            w("皐簰揢嚴屓䌌", 2741 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr4);
            bVar2.d(((String) objArr4[0]).intern(), ((e) e()).c.h());
            Object[] objArr5 = new Object[1];
            w("皐岖⊮", 10771 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr5);
            bVar.d(((String) objArr5[0]).intern(), bVar2);
            int i = d + Opcodes.LSHR;
            a = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.cf.j n() {
            int i = d + 89;
            a = i % 128;
            int i2 = i % 2;
            if (((e) e()).a != null) {
                return new o.cf.j(((e) e()).d, true, ((e) e()).a);
            }
            int i3 = d + 31;
            a = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    int i4 = 35 / 0;
                    return null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a + 11;
            d = i % 128;
            Object obj = null;
            switch (i % 2 == 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = d + Opcodes.DREM;
            a = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    o.bb.a aVar = o.bb.a.az;
                    int i4 = a + Opcodes.DSUB;
                    d = i4 % 128;
                    int i5 = i4 % 2;
                    return aVar;
                default:
                    return super.c(i);
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = d + 69;
            a = i % 128;
            int i2 = i % 2;
            f().j().e(g(), ((e) e()).b.e(), ((e) e()).c, ((e) e()).e.d());
            int i3 = a + 17;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = d + 7;
            a = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass2.e[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((e) e()).b.e());
                    break;
                case 2:
                    f().e(g(), ((e) e()).b.e());
                    int i3 = a + 71;
                    d = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                default:
                    super.t();
                    break;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + Opcodes.LNEG;
            a = i % 128;
            switch (i % 2 == 0) {
                case false:
                    ((e) e()).j().c();
                    int i2 = 36 / 0;
                    break;
                default:
                    ((e) e()).j().c();
                    break;
            }
            int i3 = d + Opcodes.LREM;
            a = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = a + Opcodes.LREM;
            d = i % 128;
            switch (i % 2 == 0 ? 'R' : '2') {
                case Opcodes.DASTORE /* 82 */:
                    ((e) e()).j().c(dVar);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    ((e) e()).j().c(dVar);
                    int i2 = a + Opcodes.DNEG;
                    d = i2 % 128;
                    switch (i2 % 2 == 0 ? 'P' : '9') {
                        case 'P':
                            int i3 = 15 / 0;
                            return;
                        default:
                            return;
                    }
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r18, int r19, java.lang.Object[] r20) {
            /*
                Method dump skipped, instructions count: 476
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ao.e.AsyncTaskC0026e.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ao\e$c.smali */
    public static final class c implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final c b;
        private static final /* synthetic */ c[] c;
        public static final c d;
        public static final c e;
        private static int g;
        private static char h;
        private static char[] i;
        private static int j;
        private final String a;

        static void a() {
            i = new char[]{30556, 30591, 30559, 30554, 30583, 30561, 30552, 30570, 30553, 30587, 30539, 30563, 30574, 30538, 30572, 30557, 30586, 30555, 30530, 30571, 30529, 30558, 30531, 30511, 30517};
            h = (char) 17040;
        }

        static void init$0() {
            $$a = new byte[]{5, 125, -30, 121};
            $$b = Opcodes.ANEWARRAY;
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Type inference failed for: r8v1, types: [int] */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 4
                int r6 = 1 - r6
                int r7 = r7 * 3
                int r7 = 4 - r7
                int r8 = 73 - r8
                byte[] r0 = o.ao.e.c.$$a
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r4 = r8
                r3 = r2
                r8 = r7
                goto L2c
            L17:
                r3 = r2
                r5 = r8
                r8 = r7
                r7 = r5
            L1b:
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                int r3 = r3 + 1
                r4 = r0[r8]
            L2c:
                int r4 = -r4
                int r7 = r7 + r4
                int r8 = r8 + 1
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ao.e.c.k(byte, byte, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ c[] b() {
            int i2 = g + Opcodes.LSUB;
            j = i2 % 128;
            switch (i2 % 2 != 0 ? '+' : ',') {
                case '+':
                    c[] cVarArr = new c[2];
                    cVarArr[1] = d;
                    cVarArr[1] = b;
                    cVarArr[5] = e;
                    return cVarArr;
                default:
                    return new c[]{d, b, e};
            }
        }

        public static c valueOf(String str) {
            int i2 = j + 71;
            g = i2 % 128;
            boolean z = i2 % 2 == 0;
            c cVar = (c) Enum.valueOf(c.class, str);
            switch (z) {
                case true:
                    throw null;
                default:
                    int i3 = g + 3;
                    j = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case true:
                            return cVar;
                        default:
                            int i4 = 46 / 0;
                            return cVar;
                    }
            }
        }

        public static c[] values() {
            int i2 = j + 41;
            g = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    int i3 = 90 / 0;
                    return (c[]) c.clone();
                default:
                    return (c[]) c.clone();
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            g = 1;
            a();
            Object[] objArr = new Object[1];
            f((ViewConfiguration.getDoubleTapTimeout() >> 16) + 6, "\u0012\n\u0001\u0004\u0017\u0012", (byte) (105 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 6, "\u0012\n\u0001\u0004\u0017\u0012", (byte) (MotionEvent.axisFromString("") + Opcodes.FMUL), objArr2);
            d = new c(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f((Process.myTid() >> 22) + 7, "\u0001\u0004\u0001\u0003\n\u0017㘢", (byte) (68 - ExpandableListView.getPackedPositionGroup(0L)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            f(7 - Color.argb(0, 0, 0, 0), "\u0001\u0004\u0001\u0003\n\u0017㘢", (byte) (68 - (ViewConfiguration.getFadingEdgeLength() >> 16)), objArr4);
            b = new c(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            f(6 - Color.red(0), "\u000b\u000e\u0017\f\u0012\f", (byte) (Color.blue(0) + 21), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            f(6 - (ViewConfiguration.getWindowTouchSlop() >> 8), "\u000b\u000e\u0017\f\u0012\f", (byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 20), objArr6);
            e = new c(intern3, 2, ((String) objArr6[0]).intern());
            c = b();
            int i2 = j + 77;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        private c(String str, int i2, String str2) {
            this.a = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = j + 37;
            g = i2 % 128;
            switch (i2 % 2 == 0 ? (char) 17 : (char) 29) {
                case 17:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return this.a;
            }
        }

        public final j.b d() {
            int i2 = j + 53;
            g = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    int i3 = AnonymousClass2.a[ordinal()];
                    throw null;
                default:
                    switch (AnonymousClass2.a[ordinal()]) {
                        case 1:
                            j.b bVar = j.b.a;
                            int i4 = g + 79;
                            j = i4 % 128;
                            switch (i4 % 2 != 0 ? 'C' : '/') {
                                case 'C':
                                    int i5 = 53 / 0;
                                    return bVar;
                                default:
                                    return bVar;
                            }
                        case 2:
                            return j.b.b;
                        case 3:
                            return j.b.d;
                        default:
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            f(Color.rgb(0, 0, 0) + 16777234, "\u0000\b\t\u0002\u0002\u0006\u0013\u000e\t\u0011\u0003\r\r\f\u0011\u0006\u0014\u0018", (byte) (41 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), objArr);
                            throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                    }
            }
        }

        private static void f(int i2, String str, byte b2, Object[] objArr) {
            int i3;
            int i4 = $11 + 43;
            $10 = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    char[] charArray = str != null ? str.toCharArray() : str;
                    m mVar = new m();
                    char[] cArr = i;
                    int i5 = -1401577988;
                    int i6 = 8;
                    if (cArr != null) {
                        int length = cArr.length;
                        char[] cArr2 = new char[length];
                        int i7 = 0;
                        while (true) {
                            switch (i7 >= length) {
                                case true:
                                    cArr = cArr2;
                                    break;
                                default:
                                    try {
                                        Object[] objArr2 = {Integer.valueOf(cArr[i7])};
                                        Object obj = o.e.a.s.get(Integer.valueOf(i5));
                                        if (obj == null) {
                                            Class cls = (Class) o.e.a.c((ViewConfiguration.getScrollBarSize() >> i6) + 17, (char) TextUtils.getOffsetBefore("", 0), 77 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)));
                                            byte b3 = (byte) 0;
                                            byte b4 = b3;
                                            Object[] objArr3 = new Object[1];
                                            k(b3, b4, b4, objArr3);
                                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                            o.e.a.s.put(-1401577988, obj);
                                        }
                                        cArr2[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                        i7++;
                                        i5 = -1401577988;
                                        i6 = 8;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                            }
                        }
                    }
                    try {
                        Object[] objArr4 = {Integer.valueOf(h)};
                        Object obj2 = o.e.a.s.get(-1401577988);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(TextUtils.getOffsetBefore("", 0) + 17, (char) (ImageFormat.getBitsPerPixel(0) + 1), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 76);
                            byte b5 = (byte) 0;
                            byte b6 = b5;
                            Object[] objArr5 = new Object[1];
                            k(b5, b6, b6, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj2);
                        }
                        char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                        char[] cArr3 = new char[i2];
                        if (i2 % 2 != 0) {
                            i3 = i2 - 1;
                            cArr3[i3] = (char) (charArray[i3] - b2);
                        } else {
                            i3 = i2;
                        }
                        if (i3 > 1) {
                            int i8 = $10 + 83;
                            $11 = i8 % 128;
                            int i9 = i8 % 2;
                            mVar.b = 0;
                            while (true) {
                                switch (mVar.b >= i3) {
                                    case false:
                                        mVar.e = charArray[mVar.b];
                                        mVar.a = charArray[mVar.b + 1];
                                        if (mVar.e == mVar.a) {
                                            int i10 = $11 + 35;
                                            $10 = i10 % 128;
                                            int i11 = i10 % 2;
                                            cArr3[mVar.b] = (char) (mVar.e - b2);
                                            cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                                        } else {
                                            try {
                                                Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                                Object obj3 = o.e.a.s.get(696901393);
                                                if (obj3 == null) {
                                                    Class cls3 = (Class) o.e.a.c(KeyEvent.normalizeMetaState(0) + 10, (char) (8856 - TextUtils.indexOf("", "", 0, 0)), ((byte) KeyEvent.getModifierMetaStateMask()) + 325);
                                                    byte b7 = (byte) 0;
                                                    Object[] objArr7 = new Object[1];
                                                    k(b7, b7, (byte) $$a.length, objArr7);
                                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(696901393, obj3);
                                                }
                                                if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() != mVar.h) {
                                                    switch (mVar.c == mVar.d) {
                                                        case false:
                                                            int i12 = (mVar.c * charValue) + mVar.h;
                                                            int i13 = (mVar.d * charValue) + mVar.i;
                                                            cArr3[mVar.b] = cArr[i12];
                                                            cArr3[mVar.b + 1] = cArr[i13];
                                                            break;
                                                        default:
                                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                            int i14 = (mVar.c * charValue) + mVar.i;
                                                            int i15 = (mVar.d * charValue) + mVar.h;
                                                            cArr3[mVar.b] = cArr[i14];
                                                            cArr3[mVar.b + 1] = cArr[i15];
                                                            break;
                                                    }
                                                } else {
                                                    try {
                                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                        Object obj4 = o.e.a.s.get(1075449051);
                                                        if (obj4 == null) {
                                                            Class cls4 = (Class) o.e.a.c(AndroidCharacter.getMirror('0') - '%', (char) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 64);
                                                            byte b8 = (byte) 0;
                                                            byte b9 = b8;
                                                            Object[] objArr9 = new Object[1];
                                                            k(b8, b9, (byte) (b9 + 3), objArr9);
                                                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                            o.e.a.s.put(1075449051, obj4);
                                                        }
                                                        int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                        int i16 = (mVar.d * charValue) + mVar.h;
                                                        cArr3[mVar.b] = cArr[intValue];
                                                        cArr3[mVar.b + 1] = cArr[i16];
                                                    } catch (Throwable th2) {
                                                        Throwable cause2 = th2.getCause();
                                                        if (cause2 == null) {
                                                            throw th2;
                                                        }
                                                        throw cause2;
                                                    }
                                                }
                                            } catch (Throwable th3) {
                                                Throwable cause3 = th3.getCause();
                                                if (cause3 == null) {
                                                    throw th3;
                                                }
                                                throw cause3;
                                            }
                                        }
                                        mVar.b += 2;
                                }
                            }
                        }
                        int i17 = $10 + 87;
                        $11 = i17 % 128;
                        int i18 = i17 % 2;
                        int i19 = 0;
                        while (i19 < i2) {
                            int i20 = $11 + 81;
                            $10 = i20 % 128;
                            if (i20 % 2 != 0) {
                                cArr3[i19] = (char) (cArr3[i19] ^ 2377);
                                i19 += 4;
                            } else {
                                cArr3[i19] = (char) (cArr3[i19] ^ 13722);
                                i19++;
                            }
                        }
                        objArr[0] = new String(cArr3);
                        return;
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                default:
                    Object obj5 = null;
                    obj5.hashCode();
                    throw null;
            }
        }
    }

    /* renamed from: o.ao.e$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ao\e$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            c = 1;
            int[] iArr = new int[c.values().length];
            a = iArr;
            try {
                iArr[c.d.ordinal()] = 1;
                int i = c;
                int i2 = ((i | 1) << 1) - (i ^ 1);
                b = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[c.b.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[c.e.ordinal()] = 3;
                int i4 = (b + Opcodes.FMUL) - 1;
                c = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
            int[] iArr2 = new int[o.bb.a.values().length];
            e = iArr2;
            try {
                iArr2[o.bb.a.ay.ordinal()] = 1;
                int i6 = c;
                int i7 = (i6 ^ 39) + ((i6 & 39) << 1);
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[o.bb.a.az.ordinal()] = 2;
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    private static void k(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] charArray;
        char[] cArr;
        char[] cArr2;
        char c3;
        switch (str3 != null) {
            case true:
                charArray = str3.toCharArray();
                break;
            default:
                charArray = str3;
                break;
        }
        char[] cArr3 = charArray;
        Object obj = null;
        switch (str2 != null ? 'Z' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                cArr = str2;
                break;
            default:
                int i3 = $10 + 29;
                $11 = i3 % 128;
                switch (i3 % 2 == 0 ? '?' : 'Q') {
                    case '?':
                        str2.toCharArray();
                        throw null;
                    default:
                        cArr = str2.toCharArray();
                        break;
                }
        }
        char[] cArr4 = cArr;
        if (str != null) {
            int i4 = $11 + 49;
            $10 = i4 % 128;
            if (i4 % 2 != 0) {
                str.toCharArray();
                obj.hashCode();
                throw null;
            }
            cArr2 = str.toCharArray();
        } else {
            cArr2 = str;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i2));
        int length3 = cArr2.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj2 = o.e.a.s.get(-429442487);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, (char) (20955 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 344 - TextUtils.indexOf("", "", 0));
                    byte b = (byte) ($$d[3] + 1);
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    m(b, b2, (byte) (b2 | 7), objArr3);
                    obj2 = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj2);
                }
                int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj3 = o.e.a.s.get(-515165572);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c(Color.alpha(0) + 10, (char) ExpandableListView.getPackedPositionType(0L), 206 - TextUtils.indexOf((CharSequence) "", '0', 0));
                        byte b3 = (byte) ($$d[3] + 1);
                        byte b4 = b3;
                        Object[] objArr5 = new Object[1];
                        m(b3, b4, (byte) (b4 + 5), objArr5);
                        obj3 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj3);
                    }
                    int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr5[oVar.e % 4] * 32718), Integer.valueOf(cArr6[intValue])};
                        Object obj4 = o.e.a.s.get(-1614232674);
                        if (obj4 == null) {
                            Class cls3 = (Class) o.e.a.c(10 - MotionEvent.axisFromString(""), (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 281 - TextUtils.indexOf("", "", 0));
                            byte b5 = (byte) ($$d[3] + 1);
                            byte b6 = b5;
                            Object[] objArr7 = new Object[1];
                            m(b5, b6, (byte) (b6 + 3), objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj5 = o.e.a.s.get(406147795);
                            if (obj5 != null) {
                                c3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(19 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) (View.resolveSizeAndState(0, 0, 0) + 14687), AndroidCharacter.getMirror('0') + '@');
                                byte b7 = (byte) ($$d[3] + 1);
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                m(b7, b8, b8, objArr9);
                                c3 = 2;
                                obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj5);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((int) (g ^ 6565854932352255525L)) ^ ((cArr5[intValue2] ^ r6[oVar.e]) ^ (j ^ 6565854932352255525L))) ^ ((char) (i ^ 6565854932352255525L)));
                            oVar.e++;
                            cArr5 = cArr5;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
