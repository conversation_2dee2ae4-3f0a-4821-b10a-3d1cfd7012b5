package bc.org.bouncycastle.crypto.digests;

import bc.org.bouncycastle.crypto.Digest;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\g.smali */
class g {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\g$a.smali */
    private static class a implements p1 {
        private final int a;
        private final String b;
        private final q1 c;

        public a(int i, String str, q1 q1Var) {
            this.a = i;
            this.b = str;
            this.c = q1Var;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\g$b.smali */
    private static class b implements p1 {
        private final int a;
        private final int b;
        private final String c;
        private final q1 d;

        public b(int i, int i2, String str, q1 q1Var) {
            this.a = i;
            this.b = i2;
            this.c = str;
            this.d = q1Var;
        }
    }

    static p1 a(Digest digest, q1 q1Var) {
        return new a(digest.getDigestSize() * 4, digest.getAlgorithmName(), q1Var);
    }

    static p1 a(Digest digest, int i, q1 q1Var) {
        return new b(digest.getDigestSize() * 4, i, digest.getAlgorithmName(), q1Var);
    }
}
