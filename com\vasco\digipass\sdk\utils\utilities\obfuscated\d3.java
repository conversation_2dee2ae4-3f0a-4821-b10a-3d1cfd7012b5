package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d3.smali */
public class d3 extends e0 {
    private int C;

    public d3() {
        this.C = -1;
    }

    private int p() throws IOException {
        if (this.C < 0) {
            int length = this.b.length;
            int i = 0;
            for (int i2 = 0; i2 < length; i2++) {
                i += this.b[i2].toASN1Primitive().g().a(true);
            }
            this.C = i;
        }
        return this.C;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        return z.a(z, p());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    d k() {
        return new y2(u0.a(h()), false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    l l() {
        return new a3(this);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    x m() {
        return new f2(x0.a(i()));
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    f0 n() {
        return new f3(false, o());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 48);
        c3 c = zVar.c();
        int length = this.b.length;
        int i = 0;
        if (this.C >= 0 || length > 16) {
            zVar.d(p());
            while (i < length) {
                c.a(this.b[i].toASN1Primitive(), true);
                i++;
            }
            return;
        }
        b0[] b0VarArr = new b0[length];
        int i2 = 0;
        for (int i3 = 0; i3 < length; i3++) {
            b0 g = this.b[i3].toASN1Primitive().g();
            b0VarArr[i3] = g;
            i2 += g.a(true);
        }
        this.C = i2;
        zVar.d(i2);
        while (i < length) {
            c.a(b0VarArr[i], true);
            i++;
        }
    }

    public d3(h hVar) {
        super(hVar);
        this.C = -1;
    }

    public d3(i iVar) {
        super(iVar);
        this.C = -1;
    }

    d3(h[] hVarArr, boolean z) {
        super(hVarArr, z);
        this.C = -1;
    }
}
