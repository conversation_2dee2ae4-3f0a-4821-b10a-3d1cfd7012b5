package fr.antelop.sdk;

import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\WalletProvisioningCallback.smali */
public interface WalletProvisioningCallback {
    void onCheckEligibilityError(AntelopError antelopError, Object obj);

    void onDeviceEligible(boolean z, List<Product> list, Object obj);

    void onDeviceNotEligible(EligibilityDenialReason eligibilityDenialReason, Object obj, String str);

    void onInitializationError(AntelopError antelopError, Object obj);

    void onInitializationSuccess(Object obj);

    void onProvisioningError(AntelopError antelopError, Object obj);

    void onProvisioningPending(Object obj);

    void onProvisioningSuccess(Object obj);

    @Deprecated
    default void onPermissionNotGranted(String[] strArr, Object obj) {
    }
}
