package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.drawable.Drawable;
import fr.antelop.sdk.CancellationSignal;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.dw.a;
import o.ee.o;
import o.p.e;
import o.v.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecurePinDisplay.smali */
public final class SecurePinDisplay implements CustomerAuthenticatedProcess {
    private CustomerAuthenticatedProcessActivityCallback activityCallback;
    private Drawable cardDrawable = null;
    private final i<a> innerSecureDigitalCardDisplay;

    public SecurePinDisplay(i<a> iVar) {
        this.innerSecureDigitalCardDisplay = iVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardDisplay.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardDisplay.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardDisplay.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardDisplay.k();
    }

    public final SecurePinDisplay setCardBackground(Drawable drawable) {
        this.cardDrawable = drawable;
        return this;
    }

    public final SecurePinDisplay setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.activityCallback = customerAuthenticatedProcessActivityCallback;
        return this;
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        i<a> iVar = this.innerSecureDigitalCardDisplay;
        e eVar = new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardDisplay);
        this.innerSecureDigitalCardDisplay.a();
        iVar.e(context, eVar, new a());
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        i<a> iVar = this.innerSecureDigitalCardDisplay;
        o.p.i iVar2 = new o.p.i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardDisplay);
        this.innerSecureDigitalCardDisplay.a();
        iVar.e(context, iVar2, new a());
    }

    public final CancellationSignal getCancellationSignal() {
        return this.innerSecureDigitalCardDisplay.s();
    }

    public final String getMessage() {
        return null;
    }
}
