package fr.antelop.sdk.card;

import o.ac.b;
import o.ea.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali_classes17\fr\antelop\sdk\card\CreateCardRequestBuilder.smali */
public final class CreateCardRequestBuilder {
    private String bin;
    private int binLength;
    private String cardholderName;
    private String cvx2;
    private String expiryDate;
    private String financialAccountLabel;
    private String financialAccountNumber;
    private String idemiaAuthCode;
    private byte[] idemiaCipheredCardInformation;
    private String issuerCardId;
    private String issuerData;
    private String lastDigits;
    private byte[] mdesFundingAccountInfo;
    private String mdesPushAccountReceipt;
    private byte[] mdesTav;
    private String pan;
    private CreateCardRequestPanSource panSource;
    private boolean requireTermsAndConditionsApproval;
    private e secureCvx2;
    private e securePan;
    private byte[] vtsEncPaymentInstrument;

    public final CreateCardRequestBuilder setPan(String str) {
        this.pan = str;
        return this;
    }

    public final CreateCardRequestBuilder setPan(e eVar) {
        this.securePan = eVar;
        return this;
    }

    public final CreateCardRequestBuilder setBin(String str) {
        this.bin = str;
        return this;
    }

    public final CreateCardRequestBuilder setBinLength(int i) {
        this.binLength = i;
        return this;
    }

    public final CreateCardRequestBuilder setLastDigits(String str) {
        this.lastDigits = str;
        return this;
    }

    public final CreateCardRequestBuilder setExpiryDate(String str) {
        this.expiryDate = str;
        return this;
    }

    public final CreateCardRequestBuilder setCvx2(String str) {
        this.cvx2 = str;
        return this;
    }

    public final CreateCardRequestBuilder setCvx2(e eVar) {
        this.secureCvx2 = eVar;
        return this;
    }

    public final CreateCardRequestBuilder setIssuerCardId(String str) {
        this.issuerCardId = str;
        return this;
    }

    public final CreateCardRequestBuilder setIssuerAccountId(String str) {
        this.issuerCardId = str;
        return this;
    }

    public final CreateCardRequestBuilder setIssuerData(String str) {
        this.issuerData = str;
        return this;
    }

    public final CreateCardRequestBuilder setCardholderName(String str) {
        this.cardholderName = str;
        return this;
    }

    public final CreateCardRequestBuilder setPanSource(CreateCardRequestPanSource createCardRequestPanSource) {
        this.panSource = createCardRequestPanSource;
        return this;
    }

    public final CreateCardRequestBuilder setMdesTav(byte[] bArr) {
        this.mdesTav = bArr;
        return this;
    }

    public final CreateCardRequestBuilder setMdesFundingAccountInfo(byte[] bArr) {
        this.mdesFundingAccountInfo = bArr;
        return this;
    }

    public final CreateCardRequestBuilder setMdesPushAccountReceipt(String str) {
        this.mdesPushAccountReceipt = str;
        return this;
    }

    public final CreateCardRequestBuilder setFinancialAccountNumber(String str) {
        this.financialAccountNumber = str;
        return this;
    }

    public final CreateCardRequestBuilder setFinancialAccountLabel(String str) {
        this.financialAccountLabel = str;
        return this;
    }

    public final CreateCardRequestBuilder setVtsEncPaymentInstrument(byte[] bArr) {
        this.vtsEncPaymentInstrument = bArr;
        return this;
    }

    public final CreateCardRequestBuilder setIdemiaCipheredCardInformation(byte[] bArr) {
        this.idemiaCipheredCardInformation = bArr;
        return this;
    }

    public final CreateCardRequestBuilder setIdemiaAuthenticationCode(String str) {
        this.idemiaAuthCode = str;
        return this;
    }

    public final CreateCardRequestBuilder requireTermsAndConditionsApproval(boolean z) {
        this.requireTermsAndConditionsApproval = z;
        return this;
    }

    public final b build() {
        return new b(this);
    }

    public final String getBin() {
        return this.bin;
    }

    public final int getBinLength() {
        return this.binLength;
    }

    public final String getLastDigits() {
        return this.lastDigits;
    }

    public final String getPan() {
        return this.pan;
    }

    public final e getSecurePan() {
        return this.securePan;
    }

    public final String getExpiryDate() {
        return this.expiryDate;
    }

    public final String getCvx2() {
        return this.cvx2;
    }

    public final e getSecureCvx2() {
        return this.secureCvx2;
    }

    public final String getIssuerCardId() {
        return this.issuerCardId;
    }

    public final String getIssuerAccountId() {
        return this.issuerCardId;
    }

    public final String getIssuerData() {
        return this.issuerData;
    }

    public final String getCardholderName() {
        return this.cardholderName;
    }

    public final CreateCardRequestPanSource getPanSource() {
        return this.panSource;
    }

    public final byte[] getVtsEncPaymentInstrument() {
        return this.vtsEncPaymentInstrument;
    }

    public final byte[] getMdesTav() {
        return this.mdesTav;
    }

    public final byte[] getMdesFundingAccountInfo() {
        return this.mdesFundingAccountInfo;
    }

    public final String getMdesPushAccountReceipt() {
        return this.mdesPushAccountReceipt;
    }

    public final byte[] getIdemiaCipheredCardInformation() {
        return this.idemiaCipheredCardInformation;
    }

    public final String getIdemiaAuthCode() {
        return this.idemiaAuthCode;
    }

    public final boolean requiresTermsAndConditionsApproval() {
        return this.requireTermsAndConditionsApproval;
    }

    public final String getFinancialAccountNumber() {
        return this.financialAccountNumber;
    }

    public final String getFinancialAccountLabel() {
        return this.financialAccountLabel;
    }
}
