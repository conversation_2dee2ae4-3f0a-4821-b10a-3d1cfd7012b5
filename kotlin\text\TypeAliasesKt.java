package kotlin.text;

import kotlin.Metadata;

/* compiled from: TypeAliases.kt */
@Metadata(d1 = {"\u0000&\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000*\u001a\b\u0007\u0010\u0000\"\u00020\u00012\u00020\u0001B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004*,\b\u0007\u0010\u0005\"\u00020\u00062\u00020\u0006B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007B\u0010\b\b\u0012\f\b\t\u0012\b\b\fJ\u0004\b\t0\n*\u001a\b\u0007\u0010\u000b\"\u00020\f2\u00020\fB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004¨\u0006\r"}, d2 = {"Appendable", "Ljava/lang/Appendable;", "Lkotlin/SinceKotlin;", "version", "1.1", "CharacterCodingException", "Ljava/nio/charset/CharacterCodingException;", "1.4", "Lkotlin/WasExperimental;", "markerClass", "Lkotlin/ExperimentalStdlibApi;", "StringBuilder", "Ljava/lang/StringBuilder;", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\text\TypeAliasesKt.smali */
public final class TypeAliasesKt {
    public static /* synthetic */ void Appendable$annotations() {
    }

    public static /* synthetic */ void CharacterCodingException$annotations() {
    }

    public static /* synthetic */ void StringBuilder$annotations() {
    }
}
