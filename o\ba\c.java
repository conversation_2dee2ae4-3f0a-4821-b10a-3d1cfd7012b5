package o.ba;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ba\c.smali */
public final class c extends e {
    private static int a = 0;
    private static int e = 1;
    private byte[] c;
    private boolean d;

    public final boolean a() {
        int i = e + 43;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        boolean z = this.d;
        int i4 = (i2 ^ 85) + ((i2 & 85) << 1);
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return z;
            default:
                throw null;
        }
    }

    public final void c(byte[] bArr) {
        int i = (a + 82) - 1;
        e = i % 128;
        switch (i % 2 == 0 ? '%' : 'N') {
            case '%':
                this.c = bArr;
                this.d = true;
                break;
            default:
                this.c = bArr;
                this.d = false;
                break;
        }
    }

    public final byte[] e() {
        int i = a;
        int i2 = ((i | 47) << 1) - (i ^ 47);
        e = i2 % 128;
        boolean z = i2 % 2 != 0;
        byte[] bArr = this.c;
        switch (z) {
            case false:
                int i3 = 41 / 0;
            default:
                return bArr;
        }
    }
}
