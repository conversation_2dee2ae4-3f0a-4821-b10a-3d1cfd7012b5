package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP256K1Point.smali */
public class SecP256K1Point extends ECPoint.AbstractFp {
    SecP256K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP256K1FieldElement secP256K1FieldElement = (SecP256K1FieldElement) this.b;
        SecP256K1FieldElement secP256K1FieldElement2 = (SecP256K1FieldElement) this.c;
        SecP256K1FieldElement secP256K1FieldElement3 = (SecP256K1FieldElement) eCPoint.getXCoord();
        SecP256K1FieldElement secP256K1FieldElement4 = (SecP256K1FieldElement) eCPoint.getYCoord();
        SecP256K1FieldElement secP256K1FieldElement5 = (SecP256K1FieldElement) this.d[0];
        SecP256K1FieldElement secP256K1FieldElement6 = (SecP256K1FieldElement) eCPoint.getZCoord(0);
        int[] c = w5.c();
        int[] c2 = w5.c();
        int[] a = w5.a();
        int[] a2 = w5.a();
        int[] a3 = w5.a();
        boolean isOne = secP256K1FieldElement5.isOne();
        if (isOne) {
            iArr = secP256K1FieldElement3.a;
            iArr2 = secP256K1FieldElement4.a;
        } else {
            SecP256K1Field.square(secP256K1FieldElement5.a, a2, c);
            SecP256K1Field.multiply(a2, secP256K1FieldElement3.a, a, c);
            SecP256K1Field.multiply(a2, secP256K1FieldElement5.a, a2, c);
            SecP256K1Field.multiply(a2, secP256K1FieldElement4.a, a2, c);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP256K1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP256K1FieldElement.a;
            iArr4 = secP256K1FieldElement2.a;
        } else {
            SecP256K1Field.square(secP256K1FieldElement6.a, a3, c);
            SecP256K1Field.multiply(a3, secP256K1FieldElement.a, c2, c);
            SecP256K1Field.multiply(a3, secP256K1FieldElement6.a, a3, c);
            SecP256K1Field.multiply(a3, secP256K1FieldElement2.a, a3, c);
            iArr3 = c2;
            iArr4 = a3;
        }
        int[] a4 = w5.a();
        SecP256K1Field.subtract(iArr3, iArr, a4);
        SecP256K1Field.subtract(iArr4, iArr2, a);
        if (w5.b(a4)) {
            return w5.b(a) ? twice() : curve.getInfinity();
        }
        SecP256K1Field.square(a4, a2, c);
        int[] a5 = w5.a();
        SecP256K1Field.multiply(a2, a4, a5, c);
        SecP256K1Field.multiply(a2, iArr3, a2, c);
        SecP256K1Field.negate(a5, a5);
        w5.c(iArr4, a5, c2);
        SecP256K1Field.reduce32(w5.b(a2, a2, a5), a5);
        SecP256K1FieldElement secP256K1FieldElement7 = new SecP256K1FieldElement(a3);
        SecP256K1Field.square(a, secP256K1FieldElement7.a, c);
        int[] iArr5 = secP256K1FieldElement7.a;
        SecP256K1Field.subtract(iArr5, a5, iArr5);
        SecP256K1FieldElement secP256K1FieldElement8 = new SecP256K1FieldElement(a5);
        SecP256K1Field.subtract(a2, secP256K1FieldElement7.a, secP256K1FieldElement8.a);
        SecP256K1Field.multiplyAddToExt(secP256K1FieldElement8.a, a, c2);
        SecP256K1Field.reduce(c2, secP256K1FieldElement8.a);
        SecP256K1FieldElement secP256K1FieldElement9 = new SecP256K1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP256K1FieldElement9.a;
            SecP256K1Field.multiply(iArr6, secP256K1FieldElement5.a, iArr6, c);
        }
        if (!isOne2) {
            int[] iArr7 = secP256K1FieldElement9.a;
            SecP256K1Field.multiply(iArr7, secP256K1FieldElement6.a, iArr7, c);
        }
        return new SecP256K1Point(curve, secP256K1FieldElement7, secP256K1FieldElement8, new ECFieldElement[]{secP256K1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP256K1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP256K1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP256K1FieldElement secP256K1FieldElement = (SecP256K1FieldElement) this.c;
        if (secP256K1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP256K1FieldElement secP256K1FieldElement2 = (SecP256K1FieldElement) this.b;
        SecP256K1FieldElement secP256K1FieldElement3 = (SecP256K1FieldElement) this.d[0];
        int[] c = w5.c();
        int[] a = w5.a();
        SecP256K1Field.square(secP256K1FieldElement.a, a, c);
        int[] a2 = w5.a();
        SecP256K1Field.square(a, a2, c);
        int[] a3 = w5.a();
        SecP256K1Field.square(secP256K1FieldElement2.a, a3, c);
        SecP256K1Field.reduce32(w5.b(a3, a3, a3), a3);
        SecP256K1Field.multiply(a, secP256K1FieldElement2.a, a, c);
        SecP256K1Field.reduce32(c6.c(8, a, 2, 0), a);
        int[] a4 = w5.a();
        SecP256K1Field.reduce32(c6.a(8, a2, 3, 0, a4), a4);
        SecP256K1FieldElement secP256K1FieldElement4 = new SecP256K1FieldElement(a2);
        SecP256K1Field.square(a3, secP256K1FieldElement4.a, c);
        int[] iArr = secP256K1FieldElement4.a;
        SecP256K1Field.subtract(iArr, a, iArr);
        int[] iArr2 = secP256K1FieldElement4.a;
        SecP256K1Field.subtract(iArr2, a, iArr2);
        SecP256K1FieldElement secP256K1FieldElement5 = new SecP256K1FieldElement(a);
        SecP256K1Field.subtract(a, secP256K1FieldElement4.a, secP256K1FieldElement5.a);
        int[] iArr3 = secP256K1FieldElement5.a;
        SecP256K1Field.multiply(iArr3, a3, iArr3, c);
        int[] iArr4 = secP256K1FieldElement5.a;
        SecP256K1Field.subtract(iArr4, a4, iArr4);
        SecP256K1FieldElement secP256K1FieldElement6 = new SecP256K1FieldElement(a3);
        SecP256K1Field.twice(secP256K1FieldElement.a, secP256K1FieldElement6.a);
        if (!secP256K1FieldElement3.isOne()) {
            int[] iArr5 = secP256K1FieldElement6.a;
            SecP256K1Field.multiply(iArr5, secP256K1FieldElement3.a, iArr5, c);
        }
        return new SecP256K1Point(curve, secP256K1FieldElement4, secP256K1FieldElement5, new ECFieldElement[]{secP256K1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP256K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
