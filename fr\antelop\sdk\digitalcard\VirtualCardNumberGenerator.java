package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.drawable.Drawable;
import fr.antelop.sdk.CancellationSignal;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.dw.e;
import o.ee.o;
import o.p.i;
import o.v.l;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumberGenerator.smali */
public final class VirtualCardNumberGenerator implements CustomerAuthenticatedProcess {
    private CustomerAuthenticatedProcessActivityCallback activityCallback;
    private Drawable cardDrawable = null;
    private Integer cardForegroundColor = null;
    private final l innerSecureDigitalCardVcnGeneratorProcess;

    public VirtualCardNumberGenerator(l lVar) {
        this.innerSecureDigitalCardVcnGeneratorProcess = lVar;
    }

    public final VirtualCardNumberGenerator setCardBackground(Drawable drawable) {
        this.cardDrawable = drawable;
        return this;
    }

    public final VirtualCardNumberGenerator setCardForegroundColor(Integer num) {
        this.cardForegroundColor = num;
        return this;
    }

    public final VirtualCardNumberGenerator setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.activityCallback = customerAuthenticatedProcessActivityCallback;
        return this;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardVcnGeneratorProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardVcnGeneratorProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardVcnGeneratorProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardVcnGeneratorProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void create(Context context, VirtualCardNumberOption virtualCardNumberOption, boolean z, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnGeneratorProcess.c(context, virtualCardNumberOption, z, new e(), new o.p.e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnGeneratorProcess));
    }

    public final void create(Context context, VirtualCardNumberOption virtualCardNumberOption, boolean z, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnGeneratorProcess.c(context, virtualCardNumberOption, z, new e(), new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnGeneratorProcess));
    }

    public final CancellationSignal getCancellationSignal() {
        return this.innerSecureDigitalCardVcnGeneratorProcess.a();
    }
}
