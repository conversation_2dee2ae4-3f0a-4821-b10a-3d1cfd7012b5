package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\s.smali */
public final class s implements d {
    public final /* synthetic */ t a;
    public final /* synthetic */ String b;
    public final /* synthetic */ byte[] c;
    public final /* synthetic */ boolean d;
    public final /* synthetic */ byte[] e;

    public s(t tVar, String str, byte[] bArr, boolean z, byte[] bArr2) {
        this.a = tVar;
        this.b = str;
        this.c = bArr;
        this.d = z;
        this.e = bArr2;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.d
    public final void a(SecureStorageSDKException secureStorageSDKException) {
        Intrinsics.checkNotNullParameter(secureStorageSDKException, "secureStorageSDKException");
        y.b(this.e);
        h hVar = this.a.b;
        if (hVar == null) {
            Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
            hVar = null;
        }
        hVar.onInitFailed(secureStorageSDKException);
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.d
    public final void a(byte[] signedStorageEncryptionKey) {
        Intrinsics.checkNotNullParameter(signedStorageEncryptionKey, "signedStorageEncryptionKey");
        this.a.a(this.b, signedStorageEncryptionKey, this.c, this.d);
        y.b(this.e);
        y.b(signedStorageEncryptionKey);
    }
}
