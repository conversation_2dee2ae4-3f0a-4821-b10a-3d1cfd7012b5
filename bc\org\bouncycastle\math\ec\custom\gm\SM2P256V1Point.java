package bc.org.bouncycastle.math.ec.custom.gm;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\gm\SM2P256V1Point.smali */
public class SM2P256V1Point extends ECPoint.AbstractFp {
    SM2P256V1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SM2P256V1FieldElement sM2P256V1FieldElement = (SM2P256V1FieldElement) this.b;
        SM2P256V1FieldElement sM2P256V1FieldElement2 = (SM2P256V1FieldElement) this.c;
        SM2P256V1FieldElement sM2P256V1FieldElement3 = (SM2P256V1FieldElement) eCPoint.getXCoord();
        SM2P256V1FieldElement sM2P256V1FieldElement4 = (SM2P256V1FieldElement) eCPoint.getYCoord();
        SM2P256V1FieldElement sM2P256V1FieldElement5 = (SM2P256V1FieldElement) this.d[0];
        SM2P256V1FieldElement sM2P256V1FieldElement6 = (SM2P256V1FieldElement) eCPoint.getZCoord(0);
        int[] c = w5.c();
        int[] a = w5.a();
        int[] a2 = w5.a();
        int[] a3 = w5.a();
        boolean isOne = sM2P256V1FieldElement5.isOne();
        if (isOne) {
            iArr = sM2P256V1FieldElement3.a;
            iArr2 = sM2P256V1FieldElement4.a;
        } else {
            SM2P256V1Field.square(sM2P256V1FieldElement5.a, a2);
            SM2P256V1Field.multiply(a2, sM2P256V1FieldElement3.a, a);
            SM2P256V1Field.multiply(a2, sM2P256V1FieldElement5.a, a2);
            SM2P256V1Field.multiply(a2, sM2P256V1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = sM2P256V1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = sM2P256V1FieldElement.a;
            iArr4 = sM2P256V1FieldElement2.a;
        } else {
            SM2P256V1Field.square(sM2P256V1FieldElement6.a, a3);
            SM2P256V1Field.multiply(a3, sM2P256V1FieldElement.a, c);
            SM2P256V1Field.multiply(a3, sM2P256V1FieldElement6.a, a3);
            SM2P256V1Field.multiply(a3, sM2P256V1FieldElement2.a, a3);
            iArr3 = c;
            iArr4 = a3;
        }
        int[] a4 = w5.a();
        SM2P256V1Field.subtract(iArr3, iArr, a4);
        SM2P256V1Field.subtract(iArr4, iArr2, a);
        if (w5.b(a4)) {
            return w5.b(a) ? twice() : curve.getInfinity();
        }
        SM2P256V1Field.square(a4, a2);
        int[] a5 = w5.a();
        SM2P256V1Field.multiply(a2, a4, a5);
        SM2P256V1Field.multiply(a2, iArr3, a2);
        SM2P256V1Field.negate(a5, a5);
        w5.c(iArr4, a5, c);
        SM2P256V1Field.reduce32(w5.b(a2, a2, a5), a5);
        SM2P256V1FieldElement sM2P256V1FieldElement7 = new SM2P256V1FieldElement(a3);
        SM2P256V1Field.square(a, sM2P256V1FieldElement7.a);
        int[] iArr5 = sM2P256V1FieldElement7.a;
        SM2P256V1Field.subtract(iArr5, a5, iArr5);
        SM2P256V1FieldElement sM2P256V1FieldElement8 = new SM2P256V1FieldElement(a5);
        SM2P256V1Field.subtract(a2, sM2P256V1FieldElement7.a, sM2P256V1FieldElement8.a);
        SM2P256V1Field.multiplyAddToExt(sM2P256V1FieldElement8.a, a, c);
        SM2P256V1Field.reduce(c, sM2P256V1FieldElement8.a);
        SM2P256V1FieldElement sM2P256V1FieldElement9 = new SM2P256V1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = sM2P256V1FieldElement9.a;
            SM2P256V1Field.multiply(iArr6, sM2P256V1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = sM2P256V1FieldElement9.a;
            SM2P256V1Field.multiply(iArr7, sM2P256V1FieldElement6.a, iArr7);
        }
        return new SM2P256V1Point(curve, sM2P256V1FieldElement7, sM2P256V1FieldElement8, new ECFieldElement[]{sM2P256V1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SM2P256V1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SM2P256V1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SM2P256V1FieldElement sM2P256V1FieldElement = (SM2P256V1FieldElement) this.c;
        if (sM2P256V1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SM2P256V1FieldElement sM2P256V1FieldElement2 = (SM2P256V1FieldElement) this.b;
        SM2P256V1FieldElement sM2P256V1FieldElement3 = (SM2P256V1FieldElement) this.d[0];
        int[] a = w5.a();
        int[] a2 = w5.a();
        int[] a3 = w5.a();
        SM2P256V1Field.square(sM2P256V1FieldElement.a, a3);
        int[] a4 = w5.a();
        SM2P256V1Field.square(a3, a4);
        boolean isOne = sM2P256V1FieldElement3.isOne();
        int[] iArr = sM2P256V1FieldElement3.a;
        if (!isOne) {
            SM2P256V1Field.square(iArr, a2);
            iArr = a2;
        }
        SM2P256V1Field.subtract(sM2P256V1FieldElement2.a, iArr, a);
        SM2P256V1Field.add(sM2P256V1FieldElement2.a, iArr, a2);
        SM2P256V1Field.multiply(a2, a, a2);
        SM2P256V1Field.reduce32(w5.b(a2, a2, a2), a2);
        SM2P256V1Field.multiply(a3, sM2P256V1FieldElement2.a, a3);
        SM2P256V1Field.reduce32(c6.c(8, a3, 2, 0), a3);
        SM2P256V1Field.reduce32(c6.a(8, a4, 3, 0, a), a);
        SM2P256V1FieldElement sM2P256V1FieldElement4 = new SM2P256V1FieldElement(a4);
        SM2P256V1Field.square(a2, sM2P256V1FieldElement4.a);
        int[] iArr2 = sM2P256V1FieldElement4.a;
        SM2P256V1Field.subtract(iArr2, a3, iArr2);
        int[] iArr3 = sM2P256V1FieldElement4.a;
        SM2P256V1Field.subtract(iArr3, a3, iArr3);
        SM2P256V1FieldElement sM2P256V1FieldElement5 = new SM2P256V1FieldElement(a3);
        SM2P256V1Field.subtract(a3, sM2P256V1FieldElement4.a, sM2P256V1FieldElement5.a);
        int[] iArr4 = sM2P256V1FieldElement5.a;
        SM2P256V1Field.multiply(iArr4, a2, iArr4);
        int[] iArr5 = sM2P256V1FieldElement5.a;
        SM2P256V1Field.subtract(iArr5, a, iArr5);
        SM2P256V1FieldElement sM2P256V1FieldElement6 = new SM2P256V1FieldElement(a2);
        SM2P256V1Field.twice(sM2P256V1FieldElement.a, sM2P256V1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = sM2P256V1FieldElement6.a;
            SM2P256V1Field.multiply(iArr6, sM2P256V1FieldElement3.a, iArr6);
        }
        return new SM2P256V1Point(curve, sM2P256V1FieldElement4, sM2P256V1FieldElement5, new ECFieldElement[]{sM2P256V1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SM2P256V1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
