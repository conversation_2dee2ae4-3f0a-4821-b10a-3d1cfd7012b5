package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c5.smali */
class c5 extends j5 {
    private int C;
    private int L;
    private boolean R;
    private boolean u0;

    c5(InputStream inputStream, int i) throws IOException {
        super(inputStream, i);
        this.R = false;
        this.u0 = true;
        this.C = inputStream.read();
        int read = inputStream.read();
        this.L = read;
        if (read < 0) {
            throw new EOFException();
        }
        b();
    }

    void b(boolean z) {
        this.u0 = z;
        b();
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i, int i2) throws IOException {
        if (this.u0 || i2 < 3) {
            return super.read(bArr, i, i2);
        }
        if (this.R) {
            return -1;
        }
        int read = this.b.read(bArr, i + 2, i2 - 2);
        if (read < 0) {
            throw new EOFException();
        }
        bArr[i] = (byte) this.C;
        bArr[i + 1] = (byte) this.L;
        this.C = this.b.read();
        int read2 = this.b.read();
        this.L = read2;
        if (read2 >= 0) {
            return read + 2;
        }
        throw new EOFException();
    }

    private boolean b() {
        if (!this.R && this.u0 && this.C == 0 && this.L == 0) {
            this.R = true;
            a(true);
        }
        return this.R;
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (b()) {
            return -1;
        }
        int read = this.b.read();
        if (read >= 0) {
            int i = this.C;
            this.C = this.L;
            this.L = read;
            return i;
        }
        throw new EOFException();
    }
}
