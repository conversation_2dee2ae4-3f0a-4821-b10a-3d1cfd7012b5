package o.w;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.sca.PushAuthenticationRequest;
import java.lang.reflect.Method;
import java.util.Date;
import o.b.c;
import o.bd.d;
import o.dc.h;
import o.e.a;
import o.ee.g;
import o.p.c;
import o.p.j;
import o.z.e;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\w\d.smali */
public final class d extends b implements o.ee.d<PushAuthenticationRequest> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int[] r;
    private static char[] s;
    private static long t;
    private static int v;
    private static int y;
    private final String i;
    private byte[] k;
    private final Long l;
    private final boolean m;
    private final Date n;

    /* renamed from: o, reason: collision with root package name */
    private boolean f114o;
    private c p;
    private boolean q;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        y = 0;
        v = 1;
        r = new int[]{1832173352, 1412557221, -1984017879, 976332358, -2130109241, -1880021639, 1398187151, 1215525668, -1644953574, 99426029, -1172442622, -529562003, -1662123325, 566923462, -1860347864, 871320165, -923224540, -807185944};
        s = new char[]{11420, 16923, 61934, 26456, 38509, 1455, 47982, 10944, 22946, 52997, 32495, 60494, 800, 45696, 8231, 22499, 50859, 29706, 60404, 6472, 49441, 44937, 7269, 35534, 31649, 59401, 22218, 51036, 46131, 8847, 37745, 394, 61164};
        t = 4571118102031188584L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void G(short r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 4
            int r9 = 116 - r9
            byte[] r0 = o.w.d.$$d
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L33
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            int r7 = r7 + 1
            if (r5 != r8) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L33:
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.w.d.G(short, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{124, 92, -85, -9};
        $$e = Opcodes.IF_ACMPEQ;
    }

    @Override // o.ee.d
    public final /* synthetic */ PushAuthenticationRequest a() {
        int i = v + 45;
        y = i % 128;
        int i2 = i % 2;
        PushAuthenticationRequest y2 = y();
        int i3 = v + 79;
        y = i3 % 128;
        switch (i3 % 2 != 0 ? 'D' : (char) 1) {
            case 1:
                return y2;
            default:
                int i4 = 60 / 0;
                return y2;
        }
    }

    public d(String str, byte[] bArr, String str2, Date date, Long l, boolean z) {
        super(str, str2);
        Object[] objArr = new Object[1];
        E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1278146133, 2146445485}, 30 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        this.i = ((String) objArr[0]).intern();
        this.f114o = false;
        this.q = false;
        this.k = bArr;
        this.n = date;
        this.l = l;
        this.m = z;
    }

    @Override // o.p.h
    public final synchronized void b(Context context, o.f.e eVar) throws j {
        int i = v + 53;
        y = i % 128;
        int i2 = i % 2;
        if (C()) {
            throw new j(j.c.e);
        }
        super.b(context, eVar);
        int i3 = v + 39;
        y = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0024, code lost:
    
        if (C() == false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0029, code lost:
    
        if (r4.q != false) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x002b, code lost:
    
        d(r5, r6);
        r5 = o.w.d.y + com.esotericsoftware.asm.Opcodes.LSUB;
        o.w.d.v = r5 % 128;
        r5 = r5 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0038, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0039, code lost:
    
        r6 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState;
        r3 = new java.lang.Object[1];
        E(new int[]{-1819587285, 604745176, -1618471695, -338260125, -858576592, -1624498260, 1519211851, -322802803, -794716364, 307632771, -1282804663, -2062807740, 1500060767, 1925070905}, 25 - (android.view.ViewConfiguration.getFadingEdgeLength() >> 16), r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x005d, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r6, ((java.lang.String) r3[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x001e, code lost:
    
        if (C() == false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(android.content.Context r5, o.p.g r6) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r4 = this;
            int r0 = o.w.d.y
            int r0 = r0 + 3
            int r1 = r0 % 128
            o.w.d.v = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 58
            goto L11
        Lf:
            r0 = 84
        L11:
            r1 = 0
            switch(r0) {
                case 84: goto L1a;
                default: goto L15;
            }
        L15:
            boolean r0 = r4.C()
            goto L21
        L1a:
            boolean r0 = r4.C()
            if (r0 != 0) goto L5e
        L20:
            goto L27
        L21:
            r2 = 55
            int r2 = r2 / r1
            if (r0 != 0) goto L5e
            goto L20
        L27:
            boolean r0 = r4.q
            if (r0 != 0) goto L39
            r4.d(r5, r6)
            int r5 = o.w.d.y
            int r5 = r5 + 101
            int r6 = r5 % 128
            o.w.d.v = r6
            int r5 = r5 % 2
            return
        L39:
            fr.antelop.sdk.exception.WalletValidationException r5 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r6 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            r0 = 14
            int[] r0 = new int[r0]
            r0 = {x0072: FILL_ARRAY_DATA , data: [-1819587285, 604745176, -1618471695, -338260125, -858576592, -1624498260, 1519211851, -322802803, -794716364, 307632771, -1282804663, -2062807740, 1500060767, 1925070905} // fill-array
            int r2 = android.view.ViewConfiguration.getFadingEdgeLength()
            int r2 = r2 >> 16
            int r2 = 25 - r2
            r3 = 1
            java.lang.Object[] r3 = new java.lang.Object[r3]
            E(r0, r2, r3)
            r0 = r3[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r5.<init>(r6, r0)
            throw r5
        L5e:
            fr.antelop.sdk.exception.WalletValidationException r5 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r6 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            java.lang.String r0 = r4.d()
            r5.<init>(r6, r0)
            throw r5
        L6a:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.w.d.e(android.content.Context, o.p.g):void");
    }

    @Override // o.w.b
    final o.z.c t() {
        int i = v + 47;
        y = i % 128;
        switch (i % 2 != 0 ? (char) 28 : (char) 2) {
            case 28:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                if (this.m) {
                    return o.z.c.e;
                }
                o.z.c cVar = o.z.c.a;
                int i2 = y + 9;
                v = i2 % 128;
                int i3 = i2 % 2;
                return cVar;
        }
    }

    @Override // o.p.h
    public final String d() {
        int i = y + 3;
        v = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        E(new int[]{-1819587285, 604745176, -1618471695, -338260125, -858576592, -1624498260, 1519211851, -322802803, -794716364, 307632771, -1282804663, -2062807740, 1500060767, 1925070905}, 24 - TextUtils.indexOf((CharSequence) "", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = y + 75;
        v = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.p.h
    public final o.at.d b() {
        int i = y + 85;
        v = i % 128;
        int i2 = i % 2;
        o.at.d dVar = o.at.d.d;
        int i3 = y + 79;
        v = i3 % 128;
        int i4 = i3 % 2;
        return dVar;
    }

    @Override // o.p.h
    public final String e() {
        int i = v;
        int i2 = i + 13;
        y = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 65;
        y = i4 % 128;
        int i5 = i4 % 2;
        return "";
    }

    public final byte[] u() {
        int i = y + Opcodes.DMUL;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        byte[] bArr = this.k;
        int i4 = i2 + 21;
        y = i4 % 128;
        int i5 = i4 % 2;
        return bArr;
    }

    public final void e(byte[] bArr) {
        int i = y + 79;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        this.k = bArr;
        int i4 = i2 + 67;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? ']' : 'E') {
            case 'E':
                return;
            default:
                throw null;
        }
    }

    @Override // o.w.b
    final String p() {
        int i = y;
        int i2 = i + 79;
        v = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + Opcodes.DMUL;
        v = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.w.b
    final byte[] s() {
        int i = y + Opcodes.DNEG;
        int i2 = i % 128;
        v = i2;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                int i3 = i2 + 45;
                y = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.w.b
    public final Long x() {
        int i = v;
        int i2 = i + 91;
        y = i2 % 128;
        switch (i2 % 2 != 0 ? 'F' : (char) 24) {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                Long l = this.l;
                int i3 = i + 51;
                y = i3 % 128;
                int i4 = i3 % 2;
                return l;
        }
    }

    @Override // o.w.b
    public final String w() {
        int i = y + 31;
        v = i % 128;
        int i2 = i % 2;
        String w = super.w();
        int i3 = v + 85;
        y = i3 % 128;
        int i4 = i3 % 2;
        return w;
    }

    public final PushAuthenticationRequest y() {
        PushAuthenticationRequest pushAuthenticationRequest = new PushAuthenticationRequest(this);
        int i = y + Opcodes.DDIV;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return pushAuthenticationRequest;
        }
    }

    @Override // o.p.h
    public final boolean f() {
        int i = y + 47;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        switch (this.p == null) {
            case true:
                return false;
            default:
                int i4 = i2 + 41;
                y = i4 % 128;
                int i5 = i4 % 2;
                return true;
        }
    }

    @Override // o.p.h
    public final boolean g() {
        int i = y + 71;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        boolean z = this.f114o;
        int i4 = i2 + Opcodes.LSHL;
        y = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return z;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private boolean C() {
        int i = y + 97;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return this.f114o;
        }
    }

    final void b(boolean z) {
        int i = v + 25;
        int i2 = i % 128;
        y = i2;
        char c = i % 2 != 0 ? '(' : '#';
        this.q = z;
        switch (c) {
            case '(':
                int i3 = 22 / 0;
                break;
        }
        int i4 = i2 + Opcodes.LSHL;
        v = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.p.h
    public final void a(o.bv.c cVar) {
        int i = v + 75;
        y = i % 128;
        int i2 = i % 2;
        super.a(cVar);
        this.f114o = true;
        int i3 = y + 93;
        v = i3 % 128;
        int i4 = i3 % 2;
    }

    private void d(Context context, o.ei.c cVar, o.h.d dVar) {
        b(context, cVar, dVar, new e.d() { // from class: o.w.d.1
            private static int d = 0;
            private static int b = 1;

            @Override // o.z.e.d
            public final void d(String str) {
                int i = d;
                int i2 = (i ^ 3) + ((i & 3) << 1);
                b = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.z.e.d
            public final void d(o.bb.d dVar2, boolean z) {
                int i = b;
                int i2 = ((i | 95) << 1) - (i ^ 95);
                d = i2 % 128;
                int i3 = i2 % 2;
                d.this.b(z);
                int i4 = d;
                int i5 = (i4 & 71) + (i4 | 71);
                b = i5 % 128;
                int i6 = i5 % 2;
            }
        }, true, x());
        int i = v + Opcodes.DREM;
        y = i % 128;
        int i2 = i % 2;
    }

    public final synchronized void d(Context context) throws WalletValidationException {
        synchronized (this.g) {
            g.c();
            Object[] objArr = new Object[1];
            E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1278146133, 2146445485}, 30 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            E(new int[]{937210594, -1179509140, -883519280, -1633357987}, View.resolveSizeAndState(0, 0, 0) + 8, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            if (context == null) {
                WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
                Object[] objArr3 = new Object[1];
                E(new int[]{1393069209, -363280012, -1599908228, 1486156724}, TextUtils.getOffsetAfter("", 0) + 7, objArr3);
                throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
            }
            if (m() || C() || !h()) {
                throw new WalletValidationException(WalletValidationErrorCode.WrongState, d());
            }
            if (!i()) {
                if (this.p != null) {
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                    Object[] objArr4 = new Object[1];
                    E(new int[]{-349110506, 157908831, -1971232249, -1276162271, -1618471695, -338260125, -858576592, -1624498260, 1276376366, 1969469824, 577810596, -347949062, 292759620, -860718357}, 28 - Drawable.resolveOpacity(0, 0), objArr4);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
                }
                this.p = d(context, (o.p.g) null, new c.d() { // from class: o.w.d$$ExternalSyntheticLambda0
                    @Override // o.p.c.d
                    public final void runWithWallet(Context context2, o.ei.c cVar) {
                        d.this.b(context2, cVar);
                    }
                });
            } else {
                try {
                    e(context, j());
                } catch (j e) {
                    throw e.c(d());
                }
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(Context context, o.ei.c cVar) {
        synchronized (this.g) {
            g.c();
            Object[] objArr = new Object[1];
            E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1278146133, 2146445485}, Process.getGidForName("") + 31, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            E(new int[]{937210594, -1179509140, 1050723730, 342404661, 1536688297, -2087468731, -178210003, -1577658717, 495699171, -1322870685, 136779990, 1244410712, 136991167, -44883246}, TextUtils.lastIndexOf("", '0', 0, 0) + 26, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            try {
                e(context, cVar);
            } catch (j e) {
                g.c();
                Object[] objArr3 = new Object[1];
                E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1278146133, 2146445485}, 29 - ExpandableListView.getPackedPositionChild(0L), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                E(new int[]{937210594, -1179509140, 1050723730, 342404661, -25040489, 1033545846, -1149382235, -995702385, 706652783, -1901926146, -1359454521, 426965827, -96639811, 1378545743, -1181350518, 860279447, -2069014542, 1011904361, 367487016, 1692300825, -1410866985, -1617360340, 2133960346, 736658247, 451246174, -1086402899, 219962352, -1261253726, -1032058416, 701216270, 1867683526, 282980321, -879477479, 211206382, -1031709271, -1705440804, -712423128, 940854949, -1362129396, -1063504520, 747441810, -1310468116, -456835884, 546901990, 250814349, -311090761}, 91 - Color.alpha(0), objArr4);
                g.e(intern2, String.format(((String) objArr4[0]).intern(), e.e()));
                if (l() != null) {
                    l().onError(new o.bv.c(AntelopErrorCode.InternalError));
                }
            }
        }
    }

    private synchronized void e(Context context, o.ei.c cVar) throws j {
        int i = y + 75;
        v = i % 128;
        switch (i % 2 == 0 ? 'I' : '\t') {
            case 'I':
                cVar.q();
                throw null;
            default:
                if (!cVar.q()) {
                    throw new j(j.c.c);
                }
                if (!m()) {
                    int i2 = y + 43;
                    v = i2 % 128;
                    int i3 = i2 % 2;
                    switch (!C() ? 'b' : '9') {
                        case '9':
                            break;
                        default:
                            if (h()) {
                                this.f114o = true;
                                d(context, cVar, this.a);
                                break;
                            }
                            break;
                    }
                }
                throw new j(j.c.e);
        }
    }

    public static void a(Context context, final AntelopCallback antelopCallback) throws WalletValidationException {
        int i = v + 13;
        y = i % 128;
        switch (i % 2 != 0) {
            case true:
                o.ei.c.c().q();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                o.ei.c c = o.ei.c.c();
                if (!c.q()) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    E(new int[]{1942743168, -645435001, -1274301470, -1256549723}, 5 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    E(new int[]{1942743168, -645435001, -1052174243, -1133657313, -1221262254, 271450831, 1057135981, -541288939, 1092694377, 586431936, 1821256729, 276263531, 1854524805, 1378909232, -37233919, 1952927147, -667124480, 1273756274, 664578798, -779794964, -2021292397, 1580909994}, 42 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr2);
                    throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
                }
                new o.bd.d(context, new d.a() { // from class: o.w.d.3
                    private static int d = 0;
                    private static int e = 1;

                    @Override // o.bd.d.a
                    public final void c() {
                        int i2 = d + 79;
                        e = i2 % 128;
                        switch (i2 % 2 == 0) {
                            case false:
                                AntelopCallback.this.onSuccess();
                                return;
                            default:
                                AntelopCallback.this.onSuccess();
                                Object obj2 = null;
                                obj2.hashCode();
                                throw null;
                        }
                    }

                    @Override // o.bd.d.a
                    public final void e(o.bb.d dVar) {
                        int i2 = e;
                        int i3 = (i2 ^ Opcodes.DNEG) + ((i2 & Opcodes.DNEG) << 1);
                        d = i3 % 128;
                        switch (i3 % 2 != 0 ? (char) 31 : '5') {
                            case 31:
                                AntelopCallback.this.onError(o.bv.c.c(dVar).d());
                                int i4 = 86 / 0;
                                break;
                            default:
                                AntelopCallback.this.onError(o.bv.c.c(dVar).d());
                                break;
                        }
                        int i5 = d + 99;
                        e = i5 % 128;
                        switch (i5 % 2 == 0 ? 'E' : '\r') {
                            case '\r':
                                return;
                            default:
                                throw null;
                        }
                    }
                }, c).b(h.b);
                int i2 = y + 85;
                v = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    @Override // o.p.h
    public final void a(Context context, o.bv.c cVar) {
        int i = v + 43;
        y = i % 128;
        int i2 = i % 2;
        switch (cVar.d().getCode().equals(AntelopErrorCode.UserCancelled) ? '\f' : (char) 15) {
            case 15:
                break;
            default:
                g.c();
                Object[] objArr = new Object[1];
                E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1386857993, -9108101, 1549075068, 1532750714, 1501681444, 1365032547, -1381080215, -1014087647, 417485575, 91960045}, 44 - MotionEvent.axisFromString(""), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                F((char) Color.red(0), ViewConfiguration.getTapTimeout() >> 16, KeyEvent.getDeadChar(0, 0) + 20, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                try {
                    e(context, j());
                    break;
                } catch (j e) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    E(new int[]{242491073, -1523592621, 1485891981, -2105872139, -985267058, 1562826006, -256890267, -1321019586, 1858561306, 292677868, 529378893, 1237065362, 1750485251, -890473614, -1278146133, 2146445485}, MotionEvent.axisFromString("") + 31, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    F((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 60800), 20 - (ViewConfiguration.getKeyRepeatDelay() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 13, objArr4);
                    g.a(intern2, ((String) objArr4[0]).intern(), e);
                    break;
                }
        }
        super.a(context, cVar);
        o.dc.c.d(o.ei.c.c().t(), this.l);
        int i3 = y + 45;
        v = i3 % 128;
        int i4 = i3 % 2;
    }

    public final long A() {
        int i = y + Opcodes.LSHL;
        v = i % 128;
        int i2 = i % 2;
        long time = this.n.getTime();
        int i3 = v + Opcodes.DMUL;
        y = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 90 / 0;
                return time;
            default:
                return time;
        }
    }

    @Override // o.p.h
    public final void n() {
        int i = v + 23;
        y = i % 128;
        int i2 = i % 2;
        super.n();
        this.p = null;
        int i3 = y + Opcodes.DSUB;
        v = i3 % 128;
        int i4 = i3 % 2;
    }

    private static void E(int[] iArr, int i, Object[] objArr) {
        int[] iArr2;
        int i2;
        o.a.g gVar = new o.a.g();
        char[] cArr = new char[4];
        int i3 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = r;
        char c = '0';
        int i4 = -1667374059;
        int i5 = 1;
        int i6 = 0;
        switch (iArr3 != null ? '*' : '0') {
            case '*':
                int length = iArr3.length;
                int[] iArr4 = new int[length];
                int i7 = 0;
                while (true) {
                    switch (i7 < length ? i6 : 1) {
                        case 1:
                            iArr3 = iArr4;
                            break;
                        default:
                            int i8 = $10 + 33;
                            $11 = i8 % 128;
                            if (i8 % i3 == 0) {
                            }
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i6] = Integer.valueOf(iArr3[i7]);
                                Object obj = a.s.get(Integer.valueOf(i4));
                                if (obj == null) {
                                    Class cls = (Class) a.c(10 - TextUtils.indexOf("", "", i6, i6), (char) (8855 - TextUtils.indexOf("", c)), 323 - TextUtils.indexOf("", c, i6, i6));
                                    byte b = (byte) (-1);
                                    byte b2 = (byte) (b + 1);
                                    Object[] objArr3 = new Object[1];
                                    G(b, b2, b2, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    a.s.put(-1667374059, obj);
                                }
                                iArr4[i7] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                                i7++;
                                i3 = 2;
                                c = '0';
                                i4 = -1667374059;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                    }
                }
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = r;
        switch (iArr6 != null ? 'J' : (char) 28) {
            case 28:
                break;
            default:
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i9 = 0;
                while (i9 < length3) {
                    try {
                        Object[] objArr4 = new Object[i5];
                        objArr4[0] = Integer.valueOf(iArr6[i9]);
                        Object obj2 = a.s.get(-1667374059);
                        if (obj2 != null) {
                            iArr2 = iArr6;
                            i2 = length3;
                        } else {
                            Class cls2 = (Class) a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, (char) (KeyEvent.normalizeMetaState(0) + 8856), 324 - Color.red(0));
                            byte b3 = (byte) (-1);
                            byte b4 = (byte) (b3 + 1);
                            iArr2 = iArr6;
                            i2 = length3;
                            Object[] objArr5 = new Object[1];
                            G(b3, b4, b4, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                            a.s.put(-1667374059, obj2);
                        }
                        iArr7[i9] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                        i9++;
                        iArr6 = iArr2;
                        length3 = i2;
                        i5 = 1;
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                iArr6 = iArr7;
                break;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            int i10 = $10 + Opcodes.DNEG;
            $11 = i10 % 128;
            int i11 = i10 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr5);
            for (int i12 = 0; i12 < 16; i12++) {
                int i13 = $11 + 29;
                $10 = i13 % 128;
                int i14 = i13 % 2;
                gVar.e ^= iArr5[i12];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                    Object obj3 = a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 11, (char) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i15 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i15;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i16 = gVar.e;
            int i17 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            o.a.g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) a.c(View.MeasureSpec.getMode(0) + 12, (char) (TextUtils.lastIndexOf("", '0') + 55184), 515 - Drawable.resolveOpacity(0, 0));
                    byte b5 = (byte) (-1);
                    byte b6 = (byte) (b5 + 1);
                    Object[] objArr8 = new Object[1];
                    G(b5, b6, (byte) (b6 + 1), objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void F(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 738
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.w.d.F(char, int, int, java.lang.Object[]):void");
    }
}
