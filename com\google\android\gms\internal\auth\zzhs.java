package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzhs.smali */
public final class zzhs implements zzdj {
    private static final zzhs zza = new zzhs();
    private final zzdj zzb = zzdn.zza(zzdn.zzb(new zzhv()));

    public static zzhr zzb() {
        return zza.zza().zza();
    }

    public static boolean zzd() {
        return zza.zza().zzb();
    }

    public static boolean zze() {
        return zza.zza().zzc();
    }

    @Override // com.google.android.gms.internal.auth.zzdj
    /* renamed from: zzc, reason: merged with bridge method [inline-methods] */
    public final zzht zza() {
        return (zzht) this.zzb.zza();
    }
}
