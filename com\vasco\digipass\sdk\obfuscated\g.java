package com.vasco.digipass.sdk.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\g.smali */
public class g {
    public byte a;
    public byte b;
    public byte c;
    public byte e;
    public byte f;
    public boolean k;
    public byte l;
    public boolean m;
    public boolean n;

    /* renamed from: o, reason: collision with root package name */
    public byte f17o;
    public byte r;
    public byte u;
    public boolean v;
    public byte[] d = new byte[4];
    public byte[] g = new byte[16];
    public byte[] h = new byte[16];
    public byte[] i = new byte[16];
    public byte[] j = new byte[16];
    public byte[] p = new byte[16];
    public byte[] q = new byte[16];
    public byte[] s = new byte[16];
    public byte[] t = new byte[16];
}
