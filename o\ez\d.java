package o.ez;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.ct.b;
import o.ee.g;
import o.ey.e;
import o.ff.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ez\d.smali */
public final class d extends e<c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static char[] b;
    private static char d;
    private static int e;
    private static int j;
    private int c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        j = 1;
        r();
        View.getDefaultSize(0, 0);
        ViewConfiguration.getJumpTapTimeout();
        ExpandableListView.getPackedPositionGroup(0L);
        KeyEvent.keyCodeFromString("");
        Process.getElapsedCpuTime();
        TextUtils.indexOf("", "");
        ViewConfiguration.getTapTimeout();
        int i = j + Opcodes.LSUB;
        e = i % 128;
        int i2 = i % 2;
    }

    private static void B(byte b2, int i, short s, Object[] objArr) {
        int i2 = s + 68;
        byte[] bArr = $$d;
        int i3 = (i * 2) + 1;
        int i4 = (b2 * 4) + 4;
        byte[] bArr2 = new byte[i3];
        int i5 = -1;
        int i6 = i3 - 1;
        if (bArr == null) {
            i4++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i2 = i6 + i2;
            i6 = i6;
        }
        while (true) {
            int i7 = i5 + 1;
            bArr2[i7] = (byte) i2;
            if (i7 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i4];
            i4++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = i7;
            i2 = b3 + i2;
            i6 = i6;
        }
    }

    static void init$0() {
        $$d = new byte[]{83, 27, -79, -63};
        $$e = 35;
    }

    static void r() {
        a = -3658459823062523499L;
        b = new char[]{30572, 30534, 30588, 30583, 30575, 30539, 30561, 30498, 29844, 30556, 30499, 30532, 30587, 30511, 30542, 30568, 30574, 29845, 30570, 30517, 30506, 30559, 30563, 30562, 30540, 30564, 30589, 30566, 30569, 30586, 30582, 30567, 30560, 30591, 30585, 30571};
        d = (char) 17043;
    }

    @Override // o.ey.e
    public final void a(o.dd.e eVar) {
        int i = j + 35;
        e = i % 128;
        switch (i % 2 != 0 ? '\r' : '5') {
            case Opcodes.SALOAD /* 53 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ey.e
    public final void b() {
        int i = j + Opcodes.LSUB;
        e = i % 128;
        switch (i % 2 != 0 ? (char) 4 : '2') {
            case 4:
                throw null;
            default:
                return;
        }
    }

    @Override // o.ey.e
    public final /* synthetic */ boolean a(Context context, c cVar) {
        int i = e + 67;
        j = i % 128;
        int i2 = i % 2;
        boolean b2 = b(context, cVar);
        int i3 = j + 83;
        e = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    @Override // o.ey.e
    public final /* synthetic */ b<c> c() {
        int i = e + 71;
        j = i % 128;
        switch (i % 2 == 0 ? (char) 1 : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return v();
            default:
                int i2 = 80 / 0;
                return v();
        }
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ey.a e() {
        int i = j + 33;
        e = i % 128;
        int i2 = i % 2;
        a t = t();
        int i3 = e + 63;
        j = i3 % 128;
        int i4 = i3 % 2;
        return t;
    }

    public d(String str, String str2, boolean z) {
        super(str, str2, z);
        this.c = 1;
    }

    public final int p() {
        int i = j;
        int i2 = i + Opcodes.LMUL;
        e = i2 % 128;
        int i3 = i2 % 2;
        int i4 = this.c;
        int i5 = i + 73;
        e = i5 % 128;
        int i6 = i5 % 2;
        return i4;
    }

    public final void d(int i) {
        int i2 = j;
        int i3 = i2 + Opcodes.DREM;
        e = i3 % 128;
        char c = i3 % 2 != 0 ? 'b' : '.';
        this.c = i;
        switch (c) {
            case Opcodes.FADD /* 98 */:
                throw null;
            default:
                int i4 = i2 + 5;
                e = i4 % 128;
                int i5 = i4 % 2;
                return;
        }
    }

    private static a t() {
        a aVar = new a();
        int i = e + 1;
        j = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 98 / 0;
                return aVar;
            default:
                return aVar;
        }
    }

    private static o.cx.c v() {
        o.cx.c cVar = new o.cx.c();
        int i = j + 77;
        e = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:43:0x0128, code lost:
    
        if (r10 == false) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x012a, code lost:
    
        new o.dd.e(r17).c(d(), new java.lang.StringBuilder().append(r18.o()).append(r18.f()).append(r8.get(r8.size() - 1).c()).toString());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean b(android.content.Context r17, o.ff.c r18) {
        /*
            Method dump skipped, instructions count: 626
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.d.b(android.content.Context, o.ff.c):boolean");
    }

    @Override // o.ey.e
    public final o.eg.b b(o.ek.b bVar) throws o.eg.d {
        o.eg.e eVar;
        o.eg.b bVar2 = new o.eg.b();
        Object[] objArr = new Object[1];
        y(12 - ImageFormat.getBitsPerPixel(0), "\u0004\f\u001d \u0014\u001b\"\u001a\u001c\u0015\u0013\u0000㘧", (byte) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 41), objArr);
        bVar2.d(((String) objArr[0]).intern(), e(bVar));
        Object[] objArr2 = new Object[1];
        y(KeyEvent.getDeadChar(0, 0) + 2, "\u001d!", (byte) (8 - Gravity.getAbsoluteGravity(0, 0)), objArr2);
        bVar2.d(((String) objArr2[0]).intern(), d());
        switch (f() != null ? ';' : '\f') {
            case '\f':
                break;
            default:
                if (j() != null) {
                    int i = j + 35;
                    e = i % 128;
                    int i2 = i % 2;
                    c cVar = f().get(0);
                    switch (cVar != null ? 'C' : 'Y') {
                        case Opcodes.DUP /* 89 */:
                            eVar = null;
                            break;
                        default:
                            int i3 = e + 75;
                            j = i3 % 128;
                            if (i3 % 2 == 0) {
                            }
                            eVar = cVar.b(this.c);
                            break;
                    }
                    Object[] objArr3 = new Object[1];
                    u("\uf52d\uf54eꮂ\u0aba\ue8d4䁈ꨥꏲ灠趞⼜⤧＜tꑃ", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, objArr3);
                    bVar2.d(((String) objArr3[0]).intern(), eVar);
                    Object[] objArr4 = new Object[1];
                    u("\uec8d\ueceeഘ갠沄夞\u2e75몤槀⬄ꭌぱ\ue6bcꛮ–띣掐㶮뻁⨬", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr4);
                    bVar2.d(((String) objArr4[0]).intern(), j().e());
                    Date d2 = j().d();
                    if (d2 != null) {
                        Object[] objArr5 = new Object[1];
                        u("팸퍝냦ᇔ㧀ﯿ笤ᡈ噢雭︸銘\ud91cᬙ", Process.myTid() >> 22, objArr5);
                        bVar2.d(((String) objArr5[0]).intern(), d2.getTime() / 1000);
                        int i4 = j + 69;
                        e = i4 % 128;
                        int i5 = i4 % 2;
                        break;
                    }
                }
                break;
        }
        Object[] objArr6 = new Object[1];
        u("⡧⠑ᴔ밪ӡ\uf58c䘆ᘀ괪㬖쌱鳯≙뛧䡶ᯊꝒⶳ횞蚩㲦ꢙ厰", KeyEvent.normalizeMetaState(0), objArr6);
        bVar2.d(((String) objArr6[0]).intern(), x());
        g.c();
        Object[] objArr7 = new Object[1];
        u("\ue366\ue330踈⼫︋ߺ볬\ue445昞ꠛ㧎溑\ue953◼늛\ue99f汻뺳ⱄ瓃\uf7a8㮖\ua95e￭竀땿∥", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr7);
        String intern = ((String) objArr7[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr8 = new Object[1];
        u("勧劗憏삷שׁ禤맗騔ힻ䞮㳢Ⴣ壃쩠랽鞪\uddb2六⥉ઇ䘳퐕걫膮쬏媧❖", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr8);
        g.d(intern, sb.append(((String) objArr8[0]).intern()).append(bVar2.b()).toString());
        return bVar2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:17:0x006b. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:14:0x004e  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.eg.b x() throws o.eg.d {
        /*
            r8 = this;
            o.eg.b r0 = new o.eg.b
            r0.<init>()
            java.util.List r1 = r8.f()
            if (r1 == 0) goto Lf
            r1 = 75
            goto L11
        Lf:
            r1 = 81
        L11:
            r2 = 1
            r3 = 0
            switch(r1) {
                case 75: goto L17;
                default: goto L16;
            }
        L16:
            goto L6e
        L17:
            int r1 = o.ez.d.j
            int r1 = r1 + 55
            int r4 = r1 % 128
            o.ez.d.e = r4
            int r1 = r1 % 2
            if (r1 == 0) goto L25
            r1 = r3
            goto L26
        L25:
            r1 = r2
        L26:
            switch(r1) {
                case 0: goto L34;
                default: goto L29;
            }
        L29:
            java.util.List r1 = r8.f()
            java.lang.Object r1 = r1.get(r3)
            if (r1 == 0) goto L49
            goto L47
        L34:
            java.util.List r1 = r8.f()
            java.lang.Object r1 = r1.get(r2)
            if (r1 == 0) goto L41
            r1 = 38
            goto L43
        L41:
            r1 = 27
        L43:
            switch(r1) {
                case 38: goto L4e;
                default: goto L46;
            }
        L46:
            goto L6e
        L47:
            r1 = r2
            goto L4a
        L49:
            r1 = r3
        L4a:
            switch(r1) {
                case 1: goto L4e;
                default: goto L4d;
            }
        L4d:
            goto L6e
        L4e:
            java.util.List r0 = r8.f()
            java.lang.Object r0 = r0.get(r3)
            o.ff.c r0 = (o.ff.c) r0
            o.eg.b r0 = r0.n()
            int r1 = o.ez.d.e
            int r1 = r1 + 25
            int r4 = r1 % 128
            o.ez.d.j = r4
            int r1 = r1 % 2
            if (r1 != 0) goto L6a
            r1 = r2
            goto L6b
        L6a:
            r1 = r3
        L6b:
            switch(r1) {
                case 0: goto L6e;
                default: goto L6e;
            }
        L6e:
            o.eg.b r1 = new o.eg.b
            r1.<init>()
            int r4 = android.graphics.Color.alpha(r3)
            int r4 = 9 - r4
            int r5 = android.view.KeyEvent.normalizeMetaState(r3)
            int r5 = r5 + 18
            byte r5 = (byte) r5
            java.lang.Object[] r6 = new java.lang.Object[r2]
            java.lang.String r7 = "\u001e\u001f\t\u0012\u000e\u001c\u0011\u0016㗻"
            y(r4, r7, r5, r6)
            r4 = r6[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            r1.d(r4, r0)
            o.eg.b r0 = new o.eg.b
            r0.<init>()
            int r4 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r4 = (byte) r4
            int r4 = 6 - r4
            int r5 = android.view.Gravity.getAbsoluteGravity(r3, r3)
            int r5 = r5 + 62
            byte r5 = (byte) r5
            java.lang.Object[] r6 = new java.lang.Object[r2]
            java.lang.String r7 = "\u001e\u0001\u0017\u0000\u0011\r㘹"
            y(r4, r7, r5, r6)
            r4 = r6[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            r0.d(r4, r1)
            o.eg.b r1 = new o.eg.b
            r1.<init>()
            long r4 = android.os.SystemClock.elapsedRealtimeNanos()
            r6 = 0
            int r4 = (r4 > r6 ? 1 : (r4 == r6 ? 0 : -1))
            int r4 = 1 - r4
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r5 = "ᶤ᷐萶┓´綘崂鸣飢ꈍ\ud82fᓸល"
            u(r5, r4, r2)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            r1.d(r2, r0)
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.d.x():o.eg.b");
    }

    @Override // o.ey.e
    public final o.fc.e k() {
        int i = j + 13;
        e = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                switch (f() != null) {
                    case true:
                        int i2 = e + 65;
                        j = i2 % 128;
                        int i3 = i2 % 2;
                        if (f().get(0) != null) {
                            if (j() == null) {
                                g.c();
                                Object[] objArr = new Object[1];
                                u("\ue366\ue330踈⼫︋ߺ볬\ue445昞ꠛ㧎溑\ue953◼늛\ue99f汻뺳ⱄ瓃\uf7a8㮖\ua95e￭竀땿∥", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr);
                                String intern = ((String) objArr[0]).intern();
                                StringBuilder sb = new StringBuilder();
                                Object[] objArr2 = new Object[1];
                                u("䏘䎿튬玃긠鎔\uecc0瀄욕\uf4a6槨贈䧡祘\ue2a3緖쳅\ue23b籟\ue0a7圳朣省殾\uda7f\ue9eb爏\uee57嵮ዹ쿖鄊\ue7a1鞷䣡ᐇ櫶ᡒ엔麧\ued90鵿庬Ƣ瀤؟\ud867蒛ﬔ裇唃ཬ縘\u0dbc\u2e74", ViewConfiguration.getMaximumDrawingCacheSize() >> 24, objArr2);
                                StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(d());
                                Object[] objArr3 = new Object[1];
                                y(26 - Color.argb(0, 0, 0, 0), "\u0013\u0001\u000e\u0004\f\u0007\u001f\u000e\u0018\u0013\u001f\f\"\u000f\u001c\u000e\u0012\u0013\u0012\u0018 \b\u000e\u0001\u0018\u0012", (byte) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 91), objArr3);
                                g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
                                return null;
                            }
                            Date d2 = j().d();
                            if (d2 != null && d2.before(new Date())) {
                                g.c();
                                Object[] objArr4 = new Object[1];
                                u("\ue366\ue330踈⼫︋ߺ볬\ue445昞ꠛ㧎溑\ue953◼늛\ue99f汻뺳ⱄ瓃\uf7a8㮖\ua95e￭竀땿∥", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1, objArr4);
                                String intern2 = ((String) objArr4[0]).intern();
                                StringBuilder sb2 = new StringBuilder();
                                Object[] objArr5 = new Object[1];
                                u("䏘䎿튬玃긠鎔\uecc0瀄욕\uf4a6槨贈䧡祘\ue2a3緖쳅\ue23b籟\ue0a7圳朣省殾\uda7f\ue9eb爏\uee57嵮ዹ쿖鄊\ue7a1鞷䣡ᐇ櫶ᡒ엔麧\ued90鵿庬Ƣ瀤؟\ud867蒛ﬔ裇唃ཬ縘\u0dbc\u2e74", TextUtils.indexOf((CharSequence) "", '0', 0) + 1, objArr5);
                                StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(d());
                                Object[] objArr6 = new Object[1];
                                y(Color.argb(0, 0, 0, 0) + 23, "\u0013\u0001\u000e\u0004\u000e\u0011\u0007\f\u0015\u0000\u0003!\u0018\u0014\u001f\u0011\u0018\u0013\u001f\f\u0000\u0014㙙", (byte) (107 - TextUtils.getCapsMode("", 0, 0)), objArr6);
                                g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
                            }
                            return f().get(0).d(this.c);
                        }
                        break;
                }
                g.c();
                Object[] objArr7 = new Object[1];
                u("\ue366\ue330踈⼫︋ߺ볬\ue445昞ꠛ㧎溑\ue953◼늛\ue99f汻뺳ⱄ瓃\uf7a8㮖\ua95e￭竀땿∥", TextUtils.indexOf("", "", 0), objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr8 = new Object[1];
                u("䏘䎿튬玃긠鎔\uecc0瀄욕\uf4a6槨贈䧡祘\ue2a3緖쳅\ue23b籟\ue0a7圳朣省殾\uda7f\ue9eb爏\uee57嵮ዹ쿖鄊\ue7a1鞷䣡ᐇ櫶ᡒ엔麧\ued90鵿庬Ƣ瀤؟\ud867蒛ﬔ裇唃ཬ縘\u0dbc\u2e74", ViewConfiguration.getTouchSlop() >> 8, objArr8);
                StringBuilder append3 = sb3.append(((String) objArr8[0]).intern()).append(d());
                Object[] objArr9 = new Object[1];
                u("輝輽\uda40筢쮔쪄襡⤩ਕﱜేꎢ蔫熨蜕ⓙ\r\ueaa2᧳릷鯄濙", 1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr9);
                g.d(intern3, append3.append(((String) objArr9[0]).intern()).toString());
                return null;
            default:
                f();
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 364
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.d.u(java.lang.String, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:51:0x017d  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x019d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void y(int r28, java.lang.String r29, byte r30, java.lang.Object[] r31) {
        /*
            Method dump skipped, instructions count: 1016
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.d.y(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
