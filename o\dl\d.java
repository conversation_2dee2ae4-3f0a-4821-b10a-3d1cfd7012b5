package o.dl;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import o.a.o;
import o.h.a;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dl\d.smali */
public final class d extends a {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static int c;
    private static int d;
    private static long e;
    private static int g;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        g = 1;
        c();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        SystemClock.elapsedRealtime();
        int i = g + 81;
        d = i % 128;
        switch (i % 2 != 0 ? 'C' : (char) 25) {
            case 'C':
                int i2 = 43 / 0;
                break;
        }
    }

    static void c() {
        a = (char) 64282;
        c = 161105445;
        e = 6565854932352255525L;
    }

    static void init$0() {
        $$d = new byte[]{11, -55, -41, 6};
        $$e = Opcodes.INSTANCEOF;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r0 = o.dl.d.$$d
            int r7 = 106 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.d.k(int, short, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public d() {
        /*
            r9 = this;
            int r0 = android.view.ViewConfiguration.getScrollBarSize()
            int r1 = r0 >> 8
            java.lang.String r2 = "Ʊ为귍ⶕ딜ᐳ䛥嘎룏\ue505炇坣\udec3觏\udbbd鯤睙\uf16cℭ鿲擮Ὰ쳏ູ㏀펟뫱ᡒ"
            r0 = 0
            int r3 = android.graphics.Color.argb(r0, r0, r0, r0)
            int r3 = r3 + 4147
            char r3 = (char) r3
            java.lang.String r4 = "≎⏽㏤툐"
            java.lang.String r5 = "\u0000\u0000\u0000\u0000"
            r7 = 1
            java.lang.Object[] r8 = new java.lang.Object[r7]
            r6 = r8
            h(r1, r2, r3, r4, r5, r6)
            r0 = r8[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            o.i.i r1 = o.i.i.b
            r9.<init>(r0, r1, r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.d.<init>():void");
    }

    private static void h(int i, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] cArr3;
        switch (str3 != null ? ']' : 'F') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                cArr = str3;
                break;
            default:
                int i2 = $10 + 65;
                $11 = i2 % 128;
                switch (i2 % 2 == 0 ? 'L' : (char) 14) {
                    case Base64.mimeLineLength /* 76 */:
                        str3.toCharArray();
                        throw null;
                    default:
                        cArr = str3.toCharArray();
                        break;
                }
        }
        char[] cArr4 = cArr;
        switch (str2 != null ? (char) 26 : '\f') {
            case '\f':
                cArr2 = str2;
                break;
            default:
                cArr2 = str2.toCharArray();
                break;
        }
        char[] cArr5 = cArr2;
        int i3 = 0;
        if (str != null) {
            int i4 = $11 + 31;
            $10 = i4 % 128;
            if (i4 % 2 != 0) {
                cArr3 = str.toCharArray();
                int i5 = 85 / 0;
            } else {
                cArr3 = str.toCharArray();
            }
        } else {
            cArr3 = str;
        }
        o oVar = new o();
        int length = cArr5.length;
        char[] cArr6 = new char[length];
        int length2 = cArr4.length;
        char[] cArr7 = new char[length2];
        System.arraycopy(cArr5, 0, cArr6, 0, length);
        System.arraycopy(cArr4, 0, cArr7, 0, length2);
        cArr6[0] = (char) (cArr6[0] ^ c2);
        cArr7[2] = (char) (cArr7[2] + ((char) i));
        int length3 = cArr3.length;
        char[] cArr8 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(10 - (Process.myPid() >> 22), (char) ((ViewConfiguration.getEdgeSlop() >> 16) + 20954), KeyEvent.getDeadChar(i3, i3) + 344);
                    byte b = (byte) (-1);
                    Object[] objArr3 = new Object[1];
                    k(b, (byte) (b & 7), (byte) i3, objArr3);
                    String str4 = (String) objArr3[i3];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i3] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(ExpandableListView.getPackedPositionType(0L) + 10, (char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), 207 - ((Process.getThreadPriority(i3) + 20) >> 6));
                        byte b2 = (byte) (-1);
                        byte b3 = (byte) (b2 & 5);
                        Object[] objArr5 = new Object[1];
                        k(b2, b3, (byte) (b3 - 5), objArr5);
                        String str5 = (String) objArr5[i3];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i3] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i6 = cArr6[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr7[intValue]);
                        objArr6[1] = Integer.valueOf(i6);
                        objArr6[i3] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0', i3, i3) + 12, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 280 - TextUtils.lastIndexOf("", '0', i3));
                            byte b4 = (byte) (-1);
                            byte b5 = (byte) (b4 + 4);
                            Object[] objArr7 = new Object[1];
                            k(b4, b5, (byte) (b5 - 3), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr6[intValue2] * 32718), Integer.valueOf(cArr7[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(20 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (char) (14687 - Color.blue(0)), View.MeasureSpec.getMode(0) + Opcodes.IREM);
                                byte b6 = (byte) (-1);
                                byte b7 = (byte) (b6 + 1);
                                Object[] objArr9 = new Object[1];
                                k(b6, b7, b7, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr7[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr6[intValue2] = oVar.d;
                            char[] cArr9 = cArr8;
                            cArr9[oVar.e] = (char) ((((int) (c ^ 6565854932352255525L)) ^ ((cArr6[intValue2] ^ r7[oVar.e]) ^ (e ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                            oVar.e++;
                            int i7 = $10 + 69;
                            $11 = i7 % 128;
                            int i8 = i7 % 2;
                            cArr8 = cArr9;
                            i3 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr8);
    }
}
