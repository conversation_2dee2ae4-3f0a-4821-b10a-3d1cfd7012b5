package o.eo;

import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import kotlin.io.encoding.Base64;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\h.smali */
public final class h implements o.ei.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final h a;
    private static final /* synthetic */ h[] b;
    public static final h c;
    public static final h d;
    private static int f;
    private static long h;
    private static int j;
    private final String e;

    static void c() {
        h = 8612205792147897803L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.eo.h.$$a
            int r8 = r8 * 3
            int r8 = 71 - r8
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            int r6 = r6 + 1
            r1[r3] = r4
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.h.i(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{26, 103, -21, 32};
        $$b = 72;
    }

    private static /* synthetic */ h[] a() {
        int i = f + 67;
        j = i % 128;
        switch (i % 2 == 0 ? 'Q' : 'L') {
            case Base64.mimeLineLength /* 76 */:
                return new h[]{a, c, d};
            default:
                h[] hVarArr = new h[3];
                hVarArr[1] = a;
                hVarArr[1] = c;
                hVarArr[4] = d;
                return hVarArr;
        }
    }

    public static h valueOf(String str) {
        int i = f + 1;
        j = i % 128;
        char c2 = i % 2 == 0 ? '8' : 'C';
        h hVar = (h) Enum.valueOf(h.class, str);
        switch (c2) {
            case '8':
                int i2 = 72 / 0;
            default:
                return hVar;
        }
    }

    public static h[] values() {
        int i = f + 109;
        j = i % 128;
        switch (i % 2 != 0) {
            case true:
                return (h[]) b.clone();
            default:
                int i2 = 34 / 0;
                return (h[]) b.clone();
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        j = 1;
        c();
        Object[] objArr = new Object[1];
        g("㻶⻏㺠\uf15bᎺ譹謟㰬쫂Ὴ齗ₐ혲", 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g("\uebd6⥤\ueb80\ud8d6ᐱ\udfadꊲ棘ῂᡁ뛺瑤̲", ViewConfiguration.getScrollBarFadeDuration() >> 16, objArr2);
        a = new h(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g("䀩왑䁽ॊשׁ\uf19e猴䛃됕\uf75c杖婳ꣽ\ue380", KeyEvent.keyCodeFromString(""), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g("귳⯪궧\udf56ᚱ鲭ꔡ⯇姢\u1ac2녧㝥䔒ช뷛", 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr4);
        c = new h(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        g("໗ﻦ\u0e85곜쎗셜횒瘕\ufaf4쿧싀", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        g("펨ࠬ폺益㕽韠ತ₉➫㤍\u18f6", TextUtils.indexOf("", ""), objArr6);
        d = new h(intern3, 2, ((String) objArr6[0]).intern());
        b = a();
        int i = f + 43;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private h(String str, int i, String str2) {
        this.e = str2;
    }

    @Override // o.ei.b
    public final String e() {
        int i = j + 57;
        int i2 = i % 128;
        f = i2;
        int i3 = i % 2;
        String str = this.e;
        int i4 = i2 + 83;
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 358
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.h.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
