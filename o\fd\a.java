package o.fd;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.ee.g;
import o.eg.b;
import o.fc.c;
import o.fc.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fd\a.smali */
public final class a extends d {
    private static char a;
    private static char e;
    private static int f;
    private static char g;
    private static char h;
    private int b;
    private int c;
    private int d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int i = 1;

    static {
        f = 0;
        f();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        int i2 = i + Opcodes.DREM;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void f() {
        e = (char) 13810;
        h = (char) 49313;
        g = (char) 19082;
        a = (char) 47429;
    }

    public a(boolean z, c cVar, short s) {
        super(z, cVar, s);
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:21:0x003c. Please report as an issue. */
    @Override // o.fc.d
    public final boolean e(String str, o.dd.e eVar) {
        int i2 = i + 79;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 3 : 'H') {
            case 'H':
                switch (b() == c.b ? (char) 21 : '0') {
                    case 21:
                        int i3 = i + 87;
                        f = i3 % 128;
                        switch (i3 % 2 != 0) {
                        }
                        c(c.a);
                        return true;
                    default:
                        return false;
                }
            default:
                b();
                c cVar = c.b;
                throw null;
        }
    }

    public final o.eg.e c(int i2) throws o.eg.d {
        int i3 = f + 91;
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        switch (this.d != 0) {
            case true:
                o.eg.e eVar = new o.eg.e();
                int i6 = 0;
                while (true) {
                    int i7 = this.c;
                    if (i6 >= i7) {
                        while (i7 < this.d) {
                            b bVar = new b();
                            Object[] objArr = new Object[1];
                            k("俑\u2454䳸秽", 3 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
                            bVar.d(((String) objArr[0]).intern(), i2 + i7);
                            Object[] objArr2 = new Object[1];
                            k("脤佨俑\u2454凂ᾡ", View.resolveSize(0, 0) + 6, objArr2);
                            bVar.d(((String) objArr2[0]).intern(), b().c());
                            eVar.b(bVar);
                            i7++;
                        }
                        return eVar;
                    }
                    b bVar2 = new b();
                    Object[] objArr3 = new Object[1];
                    k("俑\u2454䳸秽", 3 - (ViewConfiguration.getScrollBarSize() >> 8), objArr3);
                    bVar2.d(((String) objArr3[0]).intern(), i2 + i6);
                    Object[] objArr4 = new Object[1];
                    k("脤佨俑\u2454凂ᾡ", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 6, objArr4);
                    bVar2.d(((String) objArr4[0]).intern(), c.d.c());
                    eVar.b(bVar2);
                    i6++;
                }
            default:
                int i8 = i4 + 55;
                f = i8 % 128;
                int i9 = i8 % 2;
                return null;
        }
    }

    @Override // o.fc.d
    public final short e() {
        int i2 = i + 69;
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        short s = (short) (this.d - this.c);
        switch (s > 0 ? '0' : Typography.less) {
            case '<':
                return (short) 0;
            default:
                int i5 = i3 + Opcodes.DDIV;
                i = i5 % 128;
                int i6 = i5 % 2;
                return s;
        }
    }

    public final int h() {
        int i2 = i + 39;
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        int i5 = this.b;
        int i6 = i3 + 73;
        i = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final int i() {
        int i2 = f + 13;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        int i5 = this.c;
        int i6 = i3 + 81;
        f = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final int j() {
        int i2 = f + 41;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        int i5 = this.d;
        int i6 = i3 + 45;
        f = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final void a(int i2) {
        int i3 = f + 11;
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        this.c = i2;
        int i6 = i4 + 81;
        f = i6 % 128;
        int i7 = i6 % 2;
    }

    public final o.fc.e d(int i2) {
        int i3 = i + 47;
        f = i3 % 128;
        int i4 = i3 % 2;
        switch (!a()) {
            case true:
                g.c();
                Object[] objArr = new Object[1];
                k("ﷁꃥ볜\ueae6⋲嘣㇟⬹ꛩᒫꎂ乇삛\uf8ca", 13 - ExpandableListView.getPackedPositionGroup(0L), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k("扮덧醱\u09bbﾗ놲櫘촇叀\ue9e9绵㻷穪ꕿ\ue94b\ue071䬔ꅗꎂ乇\uf32cꗠ\ue5fd\ue5e8鈅\ue2efล溙\u0ae4㔰ଁڲ\ueab6\ud97b婺\u2d2e\ud881ം藭\uead5䆭࠾軪⇕荔啳\ud8e7⟇醱\u09bb녗☕᥅署", ((Process.getThreadPriority(0) + 20) >> 6) + 54, objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                int i5 = f + 47;
                i = i5 % 128;
                int i6 = i5 % 2;
                break;
            default:
                int i7 = this.c;
                switch (i7 < this.d ? '3' : '/') {
                    case '3':
                        int i8 = i7 + i2;
                        switch (i8 >= 65535 ? '\t' : 'S') {
                            case Opcodes.AASTORE /* 83 */:
                                break;
                            default:
                                g.c();
                                Object[] objArr3 = new Object[1];
                                k("ﷁꃥ볜\ueae6⋲嘣㇟⬹ꛩᒫꎂ乇삛\uf8ca", 13 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr3);
                                String intern2 = ((String) objArr3[0]).intern();
                                Object[] objArr4 = new Object[1];
                                k("扮덧醱\u09bbﾗ놲櫘촇叀\ue9e9绵㻷穪ꕿ\ue94b\ue071䬔ꅗꎂ乇\uf32cꗠ\ue5fd\ue5e8鈅\ue2efล溙\u0ae4㔰ଁڲ\ueab6\ud97b暟ꊊ찊슄㉔đ뙃斬퉫ﲂ赽軜愜蚯쟨\u05fe坂㺅롫軣庬ﮠꐕ믟묤왧\uf182﹦\ud8e7⟇Ȏ챐რ\ue36f", 67 - Color.blue(0), objArr4);
                                g.e(intern2, ((String) objArr4[0]).intern());
                                break;
                        }
                    default:
                        int i9 = f + 67;
                        i = i9 % 128;
                        if (i9 % 2 == 0) {
                        }
                        g.c();
                        Object[] objArr5 = new Object[1];
                        k("ﷁꃥ볜\ueae6⋲嘣㇟⬹ꛩᒫꎂ乇삛\uf8ca", ImageFormat.getBitsPerPixel(0) + 14, objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        k("扮덧醱\u09bbﾗ놲櫘촇叀\ue9e9绵㻷穪ꕿ\ue94b\ue071䬔ꅗꎂ乇\uf32cꗠ\ue5fd\ue5e8鈅\ue2efล溙\u0ae4㔰ଁڲ\ueab6\ud97b婺\u2d2e\ud881ം㉔đ뙃斬퉫ﲂ赽軜愜蚯쟨\u05feㄭ῍뙃斬庬ﮠꐕ믟묤왧\uf182﹦只訏ล溙\u0ae4㔰ଁڲ鏶荛⒮\ueb3c\uf0e9㭗", Color.red(0) + 76, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        break;
                }
        }
        return null;
    }

    public final void b(int i2) {
        int i3 = i + 59;
        int i4 = i3 % 128;
        f = i4;
        int i5 = i3 % 2;
        this.b = i2;
        int i6 = i4 + 71;
        i = i6 % 128;
        int i7 = i6 % 2;
    }

    public final void e(int i2) {
        int i3 = i + 55;
        f = i3 % 128;
        char c = i3 % 2 != 0 ? '\'' : '4';
        this.d = i2;
        switch (c) {
            case '\'':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 572
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fd.a.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
