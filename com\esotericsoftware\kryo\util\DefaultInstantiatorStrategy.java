package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.reflectasm.ConstructorAccess;
import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import org.objenesis.instantiator.ObjectInstantiator;
import org.objenesis.strategy.InstantiatorStrategy;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\DefaultInstantiatorStrategy.smali */
public class DefaultInstantiatorStrategy implements InstantiatorStrategy {
    private InstantiatorStrategy fallbackStrategy;

    public DefaultInstantiatorStrategy() {
    }

    public DefaultInstantiatorStrategy(InstantiatorStrategy fallbackStrategy) {
        this.fallbackStrategy = fallbackStrategy;
    }

    public void setFallbackInstantiatorStrategy(InstantiatorStrategy fallbackStrategy) {
        this.fallbackStrategy = fallbackStrategy;
    }

    public InstantiatorStrategy getFallbackInstantiatorStrategy() {
        return this.fallbackStrategy;
    }

    @Override // org.objenesis.strategy.InstantiatorStrategy
    public ObjectInstantiator newInstantiatorOf(final Class type) {
        Constructor ctor;
        if (!Util.isAndroid) {
            Class enclosingType = type.getEnclosingClass();
            boolean isNonStaticMemberClass = (enclosingType == null || !type.isMemberClass() || Modifier.isStatic(type.getModifiers())) ? false : true;
            if (!isNonStaticMemberClass) {
                try {
                    final ConstructorAccess access = ConstructorAccess.get(type);
                    return new ObjectInstantiator() { // from class: com.esotericsoftware.kryo.util.DefaultInstantiatorStrategy.1
                        @Override // org.objenesis.instantiator.ObjectInstantiator
                        public Object newInstance() {
                            try {
                                return access.newInstance();
                            } catch (Exception ex) {
                                throw new KryoException("Error constructing instance of class: " + Util.className(type), ex);
                            }
                        }
                    };
                } catch (Exception e) {
                }
            }
        }
        try {
            try {
                ctor = type.getConstructor(null);
            } catch (Exception e2) {
                ctor = type.getDeclaredConstructor(null);
                ctor.setAccessible(true);
            }
            final Constructor constructor = ctor;
            return new ObjectInstantiator() { // from class: com.esotericsoftware.kryo.util.DefaultInstantiatorStrategy.2
                @Override // org.objenesis.instantiator.ObjectInstantiator
                public Object newInstance() {
                    try {
                        return constructor.newInstance(new Object[0]);
                    } catch (Exception ex) {
                        throw new KryoException("Error constructing instance of class: " + Util.className(type), ex);
                    }
                }
            };
        } catch (Exception e3) {
            InstantiatorStrategy instantiatorStrategy = this.fallbackStrategy;
            if (instantiatorStrategy == null) {
                if (type.isMemberClass() && !Modifier.isStatic(type.getModifiers())) {
                    throw new KryoException("Class cannot be created (non-static member class): " + Util.className(type));
                }
                StringBuilder message = new StringBuilder("Class cannot be created (missing no-arg constructor): " + Util.className(type));
                if (type.getSimpleName().equals("")) {
                    message.append("\nNote: This is an anonymous class, which is not serializable by default in Kryo. Possible solutions:\n").append("1. Remove uses of anonymous classes, including double brace initialization, from the containing\n").append("class. This is the safest solution, as anonymous classes don't have predictable names for serialization.\n").append("2. Register a FieldSerializer for the containing class and call FieldSerializer\n").append("setIgnoreSyntheticFields(false) on it. This is not safe but may be sufficient temporarily.");
                }
                throw new KryoException(message.toString());
            }
            return instantiatorStrategy.newInstantiatorOf(type);
        }
    }
}
