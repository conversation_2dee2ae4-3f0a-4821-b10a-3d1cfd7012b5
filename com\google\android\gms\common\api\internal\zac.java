package com.google.android.gms.common.api.internal;

import com.google.android.gms.common.Feature;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\api\internal\zac.smali */
public abstract class zac extends zai {
    public zac(int i) {
        super(i);
    }

    public abstract boolean zaa(zabq zabqVar);

    public abstract Feature[] zab(zabq zabqVar);
}
