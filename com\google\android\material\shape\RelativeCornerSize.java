package com.google.android.material.shape;

import android.graphics.RectF;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\material\shape\RelativeCornerSize.smali */
public final class RelativeCornerSize implements CornerSize {
    private final float percent;

    public RelativeCornerSize(float percent) {
        this.percent = percent;
    }

    public float getRelativePercent() {
        return this.percent;
    }

    @Override // com.google.android.material.shape.CornerSize
    public float getCornerSize(RectF bounds) {
        return this.percent * bounds.height();
    }

    public boolean equals(Object o2) {
        if (this == o2) {
            return true;
        }
        if (!(o2 instanceof RelativeCornerSize)) {
            return false;
        }
        RelativeCornerSize that = (RelativeCornerSize) o2;
        return this.percent == that.percent;
    }

    public int hashCode() {
        Object[] hashedFields = {Float.valueOf(this.percent)};
        return Arrays.hashCode(hashedFields);
    }
}
