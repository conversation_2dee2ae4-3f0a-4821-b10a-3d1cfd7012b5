package com.google.android.gms.tapandpay.quickaccesswallet;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\WalletCardIntentExtra.smali */
public final class WalletCardIntentExtra extends AbstractSafeParcelable {
    public static final Parcelable.Creator<WalletCardIntentExtra> CREATOR = new zzn();
    private String zza;
    private int zzb;
    private String zzc;
    private byte[] zzd;
    private boolean zze;
    private int zzf;
    private long zzg;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\WalletCardIntentExtra$Builder.smali */
    public static final class Builder {
        private final WalletCardIntentExtra zza;

        public Builder() {
            this.zza = new WalletCardIntentExtra(null);
        }

        public WalletCardIntentExtra build() {
            return this.zza;
        }

        public Builder setKey(String key) {
            this.zza.zza = key;
            return this;
        }

        public Builder setValueBoolean(boolean valueBoolean) {
            this.zza.zze = valueBoolean;
            return this;
        }

        public Builder setValueBytes(byte[] valueBytes) {
            this.zza.zzd = valueBytes;
            return this;
        }

        public Builder setValueInt(int valueInt) {
            this.zza.zzf = valueInt;
            return this;
        }

        public Builder setValueLong(long valueLong) {
            this.zza.zzg = valueLong;
            return this;
        }

        public Builder setValueString(String valueString) {
            this.zza.zzc = valueString;
            return this;
        }

        public Builder setValueType(int valueType) {
            this.zza.zzb = valueType;
            return this;
        }

        public Builder(WalletCardIntentExtra origin) {
            WalletCardIntentExtra walletCardIntentExtra = new WalletCardIntentExtra(null);
            this.zza = walletCardIntentExtra;
            walletCardIntentExtra.zza = origin.zza;
            walletCardIntentExtra.zzb = origin.zzb;
            walletCardIntentExtra.zzc = origin.zzc;
            walletCardIntentExtra.zzd = origin.zzd;
            walletCardIntentExtra.zze = origin.zze;
            walletCardIntentExtra.zzf = origin.zzf;
            walletCardIntentExtra.zzg = origin.zzg;
        }
    }

    private WalletCardIntentExtra() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof WalletCardIntentExtra) {
            WalletCardIntentExtra walletCardIntentExtra = (WalletCardIntentExtra) other;
            if (Objects.equal(this.zza, walletCardIntentExtra.zza) && Objects.equal(Integer.valueOf(this.zzb), Integer.valueOf(walletCardIntentExtra.zzb)) && Objects.equal(this.zzc, walletCardIntentExtra.zzc) && Arrays.equals(this.zzd, walletCardIntentExtra.zzd) && Objects.equal(Boolean.valueOf(this.zze), Boolean.valueOf(walletCardIntentExtra.zze)) && Objects.equal(Integer.valueOf(this.zzf), Integer.valueOf(walletCardIntentExtra.zzf)) && Objects.equal(Long.valueOf(this.zzg), Long.valueOf(walletCardIntentExtra.zzg))) {
                return true;
            }
        }
        return false;
    }

    public String getKey() {
        return this.zza;
    }

    public boolean getValueBoolean() {
        return this.zze;
    }

    public byte[] getValueBytes() {
        return this.zzd;
    }

    public int getValueInt() {
        return this.zzf;
    }

    public long getValueLong() {
        return this.zzg;
    }

    public String getValueString() {
        return this.zzc;
    }

    public int getValueType() {
        return this.zzb;
    }

    public int hashCode() {
        return Objects.hashCode(this.zza, Integer.valueOf(this.zzb), this.zzc, Integer.valueOf(Arrays.hashCode(this.zzd)), Boolean.valueOf(this.zze), Integer.valueOf(this.zzf), Long.valueOf(this.zzg));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, getKey(), false);
        SafeParcelWriter.writeInt(dest, 2, getValueType());
        SafeParcelWriter.writeString(dest, 3, getValueString(), false);
        SafeParcelWriter.writeByteArray(dest, 4, getValueBytes(), false);
        SafeParcelWriter.writeBoolean(dest, 5, getValueBoolean());
        SafeParcelWriter.writeInt(dest, 6, getValueInt());
        SafeParcelWriter.writeLong(dest, 7, getValueLong());
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    /* synthetic */ WalletCardIntentExtra(zzm zzmVar) {
    }

    WalletCardIntentExtra(String str, int i, String str2, byte[] bArr, boolean z, int i2, long j) {
        this.zza = str;
        this.zzb = i;
        this.zzc = str2;
        this.zzd = bArr;
        this.zze = z;
        this.zzf = i2;
        this.zzg = j;
    }
}
