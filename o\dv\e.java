package o.dv;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.h;
import o.dx.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dv\e.smali */
public final class e implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static int e;
    private final d d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        d();
        KeyEvent.normalizeMetaState(0);
        TextUtils.indexOf("", "", 0, 0);
        ExpandableListView.getPackedPositionChild(0L);
        int i = c + Opcodes.DNEG;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        b = 874635491;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x003a). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.dv.e.$$a
            int r7 = r7 * 2
            int r7 = 3 - r7
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r8 = r8 * 2
            int r8 = 109 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1d
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L3a
        L1d:
            r3 = r2
        L1e:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r6) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L3a:
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dv.e.f(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{62, -87, 120, -83};
        $$b = 21;
    }

    public e(d dVar) {
        this.d = dVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.dv.a
    public final java.io.InputStream b() throws o.du.e {
        /*
            Method dump skipped, instructions count: 458
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dv.e.b():java.io.InputStream");
    }

    private static void a(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        Object obj = null;
        if (str != null) {
            int i4 = $10 + 89;
            $11 = i4 % 128;
            if (i4 % 2 == 0) {
                str.toCharArray();
                obj.hashCode();
                throw null;
            }
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr3 = cArr;
        h hVar = new h();
        char[] cArr4 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            hVar.b = cArr3[hVar.a];
            cArr4[hVar.a] = (char) (i3 + hVar.b);
            int i5 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr4[i5]), Integer.valueOf(b)};
                Object obj2 = o.e.a.s.get(2038615114);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c(12 - Drawable.resolveOpacity(0, 0), (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), ExpandableListView.getPackedPositionGroup(0L) + 459);
                    byte b2 = (byte) 0;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    f(b2, b3, (byte) (b3 + 1), objArr3);
                    obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj2);
                }
                cArr4[i5] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj3 = o.e.a.s.get(-1412673904);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) TextUtils.getOffsetAfter("", 0), View.combineMeasuredStates(0, 0) + 313);
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        f(b4, b5, b5, objArr5);
                        obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj3);
                    }
                    ((Method) obj3).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            hVar.c = i;
            char[] cArr5 = new char[i2];
            System.arraycopy(cArr4, 0, cArr5, 0, i2);
            System.arraycopy(cArr5, 0, cArr4, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr5, hVar.c, cArr4, 0, i2 - hVar.c);
            int i6 = $11 + Opcodes.DSUB;
            $10 = i6 % 128;
            int i7 = i6 % 2;
        }
        switch (z) {
            case false:
                break;
            default:
                int i8 = $11 + 37;
                $10 = i8 % 128;
                switch (i8 % 2 == 0) {
                    case true:
                        cArr2 = new char[i2];
                        hVar.a = 0;
                        break;
                    default:
                        cArr2 = new char[i2];
                        hVar.a = 1;
                        break;
                }
                while (true) {
                    switch (hVar.a < i2) {
                        case false:
                            cArr4 = cArr2;
                            break;
                        default:
                            cArr2[hVar.a] = cArr4[(i2 - hVar.a) - 1];
                            try {
                                Object[] objArr6 = {hVar, hVar};
                                Object obj4 = o.e.a.s.get(-1412673904);
                                if (obj4 == null) {
                                    Class cls3 = (Class) o.e.a.c(10 - TextUtils.lastIndexOf("", '0', 0, 0), (char) ExpandableListView.getPackedPositionType(0L), TextUtils.getOffsetAfter("", 0) + 313);
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    f(b6, b7, b7, objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr6);
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                    }
                }
        }
        objArr[0] = new String(cArr4);
    }
}
