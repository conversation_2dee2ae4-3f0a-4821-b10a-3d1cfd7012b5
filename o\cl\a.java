package o.cl;

import com.esotericsoftware.asm.Opcodes;
import java.util.List;
import o.eg.b;
import o.ei.i;
import o.et.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cl\a.smali */
public final class a extends d<e> {
    private static int e = 0;
    private static int d = 1;

    @Override // o.cl.d
    final void d(List<e> list, b bVar) {
        int i = e;
        int i2 = (i & 89) + (i | 89);
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    @Override // o.cl.d, o.cc.e
    public final /* synthetic */ List a(String str, String str2, int i, String str3, b bVar) throws i {
        int i2 = d;
        int i3 = (i2 ^ Opcodes.DMUL) + ((i2 & Opcodes.DMUL) << 1);
        e = i3 % 128;
        int i4 = i3 % 2;
        List a = super.a(str, str2, i, str3, bVar);
        int i5 = d;
        int i6 = (i5 & Opcodes.DREM) + (i5 | Opcodes.DREM);
        e = i6 % 128;
        switch (i6 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return a;
        }
    }

    @Override // o.cl.d
    public final /* synthetic */ e c(String str, String str2, int i, String str3) {
        int i2 = e;
        int i3 = (i2 & 99) + (i2 | 99);
        d = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return super.c(str, str2, i, str3);
            default:
                super.c(str, str2, i, str3);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.cl.d
    final e e(String str, String str2, int i, String str3) {
        e eVar = new e(str, str2, i, str3);
        int i2 = e;
        int i3 = ((i2 | 33) << 1) - (i2 ^ 33);
        d = i3 % 128;
        int i4 = i3 % 2;
        return eVar;
    }
}
