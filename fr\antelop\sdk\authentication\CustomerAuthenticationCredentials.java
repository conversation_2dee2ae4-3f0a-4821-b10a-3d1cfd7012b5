package fr.antelop.sdk.authentication;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationCredentials.smali */
public abstract class CustomerAuthenticationCredentials {
    public final int hashCode() {
        return super.hashCode();
    }

    public final boolean equals(Object obj) {
        return super.equals(obj);
    }

    protected final void finalize() throws Throwable {
        super.finalize();
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
