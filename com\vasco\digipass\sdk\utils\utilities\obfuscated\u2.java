package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u2.smali */
public class u2 extends r2 {
    private BigInteger c;

    public u2(BigInteger bigInteger, t2 t2Var) {
        super(true, t2Var);
        this.c = bigInteger;
    }

    public BigInteger b() {
        return this.c;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r2
    public boolean equals(Object obj) {
        return (obj instanceof u2) && ((u2) obj).b().equals(this.c) && super.equals(obj);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r2
    public int hashCode() {
        return this.c.hashCode() ^ super.hashCode();
    }
}
