package bc.org.bouncycastle.crypto.engines;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.params.KeyParameter;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w3;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\engines\DESedeEngine.smali */
public class DESedeEngine extends a implements BlockCipher {
    private int[] n = null;

    /* renamed from: o, reason: collision with root package name */
    private int[] f8o = null;
    private int[] p = null;
    private boolean q;

    public DESedeEngine() {
        t1.a(new w3(getAlgorithmName(), a()));
    }

    private int a() {
        int[] iArr = this.n;
        if (iArr == null || iArr != this.p) {
            return Opcodes.IREM;
        }
        return 80;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return "DESede";
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return 8;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) {
        if (!(cipherParameters instanceof KeyParameter)) {
            throw new IllegalArgumentException("invalid parameter passed to DESede init - " + cipherParameters.getClass().getName());
        }
        byte[] key = ((KeyParameter) cipherParameters).getKey();
        if (key.length != 24 && key.length != 16) {
            throw new IllegalArgumentException("key size must be 16 or 24 bytes.");
        }
        this.q = z;
        byte[] bArr = new byte[8];
        System.arraycopy(key, 0, bArr, 0, 8);
        this.n = a(z, bArr);
        byte[] bArr2 = new byte[8];
        System.arraycopy(key, 8, bArr2, 0, 8);
        this.f8o = a(!z, bArr2);
        if (key.length == 24) {
            byte[] bArr3 = new byte[8];
            System.arraycopy(key, 16, bArr3, 0, 8);
            this.p = a(z, bArr3);
        } else {
            this.p = this.n;
        }
        t1.a(new w3(getAlgorithmName(), a(), cipherParameters, b.a(this.q)));
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i, byte[] bArr2, int i2) {
        int[] iArr = this.n;
        if (iArr == null) {
            throw new IllegalStateException("DESede engine not initialised");
        }
        if (i + 8 > bArr.length) {
            throw new DataLengthException("input buffer too short");
        }
        if (i2 + 8 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        byte[] bArr3 = new byte[8];
        if (this.q) {
            a(iArr, bArr, i, bArr3, 0);
            a(this.f8o, bArr3, 0, bArr3, 0);
            a(this.p, bArr3, 0, bArr2, i2);
        } else {
            a(this.p, bArr, i, bArr3, 0);
            a(this.f8o, bArr3, 0, bArr3, 0);
            a(this.n, bArr3, 0, bArr2, i2);
        }
        return 8;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void reset() {
    }
}
