package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.InvalidCipherTextException;
import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.util.Arrays;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\h6.smali */
public class h6 implements t0 {
    private SecureRandom a;
    private t0 b;
    private boolean c;
    private boolean d;
    private byte[] h;
    private int f = -1;
    private byte[] g = null;
    private boolean e = c();

    public h6(t0 t0Var) {
        this.b = t0Var;
    }

    private boolean c() {
        if (r6.a("bc.org.bouncycastle.pkcs1.not_strict", true)) {
            return false;
        }
        return !r6.a("bc.org.bouncycastle.pkcs1.strict", false);
    }

    private byte[] d(byte[] bArr, int i, int i2) throws InvalidCipherTextException {
        if (i2 > b()) {
            throw new IllegalArgumentException("input data too large");
        }
        int b = this.b.b();
        byte[] bArr2 = new byte[b];
        if (this.d) {
            bArr2[0] = 1;
            for (int i3 = 1; i3 != (b - i2) - 1; i3++) {
                bArr2[i3] = -1;
            }
        } else {
            this.a.nextBytes(bArr2);
            bArr2[0] = 2;
            for (int i4 = 1; i4 != (b - i2) - 1; i4++) {
                while (bArr2[i4] == 0) {
                    bArr2[i4] = (byte) this.a.nextInt();
                }
            }
        }
        int i5 = b - i2;
        bArr2[i5 - 1] = 0;
        System.arraycopy(bArr, i, bArr2, i5, i2);
        return this.b.a(bArr2, 0, b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public int a() {
        int a = this.b.a();
        return this.c ? a : a - 10;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public int b() {
        int b = this.b.b();
        return this.c ? b - 10 : b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public void init(boolean z, CipherParameters cipherParameters) {
        AsymmetricKeyParameter asymmetricKeyParameter;
        if (cipherParameters instanceof k6) {
            k6 k6Var = (k6) cipherParameters;
            this.a = k6Var.b();
            asymmetricKeyParameter = (AsymmetricKeyParameter) k6Var.a();
        } else {
            asymmetricKeyParameter = (AsymmetricKeyParameter) cipherParameters;
            if (!asymmetricKeyParameter.isPrivate() && z) {
                this.a = t1.b();
            }
        }
        this.b.init(z, cipherParameters);
        this.d = asymmetricKeyParameter.isPrivate();
        this.c = z;
        this.h = new byte[this.b.a()];
        if (this.f > 0 && this.g == null && this.a == null) {
            throw new IllegalArgumentException("encoder requires random");
        }
    }

    private static int b(byte[] bArr) {
        int i = -((bArr[0] & 255) ^ 2);
        int i2 = 0;
        int i3 = 0;
        for (int i4 = 1; i4 < bArr.length; i4++) {
            int i5 = (((bArr[i4] & 255) ^ 0) - 1) >> 31;
            i2 ^= ((~i3) & i4) & i5;
            i3 |= i5;
        }
        return ((bArr.length - 1) - i2) | (((i2 - 9) | i) >> 31);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public byte[] a(byte[] bArr, int i, int i2) throws InvalidCipherTextException {
        if (this.c) {
            return d(bArr, i, i2);
        }
        return b(bArr, i, i2);
    }

    private byte[] c(byte[] bArr, int i, int i2) throws InvalidCipherTextException {
        if (this.d) {
            int i3 = this.f;
            byte[] bArr2 = this.g;
            if (bArr2 == null) {
                bArr2 = new byte[i3];
                this.a.nextBytes(bArr2);
            }
            int a = this.b.a();
            byte[] a2 = this.b.a(bArr, i, i2);
            byte[] bArr3 = (a2.length == a || (!this.e && a2.length >= a)) ? a2 : this.h;
            int a3 = a(bArr3, i3) | 0;
            int length = bArr3.length - i3;
            byte[] bArr4 = new byte[i3];
            for (int i4 = 0; i4 < i3; i4++) {
                bArr4[i4] = (byte) ((bArr3[length + i4] & (~a3)) | (bArr2[i4] & a3));
            }
            Arrays.fill(a2, (byte) 0);
            byte[] bArr5 = this.h;
            Arrays.fill(bArr5, 0, Math.max(0, bArr5.length - a2.length), (byte) 0);
            return bArr4;
        }
        throw new InvalidCipherTextException("sorry, this method is only for decryption, not for signing");
    }

    private static int a(byte[] bArr) {
        int i = 0;
        int i2 = -((bArr[0] & 255) ^ 1);
        int i3 = 0;
        for (int i4 = 1; i4 < bArr.length; i4++) {
            int i5 = bArr[i4] & 255;
            int i6 = ((i5 ^ 0) - 1) >> 31;
            i ^= ((~i3) & i4) & i6;
            i3 |= i6;
            i2 |= ~((((i5 ^ 255) - 1) >> 31) | i3);
        }
        return ((bArr.length - 1) - i) | (((i - 9) | i2) >> 31);
    }

    private byte[] b(byte[] bArr, int i, int i2) throws InvalidCipherTextException {
        if (this.d && this.f != -1) {
            return c(bArr, i, i2);
        }
        int a = this.b.a();
        byte[] a2 = this.b.a(bArr, i, i2);
        boolean z = this.e & (a2.length != a);
        byte[] bArr2 = a2.length < a ? this.h : a2;
        int b = this.d ? b(bArr2) : a(bArr2);
        try {
            if (b < 0) {
                throw new InvalidCipherTextException("block incorrect");
            }
            if (!z) {
                byte[] bArr3 = new byte[b];
                System.arraycopy(bArr2, bArr2.length - b, bArr3, 0, b);
                return bArr3;
            }
            throw new InvalidCipherTextException("block incorrect size");
        } finally {
            Arrays.fill(a2, (byte) 0);
            byte[] bArr4 = this.h;
            Arrays.fill(bArr4, 0, Math.max(0, bArr4.length - a2.length), (byte) 0);
        }
    }

    private static int a(byte[] bArr, int i) {
        int i2 = -((bArr[0] & 255) ^ 2);
        int length = (bArr.length - 1) - i;
        int i3 = (length - 9) | i2;
        for (int i4 = 1; i4 < length; i4++) {
            i3 |= (bArr[i4] & 255) - 1;
        }
        return ((-(bArr[length] & 255)) | i3) >> 31;
    }
}
