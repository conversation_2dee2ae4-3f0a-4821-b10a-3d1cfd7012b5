package o.p;

import android.content.Context;
import kotlin.io.encoding.Base64;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\d.smali */
public final class d implements o.b.b {
    private static int b = 0;
    private static int e = 1;

    @Override // o.b.b
    public final o.b.d c(Context context, o.b.e eVar) {
        int i = e;
        int i2 = (i ^ 45) + ((i & 45) << 1);
        b = i2 % 128;
        switch (i2 % 2 != 0 ? '%' : 'L') {
            case Base64.mimeLineLength /* 76 */:
                return o.b.d.b;
            default:
                int i3 = 9 / 0;
                return o.b.d.b;
        }
    }

    @Override // o.b.b
    public final f b(Context context) {
        int i = e;
        int i2 = (i & 47) + (i | 47);
        b = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return null;
            default:
                int i3 = 30 / 0;
                return null;
        }
    }
}
