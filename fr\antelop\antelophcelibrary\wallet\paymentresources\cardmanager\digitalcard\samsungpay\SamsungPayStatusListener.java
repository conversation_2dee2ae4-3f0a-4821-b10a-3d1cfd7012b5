package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.view.ViewCompat;
import com.samsung.android.sdk.samsungpay.v2.StatusListener;
import fr.antelop.sdk.AntelopErrorCode;
import kotlin.text.Typography;
import o.ee.g;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayStatusListener.smali */
public class SamsungPayStatusListener implements StatusListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int c;
    private static int[] d;
    private final a.InterfaceC0042a<a.b> e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        c = 1;
        e();
        ViewConfiguration.getFadingEdgeLength();
        int i = c + 37;
        a = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        d = new int[]{-1582330842, -793248585, -3542397, -374937075, -2026285166, -1170899817, -1048724238, 1258667334, -66046592, -1363831582, 1309818498, 375769787, -622543763, 1348963008, -141827760, 1921610485, 688639877, -1611685582};
        b = -8279520901819557699L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002a). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = 116 - r8
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r7 = r7 * 3
            int r7 = 4 - r7
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayStatusListener.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2a
        L15:
            r3 = r2
        L16:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r5
        L2a:
            int r7 = r7 + r4
            int r8 = r8 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayStatusListener.h(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{19, -117, -13, -22};
        $$b = 5;
    }

    protected SamsungPayStatusListener(a.InterfaceC0042a<a.b> interfaceC0042a) {
        this.e = interfaceC0042a;
    }

    public void onSuccess(int i, Bundle bundle) {
        Object obj;
        Object[] objArr = new Object[1];
        f(new int[]{-768293786, 511991102, -1840381064, 394531274, 1993250962, 209827960, -1630694250, 1003731709, 1517029007, -1930672836, 1523025899, -322900956}, 23 - MotionEvent.axisFromString(""), objArr);
        String intern = ((String) objArr[0]).intern();
        g.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g("囇嚨\uf11fꏎ屋⤓ፙ픘㜬좖뇧뚖閤橍퀠ၝ爬එ盳\uf181킒꿮锊匥", 1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        f(new int[]{1881820112, -1694636229, -2050484779, 676975872, -401596350, -1103087027}, (Process.myTid() >> 22) + 11, objArr3);
        StringBuilder append2 = append.append(((String) objArr3[0]).intern()).append(bundle.toString());
        Object[] objArr4 = new Object[1];
        g("骒骻◿蜯ﴥ", KeyEvent.normalizeMetaState(0), objArr4);
        g.d(intern, append2.append(((String) objArr4[0]).intern()).toString());
        switch (i != 0) {
            case true:
                if (i == 1) {
                    g.c();
                    Object[] objArr5 = new Object[1];
                    f(new int[]{-1981902339, 339088608, 732994840, -1547628964, 95500973, -1175338027, -356744667, -307402180, 642468424, -186879867, -1630694250, 1003731709, 522343367, -1532118080, -1702443146, 1168505987, -1469354690, -95374418, 2056788165, 1925188033, 1431850976, -1120530692, 121648434, -111002121, 1100607387, -2037889073, -507423215, -1533855770, -815930171, -283670308, -1745882989, -1383651302}, 62 - TextUtils.getOffsetBefore("", 0), objArr5);
                    g.d(intern, ((String) objArr5[0]).intern());
                    this.e.e((a.InterfaceC0042a<a.b>) a.b.a);
                    return;
                }
                if (i == 3) {
                    g.c();
                    Object[] objArr6 = new Object[1];
                    f(new int[]{-1981902339, 339088608, 732994840, -1547628964, 95500973, -1175338027, -356744667, -307402180, 642468424, -186879867, -1630694250, 1003731709, 522343367, -1532118080, -1702443146, 1168505987, -1469354690, -95374418, 2056788165, 1925188033, 1431850976, -1120530692, 121648434, -111002121, 1100607387, -2037889073, -507423215, -1533855770, 1927603841, 1577845877, -939798432, -1236390603, -1640805534, -1429277809, 1467864547, -2003998029, 29062716, -1311864936}, 76 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr6);
                    g.d(intern, ((String) objArr6[0]).intern());
                    this.e.e((a.InterfaceC0042a<a.b>) a.b.a);
                    return;
                }
                switch (i != 2) {
                    case true:
                        g.c();
                        Object[] objArr7 = new Object[1];
                        f(new int[]{-1981902339, 339088608, 732994840, -1547628964, 95500973, -1175338027, -356744667, -307402180, 642468424, -186879867, -1630694250, 1003731709, 522343367, -1532118080, -1702443146, 1168505987, -1469354690, -95374418, 2056788165, 1925188033, 1431850976, -1120530692, 121648434, -111002121, -254708103, -400288899, 224696537, 1558797990}, 53 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr7);
                        g.d(intern, ((String) objArr7[0]).intern());
                        this.e.e((a.InterfaceC0042a<a.b>) a.b.b);
                        return;
                    default:
                        int i2 = a + 43;
                        c = i2 % 128;
                        if (i2 % 2 == 0) {
                        }
                        g.c();
                        Object[] objArr8 = new Object[1];
                        g("形弅圩俠ﯧ輮ｐ犅㺏溵巅ᄪ鰗찋㱕램箭ꮲ髐嘵\ud927য়祗\uf4d5뢫靗ퟹ譚ᙲ皖똴⧞\uf5b4푠ᓩ졚十돮\uf297滲㊊ᄮ兌ൺ遆\uf0fa쾀ꏤ濉帳깆䈹쵑㶻ಅ\ue038곕鬱\ueb09蚨\u0a56窢", KeyEvent.normalizeMetaState(0), objArr8);
                        g.d(intern, ((String) objArr8[0]).intern());
                        this.e.e((a.InterfaceC0042a<a.b>) a.b.c);
                        int i3 = c + 59;
                        a = i3 % 128;
                        switch (i3 % 2 != 0) {
                            case false:
                                return;
                            default:
                                int i4 = 1 / 0;
                                return;
                        }
                }
            default:
                int i5 = a + 79;
                c = i5 % 128;
                if (i5 % 2 == 0) {
                    g.c();
                    Object[] objArr9 = new Object[1];
                    g("蟳螔Თᠴ繃쒐ꢄ\uf721\ue61e┋\u0a11钎䒆螵殁㈌ꌼ\ue00c촄펑ƶ䉡⺃煱怺\udce9耭\u0efe컣㴨\ue1e0걺ⴥ鿞䌽䷾诐\uf850ꕃ\ueb56\uea1b媐ژ裞䣗뭄顔♀띘ᖍ漣잝ᗀ瘅孑斜瑄킓볗̙틜ㄖṕꂅㅛ鎂翚㸉鼶\uec31", View.MeasureSpec.getSize(0), objArr9);
                    obj = objArr9[0];
                } else {
                    g.c();
                    Object[] objArr10 = new Object[1];
                    g("蟳螔Თᠴ繃쒐ꢄ\uf721\ue61e┋\u0a11钎䒆螵殁㈌ꌼ\ue00c촄펑ƶ䉡⺃煱怺\udce9耭\u0efe컣㴨\ue1e0걺ⴥ鿞䌽䷾诐\uf850ꕃ\ueb56\uea1b媐ژ裞䣗뭄顔♀띘ᖍ漣잝ᗀ瘅孑斜瑄킓볗̙틜ㄖṕꂅㅛ鎂翚㸉鼶\uec31", View.MeasureSpec.getSize(0), objArr10);
                    obj = objArr10[0];
                }
                g.d(intern, ((String) obj).intern());
                this.e.e((a.InterfaceC0042a<a.b>) a.b.b);
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x013b. Please report as an issue. */
    public void onFail(int i, Bundle bundle) {
        g.c();
        Object[] objArr = new Object[1];
        f(new int[]{-768293786, 511991102, -1840381064, 394531274, 1993250962, 209827960, -1630694250, 1003731709, 1517029007, -1930672836, 1523025899, -322900956}, Color.red(0) + 24, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f(new int[]{-1237756717, 207527219, -1880854746, 1599472548, -1300070238, 953456704, 1526137161, -1501612006, 532407469, -1867700297}, 20 - TextUtils.getOffsetBefore("", 0), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        f(new int[]{-375512725, 1622985216, -772255032, 1607228578, -814955465, -98801679, -1970239906, -826477789, -1270893683, -1468694358}, (Process.myTid() >> 22) + 18, objArr3);
        StringBuilder append2 = append.append(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        g("\ude41\ude24ꍢ麵囦筲⸃\udfaf뾻髚貜방ᴢ㡿\ued0f", Process.getGidForName("") + 1, objArr4);
        StringBuilder append3 = append2.append(bundle.getInt(((String) objArr4[0]).intern(), -999));
        Object[] objArr5 = new Object[1];
        g("骒骻◿蜯ﴥ", 1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr5);
        g.d(intern, append3.append(((String) objArr5[0]).intern()).toString());
        Object[] objArr6 = new Object[1];
        f(new int[]{-772255032, 1607228578, -1621520737, -1727989528, -378938874, 673886685, 364795748, 1217268438, 2141746058, -893266368}, (ViewConfiguration.getFadingEdgeLength() >> 16) + 18, objArr6);
        if (bundle.containsKey(((String) objArr6[0]).intern())) {
            Object[] objArr7 = new Object[1];
            f(new int[]{-768293786, 511991102, -1840381064, 394531274, 1993250962, 209827960, -1630694250, 1003731709, 1517029007, -1930672836, 1523025899, -322900956}, View.resolveSizeAndState(0, 0, 0) + 24, objArr7);
            String intern2 = ((String) objArr7[0]).intern();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr8 = new Object[1];
            g("\ue9c5\ue9aaἠ麡针윬⸣᷏蠤⚦賍繃⪭萦\ued07\ud8df쵽\ue3a8䮘㥗澖䇍꠫鯮\u0e00\udf4fھ\ue435ꂔ㻕朰䛤䍝", ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), objArr8);
            StringBuilder append4 = sb2.append(((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            f(new int[]{-772255032, 1607228578, -1621520737, -1727989528, -378938874, 673886685, 364795748, 1217268438, 2141746058, -893266368}, 18 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr9);
            g.e(intern2, append4.append(bundle.getString(((String) objArr9[0]).intern())).toString());
            int i2 = a + 29;
            c = i2 % 128;
            switch (i2 % 2 == 0 ? Typography.greater : (char) 19) {
            }
        }
        this.e.e(new o.bv.c(AntelopErrorCode.SamsungPayWalletNotAvailable));
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 866
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayStatusListener.f(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v1 */
    /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
    private static void g(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 384
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayStatusListener.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
