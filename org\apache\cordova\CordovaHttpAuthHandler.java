package org.apache.cordova;

import android.webkit.HttpAuthHandler;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\CordovaHttpAuthHandler.smali */
public class CordovaHttpAuthHandler implements ICordovaHttpAuthHandler {
    private final HttpAuthHandler handler;

    public CordovaHttpAuthHandler(HttpAuthHandler handler) {
        this.handler = handler;
    }

    @Override // org.apache.cordova.ICordovaHttpAuthHandler
    public void cancel() {
        this.handler.cancel();
    }

    @Override // org.apache.cordova.ICordovaHttpAuthHandler
    public void proceed(String username, String password) {
        this.handler.proceed(username, password);
    }
}
