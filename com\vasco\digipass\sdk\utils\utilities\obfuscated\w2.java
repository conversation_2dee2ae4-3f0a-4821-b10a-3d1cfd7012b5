package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w2.smali */
public class w2 extends r2 {
    private static final BigInteger d = BigInteger.valueOf(1);
    private static final BigInteger e = BigInteger.valueOf(2);
    private BigInteger c;

    public w2(BigInteger bigInteger, t2 t2Var) {
        super(false, t2Var);
        this.c = a(bigInteger, t2Var);
    }

    private BigInteger a(BigInteger bigInteger, t2 t2Var) {
        if (bigInteger == null) {
            throw new NullPointerException("y value cannot be null");
        }
        BigInteger b = t2Var.b();
        BigInteger bigInteger2 = e;
        if (bigInteger.compareTo(bigInteger2) < 0 || bigInteger.compareTo(b.subtract(bigInteger2)) > 0) {
            throw new IllegalArgumentException("invalid DH public key");
        }
        BigInteger c = t2Var.c();
        if (c == null) {
            return bigInteger;
        }
        if (b.testBit(0) && b.bitLength() - 1 == c.bitLength() && b.shiftRight(1).equals(c)) {
            if (1 == a(bigInteger, b)) {
                return bigInteger;
            }
        } else if (d.equals(bigInteger.modPow(c, b))) {
            return bigInteger;
        }
        throw new IllegalArgumentException("Y value does not appear to be in correct group");
    }

    public BigInteger b() {
        return this.c;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r2
    public boolean equals(Object obj) {
        return (obj instanceof w2) && ((w2) obj).b().equals(this.c) && super.equals(obj);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r2
    public int hashCode() {
        return this.c.hashCode() ^ super.hashCode();
    }

    private static int a(BigInteger bigInteger, BigInteger bigInteger2) {
        int bitLength = bigInteger2.bitLength();
        int[] a = c6.a(bitLength, bigInteger);
        int[] a2 = c6.a(bitLength, bigInteger2);
        int length = a2.length;
        int i = 0;
        while (true) {
            int i2 = a[0];
            if (i2 == 0) {
                c6.e(length, a, 0);
            } else {
                int b = e5.b(i2);
                if (b > 0) {
                    c6.b(length, a, b, 0);
                    int i3 = a2[0];
                    i ^= (b << 1) & (i3 ^ (i3 >>> 1));
                }
                int b2 = c6.b(length, a, a2);
                if (b2 == 0) {
                    break;
                }
                if (b2 < 0) {
                    i ^= a[0] & a2[0];
                    int[] iArr = a2;
                    a2 = a;
                    a = iArr;
                }
                while (true) {
                    int i4 = length - 1;
                    if (a[i4] != 0) {
                        break;
                    }
                    length = i4;
                }
                c6.d(length, a, a2, a);
            }
        }
        if (c6.d(length, a2)) {
            return 1 - (i & 2);
        }
        return 0;
    }
}
