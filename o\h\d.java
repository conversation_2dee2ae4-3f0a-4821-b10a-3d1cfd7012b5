package o.h;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import o.a.o;
import o.ee.g;
import o.f.e;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\h\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static char b;
    private static int d;
    private static int f;
    private static int h;
    private final Map<f, e> c = new HashMap();
    private String e = null;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        e();
        View.MeasureSpec.getSize(0);
        KeyEvent.getModifierMetaStateMask();
        int i = h + 17;
        f = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        b = (char) 17957;
        d = 161105445;
        a = 3854652872909358258L;
    }

    private static void i(byte b2, byte b3, short s, Object[] objArr) {
        int i = 1 - (b2 * 4);
        int i2 = b3 + 99;
        byte[] bArr = $$a;
        int i3 = s + 4;
        byte[] bArr2 = new byte[i];
        int i4 = -1;
        int i5 = i - 1;
        if (bArr == null) {
            i2 += i3;
            i3 = i3;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
        }
        while (true) {
            int i6 = i4 + 1;
            bArr2[i6] = (byte) i2;
            if (i6 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i7 = i3 + 1;
            Object[] objArr2 = objArr;
            byte[] bArr3 = bArr2;
            byte[] bArr4 = bArr;
            i2 += bArr[i7];
            i3 = i7;
            objArr = objArr2;
            bArr = bArr4;
            bArr2 = bArr3;
            i4 = i6;
        }
    }

    static void init$0() {
        $$a = new byte[]{99, 114, -3, -82};
        $$b = 36;
    }

    final synchronized void c(e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        g(Color.green(0) - 1710687950, "펕샶榭\uecfd쮱⨞\ud97e짱䳔\uef48ሾ傃\uefc7ꛩ腚\uea77ⴏ쑶ᢷ禕貍", (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "㊖ࣹ\ue09a粥", "슗義\ue01b湠", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        g(TextUtils.lastIndexOf("", '0', 0) - 891045210, "\u089f\udfe5蔿೨櫧䇿\uef30斦䴠㡁⑰Ұ῾燋\ue4c6\uf4d5\ufff3", (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 36750), "ꕰ\ue3ba迊䮏", "슗義\ue01b湠", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar.b()).toString());
        this.c.put(eVar.b(), eVar);
        int i = f + Opcodes.LSHL;
        h = i % 128;
        int i2 = i % 2;
    }

    public final synchronized Collection<e> b() {
        Collection<e> values;
        int i = f + 53;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                values = this.c.values();
                int i2 = h + Opcodes.LNEG;
                f = i2 % 128;
                int i3 = i2 % 2;
                break;
            default:
                this.c.values();
                throw null;
        }
        return values;
    }

    public final synchronized void a() {
        int i = f + 45;
        h = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1710687951, "펕샶榭\uecfd쮱⨞\ud97e짱䳔\uef48ሾ傃\uefc7ꛩ腚\uea77ⴏ쑶ᢷ禕貍", (char) Color.green(0), "㊖ࣹ\ue09a粥", "슗義\ue01b湠", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(2128873746 - (ViewConfiguration.getWindowTouchSlop() >> 8), "媗纎⾢㣥䘲", (char) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 65259), "ሚ\ue409\ueb7eꣾ", "슗義\ue01b湠", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.c.clear();
        int i3 = f + Opcodes.DREM;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 5 : 'T') {
            case Opcodes.BASTORE /* 84 */:
                break;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String d() {
        int i = h + 75;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                return this.e;
            default:
                int i2 = 30 / 0;
                return this.e;
        }
    }

    public final void d(String str) {
        int i = f + Opcodes.LSUB;
        int i2 = i % 128;
        h = i2;
        boolean z = i % 2 == 0;
        Object obj = null;
        this.e = str;
        switch (z) {
            case false:
                throw null;
            default:
                int i3 = i2 + 79;
                f = i3 % 128;
                switch (i3 % 2 == 0 ? '*' : '\f') {
                    case '*':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0021. Please report as an issue. */
    private static void g(int i, String str, char c, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] charArray;
        char[] cArr2;
        int i2 = 2;
        int i3 = 0;
        if (str3 != null) {
            int i4 = $10 + 69;
            $11 = i4 % 128;
            int i5 = i4 % 2;
            cArr = str3.toCharArray();
            int i6 = $10 + 27;
            $11 = i6 % 128;
            switch (i6 % 2 == 0) {
            }
        } else {
            cArr = str3;
        }
        char[] cArr3 = cArr;
        switch (str2 != null ? (char) 31 : 'I') {
            case 31:
                charArray = str2.toCharArray();
                break;
            default:
                charArray = str2;
                break;
        }
        char[] cArr4 = charArray;
        switch (str != null) {
            case false:
                cArr2 = str;
                break;
            default:
                cArr2 = str.toCharArray();
                break;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = cArr2.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i7 = $10 + 39;
            $11 = i7 % 128;
            int i8 = i7 % i2;
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) (20954 - KeyEvent.keyCodeFromString("")), 344 - View.MeasureSpec.getMode(i3));
                    byte b2 = (byte) i3;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    i(b2, b3, (byte) (b3 - 1), objArr3);
                    String str4 = (String) objArr3[i3];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i3] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - View.resolveSizeAndState(i3, i3, i3), (char) ExpandableListView.getPackedPositionGroup(0L), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 206);
                        byte b4 = (byte) i3;
                        byte b5 = (byte) (b4 + 2);
                        Object[] objArr5 = new Object[1];
                        i(b4, b5, (byte) (b5 - 3), objArr5);
                        String str5 = (String) objArr5[i3];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i3] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i9 = cArr5[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr6[intValue]);
                        objArr6[1] = Integer.valueOf(i9);
                        objArr6[i3] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(TextUtils.indexOf("", "") + 11, (char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 280 - TextUtils.lastIndexOf("", '0', i3));
                            byte length4 = (byte) $$a.length;
                            Object[] objArr7 = new Object[1];
                            i((byte) i3, length4, (byte) (length4 - 5), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(19 - KeyEvent.keyCodeFromString(""), (char) (14687 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), (ViewConfiguration.getTouchSlop() >> 8) + Opcodes.IREM);
                                byte b6 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                i(b6, (byte) (b6 | 7), (byte) (-1), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r5[oVar.e]) ^ (a ^ 6565854932352255525L)) ^ ((int) (d ^ 6565854932352255525L))) ^ ((char) (b ^ 6565854932352255525L)));
                            oVar.e++;
                            i2 = 2;
                            i3 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str6 = new String(cArr7);
        int i10 = $11 + Opcodes.LSHR;
        $10 = i10 % 128;
        int i11 = i10 % 2;
        objArr[0] = str6;
    }
}
