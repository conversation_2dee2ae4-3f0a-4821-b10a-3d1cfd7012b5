package o.er;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.TokenRequestor;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import o.em.e;
import o.eo.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\s.smali */
public final class s extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        d();
        ViewConfiguration.getTapTimeout();
        Color.green(0);
        ViewConfiguration.getWindowTouchSlop();
        int i = b + Opcodes.LSUB;
        a = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        e = 874635298;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.er.s.$$a
            int r6 = r6 * 2
            int r6 = r6 + 107
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 4
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L37
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = r6 + r7
            int r7 = r8 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.s.g(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{26, 103, -21, 32};
        $$b = Opcodes.I2L;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = a + 65;
        b = i % 128;
        int i2 = i % 2;
        boolean b2 = super.b();
        int i3 = b + 35;
        a = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    public s(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        int i = a + 71;
        b = i % 128;
        int i2 = i % 2;
        a[] aVarArr = {this.d.j()};
        int i3 = a + 95;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return aVarArr;
        }
    }

    public final void b(Context context, final OperationCallback<List<TokenRequestor>> operationCallback) throws WalletValidationException {
        boolean z;
        int i = b + 91;
        a = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(TextUtils.indexOf((CharSequence) "", '0') + 23, "\b￭\b\u0004\ufffe\u0007￫\ufffe\n\u000e\ufffe\f\r\b\u000b￬\ufffe\u000b\u000f\u0002￼\ufffe￢\u0007\u0007\ufffe\u000b￩\u000e\f\u0001￭", 32 - (ViewConfiguration.getTapTimeout() >> 16), 196 - KeyEvent.normalizeMetaState(0), false, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        f(TextUtils.getCapsMode("", 0, 0) + 13, "\u0001\ufffb\u0004￨\ufffb\u0007\u000b\ufffb\t\n\u0005\b\t�\ufffb\n￪\u0005", 18 - TextUtils.getCapsMode("", 0, 0), 199 - Color.red(0), false, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (this.c.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            f((ViewConfiguration.getPressedStateDuration() >> 16) + 1, "￥\u0006\u0014\u0003", 4 - View.resolveSizeAndState(0, 0, 0), 187 - Color.green(0), true, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (!b()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            f(TextUtils.lastIndexOf("", '0', 0, 0) + 15, "￮\t\u0005\uffff\b￬\uffff\u000b\u000f\uffff\r\u000e\t\f￭\uffff�\u000f\f\uffff\uffdd\ufffb\f\ufffe￪\u000f\r\u0002￮\t", 30 - View.combineMeasuredStates(0, 0), 196 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), false, objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        final o.em.e j = o.ei.c.c().j();
        List<t> d = j.b().d(this.c.e());
        switch (d.isEmpty() ? '4' : '(') {
            case '(':
                Long e2 = j.b().e(this.c.e());
                if (e2 != null) {
                    switch (e2.longValue() < System.currentTimeMillis() - 604800000 ? '^' : '6') {
                        case Opcodes.DUP2_X2 /* 94 */:
                            int i3 = b + 85;
                            a = i3 % 128;
                            int i4 = i3 % 2;
                            o.ee.g.c();
                            Object[] objArr5 = new Object[1];
                            f(51 - KeyEvent.keyCodeFromString(""), "\u000f\r\u001c￼\u0017\u0013\r\u0016\ufffa\r\u0019\u001d\r\u001b\u001c\u0017\u001a\u001b\uffc8ￕ\uffc8\ufff4\t\u001b\u001c\uffc8\u001a\r\u001c\u001a\u0011\r\u001e\r\uffc8\u0018\t\u001b\u001c\uffc8\uffde\uffd8ￜ￠\uffd8\uffd8\uffd8\uffd8\uffd8\u0015\u001b", Drawable.resolveOpacity(0, 0) + 51, TextUtils.getOffsetBefore("", 0) + Opcodes.PUTFIELD, false, objArr5);
                            o.ee.g.d(intern, ((String) objArr5[0]).intern());
                            z = true;
                            break;
                        default:
                            z = false;
                            break;
                    }
                } else {
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    f((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 37, "\u0017\u001b\u000b\u0019\u001a\u0015\u0018\u0019ￆￓￆ￪\u0007\u001a\u000bￆ\u000f\u0019ￆ\u0014\u001b\u0012\u0012ￆￓ￤ￆ￪\u0015ￆ\u0018\u000b\f\u0018\u000b\u0019\u000e\r\u000b\u001a\ufffa\u0015\u0011\u000b\u0014\ufff8\u000b", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 47, ImageFormat.getBitsPerPixel(0) + Opcodes.INVOKESTATIC, false, objArr6);
                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                    int i5 = a + 89;
                    b = i5 % 128;
                    int i6 = i5 % 2;
                    z = true;
                    break;
                }
            default:
                o.ee.g.c();
                Object[] objArr7 = new Object[1];
                f((ViewConfiguration.getKeyRepeatDelay() >> 16) + 27, "\u000e\u0012\u0017ￃ\u0012\ufff1ￃ\uffd0ￃ\u0016\u0015\u0012\u0017\u0016\b\u0018\u0014\b\ufff5\u0011\b\u000e\u0012\ufff7\u0017\b\n\u000b\u0016\b\u0015\t\b\u0015ￃ\u0012\uffe7ￃ￡\uffd0ￃ\u0015\u0012\u0017\u0016\b\u0018\u0014\b\u0015ￃ\u0011\b", Color.blue(0) + 53, 185 - TextUtils.lastIndexOf("", '0', 0), true, objArr7);
                o.ee.g.d(intern, ((String) objArr7[0]).intern());
                z = true;
                break;
        }
        switch (!z ? (char) 11 : 'L') {
            case Base64.mimeLineLength /* 76 */:
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                f(18 - View.getDefaultSize(0, 0), "\u0013\u0014\u000f\u0012\u0013\uffc0ￍ\uffc0￤\u000f\uffc0\u0012\u0005\u0006\u0012\u0005\u0013\b\u0007\u0005\u0014\ufff4\u000f\u000b\u0005\u000e\ufff2\u0005\u0011\u0015\u0005", 31 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 189 - View.MeasureSpec.makeMeasureSpec(0, 0), false, objArr8);
                o.ee.g.d(intern, ((String) objArr8[0]).intern());
                j.d(context, this.c.e(), new e.c<t>() { // from class: o.er.s.1
                    private static int c = 0;
                    private static int e = 1;

                    @Override // o.em.e.c
                    public final void a(List<t> list) {
                        int i7 = c;
                        int i8 = (i7 ^ Opcodes.LSHR) + ((i7 & Opcodes.LSHR) << 1);
                        e = i8 % 128;
                        int i9 = i8 % 2;
                        s.this.e(list, operationCallback, j.b());
                        int i10 = c;
                        int i11 = (i10 ^ 97) + ((i10 & 97) << 1);
                        e = i11 % 128;
                        switch (i11 % 2 == 0) {
                            case false:
                                return;
                            default:
                                throw null;
                        }
                    }

                    @Override // o.em.e.c
                    public final void d(AntelopError antelopError) {
                        int i7 = c;
                        int i8 = (i7 & 61) + (i7 | 61);
                        e = i8 % 128;
                        int i9 = i8 % 2;
                        operationCallback.onError(antelopError);
                        int i10 = e;
                        int i11 = (i10 & 13) + (i10 | 13);
                        c = i11 % 128;
                        int i12 = i11 % 2;
                    }
                });
                int i7 = b + 63;
                a = i7 % 128;
                if (i7 % 2 == 0) {
                    int i8 = 18 / 0;
                    return;
                }
                return;
            default:
                int i9 = b + 77;
                a = i9 % 128;
                if (i9 % 2 == 0) {
                }
                o.ee.g.c();
                Object[] objArr9 = new Object[1];
                f((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 60, "\u0005\u0011\u0015\u0005\u0013\u0014\u000f\u0012\u0013\uffc0ￍ\uffc0￮\u000f\uffc0\u0012\u0005\u0006\u0012\u0005\u0013\b\uffc0\u0014\u000f\uffc0\u0004\u000fￌ\uffc0\u0012\u0005\u0014\u0015\u0012\u000e\uffc0\u0003\u0015\u0012\u0012\u0005\u000e\u0014\uffc0\u0014\u000f\u000b\u0005\u000e\uffc0\u0012\u0005\u0011\u0015\u0005\u0013\u0014\u000f\u0012\u0013\u0007\u0005\u0014\ufff4\u000f\u000b\u0005\u000e\ufff2", Drawable.resolveOpacity(0, 0) + 70, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 188, false, objArr9);
                o.ee.g.d(intern, ((String) objArr9[0]).intern());
                e(d, operationCallback, j.b());
                return;
        }
    }

    final void e(List<t> list, OperationCallback<List<TokenRequestor>> operationCallback, o.em.a aVar) {
        o.eo.f next;
        List<o.eo.f> b2 = aVar.b(this.c.e());
        ArrayList arrayList = new ArrayList();
        Iterator<o.eo.f> it = b2.iterator();
        int i = a + 9;
        b = i % 128;
        switch (i % 2 == 0) {
        }
        while (it.hasNext()) {
            int i2 = b + Opcodes.LSHR;
            a = i2 % 128;
            if (i2 % 2 == 0) {
                next = it.next();
                int i3 = 32 / 0;
                switch (next.n() != f.d.c) {
                    case false:
                        break;
                    default:
                        arrayList.add(next.e());
                        break;
                }
            } else {
                next = it.next();
                switch (next.n() != f.d.c) {
                    case false:
                        break;
                    default:
                        arrayList.add(next.e());
                        break;
                }
            }
        }
        ArrayList arrayList2 = new ArrayList();
        for (t tVar : list) {
            if (!arrayList.contains(tVar.e())) {
                arrayList2.add(new TokenRequestor(this.c, tVar));
            }
        }
        operationCallback.onSuccess(arrayList2);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r18, java.lang.String r19, int r20, int r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 540
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.s.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
