package o.fn;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.settings.WalletSettingsRights;
import fr.antelop.sdk.settings.WalletSettingsValue;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import o.a.l;
import o.ee.g;
import o.ee.o;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\d.smali */
public final class d extends c<d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int h;
    private static char[] i;
    private static int j;
    private final int a;
    private final int b;
    private final int c;
    private final BigDecimal d;
    private final int e;
    private final int f;
    private final int g;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        f();
        int i2 = j + Opcodes.LSUB;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 4 : 'F') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                break;
            default:
                int i3 = 46 / 0;
                break;
        }
    }

    static void f() {
        i = new char[]{50923, 50839, 50854, 50854, 50851, 50878, 50820, 50829, 50852, 50854, 50831, 50826, 50851, 50876, 50823, 50825, 50854, 50854, 50851, 50851, 50856, 50859, 50855, 50876, 50850, 50857, 50819, 50778, 50772, 50752, 50757, 50772, 50772, 50769, 50796, 50753, 50756, 50937, 50849, 50851, 50860, 50835, 50854, 50852, 50855, 50879, 50873, 50823, 50923, 50923, 50827, 50855, 50854, 50854, 50851, 50878, 50820, 50826, 50851, 50876, 50823, 50825, 50854, 50854, 50851, 50851, 50856, 50859, 50855, 50876, 50850, 50857, 50821, 50923, 50820, 50854, 50856, 50856, 50854, 50879, 50848, 50854, 50855, 50830, 50823, 50876, 50826, 50827, 50858, 50858, 50854, 50858, 50856, 50849, 50878, 50823, 50831, 50857, 50856, 50858, 50854, 50852, 50857, 50831, 50822, 50854, 50859, 50877, 50848, 50859, 50859, 50849, 50838, 50836, 50851, 50851, 50852, 50857, 50854, 50850, 50728, 50692, 50701, 50733, 50727, 50728, 50732, 50731, 50725, 50693, 50697, 50729, 50729, 50730, 50730, 50707, 50706, 50717, 50719, 50730, 50730, 50735, 50704, 50729, 50730, 50728, 50730, 50711, 50714, 50729, 50735, 50734, 50726, 50720, 50935, 50823, 50822, 50854, 50859, 50877, 50848, 50859, 50859, 50849, 50838, 50836, 50851, 50851, 50852, 50857, 50854, 50851, 50849, 50851, 50860, 50835, 50854, 50852, 50855, 50879, 50873, 50823, 50923, 50923, 50827, 50858, 50858, 50854, 50858, 50856, 50849, 50878, 50823, 50822, 50854, 50859, 50877, 50848, 50831, 50826, 50851, 50876, 50823, 50830, 50855, 50879, 50876, 50852, 50831, 50923, 50923, 50823, 50873, 50851, 50854, 50855, 50830, 50831, 50857, 50856, 50862, 50854, 50877};
    }

    static void init$0() {
        $$a = new byte[]{88, 54, 68, 27};
        $$b = Opcodes.TABLESWITCH;
    }

    private static void l(byte b, int i2, short s, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = 122 - i2;
        int i4 = 3 - (s * 2);
        int i5 = 1 - (b * 4);
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            int i8 = i7 + i4;
            i4 = i4;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i3 = i8;
            i7 = i7;
        }
        while (true) {
            int i9 = i6 + 1;
            int i10 = i4 + 1;
            bArr2[i9] = (byte) i3;
            if (i9 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b2 = bArr[i10];
            i4 = i10;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i9;
            i3 = b2 + i3;
            i7 = i7;
        }
    }

    public d(BigDecimal bigDecimal, int i2, int i3, int i4, int i5, int i6, int i7) {
        super(true);
        this.d = bigDecimal;
        this.b = i2 == 0 ? 1 : i2;
        this.c = i3;
        this.e = i4;
        this.a = i5;
        this.f = i6;
        this.g = i7;
        o.i.d.c();
    }

    public d() {
        super(false);
        this.d = null;
        this.b = 0;
        this.c = 0;
        this.e = 0;
        this.a = 0;
        this.f = 0;
        this.g = 0;
    }

    public final WalletSettingsValue<BigDecimal> c() {
        BigDecimal bigDecimal;
        int i2 = h + Opcodes.DNEG;
        j = i2 % 128;
        int i3 = i2 % 2;
        try {
            bigDecimal = a();
        } catch (o.ei.j e) {
            bigDecimal = null;
        }
        WalletSettingsValue<BigDecimal> walletSettingsValue = new WalletSettingsValue<>(bigDecimal, WalletSettingsRights.ReadOnly);
        int i4 = h + 99;
        j = i4 % 128;
        int i5 = i4 % 2;
        return walletSettingsValue;
    }

    public final BigDecimal a() throws o.ei.j {
        Object obj;
        int i2 = j + 45;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '1' : 'C') {
            case '1':
                Object[] objArr = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        BigDecimal bigDecimal = this.d;
        int i3 = j + 57;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? '.' : '\'') {
            case '.':
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return bigDecimal;
        }
    }

    private int i() throws o.ei.j {
        int i2 = j + 17;
        h = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr);
        d(((String) objArr[0]).intern());
        int i4 = this.b;
        int i5 = h + 77;
        j = i5 % 128;
        switch (i5 % 2 == 0 ? '!' : 'O') {
            case '!':
                throw null;
            default:
                return i4;
        }
    }

    public final int a(Context context) {
        int i2;
        int i3 = j + 51;
        h = i3 % 128;
        try {
            switch (i3 % 2 != 0 ? '[' : 'O') {
                case Opcodes.IASTORE /* 79 */:
                    i2 = i();
                    break;
                default:
                    i();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (o.ei.j e) {
            g.c();
            Object[] objArr = new Object[1];
            k("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001", new int[]{26, 11, 46, 9}, false, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k("\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001", new int[]{37, 82, 0, 72}, false, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            try {
                Object[] objArr3 = new Object[1];
                k("\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001", new int[]{Opcodes.DNEG, 35, Opcodes.LSHL, 0}, false, objArr3);
                i2 = Integer.parseInt(o.a(context, ((String) objArr3[0]).intern()));
            } catch (PackageManager.NameNotFoundException e2) {
                g.c();
                Object[] objArr4 = new Object[1];
                k("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001", new int[]{26, 11, 46, 9}, false, objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                k("\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{Opcodes.IFNE, 69, 0, 7}, false, objArr5);
                g.d(intern2, ((String) objArr5[0]).intern());
                i2 = 60;
            }
        }
        int i4 = h + 65;
        j = i4 % 128;
        int i5 = i4 % 2;
        return i2;
    }

    public final int e() throws o.ei.j {
        Object obj;
        int i2 = j + 15;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? ' ' : (char) 30) {
            case ' ':
                Object[] objArr = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        int i3 = this.c;
        int i4 = j + Opcodes.LMUL;
        h = i4 % 128;
        int i5 = i4 % 2;
        return i3;
    }

    public final int b() throws o.ei.j {
        Object obj;
        int i2 = j + 83;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 0 : 'F') {
            case 0:
                Object[] objArr = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        return this.a;
    }

    public final int j() throws o.ei.j {
        int i2 = h + 57;
        j = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 26, 0, 0}, false, objArr);
        d(((String) objArr[0]).intern());
        int i4 = this.f;
        int i5 = h + 1;
        j = i5 % 128;
        int i6 = i5 % 2;
        return i4;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:81:0x0039. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:13:0x005b  */
    /* JADX WARN: Removed duplicated region for block: B:21:0x008f  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0094  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x00a6  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x00ac  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x00c1  */
    /* JADX WARN: Removed duplicated region for block: B:37:0x00ca  */
    /* JADX WARN: Removed duplicated region for block: B:43:0x00e6  */
    /* JADX WARN: Removed duplicated region for block: B:45:0x00ec  */
    /* JADX WARN: Removed duplicated region for block: B:50:0x00fd A[FALL_THROUGH, RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:51:0x00e8  */
    /* JADX WARN: Removed duplicated region for block: B:53:0x00e3 A[FALL_THROUGH] */
    /* JADX WARN: Removed duplicated region for block: B:54:0x00c4  */
    /* JADX WARN: Removed duplicated region for block: B:55:0x00be A[FALL_THROUGH] */
    /* JADX WARN: Removed duplicated region for block: B:56:0x00a8  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(o.fn.d r6) {
        /*
            Method dump skipped, instructions count: 314
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.d.a(o.fn.d):boolean");
    }

    private static void k(String str, int[] iArr, boolean z, Object[] objArr) {
        int i2;
        char[] cArr;
        String str2 = str;
        int i3 = $11 + 33;
        $10 = i3 % 128;
        int i4 = 0;
        Object obj = null;
        byte[] bArr = str2;
        switch (i3 % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                if (str2 != null) {
                    bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                }
                byte[] bArr2 = bArr;
                l lVar = new l();
                int i5 = iArr[0];
                int i6 = iArr[1];
                int i7 = iArr[2];
                int i8 = iArr[3];
                char[] cArr2 = i;
                float f = 0.0f;
                if (cArr2 != null) {
                    int length = cArr2.length;
                    char[] cArr3 = new char[length];
                    int i9 = 0;
                    while (i9 < length) {
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i4] = Integer.valueOf(cArr2[i9]);
                            Object obj2 = o.e.a.s.get(1951085128);
                            if (obj2 != null) {
                                cArr = cArr2;
                            } else {
                                Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) ((AudioTrack.getMaxVolume() > f ? 1 : (AudioTrack.getMaxVolume() == f ? 0 : -1)) - 1), 43 - View.MeasureSpec.makeMeasureSpec(i4, i4));
                                byte b = (byte) i4;
                                byte b2 = (byte) (b + 2);
                                cArr = cArr2;
                                Object[] objArr3 = new Object[1];
                                l(b, b2, (byte) (b2 - 2), objArr3);
                                obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj2);
                            }
                            cArr3[i9] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                            i9++;
                            cArr2 = cArr;
                            i4 = 0;
                            f = 0.0f;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr2 = cArr3;
                }
                char[] cArr4 = new char[i6];
                System.arraycopy(cArr2, i5, cArr4, 0, i6);
                switch (bArr2 != null ? '\f' : (char) 5) {
                    case '\f':
                        char[] cArr5 = new char[i6];
                        lVar.d = 0;
                        int i10 = $11 + 29;
                        $10 = i10 % 128;
                        int i11 = i10 % 2;
                        char c = 0;
                        while (true) {
                            switch (lVar.d < i6 ? 'a' : '\'') {
                                case Opcodes.LADD /* 97 */:
                                    switch (bArr2[lVar.d] == 1 ? (char) 17 : '%') {
                                        case '%':
                                            int i12 = lVar.d;
                                            try {
                                                Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                                Object obj3 = o.e.a.s.get(804049217);
                                                if (obj3 == null) {
                                                    Class cls2 = (Class) o.e.a.c(Color.blue(0) + 10, (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 207 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)));
                                                    byte b3 = (byte) 0;
                                                    byte b4 = b3;
                                                    Object[] objArr5 = new Object[1];
                                                    l(b3, b4, b4, objArr5);
                                                    obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                                    o.e.a.s.put(804049217, obj3);
                                                }
                                                cArr5[i12] = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
                                                break;
                                            } catch (Throwable th2) {
                                                Throwable cause2 = th2.getCause();
                                                if (cause2 == null) {
                                                    throw th2;
                                                }
                                                throw cause2;
                                            }
                                        default:
                                            int i13 = lVar.d;
                                            try {
                                                Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                                Object obj4 = o.e.a.s.get(2016040108);
                                                if (obj4 == null) {
                                                    Class cls3 = (Class) o.e.a.c((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 10, (char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), 448 - TextUtils.indexOf("", "", 0, 0));
                                                    byte b5 = (byte) 0;
                                                    byte b6 = (byte) (b5 + 3);
                                                    Object[] objArr7 = new Object[1];
                                                    l(b5, b6, (byte) (b6 - 3), objArr7);
                                                    obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                                    o.e.a.s.put(2016040108, obj4);
                                                }
                                                cArr5[i13] = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                                                break;
                                            } catch (Throwable th3) {
                                                Throwable cause3 = th3.getCause();
                                                if (cause3 == null) {
                                                    throw th3;
                                                }
                                                throw cause3;
                                            }
                                    }
                                    c = cArr5[lVar.d];
                                    try {
                                        Object[] objArr8 = {lVar, lVar};
                                        Object obj5 = o.e.a.s.get(-2112603350);
                                        if (obj5 == null) {
                                            Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getLongPressTimeout() >> 16), (char) TextUtils.indexOf("", "", 0), 259 - (ViewConfiguration.getTapTimeout() >> 16));
                                            byte b7 = (byte) 0;
                                            Object[] objArr9 = new Object[1];
                                            l(b7, (byte) (b7 | 56), b7, objArr9);
                                            obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                            o.e.a.s.put(-2112603350, obj5);
                                        }
                                        ((Method) obj5).invoke(null, objArr8);
                                    } catch (Throwable th4) {
                                        Throwable cause4 = th4.getCause();
                                        if (cause4 == null) {
                                            throw th4;
                                        }
                                        throw cause4;
                                    }
                                default:
                                    cArr4 = cArr5;
                                    break;
                            }
                        }
                }
                if (i8 > 0) {
                    int i14 = $10 + 27;
                    $11 = i14 % 128;
                    if (i14 % 2 == 0) {
                        char[] cArr6 = new char[i6];
                        System.arraycopy(cArr4, 0, cArr6, 0, i6);
                        System.arraycopy(cArr6, 1, cArr4, i6 * i8, i8);
                        System.arraycopy(cArr6, i8, cArr4, 1, i6 >> i8);
                        i2 = 0;
                    } else {
                        char[] cArr7 = new char[i6];
                        i2 = 0;
                        System.arraycopy(cArr4, 0, cArr7, 0, i6);
                        int i15 = i6 - i8;
                        System.arraycopy(cArr7, 0, cArr4, i15, i8);
                        System.arraycopy(cArr7, i8, cArr4, 0, i15);
                    }
                } else {
                    i2 = 0;
                }
                if (z) {
                    char[] cArr8 = new char[i6];
                    while (true) {
                        lVar.d = i2;
                        if (lVar.d < i6) {
                            int i16 = $10 + 91;
                            $11 = i16 % 128;
                            int i17 = i16 % 2;
                            cArr8[lVar.d] = cArr4[(i6 - lVar.d) - 1];
                            i2 = lVar.d + 1;
                        } else {
                            cArr4 = cArr8;
                        }
                    }
                }
                if (i7 > 0) {
                    int i18 = 0;
                    while (true) {
                        lVar.d = i18;
                        if (lVar.d < i6) {
                            cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                            i18 = lVar.d + 1;
                        }
                    }
                }
                objArr[0] = new String(cArr4);
                return;
        }
    }
}
