package bc.org.bouncycastle.math.ec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\FixedPointPreCompInfo.smali */
public class FixedPointPreCompInfo implements PreCompInfo {
    protected ECPoint a = null;
    protected ECLookupTable b = null;
    protected int c = -1;

    public ECLookupTable getLookupTable() {
        return this.b;
    }

    public ECPoint getOffset() {
        return this.a;
    }

    public int getWidth() {
        return this.c;
    }

    public void setLookupTable(ECLookupTable eCLookupTable) {
        this.b = eCLookupTable;
    }

    public void setOffset(ECPoint eCPoint) {
        this.a = eCPoint;
    }

    public void setWidth(int i) {
        this.c = i;
    }
}
