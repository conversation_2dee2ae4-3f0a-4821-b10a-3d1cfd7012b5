package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.ClassResolver;
import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.minlog.Log;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\DefaultClassResolver.smali */
public class DefaultClassResolver implements ClassResolver {
    public static final byte NAME = -1;
    protected IdentityObjectIntMap<Class> classToNameId;
    protected Kryo kryo;
    private Class memoizedClass;
    private Registration memoizedClassIdValue;
    private Registration memoizedClassValue;
    protected IntMap<Class> nameIdToClass;
    protected ObjectMap<String, Class> nameToClass;
    protected int nextNameId;
    protected final IntMap<Registration> idToRegistration = new IntMap<>();
    protected final IdentityMap<Class, Registration> classToRegistration = new IdentityMap<>();
    private int memoizedClassId = -1;

    @Override // com.esotericsoftware.kryo.ClassResolver
    public void setKryo(Kryo kryo) {
        this.kryo = kryo;
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration register(Registration registration) {
        this.memoizedClassId = -1;
        this.memoizedClass = null;
        if (registration != null) {
            if (registration.getId() != -1) {
                if (Log.TRACE) {
                    Log.trace("kryo", "Register class ID " + registration.getId() + ": " + Util.className(registration.getType()) + " (" + registration.getSerializer().getClass().getName() + ")");
                }
                this.idToRegistration.put(registration.getId(), registration);
            } else if (Log.TRACE) {
                Log.trace("kryo", "Register class name: " + Util.className(registration.getType()) + " (" + registration.getSerializer().getClass().getName() + ")");
            }
            this.classToRegistration.put(registration.getType(), registration);
            Class wrapperClass = Util.getWrapperClass(registration.getType());
            if (wrapperClass != registration.getType()) {
                this.classToRegistration.put(wrapperClass, registration);
            }
            return registration;
        }
        throw new IllegalArgumentException("registration cannot be null.");
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration unregister(int classID) {
        Registration registration = this.idToRegistration.remove(classID);
        if (registration != null) {
            this.classToRegistration.remove(registration.getType());
            this.memoizedClassId = -1;
            this.memoizedClass = null;
            Class wrapperClass = Util.getWrapperClass(registration.getType());
            if (wrapperClass != registration.getType()) {
                this.classToRegistration.remove(wrapperClass);
            }
        }
        return registration;
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration registerImplicit(Class type) {
        return register(new Registration(type, this.kryo.getDefaultSerializer(type), -1));
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration getRegistration(Class type) {
        if (type == this.memoizedClass) {
            return this.memoizedClassValue;
        }
        Registration registration = this.classToRegistration.get(type);
        if (registration != null) {
            this.memoizedClass = type;
            this.memoizedClassValue = registration;
        }
        return registration;
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration getRegistration(int classID) {
        return this.idToRegistration.get(classID);
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration writeClass(Output output, Class type) {
        if (type == null) {
            if (Log.TRACE || (Log.DEBUG && this.kryo.getDepth() == 1)) {
                Util.log("Write", null, output.position());
            }
            output.writeByte((byte) 0);
            return null;
        }
        Registration registration = this.kryo.getRegistration(type);
        if (registration.getId() == -1) {
            writeName(output, type, registration);
        } else {
            if (Log.TRACE) {
                Log.trace("kryo", "Write class " + registration.getId() + ": " + Util.className(type) + Util.pos(output.position()));
            }
            output.writeVarInt(registration.getId() + 2, true);
        }
        return registration;
    }

    protected void writeName(Output output, Class type, Registration registration) {
        int nameId;
        output.writeByte(1);
        IdentityObjectIntMap<Class> identityObjectIntMap = this.classToNameId;
        if (identityObjectIntMap != null && (nameId = identityObjectIntMap.get(type, -1)) != -1) {
            if (Log.TRACE) {
                Log.trace("kryo", "Write class name reference " + nameId + ": " + Util.className(type) + Util.pos(output.position()));
            }
            output.writeVarInt(nameId, true);
            return;
        }
        if (Log.TRACE) {
            Log.trace("kryo", "Write class name: " + Util.className(type) + Util.pos(output.position()));
        }
        int nameId2 = this.nextNameId;
        this.nextNameId = nameId2 + 1;
        if (this.classToNameId == null) {
            this.classToNameId = new IdentityObjectIntMap<>();
        }
        this.classToNameId.put(type, nameId2);
        output.writeVarInt(nameId2, true);
        if (registration.isTypeNameAscii()) {
            output.writeAscii(type.getName());
        } else {
            output.writeString(type.getName());
        }
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public Registration readClass(Input input) {
        int classID = input.readVarInt(true);
        switch (classID) {
            case 0:
                if (Log.TRACE || (Log.DEBUG && this.kryo.getDepth() == 1)) {
                    Util.log("Read", null, input.position());
                }
                return null;
            case 1:
                return readName(input);
            default:
                if (classID == this.memoizedClassId) {
                    if (Log.TRACE) {
                        Log.trace("kryo", "Read class " + (classID - 2) + ": " + Util.className(this.memoizedClassIdValue.getType()) + Util.pos(input.position()));
                    }
                    return this.memoizedClassIdValue;
                }
                Registration registration = this.idToRegistration.get(classID - 2);
                if (registration == null) {
                    throw new KryoException("Encountered unregistered class ID: " + (classID - 2));
                }
                if (Log.TRACE) {
                    Log.trace("kryo", "Read class " + (classID - 2) + ": " + Util.className(registration.getType()) + Util.pos(input.position()));
                }
                this.memoizedClassId = classID;
                this.memoizedClassIdValue = registration;
                return registration;
        }
    }

    protected Registration readName(Input input) {
        int nameId = input.readVarInt(true);
        if (this.nameIdToClass == null) {
            this.nameIdToClass = new IntMap<>();
        }
        Class type = this.nameIdToClass.get(nameId);
        if (type == null) {
            String className = input.readString();
            type = getTypeByName(className);
            if (type == null) {
                try {
                    type = Class.forName(className, false, this.kryo.getClassLoader());
                } catch (ClassNotFoundException ex) {
                    try {
                        type = Class.forName(className, false, Kryo.class.getClassLoader());
                    } catch (ClassNotFoundException e) {
                        throw new KryoException("Unable to find class: " + className, ex);
                    }
                }
                if (this.nameToClass == null) {
                    this.nameToClass = new ObjectMap<>();
                }
                this.nameToClass.put(className, type);
            }
            this.nameIdToClass.put(nameId, type);
            if (Log.TRACE) {
                Log.trace("kryo", "Read class name: " + className + Util.pos(input.position()));
            }
        } else if (Log.TRACE) {
            Log.trace("kryo", "Read class name reference " + nameId + ": " + Util.className(type) + Util.pos(input.position()));
        }
        return this.kryo.getRegistration(type);
    }

    protected Class getTypeByName(String className) {
        ObjectMap<String, Class> objectMap = this.nameToClass;
        if (objectMap != null) {
            return objectMap.get(className);
        }
        return null;
    }

    @Override // com.esotericsoftware.kryo.ClassResolver
    public void reset() {
        if (!this.kryo.isRegistrationRequired()) {
            IdentityObjectIntMap<Class> identityObjectIntMap = this.classToNameId;
            if (identityObjectIntMap != null) {
                identityObjectIntMap.clear(2048);
            }
            IntMap<Class> intMap = this.nameIdToClass;
            if (intMap != null) {
                intMap.clear();
            }
            this.nextNameId = 0;
        }
    }
}
