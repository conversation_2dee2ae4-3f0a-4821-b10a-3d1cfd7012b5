package o.di;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\di\b.smali */
public class b {
    private static int b;
    private static int e = 1;
    private static final byte[] a = {-112, 0};

    static {
        b = 0;
        int i = (1 & Opcodes.DMUL) + (1 | Opcodes.DMUL);
        b = i % 128;
        int i2 = i % 2;
    }

    public static byte[] e() {
        int i = b;
        int i2 = (i & 69) + (i | 69);
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                byte[] bArr = (byte[]) a.clone();
                int i3 = b;
                int i4 = (i3 ^ 3) + ((i3 & 3) << 1);
                e = i4 % 128;
                int i5 = i4 % 2;
                return bArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final int hashCode() {
        int i = e;
        int i2 = ((i | 73) << 1) - (i ^ 73);
        b = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = b;
        int i5 = (i4 & 71) + (i4 | 71);
        e = i5 % 128;
        int i6 = i5 % 2;
        return hashCode;
    }

    public final boolean equals(Object obj) {
        int i = b;
        int i2 = (i & 53) + (i | 53);
        e = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = e + 61;
        b = i4 % 128;
        int i5 = i4 % 2;
        return equals;
    }

    public final String toString() {
        int i = e;
        int i2 = (i ^ 39) + ((i & 39) << 1);
        b = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                super.toString();
                throw null;
            default:
                String obj2 = super.toString();
                int i3 = b + 79;
                e = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return obj2;
                }
        }
    }

    protected final void finalize() throws Throwable {
        int i = e;
        int i2 = (i ^ 71) + ((i & 71) << 1);
        b = i2 % 128;
        boolean z = i2 % 2 == 0;
        super.finalize();
        switch (z) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
