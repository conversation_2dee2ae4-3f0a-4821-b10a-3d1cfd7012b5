package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j6.smali */
public abstract class j6 {
    public static int a(byte[] bArr, int i) {
        int i2 = bArr[i] << 24;
        int i3 = i + 1;
        int i4 = i2 | ((bArr[i3] & 255) << 16);
        int i5 = i3 + 1;
        return (bArr[i5 + 1] & 255) | i4 | ((bArr[i5] & 255) << 8);
    }

    public static long b(byte[] bArr, int i) {
        return (a(bArr, i + 4) & 4294967295L) | ((a(bArr, i) & 4294967295L) << 32);
    }

    public static int c(byte[] bArr, int i) {
        int i2 = bArr[i] & 255;
        int i3 = i + 1;
        int i4 = i2 | ((bArr[i3] & 255) << 8);
        int i5 = i3 + 1;
        return (bArr[i5 + 1] << 24) | i4 | ((bArr[i5] & 255) << 16);
    }

    public static long d(byte[] bArr, int i) {
        return ((c(bArr, i + 4) & 4294967295L) << 32) | (c(bArr, i) & 4294967295L);
    }

    public static void b(int i, byte[] bArr, int i2) {
        bArr[i2] = (byte) i;
        int i3 = i2 + 1;
        bArr[i3] = (byte) (i >>> 8);
        int i4 = i3 + 1;
        bArr[i4] = (byte) (i >>> 16);
        bArr[i4 + 1] = (byte) (i >>> 24);
    }

    public static void a(int i, byte[] bArr, int i2) {
        bArr[i2] = (byte) (i >>> 24);
        int i3 = i2 + 1;
        bArr[i3] = (byte) (i >>> 16);
        int i4 = i3 + 1;
        bArr[i4] = (byte) (i >>> 8);
        bArr[i4 + 1] = (byte) i;
    }

    public static void b(long j, byte[] bArr, int i) {
        b((int) (4294967295L & j), bArr, i);
        b((int) (j >>> 32), bArr, i + 4);
    }

    public static void a(int[] iArr, byte[] bArr, int i) {
        for (int i2 : iArr) {
            a(i2, bArr, i);
            i += 4;
        }
    }

    public static void a(long j, byte[] bArr, int i) {
        a((int) (j >>> 32), bArr, i);
        a((int) (j & 4294967295L), bArr, i + 4);
    }

    public static void a(byte[] bArr, int i, int[] iArr, int i2, int i3) {
        for (int i4 = 0; i4 < i3; i4++) {
            iArr[i2 + i4] = c(bArr, i);
            i += 4;
        }
    }

    public static void a(long[] jArr, int i, int i2, byte[] bArr, int i3) {
        for (int i4 = 0; i4 < i2; i4++) {
            b(jArr[i + i4], bArr, i3);
            i3 += 8;
        }
    }
}
