package bc.org.bouncycastle.crypto.macs;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.Digest;
import bc.org.bouncycastle.crypto.params.KeyParameter;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q4;
import java.util.Hashtable;
import org.bouncycastle.pqc.jcajce.spec.McElieceCCA2KeyGenParameterSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\macs\HMac.smali */
public class HMac implements l5 {
    private static Hashtable h;
    private Digest a;
    private int b;
    private int c;
    private m5 d;
    private m5 e;
    private byte[] f;
    private byte[] g;

    static {
        Hashtable hashtable = new Hashtable();
        h = hashtable;
        hashtable.put("GOST3411", e5.c(32));
        h.put("MD2", e5.c(16));
        h.put("MD4", e5.c(64));
        h.put("MD5", e5.c(64));
        h.put("RIPEMD128", e5.c(64));
        h.put("RIPEMD160", e5.c(64));
        h.put(McElieceCCA2KeyGenParameterSpec.SHA1, e5.c(64));
        h.put(McElieceCCA2KeyGenParameterSpec.SHA224, e5.c(64));
        h.put("SHA-256", e5.c(64));
        h.put(McElieceCCA2KeyGenParameterSpec.SHA384, e5.c(128));
        h.put("SHA-512", e5.c(128));
        h.put("Tiger", e5.c(64));
        h.put("Whirlpool", e5.c(64));
    }

    public HMac(Digest digest) {
        this(digest, a(digest));
    }

    private static int a(Digest digest) {
        if (digest instanceof q4) {
            return ((q4) digest).getByteLength();
        }
        Integer num = (Integer) h.get(digest.getAlgorithmName());
        if (num != null) {
            return num.intValue();
        }
        throw new IllegalArgumentException("unknown digest passed: " + digest.getAlgorithmName());
    }

    public int doFinal(byte[] bArr, int i) {
        this.a.doFinal(this.g, this.c);
        m5 m5Var = this.e;
        if (m5Var != null) {
            ((m5) this.a).reset(m5Var);
            Digest digest = this.a;
            digest.update(this.g, this.c, digest.getDigestSize());
        } else {
            Digest digest2 = this.a;
            byte[] bArr2 = this.g;
            digest2.update(bArr2, 0, bArr2.length);
        }
        int doFinal = this.a.doFinal(bArr, i);
        int i2 = this.c;
        while (true) {
            byte[] bArr3 = this.g;
            if (i2 >= bArr3.length) {
                break;
            }
            bArr3[i2] = 0;
            i2++;
        }
        m5 m5Var2 = this.d;
        if (m5Var2 != null) {
            ((m5) this.a).reset(m5Var2);
        } else {
            Digest digest3 = this.a;
            byte[] bArr4 = this.f;
            digest3.update(bArr4, 0, bArr4.length);
        }
        return doFinal;
    }

    public String getAlgorithmName() {
        return this.a.getAlgorithmName() + "/HMAC";
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l5
    public int getMacSize() {
        return this.b;
    }

    public Digest getUnderlyingDigest() {
        return this.a;
    }

    public void init(CipherParameters cipherParameters) {
        byte[] bArr;
        this.a.reset();
        byte[] key = ((KeyParameter) cipherParameters).getKey();
        int length = key.length;
        if (length > this.c) {
            this.a.update(key, 0, length);
            this.a.doFinal(this.f, 0);
            length = this.b;
        } else {
            System.arraycopy(key, 0, this.f, 0, length);
        }
        while (true) {
            bArr = this.f;
            if (length >= bArr.length) {
                break;
            }
            bArr[length] = 0;
            length++;
        }
        System.arraycopy(bArr, 0, this.g, 0, this.c);
        a(this.f, this.c, (byte) 54);
        a(this.g, this.c, (byte) 92);
        Digest digest = this.a;
        if (digest instanceof m5) {
            m5 copy = ((m5) digest).copy();
            this.e = copy;
            ((Digest) copy).update(this.g, 0, this.c);
        }
        Digest digest2 = this.a;
        byte[] bArr2 = this.f;
        digest2.update(bArr2, 0, bArr2.length);
        Digest digest3 = this.a;
        if (digest3 instanceof m5) {
            this.d = ((m5) digest3).copy();
        }
    }

    public void reset() {
        m5 m5Var = this.d;
        if (m5Var != null) {
            ((m5) this.a).reset(m5Var);
            return;
        }
        this.a.reset();
        Digest digest = this.a;
        byte[] bArr = this.f;
        digest.update(bArr, 0, bArr.length);
    }

    public void update(byte b) {
        this.a.update(b);
    }

    private HMac(Digest digest, int i) {
        this.a = digest;
        int digestSize = digest.getDigestSize();
        this.b = digestSize;
        this.c = i;
        this.f = new byte[i];
        this.g = new byte[i + digestSize];
    }

    public void update(byte[] bArr, int i, int i2) {
        this.a.update(bArr, i, i2);
    }

    private static void a(byte[] bArr, int i, byte b) {
        for (int i2 = 0; i2 < i; i2++) {
            bArr[i2] = (byte) (bArr[i2] ^ b);
        }
    }
}
