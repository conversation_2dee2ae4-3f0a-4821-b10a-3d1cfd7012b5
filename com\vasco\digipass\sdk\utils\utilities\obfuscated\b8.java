package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\b8.smali */
public interface b8 {
    public static final w a = new w("*******").j();
    public static final w b = new w("*******").j();
    public static final w c = new w("*******").j();
    public static final w d = new w("*******").j();
    public static final w e = new w("********").j();
    public static final w f = new w("********").j();
    public static final w g = new w("********").j();
    public static final w h = new w("********").j();
    public static final w i = new w("********").j();
    public static final w j = new w("********.2.26").j();
    public static final w k = new w("********.2.1").j();
    public static final w l = new w("********.3.1.2").j();
    public static final w m = new w("*******.1").j();
    public static final w n;

    /* renamed from: o, reason: collision with root package name */
    public static final w f22o;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.3.6.1.5.5.7");
        n = wVar;
        f22o = wVar.a("6.30");
        p = wVar.a("6.31");
        q = wVar.a("6.32");
        r = wVar.a("6.33");
        s = wVar.a("1");
        t = new w("2.5.29");
        w a2 = wVar.a("48");
        u = a2;
        w j2 = a2.a("2").j();
        v = j2;
        w j3 = a2.a("1").j();
        w = j3;
        x = j3;
        y = j2;
        z = new w("1.2.840.113533.7.66.13");
    }
}
