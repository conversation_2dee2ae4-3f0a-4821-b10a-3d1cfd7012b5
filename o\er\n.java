package o.er;

import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import o.ah.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\n.smali */
public final class n extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static boolean b;
    private static int e;
    private static int f;
    private static int g;
    private static boolean i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        e();
        Process.getThreadPriority(0);
        int i2 = g + 89;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                break;
            default:
                int i3 = 87 / 0;
                break;
        }
    }

    static void e() {
        a = new char[]{61759, 61794, 61787, 61798, 61760, 61791, 61748, 61797, 61792, 61788, 61783, 61807, 61765, 61802, 61781, 61785, 61796, 61751, 61803, 61784, 61793, 61762, 61795, 61712, 61731, 61771, 61782, 61780, 61786, 61777};
        b = true;
        i = true;
        e = 782103024;
    }

    static void init$0() {
        $$a = new byte[]{70, -116, 4, 37};
        $$b = Opcodes.ISHR;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(int r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 117
            int r9 = r9 * 2
            int r9 = 4 - r9
            byte[] r0 = o.er.n.$$a
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r9]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r9 = -r9
            int r8 = r8 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.n.j(int, byte, byte, java.lang.Object[]):void");
    }

    @Override // o.er.h
    public final /* synthetic */ boolean b() {
        int i2 = g + 69;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                super.b();
                throw null;
            default:
                boolean b2 = super.b();
                int i3 = g + 57;
                f = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return b2;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public n(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        int i2 = g + 77;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return new a[]{this.d.b()};
            default:
                a[] aVarArr = new a[1];
                aVarArr[1] = this.d.b();
                return aVarArr;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0081. Please report as an issue. */
    private String d() throws WalletValidationException {
        int i2 = f + 93;
        g = i2 % 128;
        int i3 = i2 % 2;
        String d = this.d.b().d();
        switch (d == null ? (char) 27 : '#') {
            case '#':
                return d;
            default:
                int i4 = f + 29;
                g = i4 % 128;
                int i5 = i4 % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                h(null, 127 - (ViewConfiguration.getJumpTapTimeout() >> 16), null, "\u0083\u008f\u0086\u008e\u0084\u0083\u008d\u008c\u008b\u008a\u0089\u0088\u0086\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                h(null, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 127, null, "\u0082\u0084\u0083\u0091\u0091\u008b\u0089\u0098\u0094\u0091\u0093\u008b\u0098\u0095\u0082\u0098\u0091\u008a\u0093\u008b\u009d\u0083\u009c\u0098\u0083\u0094\u0091\u0098\u0083\u0088\u009a\u0098\u0099\u0098\u009c\u0083\u0091\u008b\u0086\u008f\u0095\u0088\u0088\u008b\u0098\u0083\u0097\u008b\u0082\u0098\u0082\u0084\u0083\u0091\u0091\u008b\u0089\u0098\u008c\u008b\u008a\u0089\u0088\u0086\u009c\u0098\u0091\u0083\u0090\u0098\u0095\u0091\u0098\u0083\u008a\u009b\u008b\u0082\u009a\u0098\u0099\u0098\u0083\u0097\u008b\u0096\u0082\u0084\u0083\u0091\u0091\u008b\u0085\u0082\u0095\u0086\u0091\u008b\u008f\u0086\u0091\u0082\u0083\u0094\u0091\u0093\u0092\u0091\u0083\u0090", objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                h(null, 127 - Drawable.resolveOpacity(0, 0), null, "\u0082\u0095\u0086\u0091\u008b\u008f\u0086\u0091\u0082\u0083\u0094\u0091\u0093\u008b\u009e\u0095\u0082", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                int i6 = f + 79;
                g = i6 % 128;
                switch (i6 % 2 != 0) {
                }
                return intern2;
        }
    }

    public final o.v.i<o.dw.a> a() throws WalletValidationException {
        String d = d();
        o.eo.e eVar = this.c;
        boolean b2 = b();
        e.a aVar = e.a.c;
        o.dw.c cVar = new o.dw.c() { // from class: o.er.n.2
        };
        Object[] objArr = new Object[1];
        h(null, TextUtils.indexOf("", "", 0) + 127, null, "\u0083\u008f\u0086\u008e\u0084\u0083\u008d\u008c\u008b\u008a\u0089\u0088\u0086\u0087\u0082\u0086\u0085", objArr);
        o.v.i<o.dw.a> iVar = new o.v.i<>(d, eVar, b2, aVar, cVar, ((String) objArr[0]).intern());
        int i2 = g + 5;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int i3 = 60 / 0;
                return iVar;
            default:
                return iVar;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x02d0, code lost:
    
        r3 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getWindowTouchSlop() >> 8), (char) android.view.View.resolveSizeAndState(0, 0, 0), 207 - android.view.View.resolveSizeAndState(0, 0, 0));
        r10 = (byte) 0;
        r11 = r10;
        r14 = new java.lang.Object[1];
        j(r10, r11, r11, r14);
        r3 = r3.getMethod((java.lang.String) r14[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(745816316, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:103:0x0318, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:104:0x0319, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x031d, code lost:
    
        if (r1 != null) goto L110;
     */
    /* JADX WARN: Code restructure failed: missing block: B:106:0x031f, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x0320, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x02b4, code lost:
    
        r23[0] = new java.lang.String(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:10:0x0036, code lost:
    
        if ((r5 % 2) == 0) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:110:0x02bc, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:111:0x029c, code lost:
    
        r1 = '3';
     */
    /* JADX WARN: Code restructure failed: missing block: B:112:0x0321, code lost:
    
        r6.e = r21.length;
        r1 = new char[r6.e];
        r6.c = 0;
        r3 = o.er.n.$11 + 47;
        o.er.n.$10 = r3 % 128;
        r3 = r3 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:114:0x033b, code lost:
    
        if (r6.c >= r6.e) goto L159;
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x033d, code lost:
    
        r3 = o.er.n.$11 + 29;
        o.er.n.$10 = r3 % 128;
        r3 = r3 % 2;
        r1[r6.c] = (char) (r7[r21[(r6.e - 1) - r6.c] - r20] - r2);
        r6.c++;
     */
    /* JADX WARN: Code restructure failed: missing block: B:117:0x035f, code lost:
    
        r0 = new java.lang.String(r1);
        r1 = o.er.n.$10 + 41;
        o.er.n.$11 = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x036e, code lost:
    
        if ((r1 % 2) == 0) goto L120;
     */
    /* JADX WARN: Code restructure failed: missing block: B:119:0x0370, code lost:
    
        r23[0] = r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0038, code lost:
    
        r5 = r19.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:120:0x0373, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x0374, code lost:
    
        r0 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:123:0x0375, code lost:
    
        r0.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:124:0x0378, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:129:0x0114, code lost:
    
        r2 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16), (char) (8856 - ((android.os.Process.getThreadPriority(0) + 20) >> 6)), 325 - (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)));
        r8 = (byte) 1;
        r9 = (byte) (r8 - 1);
        r11 = new java.lang.Object[1];
        j(r8, r9, r9, r11);
        r2 = r2.getMethod((java.lang.String) r11[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1667314477, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0048, code lost:
    
        r5 = r5;
        r6 = new o.a.j();
        r7 = o.er.n.a;
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x037b, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x037c, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:132:0x0380, code lost:
    
        if (r1 != null) goto L128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:133:0x0382, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:134:0x0383, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:135:0x0056, code lost:
    
        r9 = ']';
     */
    /* JADX WARN: Code restructure failed: missing block: B:136:0x003d, code lost:
    
        r19.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:138:0x0040, code lost:
    
        r2.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:139:0x0043, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0051, code lost:
    
        if (r7 == null) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:143:0x0046, code lost:
    
        r5 = r19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:144:0x0022, code lost:
    
        r1 = r1.getBytes(org.bouncycastle.i18n.LocalizedMessage.DEFAULT_ENCODING);
     */
    /* JADX WARN: Code restructure failed: missing block: B:148:0x0020, code lost:
    
        if (r1 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0053, code lost:
    
        r9 = '\b';
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0058, code lost:
    
        r10 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x005d, code lost:
    
        switch(r9) {
            case 93: goto L51;
            default: goto L32;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0060, code lost:
    
        r9 = r7.length;
        r14 = new char[r9];
        r15 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0064, code lost:
    
        if (r15 >= r9) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0066, code lost:
    
        r16 = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x006b, code lost:
    
        switch(r16) {
            case 0: goto L38;
            default: goto L149;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0070, code lost:
    
        r8 = o.er.n.$10 + 1;
        o.er.n.$11 = r8 % 128;
        r8 = r8 % r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x007b, code lost:
    
        r8 = new java.lang.Object[1];
        r8[r4] = java.lang.Integer.valueOf(r7[r15]);
        r2 = o.e.a.s.get(1085633688);
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0090, code lost:
    
        if (r2 == null) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00e4, code lost:
    
        r14[r15] = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r8)).charValue();
        r15 = r15 + 1;
        r3 = 2;
        r4 = 0;
        r10 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0093, code lost:
    
        r2 = (java.lang.Class) o.e.a.c(android.os.Process.getGidForName("") + 12, (char) ((android.os.Process.getElapsedCpuTime() > r10 ? 1 : (android.os.Process.getElapsedCpuTime() == r10 ? 0 : -1)) - 1), 494 - (android.view.ViewConfiguration.getZoomControlsTimeout() > r10 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == r10 ? 0 : -1)));
        r3 = (byte) o.er.n.$$a.length;
        r4 = (byte) (r3 - 4);
        r11 = new java.lang.Object[1];
        j(r3, r4, r4, r11);
        r2 = r2.getMethod((java.lang.String) r11[0], java.lang.Integer.TYPE);
        o.e.a.s.put(1085633688, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00ef, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x00f0, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x00f4, code lost:
    
        if (r1 != null) goto L48;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x00f6, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x00f7, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00f8, code lost:
    
        r7 = r14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0069, code lost:
    
        r16 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x00fb, code lost:
    
        r3 = new java.lang.Object[]{java.lang.Integer.valueOf(o.er.n.e)};
        r2 = o.e.a.s.get(-1667314477);
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0111, code lost:
    
        if (r2 == null) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x015c, code lost:
    
        r2 = ((java.lang.Integer) ((java.lang.reflect.Method) r2).invoke(null, r3)).intValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x016e, code lost:
    
        if (o.er.n.i == false) goto L90;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x0171, code lost:
    
        r6.e = r1.length;
        r0 = new char[r6.e];
        r6.c = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x017f, code lost:
    
        if (r6.c >= r6.e) goto L151;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0181, code lost:
    
        r3 = o.er.n.$10 + 3;
        o.er.n.$11 = r3 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x018b, code lost:
    
        if ((r3 % 2) != 0) goto L150;
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x0202, code lost:
    
        r0[r6.c] = (char) (r7[r1[(r6.e - 1) - r6.c] + r20] - r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0214, code lost:
    
        r3 = new java.lang.Object[]{r6, r6};
        r5 = o.e.a.s.get(745816316);
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x0222, code lost:
    
        if (r5 == null) goto L80;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x026a, code lost:
    
        ((java.lang.reflect.Method) r5).invoke(null, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x0225, code lost:
    
        r5 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getPressedStateDuration() >> 16), (char) (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 206 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0, 0));
        r8 = (byte) 0;
        r9 = r8;
        r14 = new java.lang.Object[1];
        j(r8, r9, r9, r14);
        r5 = r5.getMethod((java.lang.String) r14[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(745816316, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0272, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0273, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x0277, code lost:
    
        if (r1 != null) goto L86;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x0279, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x027a, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x018d, code lost:
    
        r0[r6.c] = (char) (r7[r1[(r6.e - 0) + r6.c] / r20] << r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x001a, code lost:
    
        if (r1 != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x01a0, code lost:
    
        r3 = new java.lang.Object[]{r6, r6};
        r5 = o.e.a.s.get(745816316);
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x01ae, code lost:
    
        if (r5 == null) goto L68;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x01f2, code lost:
    
        ((java.lang.reflect.Method) r5).invoke(null, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x01b1, code lost:
    
        r5 = (java.lang.Class) o.e.a.c(android.text.TextUtils.getOffsetAfter("", 0) + 10, (char) (android.view.ViewConfiguration.getScrollDefaultDelay() >> 16), android.text.TextUtils.indexOf("", "") + 207);
        r9 = (byte) 0;
        r10 = r9;
        r14 = new java.lang.Object[1];
        j(r9, r10, r10, r14);
        r5 = r5.getMethod((java.lang.String) r14[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(745816316, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x01f9, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x01fa, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0028, code lost:
    
        r1 = r1;
        r2 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x01fe, code lost:
    
        if (r1 != null) goto L74;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0200, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0201, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x027b, code lost:
    
        r23[0] = new java.lang.String(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:85:0x0283, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x0286, code lost:
    
        if (o.er.n.b == false) goto L112;
     */
    /* JADX WARN: Code restructure failed: missing block: B:88:0x0289, code lost:
    
        r6.e = r5.length;
        r0 = new char[r6.e];
        r6.c = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x002b, code lost:
    
        if (r19 == null) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x0297, code lost:
    
        if (r6.c >= r6.e) goto L96;
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x0299, code lost:
    
        r1 = '+';
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x029e, code lost:
    
        switch(r1) {
            case 51: goto L156;
            default: goto L98;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x02a1, code lost:
    
        r0[r6.c] = (char) (r7[r5[(r6.e - 1) - r6.c] - r20] - r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:95:0x02bd, code lost:
    
        r1 = new java.lang.Object[]{r6, r6};
        r3 = o.e.a.s.get(745816316);
     */
    /* JADX WARN: Code restructure failed: missing block: B:96:0x02cb, code lost:
    
        if (r3 == null) goto L104;
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x0310, code lost:
    
        ((java.lang.reflect.Method) r3).invoke(null, r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x002d, code lost:
    
        r5 = o.er.n.$10 + 13;
        o.er.n.$11 = r5 % 128;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 928
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.n.h(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
