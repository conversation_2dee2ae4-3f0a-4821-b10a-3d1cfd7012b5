package o.ey;

import android.content.Context;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import kotlin.io.encoding.Base64;
import o.a.m;
import o.ee.g;
import o.fc.c;
import o.fc.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\b.smali */
public abstract class b<T extends o.fc.e> extends e<T> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static char b;
    private static char[] c;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        p();
        int i = d + 77;
        e = i % 128;
        switch (i % 2 != 0 ? '[' : 'L') {
            case Base64.mimeLineLength /* 76 */:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void C(int r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r9 = r9 + 66
            byte[] r0 = o.ey.b.$$d
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            int r7 = r7 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.b.C(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{114, -113, -41, 111};
        $$e = 89;
    }

    static void p() {
        c = new char[]{50943, 50843, 50842, 50853, 50855, 50858, 50855, 50877, 50860, 50839, 50854, 50852, 50863, 50835, 50805, 50786, 50787, 50810, 50807, 50815, 50784, 50784, 50809, 50814, 50822, 50768, 50774, 50766, 50754, 50795, 50771, 50768, 50792, 50770, 50924, 50836, 50851, 50876, 50855, 50856, 50854, 50852, 50854, 50860, 50837, 50848, 50878, 50852, 50852, 50876, 50834, 50837, 50850, 50705, 50714, 50733, 50707, 50755, 50789, 50755, 50735, 50734, 50707, 50735, 50734, 50704, 50755, 50774, 50755, 50732, 50734, 50735, 50690, 50735, 50700, 50735, 50941, 50848, 50878, 50852, 50852, 50876, 50823, 50923, 50923, 50829, 50853, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50912, 50912, 50830, 50859, 50849, 50860, 50836, 50851, 50875, 50862, 50835, 50854, 50855, 50852, 50853, 50832, 50857, 50849, 50837, 50837, 50850, 50859, 50860, 50863, 50857, 50849, 50840, 50839, 50877, 50860, 50943, 50850, 50845, 50816, 50819, 50921, 50829, 50854, 50854, 50856, 50855, 50859, 50860, 50858, 50853, 50831, 50830, 50855, 50877, 50820, 50821, 50853, 50848, 50878, 50852, 50852, 50876, 50823, 50825, 50857, 50855, 50820, 50824, 50929, 50870, 50837, 50832, 50866, 50866, 50866, 50867, 50833, 50932, 50932, 50840, 50874, 50866, 50867, 50874, 50866, 50866, 50858, 50850, 50866, 50850, 50868, 50767, 50854, 50873, 50764, 50849, 50876, 50764, 50761, 50763, 50764, 50864, 50836, 50834};
        a = new char[]{17044, 30587, 30517, 30571, 30574, 30588, 30561, 30560, 17046, 30568, 30564, 30562, 30498, 30554, 30591, 30563, 30569, 30567, 30572, 30582, 30570, 30555, 30589, 30556, 30511, 30559, 30534, 30529, 30573, 17047, 30583, 30537, 17041, 30585, 30566, 30532};
        b = (char) 17043;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // o.ey.e
    public final /* synthetic */ boolean a(Context context, o.fc.d dVar) {
        int i = d + 19;
        e = i % 128;
        o.fc.e eVar = (o.fc.e) dVar;
        switch (i % 2 == 0) {
            case true:
                boolean d2 = d(eVar);
                int i2 = e + Opcodes.LMUL;
                d = i2 % 128;
                switch (i2 % 2 == 0 ? '\b' : (char) 7) {
                    case '\b':
                        int i3 = 79 / 0;
                        return d2;
                    default:
                        return d2;
                }
            default:
                d(eVar);
                throw null;
        }
    }

    public b(String str, String str2, boolean z) {
        super(str, str2, z);
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x008b, code lost:
    
        if (r3.hasNext() == false) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x008d, code lost:
    
        r5 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0090, code lost:
    
        switch(r5) {
            case 0: goto L23;
            default: goto L32;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x00e1, code lost:
    
        r10.b(((o.fc.e) r3.next()).j());
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0093, code lost:
    
        r3 = new java.lang.Object[1];
        v("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{13, 11, 73, 0}, true, r3);
        r0.d(((java.lang.String) r3[0]).intern(), r10);
        r3 = new java.lang.Object[1];
        B((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 16, "\u0013\u0017\u0015\u0002\u0012\b\u0004\u001f\u0003\u0010\u000b\u001d\u0013\u0002\u001b\u0002", (byte) (android.graphics.Color.blue(0) + 104), r3);
        r0.d(((java.lang.String) r3[0]).intern(), j().e());
        r10 = j().d();
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00de, code lost:
    
        if (r10 == null) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x00f1, code lost:
    
        r2 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00f2, code lost:
    
        switch(r2) {
            case 1: goto L28;
            default: goto L27;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00f5, code lost:
    
        r3 = new java.lang.Object[1];
        v("\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{24, 10, 49, 0}, true, r3);
        r0.d(((java.lang.String) r3[0]).intern(), r10.getTime() / 1000);
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00ef, code lost:
    
        r2 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x008f, code lost:
    
        r5 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x006d, code lost:
    
        if (j() != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0064, code lost:
    
        if (j() != null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x006f, code lost:
    
        r10 = new o.eg.e();
        r3 = f().iterator();
        r5 = o.ey.b.d + 57;
        o.ey.b.e = r5 % 128;
        r5 = r5 % 2;
     */
    @Override // o.ey.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public o.eg.b b(o.ek.b r10) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 370
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.b.b(o.ek.b):o.eg.b");
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x005e, code lost:
    
        if (f().isEmpty() == false) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0060, code lost:
    
        r0 = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0067, code lost:
    
        if (j() != null) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0069, code lost:
    
        o.ee.g.c();
        r0 = new java.lang.StringBuilder();
        r5 = new java.lang.Object[1];
        v("\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{76, 51, 0, 23}, false, r5);
        r0 = r0.append(((java.lang.String) r5[0]).intern()).append(d());
        r1 = new java.lang.Object[1];
        B(26 - (android.view.ViewConfiguration.getPressedStateDuration() >> 16), "\u001d\f\u0005\u0000\u001e\f\u0006\u0019\b\u0016\u0012\u0019\u0010\u0002\u001c\n\b\u0017\u0002\u0013\u0017\u0004\u001d\u0000\u0013\u0002", (byte) (5 - (android.view.ViewConfiguration.getFadingEdgeLength() >> 16)), r1);
        o.ee.g.d(r3, r0.append(((java.lang.String) r1[0]).intern()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00ba, code lost:
    
        r0 = j().d();
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x00c2, code lost:
    
        if (r0 == null) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00da, code lost:
    
        if (r0.before(new java.util.Date(new java.util.Date().getTime() - 120000)) == false) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00dc, code lost:
    
        o.ee.g.c();
        r0 = new java.lang.StringBuilder();
        r5 = new java.lang.Object[1];
        v("\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{76, 51, 0, 23}, false, r5);
        r0 = r0.append(((java.lang.String) r5[0]).intern()).append(d());
        r1 = new java.lang.Object[1];
        B(23 - (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16), "\u001d\f\u0005\u0000\u001c\u0000\f\u001e\u0012 \u0010 \u0017\u0015\u0000\u001b\b\u0016\u0012\u0019\u0002\u0017㗯", (byte) (1 - android.graphics.Color.alpha(0)), r1);
        o.ee.g.d(r3, r0.append(((java.lang.String) r1[0]).intern()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x012a, code lost:
    
        r4 = f().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0136, code lost:
    
        if (r4.hasNext() == false) goto L31;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0138, code lost:
    
        r0 = '%';
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0143, code lost:
    
        switch(r0) {
            case 47: goto L45;
            default: goto L34;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0146, code lost:
    
        r0 = (o.fc.e) r4.next();
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0150, code lost:
    
        if (r0.a() == false) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x01b8, code lost:
    
        o.ee.g.c();
        r4 = new java.lang.StringBuilder();
        r9 = new java.lang.Object[1];
        B((android.widget.ExpandableListView.getPackedPositionForGroup(0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForGroup(0) == 0 ? 0 : -1)) + 43, "\b\u0015\u0003\u0019\u0012 \u0005\u001f\u0015\u0014\u0003\u0017\r\u0013\u0002\u0017\u0001\r\u0013\u001c\u0001\u0016\b\u0017\u0007\u0000\u001e\u0012\u001c\u0000㙎㙎\u0010!\u0016\u0000\u0004\u001f\b\u0007\u001a\u0000㘞", (byte) (99 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0, 0)), r9);
        r4 = r4.append(((java.lang.String) r9[0]).intern()).append(d());
        r1 = new java.lang.Object[1];
        v("\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001", new int[]{127, 33, 0, 27}, false, r1);
        o.ee.g.d(r3, r4.append(((java.lang.String) r1[0]).intern()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0153, code lost:
    
        o.ee.g.c();
        r0 = new java.lang.StringBuilder();
        r8 = new java.lang.Object[1];
        B(42 - android.widget.ExpandableListView.getPackedPositionChild(0), "\b\u0015\u0003\u0019\u0012 \u0005\u001f\u0015\u0014\u0003\u0017\r\u0013\u0002\u0017\u0001\r\u0013\u001c\u0001\u0016\b\u0017\u0007\u0000\u001e\u0012\u001c\u0000㙎㙎\u0010!\u0016\u0000\u0004\u001f\b\u0007\u001a\u0000㘞", (byte) (100 - (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), r8);
        r0 = r0.append(((java.lang.String) r8[0]).intern()).append(d());
        r1 = new java.lang.Object[1];
        B(33 - android.graphics.Color.red(0), "\u001d\f\u0005\u0000\u001e\f\u0006\u0019\b\u0016\u0012\u0019\u0003\"\n\u0004\u0010\u0003\u001b\u0010\u0012\u001a\r\n\u0012\u001c\u0010\u0002\u0017\u0007\u0012\b㙊", (byte) ((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 92), r1);
        o.ee.g.d(r3, r0.append(((java.lang.String) r1[0]).intern()).toString());
        r0 = o.ey.b.e + com.esotericsoftware.asm.Opcodes.DDIV;
        o.ey.b.d = r0 % 128;
        r0 = r0 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x013b, code lost:
    
        r0 = '/';
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0211, code lost:
    
        o.ee.g.c();
        r3 = new java.lang.StringBuilder();
        r5 = new java.lang.Object[1];
        v("\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{76, 51, 0, 23}, false, r5);
        r3 = r3.append(((java.lang.String) r5[0]).intern()).append(d());
        r1 = new java.lang.Object[1];
        B((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 18, "\u001d\f\u0005\u0000\u001e\f\u0006\u0019\u0016\u001c\u0017\u0002\f\u001d\b\u0016\u0017\u0001", (byte) (77 - (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16)), r1);
        o.ee.g.d(r0, r3.append(((java.lang.String) r1[0]).intern()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0053, code lost:
    
        if (f() != null) goto L10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x003a, code lost:
    
        if (f() != null) goto L10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x003c, code lost:
    
        r3 = r0;
     */
    @Override // o.ey.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.fc.e k() {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.b.k():o.fc.e");
    }

    private boolean d(T t) {
        if (f() == null) {
            c(new ArrayList());
        }
        ListIterator listIterator = f().listIterator();
        while (true) {
            boolean z = true;
            if (!listIterator.hasNext()) {
                f().add(t);
                int i = d + Opcodes.LREM;
                e = i % 128;
                int i2 = i % 2;
                return true;
            }
            o.fc.e eVar = (o.fc.e) listIterator.next();
            if (eVar.h() == t.h()) {
                z = false;
            }
            switch (z) {
                case true:
                default:
                    int i3 = d + 87;
                    e = i3 % 128;
                    if (i3 % 2 == 0) {
                        if (eVar.b() == c.b) {
                            listIterator.set(t);
                        }
                        return false;
                    }
                    eVar.b();
                    c cVar = c.b;
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }
    }

    @Override // o.ey.e
    public final void b() {
        String intern;
        Object obj;
        int i = e + 59;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                g.c();
                Object[] objArr = new Object[1];
                v("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000", new int[]{34, 19, 0, 9}, true, objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                v("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{Opcodes.IF_ICMPNE, 36, 19, 30}, true, objArr2);
                obj = objArr2[0];
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                v("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000", new int[]{34, 19, 0, 9}, false, objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                v("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{Opcodes.IF_ICMPNE, 36, 19, 30}, true, objArr4);
                obj = objArr4[0];
                break;
        }
        g.d(intern, ((String) obj).intern());
        c((List) null);
        int i2 = d + 5;
        e = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:86:0x00f0, code lost:
    
        if (r0[r4.d] == 1) goto L63;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:56:0x0227 A[Catch: all -> 0x027a, TryCatch #3 {all -> 0x027a, blocks: (B:52:0x0213, B:55:0x0272, B:56:0x0227), top: B:51:0x0213 }] */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v23, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(java.lang.String r20, int[] r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 840
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.b.v(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    private static void B(int i, String str, byte b2, Object[] objArr) {
        char[] cArr;
        int i2;
        char c2;
        if (str != null) {
            int i3 = $10 + 89;
            $11 = i3 % 128;
            int i4 = i3 % 2;
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = a;
        char c3 = '0';
        float f = 0.0f;
        int i5 = -1401577988;
        if (cArr3 != null) {
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int i6 = 0;
            while (i6 < length) {
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr3[i6])};
                    Object obj = o.e.a.s.get(Integer.valueOf(i5));
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((AudioTrack.getMinVolume() > f ? 1 : (AudioTrack.getMinVolume() == f ? 0 : -1)) + 17, (char) (TextUtils.lastIndexOf("", c3, 0, 0) + 1), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 76);
                        byte b3 = (byte) 0;
                        byte b4 = b3;
                        Object[] objArr3 = new Object[1];
                        C(b3, b4, (byte) (b4 | 7), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj);
                    }
                    cArr4[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i6++;
                    c3 = '0';
                    f = 0.0f;
                    i5 = -1401577988;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr3 = cArr4;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(b)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 17, (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 75 - TextUtils.indexOf((CharSequence) "", '0'));
                byte b5 = (byte) 0;
                byte b6 = b5;
                Object[] objArr5 = new Object[1];
                C(b5, b6, (byte) (b6 | 7), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i];
            char c4 = 3;
            if (i % 2 != 0) {
                int i7 = $10;
                int i8 = i7 + 67;
                $11 = i8 % 128;
                int i9 = i8 % 2;
                i2 = i - 1;
                cArr5[i2] = (char) (cArr2[i2] - b2);
                int i10 = i7 + 3;
                $11 = i10 % 128;
                int i11 = i10 % 2;
            } else {
                i2 = i;
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (true) {
                    switch (mVar.b >= i2) {
                        case true:
                            break;
                        default:
                            mVar.e = cArr2[mVar.b];
                            mVar.a = cArr2[mVar.b + 1];
                            switch (mVar.e != mVar.a) {
                                case true:
                                    try {
                                        Object[] objArr6 = new Object[13];
                                        objArr6[12] = mVar;
                                        objArr6[11] = Integer.valueOf(charValue);
                                        objArr6[10] = mVar;
                                        objArr6[9] = mVar;
                                        objArr6[8] = Integer.valueOf(charValue);
                                        objArr6[7] = mVar;
                                        objArr6[6] = mVar;
                                        objArr6[5] = Integer.valueOf(charValue);
                                        objArr6[4] = mVar;
                                        objArr6[c4] = mVar;
                                        objArr6[2] = Integer.valueOf(charValue);
                                        objArr6[1] = mVar;
                                        objArr6[0] = mVar;
                                        Object obj3 = o.e.a.s.get(696901393);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10, (char) (8856 - View.MeasureSpec.getSize(0)), 323 - ExpandableListView.getPackedPositionChild(0L));
                                            byte b7 = (byte) 0;
                                            byte b8 = b7;
                                            Object[] objArr7 = new Object[1];
                                            C(b7, b8, (byte) (b8 + 3), objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(696901393, obj3);
                                        }
                                        switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h ? '!' : '\b') {
                                            case '!':
                                                int i12 = $11 + 93;
                                                $10 = i12 % 128;
                                                int i13 = i12 % 2;
                                                try {
                                                    Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                    Object obj4 = o.e.a.s.get(1075449051);
                                                    if (obj4 != null) {
                                                        c2 = 3;
                                                    } else {
                                                        Class cls4 = (Class) o.e.a.c((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 11, (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 65 - KeyEvent.keyCodeFromString(""));
                                                        byte b9 = (byte) 0;
                                                        Object[] objArr9 = new Object[1];
                                                        C(b9, b9, (byte) $$d.length, objArr9);
                                                        String str2 = (String) objArr9[0];
                                                        c2 = 3;
                                                        obj4 = cls4.getMethod(str2, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                        o.e.a.s.put(1075449051, obj4);
                                                    }
                                                    int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                    int i14 = (mVar.d * charValue) + mVar.h;
                                                    cArr5[mVar.b] = cArr3[intValue];
                                                    cArr5[mVar.b + 1] = cArr3[i14];
                                                    int i15 = $11 + 37;
                                                    $10 = i15 % 128;
                                                    int i16 = i15 % 2;
                                                    break;
                                                } catch (Throwable th2) {
                                                    Throwable cause2 = th2.getCause();
                                                    if (cause2 == null) {
                                                        throw th2;
                                                    }
                                                    throw cause2;
                                                }
                                            default:
                                                c2 = 3;
                                                if (mVar.c != mVar.d) {
                                                    int i17 = (mVar.c * charValue) + mVar.h;
                                                    int i18 = (mVar.d * charValue) + mVar.i;
                                                    cArr5[mVar.b] = cArr3[i17];
                                                    cArr5[mVar.b + 1] = cArr3[i18];
                                                    break;
                                                } else {
                                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                    int i19 = (mVar.c * charValue) + mVar.i;
                                                    int i20 = (mVar.d * charValue) + mVar.h;
                                                    cArr5[mVar.b] = cArr3[i19];
                                                    cArr5[mVar.b + 1] = cArr3[i20];
                                                    break;
                                                }
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                default:
                                    c2 = c4;
                                    cArr5[mVar.b] = (char) (mVar.e - b2);
                                    cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                    break;
                            }
                            mVar.b += 2;
                            c4 = c2;
                    }
                }
            }
            int i21 = 0;
            while (i21 < i) {
                int i22 = $10 + 77;
                $11 = i22 % 128;
                if (i22 % 2 == 0) {
                    cArr5[i21] = (char) (cArr5[i21] ^ 533);
                    i21 += Opcodes.INEG;
                } else {
                    cArr5[i21] = (char) (cArr5[i21] ^ 13722);
                    i21++;
                }
            }
            objArr[0] = new String(cArr5);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
