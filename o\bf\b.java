package o.bf;

import android.content.Context;
import android.graphics.Color;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.h;
import o.bb.d;
import o.cf.i;
import o.cf.j;
import o.y.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bf\b.smali */
public final class b extends o.y.b<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static int e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bf\b$e.smali */
    public interface e {
        void c();

        void c(d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        n();
        KeyEvent.getModifierMetaStateMask();
        ViewConfiguration.getMinimumFlingVelocity();
        TextUtils.lastIndexOf("", '0', 0);
        ViewConfiguration.getLongPressTimeout();
        ExpandableListView.getPackedPositionForChild(0, 0);
        SystemClock.elapsedRealtime();
        int i = b + 69;
        e = i % 128;
        switch (i % 2 != 0 ? '@' : Typography.dollar) {
            case '$':
                return;
            default:
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{110, -66, 47, -83};
        $$e = 233;
    }

    static void n() {
        c = 874635289;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.bf.b.$$d
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 109 - r7
            int r9 = r9 * 3
            int r9 = r9 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r10 = r10 + 1
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bf.b.o(short, int, byte, java.lang.Object[]):void");
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        int i = e + 85;
        b = i % 128;
        int i2 = i % 2;
        c k = k();
        int i3 = b + 17;
        e = i3 % 128;
        int i4 = i3 % 2;
        return k;
    }

    public b(Context context, e eVar, o.ei.c cVar) {
        super(context, eVar, cVar, o.bb.e.h);
    }

    public final void m() {
        int i = b + 57;
        e = i % 128;
        boolean z = i % 2 == 0;
        c();
        switch (z) {
            case true:
                int i2 = e + 47;
                b = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        int i3 = 63 / 0;
                        return;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    private c k() {
        c cVar = new c(this);
        int i = e + 69;
        b = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = e + 1;
        b = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                l(46 / View.resolveSizeAndState(1, 0, 0), "\u0007\t\uffdd\u000e\u000f\t\u0001\t￦\ufffe\b\ufffb\u0007", 37 % (ViewConfiguration.getScrollBarSize() * 29), 14610 - KeyEvent.getDeadChar(0, 1), true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                l(9 - View.resolveSizeAndState(0, 0, 0), "\u0007\t\uffdd\u000e\u000f\t\u0001\t￦\ufffe\b\ufffb\u0007", (ViewConfiguration.getScrollBarSize() >> 8) + 13, KeyEvent.getDeadChar(0, 0) + 204, true, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = e + 19;
        b = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bf\b$c.smali */
    static final class c extends o.y.c<b> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static long a;
        private static int b;
        private static char[] d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            b = 1;
            d = new char[]{27508, 19775, 10223, 6575, 62029, 54276, 48091, 40343, 63311, 51456, 8955, 1203, 24169, 45092, 35224, 58193, 50443, 7877, 28858, 19059, 44077, 34287, 57180, 12560, 2761};
            a = -4487273109822436735L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, int r7, short r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 3
                int r6 = 1 - r6
                int r7 = r7 + 102
                int r8 = r8 * 2
                int r8 = 4 - r8
                byte[] r0 = o.bf.b.c.$$d
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L33
            L19:
                r3 = r2
            L1a:
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                r4 = r0[r8]
                int r3 = r3 + 1
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L33:
                int r7 = -r7
                int r7 = r7 + r8
                int r8 = r9 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bf.b.c.B(byte, int, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{119, -13, -39, 23};
            $$e = 100;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = b + 77;
            e = i % 128;
            switch (i % 2 != 0) {
                case false:
                    break;
                default:
                    int i2 = 95 / 0;
                    break;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = e + 109;
            b = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = b + 5;
            e = i % 128;
            int i2 = i % 2;
            o.cf.d b2 = b(context);
            int i3 = e + 75;
            b = i3 % 128;
            int i4 = i3 % 2;
            return b2;
        }

        c(b bVar) {
            super(bVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = e + Opcodes.LMUL;
            b = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w((char) (TextUtils.lastIndexOf("", '0', 0) + 18386), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 6 - Gravity.getAbsoluteGravity(0, 0), objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = b + 89;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return intern;
                default:
                    int i4 = 5 / 0;
                    return intern;
            }
        }

        private static o.cf.d b(Context context) {
            Object[] objArr = new Object[1];
            w((char) (38694 - (ViewConfiguration.getEdgeSlop() >> 16)), AndroidCharacter.getMirror('0') - '*', (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 19, objArr);
            o.cf.d dVar = new o.cf.d(context, 9, ((String) objArr[0]).intern());
            int i = b + Opcodes.DDIV;
            e = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            int i = e + 89;
            b = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return bVar;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = e + Opcodes.DMUL;
            int i2 = i % 128;
            b = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.DSUB;
            e = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b + Opcodes.LUSHR;
            e = i % 128;
            switch (i % 2 != 0 ? (char) 3 : '.') {
                case '.':
                    return null;
                default:
                    int i2 = 27 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final void q() {
            int i = b + 49;
            e = i % 128;
            int i2 = i % 2;
            o.b.c.f(g());
            int i3 = b + 25;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(d dVar) {
            int i = b + Opcodes.LREM;
            e = i % 128;
            int i2 = i % 2;
            ((b) e()).j().c();
            int i3 = b + 91;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(d dVar) {
            int i = b + 19;
            e = i % 128;
            int i2 = i % 2;
            ((b) e()).j().c(dVar);
            int i3 = b + Opcodes.DMUL;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(char r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 734
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bf.b.c.w(char, int, int, java.lang.Object[]):void");
        }
    }

    private static void l(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        h hVar = new h();
        char[] cArr3 = new char[i2];
        hVar.a = 0;
        int i4 = $11 + Opcodes.LMUL;
        $10 = i4 % 128;
        int i5 = i4 % 2;
        while (hVar.a < i2) {
            hVar.b = cArr2[hVar.a];
            cArr3[hVar.a] = (char) (i3 + hVar.b);
            int i6 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr3[i6]), Integer.valueOf(c)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(13 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (char) TextUtils.getTrimmedLength(""), 459 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                    byte b2 = (byte) ($$e & 7);
                    byte b3 = (byte) (b2 - 1);
                    Object[] objArr3 = new Object[1];
                    o(b2, b3, b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr3[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) Color.alpha(0), TextUtils.getOffsetAfter("", 0) + 313);
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        o(b4, b5, b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            int i7 = $11 + 17;
            $10 = i7 % 128;
            int i8 = i7 % 2;
            hVar.c = i;
            char[] cArr4 = new char[i2];
            System.arraycopy(cArr3, 0, cArr4, 0, i2);
            System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
        }
        switch (!z) {
            case true:
                break;
            default:
                char[] cArr5 = new char[i2];
                hVar.a = 0;
                while (hVar.a < i2) {
                    int i9 = $10 + 57;
                    $11 = i9 % 128;
                    switch (i9 % 2 == 0 ? (char) 4 : '%') {
                        case 4:
                            cArr5[hVar.a] = cArr3[(i2 / hVar.a) * 0];
                            try {
                                Object[] objArr6 = {hVar, hVar};
                                Object obj3 = o.e.a.s.get(-1412673904);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getFadingEdgeLength() >> 16), (char) View.MeasureSpec.getSize(0), 313 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    o(b6, b7, b7, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr6);
                                break;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        default:
                            cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                            try {
                                Object[] objArr8 = {hVar, hVar};
                                Object obj4 = o.e.a.s.get(-1412673904);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 11, (char) Color.argb(0, 0, 0, 0), 312 - TextUtils.lastIndexOf("", '0'));
                                    byte b8 = (byte) 0;
                                    byte b9 = b8;
                                    Object[] objArr9 = new Object[1];
                                    o(b8, b9, b9, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                                break;
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                    }
                }
                cArr3 = cArr5;
                break;
        }
        objArr[0] = new String(cArr3);
    }
}
