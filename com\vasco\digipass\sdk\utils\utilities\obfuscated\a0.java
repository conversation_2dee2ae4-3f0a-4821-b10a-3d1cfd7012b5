package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a0.smali */
public class a0 extends IllegalStateException {
    private Throwable b;

    public a0(String str) {
        super(str);
    }

    @Override // java.lang.Throwable
    public Throwable getCause() {
        return this.b;
    }

    public a0(String str, Throwable th) {
        super(str);
        this.b = th;
    }
}
