package o.v;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.CancellationSignal;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import fr.antelop.sdk.digitalcard.VirtualCardNumberOption;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.ap.e;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\l.smali */
public final class l extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] l;
    private static int r;
    private static int t;
    private final boolean h;
    boolean i;
    private VirtualCardNumberOption k;
    private o.dw.e m;

    /* renamed from: o, reason: collision with root package name */
    private CancellationSignal f107o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        t = 1;
        t();
        int i = t + 63;
        r = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{96, 104, -93, 9};
        $$e = Opcodes.LOOKUPSWITCH;
    }

    static void t() {
        l = new char[]{50842, 50810, 50696, 50694, 50689, 50702, 50803, 50786, 50791, 50700, 50702, 50812, 50804, 50692, 50802, 50806, 50702, 50864, 50850, 50833, 50850, 50876, 50854, 50869, 50854, 50842, 50869, 50848, 50861, 50851, 50865, 50854, 50816, 50875, 50854, 50867, 50878, 50876, 50878, 50819, 50850, 50865, 50866, 50848, 50850, 50832, 50865, 50850, 50869, 50869, 50846, 50864, 50864, 50850, 50848, 50868, 50865, 50839, 50850, 50874, 50866, 50759, 51161, 51136, 51144, 51143, 51164, 51151, 51149, 51167, 51161, 50843, 50798, 50771, 50754, 50759, 50796, 50798, 50780, 50772, 50788, 50770, 50774, 50798, 50775, 50777, 50798, 50785, 50794, 50771, 50769, 50776, 50778, 50792, 50790};
    }

    private static void v(int i, short s, short s2, Object[] objArr) {
        int i2 = (s2 * 4) + 1;
        byte[] bArr = $$d;
        int i3 = i + 66;
        int i4 = s + 4;
        byte[] bArr2 = new byte[i2];
        int i5 = -1;
        int i6 = i2 - 1;
        if (bArr == null) {
            int i7 = (-i4) + i6;
            i6 = i6;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i4 = i4;
            i3 = i7;
        }
        while (true) {
            int i8 = i4 + 1;
            int i9 = i5 + 1;
            bArr2[i9] = (byte) i3;
            if (i9 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i6 = i6;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = i9;
            i4 = i8;
            i3 = (-bArr[i8]) + i3;
        }
    }

    static /* synthetic */ o.p.g a(l lVar) {
        int i = t + 93;
        r = i % 128;
        switch (i % 2 != 0 ? '8' : (char) 28) {
            case 28:
                o.p.g l2 = lVar.l();
                int i2 = t + 41;
                r = i2 % 128;
                int i3 = i2 % 2;
                return l2;
            default:
                lVar.l();
                throw null;
        }
    }

    static /* synthetic */ o.p.g b(l lVar) {
        int i = t + Opcodes.DDIV;
        r = i % 128;
        int i2 = i % 2;
        o.p.g l2 = lVar.l();
        int i3 = r + 37;
        t = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return l2;
        }
    }

    static /* synthetic */ void c(l lVar) {
        int i = r + 85;
        t = i % 128;
        int i2 = i % 2;
        lVar.n();
        int i3 = r + 33;
        t = i3 % 128;
        switch (i3 % 2 == 0 ? Typography.quote : (char) 29) {
            case 29:
                return;
            default:
                int i4 = 17 / 0;
                return;
        }
    }

    static /* synthetic */ o.p.g d(l lVar) {
        int i = r + 45;
        t = i % 128;
        switch (i % 2 == 0) {
            case false:
                return lVar.l();
            default:
                lVar.l();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g e(l lVar) {
        int i = t + Opcodes.DREM;
        r = i % 128;
        switch (i % 2 == 0) {
            case true:
                o.p.g l2 = lVar.l();
                int i2 = t + 47;
                r = i2 % 128;
                int i3 = i2 % 2;
                return l2;
            default:
                lVar.l();
                throw null;
        }
    }

    static /* synthetic */ o.p.g j(l lVar) {
        int i = t + Opcodes.DNEG;
        r = i % 128;
        int i2 = i % 2;
        o.p.g l2 = lVar.l();
        int i3 = r + 1;
        t = i3 % 128;
        int i4 = i3 % 2;
        return l2;
    }

    public l(String str, o.eo.e eVar, boolean z) {
        super(str, eVar);
        this.h = z;
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i = r + 53;
        t = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                u("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{0, 17, 88, 0}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                u("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{0, 17, 88, 0}, true, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = t + Opcodes.LNEG;
        r = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    @Override // o.p.h
    public final void a(final Context context, o.ei.c cVar, o.h.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, new int[]{17, 44, 10, 34}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u("\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001", new int[]{61, 10, Opcodes.IF_ICMPLT, 0}, true, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.ap.e(context, new e.c() { // from class: o.v.l.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int b;
            private static int e;
            private static int f;
            private static int h;
            private static short[] i;
            private static byte[] j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                f = 0;
                h = 1;
                j = new byte[]{40, 82, 38, -100, -125, 70, -115, ByteCompanionObject.MIN_VALUE, ByteCompanionObject.MIN_VALUE, 38, 82, 53, -77, -126, 42, -107, 83, 60, 97, 45, 83, -106, -106, 55, 34, 121, 45, -77, 45, 38, -126, 75, 97, -107, -125, 54, -126, 54, -121, 83, -103, 40, 75, 56, 38, 58, 56, 70, 26, 93, 35, 37, 73, 73, 43, 116, 75, 7, 109, 119, -25, 75, 37, -123, 56, 38, 58, 76, 117, 26, 90, 113, 59, -26, 20, -66, -26, -28, Tnaf.POW_2_WIDTH, 22, -17, 30, -33, -18, 20, -24, -24, 6, -29, -26, 50, -52, -30, 82, -26, 20, -76, 23, 17, 21, -21, -28, 53, -11, -32, 22, -112, -112, -112};
                b = 909053644;
                a = 1099058195;
                e = -1808046620;
            }

            static void init$0() {
                $$a = new byte[]{89, -101, -47, 112};
                $$b = 252;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0036). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void k(short r6, short r7, int r8, java.lang.Object[] r9) {
                /*
                    int r8 = r8 * 4
                    int r8 = r8 + 1
                    byte[] r0 = o.v.l.AnonymousClass4.$$a
                    int r7 = r7 * 2
                    int r7 = 110 - r7
                    int r6 = r6 * 4
                    int r6 = 3 - r6
                    byte[] r1 = new byte[r8]
                    int r8 = r8 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1b
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L36
                L1b:
                    r3 = r2
                L1c:
                    byte r4 = (byte) r7
                    r1[r3] = r4
                    int r6 = r6 + 1
                    if (r3 != r8) goto L2b
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2b:
                    r4 = r0[r6]
                    int r3 = r3 + 1
                    r5 = r9
                    r9 = r8
                    r8 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r5
                L36:
                    int r7 = r7 + r8
                    r8 = r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1c
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.l.AnonymousClass4.k(short, short, int, java.lang.Object[]):void");
            }

            @Override // o.ap.e.c
            public final void b(String str) {
                int i2 = h + 29;
                f = i2 % 128;
                int i3 = i2 % 2;
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                g((byte) (MotionEvent.axisFromString("") + 84), 1575720076 + (KeyEvent.getMaxKeyCode() >> 16), (short) ((-101) - View.MeasureSpec.getMode(0)), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 49, (-2007843386) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                g((byte) ((ViewConfiguration.getEdgeSlop() >> 16) + 96), 1575720118 - ExpandableListView.getPackedPositionChild(0L), (short) ((-72) - (ViewConfiguration.getJumpTapTimeout() >> 16)), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 62, (-2007843344) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                switch (l.this.i ? 'X' : (char) 17) {
                    case Opcodes.POP2 /* 88 */:
                        switch (str != null) {
                            case true:
                                l.this.a(context, str);
                                break;
                        }
                }
                switch (l.d(l.this) != null) {
                    case true:
                        int i4 = h + 45;
                        f = i4 % 128;
                        int i5 = i4 % 2;
                        l.b(l.this).onProcessSuccess();
                        break;
                }
            }

            @Override // o.ap.e.c
            public final void b(o.bb.d dVar2) {
                o.bv.c c = o.bv.c.c(dVar2);
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                g((byte) (AndroidCharacter.getMirror('0') + '#'), 1575720076 + (ViewConfiguration.getDoubleTapTimeout() >> 16), (short) ((-101) - (ViewConfiguration.getEdgeSlop() >> 16)), (-48) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (-2007843386) + View.resolveSizeAndState(0, 0, 0), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                g((byte) ((-118) - TextUtils.getOffsetBefore("", 0)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 1575720149, (short) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 2), Drawable.resolveOpacity(0, 0) - 58, (-2007843346) - ((byte) KeyEvent.getModifierMetaStateMask()), objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(c).toString());
                switch (l.e(l.this) != null) {
                    case true:
                        int i2 = h + 53;
                        f = i2 % 128;
                        int i3 = i2 % 2;
                        switch (dVar2.d() != o.bb.a.aA) {
                            case false:
                                int i4 = f + 57;
                                h = i4 % 128;
                                int i5 = i4 % 2;
                                l.c(l.this);
                                l.a(l.this).onAuthenticationDeclined();
                                break;
                            default:
                                l.j(l.this).onError(o.bv.c.c(dVar2));
                                break;
                        }
                }
            }

            private static void g(byte b2, int i2, short s, int i3, int i4, Object[] objArr3) {
                boolean z;
                int i5;
                boolean z2;
                o.a.f fVar = new o.a.f();
                StringBuilder sb = new StringBuilder();
                int i6 = 2;
                try {
                    Object[] objArr4 = {Integer.valueOf(i3), Integer.valueOf(b)};
                    int i7 = -2120899312;
                    Object obj = o.e.a.s.get(-2120899312);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(11 - TextUtils.indexOf("", ""), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), Process.getGidForName("") + 66);
                        byte b3 = (byte) 0;
                        byte b4 = (byte) (b3 + 1);
                        Object[] objArr5 = new Object[1];
                        k(b3, b4, (byte) (b4 - 1), objArr5);
                        obj = cls.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                        o.e.a.s.put(-2120899312, obj);
                    }
                    Object obj2 = null;
                    int intValue = ((Integer) ((Method) obj).invoke(null, objArr4)).intValue();
                    switch (intValue == -1 ? '[' : Typography.dollar) {
                        case '$':
                            z = false;
                            break;
                        default:
                            z = true;
                            break;
                    }
                    char c = '0';
                    if (z) {
                        byte[] bArr = j;
                        switch (bArr != null ? (char) 11 : (char) 19) {
                            default:
                                int length = bArr.length;
                                byte[] bArr2 = new byte[length];
                                int i8 = 0;
                                while (true) {
                                    switch (i8 < length ? '*' : Typography.dollar) {
                                        case '$':
                                            bArr = bArr2;
                                        default:
                                            try {
                                                Object[] objArr6 = {Integer.valueOf(bArr[i8])};
                                                Object obj3 = o.e.a.s.get(494867332);
                                                if (obj3 == null) {
                                                    Class cls2 = (Class) o.e.a.c(19 - TextUtils.indexOf("", "", 0), (char) (16425 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), View.MeasureSpec.getSize(0) + Opcodes.FCMPG);
                                                    byte b5 = (byte) 0;
                                                    byte b6 = b5;
                                                    Object[] objArr7 = new Object[1];
                                                    k(b5, b6, b6, objArr7);
                                                    obj3 = cls2.getMethod((String) objArr7[0], Integer.TYPE);
                                                    o.e.a.s.put(494867332, obj3);
                                                }
                                                bArr2[i8] = ((Byte) ((Method) obj3).invoke(null, objArr6)).byteValue();
                                                i8++;
                                                i6 = 2;
                                                i7 = -2120899312;
                                                obj2 = null;
                                                c = '0';
                                            } catch (Throwable th) {
                                                Throwable cause = th.getCause();
                                                if (cause == null) {
                                                    throw th;
                                                }
                                                throw cause;
                                            }
                                    }
                                }
                            case 19:
                                if (bArr == null) {
                                    intValue = (short) (((short) (i[i2 + ((int) (e ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                                    break;
                                } else {
                                    byte[] bArr3 = j;
                                    try {
                                        Object[] objArr8 = new Object[i6];
                                        objArr8[1] = Integer.valueOf(e);
                                        objArr8[0] = Integer.valueOf(i2);
                                        Object obj4 = o.e.a.s.get(Integer.valueOf(i7));
                                        if (obj4 == null) {
                                            Class cls3 = (Class) o.e.a.c(10 - TextUtils.lastIndexOf("", c, 0), (char) (ImageFormat.getBitsPerPixel(0) + 1), 64 - TextUtils.lastIndexOf("", c, 0, 0));
                                            byte b7 = (byte) 0;
                                            byte b8 = (byte) (b7 + 1);
                                            Object[] objArr9 = new Object[1];
                                            k(b7, b8, (byte) (b8 - 1), objArr9);
                                            String str = (String) objArr9[0];
                                            Class<?>[] clsArr = new Class[i6];
                                            clsArr[0] = Integer.TYPE;
                                            clsArr[1] = Integer.TYPE;
                                            obj4 = cls3.getMethod(str, clsArr);
                                            o.e.a.s.put(Integer.valueOf(i7), obj4);
                                        }
                                        intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj4).invoke(obj2, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                }
                        }
                    }
                    if (intValue > 0) {
                        int i9 = ((i2 + intValue) - 2) + ((int) (e ^ (-5810760824076169584L)));
                        if (z) {
                            i5 = 1;
                        } else {
                            int i10 = $11 + 61;
                            $10 = i10 % 128;
                            int i11 = i10 % 2;
                            i5 = 0;
                        }
                        fVar.d = i9 + i5;
                        try {
                            Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(a), sb};
                            Object obj5 = o.e.a.s.get(160906762);
                            if (obj5 == null) {
                                obj5 = ((Class) o.e.a.c((ViewConfiguration.getJumpTapTimeout() >> 16) + 11, (char) View.resolveSizeAndState(0, 0, 0), AndroidCharacter.getMirror('0') + 555)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                                o.e.a.s.put(160906762, obj5);
                            }
                            ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                            fVar.b = fVar.e;
                            byte[] bArr4 = j;
                            if (bArr4 != null) {
                                int length2 = bArr4.length;
                                byte[] bArr5 = new byte[length2];
                                for (int i12 = 0; i12 < length2; i12++) {
                                    bArr5[i12] = (byte) (bArr4[i12] ^ (-5810760824076169584L));
                                }
                                bArr4 = bArr5;
                            }
                            if (bArr4 != null) {
                                int i13 = $11 + Opcodes.DMUL;
                                $10 = i13 % 128;
                                z2 = i13 % 2 == 0;
                            } else {
                                z2 = false;
                            }
                            fVar.c = 1;
                            while (fVar.c < intValue) {
                                switch (z2) {
                                    case true:
                                        int i14 = $11 + 5;
                                        $10 = i14 % 128;
                                        int i15 = i14 % 2;
                                        byte[] bArr6 = j;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                        break;
                                    default:
                                        short[] sArr = i;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                        break;
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                            }
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr3[0] = sb.toString();
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
        }, cVar).c(dVar, o(), ((d) this).n.e(), this.k, this.i);
        int i = t + 73;
        r = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                int i2 = 93 / 0;
                return;
        }
    }

    final void a(Context context, String str) {
        o.dw.b bVar = new o.dw.b() { // from class: o.v.l.2
            private static int a = 0;
            private static int b = 1;

            @Override // o.dw.b
            public final int getThemeResource(o.dw.e eVar) {
                int i = b;
                int i2 = ((i | 81) << 1) - (i ^ 81);
                a = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        o.dw.e.a();
                        int i3 = R.style.antelopSecureVirtualCardNumberDisplayThemeInternal;
                        int i4 = b + 63;
                        a = i4 % 128;
                        switch (i4 % 2 != 0 ? (char) 24 : '_') {
                            case Opcodes.SWAP /* 95 */:
                                return i3;
                            default:
                                int i5 = 19 / 0;
                                return i3;
                        }
                    default:
                        o.dw.e.a();
                        int i6 = R.style.antelopSecureVirtualCardNumberDisplayThemeInternal;
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }
        };
        this.f107o = new CancellationSignal();
        bVar.setProcessCallback(l());
        bVar.launch2(context, this.m, str, this.f107o);
        int i = r + 51;
        t = i % 128;
        switch (i % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = r;
        int i2 = i + 21;
        t = i2 % 128;
        int i3 = i2 % 2;
        if (this.h) {
            int i4 = i + 31;
            t = i4 % 128;
            int i5 = i4 % 2;
        } else {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            u("\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{71, 24, 56, 20}, false, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
    }

    public final void c(Context context, VirtualCardNumberOption virtualCardNumberOption, boolean z, o.dw.e eVar, o.p.g gVar) throws WalletValidationException {
        int i = t + 59;
        r = i % 128;
        switch (i % 2 != 0) {
            case false:
                virtualCardNumberOption.validate();
                this.k = virtualCardNumberOption;
                this.i = z;
                this.m = eVar;
                d(context, gVar);
                int i2 = t + 79;
                r = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            default:
                virtualCardNumberOption.validate();
                this.k = virtualCardNumberOption;
                this.i = z;
                this.m = eVar;
                d(context, gVar);
                throw null;
        }
    }

    public final fr.antelop.sdk.CancellationSignal a() {
        int i = r + 7;
        int i2 = i % 128;
        t = i2;
        int i3 = i % 2;
        CancellationSignal cancellationSignal = this.f107o;
        switch (cancellationSignal != null) {
            case false:
                break;
            default:
                int i4 = i2 + 21;
                r = i4 % 128;
                int i5 = i4 % 2;
                switch (cancellationSignal.isCanceled() ? false : true) {
                    case true:
                        return new fr.antelop.sdk.CancellationSignal(this.f107o);
                }
        }
        int i6 = t + 91;
        r = i6 % 128;
        int i7 = i6 % 2;
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:38:0x0106  */
    /* JADX WARN: Removed duplicated region for block: B:65:0x0194  */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v19, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 774
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.l.u(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
