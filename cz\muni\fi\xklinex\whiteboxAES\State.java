package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\State.smali */
public class State implements Serializable {
    public static final int BYTES = 16;
    public static final int COLS = 4;
    public static final int ROWS = 4;
    private static final long serialVersionUID = 985187931620550251L;
    protected final boolean immutable = false;
    protected byte[] state;

    public State() {
        init();
    }

    public static void copy(State state, State state2) {
        state2.setState(state2.getState(), true);
    }

    public static int getCIdx(int i, int i2) {
        return (i2 * 4) + i;
    }

    public static int getTIdx(int i) {
        return getCIdx(i / 4, i % 4);
    }

    public static int transpose(int i) {
        return (i / 4) + ((i % 4) * 4);
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.equals(this.state, ((State) obj).state);
        }
        return false;
    }

    public byte get(int i) {
        if (i < 0 || i >= 16) {
            throw new IllegalArgumentException("Invalid byte requested");
        }
        return this.state[i];
    }

    public byte[] getState() {
        return this.state;
    }

    public byte getT(int i) {
        return get(getTIdx(i));
    }

    public int hashCode() {
        return Arrays.hashCode(this.state) + 413;
    }

    public final void init() {
        this.state = new byte[16];
    }

    public void loadFrom(State state) {
        System.arraycopy(state.getState(), 0, this.state, 0, 16);
    }

    public void set(byte b, int i) {
        if (i < 0 || i >= 16) {
            throw new IllegalArgumentException("Invalid byte requested");
        }
        byte[] bArr = this.state;
        if (bArr == null) {
            throw new NullPointerException("State is not initialized");
        }
        bArr[i] = b;
    }

    public void setColumn(W32b w32b, int i) {
        byte[] bArr = w32b.get();
        byte[] bArr2 = this.state;
        bArr2[i] = bArr[0];
        bArr2[i + 4] = bArr[1];
        bArr2[i + 8] = bArr[2];
        bArr2[i + 12] = bArr[3];
    }

    public void setState(byte[] bArr, boolean z) {
        if (bArr.length != 16) {
            throw new IllegalArgumentException("XOR table has to have 8 sub-tables");
        }
        if (z) {
            this.state = Arrays.copyOf(bArr, 16);
        } else {
            this.state = bArr;
        }
    }

    public String toString() {
        if (this.state == null) {
            return "State{state=null}";
        }
        StringBuilder sb = new StringBuilder();
        int length = this.state.length;
        int i = 0;
        while (i < length) {
            sb.append(String.format("0x%02X", Integer.valueOf(this.state[i] & 255)));
            i++;
            if (i != length) {
                sb.append(", ");
            }
        }
        return "State{state=" + ((Object) sb) + ";mem=" + Arrays.toString(this.state) + "}";
    }

    public State transpose() {
        byte[] bArr = new byte[16];
        for (int i = 0; i < 16; i++) {
            bArr[i] = getT(i);
        }
        this.state = bArr;
        return this;
    }

    public State(byte[] bArr, boolean z, boolean z2) {
        if (z2) {
            init();
            for (int i = 0; i < 16; i++) {
                this.state[i] = bArr[transpose(i)];
            }
            return;
        }
        if (z) {
            this.state = Arrays.copyOf(bArr, 16);
        } else {
            this.state = bArr;
        }
    }
}
