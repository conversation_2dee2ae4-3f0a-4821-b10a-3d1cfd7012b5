package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.security.keystore.UserNotAuthenticatedException;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.securestorage.R;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.BiometricWriteProtectionSettings;
import java.util.concurrent.Executor;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\c.smali */
public abstract class c {
    public static void a(String filename, FragmentActivity fragmentActivity, boolean z, byte[] secureEncryptionKey, s biometricSigningCompletedCallback) {
        Intrinsics.checkNotNullParameter(filename, "filename");
        Intrinsics.checkNotNullParameter(fragmentActivity, "fragmentActivity");
        Intrinsics.checkNotNullParameter(secureEncryptionKey, "secureEncryptionKey");
        Intrinsics.checkNotNullParameter(biometricSigningCompletedCallback, "biometricSigningCompletedCallback");
        SecretKey b = e.b(e.a(e.a(), e.k, filename));
        if (b == null) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.BIOMETRIC_KEY_NOT_FOUND, null, 2, null);
        }
        a(fragmentActivity, z, secureEncryptionKey, b, biometricSigningCompletedCallback);
    }

    public static void a(String filename, byte[] secureEncryptionKey, BiometricWriteProtectionSettings biometricWriteProtectionSettings, p biometricSigningCompletedCallback) {
        Intrinsics.checkNotNullParameter(filename, "filename");
        Intrinsics.checkNotNullParameter(secureEncryptionKey, "secureEncryptionKey");
        Intrinsics.checkNotNullParameter(biometricWriteProtectionSettings, "biometricWriteProtectionSettings");
        Intrinsics.checkNotNullParameter(biometricSigningCompletedCallback, "biometricSigningCompletedCallback");
        String a = e.a(e.a(), e.k, filename);
        SecretKey b = e.b(a);
        if (b == null) {
            b = e.a(a, biometricWriteProtectionSettings.getTimeout(), biometricWriteProtectionSettings.getFragmentActivity());
        }
        a(biometricWriteProtectionSettings.getFragmentActivity(), biometricWriteProtectionSettings.getFallbackToDeviceCredential(), secureEncryptionKey, b, biometricSigningCompletedCallback);
    }

    public static void a(FragmentActivity context, boolean z, byte[] bArr, SecretKey secretKey, d dVar) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKey);
            byte[] signedStorageEncryptionKey = mac.doFinal(bArr);
            Intrinsics.checkNotNullExpressionValue(signedStorageEncryptionKey, "signedStorageEncryptionKey");
            dVar.a(signedStorageEncryptionKey);
        } catch (UserNotAuthenticatedException e) {
            b callback = new b(dVar, secretKey, bArr);
            Intrinsics.checkNotNullParameter(context, "activity");
            Intrinsics.checkNotNullParameter(callback, "callback");
            Intrinsics.checkNotNullParameter(context, "context");
            BiometricPrompt.PromptInfo.Builder subtitle = new BiometricPrompt.PromptInfo.Builder().setTitle(context.getString(R.string.biometric_prompt_title_text)).setSubtitle(context.getString(R.string.biometric_prompt_subtitle_text));
            Intrinsics.checkNotNullExpressionValue(subtitle, "Builder()\n            .s…ic_prompt_subtitle_text))");
            if (z) {
                subtitle.setAllowedAuthenticators(32783);
            } else {
                subtitle.setAllowedAuthenticators(15).setNegativeButtonText(context.getString(R.string.biometric_prompt_cancel));
            }
            BiometricPrompt.PromptInfo build = subtitle.build();
            Intrinsics.checkNotNullExpressionValue(build, "prompt.build()");
            Executor mainExecutor = ContextCompat.getMainExecutor(context);
            Intrinsics.checkNotNullExpressionValue(mainExecutor, "getMainExecutor(activity)");
            new BiometricPrompt(context, mainExecutor, callback).authenticate(build);
        }
    }
}
