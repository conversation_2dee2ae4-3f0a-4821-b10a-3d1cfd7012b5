package com.google.android.gms.tapandpay.issuer;

import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeRequestCallbacks.smali */
public interface IPushTokenizeRequestCallbacks extends IInterface {

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeRequestCallbacks$Stub.smali */
    public static abstract class Stub extends com.google.android.gms.internal.tapandpay.zzb implements IPushTokenizeRequestCallbacks {

        /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeRequestCallbacks$Stub$Proxy.smali */
        public static class Proxy extends com.google.android.gms.internal.tapandpay.zza implements IPushTokenizeRequestCallbacks {
            Proxy(IBinder iBinder) {
                super(iBinder, "com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks");
            }

            @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks
            public void generatePaymentCredentials(GetPaymentCredentialsRequest request, IPushTokenizeResponseCallbacks callback) throws RemoteException {
                Parcel zza = zza();
                com.google.android.gms.internal.tapandpay.zzc.zzc(zza, request);
                com.google.android.gms.internal.tapandpay.zzc.zzd(zza, callback);
                zzc(3, zza);
            }

            @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks
            public void isWalletAvailable(String walletId, IPushTokenizeResponseCallbacks callback) throws RemoteException {
                Parcel zza = zza();
                zza.writeString(walletId);
                com.google.android.gms.internal.tapandpay.zzc.zzd(zza, callback);
                zzc(2, zza);
            }
        }

        public Stub() {
            super("com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks");
        }

        public static IPushTokenizeRequestCallbacks asInterface(IBinder obj) {
            if (obj == null) {
                return null;
            }
            IInterface queryLocalInterface = obj.queryLocalInterface("com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks");
            return queryLocalInterface instanceof IPushTokenizeRequestCallbacks ? (IPushTokenizeRequestCallbacks) queryLocalInterface : new Proxy(obj);
        }

        @Override // com.google.android.gms.internal.tapandpay.zzb
        protected boolean dispatchTransaction(int code, Parcel data, Parcel parcel, int i) throws RemoteException {
            switch (code) {
                case 2:
                    String readString = data.readString();
                    IPushTokenizeResponseCallbacks asInterface = IPushTokenizeResponseCallbacks.Stub.asInterface(data.readStrongBinder());
                    com.google.android.gms.internal.tapandpay.zzc.zzb(data);
                    isWalletAvailable(readString, asInterface);
                    return true;
                case 3:
                    GetPaymentCredentialsRequest getPaymentCredentialsRequest = (GetPaymentCredentialsRequest) com.google.android.gms.internal.tapandpay.zzc.zza(data, GetPaymentCredentialsRequest.CREATOR);
                    IPushTokenizeResponseCallbacks asInterface2 = IPushTokenizeResponseCallbacks.Stub.asInterface(data.readStrongBinder());
                    com.google.android.gms.internal.tapandpay.zzc.zzb(data);
                    generatePaymentCredentials(getPaymentCredentialsRequest, asInterface2);
                    return true;
                default:
                    return false;
            }
        }
    }

    void generatePaymentCredentials(GetPaymentCredentialsRequest getPaymentCredentialsRequest, IPushTokenizeResponseCallbacks iPushTokenizeResponseCallbacks) throws RemoteException;

    void isWalletAvailable(String str, IPushTokenizeResponseCallbacks iPushTokenizeResponseCallbacks) throws RemoteException;
}
