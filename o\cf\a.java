package o.cf;

import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.l;
import o.a.m;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.Primes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char A;
    private static char[] B;
    private static int C;
    public static final a a;
    public static final a b;
    public static final a c;
    public static final a d;
    public static final a e;
    public static final a f;
    public static final a g;
    public static final a h;
    public static final a i;
    public static final a j;
    public static final a k;
    public static final a l;
    public static final a m;
    public static final a n;

    /* renamed from: o, reason: collision with root package name */
    public static final a f47o;
    public static final a p;
    public static final a q;
    public static final a r;
    public static final a s;
    public static final a t;
    private static final /* synthetic */ a[] u;
    public static final a v;
    public static final a w;
    public static final a x;
    private static char[] y;
    private static int z;

    private static void F(byte b2, byte b3, int i2, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = 1 - (b3 * 2);
        int i4 = 3 - (b2 * 4);
        int i5 = 122 - i2;
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            i5 = i7 + i5;
            i7 = i7;
        }
        while (true) {
            i4++;
            i6++;
            bArr2[i6] = (byte) i5;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i8 = i5;
            int i9 = i7;
            i5 = i8 + bArr[i4];
            i7 = i9;
        }
    }

    static void c() {
        y = new char[]{30511, 30573, 30531, 30571, 30537, 30572, 30541, 30584, 30568, 30549, 30505, 30510, 30552, 30540, 30542, 30560, 30590, 30517, 30589, 30556, 30563, 30586, 30581, 30504, 30570, 30591, 30574, 30567, 30533, 30566, 30534, 30538, 30585, 30553, 30561, 30559, 30557, 30587, 30564, 30548, 30583, 30588, 30582, 30529, 30506, 30555, 30554, 30507, 30562};
        A = (char) 17042;
        B = new char[]{50935, 50878, 50849, 50856, 50858, 50854, 50858, 50842, 50832, 50838, 50843, 50857, 50852, 50854, 50858, 50856, 50841, 50833, 50876, 50835, 50861, 50854, 50849, 50849, 50852, 50858, 50852, 50852, 50837, 50832, 50851, 50877, 50878, 50872, 50849, 50836, 50849, 50711, 50704, 50704, 50704, 50715, 50712, 50709, 50715, 50709, 50697, 50800, 50690, 50713, 50711, 50704, 50712, 50696, 50696, 50712, 50704, 50714, 50715, 50704, 50696, 50702, 50932, 50877, 50877, 50879, 50838, 50840, 50849, 50877, 50851, 50850, 50876, 50849, 50838, 50860, 50879, 50873, 50835, 50854, 50727, 50725, 50721, 50742, 50751, 50741, 51149, 50737, 50743, 50737, 50938, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50847, 50859, 50855, 50859, 50854, 50879, 50838, 50838, 50852, 50853, 50848, 50860, 50927, 50836, 50851, 50879, 50875, 50857, 50837, 50848, 50879, 50848, 50853, 50863, 50840, 50833, 50877, 50851, 50834, 50838, 50849, 50876, 50850, 50851, 50877, 50849, 50878, 50728, 50730, 50734, 50689, 50717, 50728, 50724, 50720, 50712, 50694, 50710, 50732, 50721, 50726, 50724, 50734, 50710, 50716, 50690, 50729, 50724, 50729, 50706, 50708, 50689, 50718, 50730, 50728, 50715, 50719, 50734, 50725, 50935, 50876, 50852, 50857, 50859, 50859, 50853, 50838, 50843, 50852, 50852, 50853, 50858, 50863, 50844, 50932, 50848, 50851, 50875, 50835, 50840, 50859, 50857, 50858, 50837, 50846, 50856, 50855, 50853, 50857, 50847, 50837, 50877, 50833, 50838, 50876, 50849, 50859, 50937, 50856, 50856, 50855, 50855, 50851, 50875, 50863, 50832, 50876, 50878, 50833, 50837, 50855, 50853, 50943, 50857, 50852, 50836, 50837, 50851, 50849, 50851, 50855, 50854, 50850, 50851, 50854, 50847, 50847, 50857, 50854, 50848, 50849, 50873, 50835, 50843, 50855, 50854, 50854, 50851, 50849, 50851, 50851, 50850, 50879, 50877, 50860, 50840, 50801, 50712, 50709, 50708, 50712, 50713, 50709, 50715, 50709, 50703, 50702, 50718, 50691, 50697, 50694, 50711, 50705, 50708, 50709, 50709, 50715, 50709, 50712, 50712, 50713, 50702, 50698, 50718, 50716, 50719, 50691, 50844, 50795, 50789, 50792, 50792, 50793, 50755, 50781, 50788, 50792, 50768, 50769, 50789, 50796, 50771, 50795, 50771, 50753, 50753, 50792, 50789, 50788, 50792, 50793, 50789, 50795, 50789, 50783, 50782, 50798, 50771, 50777, 50774, 50791, 50785, 50788, 50789};
    }

    static void init$0() {
        $$a = new byte[]{84, 72, 115, -24};
        $$b = 236;
    }

    private a(String str, int i2) {
    }

    private static /* synthetic */ a[] e() {
        int i2 = C + 29;
        int i3 = i2 % 128;
        z = i3;
        int i4 = i2 % 2;
        a[] aVarArr = {a, d, e, c, b, j, h, i, f, g, f47o, l, k, n, m, q, t, p, r, s, w, v, x};
        int i5 = i3 + 49;
        C = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                throw null;
            default:
                return aVarArr;
        }
    }

    public static a valueOf(String str) {
        int i2 = z + 23;
        C = i2 % 128;
        int i3 = i2 % 2;
        a aVar = (a) Enum.valueOf(a.class, str);
        int i4 = C + 23;
        z = i4 % 128;
        switch (i4 % 2 != 0 ? 'W' : (char) 0) {
            case Opcodes.POP /* 87 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return aVar;
        }
    }

    public static a[] values() {
        int i2 = z + Opcodes.LSHR;
        C = i2 % 128;
        int i3 = i2 % 2;
        a[] aVarArr = (a[]) u.clone();
        int i4 = z + 47;
        C = i4 % 128;
        int i5 = i4 % 2;
        return aVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        z = 0;
        C = 1;
        c();
        Object[] objArr = new Object[1];
        D(19 - (ViewConfiguration.getScrollDefaultDelay() >> 16), "-\u0016#\t\u0010\u0013$-\u0010$\u0012\u001c\u0016!\u0013\u001b\u0006\u000f㘝", (byte) (31 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), objArr);
        a = new a(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        E("\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{0, 36, 0, 0}, true, objArr2);
        d = new a(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        E("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000", new int[]{36, 26, Opcodes.FMUL, 17}, false, objArr3);
        e = new a(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        D(25 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "&\u0016\u000e\u0017\u001b&&\u001e!\u0006\u000e.\u0017'$\u0016 \u0006\u0016!\u000e\u001b\u0011\u0019", (byte) (24 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), objArr4);
        c = new a(((String) objArr4[0]).intern(), 3);
        Object[] objArr5 = new Object[1];
        D(((Process.getThreadPriority(0) + 20) >> 6) + 26, "&\u0016\u000e\u0017\u001b&'\u0002\u001e\u001c\u001b\u0013$\u001d\u0017($\u0016 \u0006\u0016!\u000e\u001b\u0011\u0019", (byte) (18 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), objArr5);
        b = new a(((String) objArr5[0]).intern(), 4);
        Object[] objArr6 = new Object[1];
        D(36 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), "\u0011\u001a\u0000\u001a\u0011\u0019\b\u0014㙅㙅\u001a\u0003$\u001e\u0014\u001d\b\u0014\u001e)\u001a&(\u0010\u0017&\u0016\u001a\u0005\u0019\"\u000f\u0019\u000e㙐", (byte) (TextUtils.lastIndexOf("", '0') + 82), objArr6);
        j = new a(((String) objArr6[0]).intern(), 5);
        Object[] objArr7 = new Object[1];
        E("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000", new int[]{62, 17, 0, 17}, true, objArr7);
        h = new a(((String) objArr7[0]).intern(), 6);
        Object[] objArr8 = new Object[1];
        E("\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001", new int[]{79, 11, Opcodes.D2I, 0}, false, objArr8);
        i = new a(((String) objArr8[0]).intern(), 7);
        Object[] objArr9 = new Object[1];
        D(TextUtils.getCapsMode("", 0, 0) + 13, "\"-\u0017'+#\u001b&\u0016\u0012)0㘖", (byte) (22 - TextUtils.lastIndexOf("", '0')), objArr9);
        f = new a(((String) objArr9[0]).intern(), 8);
        Object[] objArr10 = new Object[1];
        D(24 - TextUtils.lastIndexOf("", '0'), "\u001f\u001c!\u0019\u000f\"\u0001&\u001b&\u0016\u0012)0\u0015\u001f$\u0014 \u0006\u0010\u0013/\u001b㘅", (byte) (23 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), objArr10);
        g = new a(((String) objArr10[0]).intern(), 9);
        Object[] objArr11 = new Object[1];
        E("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001", new int[]{90, 26, 0, 21}, true, objArr11);
        f47o = new a(((String) objArr11[0]).intern(), 10);
        Object[] objArr12 = new Object[1];
        D((ViewConfiguration.getWindowTouchSlop() >> 8) + 21, " \u0011\u0013\u0010\u000f'\u0017&\u0019\u000e\u001f\u001b\u0004\u0000/\u000e\u0019\u0011\u001f\u0019㘞", (byte) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 53), objArr12);
        l = new a(((String) objArr12[0]).intern(), 11);
        Object[] objArr13 = new Object[1];
        E("\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{Opcodes.INEG, 24, 0, 0}, false, objArr13);
        k = new a(((String) objArr13[0]).intern(), 12);
        Object[] objArr14 = new Object[1];
        E("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{Opcodes.F2L, 33, Opcodes.DNEG, 4}, false, objArr14);
        n = new a(((String) objArr14[0]).intern(), 13);
        Object[] objArr15 = new Object[1];
        D(TextUtils.getOffsetBefore("", 0) + 21, "\u0003\u001f\u0006\u001a\u001c\u001e\f!\u001b\r!\f-\u001f\u001f\u0006 \u0011\u0013\u0010㗵", (byte) (13 - View.MeasureSpec.getMode(0)), objArr15);
        m = new a(((String) objArr15[0]).intern(), 14);
        Object[] objArr16 = new Object[1];
        E("\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{Opcodes.LRETURN, 15, 0, 0}, true, objArr16);
        q = new a(((String) objArr16[0]).intern(), 15);
        Object[] objArr17 = new Object[1];
        E("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{188, 23, 0, 15}, false, objArr17);
        t = new a(((String) objArr17[0]).intern(), 16);
        Object[] objArr18 = new Object[1];
        E("\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{Primes.SMALL_FACTOR_LIMIT, 15, 0, 11}, false, objArr18);
        p = new a(((String) objArr18[0]).intern(), 17);
        Object[] objArr19 = new Object[1];
        D(MotionEvent.axisFromString("") + 31, "\u0013!㘎㘎\u0017&\u0001\u0010\u0003(\u001f\n\u0014\u001c\n&\u000f\"\u000f$\b$\u000f\"#,\u0001\u0010#&", (byte) (TextUtils.indexOf((CharSequence) "", '0', 0) + 25), objArr19);
        r = new a(((String) objArr19[0]).intern(), 18);
        Object[] objArr20 = new Object[1];
        D(33 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "'\u000e\u0012\u001d\"$$\u0016\u001c\u001e\u001d\r\u001f\u001c!\u0019\u000f\"\u0000\u0011\u0002(\u001e!\u0017($\u0016)\u0014\u0011\u0001㘬", (byte) (ExpandableListView.getPackedPositionChild(0L) + 46), objArr20);
        s = new a(((String) objArr20[0]).intern(), 19);
        Object[] objArr21 = new Object[1];
        E("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{226, 33, 0, 0}, true, objArr21);
        w = new a(((String) objArr21[0]).intern(), 20);
        Object[] objArr22 = new Object[1];
        E("\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001", new int[]{259, 32, Opcodes.FMUL, 14}, false, objArr22);
        v = new a(((String) objArr22[0]).intern(), 21);
        Object[] objArr23 = new Object[1];
        E("\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000", new int[]{291, 37, 58, 31}, false, objArr23);
        x = new a(((String) objArr23[0]).intern(), 22);
        u = e();
        int i2 = C + 59;
        z = i2 % 128;
        int i3 = i2 % 2;
    }

    /* renamed from: o.cf.a$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\a$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            e = 0;
            a = 1;
            int[] iArr = new int[a.values().length];
            d = iArr;
            try {
                iArr[a.a.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[a.d.ordinal()] = 2;
                int i = a;
                int i2 = (i ^ Opcodes.LSHR) + ((i & Opcodes.LSHR) << 1);
                e = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[a.e.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[a.c.ordinal()] = 4;
                int i3 = (a + Opcodes.INEG) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                d[a.b.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                d[a.j.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                d[a.h.ordinal()] = 7;
                int i5 = a;
                int i6 = (i5 ^ 39) + ((i5 & 39) << 1);
                e = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e8) {
            }
            try {
                d[a.i.ordinal()] = 8;
            } catch (NoSuchFieldError e9) {
            }
            try {
                d[a.f.ordinal()] = 9;
                int i8 = a;
                int i9 = (i8 ^ 67) + ((i8 & 67) << 1);
                e = i9 % 128;
                if (i9 % 2 != 0) {
                }
            } catch (NoSuchFieldError e10) {
            }
            try {
                d[a.g.ordinal()] = 10;
            } catch (NoSuchFieldError e11) {
            }
            try {
                d[a.f47o.ordinal()] = 11;
            } catch (NoSuchFieldError e12) {
            }
            try {
                d[a.l.ordinal()] = 12;
            } catch (NoSuchFieldError e13) {
            }
            try {
                d[a.k.ordinal()] = 13;
            } catch (NoSuchFieldError e14) {
            }
            try {
                d[a.n.ordinal()] = 14;
            } catch (NoSuchFieldError e15) {
            }
            try {
                d[a.m.ordinal()] = 15;
            } catch (NoSuchFieldError e16) {
            }
            try {
                d[a.q.ordinal()] = 16;
            } catch (NoSuchFieldError e17) {
            }
            try {
                d[a.t.ordinal()] = 17;
            } catch (NoSuchFieldError e18) {
            }
            try {
                d[a.p.ordinal()] = 18;
            } catch (NoSuchFieldError e19) {
            }
            try {
                d[a.r.ordinal()] = 19;
                int i10 = (e + 64) - 1;
                a = i10 % 128;
                if (i10 % 2 == 0) {
                }
            } catch (NoSuchFieldError e20) {
            }
            try {
                d[a.s.ordinal()] = 20;
            } catch (NoSuchFieldError e21) {
            }
            try {
                d[a.w.ordinal()] = 21;
            } catch (NoSuchFieldError e22) {
            }
            try {
                d[a.v.ordinal()] = 22;
            } catch (NoSuchFieldError e23) {
            }
            try {
                d[a.x.ordinal()] = 23;
            } catch (NoSuchFieldError e24) {
            }
        }
    }

    public final o.bb.a d() {
        int i2 = z + 97;
        C = i2 % 128;
        int i3 = i2 % 2;
        switch (AnonymousClass2.d[ordinal()]) {
            case 1:
                return o.bb.a.j;
            case 2:
            case 3:
            case 4:
            case 5:
                return o.bb.a.c;
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
                return o.bb.a.a;
            case 11:
                return o.bb.a.am;
            case 12:
            case 13:
            case 14:
                return o.bb.a.b;
            case 15:
            case 16:
                return o.bb.a.h;
            case 17:
                return o.bb.a.d;
            case 18:
                return o.bb.a.i;
            case 19:
                o.bb.a aVar = o.bb.a.l;
                int i4 = z + 75;
                C = i4 % 128;
                int i5 = i4 % 2;
                return aVar;
            case 20:
                return o.bb.a.U;
            case 21:
                o.bb.a aVar2 = o.bb.a.ab;
                int i6 = z + 11;
                C = i6 % 128;
                int i7 = i6 % 2;
                return aVar2;
            case 22:
                return o.bb.a.aa;
            case 23:
                return o.bb.a.ac;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                D(Color.green(0) + 18, "0 \u001a&\u001a\u0019\u0002(\u001f\n\u0005\u001c\u001b\u0013\u0016\u0019\u000e\u0003", (byte) (Color.rgb(0, 0, 0) + 16777329), objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    private static void D(int i2, String str, byte b2, Object[] objArr) {
        int i3;
        char c2;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr = y;
        if (cArr != null) {
            int i4 = $10 + 61;
            $11 = i4 % 128;
            int i5 = i4 % 2;
            int length = cArr.length;
            char[] cArr2 = new char[length];
            for (int i6 = 0; i6 < length; i6++) {
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr[i6])};
                    Object obj = o.e.a.s.get(-1401577988);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(17 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (char) (Process.myTid() >> 22), 76 - (ViewConfiguration.getPressedStateDuration() >> 16));
                        byte b3 = (byte) 0;
                        byte b4 = b3;
                        Object[] objArr3 = new Object[1];
                        F(b3, b4, (byte) (b4 | 49), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj);
                    }
                    cArr2[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr = cArr2;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(A)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - View.getDefaultSize(0, 0), (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 76);
                byte b5 = (byte) 0;
                byte b6 = b5;
                Object[] objArr5 = new Object[1];
                F(b5, b6, (byte) (b6 | 49), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i2];
            switch (i2 % 2 != 0 ? (char) 19 : (char) 30) {
                case 30:
                    i3 = i2;
                    break;
                default:
                    int i7 = $11 + 79;
                    $10 = i7 % 128;
                    switch (i7 % 2 != 0) {
                        case true:
                            i3 = i2 + 74;
                            cArr3[i3] = (char) (charArray[i3] >>> b2);
                            break;
                        default:
                            i3 = i2 - 1;
                            cArr3[i3] = (char) (charArray[i3] - b2);
                            break;
                    }
            }
            if (i3 > 1) {
                char c3 = 5;
                int i8 = $11 + 5;
                $10 = i8 % 128;
                int i9 = i8 % 2;
                mVar.b = 0;
                while (true) {
                    switch (mVar.b >= i3) {
                        case true:
                            break;
                        default:
                            int i10 = $10 + 3;
                            $11 = i10 % 128;
                            int i11 = i10 % 2;
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                cArr3[mVar.b] = (char) (mVar.e - b2);
                                cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                                c2 = c3;
                            } else {
                                try {
                                    Object[] objArr6 = new Object[13];
                                    objArr6[12] = mVar;
                                    objArr6[11] = Integer.valueOf(charValue);
                                    objArr6[10] = mVar;
                                    objArr6[9] = mVar;
                                    objArr6[8] = Integer.valueOf(charValue);
                                    objArr6[7] = mVar;
                                    objArr6[6] = mVar;
                                    objArr6[c3] = Integer.valueOf(charValue);
                                    objArr6[4] = mVar;
                                    objArr6[3] = mVar;
                                    objArr6[2] = Integer.valueOf(charValue);
                                    objArr6[1] = mVar;
                                    objArr6[0] = mVar;
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(10 - KeyEvent.keyCodeFromString(""), (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 8855), 324 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                                        byte b7 = (byte) 0;
                                        byte b8 = b7;
                                        Object[] objArr7 = new Object[1];
                                        F(b7, b8, (byte) (b8 | 53), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        int i12 = $10 + 59;
                                        $11 = i12 % 128;
                                        int i13 = i12 % 2;
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 != null) {
                                                c2 = 5;
                                            } else {
                                                Class cls4 = (Class) o.e.a.c(Color.argb(0, 0, 0, 0) + 11, (char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), AndroidCharacter.getMirror('0') + 17);
                                                byte b9 = (byte) 0;
                                                byte b10 = b9;
                                                Object[] objArr9 = new Object[1];
                                                F(b9, b10, (byte) (b10 | 52), objArr9);
                                                String str2 = (String) objArr9[0];
                                                c2 = 5;
                                                obj4 = cls4.getMethod(str2, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i14 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[intValue];
                                            cArr3[mVar.b + 1] = cArr[i14];
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else {
                                        c2 = 5;
                                        if (mVar.c == mVar.d) {
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i15 = (mVar.c * charValue) + mVar.i;
                                            int i16 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[i15];
                                            cArr3[mVar.b + 1] = cArr[i16];
                                        } else {
                                            int i17 = (mVar.c * charValue) + mVar.h;
                                            int i18 = (mVar.d * charValue) + mVar.i;
                                            cArr3[mVar.b] = cArr[i17];
                                            cArr3[mVar.b + 1] = cArr[i18];
                                            int i19 = $10 + 77;
                                            $11 = i19 % 128;
                                            switch (i19 % 2 != 0) {
                                            }
                                        }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                            c3 = c2;
                            break;
                    }
                }
            }
            for (int i20 = 0; i20 < i2; i20++) {
                cArr3[i20] = (char) (cArr3[i20] ^ 13722);
            }
            objArr[0] = new String(cArr3);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v35, types: [byte[]] */
    private static void E(String str, int[] iArr, boolean z2, Object[] objArr) {
        int i2;
        int i3;
        int i4;
        ?? r0 = str;
        int i5 = 1;
        int i6 = 0;
        switch (r0 == 0) {
            case false:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i7 = iArr[0];
        int i8 = iArr[1];
        int i9 = 2;
        int i10 = iArr[2];
        int i11 = iArr[3];
        char[] cArr = B;
        int i12 = 11;
        if (cArr != null) {
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i13 = 0;
            while (i13 < length) {
                int i14 = $11 + 59;
                $10 = i14 % 128;
                switch (i14 % i9 != 0 ? i12 : 49) {
                    case 11:
                        try {
                            Object[] objArr2 = new Object[i5];
                            objArr2[i6] = Integer.valueOf(cArr[i13]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                i4 = length;
                            } else {
                                Class cls = (Class) o.e.a.c(KeyEvent.normalizeMetaState(i6) + i12, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 42);
                                byte b2 = (byte) i6;
                                byte b3 = b2;
                                i4 = length;
                                Object[] objArr3 = new Object[1];
                                F(b2, b3, (byte) (b3 + 2), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr2[i13] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i13 >>= 0;
                            break;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        i4 = length;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr[i13])};
                            Object obj2 = o.e.a.s.get(1951085128);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c((Process.myPid() >> 22) + 11, (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (ViewConfiguration.getEdgeSlop() >> 16) + 43);
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                F(b4, b5, (byte) (b5 + 2), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj2);
                            }
                            cArr2[i13] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            i13++;
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                }
                length = i4;
                i5 = 1;
                i6 = 0;
                i9 = 2;
                i12 = 11;
            }
            cArr = cArr2;
        }
        char[] cArr3 = new char[i8];
        System.arraycopy(cArr, i7, cArr3, 0, i8);
        if (bArr != null) {
            char[] cArr4 = new char[i8];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i8) {
                    case true:
                        if (bArr[lVar.d] == 1) {
                            int i15 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                                Object obj3 = o.e.a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - Color.argb(0, 0, 0, 0), (char) Color.green(0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 449);
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    F(b6, b7, (byte) (b7 + 3), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj3);
                                }
                                cArr4[i15] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        } else {
                            int i16 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                                Object obj4 = o.e.a.s.get(804049217);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0'), (char) View.MeasureSpec.getSize(0), 207 - KeyEvent.keyCodeFromString(""));
                                    byte b8 = (byte) 0;
                                    byte b9 = b8;
                                    Object[] objArr9 = new Object[1];
                                    F(b8, b9, b9, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj4);
                                }
                                cArr4[i16] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        c2 = cArr4[lVar.d];
                        try {
                            Object[] objArr10 = {lVar, lVar};
                            Object obj5 = o.e.a.s.get(-2112603350);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c(TextUtils.getTrimmedLength("") + 11, (char) (ViewConfiguration.getFadingEdgeLength() >> 16), 258 - ((byte) KeyEvent.getModifierMetaStateMask()));
                                byte b10 = (byte) 0;
                                byte b11 = b10;
                                Object[] objArr11 = new Object[1];
                                F(b10, b11, (byte) (b11 | 56), objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                            int i17 = $10 + 47;
                            $11 = i17 % 128;
                            int i18 = i17 % 2;
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    default:
                        cArr3 = cArr4;
                        break;
                }
            }
        }
        if (i11 > 0) {
            char[] cArr5 = new char[i8];
            i2 = 0;
            System.arraycopy(cArr3, 0, cArr5, 0, i8);
            int i19 = i8 - i11;
            System.arraycopy(cArr5, 0, cArr3, i19, i11);
            System.arraycopy(cArr5, i11, cArr3, 0, i19);
        } else {
            i2 = 0;
        }
        if (z2) {
            char[] cArr6 = new char[i8];
            while (true) {
                lVar.d = i2;
                if (lVar.d < i8) {
                    cArr6[lVar.d] = cArr3[(i8 - lVar.d) - 1];
                    i2 = lVar.d + 1;
                } else {
                    cArr3 = cArr6;
                }
            }
        }
        if (i10 > 0) {
            int i20 = $11 + 91;
            $10 = i20 % 128;
            switch (i20 % 2 != 0) {
                case true:
                    i3 = 0;
                    break;
                default:
                    i3 = 0;
                    break;
            }
            while (true) {
                lVar.d = i3;
                if (lVar.d < i8) {
                    int i21 = $10 + 19;
                    $11 = i21 % 128;
                    switch (i21 % 2 == 0 ? (char) 28 : '\n') {
                        case 28:
                            cArr3[lVar.d] = (char) (cArr3[lVar.d] + iArr[4]);
                            i3 = lVar.d << 0;
                            break;
                        default:
                            cArr3[lVar.d] = (char) (cArr3[lVar.d] - iArr[2]);
                            i3 = lVar.d + 1;
                            break;
                    }
                }
            }
        }
        objArr[0] = new String(cArr3);
    }
}
