package o.er;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\h.smali */
public abstract class h {
    private static int a = 0;
    private static int e = 1;
    public final o.eo.e c;
    public final o.el.e d;

    public abstract a[] i();

    h(o.eo.e eVar, o.el.e eVar2) {
        this.c = eVar;
        this.d = eVar2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public boolean b() {
        /*
            r7 = this;
            int r0 = o.er.h.a
            int r0 = r0 + 106
            r1 = 1
            int r0 = r0 - r1
            int r2 = r0 % 128
            o.er.h.e = r2
            int r0 = r0 % 2
            o.er.a[] r0 = r7.i()
            int r2 = r0.length
            int r3 = o.er.h.a
            r4 = r3 & 71
            r3 = r3 | 71
            int r4 = r4 + r3
            int r3 = r4 % 128
            o.er.h.e = r3
            int r4 = r4 % 2
            r3 = 0
            r4 = r3
        L20:
            if (r4 >= r2) goto L24
            r5 = r1
            goto L25
        L24:
            r5 = r3
        L25:
            switch(r5) {
                case 0: goto L39;
                default: goto L28;
            }
        L28:
            int r5 = o.er.h.e
            r6 = r5 | 115(0x73, float:1.61E-43)
            int r6 = r6 << r1
            r5 = r5 ^ 115(0x73, float:1.61E-43)
            int r6 = r6 - r5
            int r5 = r6 % 128
            o.er.h.a = r5
            int r6 = r6 % 2
            if (r6 == 0) goto L58
            goto L58
        L39:
            int r0 = o.er.h.a
            r1 = r0 & 13
            r0 = r0 | 13
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.er.h.e = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L4b
            r0 = 20
            goto L4d
        L4b:
            r0 = 83
        L4d:
            switch(r0) {
                case 20: goto L51;
                default: goto L50;
            }
        L50:
            return r3
        L51:
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L56
            throw r0     // Catch: java.lang.Throwable -> L56
        L56:
            r0 = move-exception
            throw r0
        L58:
            r5 = r0[r4]
            boolean r5 = r5.b()
            if (r5 == 0) goto L62
            r5 = r3
            goto L63
        L62:
            r5 = r1
        L63:
            switch(r5) {
                case 1: goto L75;
                default: goto L66;
            }
        L66:
            int r0 = o.er.h.a
            r2 = r0 | 117(0x75, float:1.64E-43)
            int r2 = r2 << r1
            r0 = r0 ^ 117(0x75, float:1.64E-43)
            int r2 = r2 - r0
            int r0 = r2 % 128
            o.er.h.e = r0
            int r2 = r2 % 2
            goto L8e
        L75:
            int r4 = r4 + 1
            int r5 = o.er.h.e
            r6 = r5 ^ 73
            r5 = r5 & 73
            int r5 = r5 << r1
            int r6 = r6 + r5
            int r5 = r6 % 128
            o.er.h.a = r5
            int r6 = r6 % 2
            if (r6 == 0) goto L89
            r5 = r1
            goto L8a
        L89:
            r5 = r3
        L8a:
            switch(r5) {
                case 0: goto L8d;
                default: goto L8d;
            }
        L8d:
            goto L20
        L8e:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.h.b():boolean");
    }
}
