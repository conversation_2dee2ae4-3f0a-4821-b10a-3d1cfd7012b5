package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.ObjectMap;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.ObjectStreamClass;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\JavaSerializer.smali */
public class JavaSerializer extends Serializer {
    @Override // com.esotericsoftware.kryo.Serializer
    public void write(<PERSON><PERSON> kryo, Output output, Object object) {
        try {
            ObjectMap graphContext = kryo.getGraphContext();
            ObjectOutputStream objectStream = (ObjectOutputStream) graphContext.get(this);
            if (objectStream == null) {
                objectStream = new ObjectOutputStream(output);
                graphContext.put(this, objectStream);
            }
            objectStream.writeObject(object);
            objectStream.flush();
        } catch (Exception ex) {
            throw new KryoException("Error during Java serialization.", ex);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object read(Kryo kryo, Input input, Class type) {
        try {
            ObjectMap graphContext = kryo.getGraphContext();
            ObjectInputStream objectStream = (ObjectInputStream) graphContext.get(this);
            if (objectStream == null) {
                objectStream = new ObjectInputStreamWithKryoClassLoader(input, kryo);
                graphContext.put(this, objectStream);
            }
            return objectStream.readObject();
        } catch (Exception ex) {
            throw new KryoException("Error during Java deserialization.", ex);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\JavaSerializer$ObjectInputStreamWithKryoClassLoader.smali */
    private static class ObjectInputStreamWithKryoClassLoader extends ObjectInputStream {
        private final Kryo kryo;

        ObjectInputStreamWithKryoClassLoader(InputStream in, Kryo kryo) throws IOException {
            super(in);
            this.kryo = kryo;
        }

        @Override // java.io.ObjectInputStream
        protected Class resolveClass(ObjectStreamClass type) {
            try {
                return Class.forName(type.getName(), false, this.kryo.getClassLoader());
            } catch (ClassNotFoundException ex) {
                throw new KryoException("Class not found: " + type.getName(), ex);
            }
        }
    }
}
