package o.dp;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dp\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    public static final b e;
    public static final b g;
    public static final b h;
    private static final /* synthetic */ b[] i;
    public static final b j;
    private static char k;
    private static char l;
    private static char m;
    private static char[] n;

    /* renamed from: o, reason: collision with root package name */
    private static char f56o;
    private static int p;
    private static boolean q;
    private static int r;
    private static int s;
    private static boolean t;
    private final String f;

    static void d() {
        l = (char) 46176;
        m = (char) 30459;
        k = (char) 27811;
        f56o = (char) 41351;
        n = new char[]{61650, 61647, 61646, 61654, 61642, 61668, 61640, 61651, 61659, 61694, 61689, 61678, 61656, 61691, 61672, 61679, 61686, 61682, 61674, 61662, 61657, 61641, 61661, 61688, 61663};
        t = true;
        q = true;
        r = 782102667;
    }

    static void init$0() {
        $$a = new byte[]{12, 98, 124, -66};
        $$b = Opcodes.LOOKUPSWITCH;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = 121 - r8
            byte[] r0 = o.dp.b.$$a
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r6
            r8 = r7
            r4 = r2
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r6 = r6 + 1
            int r7 = r7 + r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dp.b.w(int, int, byte, java.lang.Object[]):void");
    }

    private static /* synthetic */ b[] b() {
        int i2 = s;
        int i3 = i2 + 65;
        p = i3 % 128;
        int i4 = i3 % 2;
        b[] bVarArr = {a, e, c, b, d, g, h, j};
        int i5 = i2 + Opcodes.LSHR;
        p = i5 % 128;
        switch (i5 % 2 != 0 ? '+' : '\r') {
            case '\r':
                return bVarArr;
            default:
                int i6 = 72 / 0;
                return bVarArr;
        }
    }

    public static b valueOf(String str) {
        int i2 = p + 49;
        s = i2 % 128;
        int i3 = i2 % 2;
        b bVar = (b) Enum.valueOf(b.class, str);
        int i4 = p + 85;
        s = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    public static b[] values() {
        int i2 = p + 87;
        s = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return (b[]) i.clone();
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        p = 0;
        s = 1;
        d();
        Object[] objArr = new Object[1];
        u("ꛒ\uaac6牙ᦏރ肼晳櫦\u05caꋄ\uf429湇鹹맠", View.combineMeasuredStates(0, 0) + 14, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u("ꒈ䷶炱\udb93ꁻ鄄䚚\u218d", 7 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr2);
        a = new b(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        u("퀣\ue76fﬓ﴾\uf429湇鹹맠", 8 - View.getDefaultSize(0, 0), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        u("䶐ꁫ薤䁺", KeyEvent.getDeadChar(0, 0) + 4, objArr4);
        e = new b(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        u("離귽ꭅ읁컃䰭贃嵿砚\udee5䋛蔝", 11 - (ViewConfiguration.getTouchSlop() >> 8), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        Object obj = null;
        v(null, TextUtils.getOffsetAfter("", 0) + 127, null, "\u0089\u0081\u0088\u0087\u0084\u0086\u0085\u0081\u0084\u0083\u0082\u0081", objArr6);
        c = new b(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        v(null, 127 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), null, "\u008f\u008c\u008e\u008d\u008c\u008b\u008a\u0089", objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        u("蝜䡞慇፫炱\udb93ꁻ鄄䚚\u218d", Color.rgb(0, 0, 0) + 16777225, objArr8);
        b = new b(intern4, 3, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        v(null, 127 - Color.red(0), null, "\u008c\u008b\u008a\u0089\u0093\u0092\u0091\u008c\u0090\u0081", objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        v(null, 127 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), null, "\u0096\u0087\u0086\u0083\u0095\u0094\u0089\u0086\u0085\u0081\u0084\u0083\u0082\u0081", objArr10);
        d = new b(intern5, 4, ((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        u("離귽ꭅ읁컃䰭䈬ᥳヾ뺡", 11 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        u("㗌學엖茼", ExpandableListView.getPackedPositionChild(0L) + 5, objArr12);
        g = new b(intern6, 5, ((String) objArr12[0]).intern());
        Object[] objArr13 = new Object[1];
        v(null, 127 - (ViewConfiguration.getScrollBarSize() >> 8), null, "\u008f\u008c\u008e\u008d\u0093\u0098\u0092\u0097", objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        v(null, AndroidCharacter.getMirror('0') + 'O', null, "\u008d\u0099\u0097", objArr14);
        h = new b(intern7, 6, ((String) objArr14[0]).intern());
        Object[] objArr15 = new Object[1];
        u("宕\uf25e턼鰾Ṵㅖ퇡䂣", TextUtils.indexOf((CharSequence) "", '0', 0) + 8, objArr15);
        String intern8 = ((String) objArr15[0]).intern();
        Object[] objArr16 = new Object[1];
        u("\ue38e罕\uf7bf峮", 3 - KeyEvent.normalizeMetaState(0), objArr16);
        j = new b(intern8, 7, ((String) objArr16[0]).intern());
        i = b();
        int i2 = p + 87;
        s = i2 % 128;
        switch (i2 % 2 == 0 ? '4' : 'W') {
            case Opcodes.POP /* 87 */:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    private b(String str, int i2, String str2) {
        this.f = str2;
    }

    public final String e() {
        int i2 = p + 61;
        s = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 17 : (char) 20) {
            case 20:
                return this.f;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 576
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dp.b.u(java.lang.String, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:121:0x002c  */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1, types: [byte[]] */
    /* JADX WARN: Type inference failed for: r1v2 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 872
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dp.b.v(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
