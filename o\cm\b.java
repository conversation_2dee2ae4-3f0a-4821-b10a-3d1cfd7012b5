package o.cm;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import o.cc.a;
import o.cc.e;
import o.eg.d;
import o.ei.i;
import o.et.c;
import o.et.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cm\b.smali */
public final class b implements e<g> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static char c;
    private static int d;
    private static long e;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        i = 1;
        e();
        Drawable.resolveOpacity(0, 0);
        ViewConfiguration.getScrollFriction();
        int i2 = i + 89;
        a = i2 % 128;
        switch (i2 % 2 != 0 ? 'Q' : '6') {
            case Opcodes.FASTORE /* 81 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        c = (char) 17957;
        d = 161105445;
        e = 6093456768497136093L;
        b = 874635509;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 + 99
            int r9 = r9 * 3
            int r9 = 3 - r9
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = o.cm.b.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L19:
            r3 = r2
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            int r9 = r9 + 1
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cm.b.h(short, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{75, 105, 70, 99};
        $$b = 43;
    }

    @Override // o.cc.e
    public final /* synthetic */ g d(String str, String str2, int i2, String str3) {
        int i3 = i + 81;
        a = i3 % 128;
        int i4 = i3 % 2;
        g e2 = e(str, str2, i2, str3);
        int i5 = a + Opcodes.DDIV;
        i = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return e2;
            default:
                int i6 = 37 / 0;
                return e2;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:80:0x0783. Please report as an issue. */
    @Override // o.cc.e
    public final List<g> a(String str, String str2, int i2, String str3, o.eg.b bVar) throws i {
        try {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 713579983, "虜큼뗀龷閭쉺ᠫ钮㆜実\u1a8c宯髞ﱰ敗≍翛", (char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 61582), "ㇸ瞢軕鏰", "\uf7f8痢퓣ྎ", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            f((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "䷭袙題诊ᘮ쫀쌧毞鎩뜑ᾇ亽ꏿ꜌", (char) (50658 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), "險໐\ue222룅", "\uf7f8痢퓣ྎ", objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
            ArrayList arrayList = new ArrayList();
            g gVar = new g(str, str2, i2, str3);
            Object[] objArr3 = new Object[1];
            g(3 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "\u0004\ufff7\ufff6\ufff5\u0011\ufff5\u0002\u0011\u0002\u0007", 9 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), 216 - (Process.myTid() >> 22), false, objArr3);
            o.eg.b v = bVar.v(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            f((-1) - ExpandableListView.getPackedPositionChild(0L), "鑰ￇଂ鸙伏겈\uee4e㭠\uefe5隔箩ꘋ\ue958Љჲ␃\ue650❎秢\uf422ﱷ瘏", (char) TextUtils.indexOf("", "", 0, 0), "ᛸ睢ꡡ㯊", "\uf7f8痢퓣ྎ", objArr4);
            o.eg.b v2 = v.v(((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            g(2 - TextUtils.indexOf((CharSequence) "", '0', 0), "�\u0005\u0000", 4 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 206 - (ViewConfiguration.getTouchSlop() >> 8), false, objArr5);
            gVar.a(v2.B(((String) objArr5[0]).intern()));
            Object[] objArr6 = new Object[1];
            f(ViewConfiguration.getScrollBarFadeDuration() >> 16, "⍳㬆⭾哵š\ud94d㥴寕㖕膃", (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), "횟ꑣ\u0fcd⯙", "\uf7f8痢퓣ྎ", objArr6);
            gVar.e(v2.B(((String) objArr6[0]).intern()));
            arrayList.add(gVar);
            Object[] objArr7 = new Object[1];
            f(View.resolveSize(0, 0), "猭\udb90㫓㙟沖晼榻ᾃ", (char) Color.blue(0), "좻쥊㯰〆", "\uf7f8痢퓣ྎ", objArr7);
            byte[] z = v2.z(((String) objArr7[0]).intern());
            Object[] objArr8 = new Object[1];
            f((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "\ue840罉䑭꽨\uec56딾昡脃㱅鐊염ᖼ聨큑臊᷉", (char) (57381 - View.resolveSizeAndState(0, 0, 0)), "Һཀ╵\uf7e0", "\uf7f8痢퓣ྎ", objArr8);
            byte[] z2 = v2.z(((String) objArr8[0]).intern());
            if (z != null && z2 != null) {
                o.ee.g.c();
                Object[] objArr9 = new Object[1];
                f((-713579983) - KeyEvent.normalizeMetaState(0), "虜큼뗀龷閭쉺ᠫ钮㆜実\u1a8c宯髞ﱰ敗≍翛", (char) ((Process.myTid() >> 22) + 61582), "ㇸ瞢軕鏰", "\uf7f8痢퓣ྎ", objArr9);
                String intern2 = ((String) objArr9[0]).intern();
                Object[] objArr10 = new Object[1];
                g(33 - TextUtils.lastIndexOf("", '0', 0, 0), "\u0004\uffbfￌ\uffbf\u0000\u000b\u0013\u0004\u0011\r\u0000\u0013\u0004\uffbf\u0000\u000f\u000f\u000b\b\u0002\u0000\u0013\b\u000e\r\uffbf\u0003\u0004\u0013\u0004\u0002\u0013\u0004\u0003\u0011\u0004\u0000\u0003\uffef\u0011\u000e\u0005\b\u000b", 44 - Gravity.getAbsoluteGravity(0, 0), 235 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), false, objArr10);
                o.ee.g.d(intern2, ((String) objArr10[0]).intern());
                g gVar2 = new g(c.d(str), str2, i2, str3);
                gVar2.a(z);
                gVar2.e(z2);
                gVar2.b(true);
                arrayList.add(gVar2);
            }
            Object[] objArr11 = new Object[1];
            f(ViewConfiguration.getLongPressTimeout() >> 16, "嫀훂⍁滄呆\udc94ᐑ춛", (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "뫺䊇㏣ﶃ", "\uf7f8痢퓣ྎ", objArr11);
            byte[] B = v2.B(((String) objArr11[0]).intern());
            Object[] objArr12 = new Object[1];
            f((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 661182095, "圖궄⢮並軙쟽㧲葲\uf6a3⚪\ud96b", (char) View.MeasureSpec.makeMeasureSpec(0, 0), "焕霩험⦐", "\uf7f8痢퓣ྎ", objArr12);
            v2.B(((String) objArr12[0]).intern());
            Object[] objArr13 = new Object[1];
            f((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\ud864\ued57㎘罌튬\udfce", (char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 14769), "Ꭾ钛노긹", "\uf7f8痢퓣ྎ", objArr13);
            v2.B(((String) objArr13[0]).intern());
            Object[] objArr14 = new Object[1];
            f((-951825106) - TextUtils.getOffsetAfter("", 0), "앃戚첱넟쫗\ud869榒仉⧁奄뜌", (char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), "⸴䑍姇镤", "\uf7f8痢퓣ྎ", objArr14);
            v2.B(((String) objArr14[0]).intern());
            Object[] objArr15 = new Object[1];
            g((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "\ufff6\ufff6\ufffb\u0001\u0014\u0004", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 5, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 213, false, objArr15);
            v2.B(((String) objArr15[0]).intern());
            Object[] objArr16 = new Object[1];
            f(ExpandableListView.getPackedPositionChild(0L) - 485279342, "ⳍ\u2d98壘润鉶\uf396힟퀧৵", (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), "釖ጹ⧣⍴", "\uf7f8痢퓣ྎ", objArr16);
            v2.B(((String) objArr16[0]).intern());
            Object[] objArr17 = new Object[1];
            g(TextUtils.getCapsMode("", 0, 0) + 8, "\u0006\u000f￼\u0018￼\ufffa\u0002￼￫", Color.red(0) + 9, 209 - View.MeasureSpec.makeMeasureSpec(0, 0), true, objArr17);
            v2.B(((String) objArr17[0]).intern());
            Object[] objArr18 = new Object[1];
            g(16 - ExpandableListView.getPackedPositionType(0L), "\u0018\u0005￨\b\t\u0018\u0005\u0010\t\ufff6\u0003ￕ\ufff0\ufff3￨\uffe7\f\u0018\u000b\u0012\t\ufff0\u0005", 23 - TextUtils.getOffsetAfter("", 0), 230 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), true, objArr18);
            String q = v2.q(((String) objArr18[0]).intern());
            if (q != null && !q.isEmpty()) {
                try {
                    Integer.valueOf(q);
                } catch (NumberFormatException e2) {
                    Object[] objArr19 = new Object[1];
                    f((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1, "᤹訍룾᜴缜\uee1e뵶䠓菔㢏ർ펄㦞㒩魂톪\ua48e⩥ଊ涟巀녇퐔嬨毶藐涳ꔽḧ׃㚥\ue953㡑\uefb3\ue68e屪闃\uecae뵴狙擄䊻≂嚤췘ӆ", (char) (ViewConfiguration.getFadingEdgeLength() >> 16), "㶊್ꒂ\ueede", "\uf7f8痢퓣ྎ", objArr19);
                    throw new i(((String) objArr19[0]).intern());
                }
            }
            Object[] objArr20 = new Object[1];
            g(TextUtils.getCapsMode("", 0, 0) + 3, "\u000e\u0003\u0004￢\ufff1￬\ufffe￢\u000e\u0014\r\u0013\u0011\u0018￢", (Process.myTid() >> 22) + 15, 235 - KeyEvent.getDeadChar(0, 0), false, objArr20);
            v2.B(((String) objArr20[0]).intern());
            Object[] objArr21 = new Object[1];
            g((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 14, "\ufff2￭\uffff￣\u0015\u0012\u0012\u0005\u000e\u0003\u0019￣\u000f\u0004\u0005￣", 16 - (ViewConfiguration.getLongPressTimeout() >> 16), 235 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), false, objArr21);
            v2.B(((String) objArr21[0]).intern());
            Object[] objArr22 = new Object[1];
            g(2 - TextUtils.indexOf((CharSequence) "", '0'), "\u0007\u0003￼\ufff7\u0007", ((Process.getThreadPriority(0) + 20) >> 6) + 5, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 215, false, objArr22);
            v2.B(((String) objArr22[0]).intern());
            Object[] objArr23 = new Object[1];
            g((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 2, "￼\u0003\u0001", 3 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (KeyEvent.getMaxKeyCode() >> 16) + 210, false, objArr23);
            v2.D(((String) objArr23[0]).intern());
            Object[] objArr24 = new Object[1];
            g(13 - ImageFormat.getBitsPerPixel(0), "\n\u000e\n￭￮\ufff7￤\u0000\u0013\u0006\u0016\u0014\u0014￪ￒ\u0015", 16 - TextUtils.getTrimmedLength(""), TextUtils.getOffsetBefore("", 0) + 233, true, objArr24);
            v2.B(((String) objArr24[0]).intern());
            Object[] objArr25 = new Object[1];
            g((ViewConfiguration.getLongPressTimeout() >> 16) + 7, "\u0000\u0013\u0006\u0016\u0014\u0014￪ￓ\u0015\n\u000e\n￭￮\ufff7￤", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 15, 233 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), true, objArr25);
            v2.B(((String) objArr25[0]).intern());
            Object[] objArr26 = new Object[1];
            g('4' - AndroidCharacter.getMirror('0'), "\f\n\u0007\u0004\uffd9\b\b\u0004\u0001\ufffb\ufff9\f\u0001\u0007\u0006ￛ\u0007\u0006", 17 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 242 - (ViewConfiguration.getPressedStateDuration() >> 16), false, objArr26);
            byte[] B2 = v2.B(((String) objArr26[0]).intern());
            Object[] objArr27 = new Object[1];
            f(KeyEvent.getMaxKeyCode() >> 16, "蔵쇴⫚岬珯ᤸ\uf400ꍖ堌\uf8b7葉移侶Ϲ眣ݐ\uf40fཆ᷶窣\uab6e\ud92f\ud91f貧짴外뤤筏", (char) (34843 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), "\ue41aꥯᮦ\uf388", "\uf7f8痢퓣ྎ", objArr27);
            v2.B(((String) objArr27[0]).intern());
            Object[] objArr28 = new Object[1];
            g(Color.red(0) + 2, "\ufff9\b\u0007\ufffa\ufff8\u0004\u0007", ((Process.getThreadPriority(0) + 20) >> 6) + 7, 245 - View.getDefaultSize(0, 0), false, objArr28);
            List<a> a2 = a(v2.s(((String) objArr28[0]).intern()));
            Object[] objArr29 = new Object[1];
            g(9 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\uffff\u0011\f\uffff\u0010\ufff2\u0016\uffff￫￫\ufff4￡\r￬�\f\r\u0007\u0012\u0001", (Process.myTid() >> 22) + 20, Color.blue(0) + 236, true, objArr29);
            if (v2.b(((String) objArr29[0]).intern())) {
                Object[] objArr30 = new Object[1];
                g((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 17, "\ufffe\u0000\u0011\u0006\f\u000b\uffde\n\f\u0012\u000b\u0011￼￫\f￠\ufff3￪￪\ufffe\u0015\ufff1\u000f\ufffe\u000b\u0010", 26 - Color.red(0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 237, false, objArr30);
                if (v2.b(((String) objArr30[0]).intern())) {
                    int i3 = a + 69;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                    try {
                        Object[] objArr31 = new Object[1];
                        g(18 - Drawable.resolveOpacity(0, 0), "\ufffe\u0000\u0011\u0006\f\u000b\uffde\n\f\u0012\u000b\u0011￼￫\f￠\ufff3￪￪\ufffe\u0015\ufff1\u000f\ufffe\u000b\u0010", 26 - (ViewConfiguration.getFadingEdgeLength() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 238, false, objArr31);
                        byte[] B3 = v2.B(((String) objArr31[0]).intern());
                        Object[] objArr32 = new Object[1];
                        g(TextUtils.lastIndexOf("", '0', 0) + 16, "\ufff2￭\uffff￣\u0015\u0012\u0012\u0005\u000e\u0003\u0019￣\u000f\u0004\u0005￣", TextUtils.indexOf("", "", 0) + 16, 234 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), false, objArr32);
                        o.ej.e c2 = o.ej.e.c(Integer.parseInt(v2.r(((String) objArr32[0]).intern()), 16));
                        if (c2 == null) {
                            Object[] objArr33 = new Object[1];
                            f(KeyEvent.normalizeMetaState(0) + 276780620, "갡歄菎獵ᬹ䗾澎͵㡑\uf1c3ࠌ\uef43䴉ކ䎞鶑\ue8dbﵴ肨괖\ue264·瞵豫ꥩ䢟璝ᎀ㰜뫿뛡錀\u0005䮇免䶪Ⓔ丢Ⳁ视ט९\u0e5f礎و嶨鯯쵷雃ﮒऄᢴ봷\u0099㎈笠엤䷖䕷峖盿ઞ䣡ឲὝ쭮퓄⹊텨", (char) (18302 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), "䲌罖缐硇", "\uf7f8痢퓣ྎ", objArr33);
                            throw new i(((String) objArr33[0]).intern());
                        }
                        o.ej.a.a(B3, c2);
                        Object[] objArr34 = new Object[1];
                        g(9 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\uffff\u0011\f\uffff\u0010\ufff2\u0016\uffff￫￫\ufff4￡\r￬�\f\r\u0007\u0012\u0001", TextUtils.indexOf("", "", 0, 0) + 20, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 236, true, objArr34);
                        o.dk.b.a(v2.B(((String) objArr34[0]).intern())).shortValueExact();
                    } catch (ArithmeticException | NumberFormatException e3) {
                        StringBuilder sb2 = new StringBuilder();
                        Object[] objArr35 = new Object[1];
                        g((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 41, "\u0017￮\uffc8\f\r\u0016\u0011\u000e\r￬\uffc8\r\u001a\u001d\ufff8\uffc8\u001c\u000b\r\u0018\u001b\r\u001a\uffc8\u001cￏ\u0016\u001b\r\u0017\f\uffc8\ufff6\ufff7\ufffb\ufff2\uffc8\u001c\u001d\u0018\u0016\u0011\uffc8ￕ\uffc8\u001c\t\u0015\u001a", 49 - (ViewConfiguration.getScrollDefaultDelay() >> 16), MotionEvent.axisFromString("") + 227, true, objArr35);
                        throw new i(sb2.append(((String) objArr35[0]).intern()).append(e3.getMessage()).toString());
                    }
                }
            }
            Object[] objArr36 = new Object[1];
            g((ViewConfiguration.getJumpTapTimeout() >> 16) + 2, "\b\u0000\ufff4\u0007", 4 - (ViewConfiguration.getJumpTapTimeout() >> 16), (KeyEvent.getMaxKeyCode() >> 16) + 215, true, objArr36);
            switch (v2.b(((String) objArr36[0]).intern()) ? '-' : 'R') {
                case '-':
                    try {
                        Object[] objArr37 = new Object[1];
                        g(View.MeasureSpec.getSize(0) + 2, "\b\u0000\ufff4\u0007", 4 - View.resolveSize(0, 0), 215 - Color.alpha(0), true, objArr37);
                        byte[] B4 = v2.B(((String) objArr37[0]).intern());
                        Object[] objArr38 = new Object[1];
                        g(15 - TextUtils.getOffsetAfter("", 0), "\ufff2￭\uffff￣\u0015\u0012\u0012\u0005\u000e\u0003\u0019￣\u000f\u0004\u0005￣", 16 - Color.green(0), Color.alpha(0) + 234, false, objArr38);
                        o.ej.e c3 = o.ej.e.c(Integer.parseInt(v2.r(((String) objArr38[0]).intern()), 16));
                        if (c3 == null) {
                            Object[] objArr39 = new Object[1];
                            f(276780620 - Color.argb(0, 0, 0, 0), "갡歄菎獵ᬹ䗾澎͵㡑\uf1c3ࠌ\uef43䴉ކ䎞鶑\ue8dbﵴ肨괖\ue264·瞵豫ꥩ䢟璝ᎀ㰜뫿뛡錀\u0005䮇免䶪Ⓔ丢Ⳁ视ט९\u0e5f礎و嶨鯯쵷雃ﮒऄᢴ봷\u0099㎈笠엤䷖䕷峖盿ઞ䣡ឲὝ쭮퓄⹊텨", (char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 18303), "䲌罖缐硇", "\uf7f8痢퓣ྎ", objArr39);
                            throw new i(((String) objArr39[0]).intern());
                        }
                        o.ej.a.a(B4, c3);
                        int i5 = i + 1;
                        a = i5 % 128;
                        switch (i5 % 2 != 0) {
                        }
                    } catch (NumberFormatException e4) {
                        StringBuilder sb3 = new StringBuilder();
                        Object[] objArr40 = new Object[1];
                        g((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 42, "\u0017￮\uffc8\f\r\u0016\u0011\u000e\r￬\uffc8\r\u001a\u001d\ufff8\uffc8\u001c\u000b\r\u0018\u001b\r\u001a\uffc8\u001cￏ\u0016\u001b\r\u0017\f\uffc8\ufff6\ufff7\ufffb\ufff2\uffc8\u001c\u001d\u0018\u0016\u0011\uffc8ￕ\uffc8\u001c\t\u0015\u001a", (ViewConfiguration.getWindowTouchSlop() >> 8) + 49, (-16776990) - Color.rgb(0, 0, 0), true, objArr40);
                        throw new i(sb3.append(((String) objArr40[0]).intern()).append(e4.getMessage()).toString());
                    }
                default:
                    Iterator it = arrayList.iterator();
                    while (it.hasNext()) {
                        g gVar3 = (g) ((c) it.next());
                        switch (arrayList.size() <= 1) {
                            case true:
                                gVar3.b(B);
                                break;
                            default:
                                gVar3.b(o.dk.e.a(gVar3.j(), B));
                                break;
                        }
                        new o.dn.a(B2);
                        short d2 = o.dk.e.d(gVar3.j(), B);
                        if (d2 == 0) {
                            Object[] objArr41 = new Object[1];
                            f(ViewConfiguration.getDoubleTapTimeout() >> 16, "᤹訍룾᜴缜\uee1e뵶䠓菔㢏ർ펄㦞㒩魂톪\ua48e⩥ଊ涟巀녇퐔嬨毶藐涳ꔽḧ׃㚥\ue953㡑\uefb3\ue68e屪闃\uecae뵴狙擄䊻≂嚤췘ӆ", (char) View.resolveSize(0, 0), "㶊್ꒂ\ueede", "\uf7f8痢퓣ྎ", objArr41);
                            throw new i(((String) objArr41[0]).intern());
                        }
                        gVar3.e(d2);
                        Iterator<a> it2 = a2.iterator();
                        while (it2.hasNext()) {
                            int i6 = i + 23;
                            a = i6 % 128;
                            if (i6 % 2 != 0) {
                                gVar3.c(it2.next());
                                int i7 = 16 / 0;
                            } else {
                                gVar3.c(it2.next());
                            }
                            int i8 = a + Opcodes.LSHL;
                            i = i8 % 128;
                            int i9 = i8 % 2;
                        }
                    }
                    return arrayList;
            }
        } catch (d e5) {
            StringBuilder sb4 = new StringBuilder();
            Object[] objArr42 = new Object[1];
            g(TextUtils.indexOf((CharSequence) "", '0') + 13, "\u0016\u000b\u0011\u0010ￂ￮\u000b\u0015\u0016ￂￜￂ￬\u0015\u0011\u0010ￂ\u0007\u001a\u0005\u0007\u0012\u0016\u000b\u0011\u0010ￂ\u0007\u0010\u0005\u0011\u0017\u0010\u0016\u0007\u0014\u0007\u0006ￂ\u0019\n\u000b\u000e\u0007ￂ\u0014\u0007\u0003\u0006\u000b\u0010\tￂ\ufff2\u0017\u0014\u0007ￂ￣\u0012\u0012\u000e\u000b\u0005\u0003", ((Process.getThreadPriority(0) + 20) >> 6) + 65, KeyEvent.normalizeMetaState(0) + 232, false, objArr42);
            throw new i(sb4.append(((String) objArr42[0]).intern()).append(e5.getMessage()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<o.cc.a> a(o.eg.e r28) throws o.eg.d, o.ei.i {
        /*
            Method dump skipped, instructions count: 978
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cm.b.a(o.eg.e):java.util.List");
    }

    private static g e(String str, String str2, int i2, String str3) {
        g gVar = new g(str, str2, i2, str3);
        int i3 = a + 81;
        i = i3 % 128;
        int i4 = i3 % 2;
        return gVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 694
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cm.b.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int r20, java.lang.String r21, int r22, int r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 524
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cm.b.g(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
