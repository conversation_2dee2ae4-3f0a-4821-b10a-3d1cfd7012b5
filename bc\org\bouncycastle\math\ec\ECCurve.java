package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.math.ec.endo.ECEndomorphism;
import bc.org.bouncycastle.math.ec.endo.GLVEndomorphism;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Collections;
import java.util.HashSet;
import java.util.Hashtable;
import java.util.Random;
import java.util.Set;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve.smali */
public abstract class ECCurve {
    public static final int COORD_AFFINE = 0;
    public static final int COORD_HOMOGENEOUS = 1;
    public static final int COORD_JACOBIAN = 2;
    public static final int COORD_JACOBIAN_CHUDNOVSKY = 3;
    public static final int COORD_JACOBIAN_MODIFIED = 4;
    public static final int COORD_LAMBDA_AFFINE = 5;
    public static final int COORD_LAMBDA_PROJECTIVE = 6;
    public static final int COORD_SKEWED = 7;
    protected r4 a;
    protected ECFieldElement b;
    protected ECFieldElement c;
    protected BigInteger d;
    protected BigInteger e;
    protected int f = 0;
    protected ECEndomorphism g = null;
    protected ECMultiplier h = null;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$Config.smali */
    public class Config {
        protected int a;
        protected ECEndomorphism b;
        protected ECMultiplier c;

        Config(int i, ECEndomorphism eCEndomorphism, ECMultiplier eCMultiplier) {
            this.a = i;
            this.b = eCEndomorphism;
            this.c = eCMultiplier;
        }

        public ECCurve create() {
            if (!ECCurve.this.supportsCoordinateSystem(this.a)) {
                throw new IllegalStateException("unsupported coordinate system");
            }
            ECCurve a = ECCurve.this.a();
            if (a == ECCurve.this) {
                throw new IllegalStateException("implementation returned current curve");
            }
            synchronized (a) {
                a.f = this.a;
                a.g = this.b;
                a.h = this.c;
            }
            return a;
        }

        public Config setCoordinateSystem(int i) {
            this.a = i;
            return this;
        }

        public Config setEndomorphism(ECEndomorphism eCEndomorphism) {
            this.b = eCEndomorphism;
            return this;
        }

        public Config setMultiplier(ECMultiplier eCMultiplier) {
            this.c = eCMultiplier;
            return this;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$F2m.smali */
    public static class F2m extends AbstractF2m {
        private int j;
        private int k;
        private int l;
        private int m;
        private ECPoint.F2m n;

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$F2m$a.smali */
        class a extends AbstractECLookupTable {
            final /* synthetic */ int a;
            final /* synthetic */ int b;
            final /* synthetic */ long[] c;
            final /* synthetic */ int[] d;

            a(int i, int i2, long[] jArr, int[] iArr) {
                this.a = i;
                this.b = i2;
                this.c = jArr;
                this.d = iArr;
            }

            private ECPoint a(long[] jArr, long[] jArr2) {
                return F2m.this.a(new ECFieldElement.F2m(F2m.this.j, this.d, new bc.org.bouncycastle.math.ec.a(jArr)), new ECFieldElement.F2m(F2m.this.j, this.d, new bc.org.bouncycastle.math.ec.a(jArr2)));
            }

            @Override // bc.org.bouncycastle.math.ec.ECLookupTable
            public int getSize() {
                return this.a;
            }

            @Override // bc.org.bouncycastle.math.ec.ECLookupTable
            public ECPoint lookup(int i) {
                int i2;
                long[] b = c6.b(this.b);
                long[] b2 = c6.b(this.b);
                int i3 = 0;
                for (int i4 = 0; i4 < this.a; i4++) {
                    long j = ((i4 ^ i) - 1) >> 31;
                    int i5 = 0;
                    while (true) {
                        i2 = this.b;
                        if (i5 < i2) {
                            long j2 = b[i5];
                            long[] jArr = this.c;
                            b[i5] = j2 ^ (jArr[i3 + i5] & j);
                            b2[i5] = b2[i5] ^ (jArr[(i2 + i3) + i5] & j);
                            i5++;
                        }
                    }
                    i3 += i2 * 2;
                }
                return a(b, b2);
            }

            @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
            public ECPoint lookupVar(int i) {
                long[] b = c6.b(this.b);
                long[] b2 = c6.b(this.b);
                int i2 = i * this.b * 2;
                int i3 = 0;
                while (true) {
                    int i4 = this.b;
                    if (i3 >= i4) {
                        return a(b, b2);
                    }
                    long[] jArr = this.c;
                    b[i3] = jArr[i2 + i3];
                    b2[i3] = jArr[i4 + i2 + i3];
                    i3++;
                }
            }
        }

        public F2m(int i, int i2, BigInteger bigInteger, BigInteger bigInteger2) {
            this(i, i2, 0, 0, bigInteger, bigInteger2, (BigInteger) null, (BigInteger) null);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECMultiplier b() {
            return isKoblitz() ? new WTauNafMultiplier() : super.b();
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
            int i3 = (this.j + 63) >>> 6;
            int[] iArr = isTrinomial() ? new int[]{this.k} : new int[]{this.k, this.l, this.m};
            long[] jArr = new long[i2 * i3 * 2];
            int i4 = 0;
            for (int i5 = 0; i5 < i2; i5++) {
                ECPoint eCPoint = eCPointArr[i + i5];
                ((ECFieldElement.F2m) eCPoint.getRawXCoord()).d.a(jArr, i4);
                int i6 = i4 + i3;
                ((ECFieldElement.F2m) eCPoint.getRawYCoord()).d.a(jArr, i6);
                i4 = i6 + i3;
            }
            return new a(i2, i3, jArr, iArr);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement fromBigInteger(BigInteger bigInteger) {
            if (bigInteger != null && bigInteger.signum() >= 0) {
                int bitLength = bigInteger.bitLength();
                int i = this.j;
                if (bitLength <= i) {
                    int i2 = this.l;
                    int i3 = this.m;
                    return new ECFieldElement.F2m(i, (i2 | i3) == 0 ? new int[]{this.k} : new int[]{this.k, i2, i3}, new bc.org.bouncycastle.math.ec.a(bigInteger));
                }
            }
            throw new IllegalArgumentException("x value invalid in F2m field element");
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public int getFieldSize() {
            return this.j;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECPoint getInfinity() {
            return this.n;
        }

        public int getK1() {
            return this.k;
        }

        public int getK2() {
            return this.l;
        }

        public int getK3() {
            return this.m;
        }

        public int getM() {
            return this.j;
        }

        public boolean isTrinomial() {
            return this.l == 0 && this.m == 0;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public boolean supportsCoordinateSystem(int i) {
            return i == 0 || i == 1 || i == 6;
        }

        public F2m(int i, int i2, BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4) {
            this(i, i2, 0, 0, bigInteger, bigInteger2, bigInteger3, bigInteger4);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECCurve a() {
            return new F2m(this.j, this.k, this.l, this.m, this.b, this.c, this.d, this.e);
        }

        public F2m(int i, int i2, int i3, int i4, BigInteger bigInteger, BigInteger bigInteger2) {
            this(i, i2, i3, i4, bigInteger, bigInteger2, (BigInteger) null, (BigInteger) null);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            return new ECPoint.F2m(this, eCFieldElement, eCFieldElement2);
        }

        public F2m(int i, int i2, int i3, int i4, BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4) {
            super(i, i2, i3, i4);
            this.j = i;
            this.k = i2;
            this.l = i3;
            this.m = i4;
            this.d = bigInteger3;
            this.e = bigInteger4;
            this.n = new ECPoint.F2m(this, null, null);
            this.b = fromBigInteger(bigInteger);
            this.c = fromBigInteger(bigInteger2);
            this.f = 6;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            return new ECPoint.F2m(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }

        protected F2m(int i, int i2, int i3, int i4, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, BigInteger bigInteger, BigInteger bigInteger2) {
            super(i, i2, i3, i4);
            this.j = i;
            this.k = i2;
            this.l = i3;
            this.m = i4;
            this.d = bigInteger;
            this.e = bigInteger2;
            this.n = new ECPoint.F2m(this, null, null);
            this.b = eCFieldElement;
            this.c = eCFieldElement2;
            this.f = 6;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$Fp.smali */
    public static class Fp extends AbstractFp {
        private static final Set<BigInteger> l = Collections.synchronizedSet(new HashSet());
        private static final f1.a m = new f1.a();
        BigInteger i;
        BigInteger j;
        ECPoint.Fp k;

        public Fp(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
            this(bigInteger, bigInteger2, bigInteger3, null, null);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECCurve a() {
            return new Fp(this.i, this.j, this.b, this.c, this.d, this.e);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement fromBigInteger(BigInteger bigInteger) {
            if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(this.i) >= 0) {
                throw new IllegalArgumentException("x value invalid for Fp field element");
            }
            return new ECFieldElement.Fp(this.i, this.j, bigInteger);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public int getFieldSize() {
            return this.i.bitLength();
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECPoint getInfinity() {
            return this.k;
        }

        public BigInteger getQ() {
            return this.i;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECPoint importPoint(ECPoint eCPoint) {
            int coordinateSystem;
            return (this == eCPoint.getCurve() || getCoordinateSystem() != 2 || eCPoint.isInfinity() || !((coordinateSystem = eCPoint.getCurve().getCoordinateSystem()) == 2 || coordinateSystem == 3 || coordinateSystem == 4)) ? super.importPoint(eCPoint) : new ECPoint.Fp(this, fromBigInteger(eCPoint.b.toBigInteger()), fromBigInteger(eCPoint.c.toBigInteger()), new ECFieldElement[]{fromBigInteger(eCPoint.d[0].toBigInteger())});
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public boolean supportsCoordinateSystem(int i) {
            return i == 0 || i == 1 || i == 2 || i == 4;
        }

        public Fp(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4, BigInteger bigInteger5) {
            this(bigInteger, bigInteger2, bigInteger3, bigInteger4, bigInteger5, false);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            return new ECPoint.Fp(this, eCFieldElement, eCFieldElement2);
        }

        public Fp(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4, BigInteger bigInteger5, boolean z) {
            super(bigInteger);
            if (z) {
                this.i = bigInteger;
                l.add(bigInteger);
            } else {
                if (!l.contains(bigInteger)) {
                    f1.a aVar = m;
                    if (!aVar.b(bigInteger)) {
                        int a = r6.a("bc.org.bouncycastle.ec.fp_max_size", 1042);
                        int a2 = r6.a("bc.org.bouncycastle.ec.fp_certainty", 100);
                        int bitLength = bigInteger.bitLength();
                        if (a >= bitLength) {
                            if (!p6.a(bigInteger) && p6.b(bigInteger, t1.b(), ECCurve.b(bitLength, a2))) {
                                aVar.a(bigInteger);
                                this.i = bigInteger;
                            } else {
                                throw new IllegalArgumentException("Fp q value not prime");
                            }
                        } else {
                            throw new IllegalArgumentException("Fp q value out of range");
                        }
                    }
                }
                this.i = bigInteger;
            }
            this.j = ECFieldElement.Fp.a(bigInteger);
            this.k = new ECPoint.Fp(this, null, null);
            this.b = fromBigInteger(bigInteger2);
            this.c = fromBigInteger(bigInteger3);
            this.d = bigInteger4;
            this.e = bigInteger5;
            this.f = 4;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            return new ECPoint.Fp(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }

        protected Fp(BigInteger bigInteger, BigInteger bigInteger2, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, BigInteger bigInteger3, BigInteger bigInteger4) {
            super(bigInteger);
            this.i = bigInteger;
            this.j = bigInteger2;
            this.k = new ECPoint.Fp(this, null, null);
            this.b = eCFieldElement;
            this.c = eCFieldElement2;
            this.d = bigInteger3;
            this.e = bigInteger4;
            this.f = 4;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ int b;
        final /* synthetic */ byte[] c;

        a(int i, int i2, byte[] bArr) {
            this.a = i;
            this.b = i2;
            this.c = bArr;
        }

        private ECPoint a(byte[] bArr, byte[] bArr2) {
            ECCurve eCCurve = ECCurve.this;
            return eCCurve.a(eCCurve.fromBigInteger(new BigInteger(1, bArr)), ECCurve.this.fromBigInteger(new BigInteger(1, bArr2)));
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            int i2;
            int i3 = this.b;
            byte[] bArr = new byte[i3];
            byte[] bArr2 = new byte[i3];
            int i4 = 0;
            for (int i5 = 0; i5 < this.a; i5++) {
                int i6 = ((i5 ^ i) - 1) >> 31;
                int i7 = 0;
                while (true) {
                    i2 = this.b;
                    if (i7 < i2) {
                        byte b = bArr[i7];
                        byte[] bArr3 = this.c;
                        bArr[i7] = (byte) (b ^ (bArr3[i4 + i7] & i6));
                        bArr2[i7] = (byte) ((bArr3[(i2 + i4) + i7] & i6) ^ bArr2[i7]);
                        i7++;
                    }
                }
                i4 += i2 * 2;
            }
            return a(bArr, bArr2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            int i2 = this.b;
            byte[] bArr = new byte[i2];
            byte[] bArr2 = new byte[i2];
            int i3 = i * i2 * 2;
            int i4 = 0;
            while (true) {
                int i5 = this.b;
                if (i4 >= i5) {
                    return a(bArr, bArr2);
                }
                byte[] bArr3 = this.c;
                bArr[i4] = bArr3[i3 + i4];
                bArr2[i4] = bArr3[i5 + i3 + i4];
                i4++;
            }
        }
    }

    protected ECCurve(r4 r4Var) {
        this.a = r4Var;
    }

    public static int[] getAllCoordinateSystems() {
        return new int[]{0, 1, 2, 3, 4, 5, 6, 7};
    }

    protected abstract ECCurve a();

    protected abstract ECPoint a(int i, BigInteger bigInteger);

    protected abstract ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2);

    protected abstract ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr);

    protected ECMultiplier b() {
        ECEndomorphism eCEndomorphism = this.g;
        return eCEndomorphism instanceof GLVEndomorphism ? new GLVMultiplier(this, (GLVEndomorphism) eCEndomorphism) : new WNafL2RMultiplier();
    }

    public synchronized Config configure() {
        return new Config(this.f, this.g, this.h);
    }

    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        int fieldSize = (getFieldSize() + 7) >>> 3;
        byte[] bArr = new byte[i2 * fieldSize * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            byte[] byteArray = eCPoint.getRawXCoord().toBigInteger().toByteArray();
            byte[] byteArray2 = eCPoint.getRawYCoord().toBigInteger().toByteArray();
            int i5 = 1;
            int i6 = byteArray.length > fieldSize ? 1 : 0;
            int length = byteArray.length - i6;
            if (byteArray2.length <= fieldSize) {
                i5 = 0;
            }
            int length2 = byteArray2.length - i5;
            int i7 = i3 + fieldSize;
            System.arraycopy(byteArray, i6, bArr, i7 - length, length);
            i3 = i7 + fieldSize;
            System.arraycopy(byteArray2, i5, bArr, i3 - length2, length2);
        }
        return new a(i2, fieldSize, bArr);
    }

    public ECPoint createPoint(BigInteger bigInteger, BigInteger bigInteger2) {
        return a(fromBigInteger(bigInteger), fromBigInteger(bigInteger2));
    }

    public ECPoint decodePoint(byte[] bArr) {
        ECPoint infinity;
        int fieldSize = (getFieldSize() + 7) / 8;
        byte b = bArr[0];
        switch (b) {
            case 0:
                if (bArr.length != 1) {
                    throw new IllegalArgumentException("Incorrect length for infinity encoding");
                }
                infinity = getInfinity();
                break;
            case 1:
            case 5:
            default:
                throw new IllegalArgumentException("Invalid point encoding 0x" + Integer.toString(b, 16));
            case 2:
            case 3:
                if (bArr.length != fieldSize + 1) {
                    throw new IllegalArgumentException("Incorrect length for compressed encoding");
                }
                infinity = a(b & 1, f1.a(bArr, 1, fieldSize));
                if (!infinity.a(true, true)) {
                    throw new IllegalArgumentException("Invalid point");
                }
                break;
            case 4:
                if (bArr.length != (fieldSize * 2) + 1) {
                    throw new IllegalArgumentException("Incorrect length for uncompressed encoding");
                }
                infinity = validatePoint(f1.a(bArr, 1, fieldSize), f1.a(bArr, fieldSize + 1, fieldSize));
                break;
            case 6:
            case 7:
                if (bArr.length != (fieldSize * 2) + 1) {
                    throw new IllegalArgumentException("Incorrect length for hybrid encoding");
                }
                BigInteger a2 = f1.a(bArr, 1, fieldSize);
                BigInteger a3 = f1.a(bArr, fieldSize + 1, fieldSize);
                if (a3.testBit(0) != (b == 7)) {
                    throw new IllegalArgumentException("Inconsistent Y coordinate in hybrid encoding");
                }
                infinity = validatePoint(a2, a3);
                break;
        }
        if (b == 0 || !infinity.isInfinity()) {
            return infinity;
        }
        throw new IllegalArgumentException("Invalid infinity encoding");
    }

    public boolean equals(ECCurve eCCurve) {
        return this == eCCurve || (eCCurve != null && getField().equals(eCCurve.getField()) && getA().toBigInteger().equals(eCCurve.getA().toBigInteger()) && getB().toBigInteger().equals(eCCurve.getB().toBigInteger()));
    }

    public abstract ECFieldElement fromBigInteger(BigInteger bigInteger);

    public ECFieldElement getA() {
        return this.b;
    }

    public ECFieldElement getB() {
        return this.c;
    }

    public BigInteger getCofactor() {
        return this.e;
    }

    public int getCoordinateSystem() {
        return this.f;
    }

    public ECEndomorphism getEndomorphism() {
        return this.g;
    }

    public r4 getField() {
        return this.a;
    }

    public abstract int getFieldSize();

    public abstract ECPoint getInfinity();

    public ECMultiplier getMultiplier() {
        if (this.h == null) {
            this.h = b();
        }
        return this.h;
    }

    public BigInteger getOrder() {
        return this.d;
    }

    public PreCompInfo getPreCompInfo(ECPoint eCPoint, String str) {
        Hashtable hashtable;
        PreCompInfo preCompInfo;
        a(eCPoint);
        synchronized (eCPoint) {
            hashtable = eCPoint.e;
        }
        if (hashtable == null) {
            return null;
        }
        synchronized (hashtable) {
            preCompInfo = (PreCompInfo) hashtable.get(str);
        }
        return preCompInfo;
    }

    public int hashCode() {
        return (getField().hashCode() ^ e5.a(getA().toBigInteger().hashCode(), 8)) ^ e5.a(getB().toBigInteger().hashCode(), 16);
    }

    public ECPoint importPoint(ECPoint eCPoint) {
        if (this == eCPoint.getCurve()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return getInfinity();
        }
        ECPoint normalize = eCPoint.normalize();
        return createPoint(normalize.getXCoord().toBigInteger(), normalize.getYCoord().toBigInteger());
    }

    public abstract boolean isValidFieldElement(BigInteger bigInteger);

    public void normalizeAll(ECPoint[] eCPointArr) {
        normalizeAll(eCPointArr, 0, eCPointArr.length, null);
    }

    public PreCompInfo precompute(ECPoint eCPoint, String str, PreCompCallback preCompCallback) {
        Hashtable hashtable;
        PreCompInfo precompute;
        a(eCPoint);
        synchronized (eCPoint) {
            hashtable = eCPoint.e;
            if (hashtable == null) {
                hashtable = new Hashtable(4);
                eCPoint.e = hashtable;
            }
        }
        synchronized (hashtable) {
            PreCompInfo preCompInfo = (PreCompInfo) hashtable.get(str);
            precompute = preCompCallback.precompute(preCompInfo);
            if (precompute != preCompInfo) {
                hashtable.put(str, precompute);
            }
        }
        return precompute;
    }

    public abstract ECFieldElement randomFieldElement(SecureRandom secureRandom);

    public abstract ECFieldElement randomFieldElementMult(SecureRandom secureRandom);

    public boolean supportsCoordinateSystem(int i) {
        return i == 0;
    }

    public ECPoint validatePoint(BigInteger bigInteger, BigInteger bigInteger2) {
        ECPoint createPoint = createPoint(bigInteger, bigInteger2);
        if (createPoint.isValid()) {
            return createPoint;
        }
        throw new IllegalArgumentException("Invalid point coordinates");
    }

    protected void a(ECPoint eCPoint) {
        if (eCPoint == null || this != eCPoint.getCurve()) {
            throw new IllegalArgumentException("'point' must be non-null and on this curve");
        }
    }

    public void normalizeAll(ECPoint[] eCPointArr, int i, int i2, ECFieldElement eCFieldElement) {
        a(eCPointArr, i, i2);
        int coordinateSystem = getCoordinateSystem();
        if (coordinateSystem == 0 || coordinateSystem == 5) {
            if (eCFieldElement != null) {
                throw new IllegalArgumentException("'iso' not valid for affine coordinates");
            }
            return;
        }
        ECFieldElement[] eCFieldElementArr = new ECFieldElement[i2];
        int[] iArr = new int[i2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            int i5 = i + i4;
            ECPoint eCPoint = eCPointArr[i5];
            if (eCPoint != null && (eCFieldElement != null || !eCPoint.isNormalized())) {
                eCFieldElementArr[i3] = eCPoint.getZCoord(0);
                iArr[i3] = i5;
                i3++;
            }
        }
        if (i3 == 0) {
            return;
        }
        ECAlgorithms.montgomeryTrick(eCFieldElementArr, 0, i3, eCFieldElement);
        for (int i6 = 0; i6 < i3; i6++) {
            int i7 = iArr[i6];
            eCPointArr[i7] = eCPointArr[i7].a(eCFieldElementArr[i6]);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$AbstractF2m.smali */
    public static abstract class AbstractF2m extends ECCurve {
        private BigInteger[] i;

        protected AbstractF2m(int i, int i2, int i3, int i4) {
            super(a(i, i2, i3, i4));
            this.i = null;
        }

        private static r4 a(int i, int i2, int i3, int i4) {
            return s4.a((i3 | i4) == 0 ? new int[]{0, i2, i} : new int[]{0, i2, i3, i4, i});
        }

        public static BigInteger inverse(int i, int[] iArr, BigInteger bigInteger) {
            return new bc.org.bouncycastle.math.ec.a(bigInteger).a(i, iArr).g();
        }

        synchronized BigInteger[] c() {
            if (this.i == null) {
                this.i = c.a(this);
            }
            return this.i;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECPoint createPoint(BigInteger bigInteger, BigInteger bigInteger2) {
            ECFieldElement fromBigInteger = fromBigInteger(bigInteger);
            ECFieldElement fromBigInteger2 = fromBigInteger(bigInteger2);
            int coordinateSystem = getCoordinateSystem();
            if (coordinateSystem == 5 || coordinateSystem == 6) {
                if (!fromBigInteger.isZero()) {
                    fromBigInteger2 = fromBigInteger2.divide(fromBigInteger).add(fromBigInteger);
                } else if (!fromBigInteger2.square().equals(getB())) {
                    throw new IllegalArgumentException();
                }
            }
            return a(fromBigInteger, fromBigInteger2);
        }

        public boolean isKoblitz() {
            return this.d != null && this.e != null && this.c.isOne() && (this.b.isZero() || this.b.isOne());
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public boolean isValidFieldElement(BigInteger bigInteger) {
            return bigInteger != null && bigInteger.signum() >= 0 && bigInteger.bitLength() <= getFieldSize();
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
            return fromBigInteger(f1.b(getFieldSize(), secureRandom));
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
            int fieldSize = getFieldSize();
            return fromBigInteger(a(secureRandom, fieldSize)).multiply(fromBigInteger(a(secureRandom, fieldSize)));
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(int i, BigInteger bigInteger) {
            ECFieldElement eCFieldElement;
            ECFieldElement fromBigInteger = fromBigInteger(bigInteger);
            if (fromBigInteger.isZero()) {
                eCFieldElement = getB().sqrt();
            } else {
                ECFieldElement a = a(fromBigInteger.square().invert().multiply(getB()).add(getA()).add(fromBigInteger));
                if (a == null) {
                    eCFieldElement = null;
                } else {
                    if (a.testBitZero() != (i == 1)) {
                        a = a.addOne();
                    }
                    int coordinateSystem = getCoordinateSystem();
                    if (coordinateSystem != 5 && coordinateSystem != 6) {
                        eCFieldElement = a.multiply(fromBigInteger);
                    } else {
                        eCFieldElement = a.add(fromBigInteger);
                    }
                }
            }
            if (eCFieldElement != null) {
                return a(fromBigInteger, eCFieldElement);
            }
            throw new IllegalArgumentException("Invalid point compression");
        }

        protected ECFieldElement a(ECFieldElement eCFieldElement) {
            ECFieldElement eCFieldElement2;
            ECFieldElement.AbstractF2m abstractF2m = (ECFieldElement.AbstractF2m) eCFieldElement;
            boolean hasFastTrace = abstractF2m.hasFastTrace();
            if (hasFastTrace && abstractF2m.trace() != 0) {
                return null;
            }
            int fieldSize = getFieldSize();
            if ((fieldSize & 1) != 0) {
                ECFieldElement halfTrace = abstractF2m.halfTrace();
                if (hasFastTrace || halfTrace.square().add(halfTrace).add(eCFieldElement).isZero()) {
                    return halfTrace;
                }
                return null;
            }
            if (eCFieldElement.isZero()) {
                return eCFieldElement;
            }
            ECFieldElement fromBigInteger = fromBigInteger(ECConstants.ZERO);
            Random random = new Random();
            do {
                ECFieldElement fromBigInteger2 = fromBigInteger(new BigInteger(fieldSize, random));
                ECFieldElement eCFieldElement3 = eCFieldElement;
                eCFieldElement2 = fromBigInteger;
                for (int i = 1; i < fieldSize; i++) {
                    ECFieldElement square = eCFieldElement3.square();
                    eCFieldElement2 = eCFieldElement2.square().add(square.multiply(fromBigInteger2));
                    eCFieldElement3 = square.add(eCFieldElement);
                }
                if (!eCFieldElement3.isZero()) {
                    return null;
                }
            } while (eCFieldElement2.square().add(eCFieldElement2).isZero());
            return eCFieldElement2;
        }

        private static BigInteger a(SecureRandom secureRandom, int i) {
            BigInteger b;
            do {
                b = f1.b(i, secureRandom);
            } while (b.signum() <= 0);
            return b;
        }
    }

    public boolean equals(Object obj) {
        return this == obj || ((obj instanceof ECCurve) && equals((ECCurve) obj));
    }

    protected void a(ECPoint[] eCPointArr, int i, int i2) {
        if (eCPointArr != null) {
            if (i < 0 || i2 < 0 || i > eCPointArr.length - i2) {
                throw new IllegalArgumentException("invalid range specified for 'points'");
            }
            for (int i3 = 0; i3 < i2; i3++) {
                ECPoint eCPoint = eCPointArr[i + i3];
                if (eCPoint != null && this != eCPoint.getCurve()) {
                    throw new IllegalArgumentException("'points' entries must be null or on this curve");
                }
            }
            return;
        }
        throw new IllegalArgumentException("'points' cannot be null");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static int b(int i, int i2) {
        if (i >= 1536) {
            if (i2 <= 100) {
                return 3;
            }
            if (i2 <= 128) {
                return 4;
            }
            return 4 + (((i2 - 128) + 1) / 2);
        }
        if (i >= 1024) {
            if (i2 <= 100) {
                return 4;
            }
            if (i2 <= 112) {
                return 5;
            }
            return (((i2 - Opcodes.IREM) + 1) / 2) + 5;
        }
        if (i < 512) {
            if (i2 <= 80) {
                return 40;
            }
            return 40 + (((i2 - 80) + 1) / 2);
        }
        if (i2 <= 80) {
            return 5;
        }
        if (i2 <= 100) {
            return 7;
        }
        return 7 + (((i2 - 100) + 1) / 2);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECCurve$AbstractFp.smali */
    public static abstract class AbstractFp extends ECCurve {
        protected AbstractFp(BigInteger bigInteger) {
            super(s4.a(bigInteger));
        }

        private static BigInteger b(SecureRandom secureRandom, BigInteger bigInteger) {
            while (true) {
                BigInteger b = f1.b(bigInteger.bitLength(), secureRandom);
                if (b.signum() > 0 && b.compareTo(bigInteger) < 0) {
                    return b;
                }
            }
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        protected ECPoint a(int i, BigInteger bigInteger) {
            ECFieldElement fromBigInteger = fromBigInteger(bigInteger);
            ECFieldElement sqrt = fromBigInteger.square().add(this.b).multiply(fromBigInteger).add(this.c).sqrt();
            if (sqrt == null) {
                throw new IllegalArgumentException("Invalid point compression");
            }
            if (sqrt.testBitZero() != (i == 1)) {
                sqrt = sqrt.negate();
            }
            return a(fromBigInteger, sqrt);
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public boolean isValidFieldElement(BigInteger bigInteger) {
            return bigInteger != null && bigInteger.signum() >= 0 && bigInteger.compareTo(getField().c()) < 0;
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
            BigInteger c = getField().c();
            return fromBigInteger(a(secureRandom, c)).multiply(fromBigInteger(a(secureRandom, c)));
        }

        @Override // bc.org.bouncycastle.math.ec.ECCurve
        public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
            BigInteger c = getField().c();
            return fromBigInteger(b(secureRandom, c)).multiply(fromBigInteger(b(secureRandom, c)));
        }

        private static BigInteger a(SecureRandom secureRandom, BigInteger bigInteger) {
            BigInteger b;
            do {
                b = f1.b(bigInteger.bitLength(), secureRandom);
            } while (b.compareTo(bigInteger) >= 0);
            return b;
        }
    }
}
