package androidx.work;

import android.content.Context;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\DelegatingWorkerFactory.smali */
public class DelegatingWorkerFactory extends WorkerFactory {
    private static final String TAG = Logger.tagWithPrefix("DelegatingWkrFctry");
    private final List<WorkerFactory> mFactories = new CopyOnWriteArrayList();

    List<WorkerFactory> getFactories() {
        return this.mFactories;
    }

    public final void addFactory(WorkerFactory workerFactory) {
        this.mFactories.add(workerFactory);
    }

    @Override // androidx.work.WorkerFactory
    public final ListenableWorker createWorker(Context appContext, String workerClassName, WorkerParameters workerParameters) {
        for (WorkerFactory factory : this.mFactories) {
            try {
                ListenableWorker worker = factory.createWorker(appContext, workerClassName, workerParameters);
                if (worker != null) {
                    return worker;
                }
            } catch (Throwable throwable) {
                String message = "Unable to instantiate a ListenableWorker (" + workerClassName + ")";
                Logger.get().error(TAG, message, throwable);
                throw throwable;
            }
        }
        return null;
    }
}
