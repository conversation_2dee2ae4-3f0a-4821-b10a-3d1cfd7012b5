package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.content.Context;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.samsung.android.sdk.samsungpay.v2.card.AddCardListener;
import com.samsung.android.sdk.samsungpay.v2.card.Card;
import kotlin.text.Typography;
import o.ee.g;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayAddCardListener.smali */
public class SamsungPayAddCardListener implements AddCardListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static long f;
    private static int g;
    private static int h;
    private final String a;
    private final Context c;
    private final a.c d;
    private final c e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        g = 1;
        c();
        int i = h + Opcodes.LUSHR;
        g = i % 128;
        switch (i % 2 == 0 ? (char) 15 : Typography.greater) {
            case '>':
                break;
            default:
                int i2 = 61 / 0;
                break;
        }
    }

    static void c() {
        b = new char[]{50916, 50839, 50858, 50877, 50873, 50876, 50855, 50838, 50837, 50848, 50859, 50862, 50855, 50855, 50873, 50873, 50834, 50839, 50851, 50878, 50849, 50852, 50852, 50854, 50783, 51182, 51155, 51137, 51166, 51178, 51149, 50742, 51177, 51183, 51142, 51137, 51152, 51158, 50740, 50706, 50706, 50704, 50732, 50738, 51177, 51183, 51142, 51139, 51179, 51168, 51174, 51161, 51148, 50743, 51145, 51142, 51150, 51136, 50692, 50801, 50740, 50801, 50816, 50769, 50755, 50762, 50754, 50779, 50768, 50775, 50776, 50778, 50771};
        f = -1343213225013226827L;
    }

    static void init$0() {
        $$a = new byte[]{106, 58, 15, 91};
        $$b = 102;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r8 = r8 + 66
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayAddCardListener.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            int r6 = r6 + 1
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayAddCardListener.k(int, int, byte, java.lang.Object[]):void");
    }

    protected SamsungPayAddCardListener(c cVar, Context context, String str, a.c cVar2) {
        this.e = cVar;
        this.d = cVar2;
        this.c = context;
        this.a = str;
    }

    public void onSuccess(int i, Card card) {
        g.c();
        Object[] objArr = new Object[1];
        i("\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        j("캼컌뷍⿒늀阧糳쮥蝗憾\udc8e쬧巄類\u0a84脆ቤ틎댨忷\ue85fҦ閭ᘟ뻔뼌⟩\uec40眯\uf1f2氷뫸촄⯢髌焀莈戆샠쾮堧钿आ薣", ExpandableListView.getPackedPositionGroup(0L), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        j("舨舄⨜롖巅祲ꉉᔖ쯲\uf66a㎙ᗊᅘ", (Process.getThreadPriority(0) + 20) >> 6, objArr3);
        g.d(intern, append.append(((String) objArr3[0]).intern()).append(card.toString()).toString());
        c cVar = this.e;
        this.d.a(cVar.a(this.c, cVar.e(), card.getCardId(), this.a, null, o.ep.d.c).c());
        int i2 = g + 93;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:47:0x01b2, code lost:
    
        if (r18.getInt(r3) != (-515)) goto L45;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void onFail(int r17, android.os.Bundle r18) {
        /*
            Method dump skipped, instructions count: 568
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayAddCardListener.onFail(int, android.os.Bundle):void");
    }

    public void onProgress(int i, int i2, Bundle bundle) {
        g.c();
        Object[] objArr = new Object[1];
        i("\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        j("틦튖吶옩᪴㸓\u0ef0릦鬍衅璺뤤䆞熥ꊰ\uf305\u0e3e㬻ᬖⷀ\uf434\ued53冧搊ꋋ囷进鸀歵᠍쐜죤턜쉕㊻̛龆诵棓뷸䑭紅ꅤ\uf7ec㈓⚨\u1ff0⹒\uf8fe", (Process.getThreadPriority(0) + 20) >> 6, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        j("⇍⇭㻓겖䶆楲鮜", TextUtils.getOffsetAfter("", 0), objArr3);
        g.d(intern, append.append(((String) objArr3[0]).intern()).append(i2).toString());
        int i3 = g + 57;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? 'M' : (char) 20) {
            case 20:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:123:0x0366, code lost:
    
        r2 = r0;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v26, types: [byte[]] */
    /* JADX WARN: Type inference failed for: r0v3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(java.lang.String r20, int[] r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 940
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayAddCardListener.i(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 372
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayAddCardListener.j(java.lang.String, int, java.lang.Object[]):void");
    }
}
