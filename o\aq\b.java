package o.aq;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.a.h;
import o.bb.e;
import o.cf.i;
import o.ee.g;
import o.ei.c;
import o.eo.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aq\b.smali */
public final class b extends o.y.b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static int e;
    List<j> b;
    String d;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aq\b$d.smali */
    public interface d {
        void c(o.bb.d dVar);

        void e(List<j> list);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        o();
        Gravity.getAbsoluteGravity(0, 0);
        ViewConfiguration.getJumpTapTimeout();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        TextUtils.getCapsMode("", 0, 0);
        ViewConfiguration.getScrollFriction();
        TextUtils.getTrimmedLength("");
        int i = a + 19;
        c = i % 128;
        switch (i % 2 != 0 ? (char) 18 : 'U') {
            case 18:
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$d = new byte[]{116, -79, 3, -53};
        $$e = 202;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 4
            int r6 = r6 * 2
            int r6 = r6 + 107
            byte[] r0 = o.aq.b.$$d
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r6 = -r6
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aq.b.m(byte, int, byte, java.lang.Object[]):void");
    }

    static void o() {
        e = 874635292;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        a k;
        int i = c + 61;
        a = i % 128;
        switch (i % 2 == 0 ? '@' : (char) 30) {
            case 30:
                k = k();
                break;
            default:
                k = k();
                int i2 = 85 / 0;
                break;
        }
        int i3 = a + 35;
        c = i3 % 128;
        int i4 = i3 % 2;
        return k;
    }

    public b(Context context, d dVar, c cVar) {
        super(context, dVar, cVar, e.t);
    }

    public final void a(String str) {
        g.c();
        Object[] objArr = new Object[1];
        l(View.resolveSizeAndState(0, 0, 0) + 15, "￡\uffff\u0010\u0002\ufff4\u0001\f\u0011￡\r\u000b\u000b\uffff\f\u0002￥\u0003\u0012", TextUtils.getCapsMode("", 0, 0) + 18, 196 - TextUtils.indexOf((CharSequence) "", '0'), false, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        l(AndroidCharacter.getMirror('0') - '\'', "ￚￍ\u0010\u000e\u001f\u0011ￍ\uffe7ￍ\u0011\u001c\ufff4\u0012!\ufff0\u000e\u001f\u0011\u0003\u0010\u001b ￍ", 23 - (ViewConfiguration.getTouchSlop() >> 8), 182 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), false, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        this.d = str;
        c();
        int i = a + 55;
        c = i % 128;
        int i2 = i % 2;
    }

    private a k() {
        a aVar = new a(this);
        int i = c + 1;
        a = i % 128;
        int i2 = i % 2;
        return aVar;
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = c + 35;
        a = i % 128;
        switch (i % 2 == 0 ? (char) 7 : (char) 20) {
            case 20:
                Object[] objArr = new Object[1];
                l((Process.myPid() >> 22) + 15, "￡\uffff\u0010\u0002\ufff4\u0001\f\u0011￡\r\u000b\u000b\uffff\f\u0002￥\u0003\u0012", TextUtils.getOffsetBefore("", 0) + 18, 198 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                l(88 % (Process.myPid() << 35), "￡\uffff\u0010\u0002\ufff4\u0001\f\u0011￡\r\u000b\u000b\uffff\f\u0002￥\u0003\u0012", 93 >> TextUtils.getOffsetBefore("", 1), 24047 - (Process.getElapsedCpuTime() > 1L ? 1 : (Process.getElapsedCpuTime() == 1L ? 0 : -1)), true, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = a + 49;
        c = i2 % 128;
        switch (i2 % 2 != 0 ? '\t' : (char) 11) {
            case '\t':
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aq\b$a.smali */
    static final class a extends o.y.c<b> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static int b;
        private static byte[] c;
        private static short[] d;
        private static int e;
        private static int f;
        private static int g;
        private static char h;
        private static long i;
        private static int j;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            f = 1;
            c = new byte[]{58, 32, 34, 73, 73, 38, 85, 100, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 117, 88, -30, -15, 46, -3, -114, -70, -116, -30, -27, -48, 19, -5, -98, -121, -64, -76, 25, -114, -117, -74, -4, 37, 35, ByteCompanionObject.MAX_VALUE, -85, -44, 96, -75, -126, -47, -60, 101, 68, -26, -24, 94, 18, 28, 76, 75, -26, 111, -127, 76, -104, 58, 25, -17, 1, 6, -13, 39, 11, 20, 21, 6, 2, -26, -3, 38, -29, 2, 11, 109, 103, 53, ByteCompanionObject.MAX_VALUE, 117, ByteCompanionObject.MAX_VALUE, 5, 7, 117, 117, 40, -111, 14, -112, -112, -112, -112, -112, -112, -112, -112, -112};
            e = 909053657;
            a = 2074605261;
            b = 1846214191;
            h = (char) 13509;
            g = 161105445;
            i = 6565854932352255525L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(int r6, short r7, byte r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 3
                int r6 = r6 + 1
                byte[] r0 = o.aq.b.a.$$d
                int r7 = r7 * 4
                int r7 = 4 - r7
                int r8 = r8 + 99
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L33
            L1a:
                r3 = r2
            L1b:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                r3 = r0[r7]
                r5 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L33:
                int r7 = -r7
                int r7 = r7 + r9
                int r8 = r8 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aq.b.a.C(int, short, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{45, -21, -97, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
            $$e = 243;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i2 = j + 33;
            f = i2 % 128;
            int i3 = i2 % 2;
        }

        a(b bVar) {
            super(bVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i2 = f + 93;
            j = i2 % 128;
            int i3 = i2 % 2;
            Object[] objArr = new Object[1];
            w((byte) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 96), (-1478881471) - View.MeasureSpec.makeMeasureSpec(0, 0), (short) ((-70) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), View.MeasureSpec.getSize(0) - 62, TextUtils.indexOf("", "", 0) - 1300822006, objArr);
            String intern = ((String) objArr[0]).intern();
            int i4 = j + 73;
            f = i4 % 128;
            switch (i4 % 2 == 0 ? (char) 31 : Typography.greater) {
                case 31:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return intern;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            B(1127513630 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "\ueaa6㍊ᝌ튽椩푮榊㨱\ue0daㆡ젾∟\ueaa7홣獎ᆀ㷕࿔\ueea4", (char) (21655 - View.MeasureSpec.getMode(0)), "Ợ㑾靃큔", "\u0000\u0000\u0000\u0000", objArr);
            o.cf.d dVar = new o.cf.d(context, 37, ((String) objArr[0]).intern());
            int i2 = f + Opcodes.LSHL;
            j = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return dVar;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w((byte) ((-80) - TextUtils.lastIndexOf("", '0', 0, 0)), (-1478881461) - TextUtils.getOffsetAfter("", 0), (short) ((-31) - ImageFormat.getBitsPerPixel(0)), (-66) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (-1300822009) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
            bVar.d(((String) objArr[0]).intern(), ((b) e()).d);
            int i2 = f + 9;
            j = i2 % 128;
            switch (i2 % 2 != 0 ? 'H' : (char) 3) {
                case 3:
                    return bVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final o.cf.j n() {
            int i2 = j + 67;
            f = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i2 = f + 39;
            int i3 = i2 % 128;
            j = i3;
            int i4 = i2 % 2;
            int i5 = i3 + 85;
            f = i5 % 128;
            int i6 = i5 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i2) {
            switch (i2) {
                case 5001:
                    o.bb.a aVar = o.bb.a.ay;
                    int i3 = j + 55;
                    f = i3 % 128;
                    switch (i3 % 2 == 0 ? '\\' : '\t') {
                        case '\t':
                            return aVar;
                        default:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                    }
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i2);
                    int i4 = f + 71;
                    j = i4 % 128;
                    switch (i4 % 2 == 0) {
                        case false:
                            int i5 = 89 / 0;
                            return c2;
                        default:
                            return c2;
                    }
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:38:0x034f, code lost:
        
            if (r6.getTime() < r5.getTime()) goto L41;
         */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void c(o.eg.b r35) throws o.eg.d {
            /*
                Method dump skipped, instructions count: 938
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aq.b.a.c(o.eg.b):void");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i2 = f + 97;
            j = i2 % 128;
            int i3 = i2 % 2;
            switch (AnonymousClass4.c[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((b) e()).d);
                    int i4 = j + 31;
                    f = i4 % 128;
                    int i5 = i4 % 2;
                    break;
                case 2:
                    f().e(g(), ((b) e()).d);
                    break;
                default:
                    super.t();
                    int i6 = f + 35;
                    j = i6 % 128;
                    int i7 = i6 % 2;
                    break;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i2 = j + 65;
            f = i2 % 128;
            switch (i2 % 2 == 0 ? 'C' : '[') {
                case Opcodes.DUP_X2 /* 91 */:
                    ((b) e()).j().e(((b) e()).b);
                    break;
                default:
                    ((b) e()).j().e(((b) e()).b);
                    int i3 = 25 / 0;
                    break;
            }
            int i4 = f + Opcodes.DMUL;
            j = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i2 = j + 27;
            f = i2 % 128;
            switch (i2 % 2 == 0 ? 'C' : 'T') {
                case Opcodes.BASTORE /* 84 */:
                    ((b) e()).j().c(dVar);
                    break;
                default:
                    ((b) e()).j().c(dVar);
                    int i3 = 17 / 0;
                    break;
            }
            int i4 = f + Opcodes.LUSHR;
            j = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX WARN: Code restructure failed: missing block: B:114:0x0374, code lost:
        
            r3 = r7;
         */
        /* JADX WARN: Code restructure failed: missing block: B:22:0x00aa, code lost:
        
            if (r2 != null) goto L32;
         */
        /* JADX WARN: Code restructure failed: missing block: B:23:0x0140, code lost:
        
            if (r2 == null) goto L52;
         */
        /* JADX WARN: Code restructure failed: missing block: B:24:0x0142, code lost:
        
            r2 = true;
         */
        /* JADX WARN: Code restructure failed: missing block: B:25:0x0145, code lost:
        
            switch(r2) {
                case 1: goto L55;
                default: goto L54;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:26:0x0148, code lost:
        
            r2 = (short) (((short) (o.aq.b.a.d[r18 + ((int) (o.aq.b.a.b ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (o.aq.b.a.e ^ (-5810760824076169584L))));
         */
        /* JADX WARN: Code restructure failed: missing block: B:27:0x0165, code lost:
        
            r2 = o.aq.b.a.$11 + 85;
            o.aq.b.a.$10 = r2 % 128;
         */
        /* JADX WARN: Code restructure failed: missing block: B:28:0x016f, code lost:
        
            if ((r2 % 2) == 0) goto L58;
         */
        /* JADX WARN: Code restructure failed: missing block: B:29:0x0171, code lost:
        
            r2 = true;
         */
        /* JADX WARN: Code restructure failed: missing block: B:31:0x0176, code lost:
        
            switch(r2) {
                case 1: goto L62;
                default: goto L61;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:32:0x0179, code lost:
        
            r2 = o.aq.b.a.c;
         */
        /* JADX WARN: Code restructure failed: missing block: B:35:0x0212, code lost:
        
            r10 = new java.lang.Object[]{java.lang.Integer.valueOf(r18), java.lang.Integer.valueOf(o.aq.b.a.b)};
            r7 = o.e.a.s.get(-2120899312);
         */
        /* JADX WARN: Code restructure failed: missing block: B:36:0x022d, code lost:
        
            if (r7 == null) goto L78;
         */
        /* JADX WARN: Code restructure failed: missing block: B:39:0x0286, code lost:
        
            r2 = ((byte) (r2[((java.lang.Integer) ((java.lang.reflect.Method) r7).invoke(null, r10)).intValue()] ^ (-5810760824076169584L))) + ((int) (o.aq.b.a.e ^ (-5810760824076169584L)));
         */
        /* JADX WARN: Code restructure failed: missing block: B:40:0x0297, code lost:
        
            r2 = (byte) r2;
         */
        /* JADX WARN: Code restructure failed: missing block: B:42:0x0230, code lost:
        
            r3 = (java.lang.Class) o.e.a.c(11 - (android.view.ViewConfiguration.getScrollBarFadeDuration() >> 16), (char) ((-1) - (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) == 0 ? 0 : -1))), 65 - android.graphics.Color.red(0));
            r4 = (byte) 0;
            r7 = r4;
            r12 = new java.lang.Object[1];
            C(r4, r7, (byte) (r7 | 9), r12);
            r7 = r3.getMethod((java.lang.String) r12[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
            o.e.a.s.put(-2120899312, r7);
         */
        /* JADX WARN: Code restructure failed: missing block: B:43:0x0299, code lost:
        
            r0 = move-exception;
         */
        /* JADX WARN: Code restructure failed: missing block: B:44:0x029a, code lost:
        
            r1 = r0.getCause();
         */
        /* JADX WARN: Code restructure failed: missing block: B:45:0x029e, code lost:
        
            if (r1 != null) goto L85;
         */
        /* JADX WARN: Code restructure failed: missing block: B:46:0x02a0, code lost:
        
            throw r1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:48:0x02a1, code lost:
        
            throw r0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:49:0x017f, code lost:
        
            r2 = o.aq.b.a.c;
         */
        /* JADX WARN: Code restructure failed: missing block: B:51:0x0184, code lost:
        
            r10 = new java.lang.Object[]{java.lang.Integer.valueOf(r18), java.lang.Integer.valueOf(o.aq.b.a.b)};
            r7 = o.e.a.s.get(-2120899312);
         */
        /* JADX WARN: Code restructure failed: missing block: B:52:0x019f, code lost:
        
            if (r7 == null) goto L66;
         */
        /* JADX WARN: Code restructure failed: missing block: B:55:0x01f5, code lost:
        
            r2 = ((byte) (r2[((java.lang.Integer) ((java.lang.reflect.Method) r7).invoke(null, r10)).intValue()] ^ (-5810760824076169584L))) << ((int) (o.aq.b.a.e - (-5810760824076169584L)));
         */
        /* JADX WARN: Code restructure failed: missing block: B:57:0x01a2, code lost:
        
            r3 = (java.lang.Class) o.e.a.c((android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 11, (char) android.widget.ExpandableListView.getPackedPositionGroup(0), 65 - android.view.View.MeasureSpec.getMode(0));
            r4 = (byte) 0;
            r7 = r4;
            r12 = new java.lang.Object[1];
            C(r4, r7, (byte) (r7 | 9), r12);
            r7 = r3.getMethod((java.lang.String) r12[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
            o.e.a.s.put(-2120899312, r7);
         */
        /* JADX WARN: Code restructure failed: missing block: B:58:0x0208, code lost:
        
            r0 = move-exception;
         */
        /* JADX WARN: Code restructure failed: missing block: B:59:0x0209, code lost:
        
            r1 = r0.getCause();
         */
        /* JADX WARN: Code restructure failed: missing block: B:60:0x020d, code lost:
        
            if (r1 != null) goto L72;
         */
        /* JADX WARN: Code restructure failed: missing block: B:61:0x020f, code lost:
        
            throw r1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:62:0x0210, code lost:
        
            throw r0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:63:0x0173, code lost:
        
            r2 = false;
         */
        /* JADX WARN: Code restructure failed: missing block: B:64:0x0144, code lost:
        
            r2 = false;
         */
        /* JADX WARN: Code restructure failed: missing block: B:65:0x00b4, code lost:
        
            r15 = r2.length;
            r4 = new byte[r15];
            r13 = 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:66:0x00b8, code lost:
        
            if (r13 >= r15) goto L35;
         */
        /* JADX WARN: Code restructure failed: missing block: B:67:0x00ba, code lost:
        
            r14 = 'D';
         */
        /* JADX WARN: Code restructure failed: missing block: B:68:0x00bf, code lost:
        
            switch(r14) {
                case 68: goto L38;
                default: goto L155;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:71:0x00c7, code lost:
        
            r9 = new java.lang.Object[]{java.lang.Integer.valueOf(r2[r13])};
            r7 = o.e.a.s.get(494867332);
         */
        /* JADX WARN: Code restructure failed: missing block: B:72:0x00dc, code lost:
        
            if (r7 == null) goto L42;
         */
        /* JADX WARN: Code restructure failed: missing block: B:75:0x0129, code lost:
        
            r4[r13] = ((java.lang.Byte) ((java.lang.reflect.Method) r7).invoke(null, r9)).byteValue();
            r13 = r13 + 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:76:0x00df, code lost:
        
            r7 = (java.lang.Class) o.e.a.c(19 - (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (android.view.View.MeasureSpec.getMode(0) + 16425), 150 - android.graphics.Color.argb(0, 0, 0, 0));
            r12 = (byte) 0;
            r14 = r12;
            r10 = new java.lang.Object[1];
            C(r12, r14, (byte) (r14 | 11), r10);
            r7 = r7.getMethod((java.lang.String) r10[0], java.lang.Integer.TYPE);
            o.e.a.s.put(494867332, r7);
         */
        /* JADX WARN: Code restructure failed: missing block: B:78:0x0137, code lost:
        
            r0 = move-exception;
         */
        /* JADX WARN: Code restructure failed: missing block: B:79:0x0138, code lost:
        
            r1 = r0.getCause();
         */
        /* JADX WARN: Code restructure failed: missing block: B:80:0x013c, code lost:
        
            if (r1 != null) goto L48;
         */
        /* JADX WARN: Code restructure failed: missing block: B:81:0x013e, code lost:
        
            throw r1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:82:0x013f, code lost:
        
            throw r0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:84:0x00c2, code lost:
        
            r2 = r4;
         */
        /* JADX WARN: Code restructure failed: missing block: B:85:0x00bd, code lost:
        
            r14 = '4';
         */
        /* JADX WARN: Code restructure failed: missing block: B:91:0x00b2, code lost:
        
            if (r2 != null) goto L32;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void w(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 1072
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aq.b.a.w(byte, int, short, int, int, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void B(int r23, java.lang.String r24, char r25, java.lang.String r26, java.lang.String r27, java.lang.Object[] r28) {
            /*
                Method dump skipped, instructions count: 684
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aq.b.a.B(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.aq.b$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aq\b$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        static final /* synthetic */ int[] c;
        private static int e;

        static {
            e = 0;
            b = 1;
            int[] iArr = new int[o.bb.a.values().length];
            c = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = e;
                int i2 = (i & 87) + (i | 87);
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[o.bb.a.az.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ Opcodes.LMUL) + ((i3 & Opcodes.LMUL) << 1);
                e = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    private static void l(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] charArray = str != null ? str.toCharArray() : str;
        h hVar = new h();
        char[] cArr = new char[i2];
        hVar.a = 0;
        while (true) {
            if (hVar.a >= i2) {
                break;
            }
            int i4 = $10 + 87;
            $11 = i4 % 128;
            int i5 = i4 % 2;
            hVar.b = charArray[hVar.a];
            cArr[hVar.a] = (char) (i3 + hVar.b);
            int i6 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr[i6]), Integer.valueOf(e)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(12 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), Color.rgb(0, 0, 0) + 16777675);
                    byte b = (byte) 0;
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    m(b, b2, b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(TextUtils.getCapsMode("", 0, 0) + 11, (char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 313);
                        byte b3 = (byte) 1;
                        byte b4 = (byte) (b3 - 1);
                        Object[] objArr5 = new Object[1];
                        m(b3, b4, b4, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            hVar.c = i;
            char[] cArr2 = new char[i2];
            System.arraycopy(cArr, 0, cArr2, 0, i2);
            System.arraycopy(cArr2, 0, cArr, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr2, hVar.c, cArr, 0, i2 - hVar.c);
        }
        if (z) {
            int i7 = $10 + 109;
            $11 = i7 % 128;
            int i8 = i7 % 2;
            char[] cArr3 = new char[i2];
            hVar.a = 0;
            while (true) {
                switch (hVar.a < i2 ? '8' : 'Z') {
                    case 'Z':
                        cArr = cArr3;
                        break;
                    default:
                        int i9 = $10 + Opcodes.LREM;
                        $11 = i9 % 128;
                        if (i9 % 2 == 0) {
                        }
                        cArr3[hVar.a] = cArr[(i2 - hVar.a) - 1];
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj3 = o.e.a.s.get(-1412673904);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 313);
                                byte b5 = (byte) 1;
                                byte b6 = (byte) (b5 - 1);
                                Object[] objArr7 = new Object[1];
                                m(b5, b6, b6, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                }
            }
        }
        String str2 = new String(cArr);
        int i10 = $11 + 95;
        $10 = i10 % 128;
        switch (i10 % 2 != 0) {
            case false:
                objArr[0] = str2;
                return;
            default:
                Object obj4 = null;
                obj4.hashCode();
                throw null;
        }
    }
}
