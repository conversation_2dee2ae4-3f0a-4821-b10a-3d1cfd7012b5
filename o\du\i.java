package o.du;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\i.smali */
public final class i implements d<o.dy.d, o.dx.d, o.dq.e, Drawable> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static long e;
    private final o.dx.d d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        g();
        ViewConfiguration.getJumpTapTimeout();
        int i = c + Opcodes.LMUL;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                break;
            default:
                int i2 = 97 / 0;
                break;
        }
    }

    static void g() {
        e = 1608195332635339614L;
    }

    static void init$0() {
        $$a = new byte[]{54, 120, -7, 107};
        $$b = 40;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = 114 - r8
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r0 = o.du.i.$$a
            int r6 = r6 * 4
            int r6 = 4 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.i.j(byte, byte, byte, java.lang.Object[]):void");
    }

    @Override // o.du.d
    public final /* synthetic */ o.dq.e a() {
        int i = a + 47;
        c = i % 128;
        switch (i % 2 != 0 ? (char) 4 : 'Z') {
            case 'Z':
                return h();
            default:
                int i2 = 85 / 0;
                return h();
        }
    }

    @Override // o.du.d
    public final /* synthetic */ o.dy.d b() {
        int i = c + Opcodes.DREM;
        a = i % 128;
        int i2 = i % 2;
        o.dy.d f = f();
        int i3 = c + 85;
        a = i3 % 128;
        int i4 = i3 % 2;
        return f;
    }

    @Override // o.du.d
    public final /* synthetic */ o.dx.d d() {
        int i = c + Opcodes.LSHR;
        a = i % 128;
        int i2 = i % 2;
        o.dx.d e2 = e();
        int i3 = c + 77;
        a = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return e2;
            default:
                throw null;
        }
    }

    public i(String str) {
        this.d = new o.dx.d(str);
    }

    private static o.dy.d f() {
        int i = c + 71;
        a = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                o.dy.d dVar = o.dy.d.a;
                throw null;
            default:
                o.dy.d dVar2 = o.dy.d.a;
                int i2 = a + Opcodes.LUSHR;
                c = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return dVar2;
                }
        }
    }

    public final o.dx.d e() {
        o.dx.d dVar;
        int i = a + 67;
        int i2 = i % 128;
        c = i2;
        switch (i % 2 != 0 ? (char) 28 : (char) 31) {
            case 31:
                dVar = this.d;
                break;
            default:
                dVar = this.d;
                int i3 = 53 / 0;
                break;
        }
        int i4 = i2 + Opcodes.LSHL;
        a = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    private static o.dq.e h() {
        o.dq.e eVar;
        int i = a + 85;
        c = i % 128;
        switch (i % 2 != 0 ? '\\' : '7') {
            case '7':
                eVar = o.dq.e.e;
                break;
            default:
                eVar = o.dq.e.e;
                int i2 = 12 / 0;
                break;
        }
        int i3 = a + 21;
        c = i3 % 128;
        int i4 = i3 % 2;
        return eVar;
    }

    @Override // o.du.d
    public final String c() {
        int i = a;
        int i2 = i + 59;
        c = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 41;
        c = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i("涚ᒘ鿗\u0600襄㎁뫽㴰ꑳ⺪톇壉쌍䩙천矶︹慨\ueb9e銐ᗗ鰌ݜ覴ツ묺≷꒢⾇훉夃쁌䪕췰琹ｱ憞\ue88a鏈ᨄ鴛", 31032 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(f().toString());
        Object[] objArr2 = new Object[1];
        i("淢ᅯ钫ᠨ龾̘蚧਼覴ത낡㑸", 31873 - Color.green(0), objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(e().toString());
        Object[] objArr3 = new Object[1];
        i("淢豛껃좴\ueb6eԃ➕䙞怉苷벹\udf7b憐", 57781 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(h().toString()).append('}').toString();
        int i = c + 11;
        a = i % 128;
        switch (i % 2 != 0) {
            case true:
                return obj;
            default:
                int i2 = 71 / 0;
                return obj;
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        Object obj2 = null;
        switch (obj != null ? 'F' : ':') {
            case Opcodes.ASTORE /* 58 */:
                break;
            default:
                int i = a + 55;
                c = i % 128;
                switch (i % 2 == 0) {
                    case false:
                        getClass();
                        obj.getClass();
                        obj2.hashCode();
                        throw null;
                    default:
                        switch (getClass() != obj.getClass()) {
                            case false:
                                return Objects.equals(this.d, ((i) obj).d);
                        }
                }
        }
        int i2 = c + 11;
        a = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                obj2.hashCode();
                throw null;
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 720
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.i.i(java.lang.String, int, java.lang.Object[]):void");
    }
}
