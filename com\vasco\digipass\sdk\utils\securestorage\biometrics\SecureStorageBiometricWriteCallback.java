package com.vasco.digipass.sdk.utils.securestorage.biometrics;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\b\u0010\u0006\u001a\u00020\u0003H&¨\u0006\u0007"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/biometrics/SecureStorageBiometricWriteCallback;", "", "onWriteFailed", "", "secureStorageSDKException", "Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorageSDKException;", "onWriteSuccess", "lib_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\biometrics\SecureStorageBiometricWriteCallback.smali */
public interface SecureStorageBiometricWriteCallback {
    void onWriteFailed(SecureStorageSDKException secureStorageSDKException);

    void onWriteSuccess();
}
