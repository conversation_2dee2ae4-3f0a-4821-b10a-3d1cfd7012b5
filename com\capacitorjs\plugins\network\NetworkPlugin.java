package com.capacitorjs.plugins.network;

import android.util.Log;
import com.capacitorjs.plugins.network.Network;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "Network")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\NetworkPlugin.smali */
public class NetworkPlugin extends Plugin {
    public static final String NETWORK_CHANGE_EVENT = "networkStatusChange";
    private Network implementation;
    private NetworkStatus prePauseNetworkStatus = null;

    @Override // com.getcapacitor.Plugin
    public void load() {
        this.implementation = new Network(getContext());
        Network.NetworkStatusChangeListener listener = new Network.NetworkStatusChangeListener() { // from class: com.capacitorjs.plugins.network.NetworkPlugin$$ExternalSyntheticLambda0
            @Override // com.capacitorjs.plugins.network.Network.NetworkStatusChangeListener
            public final void onNetworkStatusChanged(boolean z) {
                NetworkPlugin.this.lambda$load$0(z);
            }
        };
        this.implementation.setStatusChangeListener(listener);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$load$0(boolean wasLostEvent) {
        if (wasLostEvent) {
            JSObject jsObject = new JSObject();
            jsObject.put("connected", false);
            jsObject.put("connectionType", "none");
            notifyListeners(NETWORK_CHANGE_EVENT, jsObject);
            return;
        }
        updateNetworkStatus();
    }

    @Override // com.getcapacitor.Plugin
    protected void handleOnDestroy() {
        this.implementation.setStatusChangeListener(null);
    }

    @PluginMethod
    public void getStatus(PluginCall call) {
        call.resolve(parseNetworkStatus(this.implementation.getNetworkStatus()));
    }

    @Override // com.getcapacitor.Plugin
    protected void handleOnResume() {
        this.implementation.startMonitoring();
        NetworkStatus afterPauseNetworkStatus = this.implementation.getNetworkStatus();
        if (this.prePauseNetworkStatus != null && !afterPauseNetworkStatus.connected && (this.prePauseNetworkStatus.connected || afterPauseNetworkStatus.connectionType != this.prePauseNetworkStatus.connectionType)) {
            Log.d("Capacitor/NetworkPlugin", "Detected pre-pause and after-pause network status mismatch. Updating network status and notifying listeners.");
            updateNetworkStatus();
        }
        this.prePauseNetworkStatus = null;
    }

    @Override // com.getcapacitor.Plugin
    protected void handleOnPause() {
        this.prePauseNetworkStatus = this.implementation.getNetworkStatus();
        this.implementation.stopMonitoring();
    }

    private void updateNetworkStatus() {
        notifyListeners(NETWORK_CHANGE_EVENT, parseNetworkStatus(this.implementation.getNetworkStatus()));
    }

    private JSObject parseNetworkStatus(NetworkStatus networkStatus) {
        JSObject jsObject = new JSObject();
        jsObject.put("connected", networkStatus.connected);
        jsObject.put("connectionType", networkStatus.connectionType.getConnectionType());
        return jsObject;
    }
}
