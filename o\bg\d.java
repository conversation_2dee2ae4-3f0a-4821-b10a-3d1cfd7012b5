package o.bg;

import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.WalletLockReason;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static final /* synthetic */ d[] c;
    public static final d d;
    public static final d e;
    private static int h;
    private static int j;
    private final int a;

    static void b() {
        b = 1861550494126711963L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = 3 - r6
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 114 - r7
            byte[] r0 = o.bg.d.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bg.d.g(int, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{1, 25, 123, 58};
        $$b = 96;
    }

    private static /* synthetic */ d[] e() {
        int i = j;
        int i2 = i + 29;
        h = i2 % 128;
        int i3 = i2 % 2;
        d[] dVarArr = {d, e};
        int i4 = i + 13;
        h = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 21 : 'H') {
            case 21:
                int i5 = 78 / 0;
                return dVarArr;
            default:
                return dVarArr;
        }
    }

    public static d valueOf(String str) {
        int i = h + 99;
        j = i % 128;
        char c2 = i % 2 == 0 ? Typography.greater : '\f';
        d dVar = (d) Enum.valueOf(d.class, str);
        switch (c2) {
            case '>':
                throw null;
            default:
                int i2 = h + 53;
                j = i2 % 128;
                int i3 = i2 % 2;
                return dVar;
        }
    }

    public static d[] values() {
        int i = h + 19;
        j = i % 128;
        int i2 = i % 2;
        d[] dVarArr = (d[]) c.clone();
        int i3 = h + 45;
        j = i3 % 128;
        switch (i3 % 2 == 0 ? '(' : (char) 15) {
            case '(':
                int i4 = 17 / 0;
                return dVarArr;
            default:
                return dVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        b();
        Object[] objArr = new Object[1];
        f("\ue258Ь⻟傝笩鷳螜긽탧画ᵃ߷⦮偈竵鲳蝖꧸펩視ᰘڱ⥇匊疆鱨蘆ꢯ퍺\uf508\u1fd4٢", ((byte) KeyEvent.getModifierMetaStateMask()) + 58964, objArr);
        d = new d(((String) objArr[0]).intern(), 0, 1000);
        Object[] objArr2 = new Object[1];
        f("\ue244\ueb62\uf059魯옍쿈퓀\udda1ꪐ끡륇", (ViewConfiguration.getJumpTapTimeout() >> 16) + 2333, objArr2);
        e = new d(((String) objArr2[0]).intern(), 1, 9999);
        c = e();
        int i = h + 3;
        j = i % 128;
        int i2 = i % 2;
    }

    private d(String str, int i, int i2) {
        this.a = i2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.bg.d a(int r6) {
        /*
            int r0 = o.bg.d.h
            int r0 = r0 + 97
            int r1 = r0 % 128
            o.bg.d.j = r1
            int r0 = r0 % 2
            r1 = 6
            if (r0 != 0) goto L10
            r0 = 36
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 6: goto L1b;
                default: goto L14;
            }
        L14:
            o.bg.d[] r0 = values()
            int r2 = r0.length
            r3 = 1
            goto L21
        L1b:
            o.bg.d[] r0 = values()
            int r2 = r0.length
            r3 = 0
        L21:
            if (r3 >= r2) goto L26
            r4 = r1
            goto L27
        L26:
            r4 = 3
        L27:
            switch(r4) {
                case 6: goto L2d;
                default: goto L2a;
            }
        L2a:
            o.bg.d r6 = o.bg.d.e
            goto L49
        L2d:
            r4 = r0[r3]
            int r5 = r4.a
            if (r6 != r5) goto L36
            r5 = 15
            goto L38
        L36:
            r5 = 76
        L38:
            switch(r5) {
                case 15: goto L3e;
                default: goto L3b;
            }
        L3b:
            int r3 = r3 + 1
            goto L21
        L3e:
            int r6 = o.bg.d.h
            int r6 = r6 + 43
            int r0 = r6 % 128
            o.bg.d.j = r0
            int r6 = r6 % 2
            return r4
        L49:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bg.d.a(int):o.bg.d");
    }

    /* renamed from: o.bg.d$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\d$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] c;

        static {
            a = 0;
            b = 1;
            int[] iArr = new int[d.values().length];
            c = iArr;
            try {
                iArr[d.d.ordinal()] = 1;
                int i = b;
                int i2 = (i ^ Opcodes.LREM) + ((i & Opcodes.LREM) << 1);
                a = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                c[d.e.ordinal()] = 2;
                int i3 = (a + 44) - 1;
                b = i3 % 128;
                switch (i3 % 2 == 0 ? '%' : 'N') {
                    case 'N':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    public final WalletLockReason d() {
        int i = j + Opcodes.LUSHR;
        h = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? 'W' : '%') {
            case Opcodes.POP /* 87 */:
                int i2 = AnonymousClass4.c[ordinal()];
                obj.hashCode();
                throw null;
            default:
                switch (AnonymousClass4.c[ordinal()]) {
                    case 1:
                        WalletLockReason walletLockReason = WalletLockReason.StrongestCvmAttemptCountExceeded;
                        int i3 = j + 55;
                        h = i3 % 128;
                        switch (i3 % 2 != 0 ? '\t' : '0') {
                            case '\t':
                                throw null;
                            default:
                                return walletLockReason;
                        }
                    case 2:
                        return WalletLockReason.OtherReason;
                    default:
                        return WalletLockReason.OtherReason;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Removed duplicated region for block: B:66:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0029  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 526
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bg.d.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
