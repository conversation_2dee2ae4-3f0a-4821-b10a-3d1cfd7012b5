package o.fh;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.f;
import o.e.a;
import o.ee.g;
import o.eg.e;
import o.fc.c;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fh\b.smali */
public final class b extends o.fc.d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static int e;
    private static int f;
    private static int g;
    private static short[] h;
    private static byte[] i;
    private static int j;
    private int a;
    private int b;
    private int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        f = 1;
        f();
        ViewConfiguration.getDoubleTapTimeout();
        TextUtils.lastIndexOf("", '0', 0);
        View.MeasureSpec.getMode(0);
        ExpandableListView.getPackedPositionType(0L);
        ExpandableListView.getPackedPositionType(0L);
        int i2 = g + Opcodes.DDIV;
        f = i2 % 128;
        switch (i2 % 2 == 0 ? '_' : '1') {
            case '1':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void f() {
        i = new byte[]{PSSSigner.TRAILER_IMPLICIT, 64, 111, -112, -126, 124, -112, 86, 88, -107, 68, 75, -70, -74, 90, 83, -87, UtilitiesSDKConstants.SRP_LABEL_ENC, 72, 80, -127, 114, 121, -115, 123, 112, -109, 109, 57, -44, 125, 121, 54, -43, 114, 49, -33, 108, -126, 51, -117, 117, -44, 126, 113, ByteCompanionObject.MIN_VALUE, -116, 96, 105, -90, 123, 81, -75, 108, 98, -98, -118, 102, -120, -127, 125, -127, 125, 110, -89, -124, 107, 111, -110, ByteCompanionObject.MIN_VALUE, 123, -116, 107, -127, -113, 121, 116, -117, -62, 32, -118, -123, 116, 120, -108, 125, -36, 63, 116, -124, -120, 125, -101, 120, -63, 33, 115, -121, -59, 48, 115, 113, -119, -114, 112, ByteCompanionObject.MAX_VALUE, -34, 33, -98, 117, -60, 43, -104, 118, -57, ByteCompanionObject.MAX_VALUE, -127, 32, -118, -123, 116, 120, -108, -99, 82, -113, -91, 65, -104, -106, 106, 126, -110, 124, 117, -119, 117, -119, -102, 83, 112, -97, -101, 102, 116, -113, 120, -97, PSSSigner.TRAILER_IMPLICIT, 69, 71, -89, 26, -1, -76, 68, 72, -67, 91, -72, 1, -9, -79, -72, 24, -16, -77, -79, 73, 78, UtilitiesSDKConstants.SRP_LABEL_ENC, -65, 30, -31, 94, -75, 4, -111, -93, 95, 109, -65, 65, -32, 74, 69, -76, -72, 84, 93, -110, 79, 101, -127, 88, 86, -86, -66, 82, PSSSigner.TRAILER_IMPLICIT, -75, 73, -75, 73, 90, -109, UtilitiesSDKConstants.SRP_LABEL_ENC, 95, 91, -90, -76, 79, -72, 95, -112, -112, -112, -112, -112, -112};
        e = 909053586;
        j = -676976861;
        c = -1909495437;
    }

    static void init$0() {
        $$a = new byte[]{58, -5, 118, 12};
        $$b = 89;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = r9 + 4
            int r7 = r7 * 2
            int r7 = 110 - r7
            byte[] r0 = o.fh.b.$$a
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L19:
            r3 = r2
            r6 = r9
            r9 = r7
            r7 = r6
        L1d:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r7]
            r6 = r10
            r10 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L34:
            int r7 = -r7
            int r9 = r9 + r7
            int r7 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fh.b.l(int, short, byte, java.lang.Object[]):void");
    }

    public b(boolean z, c cVar, short s) {
        super(z, cVar, s);
    }

    public final void c(int i2) {
        int i3 = f + 11;
        int i4 = i3 % 128;
        g = i4;
        char c2 = i3 % 2 != 0 ? '.' : 'X';
        this.b = i2;
        switch (c2) {
            case '.':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i5 = i4 + 19;
                f = i5 % 128;
                int i6 = i5 % 2;
                return;
        }
    }

    public final void b(int i2) {
        int i3 = g + 95;
        int i4 = i3 % 128;
        f = i4;
        int i5 = i3 % 2;
        this.a = i2;
        int i6 = i4 + 33;
        g = i6 % 128;
        int i7 = i6 % 2;
    }

    public final void a(int i2) {
        int i3 = g + Opcodes.DREM;
        f = i3 % 128;
        boolean z = i3 % 2 == 0;
        this.d = i2;
        switch (z) {
            case true:
                int i4 = 10 / 0;
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0038 A[RETURN] */
    @Override // o.fc.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean e(java.lang.String r4, o.dd.e r5) {
        /*
            r3 = this;
            int r4 = o.fh.b.g
            int r4 = r4 + 17
            int r5 = r4 % 128
            o.fh.b.f = r5
            int r4 = r4 % 2
            if (r4 != 0) goto Lf
            r4 = 46
            goto L11
        Lf:
            r4 = 67
        L11:
            r5 = 1
            r0 = 0
            switch(r4) {
                case 67: goto L1d;
                default: goto L16;
            }
        L16:
            o.fc.c r4 = r3.b()
            o.fc.c r1 = o.fc.c.b
            goto L2c
        L1d:
            o.fc.c r4 = r3.b()
            o.fc.c r1 = o.fc.c.b
            if (r4 != r1) goto L27
            r4 = r5
            goto L28
        L27:
            r4 = r0
        L28:
            switch(r4) {
                case 1: goto L39;
                default: goto L2b;
            }
        L2b:
            goto L38
        L2c:
            r2 = 77
            int r2 = r2 / r0
            if (r4 != r1) goto L33
            r4 = r0
            goto L34
        L33:
            r4 = r5
        L34:
            switch(r4) {
                case 1: goto L2b;
                default: goto L37;
            }
        L37:
            goto L39
        L38:
            return r0
        L39:
            o.fc.c r4 = o.fc.c.a
            r3.c(r4)
            int r4 = o.fh.b.g
            int r4 = r4 + 109
            int r1 = r4 % 128
            o.fh.b.f = r1
            int r4 = r4 % 2
            if (r4 != 0) goto L4d
            r4 = r5
            goto L4e
        L4d:
            r4 = r0
        L4e:
            switch(r4) {
                case 1: goto L52;
                default: goto L51;
            }
        L51:
            return r5
        L52:
            r4 = 42
            int r4 = r4 / r0
            return r5
        L56:
            r4 = move-exception
            throw r4
        L58:
            r4 = move-exception
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fh.b.e(java.lang.String, o.dd.e):boolean");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x0039. Please report as an issue. */
    public final e e(int i2) throws o.eg.d {
        int i3 = g;
        int i4 = i3 + 3;
        f = i4 % 128;
        int i5 = i4 % 2;
        switch (this.d == 0) {
            case true:
                int i6 = i3 + 55;
                f = i6 % 128;
                int i7 = i6 % 2;
                return null;
            default:
                e eVar = new e();
                int i8 = g + 55;
                f = i8 % 128;
                switch (i8 % 2 == 0 ? (char) 28 : 'N') {
                }
                int i9 = 0;
                while (true) {
                    int i10 = this.a;
                    int i11 = 511104686;
                    long j2 = 0;
                    if (i9 >= i10) {
                        while (i10 < this.d) {
                            o.eg.b bVar = new o.eg.b();
                            byte packedPositionType = (byte) ((-61) - ExpandableListView.getPackedPositionType(j2));
                            int lastIndexOf = 1207932956 - TextUtils.lastIndexOf("", '0', 0, 0);
                            short edgeSlop = (short) (ViewConfiguration.getEdgeSlop() >> 16);
                            int i12 = -TextUtils.lastIndexOf("", '0');
                            int makeMeasureSpec = i11 - View.MeasureSpec.makeMeasureSpec(0, 0);
                            Object[] objArr = new Object[1];
                            k(packedPositionType, lastIndexOf, edgeSlop, i12, makeMeasureSpec, objArr);
                            bVar.d(((String) objArr[0]).intern(), i2 + i10);
                            Object[] objArr2 = new Object[1];
                            k((byte) (-MotionEvent.axisFromString("")), (Process.myPid() >> 22) + 1207932959, (short) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), 4 - (ViewConfiguration.getWindowTouchSlop() >> 8), Color.rgb(0, 0, 0) + 527881920, objArr2);
                            bVar.d(((String) objArr2[0]).intern(), b().c());
                            eVar.b(bVar);
                            i10++;
                            i11 = 511104686;
                            j2 = 0;
                        }
                        return eVar;
                    }
                    o.eg.b bVar2 = new o.eg.b();
                    Object[] objArr3 = new Object[1];
                    k((byte) ((ViewConfiguration.getEdgeSlop() >> 16) - 61), 1207932957 + (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (short) (Process.myTid() >> 22), Color.blue(0) + 1, 511104686 + (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr3);
                    bVar2.d(((String) objArr3[0]).intern(), i2 + i9);
                    Object[] objArr4 = new Object[1];
                    k((byte) (ExpandableListView.getPackedPositionGroup(0L) + 1), 1207932960 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), MotionEvent.axisFromString("") + 5, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 511104704, objArr4);
                    bVar2.d(((String) objArr4[0]).intern(), c.d.c());
                    eVar.b(bVar2);
                    i9++;
                }
        }
    }

    @Override // o.fc.d
    public final short e() {
        short s = (short) (this.d - this.a);
        switch (s <= 0) {
            case true:
                return (short) 0;
            default:
                int i2 = f + 75;
                int i3 = i2 % 128;
                g = i3;
                if (i2 % 2 != 0) {
                }
                int i4 = i3 + 59;
                f = i4 % 128;
                int i5 = i4 % 2;
                return s;
        }
    }

    public final d d(int i2) {
        int i3 = f + Opcodes.LSHR;
        g = i3 % 128;
        Object obj = null;
        if (i3 % 2 != 0) {
            a();
            obj.hashCode();
            throw null;
        }
        switch (a()) {
            case false:
                g.c();
                Object[] objArr = new Object[1];
                k((byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) - 46), 1207932964 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (short) TextUtils.indexOf("", "", 0, 0), Process.getGidForName("") + 13, ((Process.getThreadPriority(0) + 20) >> 6) + 511104676, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((byte) ((ViewConfiguration.getJumpTapTimeout() >> 16) - 24), 1207932977 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (short) View.MeasureSpec.getMode(0), 51 - MotionEvent.axisFromString(""), (ViewConfiguration.getTapTimeout() >> 16) + 511104687, objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                return null;
            default:
                switch (this.a >= this.d ? 'L' : '`') {
                    case Opcodes.IADD /* 96 */:
                        if (i2 >= 65535) {
                            g.c();
                            Object[] objArr3 = new Object[1];
                            k((byte) (TextUtils.lastIndexOf("", '0') - 45), TextUtils.indexOf((CharSequence) "", '0', 0) + 1207932965, (short) TextUtils.getOffsetAfter("", 0), (ViewConfiguration.getScrollBarSize() >> 8) + 12, 511104675 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            k((byte) ((-36) - TextUtils.indexOf("", "", 0, 0)), 1207933105 - KeyEvent.getDeadChar(0, 0), (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), View.MeasureSpec.getSize(0) + 65, 511104686 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr4);
                            g.e(intern2, ((String) objArr4[0]).intern());
                            return null;
                        }
                        return new d(c.b, c(), i2, this.b) { // from class: o.fh.b.2
                            private final b a;
                            private static int e = 0;
                            private static int d = 1;

                            {
                                this.a = b.this;
                            }

                            /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0050. Please report as an issue. */
                            @Override // o.fc.e, o.fc.d
                            public final boolean e(String str, o.dd.e eVar) {
                                int i4 = d;
                                int i5 = (i4 & 61) + (i4 | 61);
                                e = i5 % 128;
                                int i6 = i5 % 2;
                                switch (b() == c.b) {
                                    case false:
                                        int i7 = e;
                                        int i8 = (i7 ^ 75) + ((i7 & 75) << 1);
                                        d = i8 % 128;
                                        switch (i8 % 2 == 0) {
                                            case false:
                                                return false;
                                            default:
                                                Object obj2 = null;
                                                obj2.hashCode();
                                                throw null;
                                        }
                                    default:
                                        int i9 = e;
                                        int i10 = (i9 & 87) + (i9 | 87);
                                        d = i10 % 128;
                                        switch (i10 % 2 == 0 ? '4' : (char) 22) {
                                        }
                                        c(c.a);
                                        int i11 = e + Opcodes.DREM;
                                        d = i11 % 128;
                                        int i12 = i11 % 2;
                                        return true;
                                }
                            }
                        };
                    default:
                        int i4 = f + Opcodes.LMUL;
                        g = i4 % 128;
                        if (i4 % 2 != 0) {
                        }
                        g.c();
                        Object[] objArr5 = new Object[1];
                        k((byte) (View.MeasureSpec.getSize(0) - 46), (Process.myTid() >> 22) + 1207932964, (short) (Process.myPid() >> 22), 12 - KeyEvent.normalizeMetaState(0), 511104675 - TextUtils.indexOf((CharSequence) "", '0'), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        k((byte) (28 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 1207933030 - (ViewConfiguration.getKeyRepeatDelay() >> 16), (short) (ViewConfiguration.getScrollDefaultDelay() >> 16), ExpandableListView.getPackedPositionType(0L) + 74, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 511104687, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        return null;
                }
        }
    }

    public final int h() {
        int i2 = f;
        int i3 = i2 + 7;
        g = i3 % 128;
        int i4 = i3 % 2;
        int i5 = this.b;
        int i6 = i2 + 35;
        g = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final int j() {
        int i2 = f + 87;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        int i5 = this.a;
        int i6 = i3 + 95;
        f = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                return i5;
            default:
                throw null;
        }
    }

    public final int i() {
        int i2 = f;
        int i3 = i2 + 39;
        g = i3 % 128;
        int i4 = i3 % 2;
        int i5 = this.d;
        int i6 = i2 + 13;
        g = i6 % 128;
        switch (i6 % 2 == 0) {
            case false:
                int i7 = 74 / 0;
                return i5;
            default:
                return i5;
        }
    }

    private static void k(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        int i5;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(e)};
            Object obj = a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) a.c(KeyEvent.getDeadChar(0, 0) + 11, (char) ((-1) - ImageFormat.getBitsPerPixel(0)), View.resolveSizeAndState(0, 0, 0) + 65);
                byte b2 = (byte) ($$b & 7);
                byte b3 = (byte) (b2 - 1);
                Object[] objArr3 = new Object[1];
                l(b2, b3, b3, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z = intValue == -1;
            if (z) {
                byte[] bArr = i;
                long j2 = 0;
                if (bArr != null) {
                    int length = bArr.length;
                    byte[] bArr2 = new byte[length];
                    int i6 = 0;
                    while (i6 < length) {
                        try {
                            Object[] objArr4 = {Integer.valueOf(bArr[i6])};
                            Object obj2 = a.s.get(494867332);
                            if (obj2 == null) {
                                Class cls2 = (Class) a.c(20 - (Process.getElapsedCpuTime() > j2 ? 1 : (Process.getElapsedCpuTime() == j2 ? 0 : -1)), (char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 16425), 150 - (KeyEvent.getMaxKeyCode() >> 16));
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                l(b4, b5, b5, objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                a.s.put(494867332, obj2);
                            }
                            bArr2[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                            i6++;
                            j2 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    bArr = bArr2;
                }
                switch (bArr != null) {
                    case false:
                        intValue = (short) (((short) (h[i2 + ((int) (c ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L))));
                        int i7 = $10 + 43;
                        $11 = i7 % 128;
                        int i8 = i7 % 2;
                        break;
                    default:
                        int i9 = $10 + 85;
                        $11 = i9 % 128;
                        switch (i9 % 2 == 0 ? (char) 15 : '=') {
                            case 15:
                                byte[] bArr3 = i;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(c)};
                                    Object obj3 = a.s.get(-2120899312);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 12, (char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 65 - TextUtils.indexOf("", "", 0));
                                        byte b6 = (byte) ($$b & 7);
                                        byte b7 = (byte) (b6 - 1);
                                        Object[] objArr7 = new Object[1];
                                        l(b6, b7, b7, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(-2120899312, obj3);
                                    }
                                    i5 = ((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) - ((int) (e % (-5810760824076169584L)));
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                byte[] bArr4 = i;
                                try {
                                    Object[] objArr8 = {Integer.valueOf(i2), Integer.valueOf(c)};
                                    Object obj4 = a.s.get(-2120899312);
                                    if (obj4 == null) {
                                        Class cls4 = (Class) a.c(11 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) View.combineMeasuredStates(0, 0), 65 - ((Process.getThreadPriority(0) + 20) >> 6));
                                        byte b8 = (byte) ($$b & 7);
                                        byte b9 = (byte) (b8 - 1);
                                        Object[] objArr9 = new Object[1];
                                        l(b8, b9, b9, objArr9);
                                        obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(-2120899312, obj4);
                                    }
                                    i5 = ((byte) (bArr4[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L)));
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                        intValue = (byte) i5;
                        break;
                }
            }
            if (intValue > 0) {
                fVar.d = ((i2 + intValue) - 2) + ((int) (c ^ (-5810760824076169584L))) + (z ? 1 : 0);
                try {
                    Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                    Object obj5 = a.s.get(160906762);
                    if (obj5 == null) {
                        obj5 = ((Class) a.c(11 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (char) ((-1) - Process.getGidForName("")), (ViewConfiguration.getEdgeSlop() >> 16) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        a.s.put(160906762, obj5);
                    }
                    ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr5 = i;
                    switch (bArr5 != null ? 'F' : '*') {
                        case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                            int i10 = $10 + 33;
                            $11 = i10 % 128;
                            int i11 = i10 % 2;
                            int length2 = bArr5.length;
                            byte[] bArr6 = new byte[length2];
                            int i12 = 0;
                            while (i12 < length2) {
                                bArr6[i12] = (byte) (bArr5[i12] ^ (-5810760824076169584L));
                                i12++;
                                int i13 = $11 + 83;
                                $10 = i13 % 128;
                                int i14 = i13 % 2;
                            }
                            bArr5 = bArr6;
                            break;
                    }
                    boolean z2 = bArr5 != null;
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        int i15 = $10;
                        int i16 = i15 + 53;
                        $11 = i16 % 128;
                        int i17 = i16 % 2;
                        if (z2) {
                            int i18 = i15 + 65;
                            $11 = i18 % 128;
                            int i19 = i18 % 2;
                            byte[] bArr7 = i;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                            int i20 = $10 + 45;
                            $11 = i20 % 128;
                            int i21 = i20 % 2;
                        } else {
                            short[] sArr = h;
                            fVar.d = fVar.d - 1;
                            fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
