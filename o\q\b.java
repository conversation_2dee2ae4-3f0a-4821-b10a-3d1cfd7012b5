package o.q;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\q\b.smali */
public final class b implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int[] c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        c();
        Process.myPid();
        int i = b + Opcodes.LUSHR;
        a = i % 128;
        switch (i % 2 == 0 ? 'R' : (char) 15) {
            case 15:
                break;
            default:
                int i2 = 37 / 0;
                break;
        }
    }

    static void c() {
        c = new int[]{-220919378, -418579395, 929726090, 1335221732, 1276147949, -213979562, -1240433459, -1689143104, 1176435707, -1504552032, 566858123, 582980449, -842951981, 915983249, -1147032972, -903081021, 1742705107, 339392422};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.q.b.$$a
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r8 = 116 - r8
            int r7 = r7 * 4
            int r7 = 4 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r4 = r8
            r3 = r2
            r8 = r7
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r4 = -r4
            int r6 = r6 + r4
            int r8 = r8 + 1
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.b.g(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{100, 85, 7, -13};
        $$b = 99;
    }

    @Override // o.q.c
    public final void d(o.eg.b bVar) {
        int i = a + Opcodes.LMUL;
        b = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    @Override // o.q.c
    public final void d(c cVar) {
        int i = a + 9;
        b = i % 128;
        int i2 = i % 2;
    }

    public final boolean equals(Object obj) {
        int i = a + 69;
        b = i % 128;
        boolean z = obj instanceof b;
        switch (i % 2 != 0) {
            case false:
                return z;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    @Override // o.q.c
    public final o.eg.b e() {
        int i = b + 9;
        int i2 = i % 128;
        a = i2;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                int i3 = i2 + 27;
                b = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return null;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.q.c
    public final String b() {
        int i = a + 75;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(new int[]{917362984, 1389904737, 1691145962, -1215466897, 709775715, -1152269586, -2126469362, -1718499558, 2023321869, 1875010368}, ExpandableListView.getPackedPositionType(0L) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = a + 87;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return intern;
            default:
                int i4 = 47 / 0;
                return intern;
        }
    }

    private static void f(int[] iArr, int i, Object[] objArr) {
        int length;
        int[] iArr2;
        int i2;
        int[] iArr3;
        int i3;
        Object method;
        g gVar = new g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr4 = c;
        int i4 = 0;
        int i5 = -1667374059;
        switch (iArr4 != null) {
            case false:
                break;
            default:
                int length2 = iArr4.length;
                int[] iArr5 = new int[length2];
                int i6 = 0;
                while (true) {
                    switch (i6 < length2 ? '^' : 'F') {
                        case Opcodes.DUP2_X2 /* 94 */:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i4] = Integer.valueOf(iArr4[i6]);
                                Object obj = o.e.a.s.get(Integer.valueOf(i5));
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(KeyEvent.keyCodeFromString("") + 10, (char) (Gravity.getAbsoluteGravity(i4, i4) + 8856), 324 - Color.green(i4));
                                    byte b2 = (byte) i4;
                                    byte b3 = b2;
                                    Object[] objArr3 = new Object[1];
                                    g(b2, b3, b3, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj);
                                }
                                iArr5[i6] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                                i6++;
                                i4 = 0;
                                i5 = -1667374059;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            iArr4 = iArr5;
                            break;
                    }
                }
        }
        int length3 = iArr4.length;
        int[] iArr6 = new int[length3];
        int[] iArr7 = c;
        switch (iArr7 == null) {
            case false:
                int i7 = $11 + 37;
                $10 = i7 % 128;
                if (i7 % 2 != 0) {
                    length = iArr7.length;
                    iArr2 = new int[length];
                    i2 = 1;
                } else {
                    length = iArr7.length;
                    iArr2 = new int[length];
                    i2 = 0;
                }
                while (true) {
                    switch (i2 < length ? Typography.quote : (char) 17) {
                        case 17:
                            iArr7 = iArr2;
                            break;
                        default:
                            try {
                                Object[] objArr4 = {Integer.valueOf(iArr7[i2])};
                                Object obj2 = o.e.a.s.get(-1667374059);
                                if (obj2 != null) {
                                    iArr3 = iArr7;
                                    i3 = length;
                                } else {
                                    Class cls2 = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0'), (char) (8856 - TextUtils.getOffsetBefore("", 0)), 324 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    iArr3 = iArr7;
                                    i3 = length;
                                    Object[] objArr5 = new Object[1];
                                    g(b4, b5, b5, objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj2);
                                }
                                iArr2[i2] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i2++;
                                iArr7 = iArr3;
                                length = i3;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                    }
                }
        }
        System.arraycopy(iArr7, 0, iArr6, 0, length3);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            int i8 = $11 + Opcodes.DSUB;
            $10 = i8 % 128;
            int i9 = i8 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr6);
            for (int i10 = 0; i10 < 16; i10++) {
                int i11 = $11 + Opcodes.DMUL;
                $10 = i11 % 128;
                int i12 = i11 % 2;
                gVar.e ^= iArr6[i10];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 10, (char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 572 - View.MeasureSpec.getMode(0))).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i13 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i13;
            gVar.c ^= iArr6[16];
            gVar.e ^= iArr6[17];
            int i14 = gVar.e;
            int i15 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            g.d(iArr6);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 != null) {
                    method = obj4;
                } else {
                    Class cls3 = (Class) o.e.a.c(TextUtils.getTrimmedLength("") + 12, (char) (View.getDefaultSize(0, 0) + 55183), (ViewConfiguration.getLongPressTimeout() >> 16) + 515);
                    byte b6 = (byte) 0;
                    byte b7 = b6;
                    Object[] objArr8 = new Object[1];
                    g(b6, b7, (byte) (b7 + 1), objArr8);
                    method = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, method);
                }
                ((Method) method).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }
}
