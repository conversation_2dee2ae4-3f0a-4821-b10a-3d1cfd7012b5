package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.KryoObjectInput;
import com.esotericsoftware.kryo.io.KryoObjectOutput;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.ObjectMap;
import java.io.Externalizable;
import java.io.ObjectInput;
import java.io.ObjectOutput;
import java.lang.reflect.Method;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ExternalizableSerializer.smali */
public class ExternalizableSerializer extends Serializer {
    private ObjectMap<Class, JavaSerializer> javaSerializerByType;
    private KryoObjectInput objectInput = null;
    private KryoObjectOutput objectOutput = null;

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, Object object) {
        JavaSerializer serializer = getJavaSerializerIfRequired(object.getClass());
        if (serializer == null) {
            writeExternal(kryo, output, object);
        } else {
            serializer.write(kryo, output, object);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object read(Kryo kryo, Input input, Class type) {
        JavaSerializer serializer = getJavaSerializerIfRequired(type);
        return serializer == null ? readExternal(kryo, input, type) : serializer.read(kryo, input, type);
    }

    private void writeExternal(Kryo kryo, Output output, Object object) {
        try {
            ((Externalizable) object).writeExternal(getObjectOutput(kryo, output));
        } catch (Exception ex) {
            throw new KryoException(ex);
        }
    }

    private Object readExternal(Kryo kryo, Input input, Class type) {
        try {
            Externalizable object = (Externalizable) kryo.newInstance(type);
            object.readExternal(getObjectInput(kryo, input));
            return object;
        } catch (Exception ex) {
            throw new KryoException(ex);
        }
    }

    private ObjectOutput getObjectOutput(Kryo kryo, Output output) {
        KryoObjectOutput kryoObjectOutput = this.objectOutput;
        if (kryoObjectOutput == null) {
            this.objectOutput = new KryoObjectOutput(kryo, output);
        } else {
            kryoObjectOutput.setOutput(output);
        }
        return this.objectOutput;
    }

    private ObjectInput getObjectInput(Kryo kryo, Input input) {
        KryoObjectInput kryoObjectInput = this.objectInput;
        if (kryoObjectInput == null) {
            this.objectInput = new KryoObjectInput(kryo, input);
        } else {
            kryoObjectInput.setInput(input);
        }
        return this.objectInput;
    }

    private JavaSerializer getJavaSerializerIfRequired(Class type) {
        JavaSerializer javaSerializer = getCachedSerializer(type);
        return (javaSerializer == null && isJavaSerializerRequired(type)) ? new JavaSerializer() : javaSerializer;
    }

    private JavaSerializer getCachedSerializer(Class type) {
        ObjectMap<Class, JavaSerializer> objectMap = this.javaSerializerByType;
        if (objectMap == null) {
            this.javaSerializerByType = new ObjectMap<>();
            return null;
        }
        return objectMap.get(type);
    }

    private boolean isJavaSerializerRequired(Class type) {
        return hasInheritableReplaceMethod(type, "writeReplace") || hasInheritableReplaceMethod(type, "readResolve");
    }

    private static boolean hasInheritableReplaceMethod(Class type, String methodName) {
        Method method = null;
        Class current = type;
        while (true) {
            if (current == null) {
                break;
            }
            try {
                method = current.getDeclaredMethod(methodName, new Class[0]);
                break;
            } catch (NoSuchMethodException e) {
                current = current.getSuperclass();
            }
        }
        return method != null && method.getReturnType() == Object.class;
    }
}
