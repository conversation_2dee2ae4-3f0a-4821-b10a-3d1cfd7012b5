package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.content.Context;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\r.smali */
public final class r implements q {
    public final String a;
    public final Context b;
    public h c;

    public r(String storageVersion, Context context) {
        Intrinsics.checkNotNullParameter(storageVersion, "storageVersion");
        Intrinsics.checkNotNullParameter(context, "context");
        this.a = storageVersion;
        this.b = context;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.q
    public final void a(String cipheredDataWithVersion, byte[] dataEncryptionKey, String filename, int i, byte[] storageEncryptionKey, boolean z, String str) {
        boolean z2;
        l lVar;
        Intrinsics.checkNotNullParameter(cipheredDataWithVersion, "cipheredData");
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        Intrinsics.checkNotNullParameter(filename, "fileName");
        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
        h hVar = null;
        try {
            lVar = f.a(cipheredDataWithVersion, storageEncryptionKey, dataEncryptionKey);
            z2 = false;
        } catch (SecureStorageSDKException e) {
            if (e.getErrorCode() != -4305 || !Intrinsics.areEqual(this.a, x.c)) {
                h hVar2 = this.c;
                if (hVar2 != null) {
                    hVar = hVar2;
                } else {
                    Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
                }
                hVar.onInitFailed(e);
                return;
            }
            Context context = this.b;
            Intrinsics.checkNotNullParameter(filename, "filename");
            Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
            Intrinsics.checkNotNullParameter(context, "context");
            byte[] a = e.a();
            String a2 = f.a(filename);
            String packageName = context.getPackageName();
            Intrinsics.checkNotNullExpressionValue(packageName, "context.packageName");
            boolean a3 = e.a(str, i, a, false, storageEncryptionKey, a2, packageName);
            Intrinsics.checkNotNullParameter(cipheredDataWithVersion, "cipheredDataWithVersion");
            Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
            Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
            UtilitiesSDKCryptoResponse a4 = f.a(cipheredDataWithVersion, storageEncryptionKey);
            if (a4.getReturnCode() != 0) {
                v vVar = SecureStorageSDKException.Companion;
                int returnCode = a4.getReturnCode();
                vVar.getClass();
                v.a(returnCode);
                throw null;
            }
            l a5 = f.a(a4.getOutputData(), dataEncryptionKey);
            z2 = a3;
            lVar = a5;
        }
        if (z || z2) {
            h hVar3 = this.c;
            if (hVar3 != null) {
                hVar = hVar3;
            } else {
                Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
            }
            hVar.a();
            return;
        }
        h hVar4 = this.c;
        if (hVar4 != null) {
            hVar = hVar4;
        } else {
            Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
        }
        hVar.a(lVar);
    }
}
