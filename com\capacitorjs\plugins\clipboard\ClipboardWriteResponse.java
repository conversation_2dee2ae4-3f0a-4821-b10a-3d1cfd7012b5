package com.capacitorjs.plugins.clipboard;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\clipboard\ClipboardWriteResponse.smali */
public class ClipboardWriteResponse {
    private String errorMessage;
    private boolean success;

    public ClipboardWriteResponse(boolean success) {
        this(success, "");
    }

    public ClipboardWriteResponse(boolean success, String errorMessage) {
        this.success = success;
        this.errorMessage = errorMessage;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }
}
