package o.ej;

import android.os.Process;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ej\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static int b;
    private static char[] c;
    private static int d;
    private static boolean e;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        h = 1;
        d();
        View.MeasureSpec.getMode(0);
        Process.getElapsedCpuTime();
        Process.getThreadPriority(0);
        int i = b + 7;
        h = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        c = new char[]{61633, 61671, 61666, 61675, 61656, 61653, 61652, 61585, 61667, 61665, 61668, 61669, 61670, 61661, 61658, 61601, 61648, 61672, 61660, 61655, 61605, 61596};
        a = true;
        e = true;
        d = 782102897;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r6 = r6 * 3
            int r6 = 3 - r6
            int r7 = 121 - r7
            byte[] r0 = o.ej.b.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r6 = r6 + 1
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.b.g(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{24, -81, 39, 82};
        $$b = Opcodes.I2S;
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x0033, code lost:
    
        if (r6.length() >= 4) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0035, code lost:
    
        r0 = 'X';
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x003a, code lost:
    
        switch(r0) {
            case 88: goto L21;
            default: goto L24;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0042, code lost:
    
        r2 = new java.lang.Object[1];
        f(null, android.text.TextUtils.indexOf("", "") + 127, null, "\u0090", r2);
        r6 = ((java.lang.String) r2[0]).intern().concat(r6);
        r0 = o.ej.b.h + 71;
        o.ej.b.b = r0 % 128;
        r0 = r0 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0041, code lost:
    
        return o.dk.b.b(r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0038, code lost:
    
        r0 = '=';
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x002b, code lost:
    
        if (r6.length() <= 3) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0020, code lost:
    
        if (r6.length() <= 3) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0068, code lost:
    
        r1 = new java.lang.Object[1];
        f(null, android.graphics.Color.green(0) + 127, null, "\u008f\u0089\u0083\u008e\u0088\u0083\u0083\u008c\u0088\u008d\u0085\u0088\u008c\u008b\u008a\u0089\u0085\u0088\u0086\u0087\u0086\u0085\u0084\u0083\u0082\u0081", r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0082, code lost:
    
        throw new java.lang.IllegalArgumentException(((java.lang.String) r1[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] a(short r6) throws java.lang.IllegalArgumentException {
        /*
            int r0 = o.ej.b.h
            int r0 = r0 + 9
            int r1 = r0 % 128
            o.ej.b.b = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 27
            goto L11
        Lf:
            r0 = 21
        L11:
            r1 = 1
            r2 = 3
            r3 = 0
            r4 = 0
            switch(r0) {
                case 27: goto L23;
                default: goto L18;
            }
        L18:
            java.lang.String r6 = java.lang.String.valueOf(r6)
            int r0 = r6.length()
            if (r0 > r2) goto L68
        L22:
            goto L2e
        L23:
            java.lang.String r6 = java.lang.String.valueOf(r6)
            int r0 = r6.length()
            if (r0 > r2) goto L68
            goto L22
        L2e:
            int r0 = r6.length()
            r2 = 4
            if (r0 >= r2) goto L38
            r0 = 88
            goto L3a
        L38:
            r0 = 61
        L3a:
            switch(r0) {
                case 88: goto L42;
                default: goto L3d;
            }
        L3d:
            byte[] r6 = o.dk.b.b(r6)
            return r6
        L42:
            java.lang.String r0 = ""
            int r0 = android.text.TextUtils.indexOf(r0, r0)
            int r0 = r0 + 127
            java.lang.Object[] r2 = new java.lang.Object[r1]
            java.lang.String r5 = "\u0090"
            f(r4, r0, r4, r5, r2)
            r0 = r2[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.String r6 = r0.concat(r6)
            int r0 = o.ej.b.h
            int r0 = r0 + 71
            int r2 = r0 % 128
            o.ej.b.b = r2
            int r0 = r0 % 2
            goto L2e
        L68:
            java.lang.IllegalArgumentException r6 = new java.lang.IllegalArgumentException
            int r0 = android.graphics.Color.green(r3)
            int r0 = r0 + 127
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r2 = "\u008f\u0089\u0083\u008e\u0088\u0083\u0083\u008c\u0088\u008d\u0085\u0088\u008c\u008b\u008a\u0089\u0085\u0088\u0086\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            f(r4, r0, r4, r2, r1)
            r0 = r1[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r6.<init>(r0)
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.b.a(short):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static short c(byte[] r6) throws java.lang.IllegalArgumentException {
        /*
            int r0 = o.ej.b.b
            int r0 = r0 + 63
            int r1 = r0 % 128
            o.ej.b.h = r1
            r1 = 2
            int r0 = r0 % r1
            int r0 = r6.length
            r2 = 1
            r3 = 0
            if (r0 != r1) goto L49
        L10:
            r0 = r6[r3]
            if (r0 != 0) goto L17
            r0 = 10
            goto L19
        L17:
            r0 = 92
        L19:
            switch(r0) {
                case 92: goto L28;
                default: goto L1c;
            }
        L1c:
            int r0 = o.ej.b.b
            int r0 = r0 + 89
            int r4 = r0 % 128
            o.ej.b.h = r4
            int r0 = r0 % r1
            if (r0 != 0) goto L38
            goto L35
        L28:
            java.lang.String r6 = o.dk.b.e(r6)
            java.lang.Short r6 = java.lang.Short.valueOf(r6)
            short r6 = r6.shortValue()
            return r6
        L35:
            r0 = 9
            goto L3a
        L38:
            r0 = 44
        L3a:
            switch(r0) {
                case 44: goto L43;
                default: goto L3d;
            }
        L3d:
            int r0 = r6.length
            byte[] r6 = java.util.Arrays.copyOfRange(r6, r3, r0)
            goto L48
        L43:
            int r0 = r6.length
            byte[] r6 = java.util.Arrays.copyOfRange(r6, r2, r0)
        L48:
            goto L10
        L49:
            java.lang.IllegalArgumentException r6 = new java.lang.IllegalArgumentException
            double r0 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r3)
            r4 = 0
            int r0 = (r0 > r4 ? 1 : (r0 == r4 ? 0 : -1))
            int r0 = r0 + 127
            java.lang.Object[] r1 = new java.lang.Object[r2]
            r2 = 0
            java.lang.String r4 = "\u008f\u0089\u0083\u008e\u0088\u0087\u008c\u0092\u0094\u0096\u0095\u0088\u0087\u0094\u0088\u008c\u008d\u008b\u0093\u0088\u0092\u0091\u0082\u0082\u0091\u0088\u0086\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            f(r2, r0, r2, r4, r1)
            r0 = r1[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r6.<init>(r0)
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.b.c(byte[]):short");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v24, types: [byte[]] */
    private static void f(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 946
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.b.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
