package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.y5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP384R1Point.smali */
public class SecP384R1Point extends ECPoint.AbstractFp {
    SecP384R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP384R1FieldElement secP384R1FieldElement = (SecP384R1FieldElement) this.b;
        SecP384R1FieldElement secP384R1FieldElement2 = (SecP384R1FieldElement) this.c;
        SecP384R1FieldElement secP384R1FieldElement3 = (SecP384R1FieldElement) eCPoint.getXCoord();
        SecP384R1FieldElement secP384R1FieldElement4 = (SecP384R1FieldElement) eCPoint.getYCoord();
        SecP384R1FieldElement secP384R1FieldElement5 = (SecP384R1FieldElement) this.d[0];
        SecP384R1FieldElement secP384R1FieldElement6 = (SecP384R1FieldElement) eCPoint.getZCoord(0);
        int[] a = c6.a(24);
        int[] a2 = c6.a(24);
        int[] a3 = c6.a(24);
        int[] a4 = c6.a(12);
        int[] a5 = c6.a(12);
        boolean isOne = secP384R1FieldElement5.isOne();
        if (isOne) {
            iArr = secP384R1FieldElement3.a;
            iArr2 = secP384R1FieldElement4.a;
        } else {
            SecP384R1Field.square(secP384R1FieldElement5.a, a4, a);
            SecP384R1Field.multiply(a4, secP384R1FieldElement3.a, a3, a);
            SecP384R1Field.multiply(a4, secP384R1FieldElement5.a, a4, a);
            SecP384R1Field.multiply(a4, secP384R1FieldElement4.a, a4, a);
            iArr = a3;
            iArr2 = a4;
        }
        boolean isOne2 = secP384R1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP384R1FieldElement.a;
            iArr4 = secP384R1FieldElement2.a;
        } else {
            SecP384R1Field.square(secP384R1FieldElement6.a, a5, a);
            SecP384R1Field.multiply(a5, secP384R1FieldElement.a, a2, a);
            SecP384R1Field.multiply(a5, secP384R1FieldElement6.a, a5, a);
            SecP384R1Field.multiply(a5, secP384R1FieldElement2.a, a5, a);
            iArr3 = a2;
            iArr4 = a5;
        }
        int[] a6 = c6.a(12);
        SecP384R1Field.subtract(iArr3, iArr, a6);
        int[] a7 = c6.a(12);
        SecP384R1Field.subtract(iArr4, iArr2, a7);
        if (c6.e(12, a6)) {
            return c6.e(12, a7) ? twice() : curve.getInfinity();
        }
        SecP384R1Field.square(a6, a4, a);
        int[] a8 = c6.a(12);
        SecP384R1Field.multiply(a4, a6, a8, a);
        SecP384R1Field.multiply(a4, iArr3, a4, a);
        SecP384R1Field.negate(a8, a8);
        y5.a(iArr4, a8, a2);
        SecP384R1Field.reduce32(c6.b(12, a4, a4, a8), a8);
        SecP384R1FieldElement secP384R1FieldElement7 = new SecP384R1FieldElement(a5);
        SecP384R1Field.square(a7, secP384R1FieldElement7.a, a);
        int[] iArr5 = secP384R1FieldElement7.a;
        SecP384R1Field.subtract(iArr5, a8, iArr5);
        SecP384R1FieldElement secP384R1FieldElement8 = new SecP384R1FieldElement(a8);
        SecP384R1Field.subtract(a4, secP384R1FieldElement7.a, secP384R1FieldElement8.a);
        y5.a(secP384R1FieldElement8.a, a7, a3);
        SecP384R1Field.addExt(a2, a3, a2);
        SecP384R1Field.reduce(a2, secP384R1FieldElement8.a);
        SecP384R1FieldElement secP384R1FieldElement9 = new SecP384R1FieldElement(a6);
        if (!isOne) {
            int[] iArr6 = secP384R1FieldElement9.a;
            SecP384R1Field.multiply(iArr6, secP384R1FieldElement5.a, iArr6, a);
        }
        if (!isOne2) {
            int[] iArr7 = secP384R1FieldElement9.a;
            SecP384R1Field.multiply(iArr7, secP384R1FieldElement6.a, iArr7, a);
        }
        return new SecP384R1Point(curve, secP384R1FieldElement7, secP384R1FieldElement8, new ECFieldElement[]{secP384R1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP384R1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP384R1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP384R1FieldElement secP384R1FieldElement = (SecP384R1FieldElement) this.c;
        if (secP384R1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP384R1FieldElement secP384R1FieldElement2 = (SecP384R1FieldElement) this.b;
        SecP384R1FieldElement secP384R1FieldElement3 = (SecP384R1FieldElement) this.d[0];
        int[] a = c6.a(24);
        int[] a2 = c6.a(12);
        int[] a3 = c6.a(12);
        int[] a4 = c6.a(12);
        SecP384R1Field.square(secP384R1FieldElement.a, a4, a);
        int[] a5 = c6.a(12);
        SecP384R1Field.square(a4, a5, a);
        boolean isOne = secP384R1FieldElement3.isOne();
        int[] iArr = secP384R1FieldElement3.a;
        if (!isOne) {
            SecP384R1Field.square(iArr, a3, a);
            iArr = a3;
        }
        SecP384R1Field.subtract(secP384R1FieldElement2.a, iArr, a2);
        SecP384R1Field.add(secP384R1FieldElement2.a, iArr, a3);
        SecP384R1Field.multiply(a3, a2, a3, a);
        SecP384R1Field.reduce32(c6.b(12, a3, a3, a3), a3);
        SecP384R1Field.multiply(a4, secP384R1FieldElement2.a, a4, a);
        SecP384R1Field.reduce32(c6.c(12, a4, 2, 0), a4);
        SecP384R1Field.reduce32(c6.a(12, a5, 3, 0, a2), a2);
        SecP384R1FieldElement secP384R1FieldElement4 = new SecP384R1FieldElement(a5);
        SecP384R1Field.square(a3, secP384R1FieldElement4.a, a);
        int[] iArr2 = secP384R1FieldElement4.a;
        SecP384R1Field.subtract(iArr2, a4, iArr2);
        int[] iArr3 = secP384R1FieldElement4.a;
        SecP384R1Field.subtract(iArr3, a4, iArr3);
        SecP384R1FieldElement secP384R1FieldElement5 = new SecP384R1FieldElement(a4);
        SecP384R1Field.subtract(a4, secP384R1FieldElement4.a, secP384R1FieldElement5.a);
        int[] iArr4 = secP384R1FieldElement5.a;
        SecP384R1Field.multiply(iArr4, a3, iArr4, a);
        int[] iArr5 = secP384R1FieldElement5.a;
        SecP384R1Field.subtract(iArr5, a2, iArr5);
        SecP384R1FieldElement secP384R1FieldElement6 = new SecP384R1FieldElement(a3);
        SecP384R1Field.twice(secP384R1FieldElement.a, secP384R1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP384R1FieldElement6.a;
            SecP384R1Field.multiply(iArr6, secP384R1FieldElement3.a, iArr6, a);
        }
        return new SecP384R1Point(curve, secP384R1FieldElement4, secP384R1FieldElement5, new ECFieldElement[]{secP384R1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP384R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
