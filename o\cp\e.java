package o.cp;

import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.m;
import o.ee.j;
import o.ei.i;
import o.et.f;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e.smali */
public final class e implements o.cc.e<f> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static int c;
    private static int[] d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        b();
        KeyEvent.getModifierMetaStateMask();
        int i = a + 65;
        c = i % 128;
        int i2 = i % 2;
    }

    static void b() {
        d = new int[]{1061979036, 2139612247, -353642017, 188958241, -1345186633, -1104928940, 1979434027, -1310600876, 703207582, 871256341, -1372382865, -1504517372, -891302513, 2012465958, -549726203, -827953607, 464372041, -2128201961};
        b = new char[]{50861, 50698, 50717, 50690, 50690, 50700, 50804, 50702, 50702, 50692, 50692, 50698, 50699, 50703, 50693, 50699, 50694, 50716, 50698, 50698, 50690, 50805, 50813, 50935, 50855, 50855, 50851, 50859, 50836, 50837, 50852, 50852, 50858, 50877, 50878, 50937, 50854, 50852, 50857, 50831, 50942, 50839, 50877, 50850, 50858, 50855, 50851, 50854, 50850, 50879, 50873, 50823, 50936, 50819, 50844, 50819, 50938, 50831, 50852, 50855, 50849, 50877, 50823, 50816, 50823, 50820, 50854, 50849, 50876, 50855, 50857, 50854, 50823, 50934, 50834, 50851, 50855, 50829, 50943, 50841, 50856, 50933, 50862, 50841, 50822, 50830, 50830, 50847, 50855, 50855, 50760, 51143, 51149, 50740, 50739, 50728, 50719, 50724, 51148, 51148, 50723, 50744, 51141, 50750, 50726, 50742, 50847, 50795, 50779, 50777, 50785, 50785, 50777, 50864, 50781, 50788, 50793, 50790, 50808, 50758, 51163, 51152, 51154, 51143, 51138, 51180, 51153, 51157, 51154, 51161, 51142, 51158, 51152, 51164, 51149, 50736, 50736, 50738, 50939, 50848, 50852, 50855, 50913, 50837, 50861, 50861, 50877, 50762, 50753, 50753, 50780, 50773, 50779, 50777, 50777, 50776, 50753, 50846, 50785, 50812, 50815, 50790, 50788, 50774, 50777, 50799, 50794, 50786, 50785, 50792, 50792, 50793, 50797, 50789, 50785, 50791, 50785, 50769, 50770, 50786, 50792, 50796, 50788, 50831, 50758, 50782, 50766, 50761, 50754, 50754, 50783, 50867, 50852, 50926, 50878, 50749, 50747, 50723, 50722, 50726, 50721, 50749, 50723, 50749, 50710, 50713, 50721, 50723, 50937, 50855, 50851, 50851, 50876, 50935, 50855, 50863, 50859, 50855, 50851, 50877, 50853, 50859, 50853, 50862, 50860, 50843, 50832, 50851, 50849, 50831, 50760, 50753, 50755, 50781, 50777, 50753, 50753, 50765, 50879, 50848, 50875, 50872, 50728, 50704, 50728, 50720, 51152, 51157, 51183, 51183, 51157, 50737, 51166, 51158, 51161, 51157, 51160, 50690, 51158, 51155, 51166, 51154, 50690, 51163, 50762, 51182, 51195, 51198, 51137, 51146, 51198, 51199, 51172, 51170, 51194, 51198, 51196, 51198, 51137, 51146, 51171, 51168, 51171, 51194, 51195, 51195, 51196, 51197, 51197, 51172, 51146, 51165, 51193, 51168, 51170, 51168, 51146, 51139, 51197, 51173, 51178, 51174, 51197, 51170, 51141, 51149, 51177, 51198, 51170, 51144, 51144, 51168, 51192, 51198, 51170, 51174, 51178, 51170, 51198, 51196, 51198, 51137, 50742, 51154, 51198, 51189, 51138, 50751, 50751};
    }

    private static void g(byte b2, int i, int i2, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = 1 - (i2 * 2);
        int i4 = 122 - b2;
        int i5 = (i * 2) + 4;
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            int i8 = (-i5) + i7;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i5++;
            i4 = i8;
        }
        while (true) {
            int i9 = i6 + 1;
            bArr2[i9] = (byte) i4;
            if (i9 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i10 = i7;
            int i11 = i4;
            int i12 = i5;
            int i13 = (-bArr[i5]) + i11;
            i7 = i10;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i9;
            i5 = i12 + 1;
            i4 = i13;
        }
    }

    static void init$0() {
        $$a = new byte[]{117, 56, 99, 31};
        $$b = 204;
    }

    @Override // o.cc.e
    public final /* synthetic */ f d(String str, String str2, int i, String str3) {
        int i2 = a + 77;
        c = i2 % 128;
        switch (i2 % 2 != 0 ? '\f' : (char) 11) {
            case '\f':
                c(str, str2, i, str3);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return c(str, str2, i, str3);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.cc.e
    public final java.util.List<o.et.f> a(java.lang.String r46, java.lang.String r47, int r48, java.lang.String r49, o.eg.b r50) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 2472
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cp.e.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    private static f c(String str, String str2, int i, String str3) {
        f fVar = new f(str, str2, i, str3);
        int i2 = c + 43;
        a = i2 % 128;
        int i3 = i2 % 2;
        return fVar;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e$a.smali */
    static final class a {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int b;
        private static long d;
        private static int e;
        final o.dh.c c;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            b = 1;
            d = 1433252235153948733L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0039). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void f(short r5, int r6, short r7, java.lang.Object[] r8) {
            /*
                byte[] r0 = o.cp.e.a.$$a
                int r6 = r6 * 2
                int r6 = r6 + 1
                int r7 = r7 + 4
                int r5 = r5 * 2
                int r5 = r5 + 112
                byte[] r1 = new byte[r6]
                r2 = -1
                int r6 = r6 + r2
                if (r0 != 0) goto L1a
                r5 = r7
                r3 = r2
                r7 = r6
                r2 = r1
                r1 = r0
                r0 = r8
                r8 = r5
                goto L39
            L1a:
                r4 = r7
                r7 = r5
                r5 = r4
            L1d:
                int r2 = r2 + 1
                byte r3 = (byte) r7
                r1[r2] = r3
                if (r2 != r6) goto L2d
                java.lang.String r5 = new java.lang.String
                r6 = 0
                r5.<init>(r1, r6)
                r8[r6] = r5
                return
            L2d:
                int r5 = r5 + 1
                r3 = r0[r5]
                r4 = r7
                r7 = r6
                r6 = r3
                r3 = r2
                r2 = r1
                r1 = r0
                r0 = r8
                r8 = r4
            L39:
                int r6 = -r6
                int r6 = r6 + r8
                r8 = r0
                r0 = r1
                r1 = r2
                r2 = r3
                r4 = r7
                r7 = r6
                r6 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.a.f(short, int, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{9, -87, 124, -45};
            $$b = Opcodes.IF_ICMPGT;
        }

        a(String str, String str2) {
            if (str2 != null && !str2.isEmpty()) {
                Object[] objArr = new Object[1];
                a("ژ潼햱㩘", MotionEvent.axisFromString("") + 27032, objArr);
                this.c = new o.dh.c(((String) objArr[0]).intern(), b(str2));
            } else if (str == null || str.isEmpty()) {
                Object[] objArr2 = new Object[1];
                a("ژ潼햱㩘", TextUtils.lastIndexOf("", '0', 0, 0) + 27032, objArr2);
                this.c = new o.dh.c(((String) objArr2[0]).intern(), null);
            } else {
                Object[] objArr3 = new Object[1];
                a("ژ潼햱㩘", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 27031, objArr3);
                this.c = new o.dh.c(((String) objArr3[0]).intern(), b(str));
            }
        }

        private static byte[] b(String str) {
            int i = b + 33;
            e = i % 128;
            int i2 = i % 2;
            byte[] bytes = str.substring(0, Math.min(str.length(), 26)).getBytes(j.c());
            int i3 = b + 69;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    throw null;
                default:
                    return bytes;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void a(java.lang.String r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 900
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.a.a(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e$c.smali */
    static final class c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int[] e;
        final o.dh.c d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = new int[]{-629025465, -152454402, -1286735163, -383696372, -1360733290, -990702336, 1122610125, 1472389771, -574663959, 1824287778, 2046686937, -1279291360, -1265279235, 2044726430, -584021381, -1226444964, 910843022, -522788575};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void b(byte r7, byte r8, short r9, java.lang.Object[] r10) {
            /*
                int r8 = r8 * 3
                int r8 = 3 - r8
                byte[] r0 = o.cp.e.c.$$a
                int r9 = 116 - r9
                int r7 = r7 * 3
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L19
                r9 = r8
                r3 = r1
                r4 = r2
                r8 = r7
                r1 = r0
                r0 = r10
                r10 = r9
                goto L34
            L19:
                r3 = r2
            L1a:
                int r8 = r8 + 1
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                if (r4 != r7) goto L2b
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2b:
                r3 = r0[r8]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r6
            L34:
                int r9 = r9 + r7
                r7 = r8
                r8 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.c.b(byte, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{85, 91, 121, -13};
            $$b = Opcodes.ARRAYLENGTH;
        }

        c(String str, String str2, o.eg.b bVar, o.eg.b bVar2, o.eg.b bVar3, o.eg.b bVar4) throws o.eg.d, i {
            StringBuilder sb = new StringBuilder();
            sb.append(str);
            sb.append('D');
            sb.append(str2.substring(0, 4));
            if (bVar3 != null) {
                Object[] objArr = new Object[1];
                a(new int[]{-1300350335, 854291565, 1250363799, 1790779278}, Color.red(0) + 7, objArr);
                sb.append(bVar3.r(((String) objArr[0]).intern()));
                Object[] objArr2 = new Object[1];
                a(new int[]{-1233248887, 914058629, -911154323, 418470569, -1153145508, 179679551}, 11 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
                sb.append(bVar3.r(((String) objArr2[0]).intern()));
                Object[] objArr3 = new Object[1];
                a(new int[]{2044247142, 951356619, 1733040826, -1195790396, -1538159051, -1628646372, -1352772569, 149512084}, 14 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr3);
                sb.append(bVar3.r(((String) objArr3[0]).intern()));
            } else if (bVar4 != null) {
                Object[] objArr4 = new Object[1];
                a(new int[]{-1300350335, 854291565, 1250363799, 1790779278}, 7 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr4);
                sb.append(bVar4.r(((String) objArr4[0]).intern()));
            } else if (bVar != null) {
                Object[] objArr5 = new Object[1];
                a(new int[]{-1300350335, 854291565, 1250363799, 1790779278}, View.resolveSize(0, 0) + 7, objArr5);
                sb.append(bVar.r(((String) objArr5[0]).intern()));
                Object[] objArr6 = new Object[1];
                a(new int[]{-1233248887, 914058629, -911154323, 418470569, -1153145508, 179679551}, Process.getGidForName("") + 12, objArr6);
                sb.append(bVar.r(((String) objArr6[0]).intern()));
                Object[] objArr7 = new Object[1];
                a(new int[]{2044247142, 951356619, 1733040826, -1195790396, -1538159051, -1628646372, -1352772569, 149512084}, (ViewConfiguration.getJumpTapTimeout() >> 16) + 14, objArr7);
                sb.append(bVar.r(((String) objArr7[0]).intern()));
            } else if (bVar2 != null) {
                Object[] objArr8 = new Object[1];
                a(new int[]{-1300350335, 854291565, 1250363799, 1790779278}, 7 - KeyEvent.getDeadChar(0, 0), objArr8);
                sb.append(bVar2.r(((String) objArr8[0]).intern()));
            } else {
                Object[] objArr9 = new Object[1];
                a(new int[]{-980107906, -826635027, 727877689, -1828587404, -1713104759, 1311468937, -1862044149, -456680069, 1656941268, -1720520385, 1076642477, -1384025901, 1711402180, 884790429, -1068755140, 2104779945, -264264395, -103434547, -269783451, -159492377, -836148715, -838269458, -1626830937, 102603442, 1484236260, -2029815863, 1630530639, -1366391864, 1272059644, -98870042, -57249918, 313001215, -1017573010, -1860616544, -664895083, -354702757, -1410545764, 321733999, 622733841, 1422963555, -1550203595, 1857124999, -2082218759, -1115432852, 1858879031, -1994846556, 1962840699, -1387948976}, 95 - (Process.myPid() >> 22), objArr9);
                throw new i(((String) objArr9[0]).intern());
            }
            String obj = sb.toString();
            obj = obj.length() % 2 != 0 ? new StringBuilder().append(obj).append('F').toString() : obj;
            Object[] objArr10 = new Object[1];
            a(new int[]{1316555263, -1421347681}, TextUtils.indexOf("", "", 0, 0) + 2, objArr10);
            this.d = new o.dh.c(((String) objArr10[0]).intern(), o.dk.b.b(obj));
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void a(int[] r25, int r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 850
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.c.a(int[], int, java.lang.Object[]):void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e$d.smali */
    static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static boolean l;
        private static int m;
        private static boolean n;

        /* renamed from: o, reason: collision with root package name */
        private static char[] f52o;
        private static long t;
        final o.dh.c a;
        final o.dh.c b;
        final o.dh.c c;
        final byte[] d;
        final byte e;
        final byte f;
        final byte[] g;
        final byte[] h;
        final o.dh.c i;
        final byte[] j;
        final o.dh.c k;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f52o = new char[]{61949, 61696, 61699, 61938, 61939, 61902, 61714, 61709, 61730, 61717, 61731, 61720, 61727, 61713, 61712, 61726, 61724, 61722, 61924, 61718, 61708, 61715, 61728, 61725, 61925, 61936, 61927, 61920, 61937, 61921, 61923, 61922, 61729, 61733, 61926, 61916};
            n = true;
            l = true;
            m = 782102958;
            t = -4841712373951119309L;
        }

        static void init$0() {
            $$a = new byte[]{112, 25, 2, PSSSigner.TRAILER_IMPLICIT};
            $$b = 210;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void r(short r7, short r8, int r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 + 112
                int r8 = r8 * 2
                int r8 = r8 + 4
                int r9 = r9 * 3
                int r9 = 1 - r9
                byte[] r0 = o.cp.e.d.$$a
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L17
                r3 = r1
                r5 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                goto L2f
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r7
                int r5 = r3 + 1
                r1[r3] = r4
                if (r5 != r9) goto L27
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L27:
                r3 = r0[r8]
                r6 = r10
                r10 = r9
                r9 = r3
                r3 = r1
                r1 = r0
                r0 = r6
            L2f:
                int r8 = r8 + 1
                int r7 = r7 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r5
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.d.r(short, short, int, java.lang.Object[]):void");
        }

        d(o.eg.b bVar, o.eg.b bVar2) throws i, o.eg.d {
            bVar = bVar2 != null ? bVar2 : bVar;
            if (bVar != null) {
                Object[] objArr = new Object[1];
                p(null, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 127, null, "\u0098\u0089\u0096", objArr);
                this.d = bVar.B(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                p(null, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.IAND, null, "\u0085\u009b\u009a\u0099", objArr2);
                String intern = ((String) objArr2[0]).intern();
                Object[] objArr3 = new Object[1];
                q("ۀ㗳悭", TextUtils.lastIndexOf("", '0', 0) + 13110, objArr3);
                this.a = new o.dh.c(intern, bVar.z(((String) objArr3[0]).intern()));
                Object[] objArr4 = new Object[1];
                p(null, Gravity.getAbsoluteGravity(0, 0) + 127, null, "\u009d\u009c\u009a\u0099", objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                q("ۅ殶\udc2c", View.MeasureSpec.getMode(0) + 28019, objArr5);
                this.b = new o.dh.c(intern2, bVar.B(((String) objArr5[0]).intern()));
                Object[] objArr6 = new Object[1];
                p(null, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 127, null, " \u009f\u009a\u009e", objArr6);
                String intern3 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                q("ۓᴁㅯ", (Process.myPid() >> 22) + 7121, objArr7);
                this.c = new o.dh.c(intern3, new byte[]{bVar.D(((String) objArr7[0]).intern()).byteValue()});
                Object[] objArr8 = new Object[1];
                p(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u008c\u0097\u0096", objArr8);
                this.e = bVar.D(((String) objArr8[0]).intern()).byteValue();
                Object[] objArr9 = new Object[1];
                q("ۇ梿\uda2e䶕뼃⺋遱χ番\ue4d2噝링⮫鴛ಁ", 28278 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr9);
                this.j = bVar.B(((String) objArr9[0]).intern());
                Object[] objArr10 = new Object[1];
                p(null, 126 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), null, "\u008e\u0087\u008d\u0085¢\u0091\u0089\u008c¡\u008d\u0096", objArr10);
                this.g = bVar.B(((String) objArr10[0]).intern());
                Object[] objArr11 = new Object[1];
                p(null, 127 - TextUtils.getTrimmedLength(""), null, "\u0087\u008a\u0096", objArr11);
                this.f = bVar.D(((String) objArr11[0]).intern()).byteValue();
                Object[] objArr12 = new Object[1];
                q("ۂ鋩⺾", 37951 - KeyEvent.keyCodeFromString(""), objArr12);
                this.h = bVar.B(((String) objArr12[0]).intern());
                Object[] objArr13 = new Object[1];
                q("ے꣖媶ಎ빬恛መ쐢矓᧷쮈絖⽈턠茈", 44579 - TextUtils.getCapsMode("", 0, 0), objArr13);
                o.eg.b v = bVar.v(((String) objArr13[0]).intern());
                Object[] objArr14 = new Object[1];
                q("ښ锂", 37781 - View.resolveSize(0, 0), objArr14);
                String intern4 = ((String) objArr14[0]).intern();
                Object[] objArr15 = new Object[1];
                p(null, TextUtils.lastIndexOf("", '0', 0) + 128, null, "\u0092\u008f\u0088", objArr15);
                this.i = new o.dh.c(intern4, v.B(((String) objArr15[0]).intern()));
                Object[] objArr16 = new Object[1];
                p(null, 127 - View.MeasureSpec.makeMeasureSpec(0, 0), null, "¤£", objArr16);
                String intern5 = ((String) objArr16[0]).intern();
                Object[] objArr17 = new Object[1];
                p(null, (ViewConfiguration.getFadingEdgeLength() >> 16) + 127, null, "\u0090\u008a\u0088", objArr17);
                this.k = new o.dh.c(intern5, v.B(((String) objArr17[0]).intern()));
                return;
            }
            Object[] objArr18 = new Object[1];
            p(null, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 127, null, "\u0092\u008e\u0097\u008e\u0092\u0086\u008c\u008d\u008a\u0089\u0088\u0096\u008a\u0092\u0090\u0090\u0088\u0086\u0089\u0088\u0086\u0091\u008d\u008c\u0086\u008e\u008b\u0088\u0095\u0086\u0089\u0088\u0086\u0091\u008e\u0094\u0089\u008a\u008e\u008c\u0086\u0093\u0086\u008e\u0092\u008a\u008f\u008d\u0091\u0090\u0086\u008c\u008a\u0086\u0087\u008e\u008c\u008a\u008f\u008e\u0087\u0086\u0089\u008d\u008c\u0086\u008b\u008a\u0086\u0088\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr18);
            throw new i(((String) objArr18[0]).intern());
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void p(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 722
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.d.p(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void q(java.lang.String r18, int r19, java.lang.Object[] r20) {
            /*
                Method dump skipped, instructions count: 474
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.d.q(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e$b.smali */
    static final class b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static long g;
        private static int h;
        private static char[] i;
        private static char j;

        /* renamed from: o, reason: collision with root package name */
        private static int f51o;
        final o.dh.c a;
        final byte b;
        final C0034e c;
        final o.dh.c d;
        final boolean e;
        final C0034e f;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            f51o = 1;
            i = new char[]{30586, 30567, 30559, 30591, 30562, 30589, 30582, 30532, 30569, 30560, 30570, 30539, 30565, 30561, 30523, 30540, 30574, 30583, 30587, 30518, 30573, 30566, 30572, 30519, 30525};
            j = (char) 17040;
            g = 2427628538924267477L;
        }

        static void init$0() {
            $$a = new byte[]{114, 12, -103, 122};
            $$b = 18;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void m(int r7, int r8, short r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 * 4
                int r7 = 1 - r7
                int r8 = r8 * 2
                int r8 = 4 - r8
                int r9 = 73 - r9
                byte[] r0 = o.cp.e.b.$$a
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L18
                r9 = r8
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r7
                goto L34
            L18:
                r3 = r2
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L1d:
                int r4 = r3 + 1
                byte r5 = (byte) r7
                r1[r3] = r5
                if (r4 != r8) goto L2c
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2c:
                r3 = r0[r9]
                r6 = r10
                r10 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r6
            L34:
                int r7 = r7 + r8
                int r9 = r9 + 1
                r8 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.b.m(int, int, short, java.lang.Object[]):void");
        }

        b(o.eg.b bVar) throws o.eg.d {
            if (bVar == null) {
                this.e = false;
                this.a = null;
                this.d = null;
                this.b = (byte) 0;
                this.c = null;
                this.f = null;
                return;
            }
            this.e = true;
            Object[] objArr = new Object[1];
            k((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 2, "\u0018\u0014", (byte) (15 - ExpandableListView.getPackedPositionGroup(0L)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("㷚㶻㋧֝춫䁕ꏏ\ue51b\ue180↺蟱셺藦\uddd8", ViewConfiguration.getEdgeSlop() >> 16, objArr2);
            this.a = new o.dh.c(intern, bVar.B(((String) objArr2[0]).intern()));
            Object[] objArr3 = new Object[1];
            k((Process.myTid() >> 22) + 2, "\u0018\u0013", (byte) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + Opcodes.DREM), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l("回嚿咎援\ude67댟뀃ᙇ誟䟐鐾㈕\ueee1뮷\uf862\uee05싉齾", ViewConfiguration.getDoubleTapTimeout() >> 16, objArr4);
            this.d = new o.dh.c(intern2, bVar.B(((String) objArr4[0]).intern()));
            Object[] objArr5 = new Object[1];
            l("ꄁꅢ鱷\uab1cᭈ륩甌ᰂ絋輎儑㡖ᤘ獃㵈\ue462㔁", Color.blue(0), objArr5);
            this.b = bVar.D(((String) objArr5[0]).intern()).byteValue();
            Object[] objArr6 = new Object[1];
            k(13 - Color.red(0), "\u0016\u0017\u0002\u0007\u0005\u0000\u0005\f\u0005\u0010\u000f\n㘄", (byte) (TextUtils.getCapsMode("", 0, 0) + 22), objArr6);
            this.c = b(bVar.v(((String) objArr6[0]).intern()));
            Object[] objArr7 = new Object[1];
            l("휽흉\udc90\uebf5츥螬ꁚ⋗\u0b7b쿠葰ڄ漽㎿\ue823\uda89䌠ᝫ쳪빟\ua7ef筎", Color.alpha(0), objArr7);
            this.f = b(bVar.v(((String) objArr7[0]).intern()));
        }

        private static C0034e b(o.eg.b bVar) throws o.eg.d {
            Object[] objArr = new Object[1];
            k((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 10, "\u0014\f\b\u000f\u0017\u0006\u0016\u0017\u0011\u0013㘑", (byte) (18 - TextUtils.indexOf("", "")), objArr);
            String r = bVar.r(((String) objArr[0]).intern());
            Object[] objArr2 = new Object[1];
            k(9 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "\f\u000f\u0004\b\u000e\u000b\u0012\u0017", (byte) (101 - TextUtils.lastIndexOf("", '0', 0, 0)), objArr2);
            byte[] B = bVar.B(((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            l("髊骸魴갛ꆦ졧쿟洘䚋蠨\uebfe䥄⋨", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1, objArr3);
            String c = bVar.c(((String) objArr3[0]).intern(), "");
            Object[] objArr4 = new Object[1];
            k((ViewConfiguration.getPressedStateDuration() >> 16) + 14, "\f\u000f\u0001\u0017\u0006\u000f\u0010\u0017\b\u000e\u0010\u0015\u000f\r", (byte) (93 - Process.getGidForName("")), objArr4);
            o.eg.b v = bVar.v(((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 5, "\t\u000e\u0012\u0017㙭", (byte) (123 - View.MeasureSpec.makeMeasureSpec(0, 0)), objArr5);
            int parseInt = Integer.parseInt(v.r(((String) objArr5[0]).intern()));
            Object[] objArr6 = new Object[1];
            l("幦帟\uf163옌ㅃ妷弶ﳛ", 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr6);
            o.dk.b.a(parseInt, Integer.parseInt(v.r(((String) objArr6[0]).intern())));
            C0034e c0034e = new C0034e(Base64.decode(r, 2), B, Base64.decode(c, 2));
            int i2 = f51o + 61;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return c0034e;
            }
        }

        private static void k(int i2, String str, byte b, Object[] objArr) {
            char[] cArr;
            int i3;
            int i4 = $10;
            int i5 = i4 + 5;
            $11 = i5 % 128;
            int i6 = i5 % 2;
            if (str != null) {
                int i7 = i4 + 1;
                $11 = i7 % 128;
                if (i7 % 2 == 0) {
                    cArr = str.toCharArray();
                    int i8 = 35 / 0;
                } else {
                    cArr = str.toCharArray();
                }
            } else {
                cArr = str;
            }
            char[] cArr2 = cArr;
            m mVar = new m();
            char[] cArr3 = i;
            int i9 = -1401577988;
            switch (cArr3 != null ? 'S' : '?') {
                case Opcodes.AASTORE /* 83 */:
                    int length = cArr3.length;
                    char[] cArr4 = new char[length];
                    int i10 = $11 + Opcodes.LSHR;
                    $10 = i10 % 128;
                    int i11 = i10 % 2;
                    int i12 = 0;
                    while (i12 < length) {
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr3[i12])};
                            Object obj = o.e.a.s.get(Integer.valueOf(i9));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(17 - (Process.myPid() >> 22), (char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.lastIndexOf("", '0', 0) + 77);
                                byte b2 = (byte) 0;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                m(b2, b3, b3, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr4[i12] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i12++;
                            i9 = -1401577988;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr3 = cArr4;
                    break;
            }
            try {
                Object[] objArr4 = {Integer.valueOf(j)};
                Object obj2 = o.e.a.s.get(-1401577988);
                if (obj2 == null) {
                    Class cls2 = (Class) o.e.a.c(17 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) (ViewConfiguration.getFadingEdgeLength() >> 16), 76 - KeyEvent.keyCodeFromString(""));
                    byte b4 = (byte) 0;
                    byte b5 = b4;
                    Object[] objArr5 = new Object[1];
                    m(b4, b5, b5, objArr5);
                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj2);
                }
                char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                char[] cArr5 = new char[i2];
                switch (i2 % 2 != 0) {
                    case true:
                        int i13 = $11 + Opcodes.LMUL;
                        $10 = i13 % 128;
                        int i14 = i13 % 2;
                        i3 = i2 - 1;
                        cArr5[i3] = (char) (cArr2[i3] - b);
                        break;
                    default:
                        i3 = i2;
                        break;
                }
                if (i3 > 1) {
                    mVar.b = 0;
                    while (true) {
                        switch (mVar.b >= i3) {
                            case true:
                                break;
                            default:
                                int i15 = $10 + Opcodes.DSUB;
                                $11 = i15 % 128;
                                int i16 = i15 % 2;
                                mVar.e = cArr2[mVar.b];
                                mVar.a = cArr2[mVar.b + 1];
                                switch (mVar.e == mVar.a) {
                                    case true:
                                        cArr5[mVar.b] = (char) (mVar.e - b);
                                        cArr5[mVar.b + 1] = (char) (mVar.a - b);
                                        break;
                                    default:
                                        try {
                                            Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                            Object obj3 = o.e.a.s.get(696901393);
                                            if (obj3 == null) {
                                                Class cls3 = (Class) o.e.a.c(View.resolveSizeAndState(0, 0, 0) + 10, (char) (KeyEvent.keyCodeFromString("") + 8856), 324 - Color.red(0));
                                                byte b6 = (byte) 0;
                                                Object[] objArr7 = new Object[1];
                                                m(b6, b6, (byte) $$a.length, objArr7);
                                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(696901393, obj3);
                                            }
                                            if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() != mVar.h) {
                                                switch (mVar.c == mVar.d ? '5' : (char) 25) {
                                                    case 25:
                                                        int i17 = (mVar.c * charValue) + mVar.h;
                                                        int i18 = (mVar.d * charValue) + mVar.i;
                                                        cArr5[mVar.b] = cArr3[i17];
                                                        cArr5[mVar.b + 1] = cArr3[i18];
                                                        break;
                                                    default:
                                                        int i19 = $11 + Opcodes.LSUB;
                                                        $10 = i19 % 128;
                                                        if (i19 % 2 != 0) {
                                                        }
                                                        mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                        mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                        int i20 = (mVar.c * charValue) + mVar.i;
                                                        int i21 = (mVar.d * charValue) + mVar.h;
                                                        cArr5[mVar.b] = cArr3[i20];
                                                        cArr5[mVar.b + 1] = cArr3[i21];
                                                        break;
                                                }
                                            } else {
                                                int i22 = $10 + Opcodes.LSHR;
                                                $11 = i22 % 128;
                                                int i23 = i22 % 2;
                                                try {
                                                    Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                    Object obj4 = o.e.a.s.get(1075449051);
                                                    if (obj4 == null) {
                                                        Class cls4 = (Class) o.e.a.c(11 - Color.argb(0, 0, 0, 0), (char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 66 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                                                        byte b7 = (byte) 0;
                                                        byte b8 = b7;
                                                        Object[] objArr9 = new Object[1];
                                                        m(b7, b8, (byte) (b8 + 3), objArr9);
                                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                        o.e.a.s.put(1075449051, obj4);
                                                    }
                                                    int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                    int i24 = (mVar.d * charValue) + mVar.h;
                                                    cArr5[mVar.b] = cArr3[intValue];
                                                    cArr5[mVar.b + 1] = cArr3[i24];
                                                    break;
                                                } catch (Throwable th2) {
                                                    Throwable cause2 = th2.getCause();
                                                    if (cause2 == null) {
                                                        throw th2;
                                                    }
                                                    throw cause2;
                                                }
                                            }
                                        } catch (Throwable th3) {
                                            Throwable cause3 = th3.getCause();
                                            if (cause3 == null) {
                                                throw th3;
                                            }
                                            throw cause3;
                                        }
                                }
                                mVar.b += 2;
                        }
                    }
                }
                for (int i25 = 0; i25 < i2; i25++) {
                    cArr5[i25] = (char) (cArr5[i25] ^ 13722);
                }
                objArr[0] = new String(cArr5);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void l(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 352
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cp.e.b.l(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.cp.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cp\e$e.smali */
    static final class C0034e {
        final byte[] a;
        final byte[] b;
        final byte[] d;

        C0034e(byte[] bArr, byte[] bArr2, byte[] bArr3) {
            this.b = bArr;
            this.d = bArr2;
            this.a = bArr3;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void e(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 1030
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cp.e.e(int[], int, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:102:0x02b5, code lost:
    
        r2 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 826
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cp.e.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
