package fr.antelop.sdk.card;

import o.er.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali_classes17\fr\antelop\sdk\card\AccountInfo.smali */
public final class AccountInfo {
    private final j innerAccountInfo;

    public AccountInfo(j jVar) {
        this.innerAccountInfo = jVar;
    }

    public final String getAccountNumber() {
        return this.innerAccountInfo.b();
    }

    public final String getIssuerAccountId() {
        return this.innerAccountInfo.a();
    }

    public final String getAccountLabel() {
        return this.innerAccountInfo.c();
    }

    public final boolean hasEcomStaticToken() {
        return this.innerAccountInfo.d();
    }

    public final String toString() {
        return new StringBuilder("AccountInfo{accountNumber='").append(this.innerAccountInfo.b()).append('\'').append(", issuerAccountId='").append(this.innerAccountInfo.a()).append('\'').append(", accountLabel=").append(this.innerAccountInfo.c()).append('\'').append('}').toString();
    }
}
