package bc.org.bouncycastle.crypto.params;

import androidx.core.view.InputDeviceCompat;
import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\params\ECDomainParameters.smali */
public class ECDomainParameters implements ECConstants {
    private final ECCurve a;
    private final byte[] b;
    private final ECPoint c;
    private final BigInteger d;
    private final BigInteger e;
    private BigInteger f;

    public ECDomainParameters(X9ECParameters x9ECParameters) {
        this(x9ECParameters.getCurve(), x9ECParameters.getG(), x9ECParameters.getN(), x9ECParameters.getH(), x9ECParameters.getSeed());
    }

    static ECPoint a(ECCurve eCCurve, ECPoint eCPoint) {
        if (eCPoint == null) {
            throw new NullPointerException("Point cannot be null");
        }
        ECPoint normalize = ECAlgorithms.importPoint(eCCurve, eCPoint).normalize();
        if (normalize.isInfinity()) {
            throw new IllegalArgumentException("Point at infinity");
        }
        if (normalize.isValid()) {
            return normalize;
        }
        throw new IllegalArgumentException("Point not on curve");
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof ECDomainParameters)) {
            return false;
        }
        ECDomainParameters eCDomainParameters = (ECDomainParameters) obj;
        return this.a.equals(eCDomainParameters.a) && this.c.equals(eCDomainParameters.c) && this.d.equals(eCDomainParameters.d);
    }

    public ECCurve getCurve() {
        return this.a;
    }

    public ECPoint getG() {
        return this.c;
    }

    public BigInteger getH() {
        return this.e;
    }

    public synchronized BigInteger getHInv() {
        if (this.f == null) {
            this.f = f1.b(this.d, this.e);
        }
        return this.f;
    }

    public BigInteger getN() {
        return this.d;
    }

    public byte[] getSeed() {
        return Arrays.clone(this.b);
    }

    public int hashCode() {
        return ((((this.a.hashCode() ^ 1028) * InputDeviceCompat.SOURCE_KEYBOARD) ^ this.c.hashCode()) * InputDeviceCompat.SOURCE_KEYBOARD) ^ this.d.hashCode();
    }

    public BigInteger validatePrivateScalar(BigInteger bigInteger) {
        if (bigInteger == null) {
            throw new NullPointerException("Scalar cannot be null");
        }
        if (bigInteger.compareTo(ECConstants.ONE) < 0 || bigInteger.compareTo(getN()) >= 0) {
            throw new IllegalArgumentException("Scalar is not in the interval [1, n - 1]");
        }
        return bigInteger;
    }

    public ECPoint validatePublicPoint(ECPoint eCPoint) {
        return a(getCurve(), eCPoint);
    }

    public ECDomainParameters(ECCurve eCCurve, ECPoint eCPoint, BigInteger bigInteger) {
        this(eCCurve, eCPoint, bigInteger, ECConstants.ONE, null);
    }

    public ECDomainParameters(ECCurve eCCurve, ECPoint eCPoint, BigInteger bigInteger, BigInteger bigInteger2) {
        this(eCCurve, eCPoint, bigInteger, bigInteger2, null);
    }

    public ECDomainParameters(ECCurve eCCurve, ECPoint eCPoint, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr) {
        this.f = null;
        if (eCCurve == null) {
            throw new NullPointerException("curve");
        }
        if (bigInteger != null) {
            this.a = eCCurve;
            this.c = a(eCCurve, eCPoint);
            this.d = bigInteger;
            this.e = bigInteger2;
            this.b = Arrays.clone(bArr);
            return;
        }
        throw new NullPointerException("n");
    }
}
