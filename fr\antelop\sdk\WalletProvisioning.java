package fr.antelop.sdk;

import android.content.Context;
import fr.antelop.sdk.exception.WalletValidationException;
import o.ei.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\WalletProvisioning.smali */
public final class WalletProvisioning {
    private final d walletProvisioning;

    public WalletProvisioning(Context context, WalletProvisioningCallback walletProvisioningCallback) throws WalletValidationException {
        this.walletProvisioning = new d(context, walletProvisioningCallback);
    }

    public final void initialize() {
        this.walletProvisioning.e();
    }

    public final void launch(byte[] bArr, String str) throws WalletValidationException {
        this.walletProvisioning.e(bArr, str);
    }

    public final void launch(String str, String str2, String str3, String str4) throws WalletValidationException {
        this.walletProvisioning.e(str, str2, str3, str4);
    }

    public final void launch(byte[] bArr, byte[] bArr2, String str) throws WalletValidationException {
        this.walletProvisioning.e(bArr, bArr2, str);
    }

    public final void checkEligibility(boolean z) throws WalletValidationException {
        this.walletProvisioning.e(z);
    }

    public final void clean() {
        this.walletProvisioning.c();
    }
}
