package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import java.io.IOException;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\BlowfishSerializer.smali */
public class BlowfishSerializer extends Serializer {
    private static SecretKeySpec keySpec;
    private final Serializer serializer;

    public BlowfishSerializer(Serializer serializer, byte[] key) {
        this.serializer = serializer;
        keySpec = new SecretKeySpec(key, "Blowfish");
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, Object object) {
        Cipher cipher = getCipher(1);
        CipherOutputStream cipherStream = new CipherOutputStream(output, cipher);
        Output cipherOutput = new Output(cipherStream, 256) { // from class: com.esotericsoftware.kryo.serializers.BlowfishSerializer.1
            @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
            public void close() throws KryoException {
            }
        };
        this.serializer.write(kryo, cipherOutput, object);
        cipherOutput.flush();
        try {
            cipherStream.close();
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object read(Kryo kryo, Input input, Class type) {
        Cipher cipher = getCipher(2);
        CipherInputStream cipherInput = new CipherInputStream(input, cipher);
        return this.serializer.read(kryo, new Input(cipherInput, 256), type);
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public Object copy(Kryo kryo, Object original) {
        return this.serializer.copy(kryo, original);
    }

    private static Cipher getCipher(int mode) {
        try {
            Cipher cipher = Cipher.getInstance("Blowfish");
            cipher.init(mode, keySpec);
            return cipher;
        } catch (Exception ex) {
            throw new KryoException(ex);
        }
    }
}
