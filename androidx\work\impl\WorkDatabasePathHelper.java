package androidx.work.impl;

import android.content.Context;
import androidx.work.Logger;
import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.TuplesKt;
import kotlin.collections.MapsKt;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;
import kotlin.ranges.RangesKt;

/* compiled from: WorkDatabasePathHelper.kt */
@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010$\n\u0000\bÇ\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0003J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\f2\u0006\u0010\u0005\u001a\u00020\u0006¨\u0006\r"}, d2 = {"Landroidx/work/impl/WorkDatabasePathHelper;", "", "()V", "getDatabasePath", "Ljava/io/File;", "context", "Landroid/content/Context;", "getDefaultDatabasePath", "getNoBackupPath", "migrateDatabase", "", "migrationPaths", "", "work-runtime_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\WorkDatabasePathHelper.smali */
public final class WorkDatabasePathHelper {
    public static final WorkDatabasePathHelper INSTANCE = new WorkDatabasePathHelper();

    private WorkDatabasePathHelper() {
    }

    @JvmStatic
    public static final void migrateDatabase(Context context) {
        String str;
        String message;
        String str2;
        String str3;
        Intrinsics.checkNotNullParameter(context, "context");
        WorkDatabasePathHelper workDatabasePathHelper = INSTANCE;
        File defaultDatabasePath = workDatabasePathHelper.getDefaultDatabasePath(context);
        if (defaultDatabasePath.exists()) {
            Logger logger = Logger.get();
            str = WorkDatabasePathHelperKt.TAG;
            logger.debug(str, "Migrating WorkDatabase to the no-backup directory");
            Map $this$forEach$iv = workDatabasePathHelper.migrationPaths(context);
            for (Map.Entry element$iv : $this$forEach$iv.entrySet()) {
                File source = element$iv.getKey();
                File destination = element$iv.getValue();
                if (source.exists()) {
                    if (destination.exists()) {
                        Logger logger2 = Logger.get();
                        str3 = WorkDatabasePathHelperKt.TAG;
                        logger2.warning(str3, "Over-writing contents of " + destination);
                    }
                    boolean renamed = source.renameTo(destination);
                    if (renamed) {
                        message = "Migrated " + source + "to " + destination;
                    } else {
                        message = "Renaming " + source + " to " + destination + " failed";
                    }
                    Logger logger3 = Logger.get();
                    str2 = WorkDatabasePathHelperKt.TAG;
                    logger3.debug(str2, message);
                }
            }
        }
    }

    public final Map<File, File> migrationPaths(Context context) {
        String[] strArr;
        Intrinsics.checkNotNullParameter(context, "context");
        File databasePath = getDefaultDatabasePath(context);
        File migratedPath = getDatabasePath(context);
        strArr = WorkDatabasePathHelperKt.DATABASE_EXTRA_FILES;
        int capacity$iv = RangesKt.coerceAtLeast(MapsKt.mapCapacity(strArr.length), 16);
        Map destination$iv$iv = new LinkedHashMap(capacity$iv);
        int length = strArr.length;
        int i = 0;
        while (i < length) {
            String str = strArr[i];
            Pair pair = TuplesKt.to(new File(databasePath.getPath() + str), new File(migratedPath.getPath() + str));
            destination$iv$iv.put(pair.getFirst(), pair.getSecond());
            i++;
            strArr = strArr;
        }
        return MapsKt.plus(destination$iv$iv, TuplesKt.to(databasePath, migratedPath));
    }

    public final File getDefaultDatabasePath(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        File databasePath = context.getDatabasePath(WorkDatabasePathHelperKt.WORK_DATABASE_NAME);
        Intrinsics.checkNotNullExpressionValue(databasePath, "context.getDatabasePath(WORK_DATABASE_NAME)");
        return databasePath;
    }

    public final File getDatabasePath(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        return getNoBackupPath(context);
    }

    private final File getNoBackupPath(Context context) {
        return new File(Api21Impl.INSTANCE.getNoBackupFilesDir(context), WorkDatabasePathHelperKt.WORK_DATABASE_NAME);
    }
}
