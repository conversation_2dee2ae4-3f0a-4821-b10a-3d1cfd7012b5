package androidx.core.widget;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.core.widget.NestedScrollView;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\widget\NestedScrollView$SavedState$1.smali */
class NestedScrollView$SavedState$1 implements Parcelable.Creator<NestedScrollView.SavedState> {
    NestedScrollView$SavedState$1() {
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // android.os.Parcelable.Creator
    public NestedScrollView.SavedState createFromParcel(Parcel in) {
        return new NestedScrollView.SavedState(in);
    }

    /* JADX WARN: Can't rename method to resolve collision */
    @Override // android.os.Parcelable.Creator
    public NestedScrollView.SavedState[] newArray(int size) {
        return new NestedScrollView.SavedState[size];
    }
}
