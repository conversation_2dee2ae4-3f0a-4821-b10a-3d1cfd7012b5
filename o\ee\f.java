package o.ee;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\f.smali */
public final class f {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        e();
        int i = b + 33;
        e = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        a = new char[]{50911, 50845, 50836, 50942, 50915, 50940, 50837, 50849, 50822, 50942, 50915, 50824, 50830, 50830, 50832};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 4
            byte[] r0 = o.ee.f.$$a
            int r7 = r7 + 66
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2c
        L15:
            r3 = r2
        L16:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r6 = r6 + r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.f.f(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{12, 98, 124, -66};
        $$b = Opcodes.DCMPL;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0043. Please report as an issue. */
    public static boolean c(String str) {
        int i = e + Opcodes.DREM;
        b = i % 128;
        int i2 = i % 2;
        String e2 = o.e((CharSequence) str);
        Object[] objArr = new Object[1];
        d("\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{0, 15, 0, 0}, true, objArr);
        switch (e2.matches(((String) objArr[0]).intern())) {
            case true:
                int i3 = e + 27;
                int i4 = i3 % 128;
                b = i4;
                switch (i3 % 2 != 0 ? '\f' : '3') {
                }
                int i5 = i4 + Opcodes.DNEG;
                e = i5 % 128;
                int i6 = i5 % 2;
                return true;
            default:
                return false;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:151:0x0021, code lost:
    
        if (r0 != 0) goto L20;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v34, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void d(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 986
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.f.d(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
