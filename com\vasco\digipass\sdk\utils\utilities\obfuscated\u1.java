package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.WNafUtil;
import bc.org.bouncycastle.math.ec.custom.djb.Curve25519;
import bc.org.bouncycastle.math.ec.custom.gm.SM2P256V1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP128R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP160K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP160R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP160R2Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP192K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP192R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP224K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP224R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP256K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP256R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP384R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecP521R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT113R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT113R2Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT131R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT131R2Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT163K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT163R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT163R2Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT193R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT193R2Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT233K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT233R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT239K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT283K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT283R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT409K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT409R1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT571K1Curve;
import bc.org.bouncycastle.math.ec.custom.sec.SecT571R1Curve;
import bc.org.bouncycastle.math.ec.endo.GLVTypeBEndomorphism;
import bc.org.bouncycastle.math.ec.endo.GLVTypeBParameters;
import bc.org.bouncycastle.math.ec.endo.ScalarSplitParameters;
import com.esotericsoftware.asm.Opcodes;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;
import java.util.Vector;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1.smali */
public class u1 {
    static e8 a = new k();
    static e8 b = new v();
    static e8 c = new z();
    static e8 d = new a0();
    static e8 e = new b0();
    static e8 f = new c0();
    static e8 g = new d0();
    static e8 h = new e0();
    static e8 i = new f0();
    static e8 j = new a();
    static e8 k = new b();
    static e8 l = new c();
    static e8 m = new d();
    static e8 n = new e();

    /* renamed from: o, reason: collision with root package name */
    static e8 f30o = new f();
    static e8 p = new g();
    static e8 q = new h();
    static e8 r = new i();
    static e8 s = new j();
    static e8 t = new l();
    static e8 u = new m();
    static e8 v = new n();
    static e8 w = new o();
    static e8 x = new p();
    static e8 y = new q();
    static e8 z = new r();
    static e8 A = new s();
    static e8 B = new t();
    static e8 C = new u();
    static e8 D = new w();
    static e8 E = new x();
    static e8 F = new y();
    static final Hashtable G = new Hashtable();
    static final Hashtable H = new Hashtable();
    static final Hashtable I = new Hashtable();
    static final Hashtable J = new Hashtable();
    static final Vector K = new Vector();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$a.smali */
    class a extends e8 {
        a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP256K1Curve(), new GLVTypeBParameters(new BigInteger("7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee", 16), new BigInteger("5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("3086d221a7d46bcde86c90e49284eb15", 16), new BigInteger("-e4437ed6010e88286f547fa90abfe4c3", 16)}, new BigInteger[]{new BigInteger("114ca50f7a8e2f3f657c1108d9d44cfd8", 16), new BigInteger("3086d221a7d46bcde86c90e49284eb15", 16)}, new BigInteger("3086d221a7d46bcde86c90e49284eb153dab", 16), new BigInteger("e4437ed6010e88286f547fa90abfe4c42212", 16), 272)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0479BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$a0.smali */
    class a0 extends e8 {
        a0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP160R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("1053CDE42C14D696E67687561517533BF3F83345");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "044A96B5688EF573284664698968C38BB913CBFC8223A628553168947D59DCC912042351377AC5FB32"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$b.smali */
    class b extends e8 {
        b() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP256R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("C49D360886E704936A6678E1139D26B7819F7E90");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "046B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C2964FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$b0.smali */
    class b0 extends e8 {
        b0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP160R2Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("B99B99B099B323E02709A4D696E6768756151751");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0452DCB034293A117E1F4FF11B30F7199D3144CE6DFEAFFEF2E331F296E071FA0DF9982CFEA7D43F2E"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$c.smali */
    class c extends e8 {
        c() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP384R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("A335926AA319A27A1D00896A6773A4827ACDAC73");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB73617DE4A96262C6F5D9E98BF9292DC29F8F41DBD289A147CE9DA3113B5F0B8C00A60B1CE1D7E819D7A431D7C90EA0E5F"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$c0.smali */
    class c0 extends e8 {
        c0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP192K1Curve(), new GLVTypeBParameters(new BigInteger("bb85691939b869c1d087f601554b96b80cb4f55b35f433c2", 16), new BigInteger("3d84f26c12238d7b4f3d516613c1759033b1a5800175d0b1", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("71169be7330b3038edb025f1", 16), new BigInteger("-b3fb3400dec5c4adceb8655c", 16)}, new BigInteger[]{new BigInteger("12511cfe811d0f4e6bc688b4d", 16), new BigInteger("71169be7330b3038edb025f1", 16)}, new BigInteger("71169be7330b3038edb025f1d0f9", 16), new BigInteger("b3fb3400dec5c4adceb8655d4c94", 16), 208)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$d.smali */
    class d extends e8 {
        d() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP521R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("D09E8800291CB85396CC6717393284AAA0DA64BA");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0400C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66011839296A789A3BC0045C8A5FB42C7D1BD998F54449579B446817AFBD17273E662C97EE72995EF42640C550B9013FAD0761353C7086A272C24088BE94769FD16650"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$d0.smali */
    class d0 extends e8 {
        d0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP192R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("3045AE6FC8422F64ED579528D38120EAE12196D5");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF101207192B95FFC8DA78631011ED6B24CDD573F977A11E794811"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$e.smali */
    class e extends e8 {
        e() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT113R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("10E723AB14D696E6768756151756FEBF8FCB49A9");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04009D73616F35F4AB1407D73562C10F00A52830277958EE84D1315ED31886"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$e0.smali */
    class e0 extends e8 {
        e0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP224K1Curve(), new GLVTypeBParameters(new BigInteger("fe0e87005b4e83761908c5131d552a850b3f58b749c37cf5b84d6768", 16), new BigInteger("60dcd2104c4cbc0be6eeefc2bdd610739ec34e317f9b33046c9e4788", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("6b8cf07d4ca75c88957d9d670591", 16), new BigInteger("-b8adf1378a6eb73409fa6c9c637d", 16)}, new BigInteger[]{new BigInteger("1243ae1b4d71613bc9f780a03690e", 16), new BigInteger("6b8cf07d4ca75c88957d9d670591", 16)}, new BigInteger("6b8cf07d4ca75c88957d9d67059037a4", 16), new BigInteger("b8adf1378a6eb73409fa6c9c637ba7f5", 16), 240)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04A1455B334DF099DF30FC28A169A467E9E47075A90F7E650EB6B7A45C7E089FED7FBA344282CAFBD6F7E319F7C0B0BD59E2CA4BDB556D61A5"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$f.smali */
    class f extends e8 {
        f() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT113R2Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("10C0FB15760860DEF1EEF4D696E676875615175D");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0401A57A6A7B26CA5EF52FCDB816479700B3ADC94ED1FE674C06E695BABA1D"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$f0.smali */
    class f0 extends e8 {
        f0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP224R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("BD71344799D5C7FCDC45B59FA3B9AB8F6A948BC5");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$g.smali */
    class g extends e8 {
        g() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT131R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("4D696E676875615175985BD3ADBADA21B43A97E2");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040081BAF91FDF9833C40F9C181343638399078C6E7EA38C001F73C8134B1B4EF9E150"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$h.smali */
    class h extends e8 {
        h() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT131R2Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("985BD3ADBAD4D696E676875615175A21B43A97E3");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040356DCD8F2F95031AD652D23951BB366A80648F06D867940A5366D9E265DE9EB240F"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$i.smali */
    class i extends e8 {
        i() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT163K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0402FE13C0537BBC11ACAA07D793DE4E6D5E5C94EEE80289070FB05D38FF58321F2E800536D538CCDAA3D9"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$j.smali */
    class j extends e8 {
        j() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT163R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("24B7B137C8A14D696E6768756151756FD0DA2E5C");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040369979697AB43897789566789567F787A7876A65400435EDB42EFAFB2989D51FEFCE3C80988F41FF883"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$k.smali */
    class k extends e8 {
        k() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new Curve25519());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "042AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD245A20AE19A1B8A086B4E01EDD2C7748D14C923D4D7E6D7C61B229E9C5A27ECED3D9"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$l.smali */
    class l extends e8 {
        l() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT163R2Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("85E25BFE5C86226CDB12016F7553F9D0E693A268");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0403F0EBA16286A2D57EA0991168D4994637E8343E3600D51FBC6C71A0094FA2CDD545B11C5C0C797324F1"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$m.smali */
    class m extends e8 {
        m() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT193R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("103FAEC74D696E676875615175777FC5B191EF30");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0401F481BC5F0FF84A74AD6CDF6FDEF4BF6179625372D8C0C5E10025E399F2903712CCF3EA9E3A1AD17FB0B3201B6AF7CE1B05"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$n.smali */
    class n extends e8 {
        n() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT193R2Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("10B7B4D696E676875615175137C8A16FD0DA2211");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0400D9B67D192E0367C803F39E1A7E82CA14A651350AAE617E8F01CE94335607C304AC29E7DEFBD9CA01F596F927224CDECF6C"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$o.smali */
    class o extends e8 {
        o() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT233K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04017232BA853A7E731AF129F22FF4149563A419C26BF50A4C9D6EEFAD612601DB537DECE819B7F70F555A67C427A8CD9BF18AEB9B56E0C11056FAE6A3"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$p.smali */
    class p extends e8 {
        p() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT233R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("74D59FF07F6B413D0EA14B344B20A2DB049B50C3");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0400FAC9DFCBAC8313BB2139F1BB755FEF65BC391F8B36F8F8EB7371FD558B01006A08A41903350678E58528BEBF8A0BEFF867A7CA36716F7E01F81052"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$q.smali */
    class q extends e8 {
        q() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT239K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0429A0B6A887A983E9730988A68727A8B2D126C44CC2CC7B2A6555193035DC76310804F12E549BDB011C103089E73510ACB275FC312A5DC6B76553F0CA"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$r.smali */
    class r extends e8 {
        r() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT283K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040503213F78CA44883F1A3B8162F188E553CD265F23C1567A16876913B0C2AC245849283601CCDA380F1C9E318D90F95D07E5426FE87E45C0E8184698E45962364E34116177DD2259"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$s.smali */
    class s extends e8 {
        s() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT283R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("77E2B07370EB0F832A6DD5B62DFC88CD06BB84BE");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0405F939258DB7DD90E1934F8C70B0DFEC2EED25B8557EAC9C80E2E198F8CDBECD86B1205303676854FE24141CB98FE6D4B20D02B4516FF702350EDDB0826779C813F0DF45BE8112F4"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$t.smali */
    class t extends e8 {
        t() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT409K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040060F05F658F49C1AD3AB1890F7184210EFD0987E307C84C27ACCFB8F9F67CC2C460189EB5AAAA62EE222EB1B35540CFE902374601E369050B7C4E42ACBA1DACBF04299C3460782F918EA427E6325165E9EA10E3DA5F6C42E9C55215AA9CA27A5863EC48D8E0286B"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$u.smali */
    class u extends e8 {
        u() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT409R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("4099B5A457F9D69F79213D094C4BCD4D4262210B");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04015D4860D088DDB3496B0C6064756260441CDE4AF1771D4DB01FFE5B34E59703DC255A868A1180515603AEAB60794E54BB7996A70061B1CFAB6BE5F32BBFA78324ED106A7636B9C5A7BD198D0158AA4F5488D08F38514F1FDF4B4F40D2181B3681C364BA0273C706"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$v.smali */
    class v extends e8 {
        v() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP128R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("000E0D4D696E6768756151750CC03A4473D03679");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04161FF7528B899B2D0C28607CA52C5B86CF5AC8395BAFEB13C02DA292DDED7A83"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$w.smali */
    class w extends e8 {
        w() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT571K1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "04026EB7A859923FBC82189631F8103FE4AC9CA2970012D5D46024804801841CA44370958493B205E647DA304DB4CEB08CBBD1BA39494776FB988B47174DCA88C7E2945283A01C89720349DC807F4FBF374F4AEADE3BCA95314DD58CEC9F307A54FFC61EFC006D8A2C9D4979C0AC44AEA74FBEBBB9F772AEDCB620B01A7BA7AF1B320430C8591984F601CD4C143EF1C7A3"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$x.smali */
    class x extends e8 {
        x() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecT571R1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("2AA058F73A0E33AB486B0F610410C53A7F132310");
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "040303001D34B856296C16C0D40D3CD7750A93D1D2955FA80AA5F40FC8DB7B2ABDBDE53950F4C0D293CDD711A35B67FB1499AE60038614F1394ABFA3B4C850D927E1E7769C8EEC2D19037BF27342DA639B6DCCFFFEB73D69D78C6C27A6009CBBCA1980F8533921E8A684423E43BAB08A576291AF8F461BB2A8B3531D2F0485C19B16E2F1516E23DD3C1A4827AF1B8AC15B"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$y.smali */
    class y extends e8 {
        y() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SM2P256V1Curve());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "0432C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u1$z.smali */
    class z extends e8 {
        z() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return u1.b(new SecP160K1Curve(), new GLVTypeBParameters(new BigInteger("9ba48cba5ebcb9b6bd33b92830b2a2e0e192f10a", 16), new BigInteger("c39c6c3b3a36d7701b9c71a1f5804ae5d0003f4", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("9162fbe73984472a0a9e", 16), new BigInteger("-96341f1138933bc2f505", 16)}, new BigInteger[]{new BigInteger("127971af8721782ecffa3", 16), new BigInteger("9162fbe73984472a0a9e", 16)}, new BigInteger("9162fbe73984472a0a9d0590", 16), new BigInteger("96341f1138933bc2f503fd44", 16), Opcodes.ARETURN)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, u1.b(c, "043B4C382CE37AA192A4019E763036F4F5DD4D7EBB938CF935318FDCED6BC28286531733C3F03C4FEE"), c.getOrder(), c.getCofactor(), null);
        }
    }

    static {
        a("curve25519", n1.c, a);
        a("secp128r1", b7.u, b);
        a("secp160k1", b7.j, c);
        a("secp160r1", b7.i, d);
        a("secp160r2", b7.w, e);
        a("secp192k1", b7.x, f);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar = b7.G;
        a("secp192r1", wVar, g);
        a("secp224k1", b7.y, h);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar2 = b7.z;
        a("secp224r1", wVar2, i);
        a("secp256k1", b7.k, j);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar3 = b7.H;
        a("secp256r1", wVar3, k);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar4 = b7.A;
        a("secp384r1", wVar4, l);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar5 = b7.B;
        a("secp521r1", wVar5, m);
        a("sect113r1", b7.e, n);
        a("sect113r2", b7.f, f30o);
        a("sect131r1", b7.f21o, p);
        a("sect131r2", b7.p, q);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar6 = b7.b;
        a("sect163k1", wVar6, r);
        a("sect163r1", b7.c, s);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar7 = b7.l;
        a("sect163r2", wVar7, t);
        a("sect193r1", b7.q, u);
        a("sect193r2", b7.r, v);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar8 = b7.s;
        a("sect233k1", wVar8, w);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar9 = b7.t;
        a("sect233r1", wVar9, x);
        a("sect239k1", b7.d, y);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar10 = b7.m;
        a("sect283k1", wVar10, z);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar11 = b7.n;
        a("sect283r1", wVar11, A);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar12 = b7.C;
        a("sect409k1", wVar12, B);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar13 = b7.D;
        a("sect409r1", wVar13, C);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar14 = b7.E;
        a("sect571k1", wVar14, D);
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar15 = b7.F;
        a("sect571r1", wVar15, E);
        a("sm2p256v1", w4.F, F);
        a("B-163", wVar7);
        a("B-233", wVar9);
        a("B-283", wVar11);
        a("B-409", wVar13);
        a("B-571", wVar15);
        a("K-163", wVar6);
        a("K-233", wVar8);
        a("K-283", wVar10);
        a("K-409", wVar12);
        a("K-571", wVar14);
        a("P-192", wVar);
        a("P-224", wVar2);
        a("P-256", wVar3);
        a("P-384", wVar4);
        a("P-521", wVar5);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, String str) {
        f8 f8Var = new f8(eCCurve, z4.a(str));
        WNafUtil.configureBasepoint(f8Var.e());
        return f8Var;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve, GLVTypeBParameters gLVTypeBParameters) {
        return eCCurve.configure().setEndomorphism(new GLVTypeBEndomorphism(eCCurve, gLVTypeBParameters)).create();
    }

    static void a(String str, com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar, e8 e8Var) {
        K.addElement(str);
        J.put(wVar, str);
        I.put(wVar, e8Var);
        String b2 = o7.b(str);
        H.put(b2, wVar);
        G.put(b2, e8Var);
    }

    public static e8 b(String str) {
        return (e8) G.get(o7.b(str));
    }

    public static e8 b(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        return (e8) I.get(wVar);
    }

    static void a(String str, com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        Object obj = I.get(wVar);
        if (obj != null) {
            String b2 = o7.b(str);
            H.put(b2, wVar);
            G.put(b2, obj);
            return;
        }
        throw new IllegalStateException();
    }

    public static X9ECParameters a(String str) {
        e8 b2 = b(str);
        if (b2 == null) {
            return null;
        }
        return b2.d();
    }

    public static X9ECParameters a(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        e8 b2 = b(wVar);
        if (b2 == null) {
            return null;
        }
        return b2.d();
    }

    public static Enumeration a() {
        return K.elements();
    }
}
