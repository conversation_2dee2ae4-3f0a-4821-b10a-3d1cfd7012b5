package o.dg;

import com.esotericsoftware.asm.Opcodes;
import o.bv.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\a.smali */
public final class a {
    private static int h = 0;
    private static int j = 1;
    private final boolean a;
    private final boolean b;
    private final o.de.d c;
    private final Long d;
    private final g e;

    a(boolean z) {
        this(z, false, null, null);
    }

    a(boolean z, boolean z2) {
        this(z, z2, null, null);
    }

    public a(boolean z, boolean z2, Long l, o.de.d dVar) {
        this.e = new g();
        this.b = z;
        this.a = z2;
        this.d = l;
        this.c = dVar;
    }

    public final boolean c() {
        int i = h;
        int i2 = ((i | 21) << 1) - (i ^ 21);
        j = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.b;
        int i4 = ((i | 9) << 1) - (i ^ 9);
        j = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final boolean d() {
        int i = h;
        int i2 = (i ^ 35) + ((i & 35) << 1);
        j = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.a;
        int i4 = ((i | 57) << 1) - (i ^ 57);
        j = i4 % 128;
        switch (i4 % 2 != 0 ? 'W' : '9') {
            case Opcodes.POP /* 87 */:
                return z;
            default:
                int i5 = 49 / 0;
                return z;
        }
    }

    public final Long b() {
        int i = (j + 40) - 1;
        h = i % 128;
        switch (i % 2 != 0 ? 'X' : '0') {
            case '0':
                return this.d;
            default:
                throw null;
        }
    }

    public final g a() {
        int i = j + 89;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        g gVar = this.e;
        int i4 = (i2 + 6) - 1;
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                int i5 = 65 / 0;
                return gVar;
            default:
                return gVar;
        }
    }

    public final o.de.d e() {
        int i = j;
        int i2 = (i & Opcodes.DSUB) + (i | Opcodes.DSUB);
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '\b' : 'C') {
            case '\b':
                throw null;
            default:
                return this.c;
        }
    }
}
