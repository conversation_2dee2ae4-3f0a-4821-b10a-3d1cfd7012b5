package androidx.work.impl.utils;

import java.util.concurrent.Callable;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\IdGenerator$$ExternalSyntheticLambda0.smali */
public final /* synthetic */ class IdGenerator$$ExternalSyntheticLambda0 implements Callable {
    public final /* synthetic */ IdGenerator f$0;

    @Override // java.util.concurrent.Callable
    public final Object call() {
        return IdGenerator.$r8$lambda$fIl4yTd4RSvtLPTyoZbhi4q3Rnk(this.f$0);
    }
}
