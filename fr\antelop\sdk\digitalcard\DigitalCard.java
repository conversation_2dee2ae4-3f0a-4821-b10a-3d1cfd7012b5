package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.card.AccountInfo;
import fr.antelop.sdk.card.CardDisplay;
import fr.antelop.sdk.card.CardInfo;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControlService;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.Date;
import o.eo.e;
import o.er.g;
import o.er.k;
import o.er.l;
import o.er.m;
import o.er.n;
import o.er.o;
import o.er.p;
import o.er.q;
import o.er.r;
import o.er.s;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali_classes17\fr\antelop\sdk\digitalcard\DigitalCard.smali */
public final class DigitalCard {
    private CardDisplay cardDisplay;
    private final e innerCard;
    private CardInfo cardInfo = null;
    private AccountInfo accountInfo = null;

    public DigitalCard(e eVar) {
        this.innerCard = eVar;
    }

    public final void delete(Context context, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerCard.e(context, antelopCallback);
    }

    public final CardInfo getCardInfo() {
        if (this.innerCard.s() == null) {
            return null;
        }
        if (this.cardInfo == null) {
            this.cardInfo = new CardInfo(this.innerCard.s());
        }
        return this.cardInfo;
    }

    public final AccountInfo getAccountInfo() {
        if (this.innerCard.q() == null) {
            return null;
        }
        if (this.accountInfo == null) {
            this.accountInfo = new AccountInfo(this.innerCard.q());
        }
        return this.accountInfo;
    }

    @Deprecated
    public final String getBin() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().c();
        }
        return null;
    }

    @Deprecated
    public final Date getExpiryDate() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().b();
        }
        return null;
    }

    public final String getId() {
        return this.innerCard.e();
    }

    @Deprecated
    public final String getLastDigits() {
        if (this.innerCard.s() != null) {
            return this.innerCard.s().a();
        }
        return null;
    }

    public final String getIssuerData() {
        return this.innerCard.m();
    }

    public final String getIssuerCardId() {
        return this.innerCard.n();
    }

    public final boolean isProvisioned() {
        return this.innerCard.B();
    }

    public final CardStatus getStatus() {
        return this.innerCard.z();
    }

    @Deprecated
    public final CardDisplay getDisplay() {
        if (this.cardDisplay == null) {
            if (this.innerCard.s() != null) {
                this.cardDisplay = this.innerCard.s().d();
            } else {
                return null;
            }
        }
        return this.cardDisplay;
    }

    public final GooglePayService getGooglePayService() throws WalletValidationException {
        return new GooglePayService(this.innerCard);
    }

    public final SamsungPayService getSamsungPayService() throws WalletValidationException {
        return new SamsungPayService(this.innerCard);
    }

    public final ApplePayService getApplePayService() {
        return new ApplePayService();
    }

    public final CardDisplayService getCardDisplayService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new CardDisplayService(new g(eVar, (o.el.e) eVar.H()));
    }

    public final PinDisplayService getPinDisplayService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new PinDisplayService(new n(eVar, (o.el.e) eVar.H()));
    }

    public final TokenManagementService getTokenManagementService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new TokenManagementService(new q(eVar, (o.el.e) eVar.H()));
    }

    public final PushToTokenRequestorService getPushToTokenRequestorService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new PushToTokenRequestorService(new s(eVar, (o.el.e) eVar.H()));
    }

    public final IssuerNfcWalletService getIssuerNfcWalletService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new IssuerNfcWalletService(new m(eVar, (o.el.e) eVar.H()));
    }

    public final VirtualCardNumberService getVirtualCardNumberService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new VirtualCardNumberService(new r(eVar, (o.el.e) eVar.H()));
    }

    public final Boolean requiresCmsActivation() {
        return this.innerCard.a();
    }

    public final CmsActivationService getCmsActivationService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new CmsActivationService(new l(eVar, (o.el.e) eVar.H()));
    }

    public final PinUpdateService getPinUpdateService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new PinUpdateService(new o(eVar, (o.el.e) eVar.H()));
    }

    public final TransactionControlService getTransactionControlService() throws WalletValidationException {
        e eVar = this.innerCard;
        return new TransactionControlService(new p(eVar, (o.el.e) eVar.H()));
    }

    public final void deleteEcomStaticToken(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        new k(context, this.innerCard).c(operationCallback);
    }

    public final String toString() {
        String str = "";
        try {
            if (getCardInfo() != null) {
                str = new StringBuilder("DigitalCard{id='").append(getId()).append('\'').append(", bin='").append(getCardInfo().getBin()).append('\'').append(", expiryDate=").append(getCardInfo().getExpiryDate()).append(", lastDigits='").append(getCardInfo().getLastDigits()).append('\'').append(", issuerData='").append(getIssuerData()).append('\'').append(", issuerCardId='").append(getIssuerCardId()).append('\'').append('}').toString();
            } else if (getAccountInfo() != null) {
                str = new StringBuilder("DigitalCard{id='").append(getId()).append('\'').append(", accountNumber='").append(getAccountInfo().getAccountNumber()).append('\'').append(", accountLabel=").append(getAccountInfo().getAccountLabel()).append(", issuerAccountID='").append(getAccountInfo().getIssuerAccountId()).append('\'').append(", issuerData='").append(getIssuerData()).append('\'').append(", issuerCardId='").append(getIssuerCardId()).append('\'').append('}').toString();
            }
        } catch (Exception e) {
            o.ee.g.c();
            o.ee.g.d("toString error : ".concat(String.valueOf(e)));
        }
        return str;
    }
}
