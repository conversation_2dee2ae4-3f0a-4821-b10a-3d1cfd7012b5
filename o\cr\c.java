package o.cr;

import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cr\c.smali */
public final class c extends a<o.fg.a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static long b;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        e = 1;
        a();
        ExpandableListView.getPackedPositionGroup(0L);
        AudioTrack.getMaxVolume();
        Process.myPid();
        int i = d + 39;
        e = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        a = new char[]{11396, 17920, 63978, 4933, 34316, 14835, 21338, 50736, 31227, 37701, 1545, 47504, 54096, 17964, 63900, 4991, 34365, 14762, 21372, 50888, 31163, 37732, 1752, 47541, 54124, 18131, 11435, 17940, 63984, 4957, 34317, 14801, 21336, 50728, 31204, 37700, 1591, 47493, 54114, 17956, 63872};
        b = -2519660692705687967L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = 105 - r9
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r7 = r7 * 4
            int r7 = r7 + 4
            byte[] r0 = o.cr.c.$$d
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r8 = r8 + r10
            int r7 = r7 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cr.c.i(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{105, 1, -115, -23};
        $$e = 47;
    }

    @Override // o.cr.a
    protected final o.fg.a b(short s, int i, byte[] bArr) {
        g.c();
        Object[] objArr = new Object[1];
        h((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), View.MeasureSpec.getSize(0), TextUtils.getOffsetAfter("", 0) + 26, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 26 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 15 - View.getDefaultSize(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.fg.a aVar = new o.fg.a(true, o.fc.c.b, s);
        aVar.a(i);
        aVar.a(bArr);
        int i2 = e + 13;
        d = i2 % 128;
        int i3 = i2 % 2;
        return aVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 1024
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cr.c.h(char, int, int, java.lang.Object[]):void");
    }
}
