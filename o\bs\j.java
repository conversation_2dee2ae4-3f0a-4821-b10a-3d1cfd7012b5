package o.bs;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\j.smali */
public final class j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static i a;
    private static char[] b;
    private static c d;
    private static final Object e;
    private static int f;
    private static boolean g;
    private static int h;
    private static long i;
    private static boolean j;
    private static int k;
    private final String c;

    static void a() {
        b = new char[]{61612, 61648, 61616, 61638, 61649, 61653, 61642, 61632, 61605, 61634, 61655, 61644, 61658, 61581, 61647, 61639, 61636, 61571, 61582, 61633, 61593, 61645, 61643, 61619, 61622, 61617, 61654, 61637, 61651};
        j = true;
        g = true;
        h = 782102883;
        i = 286304218384869566L;
    }

    static void init$0() {
        $$a = new byte[]{32, 0, 62, 110};
        $$b = Opcodes.DRETURN;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(short r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bs.j.$$a
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r6 = 121 - r6
            int r8 = r8 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L33
        L17:
            r3 = r2
        L18:
            int r8 = r8 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.j.n(short, short, byte, java.lang.Object[]):void");
    }

    public j() {
        Object[] objArr = new Object[1];
        l(null, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0090\u0084\u008b\u0088\u0084\u008f\u0084\u0082\u008e\u008d\u0085\u008c\u008b\u0088\u008a\u0089\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        this.c = ((String) objArr[0]).intern();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        k = 1;
        a();
        Color.argb(0, 0, 0, 0);
        e = new Object();
        int i2 = k + Opcodes.LREM;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    public final c b(Context context) {
        Object[] objArr = new Object[1];
        m("湡퐜\u1aff䂈蝘총㏁禊뱃\ue221⣅溆핖ᬌ䇒螘", 47680 - ExpandableListView.getPackedPositionChild(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        synchronized (e) {
            if (a != i.a) {
                g.c();
                Object[] objArr2 = new Object[1];
                m("湉\u19ca腘\u08e2끙㯸ꍍ⫛퉐巎앇䳀\uf402美\ue700滈ᙐ臖ै냔㡛ꏗ⭑틃婟엙䵓\uf495籜\ue7d6潇ᚑ鹽৪녾㣻ꁣ⯨퍭", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 30593, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                List<c> a2 = a(context);
                Iterator<c> it = a2.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    }
                    c next = it.next();
                    i d2 = next.d(context);
                    i iVar = a;
                    if (iVar == null || iVar.e() > d2.e()) {
                        a = d2;
                        d = next;
                        if (d2 == i.a) {
                            g.c();
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr3 = new Object[1];
                            l(null, 127 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), null, "\u0092\u0095\u0092\u0084\u008f\u0094\u008a\u008f\u0087\u008a\u0086\u008a\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0082\u0092\u0082\u008c\u0092\u0093\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u0084\u0091", objArr3);
                            g.d(intern, sb.append(((String) objArr3[0]).intern()).append(d.b()).toString());
                            if (a2.indexOf(d) != 0) {
                                g.c();
                                Object[] objArr4 = new Object[1];
                                m("湉ᦤ膄ব뇡㧖ꇑ⧕턠夠섛䤎\uf13a礠\ue11c楀Ⴍ飑\u0093裶ヰ룃\u20cd꠴倣\ud859䀋졳火\uf853恋\uebba鏮ᮅ菵௷돗㯆ꌠ⬢팒孉썭䭻\uf355筃\ue2fc檦ነ髸˴諚㋃먹∭ꨍ切\uda6d䉡쩔牄", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 30703, objArr4);
                                g.d(intern, ((String) objArr4[0]).intern());
                                d(context, d.b());
                            } else {
                                g.c();
                                Object[] objArr5 = new Object[1];
                                l(null, 127 - TextUtils.indexOf("", "", 0), null, "\u0090\u0084\u008b\u0088\u0084\u008b\u0084\u0090\u0092\u0084\u0091\u0096\u008a\u0097\u0088\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0082\u0092\u0082\u008c\u0092\u008c\u0096\u0092\u0093\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u0084\u0091", objArr5);
                                g.d(intern, ((String) objArr5[0]).intern());
                            }
                            g.c();
                            Object[] objArr6 = new Object[1];
                            m("湉ࣾꌰ婾\uf489濴ٵꂯ寰\uf21a浟ތ빲夲\uf3e8櫅Ԍ뱎囤\uf120桸ʞ뷎吊컶槬?묙喑쳊杪Ƴ룧匘쩁", Color.rgb(0, 0, 0) + 16803509, objArr6);
                            g.d(intern, ((String) objArr6[0]).intern());
                            d.c(context);
                        }
                    }
                }
            }
            if (a != i.a) {
                g.c();
                Object[] objArr7 = new Object[1];
                l(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "\u0084\u008f\u0094\u008a\u008f\u0087\u008a\u0086\u008a\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0082\u0092\u0082\u008c\u0092\u008c\u0096\u0092\u0093\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u0084\u0091", objArr7);
                g.e(intern, ((String) objArr7[0]).intern());
                return null;
            }
            return d;
        }
    }

    public static o.bb.a d() {
        synchronized (e) {
            i iVar = a;
            if (iVar != null && d != null && iVar != i.a) {
                o.bb.a aVar = o.bb.a.F;
                switch (AnonymousClass1.e[a.ordinal()]) {
                    case 1:
                        aVar = o.bb.a.E;
                    case 2:
                        c cVar = d;
                        if (cVar instanceof b) {
                            aVar = o.bb.a.H;
                            break;
                        } else if (cVar instanceof a) {
                            aVar = o.bb.a.K;
                            break;
                        }
                        break;
                    case 3:
                        c cVar2 = d;
                        if (cVar2 instanceof b) {
                            aVar = o.bb.a.G;
                            break;
                        } else if (cVar2 instanceof a) {
                            aVar = o.bb.a.N;
                            break;
                        }
                        break;
                    case 4:
                        c cVar3 = d;
                        if (cVar3 instanceof b) {
                            aVar = o.bb.a.M;
                            break;
                        } else if (cVar3 instanceof a) {
                            aVar = o.bb.a.S;
                            break;
                        }
                        break;
                    case 5:
                        if (d instanceof b) {
                            aVar = o.bb.a.J;
                            break;
                        }
                        break;
                    case 6:
                        if (d instanceof a) {
                            aVar = o.bb.a.Q;
                            break;
                        }
                        break;
                    case 7:
                        c cVar4 = d;
                        if (cVar4 instanceof b) {
                            aVar = o.bb.a.L;
                            break;
                        } else if (cVar4 instanceof a) {
                            aVar = o.bb.a.O;
                            break;
                        }
                        break;
                }
                g.c();
                Object[] objArr = new Object[1];
                m("湡퐜\u1aff䂈蝘총㏁禊뱃\ue221⣅溆핖ᬌ䇒螘", 47680 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                l(null, 127 - View.combineMeasuredStates(0, 0), null, "\u0092\u0095\u0092\u0096\u008c\u0082\u008a\u0084\u009a\u008d\u008b\u0087\u008f\u0087\u0094\u008a\u008f\u0087\u008a\u0086\u008a\u0096\u0099\u0085\u0084\u0090\u0087\u0086\u008c\u0085\u0098\u008b\u0084\u0091", objArr2);
                g.d(intern, sb.append(((String) objArr2[0]).intern()).append(aVar.b()).toString());
                return aVar;
            }
            g.c();
            Object[] objArr3 = new Object[1];
            m("湡퐜\u1aff䂈蝘총㏁禊뱃\ue221⣅溆핖ᬌ䇒螘", TextUtils.getTrimmedLength("") + 47681, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l(null, 127 - KeyEvent.keyCodeFromString(""), null, "\u0090\u0084\u0096\u0087\u009c\u0084\u0090\u0092\u0096\u008c\u0082\u008a\u0084\u0085\u0092\u008d\u008b\u0087\u008f\u0087\u0094\u008a\u008f\u0087\u008a\u0086\u008a\u0096\u009b\u0092\u0084\u0088\u0087\u0086\u0085\u0084\u0082\u0092\u008c\u0096\u0092\u0095\u0092\u0096\u008c\u0082\u008a\u0084\u009a\u008d\u008b\u0087\u008f\u0087\u0094\u008a\u008f\u0087\u008a\u0086\u008a\u0096\u0099\u0085\u0084\u0090\u0087\u0086\u008c\u0085\u0098\u008b\u0084\u0091", objArr4);
            g.e(intern2, ((String) objArr4[0]).intern());
            return o.bb.a.F;
        }
    }

    /* renamed from: o.bs.j$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\j$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int b = 1;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            d = 0;
            int[] iArr = new int[i.values().length];
            e = iArr;
            try {
                iArr[i.j.ordinal()] = 1;
                int i = b;
                int i2 = ((i | Opcodes.LSHL) << 1) - (i ^ Opcodes.LSHL);
                d = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[i.h.ordinal()] = 2;
                int i4 = b;
                int i5 = (i4 & 29) + (i4 | 29);
                d = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[i.e.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[i.d.ordinal()] = 4;
                int i6 = b + Opcodes.DNEG;
                d = i6 % 128;
                if (i6 % 2 != 0) {
                }
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[i.c.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[i.i.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[i.b.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ int c(String str, c cVar, c cVar2) {
        int i2 = f + 7;
        k = i2 % 128;
        boolean z = i2 % 2 == 0;
        Object obj = null;
        String b2 = cVar.b();
        switch (z) {
            case false:
                switch (b2.equals(str)) {
                    case true:
                        int i3 = k + 23;
                        f = i3 % 128;
                        if (i3 % 2 == 0) {
                            return -1;
                        }
                        obj.hashCode();
                        throw null;
                    default:
                        switch (cVar2.b().equals(str) ? 'E' : 'I') {
                            case 'I':
                                return 0;
                            default:
                                return 1;
                        }
                }
            default:
                b2.equals(str);
                throw null;
        }
    }

    private static List<c> a(Context context) {
        ArrayList arrayList = new ArrayList();
        g.c();
        Object[] objArr = new Object[1];
        m("湡퐜\u1aff䂈蝘총㏁禊뱃\ue221⣅溆핖ᬌ䇒螘", View.MeasureSpec.getMode(0) + 47681, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(null, ((Process.getThreadPriority(0) + 20) >> 6) + 127, null, "\u0090\u0084\u0096\u0087\u009c\u0084\u0090\u0092\u0082\u0085\u0084\u0090\u0087\u0086\u008c\u0085\u0098\u0090\u0084\u008b\u0088\u0084\u008f\u0084\u0083\u0084\u0085\u009d\u0092\u008c\u0096\u0092\u0093\u0092\u0082\u0085\u0084\u0090\u0087\u0086\u008c\u0085\u0098\u008b\u0084\u0091", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        arrayList.add(new b());
        arrayList.add(new a());
        final String e2 = e(context);
        switch (e2 != null) {
            case false:
                break;
            default:
                int i2 = f + 25;
                k = i2 % 128;
                int i3 = i2 % 2;
                Collections.sort(arrayList, new Comparator() { // from class: o.bs.j$$ExternalSyntheticLambda0
                    @Override // java.util.Comparator
                    public final int compare(Object obj, Object obj2) {
                        int c;
                        c = j.c(e2, (c) obj, (c) obj2);
                        return c;
                    }
                });
                break;
        }
        int i4 = f + 73;
        k = i4 % 128;
        int i5 = i4 % 2;
        return arrayList;
    }

    private static void d(Context context, String str) {
        int i2 = f + 1;
        k = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        m("湈뜟\udc86\ue586ୌ倕秙麗ꑙ촅ኞ㮮䅤昽迡풯况̭⣰熴靷밽얅\ueb49〔姄纒葆괥\uf2caᮜ⅒䘼濨뒬\uda58\ue332ࣳ冹睽鰳ꗧ쪵ခ㧉庌摗", 55619 - (ViewConfiguration.getTouchSlop() >> 8), objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        l(null, 126 - ImageFormat.getBitsPerPixel(0), null, "\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0090\u0084\u008b\u0088\u0084\u008f\u0084\u0082\u008e\u008d\u0085\u008c\u008b\u0088\u008a\u0089\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr2);
        edit.putString(((String) objArr2[0]).intern(), str).commit();
        int i4 = f + 65;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    private static String e(Context context) {
        SharedPreferences sharedPreferences;
        Object obj;
        int i2 = f + 23;
        k = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 29 : 'c') {
            case Opcodes.DADD /* 99 */:
                Object[] objArr = new Object[1];
                m("湈뜟\udc86\ue586ୌ倕秙麗ꑙ촅ኞ㮮䅤昽迡풯况̭⣰熴靷밽얅\ueb49〔姄纒葆괥\uf2caᮜ⅒䘼濨뒬\uda58\ue332ࣳ冹睽鰳ꗧ쪵ခ㧉庌摗", (ViewConfiguration.getWindowTouchSlop() >> 8) + 55619, objArr);
                sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
                Object[] objArr2 = new Object[1];
                l(null, 126 - ExpandableListView.getPackedPositionChild(0L), null, "\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0090\u0084\u008b\u0088\u0084\u008f\u0084\u0082\u008e\u008d\u0085\u008c\u008b\u0088\u008a\u0089\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr2);
                obj = objArr2[0];
                break;
            default:
                Object[] objArr3 = new Object[1];
                m("湈뜟\udc86\ue586ୌ倕秙麗ꑙ촅ኞ㮮䅤昽迡풯况̭⣰熴靷밽얅\ueb49〔姄纒葆괥\uf2caᮜ⅒䘼濨뒬\uda58\ue332ࣳ冹睽鰳ꗧ쪵ခ㧉庌摗", (ViewConfiguration.getWindowTouchSlop() - 94) * 55619, objArr3);
                sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 1);
                Object[] objArr4 = new Object[1];
                l(null, 90 << ExpandableListView.getPackedPositionChild(1L), null, "\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0090\u0084\u008b\u0088\u0084\u008f\u0084\u0082\u008e\u008d\u0085\u008c\u008b\u0088\u008a\u0089\u0084\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr4);
                obj = objArr4[0];
                break;
        }
        String string = sharedPreferences.getString(((String) obj).intern(), null);
        int i3 = f + 15;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return string;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v18, types: [byte[]] */
    private static void l(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 762
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.j.l(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 654
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.j.m(java.lang.String, int, java.lang.Object[]):void");
    }
}
