package androidx.core.graphics;

import android.graphics.Paint;
import kotlin.Metadata;

/* compiled from: Paint.kt */
@Metadata(d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0017\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\b\u0010\u0003\u001a\u0004\u0018\u00010\u0004H\u0086\b¨\u0006\u0005"}, d2 = {"setBlendMode", "", "Landroid/graphics/Paint;", "blendModeCompat", "Landroidx/core/graphics/BlendModeCompat;", "core-ktx_release"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\graphics\PaintKt.smali */
public final class PaintKt {
    public static final boolean setBlendMode(Paint $this$setBlendMode, BlendModeCompat blendModeCompat) {
        return PaintCompat.setBlendMode($this$setBlendMode, blendModeCompat);
    }
}
