package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.InvalidCipherTextException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\t0.smali */
public interface t0 {
    int a();

    byte[] a(byte[] bArr, int i, int i2) throws InvalidCipherTextException;

    int b();

    void init(boolean z, CipherParameters cipherParameters);
}
