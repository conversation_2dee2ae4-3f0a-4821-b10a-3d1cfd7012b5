package com.google.android.gms.internal.p000authapi;

import android.os.Parcel;
import android.os.RemoteException;
import com.google.android.gms.auth.api.credentials.Credential;
import com.google.android.gms.common.api.Status;

/* compiled from: com.google.android.gms:play-services-auth@@20.6.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth-api\zbr.smali */
public abstract class zbr extends zbb implements zbs {
    public zbr() {
        super("com.google.android.gms.auth.api.credentials.internal.ICredentialsCallbacks");
    }

    @Override // com.google.android.gms.internal.p000authapi.zbb
    protected final boolean zba(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
        switch (i) {
            case 1:
                Status status = (Status) zbc.zba(parcel, Status.CREATOR);
                Credential credential = (Credential) zbc.zba(parcel, Credential.CREATOR);
                zbc.zbb(parcel);
                zbb(status, credential);
                break;
            case 2:
                Status status2 = (Status) zbc.zba(parcel, Status.CREATOR);
                zbc.zbb(parcel);
                zbc(status2);
                break;
            case 3:
                Status status3 = (Status) zbc.zba(parcel, Status.CREATOR);
                String readString = parcel.readString();
                zbc.zbb(parcel);
                zbd(status3, readString);
                break;
            default:
                return false;
        }
        parcel2.writeNoException();
        return true;
    }
}
