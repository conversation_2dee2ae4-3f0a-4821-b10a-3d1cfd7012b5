package o.be;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.util.Base64;
import com.esotericsoftware.asm.Opcodes;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import o.cf.i;
import o.ee.g;
import o.ee.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\c.smali */
public final class c extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        e = 1;
        e();
        Process.getElapsedCpuTime();
        int i = e + 31;
        d = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        b = -7358789510132229474L;
    }

    static void init$0() {
        $$a = new byte[]{26, 103, -21, 32};
        $$b = 254;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.be.c.$$a
            int r8 = r8 * 2
            int r8 = r8 + 112
            int r9 = r9 * 3
            int r9 = 4 - r9
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L31
        L19:
            r3 = r2
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r9]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L31:
            int r8 = r8 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.be.c.x(byte, int, int, java.lang.Object[]):void");
    }

    c(Context context) throws o.bi.c {
        super(context, 17);
    }

    @Override // o.cf.i
    public final void b() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        int i = e + 47;
        d = i % 128;
        switch (i % 2 != 0 ? '9' : 'C') {
            case '9':
                h();
                m();
                l();
                int i2 = 41 / 0;
                return;
            default:
                h();
                m();
                l();
                return;
        }
    }

    @Override // o.cf.i
    public final String c() {
        int i = e + 47;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        v("尶팩䈓\uf103恣非ق떮⒟宀쫩秐\ue8caᠦ輝㸂굯\udc5c卍", Color.green(0) + 36629, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = e + Opcodes.DMUL;
        d = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    public static String d(String str) {
        int i = e + Opcodes.DNEG;
        d = i % 128;
        int i2 = i % 2;
        try {
            byte[] decode = Base64.decode(str.getBytes(j.c()), 10);
            switch (decode[0] == 31 ? '1' : '!') {
                case '1':
                    int i3 = d + 89;
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    if (decode[1] == -117) {
                        GZIPInputStream gZIPInputStream = new GZIPInputStream(new ByteArrayInputStream(decode));
                        StringBuilder sb = new StringBuilder();
                        byte[] bArr = new byte[32];
                        while (true) {
                            int read = gZIPInputStream.read(bArr);
                            if (read == -1) {
                                gZIPInputStream.close();
                                return sb.toString();
                            }
                            sb.append(new String(bArr, 0, read, j.c()));
                        }
                    }
                    break;
            }
            return new String(decode, j.c());
        } catch (IOException e2) {
            g.c();
            Object[] objArr = new Object[1];
            v("屍촁纥\ue858᧹譈㐈ꖶ흑䃸\uf26a挊貶㹜꿘\ud97e䨭﮼敂雝g넏⊆䰪\ufdd7潴頝ঠ묾", 37223 - Color.red(0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            v("屨ᷖ\udf12饆媳ᓩ혖遪凅\u1311쵇躽䣩ਠ", 16824 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e2);
            return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void v(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 482
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.be.c.v(java.lang.String, int, java.lang.Object[]):void");
    }
}
