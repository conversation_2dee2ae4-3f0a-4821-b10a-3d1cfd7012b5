package o.bx;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationIssuerPasscode;
import fr.antelop.sdk.authentication.CustomerAuthenticationPasscode;
import fr.antelop.sdk.authentication.CustomerConsentCredentials;
import fr.antelop.sdk.authentication.CustomerDeviceBiometricAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerScreenUnlockAuthenticationCredentials;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import o.f.a;
import o.f.d;
import o.f.e;
import o.f.f;
import o.f.g;
import o.f.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bx\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static long d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        d = -1347743641528196201L;
    }

    private static void f(short s, byte b, byte b2, Object[] objArr) {
        byte[] bArr = $$a;
        int i = (b * 3) + 68;
        int i2 = 3 - (s * 4);
        int i3 = (b2 * 2) + 1;
        byte[] bArr2 = new byte[i3];
        int i4 = -1;
        int i5 = i3 - 1;
        if (bArr == null) {
            int i6 = i2 + i5;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
            i = i6;
            i2 = i2;
        }
        while (true) {
            int i7 = i2 + 1;
            int i8 = i4 + 1;
            bArr2[i8] = (byte) i;
            if (i8 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = i8;
            i += bArr[i7];
            i2 = i7;
        }
    }

    static void init$0() {
        $$a = new byte[]{21, -84, -91, -118};
        $$b = 229;
    }

    public static o.f.e b(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        int i = e + 49;
        c = i % 128;
        int i2 = i % 2;
        if (customerAuthenticationCredentials == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            a("荐\ud9c5箢茓\ud830㫸뵁熜ฟ꧀⡷\ueeba饑\u2438鬆᱀\u2455匃ᘦ襡랳칬胆\u0601䊟絃\uf3d1댺췵\ueba9溗\u20c6壄暄\ud9b3巤\uea23", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (customerAuthenticationCredentials instanceof CustomerAuthenticationIssuerPasscode) {
            CustomerAuthenticationIssuerPasscode customerAuthenticationIssuerPasscode = (CustomerAuthenticationIssuerPasscode) customerAuthenticationCredentials;
            d passcode = customerAuthenticationIssuerPasscode.getPasscode();
            d cryptogram = customerAuthenticationIssuerPasscode.getCryptogram();
            if (customerAuthenticationIssuerPasscode.isValid()) {
                return new g(e.d.b, new Date(), passcode, cryptogram, customerAuthenticationIssuerPasscode.getCryptogramExtraData());
            }
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr2 = new Object[1];
            a("荐\ud9c5箢茓\ud830㫸뵁熜ฟ꧀⡷\ueeba饑\u2438鬆᱀\u2455匃ᘦ襡랳칬胆\u0601䊟絃\uf3d1댺췵\ueba9溗\u20c6壄暄\ud9b3巤\uea23", TextUtils.indexOf((CharSequence) "", '0', 0) + 1, objArr2);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
        }
        switch (!(customerAuthenticationCredentials instanceof CustomerAuthenticationPasscode)) {
            case false:
                CustomerAuthenticationPasscode customerAuthenticationPasscode = (CustomerAuthenticationPasscode) customerAuthenticationCredentials;
                customerAuthenticationPasscode.getValue();
                if (!customerAuthenticationPasscode.isValid()) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr3 = new Object[1];
                    a("荐\ud9c5箢茓\ud830㫸뵁熜ฟ꧀⡷\ueeba饑\u2438鬆᱀\u2455匃ᘦ襡랳칬胆\u0601䊟絃\uf3d1댺췵\ueba9溗\u20c6壄暄\ud9b3巤\uea23", Color.green(0), objArr3);
                    throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr3[0]).intern());
                }
                f fVar = new f(e.d.b, new Date(), customerAuthenticationPasscode.getValue());
                int i3 = c + 83;
                e = i3 % 128;
                int i4 = i3 % 2;
                return fVar;
            default:
                if (customerAuthenticationCredentials instanceof CustomerConsentCredentials) {
                    return new o.f.b(e.d.b, new Date());
                }
                if (customerAuthenticationCredentials instanceof CustomerScreenUnlockAuthenticationCredentials) {
                    return new j(e.d.b, new Date(), new d(new byte[0]));
                }
                if (customerAuthenticationCredentials instanceof CustomerDeviceBiometricAuthenticationCredentials) {
                    return new a(e.d.b, new Date(), null, ((CustomerDeviceBiometricAuthenticationCredentials) customerAuthenticationCredentials).getCipher());
                }
                if (!(customerAuthenticationCredentials instanceof e)) {
                    WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr4 = new Object[1];
                    a("荐\ud9c5箢茓\ud830㫸뵁熜ฟ꧀⡷\ueeba饑\u2438鬆᱀\u2455匃ᘦ襡랳칬胆\u0601䊟絃\uf3d1댺췵\ueba9溗\u20c6壄暄\ud9b3巤\uea23", Process.myTid() >> 22, objArr4);
                    throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr4[0]).intern());
                }
                o.f.e d2 = ((e) customerAuthenticationCredentials).d();
                int i5 = e + 59;
                c = i5 % 128;
                switch (i5 % 2 == 0 ? '@' : (char) 22) {
                    case '@':
                        throw null;
                    default:
                        return d2;
                }
        }
    }

    public static CustomerAuthenticationCredentials b(o.f.e eVar) {
        e eVar2 = new e(eVar);
        int i = e + Opcodes.LUSHR;
        c = i % 128;
        int i2 = i % 2;
        return eVar2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void a(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 360
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bx.b.a(java.lang.String, int, java.lang.Object[]):void");
    }
}
