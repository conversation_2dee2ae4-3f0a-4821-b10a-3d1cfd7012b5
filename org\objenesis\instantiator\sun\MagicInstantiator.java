package org.objenesis.instantiator.sun;

import org.objenesis.instantiator.annotations.Instantiator;
import org.objenesis.instantiator.annotations.Typology;
import org.objenesis.instantiator.basic.DelegatingToExoticInstantiator;

@Instantiator(Typology.STANDARD)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\objenesis\instantiator\sun\MagicInstantiator.smali */
public class MagicInstantiator<T> extends DelegatingToExoticInstantiator<T> {
    public MagicInstantiator(Class<T> type) {
        super("org.objenesis.instantiator.exotic.MagicInstantiator", type);
    }
}
