package o.bm;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.c.a;
import o.cf.i;
import o.cf.j;
import o.i.f;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bm\e.smali */
public final class e extends o.y.b<b> {
    private static char c;
    private static char e;
    private static char f;
    private static int g;
    private static char h;
    a a;
    d b;
    o.ad.c d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int i = 1;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bm\e$b.smali */
    public interface b {
        void c(o.bb.d dVar);

        void d();
    }

    static {
        g = 0;
        n();
        View.getDefaultSize(0, 0);
        AudioTrack.getMinVolume();
        int i2 = i + 7;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void n() {
        c = (char) 9387;
        f = (char) 32857;
        h = (char) 32115;
        e = (char) 37397;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i2 = i + Opcodes.LSUB;
        g = i2 % 128;
        int i3 = i2 % 2;
        AsyncTaskC0032e o2 = o();
        int i4 = i + 47;
        g = i4 % 128;
        switch (i4 % 2 != 0 ? 'K' : Typography.amp) {
            case 'K':
                throw null;
            default:
                return o2;
        }
    }

    public e(Context context, b bVar, o.ei.c cVar) {
        super(context, bVar, cVar, o.bb.e.f40o);
    }

    public final void d(o.h.d dVar, a aVar, d dVar2) throws WalletValidationException {
        this.b = dVar2;
        this.a = aVar;
        this.d = new c(e(), aVar).c(dVar);
        if (dVar != null) {
            int i2 = i + 63;
            g = i2 % 128;
            char c2 = i2 % 2 != 0 ? '\n' : (char) 26;
            dVar.a();
            switch (c2) {
                case 26:
                    break;
                default:
                    int i3 = 48 / 0;
                    break;
            }
        }
        switch (this.d.c() ? '^' : (char) 6) {
            case Opcodes.DUP2_X2 /* 94 */:
                int i4 = i + 31;
                g = i4 % 128;
                int i5 = i4 % 2;
                if (!this.d.m()) {
                    switch (!this.d.h() ? 'F' : 'X') {
                        case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                            int i6 = g + 37;
                            i = i6 % 128;
                            int i7 = i6 % 2;
                            if (!this.d.o()) {
                                c();
                                return;
                            }
                            break;
                    }
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
                    Object[] objArr = new Object[1];
                    k("⍡횫\ued4d䷈ﮛ\uefcf\ud85dꂍ뗴뮃㔹䫌䓍됖솏哾痖ᾳ솏哾膉䬤짰矟⪲뤲䓍됖솏哾\uddf5\ue1aeꕑ胓", ((byte) KeyEvent.getModifierMetaStateMask()) + 34, objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                break;
        }
        WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
        Object[] objArr2 = new Object[1];
        k("⍡횫\ued4d䷈ﮛ\uefcf\ud85dꂍ뗴뮃㔹䫌䓍됖솏哾痖ᾳ솏哾膉䬤짰矟⪲뤲䓍됖솏哾\uddf5\ue1aeꕑ胓", 33 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr2);
        throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
    }

    private AsyncTaskC0032e o() {
        AsyncTaskC0032e asyncTaskC0032e = new AsyncTaskC0032e(this);
        int i2 = g + 45;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? '=' : 'M') {
            case 'M':
                return asyncTaskC0032e;
            default:
                throw null;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i2 = g + 47;
        i = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("蛮䂴䦁찰땑퐧\u0bab稣\udb6c\ue449呬䵻\u0bab稣㱾ᤇ\ue353ㅙ縦琤", Color.red(0) + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = i + Opcodes.DREM;
        g = i4 % 128;
        int i5 = i4 % 2;
        return intern;
    }

    /* renamed from: o.bm.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bm\e$e.smali */
    static final class AsyncTaskC0032e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int b;
        private static long c;
        private static int d;
        private static char e;
        private static int g;
        private final List<f> a;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            g = 1;
            e = (char) 28306;
            d = 161105445;
            c = 6565854932352255525L;
        }

        private static void B(short s, short s2, byte b2, Object[] objArr) {
            byte[] bArr = $$d;
            int i = (s * 2) + 1;
            int i2 = 106 - s2;
            int i3 = 4 - (b2 * 3);
            byte[] bArr2 = new byte[i];
            int i4 = -1;
            int i5 = i - 1;
            if (bArr == null) {
                i2 += i3;
                i3++;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = -1;
            }
            while (true) {
                int i6 = i4 + 1;
                bArr2[i6] = (byte) i2;
                if (i6 == i5) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                Object[] objArr2 = objArr;
                int i7 = i3;
                byte[] bArr3 = bArr2;
                byte[] bArr4 = bArr;
                i2 += bArr[i3];
                i3 = i7 + 1;
                objArr = objArr2;
                bArr = bArr4;
                bArr2 = bArr3;
                i4 = i6;
            }
        }

        static void init$0() {
            $$d = new byte[]{48, 67, 97, 27};
            $$e = Opcodes.INEG;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = b + 79;
            g = i % 128;
            switch (i % 2 == 0 ? '.' : 'O') {
                case Opcodes.IASTORE /* 79 */:
                    break;
                default:
                    int i2 = 68 / 0;
                    break;
            }
        }

        AsyncTaskC0032e(e eVar) {
            super(eVar, true);
            this.a = new ArrayList();
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = g + 61;
            b = i % 128;
            switch (i % 2 != 0 ? 'b' : Typography.quote) {
                case Opcodes.FADD /* 98 */:
                    Object[] objArr = new Object[1];
                    w(ViewConfiguration.getMinimumFlingVelocity() / 86, "崐㯽䶼䢋䄱㔚콾麛䔳䝙砘ᨔ", (char) TextUtils.getOffsetBefore("", 0), "\ue12c儽䍴酣", "\u0000\u0000\u0000\u0000", objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(ViewConfiguration.getMinimumFlingVelocity() >> 16, "崐㯽䶼䢋䄱㔚콾麛䔳䝙砘ᨔ", (char) TextUtils.getOffsetBefore("", 0), "\ue12c儽䍴酣", "\u0000\u0000\u0000\u0000", objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w((Process.myTid() >> 22) + 1039817814, "뼻\ufb45孋\u0eed⡹샹풶⅘뙓ᖄꓮ\uef4bၾƖ吿槜嫖嗣\uda6a", (char) (Color.argb(0, 0, 0, 0) + 42982), "嘸臭\ue63d禧", "\u0000\u0000\u0000\u0000", objArr);
            o.cf.d dVar = new o.cf.d(context, 4, ((String) objArr[0]).intern());
            dVar.a(((e) e()).d);
            dVar.e(((e) e()).a);
            int i = b + 47;
            g = i % 128;
            switch (i % 2 == 0 ? '!' : 'D') {
                case '!':
                    throw null;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            o.eg.b a = ((e) e()).b.a();
            if (a != null) {
                int i = g + Opcodes.LSHL;
                b = i % 128;
                int i2 = i % 2;
                Iterator<String> a2 = a.a();
                while (true) {
                    switch (a2.hasNext() ? '9' : 'Z') {
                        case 'Z':
                            break;
                        default:
                            int i3 = g + 49;
                            b = i3 % 128;
                            switch (i3 % 2 != 0) {
                                case false:
                                    String next = a2.next();
                                    bVar.d(next, a.e(next));
                                default:
                                    String next2 = a2.next();
                                    bVar.d(next2, a.e(next2));
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                            }
                    }
                }
            }
            return bVar;
        }

        @Override // o.y.c
        public final j n() {
            int i = b + 69;
            g = i % 128;
            Object obj = null;
            switch (i % 2 == 0 ? Typography.amp : (char) 24) {
                case 24:
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b;
            int i2 = i + 65;
            g = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0) {
                case false:
                    obj.hashCode();
                    throw null;
                default:
                    int i3 = i + 31;
                    g = i3 % 128;
                    int i4 = i3 % 2;
                    return null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
        
            if (r11 == 2052) goto L16;
         */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.bb.a c(int r11) {
            /*
                r10 = this;
                int r0 = o.bm.e.AsyncTaskC0032e.b
                int r1 = r0 + 3
                int r2 = r1 % 128
                o.bm.e.AsyncTaskC0032e.g = r2
                int r1 = r1 % 2
                r2 = 1
                r3 = 0
                if (r1 != 0) goto L10
                r1 = r2
                goto L11
            L10:
                r1 = r3
            L11:
                switch(r1) {
                    case 1: goto L19;
                    default: goto L14;
                }
            L14:
                r1 = 2052(0x804, float:2.875E-42)
                if (r11 != r1) goto L60
            L18:
                goto L24
            L19:
                r1 = 25361(0x6311, float:3.5538E-41)
                if (r11 != r1) goto L1f
                r1 = r3
                goto L20
            L1f:
                r1 = r2
            L20:
                switch(r1) {
                    case 0: goto L18;
                    default: goto L23;
                }
            L23:
                goto L60
            L24:
                int r0 = r0 + 103
                int r11 = r0 % 128
                o.bm.e.AsyncTaskC0032e.g = r11
                int r0 = r0 % 2
                o.ee.g.c()
                java.lang.String r11 = r10.c()
                r0 = 16777216(0x1000000, float:2.3509887E-38)
                int r1 = android.graphics.Color.rgb(r3, r3, r3)
                int r4 = r1 + r0
                java.lang.String r5 = "䕁\udcec䱞ⴄऻ橖疔\u0b7cࢥ\u2e7fᑽ䴬惬纡\ue2ae葇툕ퟵ\ue20c䲺훫毴㋭隭鵦쌣燩\uf47b\ue3af됯\uefe4אּ뭸䮲绚恃\u19ad肹༛뇻㳱츹腗̋ണ\ue8d1펉呋\udcbc펵\ue0e8\uee25ਐ\uece0Ⱇ莾컸貥镝⠰⽩搕ൡ쮨輪ᒻ鶯㏞\uec86鐈䣚\ud9fb⒳砟ᑠ︯\ufe53\ue4c2㾣蠳\udb13쩥\ue291섦䚈\uf5e1齩⚦螭ᢒࠋ䜈顅呄큺䞆밫ᜥ鿪飕熜杊뵺끩庭፡"
                java.lang.String r0 = ""
                int r0 = android.view.KeyEvent.keyCodeFromString(r0)
                r1 = 43446(0xa9b6, float:6.0881E-41)
                int r1 = r1 - r0
                char r6 = (char) r1
                java.lang.String r7 = "ⷶ暥뛥骩"
                java.lang.String r8 = "\u0000\u0000\u0000\u0000"
                java.lang.Object[] r0 = new java.lang.Object[r2]
                r9 = r0
                w(r4, r5, r6, r7, r8, r9)
                r0 = r0[r3]
                java.lang.String r0 = (java.lang.String) r0
                java.lang.String r0 = r0.intern()
                o.ee.g.d(r11, r0)
                o.bb.a r11 = o.bb.a.al
                return r11
            L60:
                o.bb.a r11 = super.c(r11)
                return r11
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bm.e.AsyncTaskC0032e.c(int):o.bb.a");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = b + 33;
            g = i % 128;
            Object obj = null;
            if (i % 2 == 0) {
                ((e) e()).d.d();
                throw null;
            }
            switch (((e) e()).d.d() == null) {
                case false:
                    this.a.add(((e) e()).d.d());
                    break;
            }
            if (((e) e()).a != null) {
                int i2 = g + 91;
                b = i2 % 128;
                int i3 = i2 % 2;
                this.a.add(((e) e()).a.c());
            }
            int i4 = b + Opcodes.LSHL;
            g = i4 % 128;
            if (i4 % 2 != 0) {
                return;
            }
            obj.hashCode();
            throw null;
        }

        /* JADX WARN: Code restructure failed: missing block: B:24:0x0021, code lost:
        
            if (r4.a.isEmpty() == false) goto L18;
         */
        /* JADX WARN: Failed to find 'out' block for switch in B:18:0x004e. Please report as an issue. */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void q() {
            /*
                r4 = this;
                int r0 = o.bm.e.AsyncTaskC0032e.g
                int r0 = r0 + 83
                int r1 = r0 % 128
                o.bm.e.AsyncTaskC0032e.b = r1
                int r0 = r0 % 2
                r1 = 1
                r2 = 0
                if (r0 == 0) goto L10
                r0 = r1
                goto L11
            L10:
                r0 = r2
            L11:
                switch(r0) {
                    case 0: goto L1b;
                    default: goto L14;
                }
            L14:
                java.util.List<o.i.f> r0 = r4.a
                boolean r0 = r0.isEmpty()
                goto L24
            L1b:
                java.util.List<o.i.f> r0 = r4.a
                boolean r0 = r0.isEmpty()
                if (r0 != 0) goto L52
            L23:
                goto L32
            L24:
                r3 = 22
                int r3 = r3 / r2
                if (r0 != 0) goto L2c
                r0 = 30
                goto L2e
            L2c:
                r0 = 93
            L2e:
                switch(r0) {
                    case 93: goto L52;
                    default: goto L31;
                }
            L31:
                goto L23
            L32:
                o.bb.d r0 = r4.h()
                o.bb.c r0 = r0.a()
                java.util.List<o.i.f> r2 = r4.a
                r0.d(r2)
                int r0 = o.bm.e.AsyncTaskC0032e.b
                int r0 = r0 + 7
                int r2 = r0 % 128
                o.bm.e.AsyncTaskC0032e.g = r2
                int r0 = r0 % 2
                if (r0 != 0) goto L4c
                goto L4e
            L4c:
                r1 = 91
            L4e:
                switch(r1) {
                    case 1: goto L51;
                    default: goto L52;
                }
            L51:
            L52:
                o.y.b r0 = r4.e()
                o.bm.e r0 = (o.bm.e) r0
                o.y.b r1 = r4.e()
                o.bm.e r1 = (o.bm.e) r1
                o.bm.d r1 = r1.b
                o.y.b r2 = r4.e()
                o.bm.e r2 = (o.bm.e) r2
                o.bb.d r2 = r2.d()
                o.bb.d r1 = r1.a(r2)
                r0.c(r1)
                return
            L72:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bm.e.AsyncTaskC0032e.q():void");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = g + Opcodes.DDIV;
            b = i % 128;
            int i2 = i % 2;
            ((e) e()).j().d();
            int i3 = b + 59;
            g = i3 % 128;
            switch (i3 % 2 == 0 ? 'Z' : 'c') {
                case 'Z':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = b + 75;
            g = i % 128;
            switch (i % 2 == 0 ? 'N' : '_') {
                case 'N':
                    ((e) e()).j().c(dVar);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    ((e) e()).j().c(dVar);
                    int i2 = g + 47;
                    b = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int r21, java.lang.String r22, char r23, java.lang.String r24, java.lang.String r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 750
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bm.e.AsyncTaskC0032e.w(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 556
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bm.e.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
