package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\b0.smali */
public abstract class b0 extends u {
    b0() {
    }

    public static b0 a(byte[] bArr) throws IOException {
        q qVar = new q(bArr);
        try {
            b0 c = qVar.c();
            if (qVar.available() == 0) {
                return c;
            }
            throw new IOException("Extra data detected in stream");
        } catch (ClassCastException e) {
            throw new IOException("cannot recognise object in stream");
        }
    }

    abstract int a(boolean z) throws IOException;

    abstract void a(z zVar, boolean z) throws IOException;

    abstract boolean a(b0 b0Var);

    public final boolean b(b0 b0Var) {
        return this == b0Var || a(b0Var);
    }

    abstract boolean e();

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public void encodeTo(OutputStream outputStream) throws IOException {
        z a = z.a(outputStream);
        a.a(this, true);
        a.a();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        return (obj instanceof h) && a(((h) obj).toASN1Primitive());
    }

    b0 f() {
        return this;
    }

    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public abstract int hashCode();

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public final b0 toASN1Primitive() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public void encodeTo(OutputStream outputStream, String str) throws IOException {
        z a = z.a(outputStream, str);
        a.a(this, true);
        a.a();
    }
}
