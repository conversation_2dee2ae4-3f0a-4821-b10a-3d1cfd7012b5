package o.eo;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.CardDisplay;
import java.util.Date;
import java.util.Objects;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\c.smali */
public final class c {
    private static int f = 0;
    private static int h = 1;
    private final String a;
    private final CardDisplay b;
    private final String c;
    private final String d;
    private final Date e;

    public c(String str, Date date, String str2, CardDisplay cardDisplay, String str3) {
        this.a = str;
        this.e = date;
        this.d = str2;
        this.b = cardDisplay;
        this.c = str3;
    }

    public final String c() {
        int i = f;
        int i2 = (i + 16) - 1;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '(' : Typography.amp) {
            case '(':
                throw null;
            default:
                String str = this.a;
                int i3 = (i & 23) + (i | 23);
                h = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final Date b() {
        int i = f;
        int i2 = ((i | 11) << 1) - (i ^ 11);
        h = i2 % 128;
        int i3 = i2 % 2;
        Date date = this.e;
        int i4 = ((i | Opcodes.DNEG) << 1) - (i ^ Opcodes.DNEG);
        h = i4 % 128;
        int i5 = i4 % 2;
        return date;
    }

    public final String a() {
        int i = h;
        int i2 = (i & Opcodes.LSHR) + (i | Opcodes.LSHR);
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                String str = this.d;
                int i3 = (i ^ 7) + ((i & 7) << 1);
                f = i3 % 128;
                int i4 = i3 % 2;
                return str;
            default:
                throw null;
        }
    }

    public final CardDisplay d() {
        CardDisplay cardDisplay;
        int i = f;
        int i2 = (i & 97) + (i | 97);
        int i3 = i2 % 128;
        h = i3;
        switch (i2 % 2 != 0) {
            case false:
                cardDisplay = this.b;
                int i4 = 0 / 0;
                break;
            default:
                cardDisplay = this.b;
                break;
        }
        int i5 = (i3 + 46) - 1;
        f = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return cardDisplay;
            default:
                int i6 = 84 / 0;
                return cardDisplay;
        }
    }

    public final String e() {
        int i = f;
        int i2 = (i + 100) - 1;
        h = i2 % 128;
        int i3 = i2 % 2;
        String str = this.c;
        int i4 = (i + 82) - 1;
        h = i4 % 128;
        switch (i4 % 2 == 0 ? ')' : '1') {
            case ')':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final int hashCode() {
        int hash;
        int i = f;
        int i2 = (i & 93) + (i | 93);
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = {this.a, this.e};
                objArr[3] = this.d;
                objArr[5] = this.b;
                objArr[5] = this.c;
                hash = Objects.hash(objArr);
                break;
            default:
                hash = Objects.hash(this.a, this.e, this.d, this.b, this.c);
                break;
        }
        int i3 = (f + 102) - 1;
        h = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return hash;
        }
    }

    public final boolean equals(Object obj) {
        int i = f;
        int i2 = i + 35;
        h = i2 % 128;
        if (i2 % 2 == 0) {
            boolean z = obj instanceof c;
            throw null;
        }
        switch (!(obj instanceof c) ? (char) 29 : 'D') {
            case 29:
                int i3 = i + Opcodes.LSUB;
                h = i3 % 128;
                switch (i3 % 2 != 0 ? '4' : 'Q') {
                    case '4':
                        return false;
                    default:
                        return true;
                }
            default:
                c cVar = (c) obj;
                switch (Objects.equals(this.a, cVar.c()) ? 'D' : '`') {
                    case 'D':
                        switch (Objects.equals(this.e, cVar.e) ? '?' : (char) 17) {
                            case '?':
                                int i4 = f;
                                int i5 = (i4 ^ 81) + ((i4 & 81) << 1);
                                h = i5 % 128;
                                int i6 = i5 % 2;
                                switch (Objects.equals(this.d, cVar.d) ? (char) 0 : '1') {
                                    case '1':
                                        break;
                                    default:
                                        switch (Objects.equals(this.c, cVar.c)) {
                                            case true:
                                                int i7 = f;
                                                int i8 = (i7 & 99) + (i7 | 99);
                                                h = i8 % 128;
                                                int i9 = i8 % 2;
                                                return true;
                                        }
                                }
                        }
                }
                int i10 = h;
                int i11 = (i10 ^ 15) + ((i10 & 15) << 1);
                f = i11 % 128;
                int i12 = i11 % 2;
                return false;
        }
    }
}
