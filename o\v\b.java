package o.v;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;
import o.al.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\b.smali */
abstract class b extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long k;
    private static int l;

    /* renamed from: o, reason: collision with root package name */
    private static int f100o;
    private final boolean h;
    private final o.eo.f i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        f100o = 1;
        s();
        SystemClock.currentThreadTimeMillis();
        int i = l + Opcodes.DMUL;
        f100o = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{24, -81, 39, 82};
        $$e = Opcodes.IF_ACMPNE;
    }

    static void s() {
        k = -4746649308230863036L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = 71 - r9
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r0 = o.v.b.$$d
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1b
            r9 = r8
            r3 = r1
            r4 = r2
            r8 = r7
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L1b:
            r3 = r2
        L1c:
            int r7 = r7 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2d
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2d:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L36:
            int r9 = r9 + r7
            r7 = r8
            r8 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.b.v(int, short, int, java.lang.Object[]):void");
    }

    public abstract c.e a();

    static /* synthetic */ o.p.g a(b bVar) {
        int i = f100o + 53;
        l = i % 128;
        boolean z = i % 2 == 0;
        o.p.g l2 = bVar.l();
        switch (z) {
            default:
                int i2 = 12 / 0;
            case true:
                return l2;
        }
    }

    static /* synthetic */ o.p.g b(b bVar) {
        int i = l + 33;
        f100o = i % 128;
        int i2 = i % 2;
        o.p.g l2 = bVar.l();
        int i3 = l + 23;
        f100o = i3 % 128;
        int i4 = i3 % 2;
        return l2;
    }

    static /* synthetic */ o.p.g c(b bVar) {
        int i = l + 39;
        f100o = i % 128;
        int i2 = i % 2;
        o.p.g l2 = bVar.l();
        int i3 = l + 49;
        f100o = i3 % 128;
        switch (i3 % 2 == 0 ? Typography.quote : 'R') {
            case '\"':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l2;
        }
    }

    static /* synthetic */ void d(b bVar) {
        int i = l + 93;
        f100o = i % 128;
        int i2 = i % 2;
        bVar.n();
        int i3 = l + 25;
        f100o = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 15 : '+') {
            case 15:
                int i4 = 26 / 0;
                return;
            default:
                return;
        }
    }

    static /* synthetic */ o.p.g e(b bVar) {
        int i = f100o + 67;
        l = i % 128;
        char c = i % 2 != 0 ? 'G' : '\\';
        o.p.g l2 = bVar.l();
        switch (c) {
            case 'G':
                int i2 = 75 / 0;
            default:
                return l2;
        }
    }

    static /* synthetic */ o.p.g f(b bVar) {
        int i = f100o + 95;
        l = i % 128;
        int i2 = i % 2;
        o.p.g l2 = bVar.l();
        int i3 = l + 35;
        f100o = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                int i4 = 49 / 0;
                return l2;
            default:
                return l2;
        }
    }

    b(String str, o.eo.e eVar, boolean z, o.eo.f fVar) {
        super(str, eVar);
        this.h = z;
        this.i = fVar;
    }

    @Override // o.p.h
    public final void a(Context context, o.ei.c cVar, o.h.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u("㗢㋕㖫혢ꥯ씠㥺俛\uebfc\ue493ᯢ氡襮蛉穇ણ껈ꁇ尹⬔䰳䏹뺡즾涌納鄓韧̙ᲅ", 1 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u("ﲱ藿ﳃ\uecb3␃爑ϫ슂⊯厗ⅲ\ue15b䀚ㇿ", Color.alpha(0) + 1, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.al.c(context, new c.a() { // from class: o.v.b.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int c;
            private static int d;
            private static int[] e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                d = 0;
                c = 1;
                e = new int[]{-1507867135, 1531705864, -283275134, -1191254100, 1587149616, -745705625, 916689274, 553451786, 1099378182, 646244157, 1417594787, 1548629772, -793436753, -1784308105, -1195543605, -1333223166, -1349010384, 531800126};
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(byte r6, short r7, short r8, java.lang.Object[] r9) {
                /*
                    int r7 = 116 - r7
                    int r6 = r6 * 2
                    int r6 = 1 - r6
                    byte[] r0 = o.v.b.AnonymousClass2.$$a
                    int r8 = r8 + 4
                    byte[] r1 = new byte[r6]
                    int r6 = r6 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L18
                    r7 = r6
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L35
                L18:
                    r3 = r2
                L19:
                    int r8 = r8 + 1
                    byte r4 = (byte) r7
                    r1[r3] = r4
                    if (r3 != r6) goto L28
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L28:
                    int r3 = r3 + 1
                    r4 = r0[r8]
                    r5 = r7
                    r7 = r6
                    r6 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    r8 = r5
                L35:
                    int r6 = -r6
                    int r6 = r6 + r8
                    r8 = r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r7
                    r7 = r6
                    r6 = r5
                    goto L19
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.b.AnonymousClass2.g(byte, short, short, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{106, -103, -121, 51};
                $$b = Opcodes.I2C;
            }

            @Override // o.al.c.a
            public final void b() {
                int i = d + 87;
                c = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f(new int[]{-1742405245, -1053885582, 996316763, -1715521669, -166391361, -1443794585, 1015528083, 642474179, 359341028, -294732231, 587294927, -823097627, 667906775, -803401520}, 25 - TextUtils.lastIndexOf("", '0', 0, 0), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f(new int[]{-1019122899, 137120577, 587294927, -823097627, 649991340, -1292594420, 231371110, 723380389, 1676105664, 2100097750, 1878741839, -1233907133, -546139868, 1154198327, 2130579751, -1720892754, 667906775, -803401520}, 34 - ExpandableListView.getPackedPositionGroup(0L), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                switch (b.c(b.this) == null) {
                    case true:
                        break;
                    default:
                        int i3 = d + 63;
                        c = i3 % 128;
                        int i4 = i3 % 2;
                        b.a(b.this).onProcessSuccess();
                        break;
                }
            }

            @Override // o.al.c.a
            public final void a(o.bb.d dVar2) {
                o.bv.c c2 = o.bv.c.c(dVar2);
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f(new int[]{-1742405245, -1053885582, 996316763, -1715521669, -166391361, -1443794585, 1015528083, 642474179, 359341028, -294732231, 587294927, -823097627, 667906775, -803401520}, (Process.myPid() >> 22) + 26, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                f(new int[]{-1019122899, 137120577, 587294927, -823097627, 649991340, -1292594420, 231371110, 723380389, 1676105664, 2100097750, 1878741839, -1233907133, -1274393353, 44868730, -1849715521, -1028078697, 862246387, -1108323524, -1392101075, -621690980}, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 36, objArr4);
                o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(c2.toString()).toString());
                if (b.e(b.this) != null) {
                    switch (dVar2.d() == o.bb.a.aA ? (char) 17 : '7') {
                        case '7':
                            b.f(b.this).onError(o.bv.c.c(dVar2));
                            int i = c + 91;
                            d = i % 128;
                            int i2 = i % 2;
                            return;
                        default:
                            int i3 = d + 49;
                            c = i3 % 128;
                            switch (i3 % 2 != 0) {
                                case true:
                                    b.d(b.this);
                                    b.b(b.this).onAuthenticationDeclined();
                                    return;
                                default:
                                    b.d(b.this);
                                    b.b(b.this).onAuthenticationDeclined();
                                    int i4 = 91 / 0;
                                    return;
                            }
                    }
                }
            }

            /* JADX WARN: Removed duplicated region for block: B:50:0x0176  */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(int[] r22, int r23, java.lang.Object[] r24) {
                /*
                    Method dump skipped, instructions count: 860
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.b.AnonymousClass2.f(int[], int, java.lang.Object[]):void");
            }
        }, cVar).a(dVar, o(), a(), ((d) this).n, this.i);
        int i = f100o + Opcodes.DREM;
        l = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = f100o + Opcodes.LMUL;
        int i2 = i % 128;
        l = i2;
        int i3 = i % 2;
        if (this.h) {
            int i4 = i2 + 43;
            f100o = i4 % 128;
            switch (i4 % 2 != 0) {
                case true:
                    return;
                default:
                    int i5 = 29 / 0;
                    return;
            }
        }
        WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
        Object[] objArr = new Object[1];
        u("隫狜雿텩憱蔨㸴蜅䢩꒖Ცꓢ⨒웈紂쉵ඊ\ue06d孧\ue3f7\uef7eϥ맹ř컔㴎", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u("빤漁븰㵔䠡飲툇껐恜륩\uf09d赹˒\udb52鄺", View.MeasureSpec.getSize(0) + 1, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(this.i.b());
        Object[] objArr3 = new Object[1];
        u("︭閗﹍跹礆戬披龸․䏣䁻뱕䊚←⇗\udadc攜ܸ߳ﭼ蟯\ue4a8\ue53f᧪Ꙑ\uda4e쫊䞌죀믙ꠢ搱\ueaad", Gravity.getAbsoluteGravity(0, 0) + 1, objArr3);
        throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
    }

    protected final o.eo.f t() {
        int i = l;
        int i2 = i + 87;
        f100o = i2 % 128;
        int i3 = i2 % 2;
        o.eo.f fVar = this.i;
        int i4 = i + Opcodes.DSUB;
        f100o = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return fVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 376
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.b.u(java.lang.String, int, java.lang.Object[]):void");
    }
}
