package o.eq;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import o.ep.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eq\b.smali */
public final class b {
    private static int $10 = 0;
    private static int $11 = 1;
    private static char e = 34877;
    private static char b = 53256;
    private static char d = 59068;
    private static char c = 32763;

    public static e a(String str) throws o.eg.d {
        o.eg.b bVar = new o.eg.b(str);
        boolean z = false;
        Object[] objArr = new Object[1];
        f("⾧뼣뿛앹\ued84꽼ﮑ둤\u2d9f\ueda4㐁⬺煩⠬킴\udf94", TextUtils.indexOf("", "", 0, 0) + 15, objArr);
        String r = bVar.r(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        f("蜈옷⊩\u2ef6爢\ue203͌\udaee\ue01b쯕柴臃", 12 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr2);
        Long m = bVar.m(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f("쩃訙蘗깖툸茬쇥명꓂녇킴\udf94", TextUtils.lastIndexOf("", '0') + 12, objArr3);
        String r2 = bVar.r(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        f("⮣紝끽諩༌ᮏ땄튆픘蛋邳❸茳㤟", Color.alpha(0) + 14, objArr4);
        if (!bVar.a(((String) objArr4[0]).intern())) {
            Object[] objArr5 = new Object[1];
            f("⮣紝끽諩༌ᮏ땄튆픘蛋邳❸茳㤟", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 13, objArr5);
            if (bVar.g(((String) objArr5[0]).intern()).booleanValue()) {
                z = true;
            }
        }
        return new e(r, m, r2, Boolean.valueOf(z));
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r28, int r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.b.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
