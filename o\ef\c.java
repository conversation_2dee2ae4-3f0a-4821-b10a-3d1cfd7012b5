package o.ef;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ef\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static final Object c;
    private static boolean d;
    private static a e;
    private static int j;

    static void b() {
        b = new char[]{50906, 50816, 50855, 50856, 50855, 50854, 50853, 50816, 50845, 50876, 50936, 50868, 50761, 50756, 50757, 50757, 50777, 50763, 50766, 50752, 50756, 50865, 50875, 50756, 50752, 50755, 50753, 50907, 50923, 50817, 50872, 50852, 50852, 50855, 50856, 50839, 50837, 50848, 50850, 50851, 50855, 50842, 50839, 50855, 50854, 50854, 50851, 50876, 50862, 50862, 50849, 50859, 50848, 50851, 50840, 50934, 50936, 50918, 50939, 50818, 50820, 50941, 50826, 50851, 50826, 50830, 50855, 50854, 50854, 50851, 50876, 50862, 50932, 50942, 50859, 50849, 50862, 50862, 50876, 50851, 50854, 50854, 50855, 50839, 50842, 50855, 50851, 50850, 50848, 50837, 50839, 50856, 50855, 50852, 50852, 50872, 50817, 50923, 50923, 50932, 50862, 50876, 50851, 50854, 50854, 50855, 50830, 50826, 50851, 50826, 50941, 50820, 50818, 50939, 50916, 50943, 50935, 50840, 50851, 50938, 50876, 50862, 50862, 50849, 50859, 50819, 50938, 50837, 50817, 50826, 50851, 50826, 50830, 50855, 50854, 50854, 50851, 50876, 50862, 50932, 50923, 50923, 50817, 50872, 50852, 50852, 50855, 50856, 50839, 50837, 50848, 50850, 50851, 50855, 50842, 50839, 50855, 50854, 50854, 50942, 50859, 50849, 50862, 50862, 50876, 50851, 50854, 50854, 50855, 50839, 50842, 50855, 50851, 50850, 50848, 50837, 50839, 50856, 50855, 50852, 50852, 50872, 50817, 50923, 50923, 50932, 50862, 50876, 50851, 50854, 50854, 50855, 50830, 50826, 50851, 50826, 50817, 50837, 50938, 50823, 50823, 50936, 50758, 50742, 50730, 50730, 51136, 51195, 51175, 51175, 51174, 51179, 51158, 51156, 51171, 51197, 51170, 51174, 51157, 51158, 51174, 51169, 51169, 51170, 51199, 51177, 51177, 51168, 51178, 51178, 51169, 51193, 51192, 51169, 51176, 51168, 51170, 51172, 51179, 51174, 51175, 51148, 51150, 51176, 51197, 51199, 51169, 51174, 51169, 51175, 51176, 51169, 51196, 50817, 50774, 50768, 50775, 50875, 50866, 50789, 50769, 50769, 50768, 50773, 50768, 50868, 50872, 50797, 50799, 50796, 50768, 50775, 50871, 50872, 50776, 50779, 50771, 50865, 50864, 50793, 50796, 50796, 50775, 50776, 50873, 50836, 50836, 50866, 50789, 50769, 50769, 50768, 50773, 50752, 50758, 50797, 50799, 50796, 50768, 50759, 50752, 50768, 50771, 50771, 50796, 50793, 50779, 50779, 50770, 50772, 50843, 50791, 50794, 50799, 50794, 50789, 50792, 50759, 50752};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            byte[] r0 = o.ef.c.$$a
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r6 = 122 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r7
            r6 = r8
            r3 = r2
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r5
        L2c:
            int r7 = r7 + 1
            int r8 = r8 + r4
            r5 = r8
            r8 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ef.c.g(byte, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{48, -21, 33, -7};
        $$b = Opcodes.IXOR;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        j = 1;
        b();
        c = new Object();
        d = false;
        e = null;
        int i = j + 9;
        a = i % 128;
        int i2 = i % 2;
    }

    public static a a(Context context) throws e {
        a aVar;
        b bVar;
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{10, 17, 31, 11}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        synchronized (c) {
            if (d) {
                throw new e();
            }
            if (e == null) {
                o.ee.e.a();
                File file = new File(o.ee.c.d(context));
                Object[] objArr2 = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000", new int[]{0, 10, 0, 0}, false, objArr2);
                File file2 = new File(file, ((String) objArr2[0]).intern());
                try {
                    byte[] bArr = new byte[20];
                    BufferedInputStream bufferedInputStream = new BufferedInputStream(new FileInputStream(file2));
                    int read = bufferedInputStream.read(bArr, 0, 20);
                    bufferedInputStream.close();
                    if (read != 20) {
                        throw new IOException();
                    }
                    byte b2 = bArr[4];
                    byte b3 = bArr[18];
                    if (b2 == 2 && b3 == -73) {
                        g.c();
                        Object[] objArr3 = new Object[1];
                        f("\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{27, 46, 0, 25}, true, objArr3);
                        g.d(intern, ((String) objArr3[0]).intern());
                        bVar = b.c;
                    } else if (b2 == 1 && b3 == 40) {
                        g.c();
                        Object[] objArr4 = new Object[1];
                        f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001", new int[]{73, 46, 0, 0}, false, objArr4);
                        g.d(intern, ((String) objArr4[0]).intern());
                        bVar = b.e;
                    } else if (b3 == 3) {
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{Opcodes.DNEG, 40, 0, 6}, true, objArr5);
                        g.d(intern, ((String) objArr5[0]).intern());
                        bVar = b.b;
                    } else if (b3 == 62) {
                        g.c();
                        Object[] objArr6 = new Object[1];
                        f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000", new int[]{Opcodes.IF_ICMPEQ, 43, 0, 0}, false, objArr6);
                        g.d(intern, ((String) objArr6[0]).intern());
                        bVar = b.a;
                    } else {
                        g.c();
                        Object[] objArr7 = new Object[1];
                        f("\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000", new int[]{202, 51, Opcodes.INSTANCEOF, 27}, true, objArr7);
                        g.e(intern, ((String) objArr7[0]).intern());
                        d = true;
                        throw new e();
                    }
                    e = new a(file2, bVar);
                } catch (IOException e2) {
                    g.c();
                    Object[] objArr8 = new Object[1];
                    f("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{253, 57, 51, 0}, true, objArr8);
                    g.a(intern, ((String) objArr8[0]).intern(), e2);
                    d = true;
                    throw new e();
                }
            }
            aVar = e;
        }
        return aVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:79:0x02c9, code lost:
    
        r2 = o.ef.c.$10 + 61;
        o.ef.c.$11 = r2 % 128;
        r2 = r2 % 2;
        r2 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 784
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ef.c.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
