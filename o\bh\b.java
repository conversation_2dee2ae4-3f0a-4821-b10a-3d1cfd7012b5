package o.bh;

import android.content.Context;
import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.bv.g;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\b.smali */
public final class b extends AsyncTask<o.bh.d, Void, Void> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int[] c;
    private static int e;
    private final Context b;
    private final d d;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\b$d.smali */
    public interface d {
        void onDeleteWalletCompleted();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        a = 1;
        a();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        int i = e + 39;
        a = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        c = new int[]{-91371534, 1944312140, -293493418, 2101339289, -1524003126, -1364020359, -492657001, -1312849305, -231872951, 931418452, 1294055860, 63016580, 726875456, 2099175451, 149588190, 136299429, 1639672742, -623403667};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 115
            int r7 = r7 + 4
            byte[] r0 = o.bh.b.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r7 = r7 + 1
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bh.b.g(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{100, 85, 7, -13};
        $$b = 92;
    }

    @Override // android.os.AsyncTask
    protected final /* synthetic */ Void doInBackground(o.bh.d[] dVarArr) {
        int i = a + 23;
        e = i % 128;
        boolean z = i % 2 != 0;
        Void c2 = c();
        switch (z) {
            case true:
                int i2 = 28 / 0;
                break;
        }
        int i3 = a + Opcodes.DDIV;
        e = i3 % 128;
        int i4 = i3 % 2;
        return c2;
    }

    @Override // android.os.AsyncTask
    protected final /* synthetic */ void onPostExecute(Void r3) {
        int i = e + 61;
        a = i % 128;
        int i2 = i % 2;
        b(r3);
        int i3 = a + 21;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public b(Context context, d dVar) {
        this.b = context;
        this.d = dVar;
    }

    public final void b(o.bh.d dVar) {
        int i = e + Opcodes.DSUB;
        a = i % 128;
        int i2 = i % 2;
        execute(dVar);
        int i3 = e + 45;
        a = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private Void c() {
        int i = a + 31;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                if (c.c() != null) {
                    try {
                        try {
                            c.s();
                            o.b.c.j(this.b);
                            g gVar = new g();
                            gVar.e();
                            gVar.b(this.b);
                            c.p();
                            int i2 = e + 75;
                            a = i2 % 128;
                            int i3 = i2 % 2;
                        } catch (InterruptedException e2) {
                            o.ee.g.c();
                            Object[] objArr = new Object[1];
                            f(new int[]{-875819718, 467202703, 1401351001, -712484113, -226576951, -1518194113, 28647051, 1669059053, -455606056, 2001557415, -272881598, -13793852, -1698990393, -1639954932}, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 27, objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            f(new int[]{995200400, 39620501, 1514119233, 545236575, -1005986734, 457411208, -1698990393, -1639954932}, 13 - TextUtils.indexOf((CharSequence) "", '0'), objArr2);
                            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
                            c.p();
                        }
                    } catch (Throwable th) {
                        c.p();
                        throw th;
                    }
                }
                return null;
            default:
                c.c();
                throw null;
        }
    }

    private void b(Void r5) {
        super.onPostExecute(r5);
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f(new int[]{-875819718, 467202703, 1401351001, -712484113, -226576951, -1518194113, 28647051, 1669059053, -455606056, 2001557415, -272881598, -13793852, -1698990393, -1639954932}, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 26, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(new int[]{-428116392, 589434471, 1305593858, 2047002126, -730261416, 1153998200, 777893251, 1314941589}, View.combineMeasuredStates(0, 0) + 13, objArr2);
        o.ee.g.e(intern, ((String) objArr2[0]).intern());
        d dVar = this.d;
        if (dVar != null) {
            int i = e + Opcodes.DMUL;
            a = i % 128;
            char c2 = i % 2 == 0 ? 'T' : (char) 23;
            dVar.onDeleteWalletCompleted();
            switch (c2) {
                case Opcodes.BASTORE /* 84 */:
                    int i2 = 34 / 0;
                    break;
            }
        }
        int i3 = e + 71;
        a = i3 % 128;
        switch (i3 % 2 == 0 ? 'K' : (char) 21) {
            case 'K':
                int i4 = 50 / 0;
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 856
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bh.b.f(int[], int, java.lang.Object[]):void");
    }
}
