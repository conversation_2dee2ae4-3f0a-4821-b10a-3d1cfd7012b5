package fr.antelop.sdk.authentication.prompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\ScreenUnlockCustomerAuthenticationPromptBuilder.smali */
public final class ScreenUnlockCustomerAuthenticationPromptBuilder extends CustomerAuthenticationPromptBuilder {
    private String subtitle;
    private String title;

    public final ScreenUnlockCustomerAuthenticationPromptBuilder setTitle(String str) {
        this.title = str;
        return this;
    }

    public final ScreenUnlockCustomerAuthenticationPromptBuilder setSubtitle(String str) {
        this.subtitle = str;
        return this;
    }

    @Override // fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder
    public final ScreenUnlockCustomerAuthenticationPrompt build() {
        return new ScreenUnlockCustomerAuthenticationPrompt(this.title, this.subtitle);
    }
}
