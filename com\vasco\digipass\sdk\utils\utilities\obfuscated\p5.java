package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p5.smali */
public class p5 {
    static final Hashtable a = new Hashtable();
    static final Hashtable b = new Hashtable();

    static {
        a("B-571", b7.F);
        a("B-409", b7.D);
        a("B-283", b7.n);
        a("B-233", b7.t);
        a("B-163", b7.l);
        a("K-571", b7.E);
        a("K-409", b7.C);
        a("K-283", b7.m);
        a("K-233", b7.s);
        a("K-163", b7.b);
        a("P-521", b7.B);
        a("P-384", b7.A);
        a("P-256", b7.H);
        a("P-224", b7.z);
        a("P-192", b7.G);
    }

    static void a(String str, w wVar) {
        a.put(str, wVar);
        b.put(wVar, str);
    }

    public static e8 b(String str) {
        w c = c(str);
        if (c != null) {
            return a7.b(c);
        }
        return null;
    }

    public static w c(String str) {
        return (w) a.get(o7.c(str));
    }

    public static X9ECParameters a(String str) {
        w c = c(str);
        if (c != null) {
            return a7.a(c);
        }
        return null;
    }

    public static Enumeration a() {
        return a.keys();
    }
}
