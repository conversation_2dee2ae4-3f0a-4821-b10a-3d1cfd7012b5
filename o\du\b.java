package o.du;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\b.smali */
public final class b implements d<o.dy.d, o.dx.d, o.dq.e, Drawable> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static char[] c;
    private static int d;
    private static char e;
    private final o.dx.d a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        f();
        Color.blue(0);
        TextUtils.indexOf((CharSequence) "", '0');
        int i = b + 75;
        d = i % 128;
        int i2 = i % 2;
    }

    static void f() {
        c = new char[]{17046, 30590, 30562, 30499, 30566, 30563, 30556, 30564, 17053, 30555, 30572, 30568, 17047, 30511, 30574, 17040, 17044, 17042, 30589, 30588, 30539, 17043, 30571, 30582, 30570, 17041, 30540, 30561, 30587, 30591, 30557, 30586, 30580, 30531, 30514, 30560};
        e = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 69
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r0 = o.du.b.$$a
            int r7 = r7 + 5
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r8 = r8 + r6
            r6 = r7
            r7 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.b.i(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
        $$b = 68;
    }

    @Override // o.du.d
    public final /* synthetic */ o.dq.e a() {
        int i = b + Opcodes.DSUB;
        d = i % 128;
        int i2 = i % 2;
        o.dq.e j = j();
        int i3 = b + 45;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return j;
            default:
                throw null;
        }
    }

    @Override // o.du.d
    public final /* synthetic */ o.dy.d b() {
        o.dy.d h;
        int i = d + 41;
        b = i % 128;
        switch (i % 2 != 0) {
            case false:
                h = h();
                int i2 = 95 / 0;
                break;
            default:
                h = h();
                break;
        }
        int i3 = b + 51;
        d = i3 % 128;
        int i4 = i3 % 2;
        return h;
    }

    @Override // o.du.d
    public final /* synthetic */ o.dx.d d() {
        int i = d + 79;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                o.dx.d e2 = e();
                int i2 = d + 109;
                b = i2 % 128;
                switch (i2 % 2 == 0 ? '?' : Typography.greater) {
                    case '?':
                        throw null;
                    default:
                        return e2;
                }
            default:
                e();
                throw null;
        }
    }

    public b(String str) {
        this.a = new o.dx.d(str);
    }

    private static o.dy.d h() {
        int i = d + Opcodes.DREM;
        b = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? 'W' : '_') {
            case Opcodes.POP /* 87 */:
                o.dy.d dVar = o.dy.d.a;
                throw null;
            default:
                o.dy.d dVar2 = o.dy.d.a;
                int i2 = b + 27;
                d = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return dVar2;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final o.dx.d e() {
        int i = b;
        int i2 = i + 49;
        d = i2 % 128;
        int i3 = i2 % 2;
        o.dx.d dVar = this.a;
        int i4 = i + 39;
        d = i4 % 128;
        switch (i4 % 2 != 0 ? 'G' : 'a') {
            case Opcodes.LADD /* 97 */:
                return dVar;
            default:
                int i5 = 1 / 0;
                return dVar;
        }
    }

    private static o.dq.e j() {
        int i = b + 89;
        d = i % 128;
        int i2 = i % 2;
        o.dq.e eVar = o.dq.e.e;
        int i3 = b + 23;
        d = i3 % 128;
        switch (i3 % 2 != 0 ? 'F' : 'B') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                int i4 = 25 / 0;
                return eVar;
            default:
                return eVar;
        }
    }

    @Override // o.du.d
    public final String c() {
        int i = b + 53;
        d = i % 128;
        switch (i % 2 != 0 ? 'T' : (char) 15) {
            case Opcodes.BASTORE /* 84 */:
                throw null;
            default:
                return null;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        g(51 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "\u0016\u0002\n\u0005\u001a\u0010\u0002\u001d\f\u0014\u0015\n\u001f\u000b\u0019\u001c\u0000\u001e\u0007\u0001\u0019\u0012\u001d\"\u0015\u001e\u0005\u0011\u001e\u001f\u001a\u0000\"\u001d\u001e\u0000\u0019\u0012\u001e \u0016\u0006\u001a\u001e\u0006\u001d\u001b\n\u001d#\u001c\u001e", (byte) (42 - TextUtils.getTrimmedLength("")), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(h().toString());
        Object[] objArr2 = new Object[1];
        g(Color.green(0) + 12, "\u0001\u000f\u0006\u001d\u0018\n\u001e \u0016\u0006\u001c\u001e", (byte) (TextUtils.getTrimmedLength("") + 87), objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(e().toString());
        Object[] objArr3 = new Object[1];
        g(ImageFormat.getBitsPerPixel(0) + 14, "\u0001\u000f\u0006\u001d\u001a\u0016\u001c\u0006\"\u0017\u001e\u0018㖽", (byte) (22 - Color.blue(0)), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(j().toString()).append('}').toString();
        int i = d + 85;
        b = i % 128;
        int i2 = i % 2;
        return obj;
    }

    public final boolean equals(Object obj) {
        int i = b;
        int i2 = i + 71;
        d = i2 % 128;
        int i3 = i2 % 2;
        switch (this == obj ? (char) 1 : 'L') {
            case Base64.mimeLineLength /* 76 */:
                if (obj == null || getClass() != obj.getClass()) {
                    return false;
                }
                return Objects.equals(this.a, ((b) obj).a);
            default:
                int i4 = i + 31;
                d = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return false;
                    default:
                        return true;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x0044, code lost:
    
        r13 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0045, code lost:
    
        if (r13 >= r11) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0047, code lost:
    
        r14 = '^';
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x004c, code lost:
    
        switch(r14) {
            case 94: goto L23;
            default: goto L115;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0052, code lost:
    
        r14 = o.du.b.$10 + 59;
        o.du.b.$11 = r14 % 128;
        r14 = r14 % r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x005d, code lost:
    
        r15 = new java.lang.Object[]{java.lang.Integer.valueOf(r6[r13])};
        r4 = o.e.a.s.get(java.lang.Integer.valueOf(r9));
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x006f, code lost:
    
        if (r4 == null) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00c6, code lost:
    
        r12[r13] = ((java.lang.Character) ((java.lang.reflect.Method) r4).invoke(null, r15)).charValue();
        r13 = r13 + 1;
        r4 = 2;
        r7 = '0';
        r9 = -1401577988;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0072, code lost:
    
        r4 = (java.lang.Class) o.e.a.c(17 - (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (android.text.TextUtils.indexOf("", r7, 0, 0) + 1), (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16) + 76);
        r7 = o.du.b.$$a[0];
        r14 = (byte) (r7 - 1);
        r9 = new java.lang.Object[1];
        i(r7, r14, (byte) (r14 + 5), r9);
        r4 = r4.getMethod((java.lang.String) r9[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1401577988, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x00d2, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00d3, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00d7, code lost:
    
        if (r1 != null) goto L33;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00d9, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x00da, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x004f, code lost:
    
        r6 = r12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x004a, code lost:
    
        r14 = '6';
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r26, java.lang.String r27, byte r28, java.lang.Object[] r29) {
        /*
            Method dump skipped, instructions count: 1066
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.b.g(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
