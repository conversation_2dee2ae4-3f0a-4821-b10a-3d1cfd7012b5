package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzhp.smali */
final /* synthetic */ class zzhp {
    static final /* synthetic */ int[] zza;

    static {
        int[] iArr = new int[7];
        zza = iArr;
        try {
            iArr[3] = 1;
        } catch (NoSuchFieldError e) {
        }
        try {
            zza[4] = 2;
        } catch (NoSuchFieldError e2) {
        }
        try {
            zza[2] = 3;
        } catch (NoSuchFieldError e3) {
        }
        try {
            zza[5] = 4;
        } catch (NoSuchFieldError e4) {
        }
        try {
            zza[6] = 5;
        } catch (NoSuchFieldError e5) {
        }
        try {
            zza[0] = 6;
        } catch (NoSuchFieldError e6) {
        }
        try {
            zza[1] = 7;
        } catch (NoSuchFieldError e7) {
        }
    }
}
