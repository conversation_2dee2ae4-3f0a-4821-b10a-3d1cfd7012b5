package o.j;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Looper;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.work.PeriodicWorkRequest;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.e.a;
import o.ee.e;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\j\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] b;
    private static int c;
    private static boolean d;
    private static int e;

    static void e() {
        b = new int[]{-980657746, -549799057, -78730204, 1652879601, 471574688, -212010230, 1070566797, -1714681603, -2004124734, 705897598, -1192270575, -1710267846, -1089283423, -1524388467, 1398594784, -168264863, -1591861673, 202662999};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r0 = o.j.c.$$a
            int r9 = 116 - r9
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r9
            r4 = r2
            r9 = r8
            r8 = r7
            goto L2e
        L16:
            r3 = r2
        L17:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2e:
            int r7 = r7 + r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.j.c.g(short, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
        $$b = 69;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        e();
        ViewConfiguration.getGlobalActionKeyTimeout();
        d = false;
        int i = e + 87;
        c = i % 128;
        int i2 = i % 2;
    }

    private static void d(Context context, b bVar) {
        int i = c;
        int i2 = i + 49;
        e = i2 % 128;
        switch (i2 % 2 == 0 ? '_' : 'J') {
            case 'J':
                if (bVar == null) {
                    int i3 = i + Opcodes.DSUB;
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                }
                g.c();
                Object[] objArr = new Object[1];
                f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, 24 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                f(new int[]{-417393073, 298555237, -1746986100, 796047987, 17091177, 406725826, -498396208, 1860371974, -1283113480, 836281380, 1755154498, -494886230, -1316045952, 496206201}, (ViewConfiguration.getTouchSlop() >> 8) + 25, objArr2);
                g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar.d()).toString());
                Object[] objArr3 = new Object[1];
                f(new int[]{1909189597, -2123340332, -669378125, 2104854843, -1986971391, 207197980, -669378125, 2104854843, 1444291440, 1749380517, 1678424710, -1036480169, 330503580, -1061370067, 940984809, 1998989421, -1386711187, -1247410302, -1778996515, -1932974746, 41328594, -1554844581, 1005539931, 1017835705}, 47 - ExpandableListView.getPackedPositionGroup(0L), objArr3);
                SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
                Object[] objArr4 = new Object[1];
                f(new int[]{-489640429, 1730404185, 1957168832, -418620526, -1283113480, 836281380, -1411221323, 113568588}, 14 - Gravity.getAbsoluteGravity(0, 0), objArr4);
                edit.putString(((String) objArr4[0]).intern(), bVar.d()).commit();
                return;
            default:
                throw null;
        }
    }

    public static b d(Context context) {
        int i = c + 73;
        e = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, 23 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(new int[]{-1162664888, 2054471420, -1572517207, -1780172586, 1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, -2044277481, 372808217}, ExpandableListView.getPackedPositionChild(0L) + 26, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f(new int[]{1909189597, -2123340332, -669378125, 2104854843, -1986971391, 207197980, -669378125, 2104854843, 1444291440, 1749380517, 1678424710, -1036480169, 330503580, -1061370067, 940984809, 1998989421, -1386711187, -1247410302, -1778996515, -1932974746, 41328594, -1554844581, 1005539931, 1017835705}, 47 - (ViewConfiguration.getTouchSlop() >> 8), objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        Object[] objArr4 = new Object[1];
        f(new int[]{-489640429, 1730404185, 1957168832, -418620526, -1283113480, 836281380, -1411221323, 113568588}, 13 - Process.getGidForName(""), objArr4);
        b d2 = b.d(sharedPreferences.getString(((String) objArr4[0]).intern(), ""));
        int i3 = c + 7;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? '8' : 'V') {
            case '8':
                throw null;
            default:
                return d2;
        }
    }

    public static boolean c(Context context, b bVar) {
        int i = c;
        int i2 = i + 15;
        e = i2 % 128;
        if (i2 % 2 == 0) {
            throw null;
        }
        switch (bVar == null ? '2' : '\\') {
            case '2':
                int i3 = i + 41;
                e = i3 % 128;
                int i4 = i3 % 2;
                return false;
            default:
                b d2 = d(context);
                d(context, bVar);
                switch (bVar != b.c) {
                    default:
                        int i5 = c + 97;
                        e = i5 % 128;
                        int i6 = i5 % 2;
                        if (d2 != b.c) {
                            g.c();
                            Object[] objArr = new Object[1];
                            f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, 24 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            f(new int[]{1430440393, -781115236, -1291158522, -411197604, -79658354, -2067841995, -199662337, 571646497, -903647165, -1031622217, 2051633088, -1218913178, -1469709325, -1672494131, 1973656572, 1495391970, -2079587316, 1556437035, 2030796682, 142945880, 17091177, 406725826, -498396208, 1860371974, 2084736168, -612172478, 2008723578, 601938706}, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 53, objArr2);
                            g.d(intern, ((String) objArr2[0]).intern());
                            int i7 = c + 35;
                            e = i7 % 128;
                            int i8 = i7 % 2;
                            return true;
                        }
                    case true:
                        return false;
                }
        }
    }

    public static void b() {
        int i = e;
        int i2 = i + 55;
        c = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                if (d) {
                    int i3 = i + 93;
                    c = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                }
                g.c();
                Object[] objArr = new Object[1];
                f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, TextUtils.indexOf((CharSequence) "", '0') + 25, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f(new int[]{-1090877288, 331409515, 726327070, -1660110892, 2030796682, 142945880, 17091177, 406725826, -498396208, 1860371974, -1960089949, -602549640, 270179380, -1807727163, -2019893682, -151170912}, (ViewConfiguration.getTouchSlop() >> 8) + 32, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                d = true;
                e.a();
                new o.ee.b(Looper.getMainLooper()).postDelayed(new Runnable() { // from class: o.j.c$$ExternalSyntheticLambda0
                    @Override // java.lang.Runnable
                    public final void run() {
                        c.d();
                    }
                }, PeriodicWorkRequest.MIN_PERIODIC_FLEX_MILLIS);
                int i5 = e + 1;
                c = i5 % 128;
                int i6 = i5 % 2;
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void d() {
        int i = c + 55;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        if (d) {
            d = false;
            g.c();
            Object[] objArr = new Object[1];
            f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, 24 - ExpandableListView.getPackedPositionGroup(0L), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(new int[]{1262355440, -591818389, -126517468, 573829909, -1529912309, 1862301126, -492126353, 1005068585, -1778978115, 888307238, -1503124707, 399726323, -203040539, -1838280212, 1571500797, -506610780}, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 30, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            int i4 = e + 5;
            c = i4 % 128;
            int i5 = i4 % 2;
            return;
        }
        int i6 = i2 + 1;
        c = i6 % 128;
        switch (i6 % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static void c() {
        if (d) {
            g.c();
            Object[] objArr = new Object[1];
            f(new int[]{1791656005, 1015601088, 1916871948, 1635888224, 1335624341, 313828338, 726760784, -721577035, 127997947, -1841024077, 774916817, -293072696}, Gravity.getAbsoluteGravity(0, 0) + 24, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(new int[]{-1025880234, -208251408, 2074777161, -1282083651, -1529912309, 1862301126, -492126353, 1005068585, -1778978115, 888307238, -1503124707, 399726323, -203040539, -1838280212, 1571500797, -506610780}, 29 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            d = false;
            int i = c + Opcodes.LUSHR;
            e = i % 128;
            int i2 = i % 2;
            return;
        }
        int i3 = c + Opcodes.LSUB;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    public static boolean a() {
        int i = e + Opcodes.LSHL;
        int i2 = i % 128;
        c = i2;
        Object obj = null;
        switch (i % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                boolean z = d;
                int i3 = i2 + Opcodes.DSUB;
                e = i3 % 128;
                switch (i3 % 2 == 0 ? '@' : '-') {
                    case '-':
                        return z;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    private static void f(int[] iArr, int i, Object[] objArr) {
        Object method;
        int[] iArr2;
        int i2;
        o.a.g gVar = new o.a.g();
        char[] cArr = new char[4];
        int i3 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = b;
        int i4 = -1667374059;
        int i5 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i6 = $11 + 95;
            $10 = i6 % 128;
            int i7 = i6 % 2;
            int i8 = 0;
            while (true) {
                switch (i8 < length ? i5 : 1) {
                    case 1:
                        iArr3 = iArr4;
                        break;
                    default:
                        int i9 = $11 + Opcodes.LSUB;
                        $10 = i9 % 128;
                        if (i9 % i3 != 0) {
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i5] = Integer.valueOf(iArr3[i8]);
                                Object obj = a.s.get(Integer.valueOf(i4));
                                if (obj == null) {
                                    Class cls = (Class) a.c(10 - Color.green(i5), (char) (8856 - View.MeasureSpec.makeMeasureSpec(i5, i5)), (ViewConfiguration.getTapTimeout() >> 16) + 324);
                                    byte b2 = (byte) i5;
                                    byte b3 = b2;
                                    Object[] objArr3 = new Object[1];
                                    g(b2, b3, b3, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    a.s.put(-1667374059, obj);
                                }
                                iArr4[i8] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                                i8 <<= 0;
                                i3 = 2;
                                i4 = -1667374059;
                                i5 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } else {
                            try {
                                Object[] objArr4 = {Integer.valueOf(iArr3[i8])};
                                Object obj2 = a.s.get(-1667374059);
                                if (obj2 == null) {
                                    Class cls2 = (Class) a.c(10 - TextUtils.indexOf("", "", 0), (char) (8856 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 324 - TextUtils.getTrimmedLength(""));
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    g(b4, b5, b5, objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    a.s.put(-1667374059, obj2);
                                }
                                iArr4[i8] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i8++;
                                i3 = 2;
                                i4 = -1667374059;
                                i5 = 0;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
            }
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = b;
        if (iArr6 != null) {
            int length3 = iArr6.length;
            int[] iArr7 = new int[length3];
            int i10 = 0;
            while (i10 < length3) {
                try {
                    Object[] objArr6 = {Integer.valueOf(iArr6[i10])};
                    Object obj3 = a.s.get(-1667374059);
                    if (obj3 != null) {
                        iArr2 = iArr6;
                        i2 = length3;
                    } else {
                        Class cls3 = (Class) a.c((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 9, (char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 8856), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 324);
                        byte b6 = (byte) 0;
                        byte b7 = b6;
                        iArr2 = iArr6;
                        i2 = length3;
                        Object[] objArr7 = new Object[1];
                        g(b6, b7, b7, objArr7);
                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                        a.s.put(-1667374059, obj3);
                    }
                    iArr7[i10] = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    i10++;
                    iArr6 = iArr2;
                    length3 = i2;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            iArr6 = iArr7;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr5);
            int i11 = 0;
            for (int i12 = 16; i11 < i12; i12 = 16) {
                int i13 = $11 + 73;
                $10 = i13 % 128;
                switch (i13 % 2 != 0) {
                    case true:
                        gVar.e &= iArr5[i11];
                        try {
                            Object[] objArr8 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                            Object obj4 = a.s.get(-2036901605);
                            if (obj4 == null) {
                                obj4 = ((Class) a.c(11 - TextUtils.indexOf("", "", 0, 0), (char) (TextUtils.lastIndexOf("", '0', 0) + 1), 572 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)))).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                                a.s.put(-2036901605, obj4);
                            }
                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                            gVar.e = gVar.c;
                            gVar.c = intValue;
                            i11 += Opcodes.LSHR;
                            break;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    default:
                        gVar.e ^= iArr5[i11];
                        try {
                            Object[] objArr9 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                            Object obj5 = a.s.get(-2036901605);
                            if (obj5 == null) {
                                obj5 = ((Class) a.c(Process.getGidForName("") + 12, (char) ((-1) - TextUtils.lastIndexOf("", '0')), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                                a.s.put(-2036901605, obj5);
                            }
                            int intValue2 = ((Integer) ((Method) obj5).invoke(null, objArr9)).intValue();
                            gVar.e = gVar.c;
                            gVar.c = intValue2;
                            i11++;
                            break;
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                }
            }
            int i14 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i14;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i15 = gVar.e;
            int i16 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            o.a.g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr10 = {gVar, gVar};
                Object obj6 = a.s.get(-331007466);
                if (obj6 != null) {
                    method = obj6;
                } else {
                    Class cls4 = (Class) a.c(KeyEvent.normalizeMetaState(0) + 12, (char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 55183), 515 - View.resolveSize(0, 0));
                    byte b8 = (byte) 0;
                    byte b9 = b8;
                    Object[] objArr11 = new Object[1];
                    g(b8, b9, (byte) (b9 + 1), objArr11);
                    method = cls4.getMethod((String) objArr11[0], Object.class, Object.class);
                    a.s.put(-331007466, method);
                }
                ((Method) method).invoke(null, objArr10);
            } catch (Throwable th6) {
                Throwable cause6 = th6.getCause();
                if (cause6 == null) {
                    throw th6;
                }
                throw cause6;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }
}
