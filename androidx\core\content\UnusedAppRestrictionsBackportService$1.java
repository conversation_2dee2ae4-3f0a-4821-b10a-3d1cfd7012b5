package androidx.core.content;

import android.os.RemoteException;
import androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback;
import androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\content\UnusedAppRestrictionsBackportService$1.smali */
class UnusedAppRestrictionsBackportService$1 extends IUnusedAppRestrictionsBackportService.Stub {
    final /* synthetic */ UnusedAppRestrictionsBackportService this$0;

    UnusedAppRestrictionsBackportService$1(UnusedAppRestrictionsBackportService this$0) {
        this.this$0 = this$0;
    }

    @Override // androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportService
    public void isPermissionRevocationEnabledForApp(IUnusedAppRestrictionsBackportCallback callback) throws RemoteException {
        if (callback == null) {
            return;
        }
        UnusedAppRestrictionsBackportCallback backportCallback = new UnusedAppRestrictionsBackportCallback(callback);
        this.this$0.isPermissionRevocationEnabled(backportCallback);
    }
}
