package o.bs;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import o.a.l;
import org.bouncycastle.i18n.LocalizedMessage;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\i.smali */
final class i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final i a;
    public static final i b;
    public static final i c;
    public static final i d;
    public static final i e;
    private static final /* synthetic */ i[] g;
    public static final i h;
    public static final i i;
    public static final i j;
    private static int k;
    private static int m;
    private static char[] n;
    private final int f;

    static void b() {
        n = new char[]{50943, 50853, 50858, 50860, 50859, 50855, 50856, 50854, 50838, 50874, 50737, 51145, 50737, 51145, 51141, 51144, 51145, 50765, 51143, 51158, 51156, 51164, 51156, 51164, 51142, 51166, 51159, 51153, 51155, 51183, 51159, 50941, 50855, 50851, 50843, 50841, 50857, 50853, 50858, 50921, 50838, 50879, 50854, 50859, 50855, 50859, 50875, 50873, 50871, 50762, 50875, 50840, 50872, 50864, 50861, 50765, 50765, 50856, 50876, 50725, 50720, 50725, 50728, 50729, 50713, 50859, 50713, 50708, 50716, 50695, 50802, 50697, 50714, 50700, 50802, 50713, 50717, 50688};
    }

    static void init$0() {
        $$a = new byte[]{71, 41, -111, Base64.padSymbol};
        $$b = 235;
    }

    private static void o(int i2, int i3, int i4, Object[] objArr) {
        int i5 = 4 - (i2 * 3);
        int i6 = (i3 * 4) + 1;
        byte[] bArr = $$a;
        int i7 = i4 + 66;
        byte[] bArr2 = new byte[i6];
        int i8 = -1;
        int i9 = i6 - 1;
        if (bArr == null) {
            i5++;
            i7 = i9 + i7;
            i9 = i9;
        }
        while (true) {
            i8++;
            bArr2[i8] = (byte) i7;
            if (i8 == i9) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b2 = bArr[i5];
            i5++;
            i7 += b2;
            i9 = i9;
        }
    }

    private static /* synthetic */ i[] d() {
        int i2 = k + 99;
        int i3 = i2 % 128;
        m = i3;
        int i4 = i2 % 2;
        i[] iVarArr = {a, c, b, d, e, i, h, j};
        int i5 = i3 + 57;
        k = i5 % 128;
        switch (i5 % 2 != 0 ? '\\' : ':') {
            case Opcodes.ASTORE /* 58 */:
                return iVarArr;
            default:
                int i6 = 97 / 0;
                return iVarArr;
        }
    }

    public static i valueOf(String str) {
        int i2 = k + 93;
        m = i2 % 128;
        int i3 = i2 % 2;
        i iVar = (i) Enum.valueOf(i.class, str);
        int i4 = m + 13;
        k = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return iVar;
            default:
                int i5 = 65 / 0;
                return iVar;
        }
    }

    public static i[] values() {
        int i2 = k + 89;
        m = i2 % 128;
        switch (i2 % 2 == 0 ? '\r' : ',') {
            case ',':
                return (i[]) g.clone();
            default:
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        m = 1;
        b();
        Object[] objArr = new Object[1];
        l("\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001", new int[]{0, 9, 0, 0}, true, objArr);
        a = new i(((String) objArr[0]).intern(), 0, 0);
        Object[] objArr2 = new Object[1];
        l("\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{9, 8, Opcodes.IFNE, 0}, false, objArr2);
        c = new i(((String) objArr2[0]).intern(), 1, 10);
        Object[] objArr3 = new Object[1];
        l("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000", new int[]{17, 14, Opcodes.DRETURN, 7}, true, objArr3);
        b = new i(((String) objArr3[0]).intern(), 2, 20);
        Object[] objArr4 = new Object[1];
        l("\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000", new int[]{31, 8, 0, 4}, true, objArr4);
        d = new i(((String) objArr4[0]).intern(), 3, 30);
        Object[] objArr5 = new Object[1];
        l("\u0001\u0001\u0000\u0001\u0001\u0001\u0001", new int[]{39, 7, 0, 0}, false, objArr5);
        e = new i(((String) objArr5[0]).intern(), 4, 40);
        Object[] objArr6 = new Object[1];
        Object obj = null;
        l(null, new int[]{46, 12, 17, 6}, true, objArr6);
        i = new i(((String) objArr6[0]).intern(), 5, 50);
        Object[] objArr7 = new Object[1];
        l("\u0001\u0000\u0000\u0000\u0001\u0001\u0000", new int[]{58, 7, Opcodes.ISHR, 6}, false, objArr7);
        h = new i(((String) objArr7[0]).intern(), 6, 60);
        Object[] objArr8 = new Object[1];
        l("\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{65, 13, 102, 5}, false, objArr8);
        j = new i(((String) objArr8[0]).intern(), 7, 70);
        g = d();
        int i2 = m + 5;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    private i(String str, int i2, int i3) {
        this.f = i3;
    }

    public final int e() {
        int i2 = m + 55;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return this.f;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static void l(String str, int[] iArr, boolean z, Object[] objArr) {
        int i2;
        char[] cArr;
        int i3;
        String str2 = str;
        Object obj = null;
        int i4 = 1;
        int i5 = 0;
        byte[] bArr = str2;
        if (str2 != null) {
            int i6 = $11 + 95;
            $10 = i6 % 128;
            switch (i6 % 2 == 0) {
                case true:
                    bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
                default:
                    str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    obj.hashCode();
                    throw null;
            }
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i7 = iArr[0];
        int i8 = iArr[1];
        int i9 = iArr[2];
        int i10 = iArr[3];
        char[] cArr2 = n;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i11 = 0;
            while (true) {
                switch (i11 < length ? '=' : 'N') {
                    case 'N':
                        cArr2 = cArr3;
                        break;
                    default:
                        try {
                            Object[] objArr2 = new Object[i4];
                            objArr2[i5] = Integer.valueOf(cArr2[i11]);
                            Object obj2 = o.e.a.s.get(1951085128);
                            if (obj2 != null) {
                                cArr = cArr2;
                                i3 = length;
                            } else {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 11, (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 43 - TextUtils.getOffsetAfter("", i5));
                                byte b2 = (byte) i5;
                                byte b3 = b2;
                                cArr = cArr2;
                                i3 = length;
                                Object[] objArr3 = new Object[1];
                                o(b2, b3, (byte) (b3 | 54), objArr3);
                                obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj2);
                            }
                            cArr3[i11] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                            i11++;
                            cArr2 = cArr;
                            length = i3;
                            i4 = 1;
                            i5 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        char[] cArr4 = new char[i8];
        System.arraycopy(cArr2, i7, cArr4, 0, i8);
        if (bArr2 != null) {
            char[] cArr5 = new char[i8];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i8 ? ')' : (char) 15) {
                    case ')':
                        if (bArr2[lVar.d] == 1) {
                            int i12 = lVar.d;
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj3 = o.e.a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls2 = (Class) o.e.a.c(';' - AndroidCharacter.getMirror('0'), (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 448 - ExpandableListView.getPackedPositionGroup(0L));
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    o(b4, b5, (byte) (b5 | 53), objArr5);
                                    obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj3);
                                }
                                cArr5[i12] = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        } else {
                            int i13 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj4 = o.e.a.s.get(804049217);
                                if (obj4 == null) {
                                    Class cls3 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 11, (char) KeyEvent.keyCodeFromString(""), 207 - (ViewConfiguration.getScrollBarSize() >> 8));
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    o(b6, b7, (byte) (b7 | 56), objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj4);
                                }
                                cArr5[i13] = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        c2 = cArr5[lVar.d];
                        try {
                            Object[] objArr8 = {lVar, lVar};
                            Object obj5 = o.e.a.s.get(-2112603350);
                            if (obj5 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) ((-1) - ImageFormat.getBitsPerPixel(0)), Color.blue(0) + 259);
                                byte b8 = (byte) 0;
                                byte b9 = b8;
                                Object[] objArr9 = new Object[1];
                                o(b8, b9, b9, objArr9);
                                obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr8);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    default:
                        cArr4 = cArr5;
                        break;
                }
            }
        }
        if (i10 > 0) {
            char[] cArr6 = new char[i8];
            i2 = 0;
            System.arraycopy(cArr4, 0, cArr6, 0, i8);
            int i14 = i8 - i10;
            System.arraycopy(cArr6, 0, cArr4, i14, i10);
            System.arraycopy(cArr6, i10, cArr4, 0, i14);
        } else {
            i2 = 0;
        }
        if (z) {
            char[] cArr7 = new char[i8];
            lVar.d = i2;
            int i15 = $11 + 9;
            $10 = i15 % 128;
            int i16 = i15 % 2;
            while (lVar.d < i8) {
                cArr7[lVar.d] = cArr4[(i8 - lVar.d) - 1];
                lVar.d++;
            }
            cArr4 = cArr7;
        }
        if (i9 > 0) {
            int i17 = 0;
            while (true) {
                lVar.d = i17;
                switch (lVar.d < i8) {
                    case false:
                        break;
                    default:
                        cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                        i17 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr4);
    }
}
