package com.esotericsoftware.asm;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\asm\Handle.smali */
public final class Handle {
    final int a;
    final String b;
    final String c;
    final String d;
    final boolean e;

    public Handle(int i, String str, String str2, String str3) {
        this(i, str, str2, str3, i == 9);
    }

    public Handle(int i, String str, String str2, String str3, boolean z) {
        this.a = i;
        this.b = str;
        this.c = str2;
        this.d = str3;
        this.e = z;
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof Handle)) {
            return false;
        }
        Handle handle = (Handle) obj;
        return this.a == handle.a && this.e == handle.e && this.b.equals(handle.b) && this.c.equals(handle.c) && this.d.equals(handle.d);
    }

    public String getDesc() {
        return this.d;
    }

    public String getName() {
        return this.c;
    }

    public String getOwner() {
        return this.b;
    }

    public int getTag() {
        return this.a;
    }

    public int hashCode() {
        return this.a + (this.e ? 64 : 0) + (this.b.hashCode() * this.c.hashCode() * this.d.hashCode());
    }

    public boolean isInterface() {
        return this.e;
    }

    public String toString() {
        return new StringBuffer().append(this.b).append('.').append(this.c).append(this.d).append(" (").append(this.a).append(this.e ? " itf" : "").append(')').toString();
    }
}
