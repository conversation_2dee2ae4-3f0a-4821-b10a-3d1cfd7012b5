package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192K1FieldElement.smali */
public class SecP192K1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37"));
    protected int[] a;

    public SecP192K1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP192K1FieldElement");
        }
        this.a = SecP192K1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192K1Field.add(this.a, ((SecP192K1FieldElement) eCFieldElement).a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = u5.a();
        SecP192K1Field.addOne(this.a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192K1Field.inv(((SecP192K1FieldElement) eCFieldElement).a, a);
        SecP192K1Field.multiply(a, this.a, a);
        return new SecP192K1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP192K1FieldElement) {
            return u5.a(this.a, ((SecP192K1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP192K1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 6);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = u5.a();
        SecP192K1Field.inv(this.a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return u5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return u5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192K1Field.multiply(this.a, ((SecP192K1FieldElement) eCFieldElement).a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = u5.a();
        SecP192K1Field.negate(this.a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (u5.b(iArr) || u5.a(iArr)) {
            return this;
        }
        int[] a = u5.a();
        SecP192K1Field.square(iArr, a);
        SecP192K1Field.multiply(a, iArr, a);
        int[] a2 = u5.a();
        SecP192K1Field.square(a, a2);
        SecP192K1Field.multiply(a2, iArr, a2);
        int[] a3 = u5.a();
        SecP192K1Field.squareN(a2, 3, a3);
        SecP192K1Field.multiply(a3, a2, a3);
        SecP192K1Field.squareN(a3, 2, a3);
        SecP192K1Field.multiply(a3, a, a3);
        SecP192K1Field.squareN(a3, 8, a);
        SecP192K1Field.multiply(a, a3, a);
        SecP192K1Field.squareN(a, 3, a3);
        SecP192K1Field.multiply(a3, a2, a3);
        int[] a4 = u5.a();
        SecP192K1Field.squareN(a3, 16, a4);
        SecP192K1Field.multiply(a4, a, a4);
        SecP192K1Field.squareN(a4, 35, a);
        SecP192K1Field.multiply(a, a4, a);
        SecP192K1Field.squareN(a, 70, a4);
        SecP192K1Field.multiply(a4, a, a4);
        SecP192K1Field.squareN(a4, 19, a);
        SecP192K1Field.multiply(a, a3, a);
        SecP192K1Field.squareN(a, 20, a);
        SecP192K1Field.multiply(a, a3, a);
        SecP192K1Field.squareN(a, 4, a);
        SecP192K1Field.multiply(a, a2, a);
        SecP192K1Field.squareN(a, 6, a);
        SecP192K1Field.multiply(a, a2, a);
        SecP192K1Field.square(a, a);
        SecP192K1Field.square(a, a2);
        if (u5.a(iArr, a2)) {
            return new SecP192K1FieldElement(a);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = u5.a();
        SecP192K1Field.square(this.a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192K1Field.subtract(this.a, ((SecP192K1FieldElement) eCFieldElement).a, a);
        return new SecP192K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return u5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return u5.c(this.a);
    }

    public SecP192K1FieldElement() {
        this.a = u5.a();
    }

    protected SecP192K1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
