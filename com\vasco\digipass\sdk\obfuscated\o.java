package com.vasco.digipass.sdk.obfuscated;

import bc.org.bouncycastle.crypto.paddings.PKCS7Padding;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.models.SecureChannelMessage;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelGenerateResponse;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessage;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessageGenerator;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\o.smali */
public final class o {
    private static final byte[] a = {UtilitiesSDKConstants.SRP_LABEL_ENC, -123, 42, -101, -7, -42, 35, 66, 12, 24, 78, -103, 87, 77, -82, -95};
    private static final byte[] b = {71, 109, 122, 15, Base64.padSymbol, -2, -54, -71, -112, -96, -31, 73, 95, ByteCompanionObject.MAX_VALUE, 109, -36};
    private static final byte[] c = {10, -73, 56, 92, -81, 66, -84, 34, -62, 92, 107, -4, 116, 80, -8, 64};
    private static final byte[] d = {-124, -16, 27, 4, -32, 115, 113, -58, -117, 55, 76, 104, 32, 81, -58, -108};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\o$b.smali */
    private static class b {
        private final c a;
        private c[] b;

        private b() {
            this.a = new c();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\o$c.smali */
    private static class c {
        private byte a;
        private boolean b;

        private c() {
        }
    }

    public static byte[] a(e eVar, String str) {
        byte[] bytes = l.a(eVar).getBytes();
        byte[] bArr = new byte[64];
        byte[] bArr2 = a;
        System.arraycopy(bArr2, 0, bArr, 0, bArr2.length);
        byte[] bArr3 = b;
        System.arraycopy(bArr3, 0, bArr, 16, bArr3.length);
        byte[] bArr4 = c;
        System.arraycopy(bArr4, 0, bArr, 32, bArr4.length);
        byte[] bArr5 = d;
        System.arraycopy(bArr5, 0, bArr, 48, bArr5.length);
        return l.a(bytes, eVar, str, q.a(bArr));
    }

    public static UtilitiesSDKSecureChannelMessage b(SecureChannelMessage secureChannelMessage) {
        UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage = new UtilitiesSDKSecureChannelMessage();
        utilitiesSDKSecureChannelMessage.authenticationTag = secureChannelMessage.authenticationTag;
        utilitiesSDKSecureChannelMessage.body = secureChannelMessage.body;
        utilitiesSDKSecureChannelMessage.encrypted = secureChannelMessage.encrypted;
        utilitiesSDKSecureChannelMessage.messageType = secureChannelMessage.messageType;
        utilitiesSDKSecureChannelMessage.nonce = secureChannelMessage.nonce;
        utilitiesSDKSecureChannelMessage.protectionType = secureChannelMessage.protectionType;
        utilitiesSDKSecureChannelMessage.protocolVersion = secureChannelMessage.protocolVersion;
        utilitiesSDKSecureChannelMessage.rawData = secureChannelMessage.rawData;
        utilitiesSDKSecureChannelMessage.serialNumber = secureChannelMessage.serialNumber;
        return utilitiesSDKSecureChannelMessage;
    }

    public static byte[] c(e eVar, String str) {
        return a(eVar, str, false);
    }

    public static byte[] b(e eVar, String str) {
        return a(eVar, str, true);
    }

    public static byte[] a(e eVar, String str, boolean z) {
        byte[] bArr;
        byte[] a2 = a(eVar, str);
        if (z) {
            bArr = eVar.d.t;
        } else {
            bArr = eVar.d.q;
        }
        byte[] a3 = a(false, a2, bArr, (byte) 2);
        q.g(a2);
        return a3;
    }

    public static void b(SecureChannelMessage secureChannelMessage, byte[] bArr) {
        if (secureChannelMessage.protectionType == 0) {
            return;
        }
        if (secureChannelMessage.authenticationTag.compareTo(a(secureChannelMessage, bArr)) != 0) {
            throw new h(DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SIGNATURE_INVALID);
        }
    }

    public static void b(byte[] bArr, byte[] bArr2) {
        if (q.f(bArr2)) {
            return;
        }
        b bVar = new b();
        a(bVar.a, bArr2[1]);
        int length = bArr2.length - 2;
        bVar.b = new c[length];
        for (int i = 0; i < length; i++) {
            c cVar = new c();
            a(cVar, bArr2[i + 2]);
            bVar.b[i] = cVar;
        }
        p.a(bArr);
        if (bArr[1] == 8) {
            if (((bArr[2] & 255) << 8) + (bArr[3] & 255) == bArr.length - 4) {
                a(bArr, 4, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT, bVar);
                return;
            }
            throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH);
        }
        throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
    }

    public static String a(SecureChannelMessage secureChannelMessage) {
        UtilitiesSDKSecureChannelGenerateResponse generateSecureChannelMessage = UtilitiesSDKSecureChannelMessageGenerator.generateSecureChannelMessage(b(secureChannelMessage));
        if (generateSecureChannelMessage.getReturnCode() == 0) {
            return generateSecureChannelMessage.getMessage();
        }
        throw new Exception("" + generateSecureChannelMessage.getReturnCode());
    }

    public static byte[] a(byte[] bArr, byte[] bArr2) {
        byte[] bArr3 = new byte[16];
        System.arraycopy(bArr2, 0, bArr3, 0, bArr2.length);
        new PKCS7Padding().addPadding(bArr3, bArr2.length);
        return UtilitiesSDK.encrypt((byte) 3, (byte) 1, bArr, null, bArr3).getOutputData();
    }

    public static byte[] a(boolean z, byte[] bArr, byte[] bArr2, byte b2, byte[] bArr3) {
        UtilitiesSDKCryptoResponse decrypt;
        if (z) {
            decrypt = UtilitiesSDK.encrypt((byte) 3, b2, bArr, bArr3, bArr2);
        } else {
            decrypt = UtilitiesSDK.decrypt((byte) 3, b2, bArr, bArr3, bArr2);
        }
        if (decrypt.getReturnCode() == 0) {
            return decrypt.getOutputData();
        }
        throw new Exception("" + decrypt.getReturnCode());
    }

    public static byte[] a(boolean z, byte[] bArr, byte[] bArr2, byte b2) {
        return a(z, bArr, bArr2, b2, null);
    }

    public static String a(boolean z, SecureChannelMessage secureChannelMessage, byte[] bArr) {
        boolean z2 = secureChannelMessage.encrypted;
        if ((z2 && z) || (!z2 && !z)) {
            return secureChannelMessage.body;
        }
        byte[] a2 = q.a(secureChannelMessage.nonce);
        byte[] bArr2 = new byte[16];
        System.arraycopy(a2, 0, bArr2, 0, a2.length);
        return q.a(a(z, bArr, q.a(secureChannelMessage.body), (byte) 4, bArr2));
    }

    public static String a(SecureChannelMessage secureChannelMessage, byte[] bArr) {
        byte[] a2 = q.a(secureChannelMessage.rawData);
        int length = a2.length - 8;
        byte[] bArr2 = new byte[length];
        System.arraycopy(a2, 0, bArr2, 0, length);
        byte[] outputData = UtilitiesSDK.hmac((byte) 3, bArr2, bArr).getOutputData();
        byte[] bArr3 = new byte[8];
        System.arraycopy(outputData, 0, bArr3, 0, 8);
        return q.a(bArr3);
    }

    private static void a(c cVar, byte b2) {
        cVar.a = (byte) (((b2 >> 4) & 15) + 1);
        cVar.b = (b2 & 15) == 1;
    }

    private static void a(byte[] bArr, int i, int i2, b bVar) {
        c cVar;
        if (q.f(bArr)) {
            return;
        }
        byte b2 = 9;
        boolean z = false;
        while (i < bArr.length) {
            int i3 = i + 1;
            byte b3 = bArr[i];
            if (i3 < bArr.length) {
                int i4 = i3 + 1;
                int i5 = bArr[i3] & 255;
                if (i4 < bArr.length) {
                    int i6 = i5 + i4;
                    if (i6 > bArr.length) {
                        throw new h(i2);
                    }
                    if (b3 == 61) {
                        z = true;
                    } else if (b3 == 17) {
                        z = false;
                    } else if (b3 == 20) {
                        i = i4 + 1;
                        b2 = bArr[i4];
                    } else if (b3 == 41 || b3 == 43) {
                        if (z) {
                            cVar = bVar.a;
                        } else {
                            cVar = b2 <= bVar.b.length ? bVar.b[b2 - 1] : null;
                        }
                        if (cVar == null) {
                            i = i4;
                        } else if (b3 == 41) {
                            i = i4 + 1;
                            bArr[i4] = cVar.a;
                        } else {
                            i = i4 + 1;
                            bArr[i4] = cVar.b ? (byte) 1 : (byte) 0;
                        }
                    } else {
                        i = i6;
                    }
                    i = i4;
                } else {
                    throw new h(i2);
                }
            } else {
                throw new h(i2);
            }
        }
    }
}
