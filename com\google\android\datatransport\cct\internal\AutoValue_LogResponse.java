package com.google.android.datatransport.cct.internal;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\cct\internal\AutoValue_LogResponse.smali */
final class AutoValue_LogResponse extends LogResponse {
    private final long nextRequestWaitMillis;

    AutoValue_LogResponse(long nextRequestWaitMillis) {
        this.nextRequestWaitMillis = nextRequestWaitMillis;
    }

    @Override // com.google.android.datatransport.cct.internal.LogResponse
    public long getNextRequestWaitMillis() {
        return this.nextRequestWaitMillis;
    }

    public String toString() {
        return "LogResponse{nextRequestWaitMillis=" + this.nextRequestWaitMillis + "}";
    }

    public boolean equals(Object o2) {
        if (o2 == this) {
            return true;
        }
        if (!(o2 instanceof LogResponse)) {
            return false;
        }
        LogResponse that = (LogResponse) o2;
        return this.nextRequestWaitMillis == that.getNextRequestWaitMillis();
    }

    public int hashCode() {
        int h$ = 1 * 1000003;
        long j = this.nextRequestWaitMillis;
        return h$ ^ ((int) (j ^ (j >>> 32)));
    }
}
