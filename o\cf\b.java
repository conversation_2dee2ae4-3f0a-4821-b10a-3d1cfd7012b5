package o.cf;

import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\b.smali */
public final class b {
    private o.bg.d d;
    private o.j.b j;
    private static int i = 0;
    private static int g = 1;
    private long a = -1;
    private boolean b = true;
    private a c = null;
    private String e = null;

    public final void d(a aVar) {
        int i2 = i;
        int i3 = ((i2 | 71) << 1) - (i2 ^ 71);
        int i4 = i3 % 128;
        g = i4;
        switch (i3 % 2 == 0) {
            case false:
                this.b = false;
                break;
            default:
                this.b = true;
                break;
        }
        this.c = aVar;
        this.e = null;
        int i5 = (i4 & Opcodes.LNEG) + (i4 | Opcodes.LNEG);
        i = i5 % 128;
        int i6 = i5 % 2;
    }

    public final boolean d() {
        int i2 = g;
        int i3 = (i2 ^ 11) + ((i2 & 11) << 1);
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        boolean z = this.b;
        int i6 = (i4 & 15) + (i4 | 15);
        g = i6 % 128;
        int i7 = i6 % 2;
        return z;
    }

    public final a e() {
        int i2 = (g + 66) - 1;
        int i3 = i2 % 128;
        i = i3;
        Object obj = null;
        switch (i2 % 2 != 0 ? 'P' : (char) 23) {
            case 'P':
                obj.hashCode();
                throw null;
            default:
                a aVar = this.c;
                int i4 = (i3 + 42) - 1;
                g = i4 % 128;
                switch (i4 % 2 == 0 ? Typography.dollar : 'B') {
                    case 'B':
                        return aVar;
                    default:
                        throw null;
                }
        }
    }

    public final long c() {
        int i2 = i + 23;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        long j = this.a;
        int i5 = ((i3 | 93) << 1) - (i3 ^ 93);
        i = i5 % 128;
        int i6 = i5 % 2;
        return j;
    }

    public final void e(long j) {
        int i2 = i;
        int i3 = (i2 & Opcodes.DREM) + (i2 | Opcodes.DREM);
        int i4 = i3 % 128;
        g = i4;
        int i5 = i3 % 2;
        this.a = j;
        int i6 = (i4 + 28) - 1;
        i = i6 % 128;
        switch (i6 % 2 != 0 ? '8' : 'J') {
            case '8':
                throw null;
            default:
                return;
        }
    }

    public final o.bg.d b() {
        int i2 = (i + 6) - 1;
        int i3 = i2 % 128;
        g = i3;
        Object obj = null;
        switch (i2 % 2 == 0 ? 'a' : (char) 31) {
            case Opcodes.LADD /* 97 */:
                obj.hashCode();
                throw null;
            default:
                o.bg.d dVar = this.d;
                int i4 = i3 + 57;
                i = i4 % 128;
                switch (i4 % 2 != 0 ? 'K' : (char) 14) {
                    case 'K':
                        obj.hashCode();
                        throw null;
                    default:
                        return dVar;
                }
        }
    }

    public final void e(o.bg.d dVar) {
        int i2 = g + 79;
        i = i2 % 128;
        boolean z = i2 % 2 == 0;
        this.d = dVar;
        switch (z) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final o.j.b a() {
        int i2 = i;
        int i3 = ((i2 | 23) << 1) - (i2 ^ 23);
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return this.j;
            default:
                throw null;
        }
    }

    public final void a(o.j.b bVar) {
        int i2 = i + 27;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        this.j = bVar;
        int i5 = (i3 + 6) - 1;
        i = i5 % 128;
        int i6 = i5 % 2;
    }
}
