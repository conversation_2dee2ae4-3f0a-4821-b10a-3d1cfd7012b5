package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.math.ec.rfc8032.Ed25519;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\g4.smali */
public final class g4 extends AsymmetricKeyParameter {
    private final Ed25519.PublicPoint b;

    public g4(byte[] bArr) {
        this(a(bArr), 0);
    }

    private static Ed25519.PublicPoint a(byte[] bArr, int i) {
        Ed25519.PublicPoint validatePublicKeyPartialExport = Ed25519.validatePublicKeyPartialExport(bArr, i);
        if (validatePublicKeyPartialExport != null) {
            return validatePublicKeyPartialExport;
        }
        throw new IllegalArgumentException("invalid public key");
    }

    public g4(byte[] bArr, int i) {
        super(false);
        this.b = a(bArr, i);
    }

    private static byte[] a(byte[] bArr) {
        if (bArr.length == 32) {
            return bArr;
        }
        throw new IllegalArgumentException("'buf' must have length 32");
    }
}
