package o.de;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import kotlin.text.Typography;
import org.bouncycastle.i18n.LocalizedMessage;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\f.smali */
public final class f {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final f A;
    public static final f B;
    private static final /* synthetic */ f[] C;
    private static long E;
    private static int F;
    private static char[] G;
    private static int H;
    public static final f a;
    public static final f b;
    public static final f c;
    public static final f d;
    public static final f e;
    public static final f f;
    public static final f g;
    public static final f h;
    public static final f i;
    public static final f j;
    public static final f k;
    public static final f l;
    public static final f m;
    public static final f n;

    /* renamed from: o, reason: collision with root package name */
    public static final f f54o;
    public static final f p;
    public static final f q;
    public static final f r;
    public static final f s;
    public static final f t;
    public static final f u;
    public static final f v;
    public static final f w;
    public static final f x;
    public static final f y;
    public static final f z;
    private final String D;

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void J(short r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.de.f.$$a
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 4 - r7
            int r9 = r9 + 102
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            goto L32
        L17:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1b:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L32:
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.f.J(short, byte, int, java.lang.Object[]):void");
    }

    static void e() {
        char[] cArr = new char[1712];
        ByteBuffer.wrap("\u001a¦i~ýxAgÕ`Yr\u00adW1T\u0085S\tJ\u009dFá4u:ù;M?Ñ\u000f%\"©\n=#\u0081\b\u0015\u0016\u0099\bíþqÿÅãIÀÝâ!ÍµÚ9Ç\u008dÂ\u0011ÀeË,\u0088_pËvwxãoon\u009bB\u0007@³I?B«T×:C4Ï5{1ç0\u0013$\u009f\u0012\u000b\t·\u0002#\u0013¯\u0002ÛôGó,\u0088_PËVwIãNo\\\u009by\u0007z³}?d«h×\u001aC\u0014Ï\u0015{\u0011ç,\u0013\b\u009f!\u000b9·\u0017#3¯$ÛÓGÖóÕ\u007fÆëø\u0017ç\u0083õ\u000fú»ñ'ê,\u0088_pËvwxãboj\u009bG\u0007[³F?S«_×8C7Ï2{)ç*\u00136\u009f\u0006\u000b\r·\u0003#\u0000¯\u001fÛð,\u0088_PËVwIãNo\\\u009by\u0007z³}?d«h×\u001aC\u0014Ï\u0015{\u0011ç!\u0013\f\u009f'\u000b*·(#3¯ ÛÙGÚóÊ\u007fÐëì\u0017ô\u0083å\u000fò»ó'îSýß\u009aK\u0092÷\u0089c®ï\u0085\u001b\u0094\u0087¯3©¿¯+¤VTÂPNOúLf@\u0092G\u001eP\u008ar6i¢o.nZ\u0016Æ\u000b,\u0088_pËvwxãoo|\u009bY\u0007@³X?@«Y×>C'Ï:{1ç&\u0013&\u009f\u001d\u000b\u0002·\u0006#\u0011¯\u001bÛêGüóö\u007fíëã\u0017Ò\u0083Ò\u000fÏ\u0016·eoñiMvÙqUc¡F=E\u0089B\u0005[\u0091Wí%y+õ*A.Ý\u001e)3¥\u00181\u0015\u008d\u0017\u0019\f\u0095\u001fáæ}åÉõEïÑÓ-Ë¹Ú5Í\u0081Ì\u001dÑiÂå¥q\u00adÍ¶Y\u0091Õº!«½\u0090\t\u0096\u0085\u0090\u0011\u009blkøotpÀs\\\u007f¨x$o°M\fV\u0098P\u0014Q`)ü4H\u0011Ä5P&¬\u00008;´\u0014\u0000\u001d\u009c\u0002è\u0017dèðç,\u0088_pËvwxãoo|\u009bY\u0007@³X?@«Y×>C'Ï:{1ç&\u0013&\u009f\u001d\u000b\u0002·\u0006#\u0011¯\u001bÛêGüóö\u007fíëã\u0017Ò\u0083Ò\u000fÏ»Ú'ÎSÙß£K¢÷²c±ï¬\u001b\u0087\u0087\u009e3\u009d¿\u00869=JåÞãbüöûzé\u008eÌ\u0012Ï¦È*Ñ¾ÝÂ¯V¡Ú n¤ò\u0094\u0006¹\u008a\u0092\u001e\u009f¢\u009d6\u0086º\u0095ÎlRoæ\u007fjeþY\u0002A\u0096P\u001aG®F2[FHÊ/^'â<v\u001bú0\u000e0\u0092\u000b&\u0015ª\u001b>\u001dCì×ð,\u0088_pËvwxãoo|\u009bY\u0007@³X?@«Y×>C'Ï:{1ç&\u0013&\u009f\u001d\u000b\u0002·\u0017#\u0000¯\u0012ÛøGúó÷\u007f÷ÿ¢\u008cz\u0018|¤c0d¼vHSÔP`WìNxB\u00040\u0090>\u001c?¨;4\u000bÀ&L\rØ\u0000d\u0002ð\u0019|\n\bó\u0094ð à¬ú8ÆÄÞPÏÜØhÙôÄ\u0080×\f°\u0098¸$£°\u0084<¯È¯T\u0094à\u008al\u0084ø\u0082\u0085s\u0011o\u009dF)fµqAkÍlYGåJqYý@\u0089;\u00150¿¿ÌGXAäOpXüK\bn\u0094w o¬w8nD\tÐ\u0010\\\rè\u0006t\u0011\u0080\u0011\f*\u00985$ °7<%HÏÔÍ`ÀìÀxÅ\u0084á\u0010ö\u009cü(í´íÀîL\u0083Ø\u0098d\u0091ð\u0092|\u0099\u0097>äæpàÌÿXøÔê Ï¼Ì\bË\u0084Ò\u0010Þl¬ø¢t£À§\\\u0097¨º$\u0091°\u009c\f\u009e\u0098\u0085\u0014\u0096`oülH|ÄfPZ¬B8S´D\u0000E\u009cXèKd,ð$L?Ø\u0018T3 -<\u0006\u0088\u001b\u0004\u001c\u0090\u001díèyäõìAçÝð)ð¥ËJ:9Â\u00adÄ\u0011Ê\u0085Ý\tÎýëaòÕêYòÍë±\u008c%\u0095©\u0088\u001d\u0083\u0081\u0094u\u0094ù¯m°Ñ»E¼É\u00ad½N!K\u0095B\u0019R\u008d^qqåjifÝy,\u0088_PËVwIãNo\\\u009by\u0007z³}?d«h×\u001aC\u0014Ï\u0015{\u0011ç!\u0013\f\u009f'\u000b*·(#3¯ ÛÙGÚóÊ\u007fÐëì\u0017ô\u0083å\u000fò»ó'îSýß\u009aK\u0092÷\u0089c®ï\u0085\u001b\u009b\u0087°3\u00ad¿ª+«V^ÂRNZúQfF\u0092F\u001e}\u008aJ6n¢u.cZ Æ\u000fr\u001eþ\u0011j\f\u00963\u00024H\u009f;g¯a\u0013o\u0087x\u000bkÿNcW×O[WÏN³)'0«-\u001f&\u00831w1û\no\u0015Ó\u001eG\u0019Ë\b¿ë#î\u0097ç\u001b÷\u008fûsÔçÏkÃßÜCÇ7Ë»´/\u00ad\u0093¢\u0007·\u008b¸\u007f\u0087,\u0088_PËVwIãNo\\\u009by\u0007z³}?d«h×\u001aC\u0014Ï\u0015{\u0011ç,\u0013\u0006\u009f=\u000b>·+#4¯8ÛÜGÉóÜ\u007fàëÅ\u0017ò\u0083ò\u000fð»Ñ'ýSèß\u009dK\u008e÷\u0086c\u0082ï\u009f\u001b¼\u0087°3·¿\u008c+¸VCÂRNTúHfJ,\u0088_pËvwxãbod\u009b[\u0007\\³U?V«^×>C'Ï>{:ç,\u0013=\u009f\u001c\u0088Öû\u000eo\bÓ\u0017G\u0010Ë\u0002?'£$\u0017#\u009b:\u000f6sDçJkKßOCx·Y;n¯l\u0013w\u0087|\u000by\u007f\u009eã\u0092W\u008eÛ\u008bO\u0096³\u008a'§« \u001f¸\u0083º÷\u0083{ßïÂS×ÇÌKÔ¿è#õ\u0097î\u001bò\u008fýò&f\u001aê\u0011^\u0018Â\u001e6\u001aº(,\u0088_pËvwxãhoe\u009bV\u0007P³W?@«A×\"C\"Ï2{3ç*\u00136\u009f\u0010\u000b\t·\b,\u0088_PËVwIãNo\\\u009by\u0007z³}?d«h×\u001aC\u0014Ï\u0015{\u0011ç?\u0013\u001b\u009f<\u000b+·.#2¯\"ÛÚGÑóí\u007fÌëÆ\u0017ò\u0083ÿ,\u0088_pËvwxãqoy\u009bZ\u0007I³P?P«D×8C?Ï${1ç \u0013\"\u009f\u0016\u000b\u0013»\u0014ÈÙ\\ÆàÚt×ø÷\fî\u0090â$÷¨ø<ç@\u009fÔ\u0087Xºì\u0085p\u0092\u0084\u008b\b½\u009c¾M\u008d>`ª\u007f\u0016c\u0082n\u000ebúMfFÒ]^XÊZ¶-,\u009b_VËIwUãXox\u009ba\u0007m³x?w«h×\u0010C\bÏ5{\nç\u001d\u0013\u0004\u009f2\u000b1·\t#$¯?ÛÂGÐóË\u007fÈëã\u0017ø\u0083å\u000fÚ»ó'îSàß\u009fK\u009c÷\u0085c\u008dï\u008e\tHz¥îºR¦Æ«J§¾\u0088\"\u0083\u0096\u0098\u001a\u009d\u008e\u009fòèfýêæ^ùÂã6ôºÅ.Ú\u0092Ã\u0006Ý\u008aÊþ-\u0082¬ñae~ÙbMoÁO5V©Z\u001dO\u0091@\u0005_y'í?a\tÕ*I(½;1\u0007¥\u001e\u0019\u0019\u008d\u0018\u0001\u001buÉéí]÷ÑÆEÿ¹Æ-Ô¡É\u0015Á\u0089ÐË\n¸ç,ø\u0090ä\u0004é\u0088å|ÁàÖTØØ×Lß0²¤©(¤\u009c³\u0000¡ô³x\u0087ì\u0095P\u0089Ä\u0082H\u009f<b |\u0014m\u0098a\ftÿK\u008c\u0086\u0018\u0099¤\u00850\u0088¼¨H±Ô½`¨ì§x¸\u0004À\u0090Ø\u001cî¨Í4ÏÀÜLàØùdþðÿ|ü\b.\u0094\n \u0010¬!8\u0018Ä!P3Ü.h&ô7\u0080\u001f\fL\u0098_$c°C<ZÈkT|àhlpøi\u0085\u008e\u0011\u008e\u009d\u0085|\u001d\u000fð\u009bï'ó³þ?òËÖWÁãÏoÀûÈ\u0087¥\u0013¾\u009f³+¤·¶C¤Ï\u0090[\u0082ç\u009es\u0095ÿ\u0088\u008bu\u0017k£z/v»cGNÓC_Oë[\u0004¸wuãj_vË{G[³B/N\u009b[\u0017T\u0083Kÿ3k+ç\u001dS>Ï<;/·\u0013#\n\u009f\r\u000b\f\u0087\u000fóÝoùÛãWÒÃë?Ò«À'Ý\u0093Õ\u000fÄ{ë÷¶cªß¡K°Ç\u00893\u0095¯\u0088\u001b\u0093\u0097\u0096\u0003\u008f~`ê{fwÒhD\u00927\u007f£`\u001f|\u008bq\u0007}óYoNÛ@WOÃG¿*+1§<\u0013+\u008f9{+÷\u001fc\rß\u0011K\u001aÇ\u0007³ú/ä\u009bõ\u0017ù\u0083ì\u007fÁëÙgÑÓØOÏ;Ö·»# \u009f§\u000b§\u0087¬,\u009b_VËIwUãXox\u009ba\u0007m³x?w«h×\u0010C\bÏ>{\u001dç\u001f\u0013\f\u009f0\u000b)·.#/¯,ÛþGÚóÀ\u007fñëÈ\u0017ñ\u0083ã\u000fþ»ö'çSÈß\u0095K\u0089÷\u0082c\u0093ï¨\u001b´\u0087\u00ad3½¿\u0093+¿VXÂWNRúIfJ\u0092|\u001ec\u008ay6f¢u.n,\u009b_vËiwuãxot\u009bP\u0007G³I?F«N×#C8Ï5{\"ç0\u0013\"\u009f\u0016\u000b\u0004·\u0018#\u0013¯\u000eÛóGíóü\u007fðëå\u0017È\u0083Ò\u000fÚ»×'ËSÖß£K¯÷¨c§ï¢\u001b\u0099\u0087\u009a3\u0086¿\u0096+\u009dVsÂpNoú`,\u009b_VËIwUãXox\u009ba\u0007m³x?w«h×\u0010C\bÏ,{\u0004ç\u0006\u0013\u001d\u009f:\u000b3· #\u0012¯\"ÛØGüóØ\u007fÑëÉ,\u009b_vËiwuãxot\u009bB\u0007^³P?W«D×9C6Ï${6ç&\u0013$\u009f\f\u000b\u001e·\u0006#\u0013¯\u000f,\u008b__ËRwDãJoN\u009bq\u0007U³v?a«K×\u0016C\u0018Ï\u0017{\u0000ç\u000b\u0013=\u009f!\u000b<·)#2¯*ÛÖGËóÐ\u007fÌëÃ,\u008b_\u007fËrwdãjon\u009bQ\u0007@³R?L«R×#C#Ï#Ù\u0084ªP>]\u0082K\u0016E\u009aAn~òZFyÊn^Q\"\r¶\u001d:\u0017\u008e\u000f\u0012\u0013æ\u0015j:þ'B$Ö\u001aZ6.Û²Þ\u0006Å\u008aÍ\u001eÁâìv÷úûNäÛ\u0014¨à<í\u0080û\u0014õ\u0098ñlÎðßDÉÈ×\\Í ¼´¼8¼,\u008b__ËRwDãJoN\u009bq\u0007U³v?a«_×\u0012C\u001cÏ\u0014{\u0011ç\n\u0013$\u009f2\u000b3·&#&¯.ÛØGÚó×\u007f×\nwy\u0083í\u008eQ\u0098Å\u0096I\u0092½\u00ad!¼\u0095·\u0019º\u008d¼ñÄeÙéÂ]ÆÁÞ5Ò¹û,\u0099_AËRwCãToH\u009ba\u0007V³w?j«y×\u001eC\u0010Ï\u0017{\fç\u0015\u0013\b\u009f'\u000b4·(#/\u00819òÁfÒÚÃNÔÂÈ6áªà\u001eð\u0092í\u0006äz\u0083î\u0098b\u009aÖ\u0089J\u0086¾\u00932²¦©\u001a®\u008e®\u0002¥RÈ!\u001aµ\u000f\t\"\u009d\u0015\u0011\u001få\u0003y>Í<A#Õ0©u=Z±M\u0005q\u0099YmBáaupÉp]lÑ`¥\u009f9¯\u008d\u0092\u0001\u008c\u0095\u008ci½\bÂ{0ï%S6Ç.K5¿\u000b#\u000e\u0097\u0005\u001b\b\u008f\u0002ó}gfëj_mÃn7u»B/C\u0093[\u0007@\u008bSÿ²c¢×¾[¢Ï\u00ad3\u0086§\u008b+\u009a\u009f\u0080\u0003\u0084w\u0089×f¤ª0³\u008c\u00ad\u0018¸\u0094¥`»ü\u0086H\u0094Ä\u008aP\u0094,ø¸ùÔ\u0016§ú3ã\u008fý\u001bè\u0097õcÆÿÁKÐÇÞSÔ/¾»®7£^,-÷¹ê\u0005ï\u0091ã\u001dïéæuÛÁÚMÍÙÃ¥¢,\u008d_vËkwnãbon\u009bJ\u0007M³\\?A«B×8C%,\u009a_JËNwSãDoF\u009b^\u0007v³u?o\u009b\u0012èâ|æÀûTìØî,Â°Ü\u0004Ø\u0088Ç\u001cÉ\u0006lu³á»]ºÉ\u009aE±±\u0094-\u0083\u0099\u008a\u0015\u009f\u0081\u009býãiðåçQÿÍô9ÈµÉ!Ã\u009d×\tÚ\u0085ëñ0m.Ù-U\"Á=,\u008f_zËowbãcoj\u009bF\u0007Z³F?W«B×<C4Ï5{:ç:\u00139\u009f\u0017\u000b\u001c·\u0013#\u0004,\u008f_VËIwDãIoo\u009b|\u0007x³p?w«l×\u001bC2Ï\u001a{\u0017ç\u000b\u0013 \u009f=\u000b;·(#3¯&ÛÔGËóÐ\u007fÌëÃ,\u008f_vËiwdãiot\u009bQ\u0007V³^?J«Y×6C=Ï${&ç.\u0013;\u009f\u0017\u000b\u0002·\u000e#\u000f¯\rÛúGíóô\u007fâëù\u0017Þ\u0083Þ\u000fÕ".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1712);
        G = cArr;
        E = -743542020127629517L;
    }

    static void init$0() {
        $$a = new byte[]{124, 92, -85, -9};
        $$b = Opcodes.L2I;
    }

    private static /* synthetic */ f[] b() {
        int i2 = F;
        int i3 = i2 + 63;
        H = i3 % 128;
        int i4 = i3 % 2;
        f[] fVarArr = {b, c, d, e, a, h, g, i, j, f, m, k, l, n, f54o, p, q, s, t, r, x, y, u, w, v, A, B, z};
        int i5 = i2 + 93;
        H = i5 % 128;
        switch (i5 % 2 != 0 ? '@' : Typography.greater) {
            case '@':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return fVarArr;
        }
    }

    public static f valueOf(String str) {
        int i2 = H + 79;
        F = i2 % 128;
        int i3 = i2 % 2;
        f fVar = (f) Enum.valueOf(f.class, str);
        int i4 = H + 31;
        F = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public static f[] values() {
        f[] fVarArr;
        int i2 = H + 109;
        F = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                fVarArr = (f[]) C.clone();
                break;
            default:
                fVarArr = (f[]) C.clone();
                int i3 = 51 / 0;
                break;
        }
        int i4 = H + 57;
        F = i4 % 128;
        int i5 = i4 % 2;
        return fVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        H = 0;
        F = 1;
        e();
        Object[] objArr = new Object[1];
        I((char) (13870 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), KeyEvent.getDeadChar(0, 0), (ViewConfiguration.getTapTimeout() >> 16) + 33, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        I((char) View.MeasureSpec.getSize(0), 34 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), TextUtils.getTrimmedLength("") + 24, objArr2);
        b = new f(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        I((char) View.resolveSize(0, 0), 56 - TextUtils.indexOf((CharSequence) "", '0', 0), 32 - Color.alpha(0), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        I((char) KeyEvent.keyCodeFromString(""), AndroidCharacter.getMirror('0') + ')', 23 - View.MeasureSpec.getMode(0), objArr4);
        c = new f(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        I((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), KeyEvent.normalizeMetaState(0) + Opcodes.IREM, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 55, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        I((char) (ViewConfiguration.getTouchSlop() >> 8), 168 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 31 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr6);
        d = new f(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        I((char) (14911 - Color.green(0)), TextUtils.indexOf("", "") + Opcodes.IFNULL, 67 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        I((char) ExpandableListView.getPackedPositionType(0L), TextUtils.getOffsetAfter("", 0) + 265, Color.red(0) + 42, objArr8);
        e = new f(intern4, 3, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        I((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 5556), 308 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), Gravity.getAbsoluteGravity(0, 0) + 45, objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        I((char) Color.red(0), (-16776864) - Color.rgb(0, 0, 0), 26 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr10);
        a = new f(intern5, 4, ((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        I((char) (54058 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), 378 - TextUtils.getTrimmedLength(""), 56 - Color.alpha(0), objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        I((char) (37687 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), ImageFormat.getBitsPerPixel(0) + 435, 37 - Process.getGidForName(""), objArr12);
        h = new f(intern6, 5, ((String) objArr12[0]).intern());
        Object[] objArr13 = new Object[1];
        I((char) (Color.argb(0, 0, 0, 0) + 48054), 471 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), ExpandableListView.getPackedPositionGroup(0L) + 50, objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        I((char) (26290 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), (-16776694) - Color.rgb(0, 0, 0), 31 - ((Process.getThreadPriority(0) + 20) >> 6), objArr14);
        g = new f(intern7, 6, ((String) objArr14[0]).intern());
        Object[] objArr15 = new Object[1];
        I((char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 554, Color.green(0) + 61, objArr15);
        String intern8 = ((String) objArr15[0]).intern();
        Object[] objArr16 = new Object[1];
        I((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 25624), 615 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 39 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr16);
        i = new f(intern8, 7, ((String) objArr16[0]).intern());
        Object[] objArr17 = new Object[1];
        I((char) ExpandableListView.getPackedPositionGroup(0L), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 652, ImageFormat.getBitsPerPixel(0) + 49, objArr17);
        String intern9 = ((String) objArr17[0]).intern();
        Object[] objArr18 = new Object[1];
        I((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 701, 18 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr18);
        j = new f(intern9, 8, ((String) objArr18[0]).intern());
        Object[] objArr19 = new Object[1];
        I((char) (Color.blue(0) + 42078), 720 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 50 - TextUtils.getOffsetAfter("", 0), objArr19);
        String intern10 = ((String) objArr19[0]).intern();
        Object[] objArr20 = new Object[1];
        I((char) TextUtils.indexOf("", ""), 769 - TextUtils.getCapsMode("", 0, 0), TextUtils.lastIndexOf("", '0', 0, 0) + 21, objArr20);
        f = new f(intern10, 9, ((String) objArr20[0]).intern());
        Object[] objArr21 = new Object[1];
        I((char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 789 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 28 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr21);
        String intern11 = ((String) objArr21[0]).intern();
        Object[] objArr22 = new Object[1];
        I((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 817, 19 - TextUtils.getCapsMode("", 0, 0), objArr22);
        m = new f(intern11, 10, ((String) objArr22[0]).intern());
        Object[] objArr23 = new Object[1];
        I((char) (38799 - TextUtils.getTrimmedLength("")), TextUtils.indexOf((CharSequence) "", '0', 0) + 838, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 19, objArr23);
        String intern12 = ((String) objArr23[0]).intern();
        Object[] objArr24 = new Object[1];
        I((char) (24854 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), ImageFormat.getBitsPerPixel(0) + 857, 13 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr24);
        k = new f(intern12, 11, ((String) objArr24[0]).intern());
        Object[] objArr25 = new Object[1];
        I((char) (Process.myPid() >> 22), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 868, 38 - View.getDefaultSize(0, 0), objArr25);
        String intern13 = ((String) objArr25[0]).intern();
        Object[] objArr26 = new Object[1];
        I((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 9683), 906 - TextUtils.getOffsetAfter("", 0), 23 - View.resolveSize(0, 0), objArr26);
        l = new f(intern13, 12, ((String) objArr26[0]).intern());
        Object[] objArr27 = new Object[1];
        I((char) (44598 - MotionEvent.axisFromString("")), 929 - TextUtils.getCapsMode("", 0, 0), TextUtils.indexOf((CharSequence) "", '0', 0) + 33, objArr27);
        String intern14 = ((String) objArr27[0]).intern();
        Object[] objArr28 = new Object[1];
        I((char) (59282 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), View.getDefaultSize(0, 0) + 961, Color.red(0) + 27, objArr28);
        n = new f(intern14, 13, ((String) objArr28[0]).intern());
        Object[] objArr29 = new Object[1];
        I((char) (54224 - View.MeasureSpec.makeMeasureSpec(0, 0)), MotionEvent.axisFromString("") + 989, TextUtils.indexOf("", "", 0, 0) + 46, objArr29);
        String intern15 = ((String) objArr29[0]).intern();
        Object[] objArr30 = new Object[1];
        I((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 20614), (ViewConfiguration.getLongPressTimeout() >> 16) + 1034, 30 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr30);
        f54o = new f(intern15, 14, ((String) objArr30[0]).intern());
        Object[] objArr31 = new Object[1];
        I((char) (10274 - TextUtils.lastIndexOf("", '0', 0)), (ViewConfiguration.getTouchSlop() >> 8) + 1065, 46 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr31);
        String intern16 = ((String) objArr31[0]).intern();
        Object[] objArr32 = new Object[1];
        I((char) (26633 - KeyEvent.getDeadChar(0, 0)), 1112 - View.combineMeasuredStates(0, 0), (ViewConfiguration.getEdgeSlop() >> 16) + 38, objArr32);
        p = new f(intern16, 15, ((String) objArr32[0]).intern());
        Object[] objArr33 = new Object[1];
        I((char) Gravity.getAbsoluteGravity(0, 0), (Process.myTid() >> 22) + 1150, 54 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr33);
        String intern17 = ((String) objArr33[0]).intern();
        Object[] objArr34 = new Object[1];
        I((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 1204 - (ViewConfiguration.getKeyRepeatDelay() >> 16), (ViewConfiguration.getFadingEdgeLength() >> 16) + 47, objArr34);
        q = new f(intern17, 16, ((String) objArr34[0]).intern());
        Object[] objArr35 = new Object[1];
        I((char) KeyEvent.getDeadChar(0, 0), 1251 - View.MeasureSpec.getMode(0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 28, objArr35);
        String intern18 = ((String) objArr35[0]).intern();
        Object[] objArr36 = new Object[1];
        I((char) (TextUtils.lastIndexOf("", '0') + 1), 1278 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 22 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr36);
        s = new f(intern18, 17, ((String) objArr36[0]).intern());
        Object[] objArr37 = new Object[1];
        I((char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 1300, 27 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr37);
        String intern19 = ((String) objArr37[0]).intern();
        Object[] objArr38 = new Object[1];
        I((char) View.getDefaultSize(0, 0), TextUtils.indexOf((CharSequence) "", '0') + 1328, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 13, objArr38);
        t = new f(intern19, 18, ((String) objArr38[0]).intern());
        Object[] objArr39 = new Object[1];
        I((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 62735), KeyEvent.keyCodeFromString("") + 1341, TextUtils.indexOf("", "") + 31, objArr39);
        String intern20 = ((String) objArr39[0]).intern();
        Object[] objArr40 = new Object[1];
        I((char) (63392 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1371, View.MeasureSpec.getMode(0) + 14, objArr40);
        r = new f(intern20, 19, ((String) objArr40[0]).intern());
        Object[] objArr41 = new Object[1];
        I((char) KeyEvent.normalizeMetaState(0), 1386 - (Process.myTid() >> 22), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 25, objArr41);
        String intern21 = ((String) objArr41[0]).intern();
        Object[] objArr42 = new Object[1];
        I((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 9979), ((byte) KeyEvent.getModifierMetaStateMask()) + 1413, (KeyEvent.getMaxKeyCode() >> 16) + 18, objArr42);
        x = new f(intern21, 20, ((String) objArr42[0]).intern());
        Object[] objArr43 = new Object[1];
        I((char) ((-1) - TextUtils.lastIndexOf("", '0')), 1430 - Gravity.getAbsoluteGravity(0, 0), 21 - KeyEvent.keyCodeFromString(""), objArr43);
        String intern22 = ((String) objArr43[0]).intern();
        Object[] objArr44 = new Object[1];
        I((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 44447), 1451 - (Process.myTid() >> 22), Color.argb(0, 0, 0, 0) + 22, objArr44);
        y = new f(intern22, 21, ((String) objArr44[0]).intern());
        Object[] objArr45 = new Object[1];
        I((char) (Color.blue(0) + 32324), ExpandableListView.getPackedPositionGroup(0L) + 1473, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 27, objArr45);
        String intern23 = ((String) objArr45[0]).intern();
        Object[] objArr46 = new Object[1];
        I((char) (TextUtils.lastIndexOf("", '0', 0) + 9295), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1501, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 32, objArr46);
        u = new f(intern23, 22, ((String) objArr46[0]).intern());
        Object[] objArr47 = new Object[1];
        I((char) (64509 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 1535 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), TextUtils.getTrimmedLength("") + 13, objArr47);
        String intern24 = ((String) objArr47[0]).intern();
        Object[] objArr48 = new Object[1];
        I((char) (KeyEvent.normalizeMetaState(0) + 63628), TextUtils.lastIndexOf("", '0') + 1548, 14 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr48);
        w = new f(intern24, 23, ((String) objArr48[0]).intern());
        Object[] objArr49 = new Object[1];
        I((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 29345), TextUtils.lastIndexOf("", '0', 0, 0) + 1562, 12 - Color.blue(0), objArr49);
        String intern25 = ((String) objArr49[0]).intern();
        Object[] objArr50 = new Object[1];
        I((char) (Process.myPid() >> 22), Gravity.getAbsoluteGravity(0, 0) + 1573, (KeyEvent.getMaxKeyCode() >> 16) + 13, objArr50);
        v = new f(intern25, 24, ((String) objArr50[0]).intern());
        Object[] objArr51 = new Object[1];
        I((char) View.MeasureSpec.getMode(0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1586, View.resolveSizeAndState(0, 0, 0) + 10, objArr51);
        String intern26 = ((String) objArr51[0]).intern();
        Object[] objArr52 = new Object[1];
        I((char) (46985 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 1596 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), ((Process.getThreadPriority(0) + 20) >> 6) + 11, objArr52);
        A = new f(intern26, 25, ((String) objArr52[0]).intern());
        Object[] objArr53 = new Object[1];
        I((char) (ImageFormat.getBitsPerPixel(0) + 10998), 1606 - TextUtils.lastIndexOf("", '0'), Process.getGidForName("") + 28, objArr53);
        String intern27 = ((String) objArr53[0]).intern();
        Object[] objArr54 = new Object[1];
        I((char) View.MeasureSpec.getMode(0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1634, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 22, objArr54);
        B = new f(intern27, 26, ((String) objArr54[0]).intern());
        Object[] objArr55 = new Object[1];
        I((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 1655, 27 - (ViewConfiguration.getEdgeSlop() >> 16), objArr55);
        String intern28 = ((String) objArr55[0]).intern();
        Object[] objArr56 = new Object[1];
        I((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1682, 30 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr56);
        z = new f(intern28, 27, ((String) objArr56[0]).intern());
        C = b();
        int i2 = H + 93;
        F = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 43 / 0;
                return;
            default:
                return;
        }
    }

    private f(String str, int i2, String str2) {
        this.D = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = F;
        int i3 = i2 + 27;
        H = i3 % 128;
        int i4 = i3 % 2;
        String str = this.D;
        int i5 = i2 + 99;
        H = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static f a(String str) {
        int i2 = H + 9;
        F = i2 % 128;
        Object obj = null;
        if (i2 % 2 == 0) {
            obj.hashCode();
            throw null;
        }
        if (str == null) {
            return null;
        }
        f[] values = values();
        int i3 = F + 51;
        H = i3 % 128;
        int i4 = i3 % 2;
        for (f fVar : values) {
            switch (fVar.D.equals(str) ? '^' : 'b') {
                case Opcodes.DUP2_X2 /* 94 */:
                    return fVar;
                default:
            }
        }
        return null;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Removed duplicated region for block: B:4:0x001f  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void I(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 598
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.f.I(char, int, int, java.lang.Object[]):void");
    }
}
