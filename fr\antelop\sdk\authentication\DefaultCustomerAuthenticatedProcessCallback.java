package fr.antelop.sdk.authentication;

import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\DefaultCustomerAuthenticatedProcessCallback.smali */
public interface DefaultCustomerAuthenticatedProcessCallback {
    CustomerAuthenticationPrompt buildCustomerAuthenticationPrompt(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationPromptBuilder customerAuthenticationPromptBuilder);

    void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess);
}
