package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a1.smali */
public class a1 implements h, b5 {
    private g0 b;

    a1(g0 g0Var) {
        this.b = g0Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return a(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new IllegalStateException(e.getMessage());
        }
    }

    static z0 a(g0 g0Var) throws IOException {
        return new z0(g0Var.b());
    }
}
