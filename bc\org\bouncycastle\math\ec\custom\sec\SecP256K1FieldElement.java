package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP256K1FieldElement.smali */
public class SecP256K1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F"));
    protected int[] a;

    public SecP256K1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP256K1FieldElement");
        }
        this.a = SecP256K1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256K1Field.add(this.a, ((SecP256K1FieldElement) eCFieldElement).a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = w5.a();
        SecP256K1Field.addOne(this.a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256K1Field.inv(((SecP256K1FieldElement) eCFieldElement).a, a);
        SecP256K1Field.multiply(a, this.a, a);
        return new SecP256K1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP256K1FieldElement) {
            return w5.b(this.a, ((SecP256K1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP256K1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 8);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = w5.a();
        SecP256K1Field.inv(this.a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return w5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return w5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256K1Field.multiply(this.a, ((SecP256K1FieldElement) eCFieldElement).a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = w5.a();
        SecP256K1Field.negate(this.a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (w5.b(iArr) || w5.a(iArr)) {
            return this;
        }
        int[] c = w5.c();
        int[] a = w5.a();
        SecP256K1Field.square(iArr, a, c);
        SecP256K1Field.multiply(a, iArr, a, c);
        int[] a2 = w5.a();
        SecP256K1Field.square(a, a2, c);
        SecP256K1Field.multiply(a2, iArr, a2, c);
        int[] a3 = w5.a();
        SecP256K1Field.squareN(a2, 3, a3, c);
        SecP256K1Field.multiply(a3, a2, a3, c);
        SecP256K1Field.squareN(a3, 3, a3, c);
        SecP256K1Field.multiply(a3, a2, a3, c);
        SecP256K1Field.squareN(a3, 2, a3, c);
        SecP256K1Field.multiply(a3, a, a3, c);
        int[] a4 = w5.a();
        SecP256K1Field.squareN(a3, 11, a4, c);
        SecP256K1Field.multiply(a4, a3, a4, c);
        SecP256K1Field.squareN(a4, 22, a3, c);
        SecP256K1Field.multiply(a3, a4, a3, c);
        int[] a5 = w5.a();
        SecP256K1Field.squareN(a3, 44, a5, c);
        SecP256K1Field.multiply(a5, a3, a5, c);
        int[] a6 = w5.a();
        SecP256K1Field.squareN(a5, 88, a6, c);
        SecP256K1Field.multiply(a6, a5, a6, c);
        SecP256K1Field.squareN(a6, 44, a5, c);
        SecP256K1Field.multiply(a5, a3, a5, c);
        SecP256K1Field.squareN(a5, 3, a3, c);
        SecP256K1Field.multiply(a3, a2, a3, c);
        SecP256K1Field.squareN(a3, 23, a3, c);
        SecP256K1Field.multiply(a3, a4, a3, c);
        SecP256K1Field.squareN(a3, 6, a3, c);
        SecP256K1Field.multiply(a3, a, a3, c);
        SecP256K1Field.squareN(a3, 2, a3, c);
        SecP256K1Field.square(a3, a, c);
        if (w5.b(iArr, a)) {
            return new SecP256K1FieldElement(a3);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = w5.a();
        SecP256K1Field.square(this.a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256K1Field.subtract(this.a, ((SecP256K1FieldElement) eCFieldElement).a, a);
        return new SecP256K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return w5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return w5.c(this.a);
    }

    public SecP256K1FieldElement() {
        this.a = w5.a();
    }

    protected SecP256K1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
