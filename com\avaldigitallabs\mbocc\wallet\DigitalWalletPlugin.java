package com.avaldigitallabs.mbocc.wallet;

import androidx.core.app.NotificationCompat;
import com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener;
import com.avaldigitallabs.mbocc.wallet.types.ProvisionStatus;
import com.avaldigitallabs.mbocc.wallet.types.WalletStatus;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.firebase.messaging.Constants;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.Wallet;
import fr.antelop.sdk.WalletManager;
import fr.antelop.sdk.WalletProvisioning;
import fr.antelop.sdk.card.CardInfo;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.DigitalCard;
import fr.antelop.sdk.digitalcard.DigitalCardServiceStatus;
import fr.antelop.sdk.digitalcard.Token;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import fr.antelop.sdk.util.OperationCallback;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import kotlinx.coroutines.debug.internal.DebugCoroutineInfoImplKt;

@CapacitorPlugin(name = "DigitalWallet")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalWalletPlugin.smali */
public class DigitalWalletPlugin extends Plugin implements DigitalWalletListener {
    public static final int PUSH_CARD_REQUEST_CODE = 50;
    private PluginCall cardStatusCall;
    private boolean connecting = false;
    private WalletManager manager;
    private WalletProvisioning provisioning;
    private Wallet wallet;

    public DigitalWalletPlugin() {
        DigitalEnrollCallback.setListener(this);
    }

    @PluginMethod
    public void connect(PluginCall call) {
        JSObject result = new JSObject();
        try {
            if (this.manager == null) {
                DigitalWalletCallback callback = new DigitalWalletCallback(this);
                this.manager = new WalletManager(getContext(), callback, null);
            }
            if (!this.connecting && this.wallet == null) {
                this.manager.connect();
                this.connecting = true;
            }
            result.put("connect", true);
        } catch (WalletValidationException e) {
            result.put("connect", false);
            result.put("errorMsg", e.getMessage());
        }
        call.resolve(result);
    }

    @PluginMethod
    public void destroy(PluginCall call) {
        JSObject result = new JSObject();
        try {
            if (this.manager == null) {
                DigitalWalletCallback callback = new DigitalWalletCallback(this);
                this.manager = new WalletManager(getContext(), callback, null);
            }
            this.manager.delete();
            result.put("destroy", true);
        } catch (WalletValidationException e) {
            result.put("destroy", false);
            result.put("errorMsg", e.getMessage());
        }
        call.resolve(result);
    }

    @PluginMethod
    public void provisionActivationCode(PluginCall call) {
        try {
        } catch (WalletValidationException e) {
            emitWalletStatusEvent(ProvisionStatus.DEVICE_ERROR, null, e.getMessage());
        }
        if (this.provisioning == null) {
            call.reject("Error provision not initialized in device");
            return;
        }
        String activationCode = call.getString("activationCode");
        String phoneNumber = call.getString("phoneNumber");
        if (activationCode != null) {
            this.provisioning.launch(activationCodeToBytes(activationCode), phoneNumber);
        }
        call.resolve();
    }

    @PluginMethod
    public void isConnected(PluginCall call) {
        JSObject result = new JSObject();
        result.put("connected", this.wallet != null);
        call.resolve(result);
    }

    @PluginMethod
    public void getWalletId(PluginCall call) {
        JSObject result = new JSObject();
        Wallet wallet = this.wallet;
        result.put("walletId", wallet != null ? wallet.getWalletId() : "");
        call.resolve(result);
    }

    @PluginMethod
    public void getCards(PluginCall call) {
        JSObject result = new JSObject();
        List<JSObject> cards = new ArrayList<>();
        Wallet wallet = this.wallet;
        if (wallet != null) {
            for (DigitalCard digitalCard : wallet.digitalCards(false).values()) {
                JSObject card = new JSObject();
                CardInfo cardInfo = digitalCard.getCardInfo();
                card.put("lastDigits", cardInfo.getLastDigits());
                card.put("bin", cardInfo.getBin());
                card.put("issuerCardId", cardInfo.getIssuerCardId());
                cards.add(card);
            }
        }
        result.put("cards", (Object) new JSArray((Collection) cards));
        call.resolve(result);
    }

    @PluginMethod
    public void isCardEnrolled(PluginCall call) {
        JSObject result = new JSObject();
        boolean z = false;
        if (this.wallet != null) {
            String cardId = call.getString("cardId");
            DigitalCard digitalCard = null;
            if (cardId != null) {
                digitalCard = getDigitalCard(this.wallet, cardId);
            }
            if (digitalCard != null && digitalCard.getStatus() == CardStatus.Active) {
                z = true;
            }
            result.put("enrolled", z);
        } else {
            result.put("enrolled", false);
        }
        call.resolve(result);
    }

    @PluginMethod
    public void enrollDigitalCard(final PluginCall call) {
        try {
            String enrollmentData = call.getString("enrollmentData");
            Wallet wallet = this.wallet;
            if (wallet != null && enrollmentData != null) {
                wallet.enrollDigitalCard(getContext(), enrollmentData, new AntelopCallback() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.1
                    @Override // fr.antelop.sdk.AntelopCallback
                    public void onSuccess() {
                        call.resolve();
                    }

                    @Override // fr.antelop.sdk.AntelopCallback
                    public void onError(AntelopError antelopError) {
                        DigitalWalletPlugin.this.emitWalletStatusEvent(WalletStatus.ENROLLMENT_ERROR, null, antelopError.getMessage());
                    }
                });
                return;
            }
            call.reject("It is not possible to enroll a card in the wallet");
        } catch (WalletValidationException e) {
            emitWalletStatusEvent(WalletStatus.ENROLLMENT_ERROR, null, e.getMessage());
        }
    }

    @PluginMethod
    public void setDisplayCard(PluginCall call) {
        JSObject result = new JSObject();
        result.put(Constants.ScionAnalytics.MessageType.DISPLAY_NOTIFICATION, false);
        call.resolve(result);
    }

    @PluginMethod
    public void verifyCardStatus(PluginCall call) {
        try {
            DigitalCard digitalCard = getDigitalCardEnrolled(call);
            if (digitalCard != null) {
                verifyGooglePayCardStatus(digitalCard);
            } else {
                JSObject result = new JSObject();
                result.put(NotificationCompat.CATEGORY_STATUS, com.avaldigitallabs.mbocc.wallet.types.CardStatus.NOT_ENROLLED);
                call.resolve(result);
            }
        } catch (Exception e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, e.getMessage());
        }
    }

    @PluginMethod
    public void pushCard(PluginCall call) {
        try {
            DigitalCard digitalCard = getDigitalCardEnrolled(call);
            if (digitalCard != null) {
                AndroidActivityResultCallback pushCardCallback = digitalCard.getGooglePayService().pushCard(getActivity(), new OperationCallback<Void>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.2
                    @Override // fr.antelop.sdk.util.OperationCallback
                    public void onSuccess(Void unused) {
                        DigitalWalletPlugin.this.emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.SUCCESS);
                    }

                    @Override // fr.antelop.sdk.util.OperationCallback
                    public void onError(AntelopError antelopError) {
                        DigitalWalletPlugin.this.emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, antelopError.getMessage());
                    }
                });
                DigitalEnrollCallback.setPushCardCallback(pushCardCallback);
            }
        } catch (Exception e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, e.getMessage());
        }
    }

    @PluginMethod
    public void getTokens(final PluginCall call) {
        getTokensCard(call, new DigitalWalletTokensCompletion() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$$ExternalSyntheticLambda3
            @Override // com.avaldigitallabs.mbocc.wallet.DigitalWalletTokensCompletion
            public final void resolve(List list, boolean z) {
                DigitalWalletPlugin.this.lambda$getTokens$0(call, list, z);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$getTokens$0(PluginCall call, List tokens, boolean enabled) {
        JSObject result = new JSObject();
        List<JSObject> _tokens = new ArrayList<>();
        Iterator it = tokens.iterator();
        while (it.hasNext()) {
            Token token = (Token) it.next();
            JSObject _token = new JSObject();
            _token.put("deviceName", token.getDeviceName());
            _token.put("id", token.getId());
            _token.put("isOsPay", token.isGooglePayToken());
            _token.put("name", token.getTokenRequestorName());
            _token.put(NotificationCompat.CATEGORY_STATUS, getTokenStatusToString(token.getStatus()));
            _tokens.add(_token);
        }
        result.put("tokens", (Object) new JSArray((Collection) _tokens));
        result.put("enabled", enabled);
        call.resolve(result);
    }

    @PluginMethod
    public void suspendToken(final PluginCall call) {
        getTokenCard(call, new DigitalWalletTokenCompletion() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$$ExternalSyntheticLambda2
            @Override // com.avaldigitallabs.mbocc.wallet.DigitalWalletTokenCompletion
            public final void resolve(Token token) {
                DigitalWalletPlugin.this.lambda$suspendToken$1(call, token);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$suspendToken$1(final PluginCall call, Token token) {
        try {
            token.suspend(getContext(), new OperationCallback<Void>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.3
                @Override // fr.antelop.sdk.util.OperationCallback
                public void onSuccess(Void unused) {
                    JSObject result = new JSObject();
                    result.put("result", true);
                    call.resolve(result);
                }

                @Override // fr.antelop.sdk.util.OperationCallback
                public void onError(AntelopError antelopError) {
                    JSObject result = new JSObject();
                    result.put("result", false);
                    call.resolve(result);
                }
            });
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.TOKEN_ERROR, e.getMessage());
            call.reject(e.getMessage());
        }
    }

    @PluginMethod
    public void resumeToken(final PluginCall call) {
        getTokenCard(call, new DigitalWalletTokenCompletion() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$$ExternalSyntheticLambda1
            @Override // com.avaldigitallabs.mbocc.wallet.DigitalWalletTokenCompletion
            public final void resolve(Token token) {
                DigitalWalletPlugin.this.lambda$resumeToken$2(call, token);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$resumeToken$2(final PluginCall call, Token token) {
        try {
            token.resume(getContext(), new OperationCallback<Void>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.4
                @Override // fr.antelop.sdk.util.OperationCallback
                public void onSuccess(Void unused) {
                    JSObject result = new JSObject();
                    result.put("result", true);
                    call.resolve(result);
                }

                @Override // fr.antelop.sdk.util.OperationCallback
                public void onError(AntelopError antelopError) {
                    JSObject result = new JSObject();
                    result.put("result", false);
                    call.resolve(result);
                }
            });
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.TOKEN_ERROR, e.getMessage());
            call.reject(e.getMessage());
        }
    }

    @PluginMethod
    public void deleteToken(final PluginCall call) {
        getTokenCard(call, new DigitalWalletTokenCompletion() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$$ExternalSyntheticLambda4
            @Override // com.avaldigitallabs.mbocc.wallet.DigitalWalletTokenCompletion
            public final void resolve(Token token) {
                DigitalWalletPlugin.this.lambda$deleteToken$3(call, token);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$deleteToken$3(final PluginCall call, Token token) {
        try {
            token.delete(getContext(), new OperationCallback<Void>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.5
                @Override // fr.antelop.sdk.util.OperationCallback
                public void onSuccess(Void unused) {
                    JSObject result = new JSObject();
                    result.put("result", true);
                    call.resolve(result);
                }

                @Override // fr.antelop.sdk.util.OperationCallback
                public void onError(AntelopError antelopError) {
                    JSObject result = new JSObject();
                    result.put("result", false);
                    call.resolve(result);
                }
            });
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.TOKEN_ERROR, e.getMessage());
            call.reject(e.getMessage());
        }
    }

    @PluginMethod
    public void disconnect(PluginCall call) {
        WalletManager walletManager = this.manager;
        if (walletManager != null) {
            walletManager.disconnect();
        }
        WalletProvisioning walletProvisioning = this.provisioning;
        if (walletProvisioning != null) {
            walletProvisioning.clean();
        }
        JSObject result = new JSObject();
        result.put("disconnect", true);
        call.resolve(result);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener
    public void onWalletStatus(String status, Wallet wallet) {
        char c;
        emitWalletStatusEvent(status, wallet);
        try {
            switch (status.hashCode()) {
                case -425476020:
                    if (status.equals(WalletStatus.CONNECTION_SUCCESS)) {
                        c = 1;
                        break;
                    }
                    c = 65535;
                    break;
                case 1220071569:
                    if (status.equals(WalletStatus.CONNECTION_ERROR)) {
                        c = 3;
                        break;
                    }
                    c = 65535;
                    break;
                case 1464254619:
                    if (status.equals(WalletStatus.PROVISION_REQUIRED)) {
                        c = 0;
                        break;
                    }
                    c = 65535;
                    break;
                case 1774019330:
                    if (status.equals(WalletStatus.DESTROY_SUCCESS)) {
                        c = 2;
                        break;
                    }
                    c = 65535;
                    break;
                default:
                    c = 65535;
                    break;
            }
            switch (c) {
                case 0:
                    initializedProvisioning();
                    break;
                case 1:
                    this.wallet = wallet;
                    break;
                case 2:
                    this.wallet = null;
                    this.manager.disconnect();
                    break;
                case 3:
                    this.connecting = false;
                    break;
            }
        } catch (WalletValidationException e) {
            emitWalletStatusEvent(WalletStatus.VALIDATION_ERROR, null, e.getMessage());
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @Override // com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener
    public void onProvisionStatus(String status) {
        char c;
        emitWalletStatusEvent(status, null);
        try {
            switch (status.hashCode()) {
                case -1681919683:
                    if (status.equals(ProvisionStatus.DEVICE_ERROR)) {
                        c = 5;
                        break;
                    }
                    c = 65535;
                    break;
                case -1594833330:
                    if (status.equals(ProvisionStatus.PROCESS_ERROR)) {
                        c = 6;
                        break;
                    }
                    c = 65535;
                    break;
                case -972396178:
                    if (status.equals(ProvisionStatus.INITIALIZATION_SUCCESS)) {
                        c = 0;
                        break;
                    }
                    c = 65535;
                    break;
                case -510858945:
                    if (status.equals(ProvisionStatus.DEVICE_NOT_ELIGIBLE)) {
                        c = 4;
                        break;
                    }
                    c = 65535;
                    break;
                case 280312521:
                    if (status.equals(ProvisionStatus.PROCESS_SUCCESS)) {
                        c = 1;
                        break;
                    }
                    c = 65535;
                    break;
                case 379279923:
                    if (status.equals(ProvisionStatus.INITIALIZATION_ERROR)) {
                        c = 2;
                        break;
                    }
                    c = 65535;
                    break;
                case 1154508770:
                    if (status.equals(ProvisionStatus.PROCESS_NOT_GRANTED)) {
                        c = 3;
                        break;
                    }
                    c = 65535;
                    break;
                default:
                    c = 65535;
                    break;
            }
            switch (c) {
                case 0:
                    this.provisioning.checkEligibility(true);
                    break;
                case 1:
                    this.manager.connect();
                    break;
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    this.connecting = false;
                    break;
            }
        } catch (WalletValidationException e) {
            emitWalletStatusEvent(WalletStatus.VALIDATION_ERROR, null, e.getMessage());
        }
    }

    @Override // com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener
    public void onEnrollStatus(String status) {
        emitEnrollStatusEvent(status);
    }

    private void initializedProvisioning() throws WalletValidationException {
        if (this.provisioning == null) {
            DigitalProvisionCallback callback = new DigitalProvisionCallback(this);
            this.provisioning = new WalletProvisioning(getContext(), callback);
        }
        this.provisioning.initialize();
    }

    private DigitalCard getDigitalCard(Wallet wallet, String cardId) {
        for (DigitalCard digitalCard : wallet.digitalCards(false).values()) {
            String lastDigits = digitalCard.getCardInfo().getLastDigits();
            if (cardId.equals(lastDigits)) {
                return digitalCard;
            }
        }
        return null;
    }

    private DigitalCard getDigitalCardEnrolled(PluginCall call) {
        Wallet wallet;
        String cardId = call.getString("cardId");
        DigitalCard digitalCard = null;
        this.cardStatusCall = call;
        if (cardId != null && (wallet = this.wallet) != null) {
            digitalCard = getDigitalCard(wallet, cardId);
        }
        if (digitalCard != null) {
            if (digitalCard.getStatus() != CardStatus.Active) {
                emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.NOT_ENROLLED);
                return null;
            }
        } else {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.NOT_INITIALIZED);
        }
        return digitalCard;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void verifyCardCanPush(DigitalCard digitalCard) {
        try {
            digitalCard.getGooglePayService().canPushCard(getContext(), new OperationCallback<Boolean>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.6
                @Override // fr.antelop.sdk.util.OperationCallback
                public void onSuccess(Boolean available) {
                    DigitalWalletPlugin.this.emitCardStatus(available.booleanValue() ? com.avaldigitallabs.mbocc.wallet.types.CardStatus.AVAILABLE : com.avaldigitallabs.mbocc.wallet.types.CardStatus.TOKENIZED);
                }

                @Override // fr.antelop.sdk.util.OperationCallback
                public void onError(AntelopError antelopError) {
                    DigitalWalletPlugin.this.emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, antelopError.getMessage());
                }
            });
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, e.getMessage());
        }
    }

    private void verifyGooglePayCardStatus(final DigitalCard digitalCard) throws WalletValidationException {
        digitalCard.getGooglePayService().getStatus(getContext(), new OperationCallback<DigitalCardServiceStatus>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.7
            @Override // fr.antelop.sdk.util.OperationCallback
            public void onSuccess(DigitalCardServiceStatus status) {
                switch (AnonymousClass9.$SwitchMap$fr$antelop$sdk$digitalcard$DigitalCardServiceStatus[status.ordinal()]) {
                    case 1:
                        DigitalWalletPlugin.this.verifyCardCanPush(digitalCard);
                        break;
                    case 2:
                        DigitalWalletPlugin.this.configureCardPush(digitalCard);
                        break;
                    default:
                        DigitalWalletPlugin.this.emitCardStatus(status.equals(DigitalCardServiceStatus.Disabled) ? com.avaldigitallabs.mbocc.wallet.types.CardStatus.DISABLED : com.avaldigitallabs.mbocc.wallet.types.CardStatus.NOT_SUPPORT);
                        break;
                }
            }

            @Override // fr.antelop.sdk.util.OperationCallback
            public void onError(AntelopError antelopError) {
                DigitalWalletPlugin.this.emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, antelopError.getMessage());
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void configureCardPush(DigitalCard digitalCard) {
        try {
            digitalCard.getGooglePayService().configureWallet(getActivity());
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.ERROR, e.getMessage());
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void emitWalletStatusEvent(String status, Wallet wallet, String errorMsg) {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, status);
        result.put("errorMsg", errorMsg);
        result.put("hasWallet", wallet != null);
        notifyListeners("walletStatusEvent", result);
    }

    private void emitWalletStatusEvent(String status, Wallet wallet) {
        emitWalletStatusEvent(status, wallet, null);
    }

    private void emitEnrollStatusEvent(String status, String errorMsg) {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, status);
        result.put("errorMsg", errorMsg);
        notifyListeners("enrollStatusEvent", result);
    }

    private void emitEnrollStatusEvent(String status) {
        emitEnrollStatusEvent(status, null);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void emitCardStatus(String status, String errorMsg) {
        if (this.cardStatusCall != null) {
            JSObject result = new JSObject();
            result.put(NotificationCompat.CATEGORY_STATUS, status);
            if (errorMsg != null) {
                result.put("errorMsg", errorMsg);
            }
            this.cardStatusCall.resolve(result);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void emitCardStatus(String status) {
        emitCardStatus(status, null);
    }

    private boolean isTokenEnabled(DigitalCardServiceStatus status) {
        return status == DigitalCardServiceStatus.Active;
    }

    private void getTokensCard(PluginCall call, final DigitalWalletTokensCompletion completion) {
        DigitalCard digitalCard = getDigitalCardEnrolled(call);
        try {
            if (isTokenEnabled(digitalCard.getTokenManagementService().getStatus())) {
                digitalCard.getTokenManagementService().getTokens(getContext(), new OperationCallback<List<Token>>() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin.8
                    @Override // fr.antelop.sdk.util.OperationCallback
                    public void onSuccess(List<Token> tokens) {
                        completion.resolve(tokens, true);
                    }

                    @Override // fr.antelop.sdk.util.OperationCallback
                    public void onError(AntelopError antelopError) {
                        completion.resolve(Collections.emptyList(), true);
                    }
                });
            } else {
                completion.resolve(Collections.emptyList(), false);
            }
        } catch (WalletValidationException e) {
            emitCardStatus(com.avaldigitallabs.mbocc.wallet.types.CardStatus.TOKEN_ERROR, e.getMessage());
        }
    }

    private void getTokenCard(final PluginCall call, final DigitalWalletTokenCompletion completion) {
        getTokensCard(call, new DigitalWalletTokensCompletion() { // from class: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$$ExternalSyntheticLambda0
            @Override // com.avaldigitallabs.mbocc.wallet.DigitalWalletTokensCompletion
            public final void resolve(List list, boolean z) {
                DigitalWalletPlugin.lambda$getTokenCard$4(PluginCall.this, completion, list, z);
            }
        });
    }

    static /* synthetic */ void lambda$getTokenCard$4(PluginCall call, DigitalWalletTokenCompletion completion, List tokens, boolean enabled) {
        String tokenId = call.getString("tokenId", "");
        Iterator it = tokens.iterator();
        while (it.hasNext()) {
            Token token = (Token) it.next();
            if (token.getId().equals(tokenId)) {
                completion.resolve(token);
                return;
            }
        }
        completion.resolve(null);
    }

    /* renamed from: com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin$9, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalWalletPlugin$9.smali */
    static /* synthetic */ class AnonymousClass9 {
        static final /* synthetic */ int[] $SwitchMap$fr$antelop$sdk$digitalcard$DigitalCardServiceStatus;
        static final /* synthetic */ int[] $SwitchMap$fr$antelop$sdk$digitalcard$Token$Status;

        static {
            int[] iArr = new int[Token.Status.values().length];
            $SwitchMap$fr$antelop$sdk$digitalcard$Token$Status = iArr;
            try {
                iArr[Token.Status.Active.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$fr$antelop$sdk$digitalcard$Token$Status[Token.Status.Inactive.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$fr$antelop$sdk$digitalcard$Token$Status[Token.Status.Suspended.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            int[] iArr2 = new int[DigitalCardServiceStatus.values().length];
            $SwitchMap$fr$antelop$sdk$digitalcard$DigitalCardServiceStatus = iArr2;
            try {
                iArr2[DigitalCardServiceStatus.Active.ordinal()] = 1;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$fr$antelop$sdk$digitalcard$DigitalCardServiceStatus[DigitalCardServiceStatus.NotConfiguredByUser.ordinal()] = 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    private String getTokenStatusToString(Token.Status status) {
        switch (AnonymousClass9.$SwitchMap$fr$antelop$sdk$digitalcard$Token$Status[status.ordinal()]) {
            case 1:
                return "ACTIVE";
            case 2:
                return "INACTIVE";
            case 3:
                return DebugCoroutineInfoImplKt.SUSPENDED;
            default:
                return "UNKNOWN";
        }
    }

    private byte[] activationCodeToBytes(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }
}
