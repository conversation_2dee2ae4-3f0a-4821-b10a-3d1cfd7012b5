package com.vasco.digipass.sdk.utils.utilities.responses;

import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\responses\UtilitiesSDKSecureChannelParseResponse.smali */
public class UtilitiesSDKSecureChannelParseResponse extends UtilitiesSDKResponse {
    private UtilitiesSDKSecureChannelMessage c;

    public UtilitiesSDKSecureChannelParseResponse(int i) {
        super(i);
    }

    public UtilitiesSDKSecureChannelMessage getMessage() {
        return this.c;
    }

    public UtilitiesSDKSecureChannelParseResponse(int i, Throwable th) {
        super(i, th);
    }

    public UtilitiesSDKSecureChannelParseResponse(int i, UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) {
        super(i);
        this.c = utilitiesSDKSecureChannelMessage;
    }
}
