package o.bv;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\b.smali */
public final class b implements o.b.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static int d;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        d = 1;
        c();
        Color.blue(0);
        TextUtils.lastIndexOf("", '0', 0);
        TypedValue.complexToFloat(0);
        int i = d + 75;
        b = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        char[] cArr = new char[1057];
        ByteBuffer.wrap(",\u0088£\u008e2×\u0081\u0000\u0010|à¹wâÆ<U\u0018$I´\u0081\u000bÝ\u009a3ivù®H\u0082ßß®\u000f=t\u008d¹\u001cÆ\u0093%b\u001dñVA\u0084Ðõ§+6m\u0086¢\u0015\u009cäú{\u0015Ê]Z¶)è¸>\u000f\u001c\u009eNn\u009aØñWÜÆ·u^ä0\u0014â\u0083¤2x¡TÐ\u001e@Ñÿ«nM\u009d?\rö¼Î+\u009fZZÉ\"y¡èôgh\u0096V\u0005\bµÄ$²S}Âur¦á\u0083, £\u008d2æ\u0081\u000f\u0010aà³wõÆ)U\u0005$O´\u0080\u000bú\u009a\u001cinù§H\u009fßÎ®\u000b=s\u008dü\u001c¨\u0093jb\u0004ñYA\u008dÐú§:6p\u0086í\u0015\u009bäÈ{@ÊHZ½)ó¸%\u000f\u0003\u009e_nÃý\u0085LqÃgS¬\"\u0080±Ô\u0000\u0014\u0097Jg¤öðE!Ô\u0019«\u001c;\u0084\u008aÆ\u0019?èwx¶Ï\u0093^Û\u0099\u008e\u0016£\u0087È4!¥OU\u009dÂÛs\u0007à+\u0091a\u0001®¾Ô/2Ü@L\u0089ý±jà\u001b%\u0088]8Ò©\u0086&D×*Dwô£eÔ\u0012\u0014\u0083^3Ã µQæÎn\u007fkï\u009f\u009cÎ\r\rº.+`ÛíH«ù_vIæ\u0082\u0097®\u0004úµ:\"dÒ\u008aCÞð\u000fa7\u001e2\u008eª?è¬\u0011]YÍ\u0098z½ëõ\u0098j\tt¹\u00956Á§\u0006T\u0007Åqu¼âö\u0093\u0013\u0000Q°\u0083!ÒÞöO+ü`l\u008e\u001dÚ\u008a\u0003;KfÕéøx\u0093ËzZ\u0014ªÆ=\u0080\u008c\\\u001fpn:þõA\u008fÐi#\u001b³Ò\u0002ê\u0095»ä~w\u0006Ç\u0085VÐÙS(i».\u000bÿ\u009aÃíK|\u001dÌÊ_â®¯1q\u0080%\u0010\u008bc\u009còVEtÔ&$ð·´\u0006A\u0089\u0017\u0019\u009ahòû¡JyÝ=-À¼Ì\u000fW\u009ecá:qäÀÿSU¢\u00192Õ\u0085ñ\u0014¾g1öbF\u0087É\u008fXV«\b:\"\u008aä\u001d¸lTÿ\u0006OÙÞ\u0093!ä°}\u00035\u0093ÕâÈuVÄ\u0012W)§ã6¬¹G\b\r, £\u008d2æ\u0081\u000f\u0010aà³wõÆ)U\u0005$O´\u0080\u000bú\u009a\u001cinù§H\u009fßÎ®\u000b=s\u008dð\u001c¥\u0093&b\u001cñ[A\u008aÐ¶§16k\u0086¹\u0015ÒäÕ{\u000fÊ]Z·)á¸%\u000f\u0010\u009e^nÃýÛL8ÃhS¬\"\u0091±\u009d\u0000\u000e\u0097Jg£öíEnÔ\u0004«H;\u0084\u008aØ\u0019'VìÙÁHªûCj-\u009aÿ\r¹¼e/I^\u0003ÎÌq¶àP\u0013\"\u0083ë2Ó¥\u0082ÔGG?÷°fäé&\u0018Q\u008b\u001b;\u008dªªÝfL;üéo\u009e\u009e\u009a\u0001I°\u0016 áSªÂguPä\u0018\u0014È\u0087Ä6m¹8)ìXÎË\u0098zJí\u0002\u001dî\u008cõ?f®^Ñ\u0016AÀð\u0088cz\u00920,¯£\u008c2\u0089\u0081\r\u0010{à®wæÆ$U\u001e$V´Á\u000bò\u009a2ipù¨H\u0095ßö®\u0000={\u008dµ\u001cë\u0093/b2ñ[A\u0095Ðÿ§)6e\u0086¹\u0015\u009bäÔ{\u000eÊ~Z¶)â¸\"\u000f;\u009eUn³ýÝL\"ÃnS\u0082\"\u0091±Î\u0000\u0011\u0097Jg·öðE Ô\u0010«l;\u0097\u008aÅ\u0019%èqx¥Ï\u0093^Í²\u0098=µ¬Þ\u001f7\u008eY~\u008béÍX\u0011Ë=ºw*¸\u0095Â\u0004$÷Vg\u009fÖ§Aö03£K\u0013Ä\u0082\u0090\rRü-ooß«NÍ9\u0002¨s\u0018\u009b\u008b¦zêå6TtÄ§·Ü&\u0000\u0091$\u0000tðºcäÒ\u0000]QÍ\u0099¼\u009b/í\u009e?\t}ù¦hÎÛ&J:5w¥µ\u0014ß\u0087\u000evSæ\u008aQ¯Àà³5\"{\u0092\u008d\u001dð\u008c\u001d\u007fCîp^¶É÷¸\b+r\u009b\u0089\nßõÿd7×sG\u00896×¡>\u0010V\u0083ns¨âøm\nÜ@LÝ?ß®ê\u0019.\u0088px\u0088ëÂZ\u000fÅA´*$µ\u0097ù\u0006=ñSa\u009aÐ\u0094Cã2-½o-ð\u009cÏ\u000f\u0011þBibÙ¡Hº;~ª\b\u001a\u0094\u0085Åtæç*VzÆò±Ï \u0005\u0093_\u0002oò²}ðì!_\nÏ\u0095¾Ù)\u001d\u00983\u000bz\u000f2\u0080\u001f\u0011t¢\u009d3óÃ!Tgå»v\u0097\u0007Ý\u0097\u0012(h¹\u008eJüÚ5k\rü\\\u008d\u0099\u001eá®n?:°øA\u0087ÒÅb\u0001óg\u0084¤\u0015ø¥86@ÇYX\u0087éÈy$\n5\u009b³,\u0082½ÛM\u0002Þ[o¤àýp3\u0001\u0001\u0092\u000f#\u0080´ËD-Õ}fµ÷\u0081\u0088Ë\u0018\u0005©\u0018:³Ëï[ ì\u000b}A\u000e\u0083\u009fË/) f1¼, £\u008d2æ\u0081\u000f\u0010aà³wõÆ)U\u0005$O´\u0080\u000bú\u009a\u001cinù§H\u009fßÎ®\u000b=s\u008dð\u001c¥\u0093:b\u0006ñKA\u0089Ð¶§16k\u0086¹\u0015\u009bäÝ{\tÊJZ¿)ó¸%\u000f\u001a\u009eTnÃýØL#ÃiS¹\"\u009d±Ù\u0000\u0007\u0097YgðöôE;Ô\u0004«H;Å\u008aÈ\u00196è8x³Ï\u0093^Ì-\u000b¼A\f¤\u0083þ\u0012$á)p\u0004ÀÇWÍ&9µv\u0005¬\u0094ÿkÔú\u0002\u008bê\u0004Ç\u0095¬&E·+GùÐ¿acòO\u0083\u0005\u0013Ê¬°=VÎ$^íïÕx\u0084\tA\u009a9*º»ï4nÅVVRæÅw¹\u0000p\u0091*!§²ÌC\u009eÜ\nm\u0011ýñ\u008e¾\u001fi¨S9\u0006ÉÌZÂëkd9ôö\u0085Ö\u0016×§F0\u000eÀîQºâbsT\f\u0015\u009cÎ-\u0094¾pO=ßåh\u009cù\u0085\u008a\\\u001b\b«î$¸µnF&×\u0006g\u008dðÜ\u0081?\u00126¢æ3°Ì\u0099]Eî\u0001~ú\u000f²\u0098f, £\u008d2æ\u0081\u000f\u0010aà³wõÆ)U\u0005$O´\u0080\u000bú\u009a\u001cinù§H\u009fßÎ®\u000b=s\u008dü\u001c¨\u0093jb\u0004ñYA\u008dÐú§:6p\u0086í\u0015\u009bäÈ{@ÊGZ±)ó¸l\u000f\u0014\u009eYn\u0097ýÁL'ÃcSï\"\u0095±Ó\u0000\u0006\u0097\u000bg¾ööE:ÔW«P;\u008a\u008aÍ\u0019<èmxµÏÖ^\u0092-D¼L\f±\u0083ï\u0012)á\u007fp_À\u0093WÅ&:µt\u0005ã\u0094îkÞú\u0014IMÙ½¨ù?&\u008en\u001d^,®£\u009b2Ó\u0081\"\u0010pà®wôÆ'U\u0003$M´\u0083\u000bñ\u009a.iqù\u008aH\u0093ßÍ®\u0007=a\u008d½\u001cñ\u0093#b\u001cñVA Ðõ§46j\u0086¢\u0015\u0085ä×{\u0005ÊMZ¹)â¸!\u000f\u0010\u009eTn\u0097ýüL#ÃoS¨\"\u0093±Ø\u0000\u0010\u0097\u000bgýö¹E/Ô\u0007«L;Å\u008aß\u0019#è\u007fx³Ï\u0097^Û-\u0001¼\r\f¶\u0083þ\u00124álp]À\u0093WÉ&1µ:\u0005î\u0094¨kÃú\u0003I^Ù¡¨ø?1\u008e\u007f\u001dYí\u0097|ÉówBuÒ¨¡ç0Ö\u0087\u001c\u0016Hæ·uëÄ![-*Bº\u0092\tÎ\u0098\u000e,®£\u009b2Ó\u0081\"\u0010pà®wôÆ'U\u0003$M´\u0083\u000bñ\u009a.iqù\u008aH\u0093ßÍ®\u0007=a\u008d½\u001cñ\u0093#b\u001cñVA Ðõ§46j\u0086¢\u0015\u0085ä×{\u0005ÊMZ¹)â¸!\u000f\u0010\u009eTn\u0097ýüL#ÃoS¨\"\u0093±Ø\u0000\u0010\u0097\u000bgýö¹E Ô\u0018«\u001c;\u0084\u008aÚ\u0019#è8x´Ï\u0086^Ø-\u0016¼L\f¶\u0083þ\u0012`ámp[À\u0093WÉ&6µn\u0005¦\u0094ì".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1057);
        e = cArr;
        a = 6424976055391003646L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 4 - r8
            int r9 = r9 * 3
            int r9 = 1 - r9
            byte[] r0 = o.bv.b.$$a
            int r7 = r7 + 102
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L34
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1c:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r7]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r8 = r8 + r7
            int r7 = r9 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.b.g(byte, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{5, 125, -30, 121};
        $$b = Opcodes.LOOKUPSWITCH;
    }

    /* renamed from: o.bv.b$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\b$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] b;
        private static int c;
        private static int d;

        static {
            d = 0;
            c = 1;
            int[] iArr = new int[o.b.e.values().length];
            b = iArr;
            try {
                iArr[o.b.e.c.ordinal()] = 1;
                int i = c;
                int i2 = ((i | 77) << 1) - (i ^ 77);
                d = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                b[o.b.e.d.ordinal()] = 2;
                int i3 = d + 83;
                c = i3 % 128;
                if (i3 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[o.b.e.e.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                b[o.b.e.b.ordinal()] = 4;
                int i4 = (c + 30) - 1;
                d = i4 % 128;
                switch (i4 % 2 != 0 ? '\\' : (char) 14) {
                    case 14:
                        return;
                    default:
                        throw null;
                }
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x01cd  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x01d3  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0200  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x01cf  */
    @Override // o.b.b
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.b.d c(android.content.Context r13, o.b.e r14) {
        /*
            Method dump skipped, instructions count: 726
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.b.c(android.content.Context, o.b.e):o.b.d");
    }

    @Override // o.b.b
    public final o.de.f b(Context context) {
        switch (new e().c(context) ? 'I' : ';') {
            case ';':
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f((char) View.combineMeasuredStates(0, 0), 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 38, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((char) TextUtils.indexOf("", "", 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 986, Color.rgb(0, 0, 0) + 16777288, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                return o.de.f.d;
            default:
                int i = b + 89;
                d = i % 128;
                if (i % 2 == 0) {
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 1, AndroidCharacter.getMirror('0') - '\t', objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 889, 97 - TextUtils.indexOf("", "", 0), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                o.de.f fVar = o.de.f.e;
                int i2 = b + Opcodes.LSHR;
                d = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 17 : (char) 5) {
                    case 5:
                        return fVar;
                    default:
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 590
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.b.f(char, int, int, java.lang.Object[]):void");
    }
}
