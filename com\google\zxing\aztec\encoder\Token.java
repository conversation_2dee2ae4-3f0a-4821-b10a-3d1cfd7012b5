package com.google.zxing.aztec.encoder;

import com.google.zxing.common.BitArray;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\zxing\aztec\encoder\Token.smali */
abstract class Token {
    static final Token EMPTY = new SimpleToken(null, 0, 0);
    private final Token previous;

    abstract void appendTo(BitArray bitArray, byte[] bArr);

    Token(Token previous) {
        this.previous = previous;
    }

    final Token getPrevious() {
        return this.previous;
    }

    final Token add(int value, int bitCount) {
        return new SimpleToken(this, value, bitCount);
    }

    final Token addBinaryShift(int start, int byteCount) {
        return new BinaryShiftToken(this, start, byteCount);
    }
}
