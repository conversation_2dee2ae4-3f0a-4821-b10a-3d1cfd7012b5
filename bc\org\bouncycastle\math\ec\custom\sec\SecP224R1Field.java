package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP224R1Field.smali */
public class SecP224R1Field {
    static final int[] a = {1, 0, 0, -1, -1, -1, -1};
    private static final int[] b = {1, 0, 0, -2, -1, -1, 0, 2, 0, 0, -2, -1, -1, -1};
    private static final int[] c = {-1, -1, -1, 1, 0, 0, -1, -3, -1, -1, 1};

    private static void a(int[] iArr) {
        long j = (iArr[0] & 4294967295L) - 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            long j4 = (j3 >> 32) + (iArr[2] & 4294967295L);
            iArr[2] = (int) j4;
            j2 = j4 >> 32;
        }
        long j5 = j2 + (4294967295L & iArr[3]) + 1;
        iArr[3] = (int) j5;
        if ((j5 >> 32) != 0) {
            c6.c(7, iArr, 4);
        }
    }

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (v5.a(iArr, iArr2, iArr3) != 0 || (iArr3[6] == -1 && v5.c(iArr3, a))) {
            a(iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.a(14, iArr, iArr2, iArr3) != 0 || (iArr3[13] == -1 && c6.d(14, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(14, iArr3, iArr4.length);
            }
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(7, iArr, iArr2) != 0 || (iArr2[6] == -1 && v5.c(iArr2, a))) {
            a(iArr2);
        }
    }

    private static void b(int[] iArr) {
        long j = (iArr[0] & 4294967295L) + 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            long j4 = (j3 >> 32) + (iArr[2] & 4294967295L);
            iArr[2] = (int) j4;
            j2 = j4 >> 32;
        }
        long j5 = j2 + ((4294967295L & iArr[3]) - 1);
        iArr[3] = (int) j5;
        if ((j5 >> 32) != 0) {
            c6.a(7, iArr, 4);
        }
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = v5.a(bigInteger);
        if (a2[6] == -1) {
            int[] iArr = a;
            if (v5.c(a2, iArr)) {
                v5.e(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(7, iArr, 0, iArr2);
        } else {
            c6.d(7, iArr2, v5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 7; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] b2 = v5.b();
        v5.c(iArr, iArr2, b2);
        reduce(b2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (v5.d(iArr, iArr2, iArr3) != 0 || (iArr3[13] == -1 && c6.d(14, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(14, iArr3, iArr4.length);
            }
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            v5.e(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            v5.e(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[28];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 7);
        } while (c6.f(7, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        long j = iArr[10] & 4294967295L;
        long j2 = iArr[11] & 4294967295L;
        long j3 = iArr[12] & 4294967295L;
        long j4 = iArr[13] & 4294967295L;
        long j5 = ((iArr[7] & 4294967295L) + j2) - 1;
        long j6 = (iArr[8] & 4294967295L) + j3;
        long j7 = (iArr[9] & 4294967295L) + j4;
        long j8 = ((iArr[0] & 4294967295L) - j5) + 0;
        long j9 = j8 & 4294967295L;
        long j10 = (j8 >> 32) + ((iArr[1] & 4294967295L) - j6);
        int i = (int) j10;
        iArr2[1] = i;
        long j11 = (j10 >> 32) + ((iArr[2] & 4294967295L) - j7);
        int i2 = (int) j11;
        iArr2[2] = i2;
        long j12 = (j11 >> 32) + (((iArr[3] & 4294967295L) + j5) - j);
        long j13 = j12 & 4294967295L;
        long j14 = (j12 >> 32) + (((iArr[4] & 4294967295L) + j6) - j2);
        iArr2[4] = (int) j14;
        long j15 = (j14 >> 32) + (((iArr[5] & 4294967295L) + j7) - j3);
        iArr2[5] = (int) j15;
        long j16 = (j15 >> 32) + (((iArr[6] & 4294967295L) + j) - j4);
        iArr2[6] = (int) j16;
        long j17 = (j16 >> 32) + 1;
        long j18 = j13 + j17;
        long j19 = j9 - j17;
        iArr2[0] = (int) j19;
        long j20 = j19 >> 32;
        if (j20 != 0) {
            long j21 = j20 + (i & 4294967295L);
            iArr2[1] = (int) j21;
            long j22 = (j21 >> 32) + (4294967295L & i2);
            iArr2[2] = (int) j22;
            j18 += j22 >> 32;
        }
        iArr2[3] = (int) j18;
        if (((j18 >> 32) == 0 || c6.c(7, iArr2, 4) == 0) && !(iArr2[6] == -1 && v5.c(iArr2, a))) {
            return;
        }
        a(iArr2);
    }

    public static void reduce32(int i, int[] iArr) {
        long j;
        if (i != 0) {
            long j2 = i & 4294967295L;
            long j3 = ((iArr[0] & 4294967295L) - j2) + 0;
            iArr[0] = (int) j3;
            long j4 = j3 >> 32;
            if (j4 != 0) {
                long j5 = j4 + (iArr[1] & 4294967295L);
                iArr[1] = (int) j5;
                long j6 = (j5 >> 32) + (iArr[2] & 4294967295L);
                iArr[2] = (int) j6;
                j4 = j6 >> 32;
            }
            long j7 = j4 + (4294967295L & iArr[3]) + j2;
            iArr[3] = (int) j7;
            j = j7 >> 32;
        } else {
            j = 0;
        }
        if ((j == 0 || c6.c(7, iArr, 4) == 0) && !(iArr[6] == -1 && v5.c(iArr, a))) {
            return;
        }
        a(iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] b2 = v5.b();
        v5.d(iArr, b2);
        reduce(b2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] b2 = v5.b();
        v5.d(iArr, b2);
        reduce(b2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            v5.d(iArr2, b2);
            reduce(b2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (v5.e(iArr, iArr2, iArr3) != 0) {
            b(iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(14, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            if (c6.g(iArr4.length, iArr4, iArr3) != 0) {
                c6.a(14, iArr3, iArr4.length);
            }
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(7, iArr, 0, iArr2) != 0 || (iArr2[6] == -1 && v5.c(iArr2, a))) {
            a(iArr2);
        }
    }
}
