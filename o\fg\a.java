package o.fg;

import o.fc.c;
import o.fc.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fg\a.smali */
public class a extends e {
    private static int a = 0;
    private static int e = 1;
    private byte[] b;

    public a(boolean z, c cVar, short s) {
        super(z, cVar, s);
    }

    public final byte[] f() {
        int i = a;
        int i2 = (i & 77) + (i | 77);
        int i3 = i2 % 128;
        e = i3;
        int i4 = i2 % 2;
        byte[] bArr = this.b;
        int i5 = i3 + 81;
        a = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bArr;
        }
    }

    public final void a(byte[] bArr) {
        int i = e;
        int i2 = (i + 88) - 1;
        a = i2 % 128;
        int i3 = i2 % 2;
        this.b = bArr;
        int i4 = (i + 34) - 1;
        a = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }
}
