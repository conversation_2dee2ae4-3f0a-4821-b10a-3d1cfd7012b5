package o.u;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.cardeventlistener.CardEventListener;
import fr.antelop.sdk.cardeventlistener.DefaultCardEventListener;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\u\e.smali */
public class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static char[] b;
    private static int d;
    private static int f;
    private final CardEventListener c = e();
    private final Context e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        f = 1;
        c();
        ViewConfiguration.getScrollBarFadeDuration();
        AudioTrack.getMinVolume();
        TextUtils.getOffsetBefore("", 0);
        ViewConfiguration.getJumpTapTimeout();
        int i = f + Opcodes.LUSHR;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    static void c() {
        b = new char[]{17047, 30534, 30566, 30561, 30587, 30571, 30588, 30585, 30564, 30591, 30572, 30573, 30560, 30563, 17046, 30538, 30574, 30569, 30589, 17044, 30497, 17041, 30540, 30570, 30531};
        a = (char) 17040;
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 251;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = 1 - r9
            int r7 = r7 + 69
            int r8 = r8 + 4
            byte[] r0 = o.u.e.$$a
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L32
        L16:
            r3 = r2
        L17:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            int r8 = r8 + 1
            if (r4 != r9) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L32:
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.u.e.l(int, int, int, java.lang.Object[]):void");
    }

    e(Context context) {
        this.e = context;
    }

    private CardEventListener e() {
        int i = f + 13;
        d = i % 128;
        int i2 = i % 2;
        try {
            Context context = this.e;
            Object[] objArr = new Object[1];
            k(TextUtils.indexOf((CharSequence) "", '0') + 29, "\u0012\u0013\u0015\u000f\u0004\u0000\u0003\u0012\u000e\u0007\u0015\u0017\u0011\u0013\n\u0014\b\u0016\u0004\u0000\u0016\u0004\t\u0001\u0003\b\u0003\u0017", (byte) (46 - View.MeasureSpec.makeMeasureSpec(0, 0)), objArr);
            String a2 = o.a(context, ((String) objArr[0]).intern());
            Object[] objArr2 = new Object[1];
            k(28 - (ViewConfiguration.getFadingEdgeLength() >> 16), "\u0012\u0013\u0015\u000f\u0004\u0000\u0003\u0012\u000e\u0007\u0015\u0017\u0011\u0013\n\u0014\b\u0016\u0004\u0000\u0016\u0004\t\u0001\u0003\b\u0003\u0017", (byte) (Color.green(0) + 46), objArr2);
            CardEventListener cardEventListener = (CardEventListener) o.e(CardEventListener.class, a2, ((String) objArr2[0]).intern());
            int i3 = f + 85;
            d = i3 % 128;
            int i4 = i3 % 2;
            return cardEventListener;
        } catch (PackageManager.NameNotFoundException | RuntimeException e) {
            g.c();
            Object[] objArr3 = new Object[1];
            k((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 22, "\u0002\u0004\b\u0003\u0011\u0017\u0011\u0013\n\u0014\b\u0016\u0004\u0000\u0015\u0011㙋㙋\u0010\u0015\r\u0005", (byte) (((Process.getThreadPriority(0) + 20) >> 6) + 85), objArr3);
            g.e(((String) objArr3[0]).intern(), e.getMessage());
            return new DefaultCardEventListener();
        }
    }

    private CardEventListener b() {
        int i = f;
        int i2 = i + 73;
        d = i2 % 128;
        int i3 = i2 % 2;
        CardEventListener cardEventListener = this.c;
        int i4 = i + 53;
        d = i4 % 128;
        int i5 = i4 % 2;
        return cardEventListener;
    }

    public final void b(String str, String str2) {
        int i = f + 47;
        d = i % 128;
        int i2 = i % 2;
        b().onCardDeleted(this.e, str, str2);
        int i3 = d + 31;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void a(String str, String str2) {
        int i = d + 51;
        f = i % 128;
        int i2 = i % 2;
        b().onCardLocked(this.e, str, str2);
        int i3 = f + 71;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void d(String str, String str2) {
        int i = d + 43;
        f = i % 128;
        int i2 = i % 2;
        b().onCardActivated(this.e, str, str2);
        int i3 = d + 13;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void e(String str, String str2) {
        int i = d + Opcodes.LSHL;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                b().onCardActivationRequired(this.e, str, str2);
                break;
            default:
                b().onCardActivationRequired(this.e, str, str2);
                int i2 = 63 / 0;
                break;
        }
        int i3 = f + Opcodes.DREM;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void c(String str, String str2) {
        int i = f + 77;
        d = i % 128;
        int i2 = i % 2;
        b().onCardActivating(this.e, str, str2);
        int i3 = f + Opcodes.LMUL;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void g(String str, String str2) {
        int i = d + Opcodes.LUSHR;
        f = i % 128;
        int i2 = i % 2;
        b().onCardPaymentKeysRefreshed(this.e, str, str2);
        int i3 = f + 41;
        d = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                int i4 = 25 / 0;
                return;
            default:
                return;
        }
    }

    public final void f(String str, String str2) {
        int i = f + 1;
        d = i % 128;
        boolean z = i % 2 != 0;
        CardEventListener b2 = b();
        switch (z) {
            case false:
                b2.onCardDisplayUpdated(this.e, str, str2);
                break;
            default:
                b2.onCardDisplayUpdated(this.e, str, str2);
                int i2 = 91 / 0;
                break;
        }
        int i3 = f + Opcodes.DMUL;
        d = i3 % 128;
        switch (i3 % 2 != 0 ? '8' : Typography.dollar) {
            case '8':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void j(String str, String str2) {
        int i = f + Opcodes.LNEG;
        d = i % 128;
        int i2 = i % 2;
        b().onCardRedigitized(this.e, str, str2);
        int i3 = d + 85;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void i(String str, String str2) {
        int i = f + 7;
        d = i % 128;
        int i2 = i % 2;
        b().onCardTermsAndConditionsApprovalRequired(this.e, str, str2);
        int i3 = f + Opcodes.LMUL;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void h(String str, String str2) {
        int i = f + 29;
        d = i % 128;
        switch (i % 2 != 0 ? '\f' : 'C') {
            case '\f':
                b().onCardPaymentInformationUpdated(this.e, str, str2);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                b().onCardPaymentInformationUpdated(this.e, str, str2);
                return;
        }
    }

    public final int hashCode() {
        int i = d + 43;
        f = i % 128;
        switch (i % 2 == 0 ? (char) 1 : '^') {
            case 1:
                super.hashCode();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int hashCode = super.hashCode();
                int i2 = d + 97;
                f = i2 % 128;
                int i3 = i2 % 2;
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = f + 17;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                return super.equals(obj);
            default:
                super.equals(obj);
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    public final String toString() {
        int i = d + 11;
        f = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = d + 95;
        f = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    protected final void finalize() throws Throwable {
        int i = f + Opcodes.DNEG;
        d = i % 128;
        boolean z = i % 2 == 0;
        super.finalize();
        switch (z) {
            case false:
                int i2 = 47 / 0;
                break;
        }
        int i3 = d + 13;
        f = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 50 / 0;
                return;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /* JADX WARN: Code restructure failed: missing block: B:50:0x017a, code lost:
    
        if (r2.e == r2.a) goto L61;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r26, java.lang.String r27, byte r28, java.lang.Object[] r29) {
        /*
            Method dump skipped, instructions count: 1066
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.u.e.k(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
