package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP160R1FieldElement.smali */
public class SecP160R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF"));
    protected int[] a;

    public SecP160R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP160R1FieldElement");
        }
        this.a = SecP160R1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R1Field.add(this.a, ((SecP160R1FieldElement) eCFieldElement).a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = t5.a();
        SecP160R1Field.addOne(this.a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R1Field.inv(((SecP160R1FieldElement) eCFieldElement).a, a);
        SecP160R1Field.multiply(a, this.a, a);
        return new SecP160R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP160R1FieldElement) {
            return t5.a(this.a, ((SecP160R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP160R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 5);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = t5.a();
        SecP160R1Field.inv(this.a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return t5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return t5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R1Field.multiply(this.a, ((SecP160R1FieldElement) eCFieldElement).a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = t5.a();
        SecP160R1Field.negate(this.a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (t5.b(iArr) || t5.a(iArr)) {
            return this;
        }
        int[] a = t5.a();
        SecP160R1Field.square(iArr, a);
        SecP160R1Field.multiply(a, iArr, a);
        int[] a2 = t5.a();
        SecP160R1Field.squareN(a, 2, a2);
        SecP160R1Field.multiply(a2, a, a2);
        SecP160R1Field.squareN(a2, 4, a);
        SecP160R1Field.multiply(a, a2, a);
        SecP160R1Field.squareN(a, 8, a2);
        SecP160R1Field.multiply(a2, a, a2);
        SecP160R1Field.squareN(a2, 16, a);
        SecP160R1Field.multiply(a, a2, a);
        SecP160R1Field.squareN(a, 32, a2);
        SecP160R1Field.multiply(a2, a, a2);
        SecP160R1Field.squareN(a2, 64, a);
        SecP160R1Field.multiply(a, a2, a);
        SecP160R1Field.square(a, a2);
        SecP160R1Field.multiply(a2, iArr, a2);
        SecP160R1Field.squareN(a2, 29, a2);
        SecP160R1Field.square(a2, a);
        if (t5.a(iArr, a)) {
            return new SecP160R1FieldElement(a2);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = t5.a();
        SecP160R1Field.square(this.a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = t5.a();
        SecP160R1Field.subtract(this.a, ((SecP160R1FieldElement) eCFieldElement).a, a);
        return new SecP160R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return t5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return t5.c(this.a);
    }

    public SecP160R1FieldElement() {
        this.a = t5.a();
    }

    protected SecP160R1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
