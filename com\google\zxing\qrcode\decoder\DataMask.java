package com.google.zxing.qrcode.decoder;

import com.google.zxing.common.BitMatrix;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\zxing\qrcode\decoder\DataMask.smali */
abstract class DataMask {
    private static final /* synthetic */ DataMask[] $VALUES;
    public static final DataMask DATA_MASK_000;
    public static final DataMask DATA_MASK_001;
    public static final DataMask DATA_MASK_010;
    public static final DataMask DATA_MASK_011;
    public static final DataMask DATA_MASK_100;
    public static final DataMask DATA_MASK_101;
    public static final DataMask DATA_MASK_110;
    public static final DataMask DATA_MASK_111;

    abstract boolean isMasked(int i, int i2);

    private DataMask(String str, int i) {
    }

    public static DataMask valueOf(String name) {
        return (DataMask) Enum.valueOf(DataMask.class, name);
    }

    public static DataMask[] values() {
        return (DataMask[]) $VALUES.clone();
    }

    static {
        DataMask dataMask = new DataMask("DATA_MASK_000", 0) { // from class: com.google.zxing.qrcode.decoder.DataMask.1
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return ((i + j) & 1) == 0;
            }
        };
        DATA_MASK_000 = dataMask;
        DataMask dataMask2 = new 2("DATA_MASK_001", 1);
        DATA_MASK_001 = dataMask2;
        DataMask dataMask3 = new 3("DATA_MASK_010", 2);
        DATA_MASK_010 = dataMask3;
        DataMask dataMask4 = new DataMask("DATA_MASK_011", 3) { // from class: com.google.zxing.qrcode.decoder.DataMask.4
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return (i + j) % 3 == 0;
            }
        };
        DATA_MASK_011 = dataMask4;
        DataMask dataMask5 = new DataMask("DATA_MASK_100", 4) { // from class: com.google.zxing.qrcode.decoder.DataMask.5
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return (((i / 2) + (j / 3)) & 1) == 0;
            }
        };
        DATA_MASK_100 = dataMask5;
        DataMask dataMask6 = new DataMask("DATA_MASK_101", 5) { // from class: com.google.zxing.qrcode.decoder.DataMask.6
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return (i * j) % 6 == 0;
            }
        };
        DATA_MASK_101 = dataMask6;
        DataMask dataMask7 = new DataMask("DATA_MASK_110", 6) { // from class: com.google.zxing.qrcode.decoder.DataMask.7
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return (i * j) % 6 < 3;
            }
        };
        DATA_MASK_110 = dataMask7;
        DataMask dataMask8 = new DataMask("DATA_MASK_111", 7) { // from class: com.google.zxing.qrcode.decoder.DataMask.8
            @Override // com.google.zxing.qrcode.decoder.DataMask
            boolean isMasked(int i, int j) {
                return (((i + j) + ((i * j) % 3)) & 1) == 0;
            }
        };
        DATA_MASK_111 = dataMask8;
        $VALUES = new DataMask[]{dataMask, dataMask2, dataMask3, dataMask4, dataMask5, dataMask6, dataMask7, dataMask8};
    }

    final void unmaskBitMatrix(BitMatrix bits, int dimension) {
        for (int i = 0; i < dimension; i++) {
            for (int j = 0; j < dimension; j++) {
                if (isMasked(i, j)) {
                    bits.flip(j, i);
                }
            }
        }
    }
}
