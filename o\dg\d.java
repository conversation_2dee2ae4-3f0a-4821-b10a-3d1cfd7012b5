package o.dg;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\d.smali */
public final class d {
    private static int c = 0;
    private static int b = 1;

    /* renamed from: o.dg.d$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\d$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int a;
        private static int d = 1;
        static final /* synthetic */ int[] e;

        static {
            a = 0;
            int[] iArr = new int[o.dc.h.values().length];
            e = iArr;
            try {
                iArr[o.dc.h.b.ordinal()] = 1;
                int i = d;
                int i2 = ((i | 17) << 1) - (i ^ 17);
                a = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.dc.h.c.ordinal()] = 2;
                int i3 = d + 55;
                a = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    public static c d(o.dc.h hVar) {
        int i = b;
        int i2 = (i & 87) + (i | 87);
        c = i2 % 128;
        int i3 = i2 % 2;
        Object obj = null;
        if (hVar == null) {
            h hVar2 = new h();
            int i4 = c + 53;
            b = i4 % 128;
            switch (i4 % 2 == 0 ? 'Z' : '[') {
                case 'Z':
                    obj.hashCode();
                    throw null;
                default:
                    return hVar2;
            }
        }
        switch (AnonymousClass3.e[hVar.ordinal()]) {
            case 1:
                return new b();
            case 2:
                e eVar = new e();
                int i5 = (c + Opcodes.IUSHR) - 1;
                b = i5 % 128;
                switch (i5 % 2 == 0 ? '-' : '?') {
                    case '?':
                        return eVar;
                    default:
                        throw null;
                }
            default:
                throw new IllegalArgumentException();
        }
    }
}
