package o.dq;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.security.MessageDigest;
import kotlin.text.Typography;
import o.dk.b;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dq\e.smali */
public final class e implements d<Drawable> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static int b;
    private static char[] c;
    private static int d;
    public static final e e;

    static void c() {
        c = new char[]{30525, 30521, 30572, 30563, 30556, 30557, 30517, 30566, 30554, 30585, 30584, 30560, 30588, 30578, 30535, 30567, 30587, 30569, 30589, 30561, 30570, 30571, 30511, 30522, 30580, 30537, 30498, 30542, 30529, 30539, 30575, 30562, 30581, 30574, 30573, 30586};
        a = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r7 = r7 * 4
            int r7 = 4 - r7
            byte[] r0 = o.dq.e.$$a
            int r8 = r8 + 69
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L38
        L1b:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1f:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L38:
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dq.e.g(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -116, 4, 37};
        $$b = 210;
    }

    @Override // o.dq.d
    public final /* synthetic */ Drawable a(File file) throws o.du.e {
        int i = b + 79;
        d = i % 128;
        boolean z = i % 2 == 0;
        Drawable b2 = b(file);
        switch (z) {
            case false:
                break;
            default:
                int i2 = 71 / 0;
                break;
        }
        int i3 = b + 61;
        d = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        d = 1;
        c();
        e = new e();
        int i = d + 91;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                int i2 = 62 / 0;
                return;
            default:
                return;
        }
    }

    private static Drawable b(File file) throws o.du.e {
        byte[] bArr;
        MessageDigest messageDigest;
        BufferedInputStream bufferedInputStream;
        try {
            Drawable createFromPath = Drawable.createFromPath(file.getAbsolutePath());
            if (createFromPath != null) {
                int i = b + 27;
                d = i % 128;
                switch (i % 2 == 0 ? Typography.less : '`') {
                    case '<':
                        int i2 = 86 / 0;
                        return createFromPath;
                    default:
                        return createFromPath;
                }
            }
            g.c();
            SystemClock.uptimeMillis();
            ExpandableListView.getPackedPositionForGroup(0);
            BufferedInputStream bufferedInputStream2 = null;
            try {
                bArr = new byte[8192];
                Object[] objArr = new Object[1];
                f(View.MeasureSpec.getSize(0) + 7, "\u0002\u0010\u001c\u001b\u0005\u0012㘥", (byte) (TextUtils.getCapsMode("", 0, 0) + Opcodes.LSHL), objArr);
                messageDigest = MessageDigest.getInstance(((String) objArr[0]).intern());
                bufferedInputStream = new BufferedInputStream(new FileInputStream(file));
            } catch (Throwable th) {
                th = th;
            }
            while (true) {
                try {
                    int read = bufferedInputStream.read(bArr);
                    switch (read <= 0) {
                        case false:
                            messageDigest.update(bArr, 0, read);
                        default:
                            String e2 = b.e(messageDigest.digest());
                            try {
                                bufferedInputStream.close();
                                int i3 = b + Opcodes.DSUB;
                                d = i3 % 128;
                                int i4 = i3 % 2;
                            } catch (Exception e3) {
                            }
                            g.c();
                            Object[] objArr2 = new Object[1];
                            f(35 - (ViewConfiguration.getDoubleTapTimeout() >> 16), "\u0018\u0017\"\t\"#\u0002\u0015\u0002\u0017#\u0007\u000e\u0016\u0002\u0017\u0011\u0006\u001e\u0017\b\u001a\u0005\n\u001e\u0017\b\u001a\u001a\u0017\u0005\b\u0016\u0015㘩", (byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 65), objArr2);
                            String intern = ((String) objArr2[0]).intern();
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr3 = new Object[1];
                            f(Color.rgb(0, 0, 0) + 16777249, "\u0016\u0015\u0005\b\u0016\u0015\u0014\u001c\u0013\u001c\u001f\t\u0002\u0015\u0016\u0017\u0011\n\u0017\u0016\u001a\b\t\u0017\u0015\u0017\r\u000b\u0002\u0015\u0012\n㘋", (byte) (81 - TextUtils.indexOf("", "")), objArr3);
                            StringBuilder append = sb.append(((String) objArr3[0]).intern()).append(file.getAbsolutePath());
                            Object[] objArr4 = new Object[1];
                            f(11 - (ViewConfiguration.getJumpTapTimeout() >> 16), "㘫㘫㘫㘫\r\u0006\u0002\u001a\u0012\n㘫", (byte) (ImageFormat.getBitsPerPixel(0) + 114), objArr4);
                            StringBuilder append2 = append.append(((String) objArr4[0]).intern()).append(file.length());
                            Object[] objArr5 = new Object[1];
                            f(TextUtils.getOffsetAfter("", 0) + 10, "㘀㘀㘀㘀\u0015\u0003\r\u0010\n\u0012", (byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 71), objArr5);
                            g.e(intern, append2.append(((String) objArr5[0]).intern()).append(e2).toString());
                            Object[] objArr6 = new Object[1];
                            f(19 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\u001d\"㘍㘍\u0015\n\u0003\t \u0017\u0017\u0013\u0016\u000e\u001e\u0017\u0014\u0015㘕", (byte) (23 - TextUtils.getTrimmedLength("")), objArr6);
                            throw new o.du.e(((String) objArr6[0]).intern());
                    }
                } catch (Throwable th2) {
                    th = th2;
                    bufferedInputStream2 = bufferedInputStream;
                }
                th = th2;
                bufferedInputStream2 = bufferedInputStream;
                if (bufferedInputStream2 != null) {
                    try {
                        bufferedInputStream2.close();
                    } catch (Exception e4) {
                    }
                }
                throw th;
            }
        } catch (Exception e5) {
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr7 = new Object[1];
            f(38 - TextUtils.indexOf("", "", 0), "\u0007\u0014\"#\u0002\u0015\u001c\u0016\n\u0017\r\u0019\u000f\u0005\"\u000f\u0015\u0017\u0018\u0017\"\t\"#\u0002\u0015\u0017\u0010\u0017\u0006\"\u0013\r\u000b\u0002\u0015\n\u0012", (byte) (120 - ExpandableListView.getPackedPositionGroup(0L)), objArr7);
            throw new o.du.e(sb2.append(((String) objArr7[0]).intern()).append(e5.getMessage()).toString());
        }
    }

    @Override // o.dq.d
    public final Class<Drawable> a() {
        Class<Drawable> cls;
        int i = d + Opcodes.LUSHR;
        int i2 = i % 128;
        b = i2;
        switch (i % 2 == 0) {
            case false:
                cls = Drawable.class;
                int i3 = 70 / 0;
                break;
            default:
                cls = Drawable.class;
                break;
        }
        int i4 = i2 + Opcodes.DREM;
        d = i4 % 128;
        int i5 = i4 % 2;
        return cls;
    }

    public final String toString() {
        Object obj;
        int i = d + 45;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                f((ViewConfiguration.getDoubleTapTimeout() >>> 58) * 46, "\u0018\u0017\"\t\"#\u0002\u0015\u0002\u0017#\u0007\u000e\u0016\u0002\u0017\u0011\u0006\u001e\u0017\b\u001a\u0005\n\u001e\u0017\b\u001a\u001a\u0017\u0005\b\u0016\u0015\u0018\u001e㗭", (byte) (5 << (SystemClock.elapsedRealtime() > 1L ? 1 : (SystemClock.elapsedRealtime() == 1L ? 0 : -1))), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f((ViewConfiguration.getDoubleTapTimeout() >> 16) + 37, "\u0018\u0017\"\t\"#\u0002\u0015\u0002\u0017#\u0007\u000e\u0016\u0002\u0017\u0011\u0006\u001e\u0017\b\u001a\u0005\n\u001e\u0017\b\u001a\u001a\u0017\u0005\b\u0016\u0015\u0018\u001e㗭", (byte) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 5), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Removed duplicated region for block: B:52:0x018c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r26, java.lang.String r27, byte r28, java.lang.Object[] r29) {
        /*
            Method dump skipped, instructions count: 1106
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dq.e.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
