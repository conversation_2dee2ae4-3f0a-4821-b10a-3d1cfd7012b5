package com.google.android.gms.tapandpay.issuer;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\PaymentCredentialsGenerator.smali */
public interface PaymentCredentialsGenerator {
    GetPaymentCredentialsResponse generate(GetPaymentCredentialsRequest getPaymentCredentialsRequest);

    boolean getGoogleOpaquePaymentCardSupported();
}
