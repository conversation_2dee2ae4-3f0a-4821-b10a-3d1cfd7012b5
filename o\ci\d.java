package o.ci;

import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.dk.e;
import o.eg.b;
import o.ei.i;
import o.et.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ci\d.smali */
public final class d extends o.cq.d<a> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static char[] b;
    private static int[] c;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        d();
        KeyEvent.getModifierMetaStateMask();
        Color.alpha(0);
        ViewConfiguration.getKeyRepeatDelay();
        int i = d + 73;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        c = new int[]{586751403, 526525870, -1909279637, -1679697551, 665271886, 1912994548, 1026125168, 272073277, -1762173900, 1220678035, -1666430514, -1761379679, 1682971221, -905187153, 1040067933, -1647823382, 476711394, 1302490749};
        b = new char[]{1720, 6293, 15073, 23594, 32280, 36968, 45970, 54706, 11439, 12935, 4346, 11404, 12938, 4336, 30268, 21508, 47704, 39347, 65431, 56773, 8968, 356, 26445, 18096, 42222, 35579, 59403, 52845, 11349, 11419, 12929, 4336, 30241, 21519, 47692, 39348, 9936, 14560, 6815, 31835, 24179, 45109, 37839, 62951, 55218, 10574, 2856, 27950, 19649, 44686, 32934, 57957, 50189, 9767, 14835, 7046, 11452, 12951, 4338, 30249, 21528, 47735, 39347, 65434, 56787, 9017, 376, 26446, 18106, 42220, 35547, 11437, 12943, 4346, 11513, 11432, 12941, 4323, 57619, 65337, 56644, 11505, 13014, 11504, 13008, 59195, 63770, 33031, 40743, 20525, 19991, 27757, 2729, 10378, 11392, 12928, 4342, 30243, 21524, 47689, 39306, 65425, 56777, 9013, 379, 26486, 18087, 42223, 35545, 59395, 52853, 11345, 13233, 4603, 30508, 21788, 47986, 39600};
        a = -5970970252748049692L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(byte r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ci.d.$$a
            int r6 = 116 - r6
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = -r7
            int r7 = r7 + r8
            int r8 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ci.d.h(byte, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{91, -22, 50, -29};
        $$b = 26;
    }

    @Override // o.cq.d
    public final /* synthetic */ a b(a aVar, b bVar, int i, String str, b bVar2) throws o.eg.d, i {
        int i2 = d + 13;
        e = i2 % 128;
        int i3 = i2 % 2;
        a c2 = c(aVar, bVar, i, str);
        int i4 = d + 5;
        e = i4 % 128;
        int i5 = i4 % 2;
        return c2;
    }

    @Override // o.cq.d
    public final /* synthetic */ a c(String str, String str2, int i, String str3, b bVar) throws o.eg.d, i {
        int i2 = d + 41;
        e = i2 % 128;
        boolean z = i2 % 2 != 0;
        a d2 = d(str, str2, i, str3, bVar);
        switch (z) {
            default:
                int i3 = 39 / 0;
            case false:
                return d2;
        }
    }

    @Override // o.cc.e
    public final /* synthetic */ o.el.d d(String str, String str2, int i, String str3) {
        int i2 = e + Opcodes.DDIV;
        d = i2 % 128;
        char c2 = i2 % 2 == 0 ? ':' : (char) 22;
        a a2 = a(str, str2, i, str3);
        switch (c2) {
            case 22:
                break;
            default:
                int i3 = 73 / 0;
                break;
        }
        int i4 = d + 11;
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                throw null;
            default:
                return a2;
        }
    }

    private static a d(String str, String str2, int i, String str3, b bVar) throws o.eg.d, i {
        Object[] objArr = new Object[1];
        f(new int[]{-303272437, -1182374689, 2140466452, -1682525456, 1042993100, 1503612570}, 11 - TextUtils.getOffsetAfter("", 0), objArr);
        b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g((char) (10785 - Gravity.getAbsoluteGravity(0, 0)), Color.argb(0, 0, 0, 0), 8 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
        b b2 = v.s(((String) objArr2[0]).intern()).b(0);
        a aVar = new a(str, str2, i, str3);
        aVar.b(false);
        Object[] objArr3 = new Object[1];
        f(new int[]{1465400971, -963660508, -1284676195, 470336618}, 8 - Color.argb(0, 0, 0, 0), objArr3);
        aVar.b(e.d(b2.B(((String) objArr3[0]).intern())));
        Object[] objArr4 = new Object[1];
        g((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), ((byte) KeyEvent.getModifierMetaStateMask()) + 9, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 3, objArr4);
        aVar.e(b2.B(((String) objArr4[0]).intern()));
        byte[] e2 = e(aVar.C());
        if (e2 == null) {
            Object[] objArr5 = new Object[1];
            f(new int[]{-1559389068, 666437950, 64379895, -2128084872, 35825098, -1416620572, -1723784237, -1502546525, 178316274, -958164044, -1514365627, -117727877, -451343468, -1719776440, 1253489890, -372627620, -1780258221, -1617103273, -551835691, 1006616213, 1566870070, 792186923}, (ViewConfiguration.getEdgeSlop() >> 16) + 44, objArr5);
            throw new i(((String) objArr5[0]).intern());
        }
        aVar.a(e2);
        aVar.e(e.d(aVar.j(), aVar.B()));
        byte[] C = aVar.C();
        Object[] objArr6 = new Object[1];
        f(new int[]{-1315105344, -1567482967}, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 4, objArr6);
        aVar.d(o.ej.d.b(C, ((String) objArr6[0]).intern()));
        Object[] objArr7 = new Object[1];
        g((char) (KeyEvent.getMaxKeyCode() >> 16), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 12, 18 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr7);
        b v2 = v.v(((String) objArr7[0]).intern());
        Object[] objArr8 = new Object[1];
        f(new int[]{-2136538920, -1194888395, 1601520137, 85865071, -676533372, -2053423253}, (ViewConfiguration.getLongPressTimeout() >> 16) + 12, objArr8);
        b v3 = v2.v(((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        g((char) (ViewConfiguration.getWindowTouchSlop() >> 8), ((byte) KeyEvent.getModifierMetaStateMask()) + 30, 6 - TextUtils.indexOf((CharSequence) "", '0'), objArr9);
        v2.s(((String) objArr9[0]).intern());
        Object[] objArr10 = new Object[1];
        f(new int[]{102419270, -1977622182, -768921920, -1200580418, -926808711, 1893218889, 1660952018, -1608301382}, 16 - (KeyEvent.getMaxKeyCode() >> 16), objArr10);
        byte[] B = v3.B(((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        f(new int[]{1234558357, 1347093411, -1180727204, -984053208, -473638229, -493287949}, Color.argb(0, 0, 0, 0) + 10, objArr11);
        byte[] z = v3.z(((String) objArr11[0]).intern());
        switch (z == null ? (char) 3 : 'W') {
            case Opcodes.POP /* 87 */:
                break;
            default:
                int i2 = e + 47;
                d = i2 % 128;
                if (i2 % 2 == 0) {
                }
                z = new byte[]{-1, -1, -1, -1, -1, -1};
                break;
        }
        Object[] objArr12 = new Object[1];
        g((char) (2684 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 36 - TextUtils.indexOf("", ""), 20 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr12);
        int intValue = v3.e(((String) objArr12[0]).intern(), (Integer) (-1)).intValue();
        Object[] objArr13 = new Object[1];
        g((char) (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getFadingEdgeLength() >> 16) + 56, 14 - Process.getGidForName(""), objArr13);
        int intValue2 = v3.e(((String) objArr13[0]).intern(), (Integer) (-1)).intValue();
        Object[] objArr14 = new Object[1];
        g((char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 71, (ViewConfiguration.getPressedStateDuration() >> 16) + 3, objArr14);
        String r = v3.r(((String) objArr14[0]).intern());
        switch (r.length() % 2 != 0) {
            case false:
                break;
            default:
                int i3 = d + 33;
                e = i3 % 128;
                int i4 = i3 % 2;
                Object[] objArr15 = new Object[1];
                g((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), 74 - KeyEvent.normalizeMetaState(0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr15);
                r = ((String) objArr15[0]).intern().concat(r);
                break;
        }
        byte c2 = o.dk.b.c(r);
        Object[] objArr16 = new Object[1];
        f(new int[]{1280545144, -1654717557, 424817490, 781794645, -228849830, 1783670295, -433996557, 1903514515, -1296575080, 1131990416}, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 16, objArr16);
        b v4 = v3.v(((String) objArr16[0]).intern());
        Object[] objArr17 = new Object[1];
        g((char) TextUtils.getCapsMode("", 0, 0), 75 - Color.green(0), Color.alpha(0) + 3, objArr17);
        byte[] B2 = v4.B(((String) objArr17[0]).intern());
        Object[] objArr18 = new Object[1];
        g((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 52668), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 78, TextUtils.indexOf((CharSequence) "", '0') + 4, objArr18);
        byte[] b3 = b(B2, v4.B(((String) objArr18[0]).intern()));
        Object[] objArr19 = new Object[1];
        f(new int[]{1302345480, -195271790, 1590701644, -1414441316, 944889689, -1334596021, -1943495198, -2002828209, 925199285, -762206821, 455243849, -1328120817, -1960282022, -43555028}, 26 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr19);
        String intern = ((String) objArr19[0]).intern();
        Object[] objArr20 = new Object[1];
        f(new int[]{-1515820602, -538528198}, TextUtils.indexOf("", "") + 2, objArr20);
        int intValue3 = Integer.valueOf(v3.c(intern, ((String) objArr20[0]).intern()), 16).intValue();
        aVar.i(B);
        aVar.h(z);
        aVar.a(intValue);
        aVar.e(intValue2);
        aVar.c(c2);
        aVar.c(b3);
        aVar.d(intValue3);
        return aVar;
    }

    private static a c(a aVar, b bVar, int i, String str) throws o.eg.d, i {
        a aVar2 = new a(a.c(aVar.n(), i), aVar.f(), aVar.i(), str);
        aVar2.b(true);
        Object[] objArr = new Object[1];
        f(new int[]{1465400971, -963660508, -1284676195, 470336618}, 8 - View.resolveSizeAndState(0, 0, 0), objArr);
        aVar2.b(e.d(bVar.B(((String) objArr[0]).intern())));
        Object[] objArr2 = new Object[1];
        g((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), 8 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 3 - KeyEvent.getDeadChar(0, 0), objArr2);
        aVar2.e(bVar.B(((String) objArr2[0]).intern()));
        byte[] e2 = e(aVar2.C());
        if (e2 == null) {
            Object[] objArr3 = new Object[1];
            f(new int[]{-1559389068, 666437950, 64379895, -2128084872, 35825098, -1416620572, -1723784237, -1502546525, 178316274, -958164044, -1514365627, -117727877, -451343468, -1719776440, 1253489890, -372627620, -1780258221, -1617103273, -551835691, 1006616213, 1566870070, 792186923}, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 44, objArr3);
            throw new i(((String) objArr3[0]).intern());
        }
        aVar2.a(e2);
        aVar2.e(e.d(aVar2.j(), aVar2.B()));
        byte[] C = aVar2.C();
        Object[] objArr4 = new Object[1];
        f(new int[]{-1315105344, -1567482967}, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 4, objArr4);
        aVar2.d(o.ej.d.b(C, ((String) objArr4[0]).intern()));
        aVar2.i(aVar.L());
        aVar2.h(aVar.N());
        aVar2.a(aVar.c());
        aVar2.e(aVar.d());
        aVar2.c(aVar.b());
        aVar2.c(aVar.M());
        aVar2.d(aVar.J());
        int i2 = d + 1;
        e = i2 % 128;
        int i3 = i2 % 2;
        return aVar2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0071. Please report as an issue. */
    @Override // o.cq.d
    public final boolean b(int i, b bVar) throws o.eg.d {
        int i2 = d + 81;
        e = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        f(new int[]{-303272437, -1182374689, 2140466452, -1682525456, 1042993100, 1503612570}, Color.green(0) + 11, objArr);
        b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g((char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10785), Gravity.getAbsoluteGravity(0, 0), (ViewConfiguration.getPressedStateDuration() >> 16) + 8, objArr2);
        switch (i + 1 < v.s(((String) objArr2[0]).intern()).d() ? Typography.greater : 'L') {
            case Base64.mimeLineLength /* 76 */:
                return false;
            default:
                int i4 = d + 87;
                e = i4 % 128;
                switch (i4 % 2 != 0 ? '*' : 'H') {
                }
                return true;
        }
    }

    @Override // o.cq.d
    public final b c(int i, b bVar) throws o.eg.d {
        int i2 = e + 51;
        d = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        f(new int[]{-303272437, -1182374689, 2140466452, -1682525456, 1042993100, 1503612570}, 11 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
        b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g((char) (10785 - ((Process.getThreadPriority(0) + 20) >> 6)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 9, objArr2);
        b b2 = v.s(((String) objArr2[0]).intern()).b(i);
        int i4 = e + Opcodes.DMUL;
        d = i4 % 128;
        switch (i4 % 2 == 0 ? 'B' : (char) 16) {
            case 16:
                return b2;
            default:
                throw null;
        }
    }

    @Override // o.cq.d
    public final o.eg.e b(b bVar) throws o.eg.d {
        int i = e + 63;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(new int[]{-303272437, -1182374689, 2140466452, -1682525456, 1042993100, 1503612570}, 10 - ExpandableListView.getPackedPositionChild(0L), objArr);
        b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 11, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 17, objArr2);
        b v2 = v.v(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 30 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 7, objArr3);
        o.eg.e s = v2.s(((String) objArr3[0]).intern());
        int i3 = d + 53;
        e = i3 % 128;
        int i4 = i3 % 2;
        return s;
    }

    @Override // o.cq.d
    public final boolean a() {
        int i = e;
        int i2 = i + 81;
        d = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + Opcodes.LSUB;
        d = i4 % 128;
        int i5 = i4 % 2;
        return false;
    }

    private static byte[] b(byte[] bArr, byte[] bArr2) {
        int i = e + 71;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) (ViewConfiguration.getTouchSlop() >> 8), 81 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 2 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr);
        byte[] e2 = o.ej.d.e(((String) objArr[0]).intern(), bArr);
        Object[] objArr2 = new Object[1];
        g((char) Color.argb(0, 0, 0, 0), View.resolveSize(0, 0) + 83, TextUtils.indexOf("", "", 0, 0) + 2, objArr2);
        byte[] e3 = o.ej.d.e(((String) objArr2[0]).intern(), bArr2);
        Object[] objArr3 = new Object[1];
        f(new int[]{1874801558, 1629914256}, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 3, objArr3);
        byte[] e4 = o.ej.d.e(((String) objArr3[0]).intern(), e2, e3);
        int i3 = d + 99;
        e = i3 % 128;
        int i4 = i3 % 2;
        return e4;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static byte[] e(byte[] r9) {
        /*
            int r0 = o.ci.d.d
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.ci.d.e = r1
            r1 = 2
            int r0 = r0 % r1
            java.util.ArrayList r9 = o.ej.d.c(r9, r1)
            java.util.Iterator r9 = r9.iterator()
        L12:
            boolean r0 = r9.hasNext()
            if (r0 == 0) goto L1a
            r0 = r1
            goto L1c
        L1a:
            r0 = 78
        L1c:
            switch(r0) {
                case 2: goto L20;
                default: goto L1f;
            }
        L1f:
            goto L7b
        L20:
            java.lang.Object r0 = r9.next()
            o.ej.d r0 = (o.ej.d) r0
            java.lang.String r2 = r0.a()
            java.lang.String r3 = ""
            r4 = 0
            int r3 = android.text.TextUtils.getCapsMode(r3, r4, r4)
            r5 = 52170(0xcbca, float:7.3106E-41)
            int r3 = r3 + r5
            char r3 = (char) r3
            int r5 = android.os.Process.getThreadPriority(r4)
            int r5 = r5 + 20
            int r5 = r5 >> 6
            int r5 = 85 - r5
            int r6 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r6 = r6 >> 16
            int r6 = r6 + r1
            r7 = 1
            java.lang.Object[] r8 = new java.lang.Object[r7]
            g(r3, r5, r6, r8)
            r3 = r8[r4]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            boolean r2 = r2.equals(r3)
            if (r2 == 0) goto L5d
            r4 = r7
            goto L5e
        L5d:
        L5e:
            switch(r4) {
                case 0: goto L66;
                default: goto L61;
            }
        L61:
            byte[] r9 = r0.e()
            return r9
        L66:
            int r0 = o.ci.d.e
            int r0 = r0 + 29
            int r2 = r0 % 128
            o.ci.d.d = r2
            int r0 = r0 % r1
            if (r0 != 0) goto L75
            r0 = 89
            goto L77
        L75:
            r0 = 66
        L77:
            switch(r0) {
                case 89: goto L7a;
                default: goto L7a;
            }
        L7a:
            goto L12
        L7b:
            int r9 = o.ci.d.e
            int r9 = r9 + 49
            int r0 = r9 % 128
            o.ci.d.d = r0
            int r9 = r9 % r1
            r9 = 0
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ci.d.e(byte[]):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.cq.d
    public final java.util.List<o.cc.a> b(o.eg.e r12) throws o.ei.i, o.eg.d {
        /*
            r11 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            o.di.a r1 = new o.di.a
            r1.<init>()
            r1 = 0
            r2 = r1
        Ld:
            int r3 = r12.d()
            if (r2 >= r3) goto L16
            r3 = 12
            goto L17
        L16:
            r3 = 7
        L17:
            r4 = 1
            r5 = 2
            switch(r3) {
                case 12: goto L1e;
                default: goto L1c;
            }
        L1c:
            goto Ld1
        L1e:
            int r3 = o.ci.d.e
            int r3 = r3 + 55
            int r6 = r3 % 128
            o.ci.d.d = r6
            int r3 = r3 % r5
            o.eg.b r3 = r12.b(r2)
            r6 = 44455(0xada7, float:6.2295E-41)
            java.lang.String r7 = ""
            int r7 = android.text.TextUtils.indexOf(r7, r7, r1, r1)
            int r7 = r7 + r6
            char r6 = (char) r7
            long r7 = android.os.Process.getElapsedCpuTime()
            r9 = 0
            int r7 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            int r7 = 88 - r7
            int r8 = android.view.ViewConfiguration.getMaximumDrawingCacheSize()
            int r8 = r8 >> 24
            int r8 = 2 - r8
            java.lang.Object[] r9 = new java.lang.Object[r4]
            g(r6, r7, r8, r9)
            r6 = r9[r1]
            java.lang.String r6 = (java.lang.String) r6
            java.lang.String r6 = r6.intern()
            java.lang.String r6 = r3.r(r6)
            int r7 = r6.length()
            r8 = 4
            if (r7 != r8) goto Laf
            java.lang.String r7 = r6.substring(r1, r5)
            byte r7 = o.dk.b.c(r7)
            java.lang.String r5 = r6.substring(r5, r8)
            byte r5 = o.dk.b.c(r5)
            int r6 = android.view.ViewConfiguration.getEdgeSlop()
            int r6 = r6 >> 16
            int r6 = 31890 - r6
            char r6 = (char) r6
            int r8 = android.view.ViewConfiguration.getTapTimeout()
            int r8 = r8 >> 16
            int r8 = r8 + 89
            r9 = 0
            float r10 = android.util.TypedValue.complexToFraction(r1, r9, r9)
            int r9 = (r10 > r9 ? 1 : (r10 == r9 ? 0 : -1))
            int r9 = 5 - r9
            java.lang.Object[] r4 = new java.lang.Object[r4]
            g(r6, r8, r9, r4)
            r4 = r4[r1]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            byte[] r3 = r3.B(r4)
            byte[] r4 = o.di.a.e()
            byte[] r3 = o.ej.d.e(r3, r4)
            o.cc.a r4 = new o.cc.a
            r4.<init>(r7, r5, r3)
            r0.add(r4)
            int r2 = r2 + 1
            goto Ld
        Laf:
            o.ei.i r12 = new o.ei.i
            r0 = 20
            int[] r0 = new int[r0]
            r0 = {x00f2: FILL_ARRAY_DATA , data: [1012959540, 1740736373, 1070356976, -947020587, -1370229905, -1734233071, 1074069882, 1495138678, 1615743101, -1322820684, -270667064, -612804828, 345244347, 1962905829, 392767742, -1769803773, -1593631687, 2004751849, 1159441601, 635722932} // fill-array
            int r2 = android.view.ViewConfiguration.getFadingEdgeLength()
            int r2 = r2 >> 16
            int r2 = r2 + 38
            java.lang.Object[] r3 = new java.lang.Object[r4]
            f(r0, r2, r3)
            r0 = r3[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r12.<init>(r0)
            throw r12
        Ld1:
            int r12 = o.ci.d.d
            int r12 = r12 + 101
            int r2 = r12 % 128
            o.ci.d.e = r2
            int r12 = r12 % r5
            if (r12 == 0) goto Ldd
            r1 = r4
        Ldd:
            switch(r1) {
                case 0: goto Le1;
                default: goto Le0;
            }
        Le0:
            goto Le2
        Le1:
            return r0
        Le2:
            r12 = 0
            throw r12     // Catch: java.lang.Throwable -> Le4
        Le4:
            r12 = move-exception
            throw r12
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ci.d.b(o.eg.e):java.util.List");
    }

    private static a a(String str, String str2, int i, String str3) {
        a aVar = new a(str, str2, i, str3);
        int i2 = d + 19;
        e = i2 % 128;
        int i3 = i2 % 2;
        return aVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r25, int r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 864
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ci.d.f(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 596
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ci.d.g(char, int, int, java.lang.Object[]):void");
    }
}
