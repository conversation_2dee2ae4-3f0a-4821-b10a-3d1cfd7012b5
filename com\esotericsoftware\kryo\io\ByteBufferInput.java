package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.KryoException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\ByteBufferInput.smali */
public class ByteBufferInput extends Input {
    private static final ByteOrder nativeOrder = ByteOrder.nativeOrder();
    protected ByteBuffer byteBuffer;
    private byte[] tempBuffer;

    public ByteBufferInput() {
    }

    public ByteBufferInput(int bufferSize) {
        this.capacity = bufferSize;
        this.byteBuffer = ByteBuffer.allocateDirect(bufferSize);
    }

    public ByteBufferInput(byte[] bytes) {
        this(bytes, 0, bytes.length);
    }

    public ByteBufferInput(byte[] bytes, int offset, int count) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        ByteBuffer buffer = ByteBuffer.allocateDirect(bytes.length);
        buffer.put(bytes);
        flipBuffer(buffer);
        setBuffer(buffer);
    }

    public ByteBufferInput(ByteBuffer buffer) {
        setBuffer(buffer);
    }

    public ByteBufferInput(InputStream inputStream) {
        this(4096);
        if (inputStream == null) {
            throw new IllegalArgumentException("inputStream cannot be null.");
        }
        this.inputStream = inputStream;
    }

    public ByteBufferInput(InputStream inputStream, int bufferSize) {
        this(bufferSize);
        if (inputStream == null) {
            throw new IllegalArgumentException("inputStream cannot be null.");
        }
        this.inputStream = inputStream;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public byte[] getBuffer() {
        throw new UnsupportedOperationException("This input does not used a byte[], see #getByteBuffer().");
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setBuffer(byte[] bytes) {
        throw new UnsupportedOperationException("This input does not used a byte[], see #setByteBuffer(ByteBuffer).");
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setBuffer(byte[] bytes, int offset, int count) {
        throw new UnsupportedOperationException("This input does not used a byte[], see #setByteBufferByteBuffer().");
    }

    public void setBuffer(ByteBuffer buffer) {
        if (buffer == null) {
            throw new IllegalArgumentException("buffer cannot be null.");
        }
        this.byteBuffer = buffer;
        this.position = buffer.position();
        this.limit = buffer.limit();
        this.capacity = buffer.capacity();
        this.total = 0L;
        this.inputStream = null;
    }

    public ByteBuffer getByteBuffer() {
        return this.byteBuffer;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
        this.limit = 0;
        reset();
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream, com.esotericsoftware.kryo.util.Pool.Poolable
    public void reset() {
        super.reset();
        setBufferPosition(this.byteBuffer, 0);
    }

    protected int fill(ByteBuffer buffer, int offset, int count) throws KryoException {
        if (this.inputStream == null) {
            return -1;
        }
        try {
            if (this.tempBuffer == null) {
                this.tempBuffer = new byte[2048];
            }
            setBufferPosition(buffer, offset);
            int total = 0;
            while (true) {
                if (count <= 0) {
                    break;
                }
                InputStream inputStream = this.inputStream;
                byte[] bArr = this.tempBuffer;
                int read = inputStream.read(bArr, 0, Math.min(bArr.length, count));
                if (read == -1) {
                    if (total == 0) {
                        return -1;
                    }
                } else {
                    buffer.put(this.tempBuffer, 0, read);
                    count -= read;
                    total += read;
                }
            }
            return total;
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }

    @Override // com.esotericsoftware.kryo.io.Input
    protected int require(int required) throws KryoException {
        int remaining = this.limit - this.position;
        if (remaining >= required) {
            return remaining;
        }
        if (required > this.capacity) {
            throw new KryoException("Buffer too small: capacity: " + this.capacity + ", required: " + required);
        }
        if (remaining > 0) {
            int count = fill(this.byteBuffer, this.limit, this.capacity - this.limit);
            if (count == -1) {
                throw new KryoBufferUnderflowException("Buffer underflow.");
            }
            setBufferPosition(this.byteBuffer, this.position);
            remaining += count;
            if (remaining >= required) {
                this.limit += count;
                return remaining;
            }
        }
        this.byteBuffer.compact();
        this.total += this.position;
        this.position = 0;
        while (true) {
            int count2 = fill(this.byteBuffer, remaining, this.capacity - remaining);
            if (count2 == -1) {
                if (remaining < required) {
                    throw new KryoBufferUnderflowException("Buffer underflow.");
                }
            } else {
                remaining += count2;
                if (remaining >= required) {
                    break;
                }
            }
        }
        this.limit = remaining;
        setBufferPosition(this.byteBuffer, 0);
        return remaining;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    protected int optional(int optional) throws KryoException {
        int remaining = this.limit - this.position;
        if (remaining >= optional) {
            return optional;
        }
        int optional2 = Math.min(optional, this.capacity);
        int count = fill(this.byteBuffer, this.limit, this.capacity - this.limit);
        setBufferPosition(this.byteBuffer, this.position);
        if (count == -1) {
            if (remaining == 0) {
                return -1;
            }
            return Math.min(remaining, optional2);
        }
        int remaining2 = remaining + count;
        if (remaining2 >= optional2) {
            this.limit += count;
            return optional2;
        }
        this.byteBuffer.compact();
        this.total += this.position;
        this.position = 0;
        do {
            int count2 = fill(this.byteBuffer, remaining2, this.capacity - remaining2);
            if (count2 == -1) {
                break;
            }
            remaining2 += count2;
        } while (remaining2 < optional2);
        this.limit = remaining2;
        setBufferPosition(this.byteBuffer, 0);
        if (remaining2 == 0) {
            return -1;
        }
        return Math.min(remaining2, optional2);
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public int read() throws KryoException {
        if (optional(1) <= 0) {
            return -1;
        }
        this.position++;
        return this.byteBuffer.get() & 255;
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public int read(byte[] bytes) throws KryoException {
        return read(bytes, 0, bytes.length);
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public int read(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            this.byteBuffer.get(bytes, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count == 0) {
                break;
            }
            offset += copyCount;
            copyCount = optional(count);
            if (copyCount == -1) {
                if (count == count) {
                    return -1;
                }
            } else if (this.position == this.limit) {
                break;
            }
        }
        return count - count;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setPosition(int position) {
        this.position = position;
        setBufferPosition(this.byteBuffer, position);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void setLimit(int limit) {
        this.limit = limit;
        setBufferLimit(this.byteBuffer, limit);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void skip(int count) throws KryoException {
        super.skip(count);
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public long skip(long count) throws KryoException {
        long remaining = count;
        while (remaining > 0) {
            int skip = (int) Math.min(2147483639L, remaining);
            skip(skip);
            remaining -= skip;
        }
        return count;
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws KryoException {
        if (this.inputStream != null) {
            try {
                this.inputStream.close();
            } catch (IOException e) {
            }
        }
    }

    private int getBufferPosition(Buffer buffer) {
        return buffer.position();
    }

    private void setBufferPosition(Buffer buffer, int position) {
        buffer.position(position);
    }

    private void setBufferLimit(Buffer buffer, int limit) {
        buffer.limit(limit);
    }

    private void flipBuffer(Buffer buffer) {
        buffer.flip();
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public byte readByte() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        this.position++;
        return this.byteBuffer.get();
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readByteUnsigned() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        this.position++;
        return this.byteBuffer.get() & 255;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public byte[] readBytes(int length) throws KryoException {
        byte[] bytes = new byte[length];
        readBytes(bytes, 0, length);
        return bytes;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void readBytes(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            this.byteBuffer.get(bytes, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(count, this.capacity);
                require(copyCount);
            } else {
                return;
            }
        }
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readInt() throws KryoException {
        require(4);
        this.position += 4;
        ByteBuffer byteBuffer = this.byteBuffer;
        return (byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readVarInt(boolean optimizePositive) throws KryoException {
        if (require(1) < 5) {
            return readVarInt_slow(optimizePositive);
        }
        int b = this.byteBuffer.get();
        int result = b & 127;
        if ((b & 128) != 0) {
            ByteBuffer byteBuffer = this.byteBuffer;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        result |= (byteBuffer.get() & 127) << 28;
                    }
                }
            }
        }
        this.position = getBufferPosition(this.byteBuffer);
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    private int readVarInt_slow(boolean optimizePositive) {
        this.position++;
        int b = this.byteBuffer.get();
        int result = b & 127;
        if ((b & 128) != 0) {
            if (this.position == this.limit) {
                require(1);
            }
            ByteBuffer byteBuffer = this.byteBuffer;
            this.position++;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                if (this.position == this.limit) {
                    require(1);
                }
                this.position++;
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    if (this.position == this.limit) {
                        require(1);
                    }
                    this.position++;
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        if (this.position == this.limit) {
                            require(1);
                        }
                        this.position++;
                        result |= (byteBuffer.get() & 127) << 28;
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean canReadVarInt() throws KryoException {
        if (this.limit - this.position >= 5) {
            return true;
        }
        if (optional(5) <= 0) {
            return false;
        }
        int p = this.position;
        int limit = this.limit;
        ByteBuffer byteBuffer = this.byteBuffer;
        int p2 = p + 1;
        if ((byteBuffer.get(p) & 128) == 0) {
            return true;
        }
        if (p2 == limit) {
            return false;
        }
        int p3 = p2 + 1;
        if ((byteBuffer.get(p2) & 128) == 0) {
            return true;
        }
        if (p3 == limit) {
            return false;
        }
        int p4 = p3 + 1;
        if ((byteBuffer.get(p3) & 128) == 0) {
            return true;
        }
        if (p4 == limit) {
            return false;
        }
        return (byteBuffer.get(p4) & 128) == 0 || p4 + 1 != limit;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean readVarIntFlag() {
        if (this.position == this.limit) {
            require(1);
        }
        return (this.byteBuffer.get(this.position) & ByteCompanionObject.MIN_VALUE) != 0;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readVarIntFlag(boolean optimizePositive) {
        if (require(1) < 5) {
            return readVarIntFlag_slow(optimizePositive);
        }
        int b = this.byteBuffer.get();
        int result = b & 63;
        if ((b & 64) != 0) {
            ByteBuffer byteBuffer = this.byteBuffer;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 6;
            if ((b2 & 128) != 0) {
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 13;
                if ((b3 & 128) != 0) {
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 20;
                    if ((b4 & 128) != 0) {
                        result |= (byteBuffer.get() & 127) << 27;
                    }
                }
            }
        }
        this.position = getBufferPosition(this.byteBuffer);
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    private int readVarIntFlag_slow(boolean optimizePositive) {
        this.position++;
        int b = this.byteBuffer.get();
        int result = b & 63;
        if ((b & 64) != 0) {
            if (this.position == this.limit) {
                require(1);
            }
            this.position++;
            ByteBuffer byteBuffer = this.byteBuffer;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 6;
            if ((b2 & 128) != 0) {
                if (this.position == this.limit) {
                    require(1);
                }
                this.position++;
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 13;
                if ((b3 & 128) != 0) {
                    if (this.position == this.limit) {
                        require(1);
                    }
                    this.position++;
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 20;
                    if ((b4 & 128) != 0) {
                        if (this.position == this.limit) {
                            require(1);
                        }
                        this.position++;
                        result |= (byteBuffer.get() & 127) << 27;
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public long readLong() throws KryoException {
        require(8);
        this.position += 8;
        ByteBuffer byteBuffer = this.byteBuffer;
        return ((byteBuffer.get() & 255) << 8) | (byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24) | ((byteBuffer.get() & 255) << 32) | ((byteBuffer.get() & 255) << 40) | ((byteBuffer.get() & 255) << 48) | (byteBuffer.get() << 56);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public long readVarLong(boolean optimizePositive) throws KryoException {
        if (require(1) < 9) {
            return readVarLong_slow(optimizePositive);
        }
        int b = this.byteBuffer.get();
        long result = b & 127;
        if ((b & 128) != 0) {
            ByteBuffer byteBuffer = this.byteBuffer;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        int b5 = byteBuffer.get();
                        result |= (b5 & 127) << 28;
                        if ((b5 & 128) != 0) {
                            int b6 = byteBuffer.get();
                            result |= (b6 & 127) << 35;
                            if ((b6 & 128) != 0) {
                                int b7 = byteBuffer.get();
                                result |= (b7 & 127) << 42;
                                if ((b7 & 128) != 0) {
                                    int b8 = byteBuffer.get();
                                    result |= (b8 & 127) << 49;
                                    if ((b8 & 128) != 0) {
                                        result |= byteBuffer.get() << 56;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        this.position = getBufferPosition(this.byteBuffer);
        return optimizePositive ? result : (result >>> 1) ^ (-(1 & result));
    }

    private long readVarLong_slow(boolean optimizePositive) {
        this.position++;
        int b = this.byteBuffer.get();
        long result = b & 127;
        if ((b & 128) != 0) {
            if (this.position == this.limit) {
                require(1);
            }
            ByteBuffer byteBuffer = this.byteBuffer;
            this.position++;
            int b2 = byteBuffer.get();
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                if (this.position == this.limit) {
                    require(1);
                }
                this.position++;
                int b3 = byteBuffer.get();
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    if (this.position == this.limit) {
                        require(1);
                    }
                    this.position++;
                    int b4 = byteBuffer.get();
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        if (this.position == this.limit) {
                            require(1);
                        }
                        this.position++;
                        int b5 = byteBuffer.get();
                        result |= (b5 & 127) << 28;
                        if ((b5 & 128) != 0) {
                            if (this.position == this.limit) {
                                require(1);
                            }
                            this.position++;
                            int b6 = byteBuffer.get();
                            result |= (b6 & 127) << 35;
                            if ((b6 & 128) != 0) {
                                if (this.position == this.limit) {
                                    require(1);
                                }
                                this.position++;
                                int b7 = byteBuffer.get();
                                result |= (b7 & 127) << 42;
                                if ((b7 & 128) != 0) {
                                    if (this.position == this.limit) {
                                        require(1);
                                    }
                                    this.position++;
                                    int b8 = byteBuffer.get();
                                    result |= (b8 & 127) << 49;
                                    if ((b8 & 128) != 0) {
                                        if (this.position == this.limit) {
                                            require(1);
                                        }
                                        this.position++;
                                        result |= byteBuffer.get() << 56;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(1 & result));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean canReadVarLong() throws KryoException {
        if (this.limit - this.position >= 9) {
            return true;
        }
        if (optional(5) <= 0) {
            return false;
        }
        int p = this.position;
        int limit = this.limit;
        ByteBuffer byteBuffer = this.byteBuffer;
        int p2 = p + 1;
        if ((byteBuffer.get(p) & 128) == 0) {
            return true;
        }
        if (p2 == limit) {
            return false;
        }
        int p3 = p2 + 1;
        if ((byteBuffer.get(p2) & 128) == 0) {
            return true;
        }
        if (p3 == limit) {
            return false;
        }
        int p4 = p3 + 1;
        if ((byteBuffer.get(p3) & 128) == 0) {
            return true;
        }
        if (p4 == limit) {
            return false;
        }
        int p5 = p4 + 1;
        if ((byteBuffer.get(p4) & 128) == 0) {
            return true;
        }
        if (p5 == limit) {
            return false;
        }
        int p6 = p5 + 1;
        if ((byteBuffer.get(p5) & 128) == 0) {
            return true;
        }
        if (p6 == limit) {
            return false;
        }
        int p7 = p6 + 1;
        if ((byteBuffer.get(p6) & 128) == 0) {
            return true;
        }
        if (p7 == limit) {
            return false;
        }
        int p8 = p7 + 1;
        if ((byteBuffer.get(p7) & 128) == 0) {
            return true;
        }
        if (p8 == limit) {
            return false;
        }
        return (byteBuffer.get(p8) & 128) == 0 || p8 + 1 != limit;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public float readFloat() throws KryoException {
        require(4);
        ByteBuffer byteBuffer = this.byteBuffer;
        int p = this.position;
        this.position = p + 4;
        return Float.intBitsToFloat((byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public double readDouble() throws KryoException {
        require(8);
        ByteBuffer byteBuffer = this.byteBuffer;
        int p = this.position;
        this.position = p + 8;
        return Double.longBitsToDouble(((byteBuffer.get() & 255) << 8) | (byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24) | ((byteBuffer.get() & 255) << 32) | ((byteBuffer.get() & 255) << 40) | ((byteBuffer.get() & 255) << 48) | (byteBuffer.get() << 56));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean readBoolean() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        this.position++;
        return this.byteBuffer.get() == 1;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public short readShort() throws KryoException {
        require(2);
        this.position += 2;
        return (short) ((this.byteBuffer.get() & 255) | ((this.byteBuffer.get() & 255) << 8));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readShortUnsigned() throws KryoException {
        require(2);
        this.position += 2;
        return (this.byteBuffer.get() & 255) | ((this.byteBuffer.get() & 255) << 8);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public char readChar() throws KryoException {
        require(2);
        this.position += 2;
        return (char) ((this.byteBuffer.get() & 255) | ((this.byteBuffer.get() & 255) << 8));
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public String readString() {
        if (!readVarIntFlag()) {
            return readAsciiString();
        }
        int charCount = readVarIntFlag(true);
        switch (charCount) {
            case 0:
                return null;
            case 1:
                return "";
            default:
                int charCount2 = charCount - 1;
                readUtf8Chars(charCount2);
                return new String(this.chars, 0, charCount2);
        }
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public StringBuilder readStringBuilder() {
        if (!readVarIntFlag()) {
            return new StringBuilder(readAsciiString());
        }
        int charCount = readVarIntFlag(true);
        switch (charCount) {
            case 0:
                return null;
            case 1:
                return new StringBuilder("");
            default:
                int charCount2 = charCount - 1;
                readUtf8Chars(charCount2);
                StringBuilder builder = new StringBuilder(charCount2);
                builder.append(this.chars, 0, charCount2);
                return builder;
        }
    }

    private void readUtf8Chars(int charCount) {
        if (this.chars.length < charCount) {
            this.chars = new char[charCount];
        }
        char[] chars = this.chars;
        ByteBuffer byteBuffer = this.byteBuffer;
        int charIndex = 0;
        int count = Math.min(require(1), charCount);
        while (charIndex < count) {
            int b = byteBuffer.get();
            if (b < 0) {
                break;
            }
            chars[charIndex] = (char) b;
            charIndex++;
        }
        this.position += charIndex;
        if (charIndex < charCount) {
            setBufferPosition(byteBuffer, this.position);
            readUtf8Chars_slow(charCount, charIndex);
        }
    }

    private void readUtf8Chars_slow(int charCount, int charIndex) {
        ByteBuffer byteBuffer = this.byteBuffer;
        char[] chars = this.chars;
        while (charIndex < charCount) {
            if (this.position == this.limit) {
                require(1);
            }
            this.position++;
            int b = byteBuffer.get() & 255;
            switch (b >> 4) {
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                    chars[charIndex] = (char) b;
                    break;
                case 12:
                case 13:
                    int b2 = this.position;
                    if (b2 == this.limit) {
                        require(1);
                    }
                    this.position++;
                    chars[charIndex] = (char) (((b & 31) << 6) | (byteBuffer.get() & 63));
                    break;
                case 14:
                    require(2);
                    this.position += 2;
                    int b22 = byteBuffer.get();
                    int b3 = byteBuffer.get();
                    chars[charIndex] = (char) (((b & 15) << 12) | ((b22 & 63) << 6) | (b3 & 63));
                    break;
            }
            charIndex++;
        }
    }

    private String readAsciiString() {
        char[] chars = this.chars;
        ByteBuffer byteBuffer = this.byteBuffer;
        int charCount = 0;
        int n = Math.min(chars.length, this.limit - this.position);
        while (charCount < n) {
            int b = byteBuffer.get();
            if ((b & 128) == 128) {
                this.position = getBufferPosition(byteBuffer);
                chars[charCount] = (char) (b & 127);
                return new String(chars, 0, charCount + 1);
            }
            chars[charCount] = (char) b;
            charCount++;
        }
        int n2 = getBufferPosition(byteBuffer);
        this.position = n2;
        return readAscii_slow(charCount);
    }

    private String readAscii_slow(int charCount) {
        char[] chars = this.chars;
        ByteBuffer byteBuffer = this.byteBuffer;
        while (true) {
            if (this.position == this.limit) {
                require(1);
            }
            this.position++;
            int b = byteBuffer.get();
            if (charCount == chars.length) {
                char[] newChars = new char[charCount * 2];
                System.arraycopy(chars, 0, newChars, 0, charCount);
                chars = newChars;
                this.chars = newChars;
            }
            if ((b & 128) == 128) {
                chars[charCount] = (char) (b & 127);
                return new String(chars, 0, charCount + 1);
            }
            chars[charCount] = (char) b;
            charCount++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int[] readInts(int length) throws KryoException {
        int[] array = new int[length];
        if (optional(length << 2) == (length << 2)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = (byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24);
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readInt();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public long[] readLongs(int length) throws KryoException {
        long[] array = new long[length];
        if (optional(length << 3) == (length << 3)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = (byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24) | ((byteBuffer.get() & 255) << 32) | ((byteBuffer.get() & 255) << 40) | ((byteBuffer.get() & 255) << 48) | (byteBuffer.get() << 56);
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readLong();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public float[] readFloats(int length) throws KryoException {
        float[] array = new float[length];
        if (optional(length << 2) == (length << 2)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = Float.intBitsToFloat((byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24));
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readFloat();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public double[] readDoubles(int length) throws KryoException {
        double[] array = new double[length];
        if (optional(length << 3) == (length << 3)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = Double.longBitsToDouble((byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8) | ((byteBuffer.get() & 255) << 16) | ((byteBuffer.get() & 255) << 24) | ((byteBuffer.get() & 255) << 32) | ((byteBuffer.get() & 255) << 40) | ((byteBuffer.get() & 255) << 48) | (byteBuffer.get() << 56));
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readDouble();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public short[] readShorts(int length) throws KryoException {
        short[] array = new short[length];
        if (optional(length << 1) == (length << 1)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = (short) ((byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8));
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readShort();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public char[] readChars(int length) throws KryoException {
        char[] array = new char[length];
        if (optional(length << 1) == (length << 1)) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = (char) ((byteBuffer.get() & 255) | ((byteBuffer.get() & 255) << 8));
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readChar();
            }
        }
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean[] readBooleans(int length) throws KryoException {
        boolean[] array = new boolean[length];
        if (optional(length) == length) {
            ByteBuffer byteBuffer = this.byteBuffer;
            for (int i = 0; i < length; i++) {
                array[i] = byteBuffer.get() != 0;
            }
            int i2 = getBufferPosition(byteBuffer);
            this.position = i2;
        } else {
            for (int i3 = 0; i3 < length; i3++) {
                array[i3] = readBoolean();
            }
        }
        return array;
    }
}
