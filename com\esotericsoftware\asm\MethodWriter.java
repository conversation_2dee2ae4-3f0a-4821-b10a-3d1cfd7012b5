package com.esotericsoftware.asm;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\asm\MethodWriter.smali */
class MethodWriter extends MethodVisitor {
    private ByteVector $;
    private int A;
    private Handler B;
    private Handler C;
    private int D;
    private ByteVector E;
    private int F;
    private ByteVector G;
    private int H;
    private ByteVector I;
    private Attribute J;
    private boolean K;
    private int L;
    private final int M;
    private Label N;
    private Label O;
    private Label P;
    private int Q;
    private int R;
    private int S;
    private int T;
    private AnnotationWriter U;
    private AnnotationWriter V;
    private AnnotationWriter W;
    private AnnotationWriter X;
    private int Y;
    private int Z;
    final ClassWriter b;
    private int c;
    private final int d;
    private final int e;
    private final String f;
    String g;
    int h;
    int i;
    int j;
    int[] k;
    private ByteVector l;
    private AnnotationWriter m;
    private AnnotationWriter n;

    /* renamed from: o, reason: collision with root package name */
    private AnnotationWriter[] f13o;
    private AnnotationWriter[] p;
    private Attribute q;
    private ByteVector r;
    private int s;
    private int t;
    private int u;
    private ByteVector v;
    private int w;
    private int[] x;
    private int[] z;

    MethodWriter(ClassWriter classWriter, int i, String str, String str2, String str3, String[] strArr, boolean z, boolean z2) {
        super(Opcodes.ASM5);
        this.r = new ByteVector();
        if (classWriter.D == null) {
            classWriter.D = this;
        } else {
            classWriter.E.mv = this;
        }
        classWriter.E = this;
        this.b = classWriter;
        this.c = i;
        if ("<init>".equals(str)) {
            this.c |= 524288;
        }
        this.d = classWriter.newUTF8(str);
        this.e = classWriter.newUTF8(str2);
        this.f = str2;
        this.g = str3;
        if (strArr != null && strArr.length > 0) {
            int length = strArr.length;
            this.j = length;
            this.k = new int[length];
            for (int i2 = 0; i2 < this.j; i2++) {
                this.k[i2] = classWriter.newClass(strArr[i2]);
            }
        }
        this.M = z2 ? 0 : z ? 1 : 2;
        if (z || z2) {
            int argumentsAndReturnSizes = Type.getArgumentsAndReturnSizes(this.f) >> 2;
            argumentsAndReturnSizes = (i & 8) != 0 ? argumentsAndReturnSizes - 1 : argumentsAndReturnSizes;
            this.t = argumentsAndReturnSizes;
            this.T = argumentsAndReturnSizes;
            Label label = new Label();
            this.N = label;
            label.a |= 8;
            visitLabel(this.N);
        }
    }

    private int a(int i, int i2, int i3) {
        int i4 = i2 + 3 + i3;
        int[] iArr = this.z;
        if (iArr == null || iArr.length < i4) {
            this.z = new int[i4];
        }
        int[] iArr2 = this.z;
        iArr2[0] = i;
        iArr2[1] = i2;
        iArr2[2] = i3;
        return 3;
    }

    static int a(byte[] bArr, int i) {
        return (bArr[i + 3] & 255) | ((bArr[i] & 255) << 24) | ((bArr[i + 1] & 255) << 16) | ((bArr[i + 2] & 255) << 8);
    }

    static int a(int[] iArr, int[] iArr2, int i, int i2) {
        int i3 = i2 - i;
        for (int i4 = 0; i4 < iArr.length; i4++) {
            int i5 = iArr[i4];
            if (i < i5 && i5 <= i2) {
                i3 += iArr2[i4];
            } else if (i2 < i5 && i5 <= i) {
                i3 -= iArr2[i4];
            }
        }
        return i3;
    }

    private void a(int i, int i2) {
        char c;
        ByteVector putByte;
        int newClass;
        while (i < i2) {
            int i3 = this.z[i];
            int i4 = (-268435456) & i3;
            if (i4 == 0) {
                int i5 = i3 & 1048575;
                switch (i3 & 267386880) {
                    case 24117248:
                        putByte = this.v.putByte(7);
                        ClassWriter classWriter = this.b;
                        newClass = classWriter.newClass(classWriter.H[i5].g);
                        break;
                    case 25165824:
                        putByte = this.v.putByte(8);
                        newClass = this.b.H[i5].c;
                        break;
                    default:
                        this.v.putByte(i5);
                        continue;
                }
            } else {
                StringBuffer stringBuffer = new StringBuffer();
                int i6 = i4 >> 28;
                while (true) {
                    int i7 = i6 - 1;
                    if (i6 > 0) {
                        stringBuffer.append('[');
                        i6 = i7;
                    } else {
                        if ((i3 & 267386880) != 24117248) {
                            switch (i3 & 15) {
                                case 1:
                                    c = 'I';
                                    break;
                                case 2:
                                    c = 'F';
                                    break;
                                case 3:
                                    c = 'D';
                                    break;
                                case 4:
                                case 5:
                                case 6:
                                case 7:
                                case 8:
                                default:
                                    c = 'J';
                                    break;
                                case 9:
                                    c = 'Z';
                                    break;
                                case 10:
                                    c = 'B';
                                    break;
                                case 11:
                                    c = 'C';
                                    break;
                                case 12:
                                    c = 'S';
                                    break;
                            }
                        } else {
                            stringBuffer.append('L');
                            stringBuffer.append(this.b.H[i3 & 1048575].g);
                            c = ';';
                        }
                        stringBuffer.append(c);
                        putByte = this.v.putByte(7);
                        newClass = this.b.newClass(stringBuffer.toString());
                    }
                }
            }
            putByte.putShort(newClass);
            i++;
        }
    }

    private void a(int i, Label label) {
        Edge edge = new Edge();
        edge.a = i;
        edge.b = label;
        edge.c = this.P.j;
        this.P.j = edge;
    }

    private void a(Label label, Label[] labelArr) {
        Label label2 = this.P;
        if (label2 != null) {
            if (this.M == 0) {
                label2.h.a(Opcodes.LOOKUPSWITCH, 0, (ClassWriter) null, (Item) null);
                a(0, label);
                label.a().a |= 16;
                for (int i = 0; i < labelArr.length; i++) {
                    a(0, labelArr[i]);
                    labelArr[i].a().a |= 16;
                }
            } else {
                int i2 = this.Q - 1;
                this.Q = i2;
                a(i2, label);
                for (Label label3 : labelArr) {
                    a(this.Q, label3);
                }
            }
            e();
        }
    }

    private void a(Object obj) {
        ByteVector putByte;
        int i;
        if (obj instanceof String) {
            putByte = this.v.putByte(7);
            i = this.b.newClass((String) obj);
        } else if (obj instanceof Integer) {
            this.v.putByte(((Integer) obj).intValue());
            return;
        } else {
            putByte = this.v.putByte(8);
            i = ((Label) obj).c;
        }
        putByte.putShort(i);
    }

    static void a(byte[] bArr, int i, int i2) {
        bArr[i] = (byte) (i2 >>> 8);
        bArr[i + 1] = (byte) i2;
    }

    static void a(int[] iArr, int[] iArr2, Label label) {
        if ((label.a & 4) == 0) {
            label.c = a(iArr, iArr2, 0, label.c);
            label.a |= 4;
        }
    }

    static short b(byte[] bArr, int i) {
        return (short) ((bArr[i + 1] & 255) | ((bArr[i] & 255) << 8));
    }

    private void b() {
        if (this.x != null) {
            if (this.v == null) {
                this.v = new ByteVector();
            }
            c();
            this.u++;
        }
        this.x = this.z;
        this.z = null;
    }

    private void b(Frame frame) {
        int[] iArr = frame.c;
        int[] iArr2 = frame.d;
        int i = 0;
        int i2 = 0;
        int i3 = 0;
        int i4 = 0;
        while (i2 < iArr.length) {
            int i5 = iArr[i2];
            i4++;
            if (i5 != 16777216) {
                i3 += i4;
                i4 = 0;
            }
            if (i5 == 16777220 || i5 == 16777219) {
                i2++;
            }
            i2++;
        }
        int i6 = 0;
        int i7 = 0;
        while (i6 < iArr2.length) {
            int i8 = iArr2[i6];
            i7++;
            if (i8 == 16777220 || i8 == 16777219) {
                i6++;
            }
            i6++;
        }
        int a = a(frame.b.c, i3, i7);
        int i9 = 0;
        while (i3 > 0) {
            int i10 = iArr[i9];
            int i11 = a + 1;
            this.z[a] = i10;
            if (i10 == 16777220 || i10 == 16777219) {
                i9++;
            }
            i9++;
            i3--;
            a = i11;
        }
        while (i < iArr2.length) {
            int i12 = iArr2[i];
            int i13 = a + 1;
            this.z[a] = i12;
            if (i12 == 16777220 || i12 == 16777219) {
                i++;
            }
            i++;
            a = i13;
        }
        b();
    }

    static int c(byte[] bArr, int i) {
        return (bArr[i + 1] & 255) | ((bArr[i] & 255) << 8);
    }

    private void c() {
        int i;
        char c;
        ByteVector putByte;
        int[] iArr = this.z;
        int i2 = iArr[1];
        int i3 = iArr[2];
        int i4 = 0;
        if ((this.b.b & 65535) < 50) {
            this.v.putShort(this.z[0]).putShort(i2);
            int i5 = i2 + 3;
            a(3, i5);
            this.v.putShort(i3);
            a(i5, i3 + i5);
        }
        int i6 = this.x[1];
        int i7 = this.u == 0 ? this.z[0] : (this.z[0] - r3[0]) - 1;
        if (i3 == 0) {
            i = i2 - i6;
            switch (i) {
                case -3:
                case -2:
                case -1:
                    c = 248;
                    i6 = i2;
                    break;
                case 0:
                    if (i7 >= 64) {
                        c = 251;
                        break;
                    } else {
                        c = 0;
                        break;
                    }
                case 1:
                case 2:
                case 3:
                    c = 252;
                    break;
                default:
                    c = 255;
                    break;
            }
        } else if (i2 == i6 && i3 == 1) {
            c = i7 < 63 ? '@' : (char) 247;
            i = 0;
        } else {
            i = 0;
            c = 255;
        }
        if (c != 255) {
            int i8 = 3;
            while (true) {
                if (i4 < i6) {
                    if (this.z[i8] != this.x[i8]) {
                        c = 255;
                    } else {
                        i8++;
                        i4++;
                    }
                }
            }
        }
        switch (c) {
            case 0:
                this.v.putByte(i7);
                break;
            case '@':
                this.v.putByte(i7 + 64);
                a(i2 + 3, i2 + 4);
                break;
            case 247:
                this.v.putByte(247).putShort(i7);
                a(i2 + 3, i2 + 4);
                break;
            case 248:
                putByte = this.v.putByte(i + 251);
                putByte.putShort(i7);
                break;
            case 251:
                putByte = this.v.putByte(251);
                putByte.putShort(i7);
                break;
            case 252:
                this.v.putByte(i + 251).putShort(i7);
                a(i6 + 3, i2 + 3);
                break;
            default:
                this.v.putByte(255).putShort(i7).putShort(i2);
                int i9 = i2 + 3;
                a(3, i9);
                this.v.putShort(i3);
                a(i9, i3 + i9);
                break;
        }
    }

    private void d() {
        int i;
        char c;
        int b;
        int i2;
        int b2;
        int i3;
        int i4;
        byte[] bArr = this.r.a;
        int[] iArr = new int[0];
        int[] iArr2 = new int[0];
        boolean[] zArr = new boolean[this.r.b];
        int i5 = 3;
        do {
            if (i5 == 3) {
                i5 = 2;
            }
            int i6 = 0;
            while (true) {
                i = 218;
                c = 132;
                if (i6 < bArr.length) {
                    int i7 = bArr[i6] & 255;
                    switch (ClassWriter.a[i7]) {
                        case 0:
                        case 4:
                            i6++;
                            i3 = 0;
                            break;
                        case 1:
                        case 3:
                        case 11:
                            i6 += 2;
                            i3 = 0;
                            break;
                        case 2:
                        case 5:
                        case 6:
                        case 12:
                        case 13:
                            i6 += 3;
                            i3 = 0;
                            break;
                        case 7:
                        case 8:
                        case 10:
                            i6 += 5;
                            i3 = 0;
                            break;
                        case 9:
                            if (i7 > 201) {
                                i7 = i7 < 218 ? i7 - 49 : i7 - 20;
                                b2 = c(bArr, i6 + 1);
                            } else {
                                b2 = b(bArr, i6 + 1);
                            }
                            int a = a(iArr, iArr2, i6, b2 + i6);
                            if ((a < -32768 || a > 32767) && !zArr[i6]) {
                                int i8 = (i7 == 167 || i7 == 168) ? 2 : 5;
                                zArr[i6] = true;
                                i3 = i8;
                            } else {
                                i3 = 0;
                            }
                            i6 += 3;
                            break;
                        case 14:
                            if (i5 == 1) {
                                i4 = -(a(iArr, iArr2, 0, i6) & 3);
                            } else if (zArr[i6]) {
                                i3 = 0;
                                int i9 = (i6 + 4) - (i6 & 3);
                                i6 = i9 + (((a(bArr, i9 + 8) - a(bArr, i9 + 4)) + 1) * 4) + 12;
                                break;
                            } else {
                                i4 = i6 & 3;
                                zArr[i6] = true;
                            }
                            i3 = i4;
                            int i92 = (i6 + 4) - (i6 & 3);
                            i6 = i92 + (((a(bArr, i92 + 8) - a(bArr, i92 + 4)) + 1) * 4) + 12;
                        case 15:
                            if (i5 == 1) {
                                i3 = -(a(iArr, iArr2, 0, i6) & 3);
                            } else if (zArr[i6]) {
                                i3 = 0;
                            } else {
                                i3 = i6 & 3;
                                zArr[i6] = true;
                            }
                            int i10 = (i6 + 4) - (i6 & 3);
                            i6 = i10 + (a(bArr, i10 + 4) * 8) + 8;
                            break;
                        case 17:
                            if ((bArr[i6 + 1] & 255) == 132) {
                                i6 += 6;
                                i3 = 0;
                                break;
                            }
                        case 16:
                        default:
                            i6 += 4;
                            i3 = 0;
                            break;
                    }
                    if (i3 != 0) {
                        int[] iArr3 = new int[iArr.length + 1];
                        int[] iArr4 = new int[iArr2.length + 1];
                        System.arraycopy(iArr, 0, iArr3, 0, iArr.length);
                        System.arraycopy(iArr2, 0, iArr4, 0, iArr2.length);
                        iArr3[iArr.length] = i6;
                        iArr4[iArr2.length] = i3;
                        if (i3 > 0) {
                            i5 = 3;
                        }
                        iArr = iArr3;
                        iArr2 = iArr4;
                    }
                } else if (i5 < 3) {
                    i5--;
                }
            }
        } while (i5 != 0);
        ByteVector byteVector = new ByteVector(this.r.b);
        int i11 = 0;
        while (i11 < this.r.b) {
            int i12 = bArr[i11] & 255;
            switch (ClassWriter.a[i12]) {
                case 0:
                case 4:
                    byteVector.putByte(i12);
                    i11++;
                    break;
                case 1:
                case 3:
                case 11:
                    byteVector.putByteArray(bArr, i11, 2);
                    i11 += 2;
                    break;
                case 2:
                case 5:
                case 6:
                case 12:
                case 13:
                    byteVector.putByteArray(bArr, i11, 3);
                    i11 += 3;
                    break;
                case 7:
                case 8:
                    byteVector.putByteArray(bArr, i11, 5);
                    i11 += 5;
                    break;
                case 9:
                    if (i12 > 201) {
                        i12 = i12 < i ? i12 - 49 : i12 - 20;
                        b = c(bArr, i11 + 1);
                    } else {
                        b = b(bArr, i11 + 1);
                    }
                    int a2 = a(iArr, iArr2, i11, b + i11);
                    if (zArr[i11]) {
                        if (i12 == 167) {
                            byteVector.putByte(200);
                        } else if (i12 == 168) {
                            byteVector.putByte(201);
                        } else {
                            byteVector.putByte(i12 <= 166 ? ((i12 + 1) ^ 1) - 1 : i12 ^ 1);
                            byteVector.putShort(8);
                            byteVector.putByte(200);
                            a2 -= 3;
                        }
                        byteVector.putInt(a2);
                    } else {
                        byteVector.putByte(i12);
                        byteVector.putShort(a2);
                    }
                    i11 += 3;
                    break;
                case 10:
                    int a3 = a(iArr, iArr2, i11, a(bArr, i11 + 1) + i11);
                    byteVector.putByte(i12);
                    byteVector.putInt(a3);
                    i11 += 5;
                    break;
                case 14:
                    int i13 = (i11 + 4) - (i11 & 3);
                    byteVector.putByte(Opcodes.TABLESWITCH);
                    byteVector.putByteArray(null, 0, (4 - (byteVector.b % 4)) % 4);
                    int a4 = a(bArr, i13) + i11;
                    int i14 = i13 + 4;
                    byteVector.putInt(a(iArr, iArr2, i11, a4));
                    int a5 = a(bArr, i14);
                    int i15 = i14 + 4;
                    byteVector.putInt(a5);
                    i2 = i15 + 4;
                    byteVector.putInt(a(bArr, i2 - 4));
                    for (int a6 = (a(bArr, i15) - a5) + 1; a6 > 0; a6--) {
                        int a7 = a(bArr, i2) + i11;
                        i2 += 4;
                        byteVector.putInt(a(iArr, iArr2, i11, a7));
                    }
                    i11 = i2;
                    break;
                case 15:
                    int i16 = (i11 + 4) - (i11 & 3);
                    byteVector.putByte(Opcodes.LOOKUPSWITCH);
                    byteVector.putByteArray(null, 0, (4 - (byteVector.b % 4)) % 4);
                    int a8 = a(bArr, i16) + i11;
                    int i17 = i16 + 4;
                    byteVector.putInt(a(iArr, iArr2, i11, a8));
                    int a9 = a(bArr, i17);
                    i2 = i17 + 4;
                    byteVector.putInt(a9);
                    while (a9 > 0) {
                        byteVector.putInt(a(bArr, i2));
                        int i18 = i2 + 4;
                        int a10 = a(bArr, i18) + i11;
                        i2 = i18 + 4;
                        byteVector.putInt(a(iArr, iArr2, i11, a10));
                        a9--;
                    }
                    i11 = i2;
                    break;
                case 16:
                default:
                    byteVector.putByteArray(bArr, i11, 4);
                    i11 += 4;
                    break;
                case 17:
                    if ((bArr[i11 + 1] & 255) == c) {
                        byteVector.putByteArray(bArr, i11, 6);
                        i11 += 6;
                    } else {
                        byteVector.putByteArray(bArr, i11, 4);
                        i11 += 4;
                    }
                    break;
            }
            i = 218;
            c = 132;
        }
        if (this.M == 0) {
            for (Label label = this.N; label != null; label = label.i) {
                int i19 = label.c - 3;
                if (i19 >= 0 && zArr[i19]) {
                    label.a |= 16;
                }
                a(iArr, iArr2, label);
            }
            if (this.b.H != null) {
                for (int i20 = 0; i20 < this.b.H.length; i20++) {
                    Item item = this.b.H[i20];
                    if (item != null && item.b == 31) {
                        item.c = a(iArr, iArr2, 0, item.c);
                    }
                }
            }
        } else if (this.u > 0) {
            this.b.L = true;
        }
        for (Handler handler = this.B; handler != null; handler = handler.f) {
            a(iArr, iArr2, handler.a);
            a(iArr, iArr2, handler.b);
            a(iArr, iArr2, handler.c);
        }
        int i21 = 0;
        while (i21 < 2) {
            ByteVector byteVector2 = i21 == 0 ? this.E : this.G;
            if (byteVector2 != null) {
                byte[] bArr2 = byteVector2.a;
                for (int i22 = 0; i22 < byteVector2.b; i22 += 10) {
                    int c2 = c(bArr2, i22);
                    int a11 = a(iArr, iArr2, 0, c2);
                    a(bArr2, i22, a11);
                    int i23 = i22 + 2;
                    a(bArr2, i23, a(iArr, iArr2, 0, c2 + c(bArr2, i23)) - a11);
                }
            }
            i21++;
        }
        ByteVector byteVector3 = this.I;
        if (byteVector3 != null) {
            byte[] bArr3 = byteVector3.a;
            for (int i24 = 0; i24 < this.I.b; i24 += 4) {
                a(bArr3, i24, a(iArr, iArr2, 0, c(bArr3, i24)));
            }
        }
        for (Attribute attribute = this.J; attribute != null; attribute = attribute.a) {
            Label[] labels = attribute.getLabels();
            if (labels != null) {
                for (int length = labels.length - 1; length >= 0; length--) {
                    a(iArr, iArr2, labels[length]);
                }
            }
        }
        this.r = byteVector;
    }

    private void e() {
        if (this.M == 0) {
            Label label = new Label();
            label.h = new Frame();
            label.h.b = label;
            label.a(this, this.r.b, this.r.a);
            this.O.i = label;
            this.O = label;
        } else {
            this.P.g = this.R;
        }
        this.P = null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0041, code lost:
    
        r10.z[1] = r0 - 3;
        b();
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0049, code lost:
    
        return;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void f() {
        /*
            Method dump skipped, instructions count: 242
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.MethodWriter.f():void");
    }

    final int a() {
        int i;
        if (this.h != 0) {
            return this.i + 6;
        }
        if (this.r.b <= 0) {
            i = 8;
        } else {
            if (this.r.b > 65535) {
                throw new RuntimeException("Method code too large!");
            }
            this.b.newUTF8("Code");
            i = this.r.b + 18 + (this.A * 8) + 8;
            if (this.E != null) {
                this.b.newUTF8("LocalVariableTable");
                i += this.E.b + 8;
            }
            if (this.G != null) {
                this.b.newUTF8("LocalVariableTypeTable");
                i += this.G.b + 8;
            }
            if (this.I != null) {
                this.b.newUTF8("LineNumberTable");
                i += this.I.b + 8;
            }
            if (this.v != null) {
                this.b.newUTF8((this.b.b & 65535) >= 50 ? "StackMapTable" : "StackMap");
                i += this.v.b + 8;
            }
            if (this.W != null) {
                this.b.newUTF8("RuntimeVisibleTypeAnnotations");
                i += this.W.a() + 8;
            }
            if (this.X != null) {
                this.b.newUTF8("RuntimeInvisibleTypeAnnotations");
                i += this.X.a() + 8;
            }
            Attribute attribute = this.J;
            if (attribute != null) {
                i += attribute.a(this.b, this.r.a, this.r.b, this.s, this.t);
            }
        }
        if (this.j > 0) {
            this.b.newUTF8("Exceptions");
            i += (this.j * 2) + 8;
        }
        if ((this.c & 4096) != 0 && ((65535 & this.b.b) < 49 || (this.c & 262144) != 0)) {
            this.b.newUTF8("Synthetic");
            i += 6;
        }
        if ((this.c & 131072) != 0) {
            this.b.newUTF8("Deprecated");
            i += 6;
        }
        if (this.g != null) {
            this.b.newUTF8("Signature");
            this.b.newUTF8(this.g);
            i += 8;
        }
        if (this.$ != null) {
            this.b.newUTF8("MethodParameters");
            i += this.$.b + 7;
        }
        if (this.l != null) {
            this.b.newUTF8("AnnotationDefault");
            i += this.l.b + 6;
        }
        if (this.m != null) {
            this.b.newUTF8("RuntimeVisibleAnnotations");
            i += this.m.a() + 8;
        }
        if (this.n != null) {
            this.b.newUTF8("RuntimeInvisibleAnnotations");
            i += this.n.a() + 8;
        }
        if (this.U != null) {
            this.b.newUTF8("RuntimeVisibleTypeAnnotations");
            i += this.U.a() + 8;
        }
        if (this.V != null) {
            this.b.newUTF8("RuntimeInvisibleTypeAnnotations");
            i += this.V.a() + 8;
        }
        if (this.f13o != null) {
            this.b.newUTF8("RuntimeVisibleParameterAnnotations");
            AnnotationWriter[] annotationWriterArr = this.f13o;
            i += ((annotationWriterArr.length - this.S) * 2) + 7;
            for (int length = annotationWriterArr.length - 1; length >= this.S; length--) {
                AnnotationWriter annotationWriter = this.f13o[length];
                i += annotationWriter == null ? 0 : annotationWriter.a();
            }
        }
        if (this.p != null) {
            this.b.newUTF8("RuntimeInvisibleParameterAnnotations");
            AnnotationWriter[] annotationWriterArr2 = this.p;
            i += ((annotationWriterArr2.length - this.S) * 2) + 7;
            for (int length2 = annotationWriterArr2.length - 1; length2 >= this.S; length2--) {
                AnnotationWriter annotationWriter2 = this.p[length2];
                i += annotationWriter2 == null ? 0 : annotationWriter2.a();
            }
        }
        Attribute attribute2 = this.q;
        return attribute2 != null ? i + attribute2.a(this.b, null, 0, -1, -1) : i;
    }

    /* JADX WARN: Removed duplicated region for block: B:133:0x029a  */
    /* JADX WARN: Removed duplicated region for block: B:147:0x02e9  */
    /* JADX WARN: Removed duplicated region for block: B:150:0x02fc  */
    /* JADX WARN: Removed duplicated region for block: B:153:0x031b  */
    /* JADX WARN: Removed duplicated region for block: B:156:0x0344  */
    /* JADX WARN: Removed duplicated region for block: B:159:0x0365  */
    /* JADX WARN: Removed duplicated region for block: B:162:0x0379  */
    /* JADX WARN: Removed duplicated region for block: B:165:0x038d  */
    /* JADX WARN: Removed duplicated region for block: B:168:0x039f  */
    /* JADX WARN: Removed duplicated region for block: B:171:0x03b1  */
    /* JADX WARN: Removed duplicated region for block: B:174:0x03c7  */
    /* JADX WARN: Removed duplicated region for block: B:177:0x03dd  */
    /* JADX WARN: Removed duplicated region for block: B:179:? A[RETURN, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    final void a(com.esotericsoftware.asm.ByteVector r23) {
        /*
            Method dump skipped, instructions count: 1001
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.MethodWriter.a(com.esotericsoftware.asm.ByteVector):void");
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitAnnotation(String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, 2);
        if (z) {
            annotationWriter.g = this.m;
            this.m = annotationWriter;
        } else {
            annotationWriter.g = this.n;
            this.n = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitAnnotationDefault() {
        this.l = new ByteVector();
        return new AnnotationWriter(this.b, false, this.l, null, 0);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitAttribute(Attribute attribute) {
        if (attribute.isCodeAttribute()) {
            attribute.a = this.J;
            this.J = attribute;
        } else {
            attribute.a = this.q;
            this.q = attribute;
        }
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitCode() {
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitEnd() {
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitFieldInsn(int i, String str, String str2, String str3) {
        int i2;
        int i3;
        this.Y = this.r.b;
        Item a = this.b.a(str, str2, str3);
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, 0, this.b, a);
            } else {
                char charAt = str3.charAt(0);
                int i4 = -2;
                switch (i) {
                    case Opcodes.GETSTATIC /* 178 */:
                        i2 = this.Q + ((charAt == 'D' || charAt == 'J') ? 2 : 1);
                        break;
                    case Opcodes.PUTSTATIC /* 179 */:
                        i3 = this.Q;
                        if (charAt != 'D' && charAt != 'J') {
                            i4 = -1;
                        }
                        i2 = i4 + i3;
                        break;
                    case Opcodes.GETFIELD /* 180 */:
                        i2 = this.Q + ((charAt == 'D' || charAt == 'J') ? 1 : 0);
                        break;
                    default:
                        i3 = this.Q;
                        if (charAt == 'D' || charAt == 'J') {
                            i4 = -3;
                        }
                        i2 = i4 + i3;
                        break;
                }
                if (i2 > this.R) {
                    this.R = i2;
                }
                this.Q = i2;
            }
        }
        this.r.b(i, a.a);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitFrame(int i, int i2, Object[] objArr, int i3, Object[] objArr2) {
        int i4;
        ByteVector byteVector;
        int i5;
        int i6;
        if (this.M == 0) {
            return;
        }
        int i7 = 0;
        if (i == -1) {
            if (this.x == null) {
                f();
            }
            this.T = i2;
            int a = a(this.r.b, i2, i3);
            for (int i8 = 0; i8 < i2; i8++) {
                Object obj = objArr[i8];
                if (obj instanceof String) {
                    i6 = a + 1;
                    this.z[a] = 24117248 | this.b.c((String) obj);
                } else if (obj instanceof Integer) {
                    i6 = a + 1;
                    this.z[a] = ((Integer) obj).intValue();
                } else {
                    this.z[a] = this.b.a("", ((Label) obj).c) | 25165824;
                    a++;
                }
                a = i6;
            }
            while (i7 < i3) {
                Object obj2 = objArr2[i7];
                if (obj2 instanceof String) {
                    i5 = a + 1;
                    this.z[a] = this.b.c((String) obj2) | 24117248;
                } else if (obj2 instanceof Integer) {
                    i5 = a + 1;
                    this.z[a] = ((Integer) obj2).intValue();
                } else {
                    i5 = a + 1;
                    this.z[a] = this.b.a("", ((Label) obj2).c) | 25165824;
                }
                a = i5;
                i7++;
            }
            b();
        } else {
            if (this.v == null) {
                this.v = new ByteVector();
                i4 = this.r.b;
            } else {
                i4 = (this.r.b - this.w) - 1;
                if (i4 < 0) {
                    if (i != 3) {
                        throw new IllegalStateException();
                    }
                    return;
                }
            }
            int i9 = 251;
            switch (i) {
                case 0:
                    this.T = i2;
                    this.v.putByte(255).putShort(i4).putShort(i2);
                    for (int i10 = 0; i10 < i2; i10++) {
                        a(objArr[i10]);
                    }
                    this.v.putShort(i3);
                    while (i7 < i3) {
                        a(objArr2[i7]);
                        i7++;
                    }
                    break;
                case 1:
                    this.T += i2;
                    this.v.putByte(i2 + 251).putShort(i4);
                    while (i7 < i2) {
                        a(objArr[i7]);
                        i7++;
                    }
                    break;
                case 2:
                    this.T -= i2;
                    byteVector = this.v;
                    i9 = 251 - i2;
                    byteVector.putByte(i9).putShort(i4);
                    break;
                case 3:
                    byteVector = this.v;
                    if (i4 < 64) {
                        byteVector.putByte(i4);
                        break;
                    }
                    byteVector.putByte(i9).putShort(i4);
                    break;
                case 4:
                    ByteVector byteVector2 = this.v;
                    if (i4 < 64) {
                        byteVector2.putByte(i4 + 64);
                    } else {
                        byteVector2.putByte(247).putShort(i4);
                    }
                    a(objArr2[0]);
                    break;
            }
            this.w = this.r.b;
            this.u++;
        }
        this.s = Math.max(this.s, i3);
        this.t = Math.max(this.t, this.T);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitIincInsn(int i, int i2) {
        int i3;
        this.Y = this.r.b;
        Label label = this.P;
        if (label != null && this.M == 0) {
            label.h.a(Opcodes.IINC, i, (ClassWriter) null, (Item) null);
        }
        if (this.M != 2 && (i3 = i + 1) > this.t) {
            this.t = i3;
        }
        if (i > 255 || i2 > 127 || i2 < -128) {
            this.r.putByte(196).b(Opcodes.IINC, i).putShort(i2);
        } else {
            this.r.putByte(Opcodes.IINC).a(i, i2);
        }
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitInsn(int i) {
        this.Y = this.r.b;
        this.r.putByte(i);
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, 0, (ClassWriter) null, (Item) null);
            } else {
                int i2 = this.Q + Frame.a[i];
                if (i2 > this.R) {
                    this.R = i2;
                }
                this.Q = i2;
            }
            if ((i < 172 || i > 177) && i != 191) {
                return;
            }
            e();
        }
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitInsnAnnotation(int i, TypePath typePath, String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        AnnotationWriter.a((i & (-16776961)) | (this.Y << 8), typePath, byteVector);
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, byteVector.b - 2);
        if (z) {
            annotationWriter.g = this.W;
            this.W = annotationWriter;
        } else {
            annotationWriter.g = this.X;
            this.X = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitIntInsn(int i, int i2) {
        this.Y = this.r.b;
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, i2, (ClassWriter) null, (Item) null);
            } else if (i != 188) {
                int i3 = this.Q + 1;
                if (i3 > this.R) {
                    this.R = i3;
                }
                this.Q = i3;
            }
        }
        if (i == 17) {
            this.r.b(i, i2);
        } else {
            this.r.a(i, i2);
        }
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitInvokeDynamicInsn(String str, String str2, Handle handle, Object... objArr) {
        this.Y = this.r.b;
        Item a = this.b.a(str, str2, handle, objArr);
        int i = a.c;
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(Opcodes.INVOKEDYNAMIC, 0, this.b, a);
            } else {
                if (i == 0) {
                    i = Type.getArgumentsAndReturnSizes(str2);
                    a.c = i;
                }
                int i2 = (this.Q - (i >> 2)) + (i & 3) + 1;
                if (i2 > this.R) {
                    this.R = i2;
                }
                this.Q = i2;
            }
        }
        this.r.b(Opcodes.INVOKEDYNAMIC, a.a);
        this.r.putShort(0);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitJumpInsn(int i, Label label) {
        this.Y = this.r.b;
        Label label2 = this.P;
        Label label3 = null;
        if (label2 != null) {
            if (this.M == 0) {
                label2.h.a(i, 0, (ClassWriter) null, (Item) null);
                label.a().a |= 16;
                a(0, label);
                if (i != 167) {
                    label3 = new Label();
                }
            } else if (i == 168) {
                if ((label.a & 512) == 0) {
                    label.a |= 512;
                    this.L++;
                }
                this.P.a |= 128;
                a(this.Q + 1, label);
                label3 = new Label();
            } else {
                int i2 = this.Q + Frame.a[i];
                this.Q = i2;
                a(i2, label);
            }
        }
        if ((label.a & 2) == 0 || label.c - this.r.b >= -32768) {
            this.r.putByte(i);
            ByteVector byteVector = this.r;
            label.a(this, byteVector, byteVector.b - 1, false);
        } else {
            if (i != 167) {
                if (i == 168) {
                    this.r.putByte(201);
                    ByteVector byteVector2 = this.r;
                    label.a(this, byteVector2, byteVector2.b - 1, true);
                } else {
                    if (label3 != null) {
                        label3.a |= 16;
                    }
                    this.r.putByte(i <= 166 ? ((i + 1) ^ 1) - 1 : i ^ 1);
                    this.r.putShort(8);
                }
            }
            this.r.putByte(200);
            ByteVector byteVector22 = this.r;
            label.a(this, byteVector22, byteVector22.b - 1, true);
        }
        if (this.P != null) {
            if (label3 != null) {
                visitLabel(label3);
            }
            if (i == 167) {
                e();
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:32:0x008b, code lost:
    
        if (r0 != null) goto L30;
     */
    @Override // com.esotericsoftware.asm.MethodVisitor
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void visitLabel(com.esotericsoftware.asm.Label r4) {
        /*
            r3 = this;
            boolean r0 = r3.K
            com.esotericsoftware.asm.ByteVector r1 = r3.r
            int r1 = r1.b
            com.esotericsoftware.asm.ByteVector r2 = r3.r
            byte[] r2 = r2.a
            boolean r1 = r4.a(r3, r1, r2)
            r0 = r0 | r1
            r3.K = r0
            int r0 = r4.a
            r1 = 1
            r0 = r0 & r1
            if (r0 == 0) goto L18
            return
        L18:
            int r0 = r3.M
            r2 = 0
            if (r0 != 0) goto L74
            com.esotericsoftware.asm.Label r0 = r3.P
            if (r0 == 0) goto L3e
            int r0 = r4.c
            com.esotericsoftware.asm.Label r1 = r3.P
            int r1 = r1.c
            if (r0 != r1) goto L3b
            com.esotericsoftware.asm.Label r0 = r3.P
            int r1 = r0.a
            int r2 = r4.a
            r2 = r2 & 16
            r1 = r1 | r2
            r0.a = r1
            com.esotericsoftware.asm.Label r0 = r3.P
            com.esotericsoftware.asm.Frame r0 = r0.h
            r4.h = r0
            return
        L3b:
            r3.a(r2, r4)
        L3e:
            r3.P = r4
            com.esotericsoftware.asm.Frame r0 = r4.h
            if (r0 != 0) goto L4f
            com.esotericsoftware.asm.Frame r0 = new com.esotericsoftware.asm.Frame
            r0.<init>()
            r4.h = r0
            com.esotericsoftware.asm.Frame r0 = r4.h
            r0.b = r4
        L4f:
            com.esotericsoftware.asm.Label r0 = r3.O
            if (r0 == 0) goto L8f
            int r0 = r4.c
            com.esotericsoftware.asm.Label r1 = r3.O
            int r1 = r1.c
            if (r0 != r1) goto L71
            com.esotericsoftware.asm.Label r0 = r3.O
            int r1 = r0.a
            int r2 = r4.a
            r2 = r2 & 16
            r1 = r1 | r2
            r0.a = r1
            com.esotericsoftware.asm.Label r0 = r3.O
            com.esotericsoftware.asm.Frame r0 = r0.h
            r4.h = r0
            com.esotericsoftware.asm.Label r4 = r3.O
            r3.P = r4
            return
        L71:
            com.esotericsoftware.asm.Label r0 = r3.O
            goto L8d
        L74:
            if (r0 != r1) goto L91
            com.esotericsoftware.asm.Label r0 = r3.P
            if (r0 == 0) goto L83
            int r1 = r3.R
            r0.g = r1
            int r0 = r3.Q
            r3.a(r0, r4)
        L83:
            r3.P = r4
            r3.Q = r2
            r3.R = r2
            com.esotericsoftware.asm.Label r0 = r3.O
            if (r0 == 0) goto L8f
        L8d:
            r0.i = r4
        L8f:
            r3.O = r4
        L91:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.MethodWriter.visitLabel(com.esotericsoftware.asm.Label):void");
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitLdcInsn(Object obj) {
        ByteVector byteVector;
        int i;
        this.Y = this.r.b;
        Item a = this.b.a(obj);
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(18, 0, this.b, a);
            } else {
                int i2 = (a.b == 5 || a.b == 6) ? this.Q + 2 : this.Q + 1;
                if (i2 > this.R) {
                    this.R = i2;
                }
                this.Q = i2;
            }
        }
        int i3 = a.a;
        if (a.b == 5 || a.b == 6) {
            byteVector = this.r;
            i = 20;
        } else if (i3 < 256) {
            this.r.a(18, i3);
            return;
        } else {
            byteVector = this.r;
            i = 19;
        }
        byteVector.b(i, i3);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitLineNumber(int i, Label label) {
        if (this.I == null) {
            this.I = new ByteVector();
        }
        this.H++;
        this.I.putShort(label.c);
        this.I.putShort(i);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitLocalVariable(String str, String str2, String str3, Label label, Label label2, int i) {
        if (str3 != null) {
            if (this.G == null) {
                this.G = new ByteVector();
            }
            this.F++;
            this.G.putShort(label.c).putShort(label2.c - label.c).putShort(this.b.newUTF8(str)).putShort(this.b.newUTF8(str3)).putShort(i);
        }
        if (this.E == null) {
            this.E = new ByteVector();
        }
        this.D++;
        this.E.putShort(label.c).putShort(label2.c - label.c).putShort(this.b.newUTF8(str)).putShort(this.b.newUTF8(str2)).putShort(i);
        if (this.M != 2) {
            char charAt = str2.charAt(0);
            int i2 = i + ((charAt == 'J' || charAt == 'D') ? 2 : 1);
            if (i2 > this.t) {
                this.t = i2;
            }
        }
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitLocalVariableAnnotation(int i, TypePath typePath, Label[] labelArr, Label[] labelArr2, int[] iArr, String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        byteVector.putByte(i >>> 24).putShort(labelArr.length);
        for (int i2 = 0; i2 < labelArr.length; i2++) {
            byteVector.putShort(labelArr[i2].c).putShort(labelArr2[i2].c - labelArr[i2].c).putShort(iArr[i2]);
        }
        if (typePath == null) {
            byteVector.putByte(0);
        } else {
            byteVector.putByteArray(typePath.a, typePath.b, (typePath.a[typePath.b] * 2) + 1);
        }
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, byteVector.b - 2);
        if (z) {
            annotationWriter.g = this.W;
            this.W = annotationWriter;
        } else {
            annotationWriter.g = this.X;
            this.X = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitLookupSwitchInsn(Label label, int[] iArr, Label[] labelArr) {
        this.Y = this.r.b;
        int i = this.r.b;
        this.r.putByte(Opcodes.LOOKUPSWITCH);
        ByteVector byteVector = this.r;
        byteVector.putByteArray(null, 0, (4 - (byteVector.b % 4)) % 4);
        label.a(this, this.r, i, true);
        this.r.putInt(labelArr.length);
        for (int i2 = 0; i2 < labelArr.length; i2++) {
            this.r.putInt(iArr[i2]);
            labelArr[i2].a(this, this.r, i, true);
        }
        a(label, labelArr);
    }

    /*  JADX ERROR: JadxOverflowException in pass: LoopRegionVisitor
        jadx.core.utils.exceptions.JadxOverflowException: LoopRegionVisitor.assignOnlyInLoop endless recursion
        	at jadx.core.utils.ErrorsCounter.addError(ErrorsCounter.java:59)
        	at jadx.core.utils.ErrorsCounter.error(ErrorsCounter.java:31)
        	at jadx.core.dex.attributes.nodes.NotificationAttrNode.addError(NotificationAttrNode.java:19)
        */
    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitMaxs(int r14, int r15) {
        /*
            Method dump skipped, instructions count: 507
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.MethodWriter.visitMaxs(int, int):void");
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitMethodInsn(int i, String str, String str2, String str3, boolean z) {
        this.Y = this.r.b;
        Item a = this.b.a(str, str2, str3, z);
        int i2 = a.c;
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, 0, this.b, a);
            } else {
                if (i2 == 0) {
                    i2 = Type.getArgumentsAndReturnSizes(str3);
                    a.c = i2;
                }
                int i3 = i == 184 ? (this.Q - (i2 >> 2)) + (i2 & 3) + 1 : (this.Q - (i2 >> 2)) + (i2 & 3);
                if (i3 > this.R) {
                    this.R = i3;
                }
                this.Q = i3;
            }
        }
        if (i != 185) {
            this.r.b(i, a.a);
            return;
        }
        if (i2 == 0) {
            i2 = Type.getArgumentsAndReturnSizes(str3);
            a.c = i2;
        }
        this.r.b(Opcodes.INVOKEINTERFACE, a.a).a(i2 >> 2, 0);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitMultiANewArrayInsn(String str, int i) {
        this.Y = this.r.b;
        Item a = this.b.a(str);
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(Opcodes.MULTIANEWARRAY, i, this.b, a);
            } else {
                this.Q += 1 - i;
            }
        }
        this.r.b(Opcodes.MULTIANEWARRAY, a.a).putByte(i);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitParameter(String str, int i) {
        if (this.$ == null) {
            this.$ = new ByteVector();
        }
        this.Z++;
        this.$.putShort(str == null ? 0 : this.b.newUTF8(str)).putShort(i);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitParameterAnnotation(int i, String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        if ("Ljava/lang/Synthetic;".equals(str)) {
            this.S = Math.max(this.S, i + 1);
            return new AnnotationWriter(this.b, false, byteVector, null, 0);
        }
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, 2);
        if (z) {
            if (this.f13o == null) {
                this.f13o = new AnnotationWriter[Type.getArgumentTypes(this.f).length];
            }
            annotationWriter.g = this.f13o[i];
            this.f13o[i] = annotationWriter;
        } else {
            if (this.p == null) {
                this.p = new AnnotationWriter[Type.getArgumentTypes(this.f).length];
            }
            annotationWriter.g = this.p[i];
            this.p[i] = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitTableSwitchInsn(int i, int i2, Label label, Label... labelArr) {
        this.Y = this.r.b;
        int i3 = this.r.b;
        this.r.putByte(Opcodes.TABLESWITCH);
        ByteVector byteVector = this.r;
        byteVector.putByteArray(null, 0, (4 - (byteVector.b % 4)) % 4);
        label.a(this, this.r, i3, true);
        this.r.putInt(i).putInt(i2);
        for (Label label2 : labelArr) {
            label2.a(this, this.r, i3, true);
        }
        a(label, labelArr);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitTryCatchAnnotation(int i, TypePath typePath, String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        AnnotationWriter.a(i, typePath, byteVector);
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, byteVector.b - 2);
        if (z) {
            annotationWriter.g = this.W;
            this.W = annotationWriter;
        } else {
            annotationWriter.g = this.X;
            this.X = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitTryCatchBlock(Label label, Label label2, Label label3, String str) {
        this.A++;
        Handler handler = new Handler();
        handler.a = label;
        handler.b = label2;
        handler.c = label3;
        handler.d = str;
        handler.e = str != null ? this.b.newClass(str) : 0;
        Handler handler2 = this.C;
        if (handler2 == null) {
            this.B = handler;
        } else {
            handler2.f = handler;
        }
        this.C = handler;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public AnnotationVisitor visitTypeAnnotation(int i, TypePath typePath, String str, boolean z) {
        ByteVector byteVector = new ByteVector();
        AnnotationWriter.a(i, typePath, byteVector);
        byteVector.putShort(this.b.newUTF8(str)).putShort(0);
        AnnotationWriter annotationWriter = new AnnotationWriter(this.b, true, byteVector, byteVector, byteVector.b - 2);
        if (z) {
            annotationWriter.g = this.U;
            this.U = annotationWriter;
        } else {
            annotationWriter.g = this.V;
            this.V = annotationWriter;
        }
        return annotationWriter;
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitTypeInsn(int i, String str) {
        this.Y = this.r.b;
        Item a = this.b.a(str);
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, this.r.b, this.b, a);
            } else if (i == 187) {
                int i2 = this.Q + 1;
                if (i2 > this.R) {
                    this.R = i2;
                }
                this.Q = i2;
            }
        }
        this.r.b(i, a.a);
    }

    @Override // com.esotericsoftware.asm.MethodVisitor
    public void visitVarInsn(int i, int i2) {
        this.Y = this.r.b;
        Label label = this.P;
        if (label != null) {
            if (this.M == 0) {
                label.h.a(i, i2, (ClassWriter) null, (Item) null);
            } else if (i == 169) {
                label.a |= 256;
                this.P.f = this.Q;
                e();
            } else {
                int i3 = this.Q + Frame.a[i];
                if (i3 > this.R) {
                    this.R = i3;
                }
                this.Q = i3;
            }
        }
        if (this.M != 2) {
            int i4 = (i == 22 || i == 24 || i == 55 || i == 57) ? i2 + 2 : i2 + 1;
            if (i4 > this.t) {
                this.t = i4;
            }
        }
        if (i2 >= 4 || i == 169) {
            ByteVector byteVector = this.r;
            if (i2 >= 256) {
                byteVector.putByte(196).b(i, i2);
            } else {
                byteVector.a(i, i2);
            }
        } else {
            this.r.putByte((i < 54 ? ((i - 21) << 2) + 26 : ((i - 54) << 2) + 59) + i2);
        }
        if (i < 54 || this.M != 0 || this.A <= 0) {
            return;
        }
        visitLabel(new Label());
    }
}
