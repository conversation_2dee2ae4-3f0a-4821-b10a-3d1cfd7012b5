package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w6.smali */
public class w6 extends u {
    private BigInteger C;
    private BigInteger L;
    private BigInteger R;
    private BigInteger b;
    private BigInteger u0;
    private BigInteger v0;
    private BigInteger w0;
    private BigInteger x;
    private BigInteger x0;
    private e0 y0;

    private w6(e0 e0Var) {
        this.y0 = null;
        Enumeration j = e0Var.j();
        r rVar = (r) j.nextElement();
        int k = rVar.k();
        if (k < 0 || k > 1) {
            throw new IllegalArgumentException("wrong version for RSA private key");
        }
        this.b = rVar.i();
        this.x = ((r) j.nextElement()).i();
        this.C = ((r) j.nextElement()).i();
        this.L = ((r) j.nextElement()).i();
        this.R = ((r) j.nextElement()).i();
        this.u0 = ((r) j.nextElement()).i();
        this.v0 = ((r) j.nextElement()).i();
        this.w0 = ((r) j.nextElement()).i();
        this.x0 = ((r) j.nextElement()).i();
        if (j.hasMoreElements()) {
            this.y0 = (e0) j.nextElement();
        }
    }

    public static w6 a(Object obj) {
        if (obj instanceof w6) {
            return (w6) obj;
        }
        if (obj != null) {
            return new w6(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.x0;
    }

    public BigInteger f() {
        return this.v0;
    }

    public BigInteger g() {
        return this.w0;
    }

    public BigInteger h() {
        return this.x;
    }

    public BigInteger i() {
        return this.R;
    }

    public BigInteger j() {
        return this.u0;
    }

    public BigInteger k() {
        return this.L;
    }

    public BigInteger l() {
        return this.C;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(10);
        iVar.a(new r(this.b));
        iVar.a(new r(h()));
        iVar.a(new r(l()));
        iVar.a(new r(k()));
        iVar.a(new r(i()));
        iVar.a(new r(j()));
        iVar.a(new r(f()));
        iVar.a(new r(g()));
        iVar.a(new r(e()));
        e0 e0Var = this.y0;
        if (e0Var != null) {
            iVar.a(e0Var);
        }
        return new j2(iVar);
    }
}
