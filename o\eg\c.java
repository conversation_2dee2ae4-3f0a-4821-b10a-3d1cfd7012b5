package o.eg;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import o.a.m;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static char e;
    private static int f;
    private static short[] g;
    private static byte[] h;
    private static int i;
    private static int j;
    private static int l;
    private static int m;
    private final String a;
    private final List<EnumC0040c> c;
    private StringBuilder d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        l = 1;
        b = new char[]{30561, 30546, 30517, 30560, 30547, 30548, 30469, 30573, 30563};
        e = (char) 17046;
        h = new byte[]{21, 27, 22, 19, 72, -86, 29, -27, 7, -17, 85, -45, 23, 17, 74, -47, -17, 18, 29, -21, 30, -19, 30, 67, -4, -61, 30, -17, 0, -23, 19, 24, 70, -81, -17, 27, -21, 23, 4, 13, -92, -75, -90, -113, -71, -66, -20, 117, -75, -95, -79, -67, -86, -45, -14, 8, 44, 107, 43, 82, -9, 64, 78, 69, 117, -113, 13, 77, 79, PSSSigner.TRAILER_IMPLICIT, 9, 113, UtilitiesSDKConstants.SRP_LABEL_ENC, 26, 79, 76, 118, -69, 27, 124, 70, 122, 97, -57, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112};
        f = 909053582;
        j = -2018458674;
        i = -1484423607;
    }

    static void init$0() {
        $$a = new byte[]{106, 33, -117, 89};
        $$b = 9;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r0 = o.eg.c.$$a
            int r8 = r8 + 4
            int r7 = r7 + 69
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2c
        L15:
            r3 = r2
        L16:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r6 = r6 + r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.o(int, short, byte, java.lang.Object[]):void");
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* renamed from: o.eg.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\c$c.smali */
    static final class EnumC0040c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final EnumC0040c a;
        public static final EnumC0040c b;
        public static final EnumC0040c c;
        public static final EnumC0040c d;
        public static final EnumC0040c e;
        private static int f;
        private static long g;
        private static int h;
        private static final /* synthetic */ EnumC0040c[] i;
        public static final EnumC0040c j;

        static void a() {
            g = -5231276321200587966L;
        }

        static void init$0() {
            $$a = new byte[]{70, -116, 4, 37};
            $$b = 86;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(int r6, int r7, short r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 3
                int r6 = r6 + 68
                int r8 = r8 + 4
                int r7 = r7 * 3
                int r7 = 1 - r7
                byte[] r0 = o.eg.c.EnumC0040c.$$a
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L36
            L1a:
                r3 = r2
            L1b:
                int r8 = r8 + 1
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r7) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                int r3 = r3 + 1
                r4 = r0[r8]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L36:
                int r7 = -r7
                int r6 = r6 + r7
                r7 = r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eg.c.EnumC0040c.l(int, int, short, java.lang.Object[]):void");
        }

        private EnumC0040c(String str, int i2) {
        }

        private static /* synthetic */ EnumC0040c[] c() {
            int i2 = f + 19;
            int i3 = i2 % 128;
            h = i3;
            int i4 = i2 % 2;
            EnumC0040c[] enumC0040cArr = {c, e, a, b, d, j};
            int i5 = i3 + 33;
            f = i5 % 128;
            switch (i5 % 2 == 0 ? '\t' : 'J') {
                case 'J':
                    return enumC0040cArr;
                default:
                    int i6 = 92 / 0;
                    return enumC0040cArr;
            }
        }

        public static EnumC0040c valueOf(String str) {
            int i2 = h + 21;
            f = i2 % 128;
            int i3 = i2 % 2;
            EnumC0040c enumC0040c = (EnumC0040c) Enum.valueOf(EnumC0040c.class, str);
            int i4 = f + 23;
            h = i4 % 128;
            int i5 = i4 % 2;
            return enumC0040c;
        }

        public static EnumC0040c[] values() {
            int i2 = f + 91;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? '^' : '(') {
                case '(':
                    return (EnumC0040c[]) i.clone();
                default:
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            f = 1;
            a();
            Object[] objArr = new Object[1];
            k("熹燼琎ቔ\uf296\u0fde\ue53e耕龔Ṁ\uf7bb鎏괃Ⳋ옯", 1 - (Process.myPid() >> 22), objArr);
            c = new EnumC0040c(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            k("\ud817\ud859\u2fe5竒ᴫ吷趦澹㘮䖤鼨簹Ҡ眡꺢勆ጊ楅", 1 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr2);
            e = new EnumC0040c(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            k("沾泻滂歛南ᔒ鰱⇔芓Ҍ躺㉞뀜㘂뼺\u1cbc", 1 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr3);
            a = new EnumC0040c(((String) objArr3[0]).intern(), 2);
            Object[] objArr4 = new Object[1];
            k("ỿẻḉ\ue42b쐇旕፟뚗\uf0c7瑑ǋꔋ쉈䛇が诡", -TextUtils.indexOf((CharSequence) "", '0'), objArr4);
            b = new EnumC0040c(((String) objArr4[0]).intern(), 3);
            Object[] objArr5 = new Object[1];
            k("僤傪ﵹ\uf373悤蚫Їሶ뻝霸ᚉƶ豓ꖳ✓⽑鯽믃ㆱ", View.resolveSize(0, 0) + 1, objArr5);
            d = new EnumC0040c(((String) objArr5[0]).intern(), 4);
            Object[] objArr6 = new Object[1];
            k("멲먼\uebefえ윢逧윾떹", -TextUtils.lastIndexOf("", '0', 0, 0), objArr6);
            j = new EnumC0040c(((String) objArr6[0]).intern(), 5);
            i = c();
            int i2 = f + Opcodes.LSHR;
            h = i2 % 128;
            int i3 = i2 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 362
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eg.c.EnumC0040c.k(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    public c() {
        this.d = new StringBuilder();
        this.c = new ArrayList();
        this.a = null;
    }

    c(int i2) {
        this.d = new StringBuilder();
        this.c = new ArrayList();
        char[] cArr = new char[4];
        Arrays.fill(cArr, ' ');
        this.a = new String(cArr);
    }

    public final c b() throws d {
        EnumC0040c enumC0040c;
        Object obj;
        int i2 = l + 15;
        m = i2 % 128;
        switch (i2 % 2 != 0 ? '+' : '\n') {
            case '+':
                enumC0040c = EnumC0040c.c;
                Object[] objArr = new Object[1];
                k(1 - TextUtils.indexOf("", "", 1, 1), "㗰", (byte) (89 << TextUtils.lastIndexOf("", (char) 14, 1, 0)), objArr);
                obj = objArr[0];
                break;
            default:
                enumC0040c = EnumC0040c.c;
                Object[] objArr2 = new Object[1];
                k(TextUtils.indexOf("", "", 0, 0) + 1, "㗰", (byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 48), objArr2);
                obj = objArr2[0];
                break;
        }
        return d(enumC0040c, ((String) obj).intern());
    }

    public final c a() throws d {
        EnumC0040c enumC0040c;
        EnumC0040c enumC0040c2;
        Object obj;
        int i2 = l + 45;
        m = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 7 : '4') {
            case '4':
                enumC0040c = EnumC0040c.c;
                enumC0040c2 = EnumC0040c.e;
                Object[] objArr = new Object[1];
                k(Gravity.getAbsoluteGravity(0, 0) + 1, "㘊", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 68), objArr);
                obj = objArr[0];
                break;
            default:
                enumC0040c = EnumC0040c.c;
                enumC0040c2 = EnumC0040c.e;
                Object[] objArr2 = new Object[1];
                k(Gravity.getAbsoluteGravity(0, 0) + 0, "㘊", (byte) (Opcodes.DDIV >> ((byte) KeyEvent.getModifierMetaStateMask())), objArr2);
                obj = objArr2[0];
                break;
        }
        c c = c(enumC0040c, enumC0040c2, ((String) obj).intern());
        int i3 = l + 27;
        m = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    public final c c() throws d {
        int i2 = l + Opcodes.DDIV;
        m = i2 % 128;
        int i3 = i2 % 2;
        EnumC0040c enumC0040c = EnumC0040c.a;
        Object[] objArr = new Object[1];
        n((byte) Gravity.getAbsoluteGravity(0, 0), 1851100967 + TextUtils.getOffsetBefore("", 0), (short) (Color.blue(0) + 96), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 28, 1314927389 + (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr);
        c d = d(enumC0040c, ((String) objArr[0]).intern());
        int i4 = m + 89;
        l = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return d;
            default:
                int i5 = 60 / 0;
                return d;
        }
    }

    public final c e() throws d {
        int i2 = l + 31;
        m = i2 % 128;
        int i3 = i2 % 2;
        EnumC0040c enumC0040c = EnumC0040c.a;
        EnumC0040c enumC0040c2 = EnumC0040c.d;
        Object[] objArr = new Object[1];
        n((byte) (ViewConfiguration.getLongPressTimeout() >> 16), 1851100967 + (ViewConfiguration.getWindowTouchSlop() >> 8), (short) ((KeyEvent.getMaxKeyCode() >> 16) - 54), (-29) - Color.blue(0), 1314927391 - TextUtils.getOffsetBefore("", 0), objArr);
        c c = c(enumC0040c, enumC0040c2, ((String) objArr[0]).intern());
        int i4 = l + 99;
        m = i4 % 128;
        int i5 = i4 % 2;
        return c;
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x003a, code lost:
    
        if (r11.d.length() <= 0) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x003d, code lost:
    
        r13 = new java.lang.Object[1];
        n((byte) android.view.View.MeasureSpec.getSize(0), 1851100968 - (android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)), (short) (122 - android.view.View.MeasureSpec.getMode(0)), 11 - android.view.KeyEvent.keyCodeFromString(""), 1314927345 - (android.os.SystemClock.currentThreadTimeMillis() > (-1) ? 1 : (android.os.SystemClock.currentThreadTimeMillis() == (-1) ? 0 : -1)), r13);
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x007e, code lost:
    
        throw new o.eg.d(((java.lang.String) r13[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0034, code lost:
    
        if (r11.d.length() <= 0) goto L25;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.eg.c d(o.eg.c.EnumC0040c r12, java.lang.String r13) throws o.eg.d {
        /*
            r11 = this;
            java.util.List<o.eg.c$c> r0 = r11.c
            boolean r0 = r0.isEmpty()
            if (r0 == 0) goto Lc
            r0 = 76
            goto Le
        Lc:
            r0 = 96
        Le:
            switch(r0) {
                case 76: goto L13;
                default: goto L11;
            }
        L11:
            goto L81
        L13:
            int r0 = o.eg.c.m
            int r0 = r0 + 101
            int r1 = r0 % 128
            o.eg.c.l = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L23
            r0 = r1
            goto L24
        L23:
            r0 = r2
        L24:
            switch(r0) {
                case 0: goto L2e;
                default: goto L27;
            }
        L27:
            java.lang.StringBuilder r0 = r11.d
            int r0 = r0.length()
            goto L37
        L2e:
            java.lang.StringBuilder r0 = r11.d
            int r0 = r0.length()
            if (r0 > 0) goto L3d
            goto L81
        L37:
            r3 = 69
            int r3 = r3 / r2
            if (r0 > 0) goto L3d
            goto L11
        L3d:
            o.eg.d r12 = new o.eg.d
            int r13 = android.view.View.MeasureSpec.getSize(r2)
            byte r3 = (byte) r13
            long r4 = android.view.ViewConfiguration.getZoomControlsTimeout()
            r6 = 0
            int r13 = (r4 > r6 ? 1 : (r4 == r6 ? 0 : -1))
            r0 = 1851100968(0x6e558f28, float:1.6523343E28)
            int r4 = r0 - r13
            int r13 = android.view.View.MeasureSpec.getMode(r2)
            int r13 = 122 - r13
            short r5 = (short) r13
            java.lang.String r13 = ""
            int r13 = android.view.KeyEvent.keyCodeFromString(r13)
            int r6 = 11 - r13
            long r7 = android.os.SystemClock.currentThreadTimeMillis()
            r9 = -1
            int r13 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            r0 = 1314927345(0x4e6032f1, float:9.4035872E8)
            int r7 = r0 - r13
            java.lang.Object[] r13 = new java.lang.Object[r1]
            r8 = r13
            n(r3, r4, r5, r6, r7, r8)
            r13 = r13[r2]
            java.lang.String r13 = (java.lang.String) r13
            java.lang.String r13 = r13.intern()
            r12.<init>(r13)
            throw r12
        L7f:
            r12 = move-exception
            throw r12
        L81:
            r11.j()
            java.util.List<o.eg.c$c> r0 = r11.c
            r0.add(r12)
            java.lang.StringBuilder r12 = r11.d
            r12.append(r13)
            int r12 = o.eg.c.l
            int r12 = r12 + 79
            int r13 = r12 % 128
            o.eg.c.m = r13
            int r12 = r12 % 2
            return r11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.d(o.eg.c$c, java.lang.String):o.eg.c");
    }

    private c c(EnumC0040c enumC0040c, EnumC0040c enumC0040c2, String str) throws d {
        EnumC0040c h2 = h();
        Object obj = null;
        switch (h2 != enumC0040c2) {
            case true:
                int i2 = l + 59;
                m = i2 % 128;
                switch (i2 % 2 != 0 ? 'L' : '?') {
                    case '?':
                        if (h2 != enumC0040c) {
                            Object[] objArr = new Object[1];
                            n((byte) (TextUtils.lastIndexOf("", '0') + 1), TextUtils.getTrimmedLength("") + 1851101007, (short) (View.getDefaultSize(0, 0) - 44), View.getDefaultSize(0, 0) - 15, 1314927344 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
                            throw new d(((String) objArr[0]).intern());
                        }
                        break;
                    default:
                        throw null;
                }
        }
        List<EnumC0040c> list = this.c;
        list.remove(list.size() - 1);
        switch (h2 != enumC0040c2) {
            case false:
                int i3 = l + 17;
                m = i3 % 128;
                if (i3 % 2 != 0) {
                    i();
                    obj.hashCode();
                    throw null;
                }
                i();
                break;
        }
        this.d.append(str);
        return this;
    }

    private EnumC0040c h() throws d {
        int i2 = m + Opcodes.LSHL;
        l = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? (char) 27 : 'b') {
            case 27:
                this.c.isEmpty();
                obj.hashCode();
                throw null;
            default:
                if (!this.c.isEmpty()) {
                    List<EnumC0040c> list = this.c;
                    EnumC0040c enumC0040c = list.get(list.size() - 1);
                    int i3 = l + 73;
                    m = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            throw null;
                        default:
                            return enumC0040c;
                    }
                }
                Object[] objArr = new Object[1];
                n((byte) ((-1) - ExpandableListView.getPackedPositionChild(0L)), 1851101007 + KeyEvent.keyCodeFromString(""), (short) (KeyEvent.getDeadChar(0, 0) - 44), (-15) - (ViewConfiguration.getKeyRepeatDelay() >> 16), 1314927344 + (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
                throw new d(((String) objArr[0]).intern());
        }
    }

    private void e(EnumC0040c enumC0040c) {
        int i2 = m + 95;
        l = i2 % 128;
        int i3 = i2 % 2;
        List<EnumC0040c> list = this.c;
        list.set(list.size() - 1, enumC0040c);
        int i4 = l + 19;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                int i5 = 13 / 0;
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:35:0x006a, code lost:
    
        if (r11 == o.eg.b.b) goto L48;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0076, code lost:
    
        if ((r11 instanceof java.lang.Number) == false) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0078, code lost:
    
        r10.d.append(o.eg.b.b((java.lang.Number) r11));
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x0084, code lost:
    
        e(r11.toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0071, code lost:
    
        if (r11 == o.eg.b.b) goto L48;
     */
    /* JADX WARN: Removed duplicated region for block: B:47:0x008c A[FALL_THROUGH] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.eg.c c(java.lang.Object r11) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 230
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.c(java.lang.Object):o.eg.c");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private void e(java.lang.String r21) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.e(java.lang.String):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private void i() {
        /*
            r6 = this;
            int r0 = o.eg.c.l
            int r0 = r0 + 91
            int r1 = r0 % 128
            o.eg.c.m = r1
            int r0 = r0 % 2
            java.lang.String r0 = r6.a
            if (r0 != 0) goto Lf
            return
        Lf:
            java.lang.StringBuilder r0 = r6.d
            r1 = 0
            int r2 = android.view.KeyEvent.getDeadChar(r1, r1)
            r3 = 1
            int r2 = 1 - r2
            java.lang.String r4 = ""
            r5 = 48
            int r4 = android.text.TextUtils.indexOf(r4, r5, r1, r1)
            int r4 = 9 - r4
            byte r4 = (byte) r4
            java.lang.Object[] r3 = new java.lang.Object[r3]
            java.lang.String r5 = "㖚"
            k(r2, r5, r4, r3)
            r2 = r3[r1]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            r0.append(r2)
            int r0 = o.eg.c.m
            int r0 = r0 + 91
            int r2 = r0 % 128
            o.eg.c.l = r2
            int r0 = r0 % 2
        L40:
            java.util.List<o.eg.c$c> r0 = r6.c
            int r0 = r0.size()
            if (r1 >= r0) goto L4b
            r0 = 64
            goto L4d
        L4b:
            r0 = 62
        L4d:
            switch(r0) {
                case 62: goto L5d;
                default: goto L50;
            }
        L50:
            int r0 = o.eg.c.m
            int r0 = r0 + 23
            int r2 = r0 % 128
            o.eg.c.l = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L5e
            goto L5e
        L5d:
            return
        L5e:
            java.lang.StringBuilder r0 = r6.d
            java.lang.String r2 = r6.a
            r0.append(r2)
            int r1 = r1 + 1
            goto L40
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.i():void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x001e, code lost:
    
        f();
        e(r9);
        r9 = o.eg.c.m + 15;
        o.eg.c.l = r9 % 128;
        r9 = r9 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x002f, code lost:
    
        return r8;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0019, code lost:
    
        if (r9 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0013, code lost:
    
        if (r9 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0030, code lost:
    
        r0 = new java.lang.Object[1];
        n((byte) android.text.TextUtils.getTrimmedLength(""), android.graphics.Color.green(0) + 1851101029, (short) (android.graphics.Color.red(0) + 34), (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 8, 1314927344 - android.view.View.MeasureSpec.makeMeasureSpec(0, 0), r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x006c, code lost:
    
        throw new o.eg.d(((java.lang.String) r0[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.eg.c c(java.lang.String r9) throws o.eg.d {
        /*
            r8 = this;
            int r0 = o.eg.c.l
            int r0 = r0 + 45
            int r1 = r0 % 128
            o.eg.c.m = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Le
            r0 = 4
            goto Lf
        Le:
            r0 = 7
        Lf:
            r1 = 0
            switch(r0) {
                case 4: goto L16;
                default: goto L13;
            }
        L13:
            if (r9 == 0) goto L30
        L15:
            goto L1e
        L16:
            r0 = 18
            int r0 = r0 / r1
            if (r9 == 0) goto L30
            goto L15
        L1c:
            r9 = move-exception
            throw r9
        L1e:
            r8.f()
            r8.e(r9)
            int r9 = o.eg.c.m
            int r9 = r9 + 15
            int r0 = r9 % 128
            o.eg.c.l = r0
            int r9 = r9 % 2
            return r8
        L30:
            o.eg.d r9 = new o.eg.d
            java.lang.String r0 = ""
            int r0 = android.text.TextUtils.getTrimmedLength(r0)
            byte r2 = (byte) r0
            r0 = 1851101029(0x6e558f65, float:1.6523415E28)
            int r3 = android.graphics.Color.green(r1)
            int r3 = r3 + r0
            int r0 = android.graphics.Color.red(r1)
            int r0 = r0 + 34
            short r4 = (short) r0
            r0 = 0
            float r5 = android.graphics.PointF.length(r0, r0)
            int r0 = (r5 > r0 ? 1 : (r5 == r0 ? 0 : -1))
            int r5 = r0 + (-8)
            r0 = 1314927344(0x4e6032f0, float:9.4035866E8)
            int r6 = android.view.View.MeasureSpec.makeMeasureSpec(r1, r1)
            int r6 = r0 - r6
            r0 = 1
            java.lang.Object[] r0 = new java.lang.Object[r0]
            r7 = r0
            n(r2, r3, r4, r5, r6, r7)
            r0 = r0[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r9.<init>(r0)
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.c(java.lang.String):o.eg.c");
    }

    private void f() throws d {
        int i2 = m + 9;
        l = i2 % 128;
        int i3 = i2 % 2;
        EnumC0040c h2 = h();
        switch (h2 == EnumC0040c.d ? (char) 23 : 'S') {
            case Opcodes.AASTORE /* 83 */:
                if (h2 != EnumC0040c.a) {
                    Object[] objArr = new Object[1];
                    n((byte) (ExpandableListView.getPackedPositionChild(0L) + 1), 1851101007 + (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 45), (-15) - (Process.myTid() >> 22), 1314927343 - ExpandableListView.getPackedPositionChild(0L), objArr);
                    throw new d(((String) objArr[0]).intern());
                }
                break;
            default:
                int i4 = m + 69;
                l = i4 % 128;
                if (i4 % 2 == 0) {
                }
                this.d.append(',');
                break;
        }
        i();
        e(EnumC0040c.b);
    }

    private void j() throws d {
        String intern;
        StringBuilder sb;
        char c;
        int i2 = m + Opcodes.DMUL;
        l = i2 % 128;
        int i3 = i2 % 2;
        if (this.c.isEmpty()) {
            return;
        }
        EnumC0040c h2 = h();
        if (h2 == EnumC0040c.c) {
            int i4 = m + Opcodes.LREM;
            l = i4 % 128;
            int i5 = i4 % 2;
            e(EnumC0040c.e);
            i();
            return;
        }
        if (h2 == EnumC0040c.e) {
            int i6 = m + 73;
            l = i6 % 128;
            switch (i6 % 2 != 0) {
                case false:
                    sb = this.d;
                    c = ']';
                    break;
                default:
                    sb = this.d;
                    c = ',';
                    break;
            }
            sb.append(c);
            i();
            return;
        }
        switch (h2 == EnumC0040c.b ? (char) 7 : ':') {
            case 7:
                StringBuilder sb2 = this.d;
                switch (this.a != null) {
                    case true:
                        Object[] objArr = new Object[1];
                        n((byte) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 1851101050 - TextUtils.getOffsetAfter("", 0), (short) (View.resolveSizeAndState(0, 0, 0) - 113), (-28) - View.getDefaultSize(0, 0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1314927324, objArr);
                        intern = ((String) objArr[0]).intern();
                        break;
                    default:
                        Object[] objArr2 = new Object[1];
                        k(View.MeasureSpec.getSize(0) + 1, "㗴", (byte) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 84), objArr2);
                        intern = ((String) objArr2[0]).intern();
                        break;
                }
                sb2.append(intern);
                e(EnumC0040c.d);
                return;
            default:
                if (h2 == EnumC0040c.j) {
                    return;
                }
                Object[] objArr3 = new Object[1];
                n((byte) TextUtils.indexOf("", ""), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1851101006, (short) ((-44) - TextUtils.getOffsetAfter("", 0)), Drawable.resolveOpacity(0, 0) - 15, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1314927343, objArr3);
                throw new d(((String) objArr3[0]).intern());
        }
    }

    public final String d() {
        int i2 = m + Opcodes.LSUB;
        l = i2 % 128;
        int i3 = i2 % 2;
        switch (this.d.length() == 0) {
            case false:
                String obj = this.d.toString();
                int i4 = l + 13;
                m = i4 % 128;
                switch (i4 % 2 != 0 ? 'N' : '\b') {
                    case '\b':
                        return obj;
                    default:
                        Object obj2 = null;
                        obj2.hashCode();
                        throw null;
                }
            default:
                int i5 = m + Opcodes.LNEG;
                l = i5 % 128;
                int i6 = i5 % 2;
                return "";
        }
    }

    private static void k(int i2, String str, byte b2, Object[] objArr) {
        char[] cArr;
        int i3;
        char c;
        char c2;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = b;
        long j2 = 0;
        switch (cArr3 != null ? (char) 6 : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                break;
            default:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i4 = 0;
                while (i4 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr3[i4])};
                        Object obj = o.e.a.s.get(-1401577988);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(16 - (ExpandableListView.getPackedPositionForChild(0, 0) > j2 ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == j2 ? 0 : -1)), (char) Color.blue(0), 76 - (KeyEvent.getMaxKeyCode() >> 16));
                            byte length2 = (byte) $$a.length;
                            Object[] objArr3 = new Object[1];
                            o((byte) 0, length2, (byte) (length2 - 5), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj);
                        }
                        cArr4[i4] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i4++;
                        int i5 = $10 + 7;
                        $11 = i5 % 128;
                        int i6 = i5 % 2;
                        j2 = 0;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(e)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) View.resolveSize(0, 0), ExpandableListView.getPackedPositionGroup(0L) + 76);
                byte length3 = (byte) $$a.length;
                Object[] objArr5 = new Object[1];
                o((byte) 0, length3, (byte) (length3 - 5), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i2];
            if (i2 % 2 != 0) {
                i3 = i2 - 1;
                cArr5[i3] = (char) (cArr2[i3] - b2);
            } else {
                i3 = i2;
            }
            char c3 = '\t';
            switch (i3 <= 1) {
                case false:
                    int i7 = $10 + 91;
                    $11 = i7 % 128;
                    int i8 = i7 % 2;
                    mVar.b = 0;
                    while (true) {
                        switch (mVar.b < i3) {
                            case false:
                                c = c3;
                                break;
                            default:
                                mVar.e = cArr2[mVar.b];
                                mVar.a = cArr2[mVar.b + 1];
                                if (mVar.e == mVar.a) {
                                    int i9 = $10 + Opcodes.LUSHR;
                                    $11 = i9 % 128;
                                    if (i9 % 2 == 0) {
                                        cArr5[mVar.b] = (char) (mVar.e >>> b2);
                                        cArr5[mVar.b % 0] = (char) (mVar.a >>> b2);
                                    } else {
                                        cArr5[mVar.b] = (char) (mVar.e - b2);
                                        cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                    }
                                    c2 = c3;
                                } else {
                                    try {
                                        Object[] objArr6 = new Object[13];
                                        objArr6[12] = mVar;
                                        objArr6[11] = Integer.valueOf(charValue);
                                        objArr6[10] = mVar;
                                        objArr6[c3] = mVar;
                                        objArr6[8] = Integer.valueOf(charValue);
                                        objArr6[7] = mVar;
                                        objArr6[6] = mVar;
                                        objArr6[5] = Integer.valueOf(charValue);
                                        objArr6[4] = mVar;
                                        objArr6[3] = mVar;
                                        objArr6[2] = Integer.valueOf(charValue);
                                        objArr6[1] = mVar;
                                        objArr6[0] = mVar;
                                        Object obj3 = o.e.a.s.get(696901393);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 11, (char) (8856 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 324 - View.resolveSize(0, 0));
                                            byte b3 = (byte) 0;
                                            byte b4 = b3;
                                            Object[] objArr7 = new Object[1];
                                            o(b3, b4, (byte) (b4 - 1), objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(696901393, obj3);
                                        }
                                        if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                            try {
                                                Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                Object obj4 = o.e.a.s.get(1075449051);
                                                if (obj4 != null) {
                                                    c2 = '\t';
                                                } else {
                                                    Class cls4 = (Class) o.e.a.c(11 - View.MeasureSpec.getSize(0), (char) TextUtils.getCapsMode("", 0, 0), 66 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                                                    byte b5 = (byte) 0;
                                                    byte b6 = (byte) (b5 + 1);
                                                    Object[] objArr9 = new Object[1];
                                                    o(b5, b6, (byte) (-b6), objArr9);
                                                    c2 = '\t';
                                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(1075449051, obj4);
                                                }
                                                int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                int i10 = (mVar.d * charValue) + mVar.h;
                                                cArr5[mVar.b] = cArr3[intValue];
                                                cArr5[mVar.b + 1] = cArr3[i10];
                                            } catch (Throwable th2) {
                                                Throwable cause2 = th2.getCause();
                                                if (cause2 == null) {
                                                    throw th2;
                                                }
                                                throw cause2;
                                            }
                                        } else {
                                            c2 = '\t';
                                            if (mVar.c == mVar.d) {
                                                mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                int i11 = (mVar.c * charValue) + mVar.i;
                                                int i12 = (mVar.d * charValue) + mVar.h;
                                                cArr5[mVar.b] = cArr3[i11];
                                                cArr5[mVar.b + 1] = cArr3[i12];
                                            } else {
                                                int i13 = (mVar.c * charValue) + mVar.h;
                                                int i14 = (mVar.d * charValue) + mVar.i;
                                                cArr5[mVar.b] = cArr3[i13];
                                                cArr5[mVar.b + 1] = cArr3[i14];
                                            }
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                                mVar.b += 2;
                                c3 = c2;
                        }
                    }
                default:
                    c = '\t';
                    break;
            }
            int i15 = 0;
            while (true) {
                switch (i15 < i2 ? (char) 24 : c) {
                    case 24:
                        int i16 = $11 + 77;
                        $10 = i16 % 128;
                        int i17 = i16 % 2;
                        cArr5[i15] = (char) (cArr5[i15] ^ 13722);
                        i15++;
                    default:
                        String str2 = new String(cArr5);
                        int i18 = $10 + 43;
                        $11 = i18 % 128;
                        if (i18 % 2 != 0) {
                            objArr[0] = str2;
                            return;
                        } else {
                            Object obj5 = null;
                            obj5.hashCode();
                            throw null;
                        }
                }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x021b, code lost:
    
        if (r4 != false) goto L68;
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x00a0, code lost:
    
        r4 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0209, code lost:
    
        if (r4 != false) goto L68;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0220, code lost:
    
        r4 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x021e, code lost:
    
        r4 = 1;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:57:0x01f7. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 872
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.c.n(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
