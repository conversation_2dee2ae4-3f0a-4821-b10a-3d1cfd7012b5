package com.google.android.material.transformation;

import android.content.Context;
import android.util.AttributeSet;
import com.google.android.material.circularreveal.cardview.CircularRevealCardView;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\material\transformation\TransformationChildCard.smali */
public class TransformationChildCard extends CircularRevealCardView {
    public TransformationChildCard(Context context) {
        this(context, null);
    }

    public TransformationChildCard(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
}
