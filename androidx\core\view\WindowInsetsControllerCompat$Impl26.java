package androidx.core.view;

import android.view.Window;
import androidx.core.view.accessibility.AccessibilityEventCompat;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\view\WindowInsetsControllerCompat$Impl26.smali */
public class WindowInsetsControllerCompat$Impl26 extends WindowInsetsControllerCompat$Impl23 {
    WindowInsetsControllerCompat$Impl26(final Window window, final SoftwareKeyboardControllerCompat softwareKeyboardControllerCompat) {
        new WindowInsetsControllerCompat$Impl20(window, softwareKeyboardControllerCompat) { // from class: androidx.core.view.WindowInsetsControllerCompat$Impl23
            @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
            public boolean isAppearanceLightStatusBars() {
                return (this.mWindow.getDecorView().getSystemUiVisibility() & 8192) != 0;
            }

            @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
            public void setAppearanceLightStatusBars(boolean isLight) {
                if (isLight) {
                    unsetWindowFlag(AccessibilityEventCompat.TYPE_VIEW_TARGETED_BY_SCROLL);
                    setWindowFlag(Integer.MIN_VALUE);
                    setSystemUiFlag(8192);
                    return;
                }
                unsetSystemUiFlag(8192);
            }
        };
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public boolean isAppearanceLightNavigationBars() {
        return (this.mWindow.getDecorView().getSystemUiVisibility() & 16) != 0;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public void setAppearanceLightNavigationBars(boolean isLight) {
        if (isLight) {
            unsetWindowFlag(134217728);
            setWindowFlag(Integer.MIN_VALUE);
            setSystemUiFlag(16);
            return;
        }
        unsetSystemUiFlag(16);
    }
}
