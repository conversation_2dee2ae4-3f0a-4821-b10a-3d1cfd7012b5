package o.ad;

import com.esotericsoftware.asm.Opcodes;
import java.util.List;
import o.f.e;
import o.i.f;
import o.i.g;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ad\c.smali */
public final class c extends o.x.b {
    private static int d = 0;
    private static int j = 1;
    private final e a;
    private boolean b;
    private boolean c;
    private boolean e;

    private c(boolean z, List<g> list, e eVar) {
        super(z, list);
        this.a = eVar;
    }

    public final f d() {
        int i = (j + 104) - 1;
        int i2 = i % 128;
        d = i2;
        int i3 = i % 2;
        e eVar = this.a;
        switch (eVar == null) {
            case true:
                int i4 = (i2 + 72) - 1;
                int i5 = i4 % 128;
                j = i5;
                int i6 = i4 % 2;
                int i7 = (i5 & Opcodes.LUSHR) + (i5 | Opcodes.LUSHR);
                d = i7 % 128;
                switch (i7 % 2 != 0) {
                    case true:
                        int i8 = 13 / 0;
                        return null;
                    default:
                        return null;
                }
            default:
                return eVar.b();
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0035  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x0073  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final byte[] b() {
        /*
            r6 = this;
            int r0 = o.ad.c.j
            r1 = r0 | 15
            r2 = 1
            int r1 = r1 << r2
            r3 = r0 ^ 15
            int r1 = r1 - r3
            int r3 = r1 % 128
            o.ad.c.d = r3
            int r1 = r1 % 2
            r3 = 0
            if (r1 == 0) goto L14
            r1 = r3
            goto L15
        L14:
            r1 = r2
        L15:
            r4 = 0
            switch(r1) {
                case 0: goto L1e;
                default: goto L19;
            }
        L19:
            o.f.e r1 = r6.a
            if (r1 != 0) goto L30
            goto L2e
        L1e:
            o.f.e r1 = r6.a
            r5 = 54
            int r5 = r5 / r3
            if (r1 != 0) goto L27
            r1 = r2
            goto L28
        L27:
            r1 = r3
        L28:
            switch(r1) {
                case 1: goto L34;
                default: goto L2b;
            }
        L2b:
            goto L35
        L2c:
            r0 = move-exception
            throw r0
        L2e:
            r1 = r3
            goto L31
        L30:
            r1 = r2
        L31:
            switch(r1) {
                case 1: goto L35;
                default: goto L34;
            }
        L34:
            goto L73
        L35:
            o.f.e r0 = r6.a
            boolean r0 = r0.g()
            if (r0 == 0) goto L3f
            r3 = r2
            goto L40
        L3f:
        L40:
            switch(r3) {
                case 1: goto L4a;
                default: goto L43;
            }
        L43:
            o.f.e r0 = r6.a
            byte[] r0 = r0.d()
            return r0
        L4a:
            int r0 = o.ad.c.d
            int r1 = r0 + 44
            int r1 = r1 - r2
            int r3 = r1 % 128
            o.ad.c.j = r3
            int r1 = r1 % 2
            r1 = r0 ^ 89
            r0 = r0 & 89
            int r0 = r0 << r2
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.ad.c.j = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L67
            r0 = 33
            goto L69
        L67:
            r0 = 14
        L69:
            switch(r0) {
                case 33: goto L6d;
                default: goto L6c;
            }
        L6c:
            return r4
        L6d:
            r4.hashCode()     // Catch: java.lang.Throwable -> L71
            throw r4     // Catch: java.lang.Throwable -> L71
        L71:
            r0 = move-exception
            throw r0
        L73:
            r1 = r0 | 119(0x77, float:1.67E-43)
            int r1 = r1 << r2
            r0 = r0 ^ 119(0x77, float:1.67E-43)
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.ad.c.d = r0
            int r1 = r1 % 2
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.c.b():byte[]");
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x003a  */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0058  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String e() {
        /*
            r6 = this;
            int r0 = o.ad.c.d
            r1 = r0 ^ 77
            r2 = r0 & 77
            r3 = 1
            int r2 = r2 << r3
            int r1 = r1 + r2
            int r2 = r1 % 128
            o.ad.c.j = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L15
            r1 = 34
            goto L17
        L15:
            r1 = 56
        L17:
            r2 = 0
            r4 = 0
            switch(r1) {
                case 56: goto L1f;
                default: goto L1c;
            }
        L1c:
            o.f.e r1 = r6.a
            goto L2c
        L1f:
            o.f.e r1 = r6.a
            if (r1 != 0) goto L26
            r1 = 35
            goto L28
        L26:
            r1 = 10
        L28:
            switch(r1) {
                case 35: goto L3a;
                default: goto L2b;
            }
        L2b:
            goto L58
        L2c:
            r5 = 71
            int r5 = r5 / r4
            if (r1 != 0) goto L34
            r1 = 84
            goto L36
        L34:
            r1 = 73
        L36:
            switch(r1) {
                case 84: goto L3a;
                default: goto L39;
            }
        L39:
            goto L2b
        L3a:
            r1 = r0 | 51
            int r1 = r1 << r3
            r0 = r0 ^ 51
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.ad.c.j = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L4b
            r0 = 81
            goto L4d
        L4b:
            r0 = 44
        L4d:
            switch(r0) {
                case 81: goto L51;
                default: goto L50;
            }
        L50:
            goto L57
        L51:
            r0 = 36
            int r0 = r0 / r4
            goto L50
        L55:
            r0 = move-exception
            throw r0
        L57:
            return r2
        L58:
            o.f.e r0 = r6.a
            boolean r0 = r0.g()
            if (r0 == 0) goto L61
            goto L62
        L61:
            r4 = r3
        L62:
            switch(r4) {
                case 0: goto L6c;
                default: goto L65;
            }
        L65:
            o.f.e r0 = r6.a
            java.lang.String r0 = r0.c()
            goto L7b
        L6c:
            int r0 = o.ad.c.d
            r1 = r0 | 83
            int r1 = r1 << r3
            r0 = r0 ^ 83
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.ad.c.j = r0
            int r1 = r1 % 2
            return r2
        L7b:
            int r1 = o.ad.c.d
            r4 = r1 ^ 111(0x6f, float:1.56E-43)
            r1 = r1 & 111(0x6f, float:1.56E-43)
            int r1 = r1 << r3
            int r4 = r4 + r1
            int r1 = r4 % 128
            o.ad.c.j = r1
            int r4 = r4 % 2
            if (r4 != 0) goto L8e
            r1 = 82
            goto L90
        L8e:
            r1 = 29
        L90:
            switch(r1) {
                case 82: goto L94;
                default: goto L93;
            }
        L93:
            return r0
        L94:
            throw r2     // Catch: java.lang.Throwable -> L95
        L95:
            r0 = move-exception
            throw r0
        L97:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.c.e():java.lang.String");
    }

    public final byte[] a() {
        int i = j;
        int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
        int i3 = i2 % 128;
        d = i3;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                e eVar = this.a;
                switch (eVar == null ? (char) 27 : 'b') {
                    case 27:
                        int i4 = i3 + 61;
                        j = i4 % 128;
                        switch (i4 % 2 == 0 ? (char) 4 : 'B') {
                            case 'B':
                                return null;
                            default:
                                int i5 = 25 / 0;
                                return null;
                        }
                    default:
                        switch (eVar.g()) {
                            case true:
                                int i6 = j;
                                int i7 = (i6 & 71) + (i6 | 71);
                                d = i7 % 128;
                                int i8 = i7 % 2;
                                byte[] d2 = this.a.d();
                                int i9 = d;
                                int i10 = ((i9 | 33) << 1) - (i9 ^ 33);
                                j = i10 % 128;
                                int i11 = i10 % 2;
                                return d2;
                            default:
                                int i12 = d + 5;
                                j = i12 % 128;
                                int i13 = i12 % 2;
                                return null;
                        }
                }
        }
    }

    public final String g() {
        int i = d + 13;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        e eVar = this.a;
        switch (eVar != null) {
            case true:
                switch (eVar.g() ? (char) 5 : '\r') {
                    case 5:
                        int i4 = d + 97;
                        j = i4 % 128;
                        switch (i4 % 2 != 0) {
                            case false:
                                this.a.c();
                                throw null;
                            default:
                                String c = this.a.c();
                                int i5 = (j + 92) - 1;
                                d = i5 % 128;
                                switch (i5 % 2 != 0 ? '-' : '#') {
                                    case '-':
                                        int i6 = 87 / 0;
                                        return c;
                                    default:
                                        return c;
                                }
                        }
                    default:
                        return null;
                }
            default:
                int i7 = (i2 ^ 81) + ((i2 & 81) << 1);
                d = i7 % 128;
                int i8 = i7 % 2;
                return null;
        }
    }

    public final String f() {
        int i = d + 47;
        int i2 = i % 128;
        j = i2;
        switch (i % 2 == 0 ? 'N' : '%') {
            case 'N':
                throw null;
            default:
                e eVar = this.a;
                if (eVar == null) {
                    int i3 = (i2 ^ 27) + ((i2 & 27) << 1);
                    d = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            throw null;
                        default:
                            int i4 = i2 + Opcodes.DNEG;
                            d = i4 % 128;
                            switch (i4 % 2 != 0 ? (char) 26 : '\t') {
                                case '\t':
                                    return null;
                                default:
                                    throw null;
                            }
                    }
                }
                switch (!eVar.g()) {
                    case true:
                        int i5 = d + 9;
                        j = i5 % 128;
                        switch (i5 % 2 == 0 ? 'F' : '5') {
                            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                                throw null;
                            default:
                                return null;
                        }
                    default:
                        int i6 = (d + 44) - 1;
                        j = i6 % 128;
                        char c = i6 % 2 != 0 ? (char) 6 : '\t';
                        String j2 = this.a.j();
                        switch (c) {
                            case '\t':
                                int i7 = 34 / 0;
                                break;
                        }
                        int i8 = d;
                        int i9 = (i8 & 49) + (i8 | 49);
                        j = i9 % 128;
                        int i10 = i9 % 2;
                        return j2;
                }
        }
    }

    public static c d(e eVar) {
        c cVar = new c(true, null, eVar);
        int i = j;
        int i2 = (i & 47) + (i | 47);
        d = i2 % 128;
        switch (i2 % 2 != 0 ? '^' : (char) 25) {
            case Opcodes.DUP2_X2 /* 94 */:
                int i3 = 47 / 0;
                return cVar;
            default:
                return cVar;
        }
    }

    public static c i() {
        c cVar = new c(true, null, null);
        int i = d + 25;
        j = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    public static c d(List<g> list) {
        c cVar = new c(false, list, null);
        int i = d + 73;
        j = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    public final boolean h() {
        int i = d;
        int i2 = ((i | 3) << 1) - (i ^ 3);
        j = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.c;
        int i4 = (i & 55) + (i | 55);
        j = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final void j() {
        int i = j;
        int i2 = (i & 63) + (i | 63);
        int i3 = i2 % 128;
        d = i3;
        int i4 = i2 % 2;
        this.c = true;
        int i5 = (i3 & 13) + (i3 | 13);
        j = i5 % 128;
        int i6 = i5 % 2;
    }

    public final boolean m() {
        boolean z;
        int i = d;
        int i2 = (i ^ 19) + ((i & 19) << 1);
        int i3 = i2 % 128;
        j = i3;
        switch (i2 % 2 == 0) {
            case true:
                z = this.e;
                int i4 = 15 / 0;
                break;
            default:
                z = this.e;
                break;
        }
        int i5 = ((i3 | 95) << 1) - (i3 ^ 95);
        d = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0014. Please report as an issue. */
    public final void k() {
        int i = d;
        int i2 = (i ^ 3) + ((i & 3) << 1);
        j = i2 % 128;
        switch (i2 % 2 != 0) {
        }
        this.e = true;
        int i3 = i + 39;
        j = i3 % 128;
        int i4 = i3 % 2;
    }

    public final boolean o() {
        int i = j + 37;
        d = i % 128;
        switch (i % 2 != 0 ? '7' : (char) 19) {
            case '7':
                throw null;
            default:
                return this.b;
        }
    }

    public final void l() {
        int i = d;
        int i2 = (i & 73) + (i | 73);
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        this.b = true;
        int i5 = i3 + 59;
        d = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }
}
