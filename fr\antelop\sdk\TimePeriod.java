package fr.antelop.sdk;

import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\TimePeriod.smali */
public enum TimePeriod {
    Day("DAILY"),
    Week("WEEKLY"),
    Month("MONTHLY");

    private final String value;

    TimePeriod(String str) {
        this.value = str;
    }

    public final String getString() {
        return this.value;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    public static TimePeriod getPeriodFromString(String str) {
        char c;
        if (str == null) {
            return null;
        }
        switch (str.hashCode()) {
            case -1738378111:
                if (str.equals("WEEKLY")) {
                    c = 1;
                    break;
                }
                c = 65535;
                break;
            case 64808441:
                if (str.equals("DAILY")) {
                    c = 0;
                    break;
                }
                c = 65535;
                break;
            case 1954618349:
                if (str.equals("MONTHLY")) {
                    c = 2;
                    break;
                }
                c = 65535;
                break;
            default:
                c = 65535;
                break;
        }
        switch (c) {
            case 0:
                return Day;
            case 1:
                return Week;
            case 2:
                return Month;
            default:
                g.c();
                g.e("TimePeriod", "getPeriodFromString - Velocity Period not recognized : ".concat(String.valueOf(str)));
                return null;
        }
    }
}
