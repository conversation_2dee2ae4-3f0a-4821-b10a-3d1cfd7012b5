package fr.antelop.sdk.card.emvapplication;

import android.content.Context;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.Product;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.et.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\emvapplication\HceEmvApplication.smali */
public final class HceEmvApplication implements EmvApplication {
    private final c innerApplication;

    public HceEmvApplication(c cVar) {
        this.innerApplication = cVar;
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final String getId() {
        return this.innerApplication.n();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final Product getProduct() {
        return this.innerApplication.z().d();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final EmvApplicationType getType() {
        return this.innerApplication.e();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final EmvApplicationStatus getStatus() {
        return this.innerApplication.t();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final List<EmvApplicationActivationMethod> getActivationMethods() {
        return this.innerApplication.v();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final String getCardId() {
        return this.innerApplication.u();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final String getEmvApplicationGroupId() {
        return this.innerApplication.w();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final void selectActivationMethod(Context context, String str, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerApplication.c(context, str, antelopCallback);
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final EmvApplicationActivationMethod getSelectedActivationMethod() {
        return this.innerApplication.q();
    }

    @Override // fr.antelop.sdk.card.emvapplication.EmvApplication
    public final void submitActivationCode(Context context, String str, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerApplication.d(context, str, antelopCallback);
    }

    public final String getAid() {
        return this.innerApplication.h();
    }

    public final short getPriority() {
        return this.innerApplication.m();
    }

    public final short getAvailableCredentialsNumber() {
        return this.innerApplication.D();
    }

    public final byte[] getNextTransactionAtc() {
        return this.innerApplication.A();
    }

    public final byte[] getNextTransactionIdn() {
        return this.innerApplication.E();
    }

    public final String getTokenExternalId() {
        return this.innerApplication.f();
    }

    public final String getTokenLastDigits() {
        String I = this.innerApplication.I();
        if (I == null) {
            return "";
        }
        return I;
    }

    public final String toString() {
        return new StringBuilder("HceEmvApplication{id=").append(getId()).append(", product=").append(getProduct()).append(", type=").append(getType()).append(", status=").append(getStatus()).append(", activationMethod=").append(getActivationMethods() == null ? "" : getActivationMethods()).append(", cardId=").append(getCardId()).append(", emvApplicationGroupId=").append(getEmvApplicationGroupId() == null ? "" : getEmvApplicationGroupId()).append(", selectedActivationMethod=").append(getSelectedActivationMethod() == null ? "" : getSelectedActivationMethod().toString()).append(", aid=").append(getAid()).append(", priority=").append((int) getPriority()).append(", availableCredentialsNumber=").append((int) getAvailableCredentialsNumber()).append(", tokenExternalId=").append(getTokenExternalId() != null ? getTokenExternalId() : "").append(", tokenLastDigits").append(getTokenLastDigits()).append('}').toString();
    }
}
