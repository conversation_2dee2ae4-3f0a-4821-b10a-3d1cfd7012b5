package bc.org.bouncycastle;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.o7;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\LICENSE.smali */
public class LICENSE {
    public static final String licenseText = "Copyright (c) 2000-2023 The Legion of the Bouncy Castle Inc. (https://www.bouncycastle.org) " + o7.a() + o7.a() + "Permission is hereby granted, free of charge, to any person obtaining a copy of this software " + o7.a() + "and associated documentation files (the \"Software\"), to deal in the Software without restriction, " + o7.a() + "including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, " + o7.a() + "and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so," + o7.a() + "subject to the following conditions:" + o7.a() + o7.a() + "The above copyright notice and this permission notice shall be included in all copies or substantial" + o7.a() + "portions of the Software." + o7.a() + o7.a() + "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED," + o7.a() + "INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR" + o7.a() + "PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE" + o7.a() + "LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR" + o7.a() + "OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER" + o7.a() + "DEALINGS IN THE SOFTWARE.";

    public static void main(String[] strArr) {
        System.out.println(licenseText);
    }
}
