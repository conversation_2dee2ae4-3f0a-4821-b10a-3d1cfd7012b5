package kotlin.jvm.functions;

import kotlin.Function;
import kotlin.Metadata;

/* compiled from: Functions.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u0000*\u0006\b\u0001\u0010\u0002 \u0000*\u0006\b\u0002\u0010\u0003 \u0000*\u0006\b\u0003\u0010\u0004 \u00012\b\u0012\u0004\u0012\u0002H\u00040\u0005J&\u0010\u0006\u001a\u00028\u00032\u0006\u0010\u0007\u001a\u00028\u00002\u0006\u0010\b\u001a\u00028\u00012\u0006\u0010\t\u001a\u00028\u0002H¦\u0002¢\u0006\u0002\u0010\n¨\u0006\u000b"}, d2 = {"Lkotlin/jvm/functions/Function3;", "P1", "P2", "P3", "R", "Lkotlin/Function;", "invoke", "p1", "p2", "p3", "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\functions\Function3.smali */
public interface Function3<P1, P2, P3, R> extends Function<R> {
    R invoke(P1 p1, P2 p2, P3 p3);
}
