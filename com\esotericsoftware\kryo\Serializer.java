package com.esotericsoftware.kryo;

import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\Serializer.smali */
public abstract class Serializer<T> {
    private boolean acceptsNull;
    private boolean immutable;

    public abstract T read(Kryo kryo, Input input, Class<? extends T> cls);

    public abstract void write(Kryo kryo, Output output, T t);

    public Serializer() {
    }

    public Serializer(boolean acceptsNull) {
        this.acceptsNull = acceptsNull;
    }

    public Serializer(boolean acceptsNull, boolean immutable) {
        this.acceptsNull = acceptsNull;
        this.immutable = immutable;
    }

    public boolean getAcceptsNull() {
        return this.acceptsNull;
    }

    public void setAcceptsNull(boolean acceptsNull) {
        this.acceptsNull = acceptsNull;
    }

    public boolean isImmutable() {
        return this.immutable;
    }

    public void setImmutable(boolean immutable) {
        this.immutable = immutable;
    }

    public T copy(Kryo kryo, T original) {
        if (isImmutable()) {
            return original;
        }
        throw new KryoException("Serializer does not support copy: " + getClass().getName());
    }
}
