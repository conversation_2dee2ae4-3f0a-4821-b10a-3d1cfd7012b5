package o.f;

import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import kotlin.io.encoding.Base64;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\e.smali */
public abstract class e {
    private final d a;
    private final Date d;
    private static int c = 0;
    private static int b = 1;

    public abstract byte[] a();

    public abstract o.i.f b();

    public abstract String c();

    public abstract byte[] d();

    public abstract byte[] e();

    public abstract String j();

    public e(d dVar, Date date) {
        this.a = dVar;
        this.d = date;
    }

    public final d i() {
        d dVar;
        int i = c;
        int i2 = i + 27;
        b = i2 % 128;
        switch (i2 % 2 == 0 ? 'B' : (char) 17) {
            case 'B':
                dVar = this.a;
                int i3 = 95 / 0;
                break;
            default:
                dVar = this.a;
                break;
        }
        int i4 = i + 81;
        b = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return dVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final boolean a(Integer num, Date date) {
        int i = b;
        int i2 = (i & 83) + (i | 83);
        c = i2 % 128;
        int i3 = i2 % 2;
        switch (num != null) {
            case false:
                int i4 = (i ^ 23) + ((i & 23) << 1);
                int i5 = i4 % 128;
                c = i5;
                int i6 = i4 % 2;
                int i7 = (i5 + Opcodes.IAND) - 1;
                b = i7 % 128;
                switch (i7 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return false;
                }
            default:
                return date.after(new Date(this.d.getTime() + (num.intValue() * 1000)));
        }
    }

    public final boolean g() {
        int i = c + Opcodes.LSHL;
        b = i % 128;
        switch (i % 2 == 0 ? (char) 20 : (char) 18) {
            case 18:
                return o.a.c(b(), o.i.f.d, o.i.f.e);
            default:
                o.i.f b2 = b();
                o.i.f[] fVarArr = new o.i.f[3];
                fVarArr[1] = o.i.f.d;
                fVarArr[0] = o.i.f.e;
                return o.a.c(b2, fVarArr);
        }
    }

    public final int hashCode() {
        int i = c;
        int i2 = (i ^ Opcodes.LSHL) + ((i & Opcodes.LSHL) << 1);
        b = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = b;
        int i5 = (i4 ^ 41) + ((i4 & 41) << 1);
        c = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 27 : '\b') {
            case '\b':
                return hashCode;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = b;
        int i2 = ((i | 19) << 1) - (i ^ 19);
        c = i2 % 128;
        switch (i2 % 2 != 0 ? '9' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                return super.equals(obj);
            default:
                super.equals(obj);
                throw null;
        }
    }

    public final String toString() {
        String obj;
        int i = b + 95;
        c = i % 128;
        switch (i % 2 != 0 ? 'a' : 'L') {
            case Base64.mimeLineLength /* 76 */:
                obj = super.toString();
                break;
            default:
                obj = super.toString();
                int i2 = 8 / 0;
                break;
        }
        int i3 = b;
        int i4 = (i3 & 19) + (i3 | 19);
        c = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return obj;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    protected final void finalize() throws Throwable {
        int i = c;
        int i2 = (i ^ Opcodes.DDIV) + ((i & Opcodes.DDIV) << 1);
        b = i2 % 128;
        boolean z = i2 % 2 == 0;
        super.finalize();
        switch (z) {
            case true:
                int i3 = 93 / 0;
                break;
        }
        int i4 = b;
        int i5 = (i4 & Opcodes.LSHL) + (i4 | Opcodes.LSHL);
        c = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return;
            default:
                int i6 = 76 / 0;
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\e$d.smali */
    public static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static long a;
        public static final d b;
        public static final d c;
        private static int d;
        private static final /* synthetic */ d[] e;
        private static int f;
        private static int i;
        private static char j;

        static void c() {
            j = (char) 15156;
            d = 161105445;
            a = 6565854932352255525L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(short r8, byte r9, short r10, java.lang.Object[] r11) {
            /*
                int r8 = r8 + 99
                int r9 = r9 * 4
                int r9 = 1 - r9
                byte[] r0 = o.f.e.d.$$a
                int r10 = r10 * 3
                int r10 = r10 + 4
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L18
                r8 = r9
                r3 = r1
                r5 = r2
                r1 = r0
                r0 = r11
                r11 = r10
                goto L33
            L18:
                r3 = r2
            L19:
                byte r4 = (byte) r8
                int r5 = r3 + 1
                r1[r3] = r4
                if (r5 != r9) goto L28
                java.lang.String r8 = new java.lang.String
                r8.<init>(r1, r2)
                r11[r2] = r8
                return
            L28:
                r3 = r0[r10]
                r6 = r9
                r9 = r8
                r8 = r6
                r7 = r11
                r11 = r10
                r10 = r3
                r3 = r1
                r1 = r0
                r0 = r7
            L33:
                int r9 = r9 + r10
                int r10 = r11 + 1
                r11 = r0
                r0 = r1
                r1 = r3
                r3 = r5
                r6 = r9
                r9 = r8
                r8 = r6
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.f.e.d.h(short, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{2, Base64.padSymbol, -41, 17};
            $$b = Opcodes.GETSTATIC;
        }

        private d(String str, int i2) {
        }

        private static /* synthetic */ d[] d() {
            int i2 = i + 47;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            d[] dVarArr = {c, b};
            int i5 = i3 + 65;
            i = i5 % 128;
            switch (i5 % 2 != 0 ? (char) 7 : 'R') {
                case 7:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVarArr;
            }
        }

        public static d valueOf(String str) {
            int i2 = i + 21;
            f = i2 % 128;
            int i3 = i2 % 2;
            d dVar = (d) Enum.valueOf(d.class, str);
            int i4 = f + Opcodes.DSUB;
            i = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    int i5 = 72 / 0;
                    return dVar;
                default:
                    return dVar;
            }
        }

        public static d[] values() {
            int i2 = f + 109;
            i = i2 % 128;
            int i3 = i2 % 2;
            d[] dVarArr = (d[]) e.clone();
            int i4 = i + 85;
            f = i4 % 128;
            switch (i4 % 2 == 0 ? 'B' : 'Y') {
                case 'B':
                    throw null;
                default:
                    return dVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            f = 1;
            c();
            Object[] objArr = new Object[1];
            g((-1) - ExpandableListView.getPackedPositionChild(0L), "а\uf657\uf3c0삎㵣嫦\uf40d驗", (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), "崹澈僎㠪", "\u0000\u0000\u0000\u0000", objArr);
            c = new d(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            g(ViewConfiguration.getMinimumFlingVelocity() >> 16, "\uda6e巣瀪뫶抏㷋ą꜆", (char) View.resolveSize(0, 0), "됷㫌햖ﹱ", "\u0000\u0000\u0000\u0000", objArr2);
            b = new d(((String) objArr2[0]).intern(), 1);
            e = d();
            int i2 = f + 49;
            i = i2 % 128;
            switch (i2 % 2 != 0 ? '*' : 'Y') {
                case '*':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void g(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 674
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.f.e.d.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
        }
    }
}
