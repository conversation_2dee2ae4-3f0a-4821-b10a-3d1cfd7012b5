package o.de;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import java.util.Locale;
import o.ee.g;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\j.smali */
public final class j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int c;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        c();
        Color.rgb(0, 0, 0);
        int i = c + 97;
        d = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        a = new int[]{-1750263894, 433481715, 469052165, -1970927853, 1509384452, 288780236, -1321913442, -897963792, 547324531, 1860514578, 789119496, -1751659478, -1528506217, -2015938804, 406605799, -1497834603, -322266166, 1281226218};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(byte r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = 116 - r9
            int r7 = r7 + 4
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r0 = o.de.j.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L14
            r9 = r8
            r3 = r9
            r4 = r2
            r8 = r7
            goto L2c
        L14:
            r3 = r2
        L15:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L24
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L24:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2c:
            int r3 = -r3
            int r7 = r7 + r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.j.f(byte, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{84, 72, 115, -24};
        $$b = 33;
    }

    /* renamed from: o.de.j$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\j$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int b;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            b = 0;
            e = 1;
            int[] iArr = new int[f.values().length];
            d = iArr;
            try {
                iArr[f.b.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[f.c.ordinal()] = 2;
                int i = b;
                int i2 = (i & 65) + (i | 65);
                e = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[f.j.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[f.f.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                d[f.m.ordinal()] = 5;
                int i4 = e;
                int i5 = (i4 ^ 25) + ((i4 & 25) << 1);
                b = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                d[f.k.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                d[f.l.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
            try {
                d[f.n.ordinal()] = 8;
            } catch (NoSuchFieldError e9) {
            }
            try {
                d[f.f54o.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
            try {
                d[f.p.ordinal()] = 10;
            } catch (NoSuchFieldError e11) {
            }
            try {
                d[f.q.ordinal()] = 11;
            } catch (NoSuchFieldError e12) {
            }
            try {
                d[f.s.ordinal()] = 12;
                int i6 = e;
                int i7 = (i6 ^ Opcodes.DSUB) + ((i6 & Opcodes.DSUB) << 1);
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e13) {
            }
            try {
                d[f.t.ordinal()] = 13;
                int i9 = b + 79;
                e = i9 % 128;
                if (i9 % 2 != 0) {
                }
            } catch (NoSuchFieldError e14) {
            }
            try {
                d[f.r.ordinal()] = 14;
            } catch (NoSuchFieldError e15) {
            }
            try {
                d[f.x.ordinal()] = 15;
            } catch (NoSuchFieldError e16) {
            }
            try {
                d[f.y.ordinal()] = 16;
            } catch (NoSuchFieldError e17) {
            }
            try {
                d[f.w.ordinal()] = 17;
            } catch (NoSuchFieldError e18) {
            }
            try {
                d[f.u.ordinal()] = 18;
            } catch (NoSuchFieldError e19) {
            }
            try {
                d[f.e.ordinal()] = 19;
                int i10 = b;
                int i11 = ((i10 | 83) << 1) - (i10 ^ 83);
                e = i11 % 128;
                if (i11 % 2 == 0) {
                }
            } catch (NoSuchFieldError e20) {
            }
            try {
                d[f.h.ordinal()] = 20;
            } catch (NoSuchFieldError e21) {
            }
            try {
                d[f.i.ordinal()] = 21;
                int i12 = (e + 34) - 1;
                b = i12 % 128;
                if (i12 % 2 != 0) {
                }
            } catch (NoSuchFieldError e22) {
            }
            try {
                d[f.A.ordinal()] = 22;
            } catch (NoSuchFieldError e23) {
            }
            try {
                d[f.z.ordinal()] = 23;
                int i13 = e;
                int i14 = (i13 ^ 41) + ((i13 & 41) << 1);
                b = i14 % 128;
                if (i14 % 2 != 0) {
                }
            } catch (NoSuchFieldError e24) {
            }
            try {
                d[f.d.ordinal()] = 24;
            } catch (NoSuchFieldError e25) {
            }
            try {
                d[f.a.ordinal()] = 25;
            } catch (NoSuchFieldError e26) {
            }
            try {
                d[f.g.ordinal()] = 26;
                int i15 = (e + 86) - 1;
                b = i15 % 128;
                int i16 = i15 % 2;
            } catch (NoSuchFieldError e27) {
            }
            try {
                d[f.v.ordinal()] = 27;
            } catch (NoSuchFieldError e28) {
            }
            try {
                d[f.B.ordinal()] = 28;
            } catch (NoSuchFieldError e29) {
            }
        }
    }

    public static long a(Context context, o.ei.c cVar, f fVar) throws i {
        long j;
        g.c();
        Object[] objArr = new Object[1];
        e(new int[]{1602409001, -168137099, -1171545604, -756887594, -2043053000, 571471827, 1675831625, -1169479698, -444359606, -1300859698, -214038555, 436097369}, 21 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        e(new int[]{-886213590, 1657362497, 812846449, 1185830869, -1126435782, 1957244165, -1556097214, 1211787950, -1334182101, -38510869, 2105083252, 531326091, -1129553099, 362114686, 1678179786, 302020017, 1196262164, -312736895, 297914941, -1051185671, -1480236902, -1518612558, -197398268, -1614313465}, (ViewConfiguration.getFadingEdgeLength() >> 16) + 45, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(fVar.toString()).toString());
        Object obj = null;
        switch (AnonymousClass1.d[fVar.ordinal()]) {
            case 1:
                if (cVar.e().d().j() == -1) {
                    return 1L;
                }
                return Math.max(1, r0 / 1000);
            case 2:
                return 1L;
            case 3:
                return 1L;
            case 4:
                return cVar.e().d().e() / 1000;
            case 5:
                return 1L;
            case 6:
                try {
                    int b = cVar.e().d().b();
                    Date d2 = cVar.a().f().d();
                    if (d2 != null && d2.after(new Date())) {
                        long time = (d2.getTime() - new Date().getTime()) / 1000;
                        switch (b != -1 ? 'F' : '_') {
                            case Opcodes.SWAP /* 95 */:
                                return time;
                            default:
                                int i = c + 27;
                                d = i % 128;
                                if (i % 2 == 0) {
                                    return Math.min(b, time);
                                }
                                Math.min(b, time);
                                throw null;
                        }
                    }
                    if (b != -1) {
                        int i2 = c + 81;
                        d = i2 % 128;
                        int i3 = i2 % 2;
                        return b;
                    }
                } catch (i e) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    e(new int[]{1602409001, -168137099, -1171545604, -756887594, -2043053000, 571471827, 1675831625, -1169479698, -444359606, -1300859698, -214038555, 436097369}, ImageFormat.getBitsPerPixel(0) + 22, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    e(new int[]{-2113865416, 2101010687, -444359606, -1300859698, 2126155011, 1067660456, 339663501, -940409304, 1169879246, -1357953570, 1400717930, -1337153380, -705300082, -419923946, 398802369, -1730386772, 1975285150, 508953541, 1297985699, 1194364204, -1886715441, -1126143280, -1621856187, 1790414674, 860155787, 1743839269, -197398268, -1614313465}, (-16777163) - Color.rgb(0, 0, 0), objArr4);
                    g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(e.getMessage()).toString());
                    return -1L;
                }
                break;
            case 7:
                return 1L;
            case 8:
                return 120L;
            case 9:
                return 1L;
            case 10:
                return 1L;
            case 11:
                return 1L;
            case 12:
                return 30L;
            case 13:
                return 30L;
            case 14:
                return 1L;
            case 15:
                return 1L;
            case 16:
                return 1L;
            case 17:
                return 1L;
            case 18:
                return 1L;
            case 19:
                return 1L;
            case 20:
                return 120L;
            case 21:
                return 120L;
            case 22:
                return 1L;
            case 23:
                return 1L;
            case 24:
            case 25:
            case 26:
            case 27:
                o.b.c.e(cVar, context);
                Long g = cVar.e().g();
                int b2 = cVar.e().d().b();
                Date d3 = cVar.a().f().d();
                if (d3 == null || !d3.after(new Date())) {
                    switch (b2 != -1) {
                        case false:
                            j = -1;
                            break;
                        default:
                            j = b2;
                            break;
                    }
                } else {
                    j = (d3.getTime() - new Date().getTime()) / 1000;
                    if (b2 != -1) {
                        int i4 = c + 3;
                        d = i4 % 128;
                        if (i4 % 2 != 0) {
                            Math.min(b2, j);
                            obj.hashCode();
                            throw null;
                        }
                        j = Math.min(b2, j);
                    }
                }
                g.c();
                Object[] objArr5 = new Object[1];
                e(new int[]{1602409001, -168137099, -1171545604, -756887594, -2043053000, 571471827, 1675831625, -1169479698, -444359606, -1300859698, -214038555, 436097369}, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 20, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Locale a2 = o.ee.j.a();
                Object[] objArr6 = new Object[1];
                e(new int[]{-2113865416, 2101010687, -444359606, -1300859698, 1174011953, -298915329, 2050624334, -514068982, -385311039, 458951015, 45788110, 1198188401, 1527154272, -807279360, -1803803441, 430783010, 1262481664, -834148632, -1327672433, 1751540900, -129383116, 968679846, -385311039, 458951015, -260167010, -616409441, 1746716181, 1468670797, 838150998, -1421434947, -1939719712, -1685163896, -673479299, -1345668680, -1126435782, 1957244165, -1556097214, 1211787950, -469639330, -2108902375, -929045964, 890095226, 258555800, -1014695124}, TextUtils.lastIndexOf("", '0') + 88, objArr6);
                g.d(intern3, String.format(a2, ((String) objArr6[0]).intern(), new Date(g.longValue() * 1000), Long.valueOf(j)));
                long max = j != -1 ? Math.max(1, ((int) (new Date((g.longValue() + j) * 1000).getTime() - new Date().getTime())) / 1000) : -1L;
                g.c();
                Object[] objArr7 = new Object[1];
                e(new int[]{1602409001, -168137099, -1171545604, -756887594, -2043053000, 571471827, 1675831625, -1169479698, -444359606, -1300859698, -214038555, 436097369}, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 20, objArr7);
                String intern4 = ((String) objArr7[0]).intern();
                Locale a3 = o.ee.j.a();
                Object[] objArr8 = new Object[1];
                e(new int[]{-2113865416, 2101010687, -444359606, -1300859698, 1174011953, -298915329, 2050624334, -514068982, -385311039, 458951015, 45788110, 1198188401, 1527154272, -807279360, -578539207, -841421539, 876958363, 1961653178, 1129332050, 1452164373, -202498454, 1373435189, -571981634, -1976278477, 1344086510, -1045058272, -912464799, 719272043, -1707062506, -343271560, -908083361, -135972402}, 64 - View.MeasureSpec.getMode(0), objArr8);
                g.d(intern4, String.format(a3, ((String) objArr8[0]).intern(), Long.valueOf(max)));
                return max;
            case 28:
                return 1L;
            default:
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr9 = new Object[1];
                e(new int[]{-256682898, 1920486735, -302652233, -1317836090, 2072396826, -976087257, -1262916280, -2015410917, -1014202938, -600893148, -861886337, 1641804055}, Drawable.resolveOpacity(0, 0) + 24, objArr9);
                throw new i(sb3.append(((String) objArr9[0]).intern()).append(fVar).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void e(int[] r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 888
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.j.e(int[], int, java.lang.Object[]):void");
    }
}
