package o.ed;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\e.smali */
public final class e {
    private final boolean a;
    private final String b;
    private final CustomerAuthenticatedProcessActivityCallback c;
    private final String d;
    private final String e;
    private static int h = 0;
    private static int f = 1;

    public e(String str, String str2, String str3, boolean z, CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.e = str;
        this.b = str2;
        this.a = z;
        this.d = str3;
        this.c = customerAuthenticatedProcessActivityCallback;
    }

    public final String a() {
        int i = h;
        int i2 = (i ^ 75) + ((i & 75) << 1);
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        String str = this.e;
        int i5 = (i3 ^ Opcodes.LSUB) + ((i3 & Opcodes.LSUB) << 1);
        h = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String e() {
        int i = f;
        int i2 = (i & 93) + (i | 93);
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        String str = this.b;
        int i5 = (i3 & 13) + (i3 | 13);
        f = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String b() {
        int i = h;
        int i2 = i + 109;
        f = i2 % 128;
        int i3 = i2 % 2;
        String str = this.d;
        int i4 = (i ^ 3) + ((i & 3) << 1);
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return str;
        }
    }

    public final boolean c() {
        int i = f;
        int i2 = (i ^ 91) + ((i & 91) << 1);
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '2' : (char) 27) {
            case 27:
                boolean z = this.a;
                int i3 = (i ^ 23) + ((i & 23) << 1);
                h = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return z;
                    default:
                        int i4 = 21 / 0;
                        return z;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final CustomerAuthenticatedProcessActivityCallback d() {
        int i = (f + 74) - 1;
        h = i % 128;
        switch (i % 2 != 0 ? 'Q' : 'P') {
            case 'P':
                return this.c;
            default:
                throw null;
        }
    }
}
