package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l1.smali */
class l1 extends InputStream {
    private boolean C = true;
    private int L = 0;
    private e R;
    private final g0 b;
    private InputStream u0;
    private final boolean x;

    l1(g0 g0Var, boolean z) {
        this.b = g0Var;
        this.x = z;
    }

    private e a() throws IOException {
        h a = this.b.a();
        if (a == null) {
            if (!this.x || this.L == 0) {
                return null;
            }
            throw new IOException("expected octet-aligned bitstring, but found padBits: " + this.L);
        }
        if (!(a instanceof e)) {
            throw new IOException("unknown object encountered: " + a.getClass());
        }
        if (this.L == 0) {
            return (e) a;
        }
        throw new IOException("only the last nested bitstring can have padding");
    }

    int b() {
        return this.L;
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i, int i2) throws IOException {
        int i3 = 0;
        if (this.u0 == null) {
            if (!this.C) {
                return -1;
            }
            e a = a();
            this.R = a;
            if (a == null) {
                return -1;
            }
            this.C = false;
            this.u0 = a.b();
        }
        while (true) {
            int read = this.u0.read(bArr, i + i3, i2 - i3);
            if (read >= 0) {
                i3 += read;
                if (i3 == i2) {
                    return i3;
                }
            } else {
                this.L = this.R.d();
                e a2 = a();
                this.R = a2;
                if (a2 == null) {
                    this.u0 = null;
                    if (i3 < 1) {
                        return -1;
                    }
                    return i3;
                }
                this.u0 = a2.b();
            }
        }
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        if (this.u0 == null) {
            if (!this.C) {
                return -1;
            }
            e a = a();
            this.R = a;
            if (a == null) {
                return -1;
            }
            this.C = false;
            this.u0 = a.b();
        }
        while (true) {
            int read = this.u0.read();
            if (read >= 0) {
                return read;
            }
            this.L = this.R.d();
            e a2 = a();
            this.R = a2;
            if (a2 == null) {
                this.u0 = null;
                return -1;
            }
            this.u0 = a2.b();
        }
    }
}
