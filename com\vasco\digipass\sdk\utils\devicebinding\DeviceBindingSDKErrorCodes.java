package com.vasco.digipass.sdk.utils.devicebinding;

import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0010\bÆ\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T¢\u0006\u0002\n\u0000¨\u0006\u0014"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDKErrorCodes;", "", "()V", "AUTHENTICATION_CANCELLED", "", "BIOMETRIC_AUTH_NOT_ENROLLED_ERROR", "BIOMETRIC_KEY_INVALIDATED", "BIOMETRIC_NO_HARDWARE_ERROR", "BIOMETRIC_UNSPECIFIED_ERROR", "CONTEXT_NULL", "HMAC_KEYGEN_ERROR", "INTERNAL_ERROR", "LEGACY_DECRYPTION_ERROR", "LEGACY_ENCRYPTION_ERROR", "PARAMETERS_NULL", "PERMISSION_DENIED", "SALT_NULL", "SALT_TOO_SHORT", "UNIQUE_DATA_UNDEFINED", "USER_NOT_AUTHENTICATED", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingSDKErrorCodes.smali */
public final class DeviceBindingSDKErrorCodes {
    public static final int AUTHENTICATION_CANCELLED = -4414;
    public static final int BIOMETRIC_AUTH_NOT_ENROLLED_ERROR = -4410;
    public static final int BIOMETRIC_KEY_INVALIDATED = -4415;
    public static final int BIOMETRIC_NO_HARDWARE_ERROR = -4411;
    public static final int BIOMETRIC_UNSPECIFIED_ERROR = -4412;
    public static final int CONTEXT_NULL = -4401;
    public static final int HMAC_KEYGEN_ERROR = -4409;
    public static final DeviceBindingSDKErrorCodes INSTANCE = new DeviceBindingSDKErrorCodes();
    public static final int INTERNAL_ERROR = -4400;
    public static final int LEGACY_DECRYPTION_ERROR = -4407;
    public static final int LEGACY_ENCRYPTION_ERROR = -4408;
    public static final int PARAMETERS_NULL = -4406;
    public static final int PERMISSION_DENIED = -4402;
    public static final int SALT_NULL = -4403;
    public static final int SALT_TOO_SHORT = -4404;
    public static final int UNIQUE_DATA_UNDEFINED = -4405;
    public static final int USER_NOT_AUTHENTICATED = -4413;

    private DeviceBindingSDKErrorCodes() {
    }
}
