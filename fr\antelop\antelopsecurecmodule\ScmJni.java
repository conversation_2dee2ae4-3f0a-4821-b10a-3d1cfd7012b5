package fr.antelop.antelopsecurecmodule;

import android.content.Context;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelopsecurecmodule\ScmJni.smali */
public final class ScmJni {
    private static boolean c;
    private final Context mContext;

    private native byte[] m(int i, int i2, byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[][] bArr5);

    private native byte[] p1(byte[] bArr, int i, int i2, int i3, byte[] bArr2, boolean z);

    private native byte[] p3(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, int i, int i2, int i3, int i4, byte[] bArr5, boolean z);

    private native int spn(boolean z, String str);

    static {
        g.c();
        g.d("ScmJni", "LoadLibrary - Before");
        System.loadLibrary("scm");
        g.c();
        g.d("ScmJni", "LoadLibrary - After");
    }

    public ScmJni(Context context) throws IllegalArgumentException {
        this.mContext = context;
        if (!c) {
            g.c();
            g.d("ScmJni", new StringBuilder("setPackageName() - before : ").append(context).toString());
            c = true;
            spn(false, context.getApplicationInfo().dataDir);
            g.c();
            g.d("ScmJni", "setPackageName() - after");
        }
    }

    public final void b() {
        c = false;
        spn(true, "");
    }

    public final byte[] a(int i, int i2, byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[][] bArr5) {
        if (i2 == 8) {
            c = false;
        }
        return m(i, i2, bArr, bArr2, bArr3, bArr4, bArr5);
    }

    public final byte[] d(byte[] bArr) {
        return p1(null, 0, 14, 2, bArr, false);
    }
}
