package o.bu;

import android.content.Context;
import android.graphics.ImageFormat;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bu\e.smali */
public final class e extends o.bs.d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static long b;
    private static final Object c;
    private static int d;
    private static c e;
    private static int g;
    private static int i;

    static void c() {
        a = (char) 5725;
        d = 161105445;
        b = 6565854932352255525L;
    }

    private static void h(byte b2, int i2, byte b3, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = 1 - (b3 * 2);
        int i4 = i2 + 99;
        int i5 = 3 - (b2 * 4);
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            i4 = i5 + i4;
            i5 = i5;
        }
        while (true) {
            int i8 = i5 + 1;
            i6++;
            bArr2[i6] = (byte) i4;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            } else {
                i4 += bArr[i8];
                i5 = i8;
            }
        }
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = 236;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        g = 1;
        c();
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getZoomControlsTimeout();
        c = new Object();
        int i2 = g + 9;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static c c(Context context) {
        c bVar;
        synchronized (c) {
            c cVar = e;
            if (cVar != null) {
                return cVar;
            }
            o.bs.c e2 = e(context);
            if (e2 == null) {
                g.c();
                Object[] objArr = new Object[1];
                f(ImageFormat.getBitsPerPixel(0) + 418840632, "\ue2c4퀅㊂뇜췞颽䠺ﳩ\uf366爘₨⡓\uefcd咟\u1af2伐賿㊝꺨ꗠ勹\ufe6d\uf3dc", (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "㟝\uf700쌘薹", "\u0000\u0000\u0000\u0000", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 54260245, "哌\udc5dἮＫ䃣\ue55e륺끃驣㞲⬛ὺ떊䧠赝贐\uf24d盁脓\udbed몑㋓\udfdd卆\ue793睏崃涽㘗蚯쯚᠍쭀䈿꺑闚\uf1e0䕉衝ﵞ镈⾔垭ኜ粷", (char) (View.combineMeasuredStates(0, 0) + 18783), "ᗩ㯲弃䝉", "\u0000\u0000\u0000\u0000", objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                return null;
            }
            if (e2 instanceof o.bs.b) {
                bVar = new a((o.bs.b) e2);
            } else if (e2 instanceof o.bs.a) {
                bVar = new b((o.bs.a) e2);
            } else {
                g.c();
                Object[] objArr3 = new Object[1];
                f(View.resolveSize(0, 0) + 418840631, "\ue2c4퀅㊂뇜췞颽䠺ﳩ\uf366爘₨⡓\uefcd咟\u1af2伐賿㊝꺨ꗠ勹\ufe6d\uf3dc", (char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1), "㟝\uf700쌘薹", "\u0000\u0000\u0000\u0000", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f(KeyEvent.keyCodeFromString(""), "褗꜉\uf77eጒႁ宴⽭蚇༼⬠姝\u0873䟨髖\uf7d7㚺➿䝋嬟\ue1e8\ue04e읰\ufff6⠻乗胍\udea4⤃躡⣀큳曃녛䛑\uf041惋澺㾳츋ꗆ⸢\ude95\ue0bf륌뱌䜿療\ue23dꪌ瀡쥬༠캽ü", (char) (62735 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), "≧瓥๎⋵", "\u0000\u0000\u0000\u0000", objArr4);
                g.e(intern2, ((String) objArr4[0]).intern());
                return null;
            }
            e = bVar;
            g.c();
            Object[] objArr5 = new Object[1];
            f(ExpandableListView.getPackedPositionChild(0L) + 418840632, "\ue2c4퀅㊂뇜췞颽䠺ﳩ\uf366爘₨⡓\uefcd咟\u1af2伐賿㊝꺨ꗠ勹\ufe6d\uf3dc", (char) ((-1) - TextUtils.lastIndexOf("", '0')), "㟝\uf700쌘薹", "\u0000\u0000\u0000\u0000", objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            f(ExpandableListView.getPackedPositionGroup(0L), "嗽협龅婟\ude2c㇑\uef20㜔䲀鵀ᆌी껷\u0ef8䳘ʲŅ믻\ue4da菇\udc4f戙婖ጩ戜ᓍ췔蠞ן䝏ඟ\ue9a1㯭\ud8d2現鶢㫼ኂ긓뚗횾䟖", (char) (19187 - View.combineMeasuredStates(0, 0)), "酞孢\uf3ad捊", "\u0000\u0000\u0000\u0000", objArr6);
            g.d(intern3, ((String) objArr6[0]).intern());
            return e;
        }
    }

    private static void f(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] charArray;
        char[] cArr;
        int i3 = $11 + Opcodes.LSHR;
        $10 = i3 % 128;
        int i4 = i3 % 2;
        switch (str3 != null ? (char) 22 : '2') {
            case 22:
                charArray = str3.toCharArray();
                break;
            default:
                charArray = str3;
                break;
        }
        char[] cArr2 = charArray;
        char[] charArray2 = str2 != null ? str2.toCharArray() : str2;
        int i5 = 0;
        if (str != null) {
            int i6 = $11 + 109;
            $10 = i6 % 128;
            switch (i6 % 2 == 0) {
                case false:
                    cArr = str.toCharArray();
                    int i7 = 8 / 0;
                    break;
                default:
                    cArr = str.toCharArray();
                    break;
            }
        } else {
            cArr = str;
        }
        o oVar = new o();
        int length = charArray2.length;
        char[] cArr3 = new char[length];
        int length2 = cArr2.length;
        char[] cArr4 = new char[length2];
        System.arraycopy(charArray2, 0, cArr3, 0, length);
        System.arraycopy(cArr2, 0, cArr4, 0, length2);
        cArr3[0] = (char) (cArr3[0] ^ c2);
        cArr4[2] = (char) (cArr4[2] + ((char) i2));
        int length3 = cArr.length;
        char[] cArr5 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(9 - ExpandableListView.getPackedPositionChild(0L), (char) (20955 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 344);
                    byte b2 = (byte) i5;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    h(b2, b3, b3, objArr3);
                    String str4 = (String) objArr3[i5];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i5] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - View.MeasureSpec.getSize(i5), (char) (ViewConfiguration.getFadingEdgeLength() >> 16), TextUtils.lastIndexOf("", '0', i5) + 208);
                        byte b4 = (byte) i5;
                        byte b5 = (byte) (b4 + 2);
                        Object[] objArr5 = new Object[1];
                        h(b4, b5, (byte) (b5 - 2), objArr5);
                        String str5 = (String) objArr5[i5];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i5] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i8 = cArr3[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr4[intValue]);
                        objArr6[1] = Integer.valueOf(i8);
                        objArr6[i5] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (char) View.resolveSize(i5, i5), 281 - TextUtils.indexOf("", "", i5, i5));
                            byte length4 = (byte) $$a.length;
                            Object[] objArr7 = new Object[1];
                            h((byte) i5, length4, (byte) (length4 - 4), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr3[intValue2] * 32718), Integer.valueOf(cArr4[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 20, (char) (TextUtils.indexOf("", "") + 14687), (ViewConfiguration.getTapTimeout() >> 16) + Opcodes.IREM);
                                byte b6 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                h(b6, (byte) (b6 | 7), b6, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr4[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr3[intValue2] = oVar.d;
                            cArr5[oVar.e] = (char) ((((r5[oVar.e] ^ cArr3[intValue2]) ^ (b ^ 6565854932352255525L)) ^ ((int) (d ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                            oVar.e++;
                            int i9 = $11 + 93;
                            $10 = i9 % 128;
                            int i10 = i9 % 2;
                            i5 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str6 = new String(cArr5);
        int i11 = $11 + 21;
        $10 = i11 % 128;
        if (i11 % 2 != 0) {
            throw null;
        }
        objArr[0] = str6;
    }
}
