package o.cz;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Currency;
import kotlin.text.Typography;
import o.a.m;
import o.ee.g;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static char[] d;
    private static char e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        c = 1;
        a();
        SystemClock.elapsedRealtimeNanos();
        View.getDefaultSize(0, 0);
        int i = b + 49;
        c = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        d = new char[]{17043, 30589, 30587, 30584, 30567, 30540, 30560, 30529, 30566, 30586, 17047, 30571, 30574, 30517, 30582, 30568, 30557, 30570, 30569, 17041, 30511, 17046, 30538, 30556, 30583, 30591, 30563, 30561, 30528, 30562, 30572, 30588, 17044, 17040, 30531, 30533};
        e = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r8 = r8 * 3
            int r8 = r8 + 4
            byte[] r0 = o.cz.b.$$a
            int r7 = r7 + 69
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1a:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1e:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L37:
            int r6 = -r6
            int r8 = r8 + r6
            int r6 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.b.g(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{102, 46, -74, -23};
        $$b = 252;
    }

    public static o.fn.a c(o.eg.b bVar) throws i {
        int i = b + 45;
        c = i % 128;
        int i2 = i % 2;
        try {
            g.c();
            Object[] objArr = new Object[1];
            f(12 - View.resolveSize(0, 0), "\u001e\n\u0000\u0012\u001d\u000e\u0011\f\u0011\u0006\r\u0005", (byte) (View.MeasureSpec.getSize(0) + 65), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(TextUtils.indexOf((CharSequence) "", '0', 0) + 36, "\t\u001a \u0001\u000f\u0018\b\u000e\u000e\u0000\t\u001a\u000e\u0015\u0018\b\u0000\u0012\u001d\u000e\u0015\u0013\u0000\u0007\u001a\u0017\u0005\u001d\u0019\n\u0015\u0013\u000e 㘓", (byte) (19 - TextUtils.lastIndexOf("", '0', 0)), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f((ViewConfiguration.getDoubleTapTimeout() >> 16) + 12, "!\u0006㘂㘂\u000f\u001d \f\u0000\u000b\u0011\u0017", (byte) (26 - Color.blue(0)), objArr3);
            try {
                o.fn.a aVar = new o.fn.a(Currency.getInstance(bVar.r(((String) objArr3[0]).intern())));
                int i3 = c + 85;
                b = i3 % 128;
                switch (i3 % 2 != 0 ? Typography.greater : (char) 26) {
                    case 26:
                        return aVar;
                    default:
                        throw null;
                }
            } catch (IllegalArgumentException e2) {
                Object[] objArr4 = new Object[1];
                f(27 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "!\u0006㘐㘐\u000f\u001d \f\u0012 \u0007\u0006\u000e\u0017\u0018\t\b\u001a!\u0007㘒㘒\u0007\u0000\u0005\u000e㘦", (byte) (40 - (Process.myTid() >> 22)), objArr4);
                throw new i(((String) objArr4[0]).intern());
            }
        } catch (o.eg.d e3) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr5 = new Object[1];
            f(52 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "\u001e \t\u0018\u0015\u0017\u001e\u0000\r\u001d\b\u000e\t\u0018\u0017\u000e\u0018!\u0007\n\u001a\u0003\r\u0005\u0017\u0011\u0015\u0002\u0002\n\u001d\u000e\u0013\u001a\u0000\u0007#\f㘀㘀\t\u001a\u000e\u0015\u0018\b\u0000\u0012\u001d\u000e\u0013\u000e㗑", (byte) (23 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), objArr5);
            throw new i(sb.append(((String) objArr5[0]).intern()).append(e3.getMessage()).toString());
        }
    }

    private static void f(int i, String str, byte b2, Object[] objArr) {
        int i2;
        char c2;
        int length;
        char[] cArr;
        int i3;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr2 = d;
        int i4 = -1401577988;
        if (cArr2 != null) {
            int i5 = $11 + 53;
            $10 = i5 % 128;
            if (i5 % 2 != 0) {
                length = cArr2.length;
                cArr = new char[length];
                i3 = 0;
            } else {
                length = cArr2.length;
                cArr = new char[length];
                i3 = 0;
            }
            while (true) {
                switch (i3 < length) {
                    case false:
                        cArr2 = cArr;
                        break;
                    default:
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr2[i3])};
                            Object obj = o.e.a.s.get(Integer.valueOf(i4));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(Color.blue(0) + 17, (char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 76);
                                byte length2 = (byte) $$a.length;
                                Object[] objArr3 = new Object[1];
                                g((byte) 0, length2, (byte) (length2 - 4), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i3++;
                            i4 = -1401577988;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(e)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getLongPressTimeout() >> 16) + 17, (char) (ViewConfiguration.getFadingEdgeLength() >> 16), 77 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)));
                byte length3 = (byte) $$a.length;
                Object[] objArr5 = new Object[1];
                g((byte) 0, length3, (byte) (length3 - 4), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i];
            char c3 = 3;
            switch (i % 2 != 0 ? (char) 3 : '(') {
                case '(':
                    i2 = i;
                    break;
                default:
                    int i6 = $10 + 69;
                    $11 = i6 % 128;
                    if (i6 % 2 != 0) {
                        i2 = i - 1;
                        cArr3[i2] = (char) (charArray[i2] - b2);
                        break;
                    } else {
                        i2 = i + 87;
                        cArr3[i2] = (char) (charArray[i2] >> b2);
                        break;
                    }
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (mVar.b < i2) {
                    mVar.e = charArray[mVar.b];
                    mVar.a = charArray[mVar.b + 1];
                    switch (mVar.e == mVar.a) {
                        case true:
                            int i7 = $11 + 25;
                            $10 = i7 % 128;
                            if (i7 % 2 != 0) {
                                cArr3[mVar.b] = (char) (mVar.e >>> b2);
                                cArr3[mVar.b >>> 1] = (char) (mVar.a >>> b2);
                            } else {
                                cArr3[mVar.b] = (char) (mVar.e - b2);
                                cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                            }
                            c2 = c3;
                            break;
                        default:
                            try {
                                Object[] objArr6 = new Object[13];
                                objArr6[12] = mVar;
                                objArr6[11] = Integer.valueOf(charValue);
                                objArr6[10] = mVar;
                                objArr6[9] = mVar;
                                objArr6[8] = Integer.valueOf(charValue);
                                objArr6[7] = mVar;
                                objArr6[6] = mVar;
                                objArr6[5] = Integer.valueOf(charValue);
                                objArr6[4] = mVar;
                                objArr6[c3] = mVar;
                                objArr6[2] = Integer.valueOf(charValue);
                                objArr6[1] = mVar;
                                objArr6[0] = mVar;
                                Object obj3 = o.e.a.s.get(696901393);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0', 0, 0), (char) (KeyEvent.keyCodeFromString("") + 8856), 323 - MotionEvent.axisFromString(""));
                                    byte b3 = (byte) 0;
                                    byte b4 = b3;
                                    Object[] objArr7 = new Object[1];
                                    g(b3, b4, b4, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                    o.e.a.s.put(696901393, obj3);
                                }
                                if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() != mVar.h) {
                                    c2 = 3;
                                    switch (mVar.c != mVar.d) {
                                        case true:
                                            int i8 = (mVar.c * charValue) + mVar.h;
                                            int i9 = (mVar.d * charValue) + mVar.i;
                                            cArr3[mVar.b] = cArr2[i8];
                                            cArr3[mVar.b + 1] = cArr2[i9];
                                            break;
                                        default:
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i10 = (mVar.c * charValue) + mVar.i;
                                            int i11 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr2[i10];
                                            cArr3[mVar.b + 1] = cArr2[i11];
                                            break;
                                    }
                                } else {
                                    try {
                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                        Object obj4 = o.e.a.s.get(1075449051);
                                        if (obj4 != null) {
                                            c2 = 3;
                                        } else {
                                            Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 65 - (ViewConfiguration.getEdgeSlop() >> 16));
                                            byte b5 = (byte) 0;
                                            byte b6 = (byte) (b5 + 1);
                                            Object[] objArr9 = new Object[1];
                                            g(b5, b6, (byte) (b6 - 1), objArr9);
                                            c2 = 3;
                                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(1075449051, obj4);
                                        }
                                        int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                        int i12 = (mVar.d * charValue) + mVar.h;
                                        cArr3[mVar.b] = cArr2[intValue];
                                        cArr3[mVar.b + 1] = cArr2[i12];
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                }
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                    }
                    mVar.b += 2;
                    c3 = c2;
                }
            }
            int i13 = 0;
            while (i13 < i) {
                int i14 = $10 + Opcodes.DDIV;
                $11 = i14 % 128;
                switch (i14 % 2 == 0 ? Typography.greater : Typography.less) {
                    case '<':
                        cArr3[i13] = (char) (cArr3[i13] ^ 13722);
                        i13++;
                        break;
                    default:
                        cArr3[i13] = (char) (cArr3[i13] | 17522);
                        i13 += 82;
                        break;
                }
            }
            objArr[0] = new String(cArr3);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
