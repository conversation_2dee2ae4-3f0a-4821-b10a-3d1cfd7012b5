package o.aj;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.TimePeriod;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.o;
import o.ee.g;
import o.es.b;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aj\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char b;
    private static int c;
    private static long d;
    private static int e;
    private static int f;
    private static byte[] g;
    private static int h;
    private static int i;
    private static short[] j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        i = 1;
        c();
        ViewConfiguration.getTouchSlop();
        Process.myTid();
        int i2 = i + 15;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 21 : '7') {
            case '7':
                break;
            default:
                int i3 = 25 / 0;
                break;
        }
    }

    static void c() {
        b = (char) 17957;
        a = 161105445;
        d = 7852706906461616079L;
        g = new byte[]{52, 95, -22, 60, 8, 55, 62, -10, -5, -27, -13, -20, -15, -24, 18, -51, -11, -29, -5, -25, -4, -1, -24, -19, 23, -27, 30, -29, 26, 4, -16, 18, 29, -23, 27, -34, -81, -16, PSSSigner.TRAILER_IMPLICIT, -48, PSSSigner.TRAILER_IMPLICIT, -62, -84, -38, -85, -83, -84, 69, 78, 65, 75, 85, 79, 69, 117, 40, 114, 64, 91, 66, 74, 93, 74, 112, 73, 114, 69, 79, 89, 115, 73, 121, 44, 118, 89, 123, 85, 73, 125, 68, 70, 116, 124, 97, 107, 121, -110, 119, 110, -104, 77, 108, 120, 103, 110, -103, -127, -73, -105, -102, -122, -105, 50, 6, 23, 8, 82, -12, 5, 9, 2, 52, 108, 96, 116, 100, 102, 120, 122, -120, 73, 98, 105, 120, -108, 101, 113, 104, -112, 114, 97, 23, 30, -19, 11, 22, -20, 27, 21, 2, 22, -40, -34, -39, -89, -42, -38, -94, -101, -101, -100, -124, -99, -86, 115, -99, -124, -109, -113, ByteCompanionObject.MIN_VALUE, 108, -125, -117, 109, PSSSigner.TRAILER_IMPLICIT, 109, 105, -113, -101, -119, -106, -107, -117, -112, -116, -97, 43, -104, -97, 110, -99, -124, -101, 100, -116, -34, 43, -104, -107, -126, -61, 108, -122, 45, -106, -106, -105, -97, -104, -91, 78, -104, -97, 110, -118, -101, 103, -98, -122, 104, -73, 104, 100, -118, -106, -124, -111, -112, -122, 107, -121, -102, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112, -112};
        c = 909053577;
        f = -1351075581;
        e = 1870483204;
    }

    static void init$0() {
        $$a = new byte[]{17, -48, -95, 64};
        $$b = 91;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.aj.a.$$a
            int r7 = 110 - r7
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r6 = r6 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            int r6 = r6 + 1
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aj.a.m(short, byte, int, java.lang.Object[]):void");
    }

    /* renamed from: o.aj.a$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aj\a$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int b;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            c = 0;
            b = 1;
            int[] iArr = new int[b.values().length];
            e = iArr;
            try {
                iArr[b.b.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[b.c.ordinal()] = 2;
                int i = b;
                int i2 = (i ^ 43) + ((i & 43) << 1);
                c = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[b.a.ordinal()] = 3;
                int i3 = c + 81;
                b = i3 % 128;
                if (i3 % 2 == 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[b.d.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[b.e.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[b.f.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[b.j.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
            try {
                e[b.g.ordinal()] = 8;
                int i4 = (c + 62) - 1;
                b = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e9) {
            }
            try {
                e[b.i.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
            try {
                e[b.h.ordinal()] = 10;
                int i6 = c + 47;
                b = i6 % 128;
                if (i6 % 2 == 0) {
                }
            } catch (NoSuchFieldError e11) {
            }
            try {
                e[b.l.ordinal()] = 11;
            } catch (NoSuchFieldError e12) {
            }
            try {
                e[b.n.ordinal()] = 12;
                int i7 = c;
                int i8 = (i7 & 35) + (i7 | 35);
                b = i8 % 128;
                if (i8 % 2 == 0) {
                }
            } catch (NoSuchFieldError e13) {
            }
            try {
                e[b.f78o.ordinal()] = 13;
            } catch (NoSuchFieldError e14) {
            }
            try {
                e[b.m.ordinal()] = 14;
                int i9 = b;
                int i10 = (i9 & 91) + (i9 | 91);
                c = i10 % 128;
                int i11 = i10 % 2;
            } catch (NoSuchFieldError e15) {
            }
            try {
                e[b.k.ordinal()] = 15;
            } catch (NoSuchFieldError e16) {
            }
            try {
                e[b.t.ordinal()] = 16;
            } catch (NoSuchFieldError e17) {
            }
            try {
                e[b.p.ordinal()] = 17;
            } catch (NoSuchFieldError e18) {
            }
            try {
                e[b.r.ordinal()] = 18;
            } catch (NoSuchFieldError e19) {
            }
            try {
                e[b.s.ordinal()] = 19;
                int i12 = c + 31;
                b = i12 % 128;
                int i13 = i12 % 2;
            } catch (NoSuchFieldError e20) {
            }
            try {
                e[b.q.ordinal()] = 20;
            } catch (NoSuchFieldError e21) {
            }
            try {
                e[b.w.ordinal()] = 21;
            } catch (NoSuchFieldError e22) {
            }
            try {
                e[b.u.ordinal()] = 22;
            } catch (NoSuchFieldError e23) {
            }
            try {
                e[b.y.ordinal()] = 23;
            } catch (NoSuchFieldError e24) {
            }
            try {
                e[b.x.ordinal()] = 24;
            } catch (NoSuchFieldError e25) {
            }
            try {
                e[b.v.ordinal()] = 25;
                int i14 = c;
                int i15 = ((i14 | Opcodes.DNEG) << 1) - (i14 ^ Opcodes.DNEG);
                b = i15 % 128;
                if (i15 % 2 == 0) {
                }
            } catch (NoSuchFieldError e26) {
            }
            try {
                e[b.A.ordinal()] = 26;
            } catch (NoSuchFieldError e27) {
            }
            try {
                e[b.C.ordinal()] = 27;
            } catch (NoSuchFieldError e28) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.eg.b b(java.util.Map<o.es.b, java.lang.Object> r36) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 2808
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aj.a.b(java.util.Map):o.eg.b");
    }

    public static Map<b, Object> b(o.eg.b bVar) {
        BigDecimal bigDecimal;
        BigDecimal bigDecimal2;
        Boolean bool;
        o.eg.b bVar2;
        o.eg.b bVar3;
        o.eg.b bVar4;
        o.eg.b bVar5;
        o.eg.b bVar6;
        o.eg.b bVar7;
        Boolean bool2;
        Boolean bool3;
        Boolean bool4;
        Boolean bool5;
        Boolean bool6;
        Boolean bool7;
        Boolean bool8;
        Boolean bool9;
        Integer[] numArr;
        String[] strArr;
        String[] w;
        String str;
        BigDecimal bigDecimal3;
        BigDecimal bigDecimal4;
        String str2;
        Object[] objArr = new Object[1];
        k((byte) ExpandableListView.getPackedPositionGroup(0L), (-1498562964) + (ViewConfiguration.getDoubleTapTimeout() >> 16), (short) (93 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (-17) - (ViewConfiguration.getScrollBarSize() >> 8), 1722337487 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(View.MeasureSpec.makeMeasureSpec(0, 0), "\ue038揨黼흰\uf52dඨ㊰㺀", (char) (Color.rgb(0, 0, 0) + 16777216), "왳☟ꩪ\ue33e", "\uf1ea絼\uf115㟤", objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        g.c();
        Object[] objArr3 = new Object[1];
        l(Process.getGidForName("") - 1656222035, "ꌮ\uee99䡽\udc95㿡얈⤮臥癢\uf325⮜垤䞲\ue98c鐵䂃峐쏒벝\udf3f治鶇\ue384떦엷ꄙ\uf83bᔕﻶ쬠蚬", (char) (37530 - KeyEvent.normalizeMetaState(0)), "걣䠎骝ᮒ", "\uf1ea絼\uf115㟤", objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        k((byte) (Process.myPid() >> 22), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 1498562816, (short) ((-16777230) - Color.rgb(0, 0, 0)), 4 - View.MeasureSpec.getSize(0), 1722337489 - TextUtils.getCapsMode("", 0, 0), objArr4);
        g.d(intern3, ((String) objArr4[0]).intern());
        HashMap hashMap = new HashMap();
        try {
            Object[] objArr5 = new Object[1];
            k((byte) Color.alpha(0), (-1498562851) + TextUtils.indexOf((CharSequence) "", '0', 0), (short) (15 - Color.blue(0)), TextUtils.indexOf("", "", 0, 0) - 19, View.resolveSizeAndState(0, 0, 0) + 1722337492, objArr5);
            o.eg.b u = bVar.u(((String) objArr5[0]).intern());
            Object[] objArr6 = new Object[1];
            k((byte) (ViewConfiguration.getTapTimeout() >> 16), (-1498562847) + (ViewConfiguration.getPressedStateDuration() >> 16), (short) (14 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 11, TextUtils.getCapsMode("", 0, 0) + 1722337505, objArr6);
            o.eg.b u2 = bVar.u(((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            k((byte) Drawable.resolveOpacity(0, 0), (-1498562834) + (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (short) (120 - TextUtils.indexOf("", "", 0)), (-14) - Color.blue(0), 1722337493 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr7);
            o.eg.b u3 = bVar.u(((String) objArr7[0]).intern());
            Object[] objArr8 = new Object[1];
            l(1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\u0883픫婞ﯭ\udbd8듣ﹷ퓌核踷\u1ad9ꧠ", (char) TextUtils.indexOf("", "", 0), "\ufd40懚聠\u20c8", "\uf1ea絼\uf115㟤", objArr8);
            o.eg.b u4 = bVar.u(((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            k((byte) (Process.myPid() >> 22), (-1498562823) + (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (short) ((ViewConfiguration.getTapTimeout() >> 16) - 67), Gravity.getAbsoluteGravity(0, 0) - 17, 1722337507 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr9);
            o.eg.b u5 = bVar.u(((String) objArr9[0]).intern());
            hashMap.put(b.b, u != null ? u.h(intern) : null);
            b bVar8 = b.c;
            switch (u != null) {
                case false:
                    bigDecimal = null;
                    break;
                default:
                    Object[] objArr10 = new Object[1];
                    k((byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), (-1498562957) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) ((-111) - MotionEvent.axisFromString("")), Color.green(0) - 9, TextUtils.lastIndexOf("", '0') + 1722337490, objArr10);
                    bigDecimal = u.o(((String) objArr10[0]).intern());
                    break;
            }
            hashMap.put(bVar8, bigDecimal);
            b bVar9 = b.a;
            if (u != null) {
                Object[] objArr11 = new Object[1];
                k((byte) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (-1498562942) - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (short) ((-128) - (ViewConfiguration.getScrollDefaultDelay() >> 16)), (-12) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (ViewConfiguration.getEdgeSlop() >> 16) + 1722337486, objArr11);
                bigDecimal2 = u.o(((String) objArr11[0]).intern());
            } else {
                bigDecimal2 = null;
            }
            hashMap.put(bVar9, bigDecimal2);
            b bVar10 = b.d;
            if (u != null) {
                Object[] objArr12 = new Object[1];
                k((byte) TextUtils.indexOf("", ""), (-1498562929) + (ViewConfiguration.getKeyRepeatTimeout() >> 16), (short) ((-66) - TextUtils.lastIndexOf("", '0')), TextUtils.indexOf((CharSequence) "", '0', 0) - 11, TextUtils.getCapsMode("", 0, 0) + 1722337505, objArr12);
                bool = u.h(((String) objArr12[0]).intern());
            } else {
                bool = null;
            }
            hashMap.put(bVar10, bool);
            if (u2 != null) {
                Object[] objArr13 = new Object[1];
                l(TextUtils.indexOf("", "") + 757141388, "⼂\uee1c\udc99", (char) (58157 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), "賍ℏⰭߣ", "\uf1ea絼\uf115㟤", objArr13);
                bVar2 = u2.u(((String) objArr13[0]).intern());
            } else {
                bVar2 = null;
            }
            if (u2 != null) {
                Object[] objArr14 = new Object[1];
                l((-1171529504) - View.MeasureSpec.getMode(0), "餿旅僸ꠗ㛜赞ன㥈\ufddf", (char) Color.green(0), "\ue082⯠\ue7ba⁑", "\uf1ea絼\uf115㟤", objArr14);
                bVar3 = u2.u(((String) objArr14[0]).intern());
            } else {
                bVar3 = null;
            }
            switch (u2 != null ? (char) 25 : (char) 18) {
                case 18:
                    bVar4 = null;
                    break;
                default:
                    int i2 = i + 69;
                    h = i2 % 128;
                    switch (i2 % 2 != 0 ? 'Y' : (char) 15) {
                        case 15:
                            Object[] objArr15 = new Object[1];
                            l(ViewConfiguration.getScrollBarFadeDuration() >> 16, "\ue0cc⢉ᯑᖲ᧔䠽㭍燐绫䱼侍幱㧨\uf7ce", (char) (Color.blue(0) + 48066), "讹痹식冻", "\uf1ea絼\uf115㟤", objArr15);
                            bVar4 = u2.u(((String) objArr15[0]).intern());
                            break;
                        default:
                            Object[] objArr16 = new Object[1];
                            l(ViewConfiguration.getScrollBarFadeDuration() % 91, "\ue0cc⢉ᯑᖲ᧔䠽㭍燐绫䱼侍幱㧨\uf7ce", (char) (48066 - Color.blue(0)), "讹痹식冻", "\uf1ea絼\uf115㟤", objArr16);
                            bVar4 = u2.u(((String) objArr16[0]).intern());
                            break;
                    }
            }
            if (u2 != null) {
                Object[] objArr17 = new Object[1];
                k((byte) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), (Process.myTid() >> 22) - 1498562862, (short) (107 - ExpandableListView.getPackedPositionType(0L)), View.resolveSizeAndState(0, 0, 0) - 14, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1722337488, objArr17);
                bVar5 = u2.u(((String) objArr17[0]).intern());
            } else {
                bVar5 = null;
            }
            switch (u2 != null) {
                case false:
                    bVar6 = null;
                    break;
                default:
                    Object[] objArr18 = new Object[1];
                    l(1734445209 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "ׂ괉猹舙롗쌋⊒\u0af4蒧", (char) Drawable.resolveOpacity(0, 0), "颬憈奧烪", "\uf1ea絼\uf115㟤", objArr18);
                    bVar6 = u2.u(((String) objArr18[0]).intern());
                    break;
            }
            switch (u2 == null) {
                case true:
                    bVar7 = null;
                    break;
                default:
                    Object[] objArr19 = new Object[1];
                    l(TextUtils.indexOf((CharSequence) "", '0') + 1570805947, "ᒳ덠菊\u0cd7剙꧌嘉穐ᲅ顯펌", (char) (ViewConfiguration.getFadingEdgeLength() >> 16), "뫫ꂘ♝ꄎ", "\uf1ea絼\uf115㟤", objArr19);
                    bVar7 = u2.u(((String) objArr19[0]).intern());
                    break;
            }
            hashMap.put(b.e, bVar2 != null ? bVar2.h(intern) : null);
            b bVar11 = b.f;
            if (bVar2 != null) {
                int i3 = h + 21;
                i = i3 % 128;
                int i4 = i3 % 2;
                bool2 = bVar2.h(intern2);
            } else {
                bool2 = null;
            }
            hashMap.put(bVar11, bool2);
            hashMap.put(b.j, bVar3 != null ? bVar3.h(intern) : null);
            b bVar12 = b.g;
            if (bVar3 != null) {
                int i5 = h + 3;
                i = i5 % 128;
                int i6 = i5 % 2;
                bool3 = bVar3.h(intern2);
            } else {
                bool3 = null;
            }
            hashMap.put(bVar12, bool3);
            hashMap.put(b.i, bVar4 != null ? bVar4.h(intern) : null);
            b bVar13 = b.h;
            if (bVar4 != null) {
                bool4 = bVar4.h(intern2);
            } else {
                int i7 = i + 5;
                h = i7 % 128;
                int i8 = i7 % 2;
                bool4 = null;
            }
            hashMap.put(bVar13, bool4);
            hashMap.put(b.l, bVar5 != null ? bVar5.h(intern) : null);
            b bVar14 = b.n;
            if (bVar5 != null) {
                int i9 = i + Opcodes.DDIV;
                h = i9 % 128;
                if (i9 % 2 != 0) {
                    bool5 = bVar5.h(intern2);
                    int i10 = 75 / 0;
                } else {
                    bool5 = bVar5.h(intern2);
                }
            } else {
                bool5 = null;
            }
            hashMap.put(bVar14, bool5);
            b bVar15 = b.f78o;
            if (bVar6 != null) {
                int i11 = h + 33;
                i = i11 % 128;
                int i12 = i11 % 2;
                bool6 = bVar6.h(intern);
            } else {
                bool6 = null;
            }
            hashMap.put(bVar15, bool6);
            b bVar16 = b.m;
            switch (bVar6 != null ? (char) 2 : '=') {
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    bool7 = null;
                    break;
                default:
                    int i13 = h + 59;
                    i = i13 % 128;
                    if (i13 % 2 == 0) {
                        bVar6.h(intern2);
                        throw null;
                    }
                    bool7 = bVar6.h(intern2);
                    break;
            }
            hashMap.put(bVar16, bool7);
            b bVar17 = b.k;
            switch (bVar7 != null ? 'O' : Typography.greater) {
                case '>':
                    bool8 = null;
                    break;
                default:
                    bool8 = bVar7.h(intern);
                    break;
            }
            hashMap.put(bVar17, bool8);
            hashMap.put(b.t, bVar7 != null ? bVar7.h(intern2) : null);
            b bVar18 = b.p;
            switch (u3 != null) {
                case false:
                    bool9 = null;
                    break;
                default:
                    bool9 = u3.h(intern);
                    break;
            }
            hashMap.put(bVar18, bool9);
            hashMap.put(b.r, u3 != null ? u3.h(intern2) : null);
            if (u3 != null) {
                int i14 = i + 79;
                h = i14 % 128;
                int i15 = i14 % 2;
                Object[] objArr20 = new Object[1];
                k((byte) Color.alpha(0), (-1498562917) - TextUtils.getCapsMode("", 0, 0), (short) (43 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (-7) - (ViewConfiguration.getDoubleTapTimeout() >> 16), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 1722337487, objArr20);
                numArr = u3.x(((String) objArr20[0]).intern());
            } else {
                numArr = null;
            }
            hashMap.put(b.s, numArr);
            if (u3 != null) {
                int i16 = h + 57;
                i = i16 % 128;
                int i17 = i16 % 2;
                Object[] objArr21 = new Object[1];
                k((byte) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), Color.red(0) - 1498562900, (short) (41 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (Process.myPid() >> 22) - 10, Color.rgb(0, 0, 0) + 1739114720, objArr21);
                strArr = u3.w(((String) objArr21[0]).intern());
            } else {
                strArr = null;
            }
            hashMap.put(b.q, strArr);
            hashMap.put(b.w, u4 != null ? u4.h(intern) : null);
            hashMap.put(b.u, u4 != null ? u4.h(intern2) : null);
            switch (u4 != null ? (char) 30 : '2') {
                case 30:
                    Object[] objArr22 = new Object[1];
                    k((byte) View.resolveSizeAndState(0, 0, 0), KeyEvent.normalizeMetaState(0) - 1498562886, (short) (34 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), View.MeasureSpec.getSize(0) - 20, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 1722337505, objArr22);
                    w = u4.w(((String) objArr22[0]).intern());
                    break;
                default:
                    w = null;
                    break;
            }
            hashMap.put(b.y, w);
            if (u5 != null) {
                Object[] objArr23 = new Object[1];
                l(View.MeasureSpec.getSize(0), "\udb78쇼ꂰ\uefa9溃間", (char) (59130 - (ViewConfiguration.getFadingEdgeLength() >> 16)), "垻툛\ufaf9ꇦ", "\uf1ea絼\uf115㟤", objArr23);
                str = u5.q(((String) objArr23[0]).intern());
            } else {
                str = null;
            }
            hashMap.put(b.x, TimePeriod.getPeriodFromString(str));
            b bVar19 = b.v;
            switch (u5 != null) {
                case false:
                    bigDecimal3 = null;
                    break;
                default:
                    Object[] objArr24 = new Object[1];
                    k((byte) Color.red(0), MotionEvent.axisFromString("") - 1498562881, (short) (View.MeasureSpec.getMode(0) + 12), (-11) - ((Process.getThreadPriority(0) + 20) >> 6), KeyEvent.normalizeMetaState(0) + 1722337487, objArr24);
                    bigDecimal3 = u5.o(((String) objArr24[0]).intern());
                    break;
            }
            hashMap.put(bVar19, bigDecimal3);
            b bVar20 = b.A;
            if (u5 != null) {
                Object[] objArr25 = new Object[1];
                k((byte) ('0' - AndroidCharacter.getMirror('0')), View.resolveSizeAndState(0, 0, 0) - 1498562942, (short) ((-128) - (ViewConfiguration.getWindowTouchSlop() >> 8)), (-11) - KeyEvent.getDeadChar(0, 0), Color.alpha(0) + 1722337486, objArr25);
                bigDecimal4 = u5.o(((String) objArr25[0]).intern());
            } else {
                bigDecimal4 = null;
            }
            hashMap.put(bVar20, bigDecimal4);
            if (u5 != null) {
                int i18 = i + 57;
                h = i18 % 128;
                int i19 = i18 % 2;
                Object[] objArr26 = new Object[1];
                k((byte) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), (-1498562870) - TextUtils.indexOf((CharSequence) "", '0'), (short) ((ViewConfiguration.getPressedStateDuration() >> 16) - 18), (-17) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), View.MeasureSpec.makeMeasureSpec(0, 0) + 1722337505, objArr26);
                str2 = u5.q(((String) objArr26[0]).intern());
            } else {
                str2 = null;
            }
            hashMap.put(b.C, str2 != null ? TimeZone.getTimeZone(str2) : null);
        } catch (o.eg.d e2) {
            g.c();
            Object[] objArr27 = new Object[1];
            l(TextUtils.getTrimmedLength("") - 1656222036, "ꌮ\uee99䡽\udc95㿡얈⤮臥癢\uf325⮜垤䞲\ue98c鐵䂃峐쏒벝\udf3f治鶇\ue384떦엷ꄙ\uf83bᔕﻶ쬠蚬", (char) (TextUtils.indexOf((CharSequence) "", '0') + 37531), "걣䠎骝ᮒ", "\uf1ea絼\uf115㟤", objArr27);
            String intern4 = ((String) objArr27[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr28 = new Object[1];
            k((byte) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (-1498562788) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) ((-9) - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 21 - ImageFormat.getBitsPerPixel(0), MotionEvent.axisFromString("") + 1722337490, objArr28);
            g.e(intern4, sb.append(((String) objArr28[0]).intern()).append(e2).toString());
        }
        return hashMap;
    }

    private static void l(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] charArray;
        char[] charArray2;
        char[] cArr;
        char c3;
        int i3 = $10;
        int i4 = i3 + 109;
        $11 = i4 % 128;
        int i5 = i4 % 2;
        int i6 = 0;
        switch (str3 != null ? '\b' : 'X') {
            case '\b':
                int i7 = i3 + 81;
                $11 = i7 % 128;
                switch (i7 % 2 == 0 ? 'E' : '\b') {
                    case '\b':
                        charArray = str3.toCharArray();
                        break;
                    default:
                        charArray = str3.toCharArray();
                        int i8 = 39 / 0;
                        break;
                }
            default:
                charArray = str3;
                break;
        }
        char[] cArr2 = charArray;
        switch (str2 == null) {
            case false:
                int i9 = $10 + 99;
                $11 = i9 % 128;
                int i10 = i9 % 2;
                charArray2 = str2.toCharArray();
                break;
            default:
                charArray2 = str2;
                break;
        }
        char[] cArr3 = charArray2;
        switch (str == null) {
            case true:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                int i11 = $11 + Opcodes.LNEG;
                $10 = i11 % 128;
                int i12 = i11 % 2;
                break;
        }
        o oVar = new o();
        int length = cArr3.length;
        char[] cArr4 = new char[length];
        int length2 = cArr2.length;
        char[] cArr5 = new char[length2];
        System.arraycopy(cArr3, 0, cArr4, 0, length);
        System.arraycopy(cArr2, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c2);
        cArr5[2] = (char) (cArr5[2] + ((char) i2));
        int length3 = cArr.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(MotionEvent.axisFromString("") + 11, (char) (20954 - TextUtils.indexOf("", "", i6)), TextUtils.indexOf((CharSequence) "", '0', i6) + 345);
                    byte b2 = (byte) (-1);
                    Object[] objArr3 = new Object[1];
                    m(b2, (byte) (b2 & 11), (byte) i6, objArr3);
                    String str4 = (String) objArr3[i6];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i6] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - TextUtils.getCapsMode("", i6, i6), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.resolveSize(i6, i6) + 207);
                        byte b3 = (byte) (-1);
                        Object[] objArr5 = new Object[1];
                        m(b3, (byte) (b3 & 9), (byte) i6, objArr5);
                        String str5 = (String) objArr5[i6];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i6] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i13 = cArr4[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr5[intValue]);
                        objArr6[1] = Integer.valueOf(i13);
                        objArr6[i6] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 11, (char) Color.red(i6), TextUtils.indexOf((CharSequence) "", '0', i6, i6) + 282);
                            byte b4 = (byte) (-1);
                            byte b5 = (byte) i6;
                            Object[] objArr7 = new Object[1];
                            m(b4, (byte) (b4 & 7), b5, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 != null) {
                                c3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(19 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) (TextUtils.getTrimmedLength("") + 14687), View.resolveSizeAndState(0, 0, 0) + Opcodes.IREM);
                                byte length4 = (byte) $$a.length;
                                Object[] objArr9 = new Object[1];
                                m((byte) (-1), length4, (byte) (length4 - 4), objArr9);
                                c3 = 2;
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((int) (a ^ 6565854932352255525L)) ^ ((cArr4[intValue2] ^ r6[oVar.e]) ^ (d ^ 6565854932352255525L))) ^ ((char) (b ^ 6565854932352255525L)));
                            oVar.e++;
                            cArr4 = cArr4;
                            i6 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }

    /* JADX WARN: Code restructure failed: missing block: B:92:0x0316, code lost:
    
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r18, int r19, short r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 1016
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aj.a.k(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
