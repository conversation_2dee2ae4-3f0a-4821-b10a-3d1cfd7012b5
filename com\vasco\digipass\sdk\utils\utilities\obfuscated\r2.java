package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r2.smali */
public class r2 extends AsymmetricKeyParameter {
    private t2 b;

    protected r2(boolean z, t2 t2Var) {
        super(z);
        this.b = t2Var;
    }

    public t2 a() {
        return this.b;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof r2)) {
            return false;
        }
        r2 r2Var = (r2) obj;
        t2 t2Var = this.b;
        return t2Var == null ? r2Var.a() == null : t2Var.equals(r2Var.a());
    }

    public int hashCode() {
        int i = !isPrivate() ? 1 : 0;
        t2 t2Var = this.b;
        return t2Var != null ? i ^ t2Var.hashCode() : i;
    }
}
