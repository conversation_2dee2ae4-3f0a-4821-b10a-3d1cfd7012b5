package com.capacitorjs.plugins.clipboard;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import androidx.webkit.internal.AssetHelper;
import com.getcapacitor.Logger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\clipboard\Clipboard.smali */
public class Clipboard {
    private static final String TAG = "Clipboard";
    private ClipboardManager clipboard;
    private Context context;

    public Clipboard(Context context) {
        this.context = context;
        this.clipboard = (ClipboardManager) context.getSystemService("clipboard");
    }

    public ClipboardWriteResponse write(String label, String content) {
        ClipboardManager clipboardManager;
        ClipData data = ClipData.newPlainText(label, content);
        if (data != null && (clipboardManager = this.clipboard) != null) {
            try {
                clipboardManager.setPrimaryClip(data);
                return new ClipboardWriteResponse(true);
            } catch (Exception e) {
                Logger.error(TAG, e);
                return new ClipboardWriteResponse(false, "Writing to the clipboard failed");
            }
        }
        if (this.clipboard == null) {
            return new ClipboardWriteResponse(false, "Problem getting a reference to the system clipboard");
        }
        return new ClipboardWriteResponse(false, "Problem formatting data");
    }

    public ClipboardData read() {
        ClipboardManager clipboardManager = this.clipboard;
        if (clipboardManager != null) {
            CharSequence value = null;
            if (clipboardManager.hasPrimaryClip()) {
                if (this.clipboard.getPrimaryClipDescription().hasMimeType(AssetHelper.DEFAULT_MIME_TYPE)) {
                    Logger.debug(TAG, "Got plaintxt");
                    ClipData.Item item = this.clipboard.getPrimaryClip().getItemAt(0);
                    value = item.getText();
                } else {
                    Logger.debug(TAG, "Not plaintext!");
                    ClipData.Item item2 = this.clipboard.getPrimaryClip().getItemAt(0);
                    value = item2.coerceToText(this.context).toString();
                }
            }
            ClipboardData clipboardData = new ClipboardData();
            String type = AssetHelper.DEFAULT_MIME_TYPE;
            if (value != null) {
                clipboardData.setValue(value.toString());
            }
            if (value != null && value.toString().startsWith("data:")) {
                type = value.toString().split(";")[0].split(":")[1];
            }
            clipboardData.setType(type);
            return clipboardData;
        }
        return null;
    }
}
