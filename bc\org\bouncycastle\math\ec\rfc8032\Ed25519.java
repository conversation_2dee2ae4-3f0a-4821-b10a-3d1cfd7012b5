package bc.org.bouncycastle.math.ec.rfc8032;

import bc.org.bouncycastle.crypto.Digest;
import bc.org.bouncycastle.math.ec.rfc7748.X25519;
import bc.org.bouncycastle.math.ec.rfc7748.X25519Field;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.security.SecureRandom;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519.smali */
public abstract class Ed25519 {
    public static final int PREHASH_SIZE = 64;
    public static final int PUBLIC_KEY_SIZE = 32;
    public static final int SECRET_KEY_SIZE = 32;
    public static final int SIGNATURE_SIZE = 64;
    private static final byte[] a = {83, 105, 103, 69, 100, 50, 53, 53, 49, 57, 32, 110, 111, 32, 69, 100, 50, 53, 53, 49, 57, 32, 99, 111, 108, 108, 105, 115, 105, 111, 110, 115};
    private static final int[] b = {-19, -1, -1, -1, -1, -1, -1, Integer.MAX_VALUE};
    private static final int[] c = {1886001095, 1339575613, 1980447930, 258412557, -95215574, -959694548, 2013120334, 2047061138};
    private static final int[] d = {-1886001114, -1339575614, -1980447931, -258412558, 95215573, 959694547, -2013120335, 100422509};
    private static final int[] e = {52811034, 25909283, 8072341, 50637101, 13785486, 30858332, 20483199, 20966410, 43936626, 4379245};
    private static final int[] f = {40265304, 26843545, 6710886, 53687091, 13421772, 40265318, 26843545, 6710886, 53687091, 13421772};
    private static final int[] g = {12052516, 1174424, 4087752, 38672185, 20040971, 21899680, 55468344, 20105554, 66708015, 9981791};
    private static final int[] h = {66430571, 45040722, 4842939, 15895846, 18981244, 46308410, 4697481, 8903007, 53646190, 12474675};
    private static final int[] i = {56195235, 47411844, 25868126, 40503822, 57364, 58321048, 30416477, 31930572, 57760639, 10749657};
    private static final int[] j = {45281625, 27714825, 18181821, 13898781, 114729, 49533232, 60832955, 30306712, 48412415, 4722099};
    private static final int[] k = {23454386, 55429651, 2809210, 27797563, 229458, 31957600, 54557047, 27058993, 29715967, 9444199};
    private static final Object l = new Object();
    private static e[] m;
    private static e[] n;

    /* renamed from: o, reason: collision with root package name */
    private static int[] f10o;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$Algorithm.smali */
    public static final class Algorithm {
        public static final int Ed25519 = 0;
        public static final int Ed25519ctx = 1;
        public static final int Ed25519ph = 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$PublicPoint.smali */
    public static final class PublicPoint {
        final int[] a;

        PublicPoint(int[] iArr) {
            this.a = iArr;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$b.smali */
    private static class b {
        int[] a;
        int[] b;
        int[] c;
        int[] d;
        int[] e;

        private b() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
            this.c = X25519Field.create();
            this.d = X25519Field.create();
            this.e = X25519Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$c.smali */
    private static class c {
        int[] a;
        int[] b;

        private c() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$d.smali */
    private static class d {
        int[] a;
        int[] b;
        int[] c;
        int[] d;

        private d() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
            this.c = X25519Field.create();
            this.d = X25519Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$e.smali */
    private static class e {
        int[] a;
        int[] b;
        int[] c;

        private e() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
            this.c = X25519Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$f.smali */
    private static class f {
        int[] a;
        int[] b;
        int[] c;
        int[] d;

        private f() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
            this.c = X25519Field.create();
            this.d = X25519Field.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\Ed25519$g.smali */
    private static class g {
        int[] a;
        int[] b;

        private g() {
            this.a = X25519Field.create();
            this.b = X25519Field.create();
        }
    }

    private static byte[] a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int[] iArr = new int[16];
        bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr, iArr);
        int[] iArr2 = new int[8];
        bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr2, iArr2);
        int[] iArr3 = new int[8];
        bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr3, iArr3);
        w5.e(iArr2, iArr3, iArr);
        byte[] bArr4 = new byte[64];
        bc.org.bouncycastle.math.ec.rfc8032.a.a(iArr, 0, 16, bArr4, 0);
        return bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr4);
    }

    private static boolean b(c cVar) {
        b bVar = new b();
        a(cVar, bVar);
        return b(bVar);
    }

    private static PublicPoint c(c cVar) {
        int[] iArr = new int[20];
        X25519Field.copy(cVar.a, 0, iArr, 0);
        X25519Field.copy(cVar.b, 0, iArr, 10);
        return new PublicPoint(iArr);
    }

    public static Digest createPrehash() {
        return a();
    }

    private static void d(b bVar) {
        X25519Field.zero(bVar.a);
        X25519Field.one(bVar.b);
        X25519Field.one(bVar.c);
        X25519Field.zero(bVar.d);
        X25519Field.one(bVar.e);
    }

    public static void encodePublicPoint(PublicPoint publicPoint, byte[] bArr, int i2) {
        X25519Field.encode(publicPoint.a, 10, bArr, i2);
        int i3 = (i2 + 32) - 1;
        bArr[i3] = (byte) (((publicPoint.a[0] & 1) << 7) | bArr[i3]);
    }

    public static void generatePrivateKey(SecureRandom secureRandom, byte[] bArr) {
        if (bArr.length != 32) {
            throw new IllegalArgumentException("k");
        }
        secureRandom.nextBytes(bArr);
    }

    public static void generatePublicKey(byte[] bArr, int i2, byte[] bArr2, int i3) {
        Digest a2 = a();
        byte[] bArr3 = new byte[64];
        a2.update(bArr, i2, 32);
        a2.doFinal(bArr3, 0);
        byte[] bArr4 = new byte[32];
        a(bArr3, 0, bArr4);
        a(bArr4, bArr2, i3);
    }

    public static void precompute() {
        synchronized (l) {
            if (f10o != null) {
                return;
            }
            d[] dVarArr = new d[96];
            g gVar = new g();
            c cVar = new c();
            int[] iArr = e;
            int i2 = 0;
            X25519Field.copy(iArr, 0, cVar.a, 0);
            int[] iArr2 = f;
            X25519Field.copy(iArr2, 0, cVar.b, 0);
            a(cVar, dVarArr, 0, 16, gVar);
            c cVar2 = new c();
            X25519Field.copy(g, 0, cVar2.a, 0);
            X25519Field.copy(h, 0, cVar2.b, 0);
            a(cVar2, dVarArr, 16, 16, gVar);
            b bVar = new b();
            X25519Field.copy(iArr, 0, bVar.a, 0);
            X25519Field.copy(iArr2, 0, bVar.b, 0);
            X25519Field.one(bVar.c);
            X25519Field.copy(bVar.a, 0, bVar.d, 0);
            X25519Field.copy(bVar.b, 0, bVar.e, 0);
            int i3 = 4;
            d[] dVarArr2 = new d[4];
            for (int i4 = 0; i4 < 4; i4++) {
                dVarArr2[i4] = new d();
            }
            d dVar = new d();
            int i5 = 0;
            int i6 = 32;
            while (i5 < 8) {
                int i7 = i6 + 1;
                d dVar2 = new d();
                dVarArr[i6] = dVar2;
                int i8 = i2;
                while (i8 < i3) {
                    if (i8 == 0) {
                        a(bVar, dVar2);
                    } else {
                        a(bVar, dVar);
                        a(dVar2, dVar, dVar2, gVar);
                    }
                    c(bVar);
                    a(bVar, dVarArr2[i8]);
                    if (i5 + i8 != 10) {
                        for (int i9 = 1; i9 < 8; i9++) {
                            c(bVar);
                        }
                    }
                    i8++;
                    i3 = 4;
                }
                int[] iArr3 = dVar2.a;
                X25519Field.negate(iArr3, iArr3);
                int[] iArr4 = dVar2.d;
                X25519Field.negate(iArr4, iArr4);
                i6 = i7;
                for (int i10 = 0; i10 < 3; i10++) {
                    int i11 = 1 << i10;
                    int i12 = 0;
                    while (i12 < i11) {
                        d dVar3 = new d();
                        dVarArr[i6] = dVar3;
                        a(dVarArr[i6 - i11], dVarArr2[i10], dVar3, gVar);
                        i12++;
                        i6++;
                    }
                }
                i5++;
                i3 = 4;
                i2 = 0;
            }
            a(dVarArr);
            m = new e[16];
            for (int i13 = 0; i13 < 16; i13++) {
                d dVar4 = dVarArr[i13];
                e[] eVarArr = m;
                e eVar = new e();
                eVarArr[i13] = eVar;
                int[] iArr5 = dVar4.a;
                X25519Field.mul(iArr5, dVar4.c, iArr5);
                int[] iArr6 = dVar4.b;
                X25519Field.mul(iArr6, dVar4.c, iArr6);
                X25519Field.apm(dVar4.b, dVar4.a, eVar.b, eVar.a);
                X25519Field.mul(dVar4.a, dVar4.b, eVar.c);
                int[] iArr7 = eVar.c;
                X25519Field.mul(iArr7, k, iArr7);
                X25519Field.normalize(eVar.a);
                X25519Field.normalize(eVar.b);
                X25519Field.normalize(eVar.c);
            }
            n = new e[16];
            for (int i14 = 0; i14 < 16; i14++) {
                d dVar5 = dVarArr[16 + i14];
                e[] eVarArr2 = n;
                e eVar2 = new e();
                eVarArr2[i14] = eVar2;
                int[] iArr8 = dVar5.a;
                X25519Field.mul(iArr8, dVar5.c, iArr8);
                int[] iArr9 = dVar5.b;
                X25519Field.mul(iArr9, dVar5.c, iArr9);
                X25519Field.apm(dVar5.b, dVar5.a, eVar2.b, eVar2.a);
                X25519Field.mul(dVar5.a, dVar5.b, eVar2.c);
                int[] iArr10 = eVar2.c;
                X25519Field.mul(iArr10, k, iArr10);
                X25519Field.normalize(eVar2.a);
                X25519Field.normalize(eVar2.b);
                X25519Field.normalize(eVar2.c);
            }
            f10o = X25519Field.createTable(192);
            e eVar3 = new e();
            int i15 = 0;
            for (int i16 = 32; i16 < 96; i16++) {
                d dVar6 = dVarArr[i16];
                int[] iArr11 = dVar6.a;
                X25519Field.mul(iArr11, dVar6.c, iArr11);
                int[] iArr12 = dVar6.b;
                X25519Field.mul(iArr12, dVar6.c, iArr12);
                X25519Field.apm(dVar6.b, dVar6.a, eVar3.b, eVar3.a);
                X25519Field.mul(dVar6.a, dVar6.b, eVar3.c);
                int[] iArr13 = eVar3.c;
                X25519Field.mul(iArr13, k, iArr13);
                X25519Field.normalize(eVar3.a);
                X25519Field.normalize(eVar3.b);
                X25519Field.normalize(eVar3.c);
                X25519Field.copy(eVar3.a, 0, f10o, i15);
                int i17 = i15 + 10;
                X25519Field.copy(eVar3.b, 0, f10o, i17);
                int i18 = i17 + 10;
                X25519Field.copy(eVar3.c, 0, f10o, i18);
                i15 = i18 + 10;
            }
        }
    }

    public static void scalarMultBaseYZ(X25519.Friend friend, byte[] bArr, int i2, int[] iArr, int[] iArr2) {
        if (friend == null) {
            throw new NullPointerException("This method is only for use by X25519");
        }
        byte[] bArr2 = new byte[32];
        a(bArr, i2, bArr2);
        b bVar = new b();
        a(bArr2, bVar);
        if (a(bVar) == 0) {
            throw new IllegalStateException();
        }
        X25519Field.copy(bVar.b, 0, iArr, 0);
        X25519Field.copy(bVar.c, 0, iArr2, 0);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, int i3, int i4, byte[] bArr3, int i5) {
        a(bArr, i2, (byte[]) null, (byte) 0, bArr2, i3, i4, bArr3, i5);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, byte[] bArr3, int i3, byte[] bArr4, int i4) {
        a(bArr, i2, bArr2, (byte) 1, bArr3, i3, 64, bArr4, i4);
    }

    public static boolean validatePublicKeyFull(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 32);
        if (!a(a2)) {
            return false;
        }
        c cVar = new c();
        if (a(a2, false, cVar)) {
            return b(cVar);
        }
        return false;
    }

    public static PublicPoint validatePublicKeyFullExport(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 32);
        if (!a(a2)) {
            return null;
        }
        c cVar = new c();
        if (a(a2, false, cVar) && b(cVar)) {
            return c(cVar);
        }
        return null;
    }

    public static boolean validatePublicKeyPartial(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 32);
        if (a(a2)) {
            return a(a2, false, new c());
        }
        return false;
    }

    public static PublicPoint validatePublicKeyPartialExport(byte[] bArr, int i2) {
        byte[] a2 = a(bArr, i2, 32);
        if (!a(a2)) {
            return null;
        }
        c cVar = new c();
        if (a(a2, false, cVar)) {
            return c(cVar);
        }
        return null;
    }

    public static boolean verify(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, int i4, int i5) {
        return a(bArr, i2, bArr2, i3, (byte[]) null, (byte) 0, bArr3, i4, i5);
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4) {
        return a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, i4, 64);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, int i4, int i5, byte[] bArr4, int i6) {
        a(bArr, i2, bArr2, i3, null, (byte) 0, bArr3, i4, i5, bArr4, i6);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, byte[] bArr5, int i5) {
        a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, i4, 64, bArr5, i5);
    }

    public static boolean verify(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, int i3, int i4) {
        return a(bArr, i2, publicPoint, null, (byte) 0, bArr2, i3, i4);
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte[] bArr3, int i3) {
        return a(bArr, i2, publicPoint, bArr2, (byte) 1, bArr3, i3, 64);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, byte[] bArr3, int i3, int i4, byte[] bArr4, int i5) {
        a(bArr, i2, bArr2, (byte) 0, bArr3, i3, i4, bArr4, i5);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, Digest digest, byte[] bArr3, int i3) {
        byte[] bArr4 = new byte[64];
        if (64 == digest.doFinal(bArr4, 0)) {
            a(bArr, i2, bArr2, (byte) 1, bArr4, 0, 64, bArr3, i3);
            return;
        }
        throw new IllegalArgumentException("ph");
    }

    public static boolean verify(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, int i5) {
        return a(bArr, i2, bArr2, i3, bArr3, (byte) 0, bArr4, i4, i5);
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, Digest digest) {
        byte[] bArr4 = new byte[64];
        if (64 == digest.doFinal(bArr4, 0)) {
            return a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr4, 0, 64);
        }
        throw new IllegalArgumentException("ph");
    }

    private static boolean b(byte[] bArr) {
        if ((bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 28) & Integer.MAX_VALUE) < b[7]) {
            return true;
        }
        int[] iArr = new int[8];
        bc.org.bouncycastle.math.ec.rfc8032.a.a(bArr, 0, iArr, 0, 8);
        iArr[7] = iArr[7] & Integer.MAX_VALUE;
        return !w5.c(iArr, r2);
    }

    public static void sign(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte[] bArr4, int i4, int i5, byte[] bArr5, int i6) {
        a(bArr, i2, bArr2, i3, bArr3, (byte) 0, bArr4, i4, i5, bArr5, i6);
    }

    public static boolean verify(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte[] bArr3, int i3, int i4) {
        return a(bArr, i2, publicPoint, bArr2, (byte) 0, bArr3, i3, i4);
    }

    private static void c(b bVar) {
        int[] iArr = bVar.a;
        int[] iArr2 = bVar.b;
        int[] iArr3 = bVar.c;
        int[] iArr4 = bVar.d;
        int[] iArr5 = bVar.e;
        X25519Field.add(iArr, iArr2, iArr4);
        X25519Field.sqr(bVar.a, iArr);
        X25519Field.sqr(bVar.b, iArr2);
        X25519Field.sqr(bVar.c, iArr3);
        X25519Field.add(iArr3, iArr3, iArr3);
        X25519Field.apm(iArr, iArr2, iArr5, iArr2);
        X25519Field.sqr(iArr4, iArr4);
        X25519Field.sub(iArr5, iArr4, iArr4);
        X25519Field.add(iArr3, iArr2, iArr);
        X25519Field.carry(iArr);
        X25519Field.mul(iArr, iArr2, bVar.c);
        X25519Field.mul(iArr, iArr4, bVar.a);
        X25519Field.mul(iArr2, iArr5, bVar.b);
    }

    private static boolean a(byte[] bArr, byte b2) {
        return (bArr == null && b2 == 0) || (bArr != null && bArr.length < 256);
    }

    private static int a(c cVar) {
        int[] create = X25519Field.create();
        int[] create2 = X25519Field.create();
        int[] create3 = X25519Field.create();
        X25519Field.sqr(cVar.a, create2);
        X25519Field.sqr(cVar.b, create3);
        X25519Field.mul(create2, create3, create);
        X25519Field.sub(create2, create3, create2);
        X25519Field.mul(create, i, create);
        X25519Field.addOne(create);
        X25519Field.add(create, create2, create);
        X25519Field.normalize(create);
        X25519Field.normalize(create3);
        return X25519Field.isZero(create) & (~X25519Field.isZero(create3));
    }

    public static PublicPoint generatePublicKey(byte[] bArr, int i2) {
        Digest a2 = a();
        byte[] bArr2 = new byte[64];
        a2.update(bArr, i2, 32);
        a2.doFinal(bArr2, 0);
        byte[] bArr3 = new byte[32];
        a(bArr2, 0, bArr3);
        b bVar = new b();
        a(bArr3, bVar);
        c cVar = new c();
        a(bVar, cVar);
        if (a(cVar) != 0) {
            return c(cVar);
        }
        throw new IllegalStateException();
    }

    private static boolean b(b bVar) {
        X25519Field.normalize(bVar.a);
        X25519Field.normalize(bVar.b);
        X25519Field.normalize(bVar.c);
        return X25519Field.isZeroVar(bVar.a) && !X25519Field.isZeroVar(bVar.b) && X25519Field.areEqualVar(bVar.b, bVar.c);
    }

    public static void signPrehash(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, Digest digest, byte[] bArr4, int i4) {
        byte[] bArr5 = new byte[64];
        if (64 == digest.doFinal(bArr5, 0)) {
            a(bArr, i2, bArr2, i3, bArr3, (byte) 1, bArr5, 0, 64, bArr4, i4);
            return;
        }
        throw new IllegalArgumentException("ph");
    }

    public static boolean verifyPrehash(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, Digest digest) {
        byte[] bArr3 = new byte[64];
        if (64 == digest.doFinal(bArr3, 0)) {
            return a(bArr, i2, publicPoint, bArr2, (byte) 1, bArr3, 0, 64);
        }
        throw new IllegalArgumentException("ph");
    }

    private static int a(b bVar) {
        int[] create = X25519Field.create();
        int[] create2 = X25519Field.create();
        int[] create3 = X25519Field.create();
        int[] create4 = X25519Field.create();
        X25519Field.sqr(bVar.a, create2);
        X25519Field.sqr(bVar.b, create3);
        X25519Field.sqr(bVar.c, create4);
        X25519Field.mul(create2, create3, create);
        X25519Field.sub(create2, create3, create2);
        X25519Field.mul(create2, create4, create2);
        X25519Field.sqr(create4, create4);
        X25519Field.mul(create, i, create);
        X25519Field.add(create, create4, create);
        X25519Field.add(create, create2, create);
        X25519Field.normalize(create);
        X25519Field.normalize(create3);
        X25519Field.normalize(create4);
        return X25519Field.isZero(create) & (~X25519Field.isZero(create3)) & (~X25519Field.isZero(create4));
    }

    private static boolean a(byte[] bArr) {
        int c2 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 28) & Integer.MAX_VALUE;
        int i2 = b[7] ^ c2;
        int i3 = c[7] ^ c2;
        int i4 = d[7] ^ c2;
        for (int i5 = 6; i5 > 0; i5--) {
            int c3 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, i5 * 4);
            c2 |= c3;
            i2 |= b[i5] ^ c3;
            i3 |= c[i5] ^ c3;
            i4 |= c3 ^ d[i5];
        }
        int c4 = bc.org.bouncycastle.math.ec.rfc8032.a.c(bArr, 0);
        if (c2 != 0 || c4 - 2147483648 > -2147483647) {
            if (i2 != 0 || c4 - 2147483648 < (b[0] - 1) - 2147483648) {
                return (((c[0] ^ c4) | i3) != 0) & (((c4 ^ d[0]) | i4) != 0);
            }
            return false;
        }
        return false;
    }

    private static byte[] a(byte[] bArr, int i2, int i3) {
        byte[] bArr2 = new byte[i3];
        System.arraycopy(bArr, i2, bArr2, 0, i3);
        return bArr2;
    }

    private static Digest a() {
        bc.org.bouncycastle.crypto.digests.d dVar = new bc.org.bouncycastle.crypto.digests.d();
        if (dVar.getDigestSize() == 64) {
            return dVar;
        }
        throw new IllegalStateException();
    }

    private static boolean a(byte[] bArr, boolean z, c cVar) {
        int i2 = (bArr[31] & ByteCompanionObject.MIN_VALUE) >>> 7;
        X25519Field.decode(bArr, cVar.b);
        int[] create = X25519Field.create();
        int[] create2 = X25519Field.create();
        X25519Field.sqr(cVar.b, create);
        X25519Field.mul(i, create, create2);
        X25519Field.subOne(create);
        X25519Field.addOne(create2);
        if (!X25519Field.sqrtRatioVar(create, create2, cVar.a)) {
            return false;
        }
        X25519Field.normalize(cVar.a);
        if (i2 == 1 && X25519Field.isZeroVar(cVar.a)) {
            return false;
        }
        int[] iArr = cVar.a;
        if (z ^ (i2 != (iArr[0] & 1))) {
            X25519Field.negate(iArr, iArr);
            X25519Field.normalize(cVar.a);
        }
        return true;
    }

    private static void a(Digest digest, byte b2, byte[] bArr) {
        byte[] bArr2 = a;
        int length = bArr2.length;
        int i2 = length + 2;
        int length2 = bArr.length + i2;
        byte[] bArr3 = new byte[length2];
        System.arraycopy(bArr2, 0, bArr3, 0, length);
        bArr3[length] = b2;
        bArr3[length + 1] = (byte) bArr.length;
        System.arraycopy(bArr, 0, bArr3, i2, bArr.length);
        digest.update(bArr3, 0, length2);
    }

    private static void a(c cVar, byte[] bArr, int i2) {
        X25519Field.encode(cVar.b, bArr, i2);
        int i3 = (i2 + 32) - 1;
        bArr[i3] = (byte) (((cVar.a[0] & 1) << 7) | bArr[i3]);
    }

    private static int a(b bVar, byte[] bArr, int i2) {
        c cVar = new c();
        a(bVar, cVar);
        int a2 = a(cVar);
        a(cVar, bArr, i2);
        return a2;
    }

    private static void a(int[] iArr) {
        for (int i2 = 0; i2 < iArr.length; i2++) {
            iArr[i2] = f5.c(iArr[i2]);
        }
    }

    private static void a(Digest digest, byte[] bArr, byte[] bArr2, byte[] bArr3, int i2, byte[] bArr4, byte b2, byte[] bArr5, int i3, int i4, byte[] bArr6, int i5) {
        if (bArr4 != null) {
            a(digest, b2, bArr4);
        }
        digest.update(bArr, 32, 32);
        digest.update(bArr5, i3, i4);
        digest.doFinal(bArr, 0);
        byte[] b3 = bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr);
        byte[] bArr7 = new byte[32];
        a(b3, bArr7, 0);
        if (bArr4 != null) {
            a(digest, b2, bArr4);
        }
        digest.update(bArr7, 0, 32);
        digest.update(bArr3, i2, 32);
        digest.update(bArr5, i3, i4);
        digest.doFinal(bArr, 0);
        byte[] a2 = a(b3, bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr), bArr2);
        System.arraycopy(bArr7, 0, bArr6, i5, 32);
        System.arraycopy(a2, 0, bArr6, i5 + 32, 32);
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2, byte b2, byte[] bArr3, int i3, int i4, byte[] bArr4, int i5) {
        if (a(bArr2, b2)) {
            Digest a2 = a();
            byte[] bArr5 = new byte[64];
            a2.update(bArr, i2, 32);
            a2.doFinal(bArr5, 0);
            byte[] bArr6 = new byte[32];
            a(bArr5, 0, bArr6);
            byte[] bArr7 = new byte[32];
            a(bArr6, bArr7, 0);
            a(a2, bArr5, bArr6, bArr7, 0, bArr2, b2, bArr3, i3, i4, bArr4, i5);
            return;
        }
        throw new IllegalArgumentException("ctx");
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte b2, byte[] bArr4, int i4, int i5, byte[] bArr5, int i6) {
        if (a(bArr3, b2)) {
            Digest a2 = a();
            byte[] bArr6 = new byte[64];
            a2.update(bArr, i2, 32);
            a2.doFinal(bArr6, 0);
            byte[] bArr7 = new byte[32];
            a(bArr6, 0, bArr7);
            a(a2, bArr6, bArr7, bArr2, i3, bArr3, b2, bArr4, i4, i5, bArr5, i6);
            return;
        }
        throw new IllegalArgumentException("ctx");
    }

    private static boolean a(byte[] bArr, int i2, byte[] bArr2, int i3, byte[] bArr3, byte b2, byte[] bArr4, int i4, int i5) {
        if (a(bArr3, b2)) {
            byte[] a2 = a(bArr, i2, 32);
            byte[] a3 = a(bArr, i2 + 32, 32);
            byte[] a4 = a(bArr2, i3, 32);
            if (!b(a2)) {
                return false;
            }
            int[] iArr = new int[8];
            if (!bc.org.bouncycastle.math.ec.rfc8032.b.a(a3, iArr) || !a(a4)) {
                return false;
            }
            c cVar = new c();
            if (!a(a2, true, cVar)) {
                return false;
            }
            c cVar2 = new c();
            if (!a(a4, true, cVar2)) {
                return false;
            }
            Digest a5 = a();
            byte[] bArr5 = new byte[64];
            if (bArr3 != null) {
                a(a5, b2, bArr3);
            }
            a5.update(a2, 0, 32);
            a5.update(a4, 0, 32);
            a5.update(bArr4, i4, i5);
            a5.doFinal(bArr5, 0);
            int[] iArr2 = new int[8];
            bc.org.bouncycastle.math.ec.rfc8032.b.b(bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr5), iArr2);
            int[] iArr3 = new int[4];
            int[] iArr4 = new int[4];
            bc.org.bouncycastle.math.ec.rfc8032.b.b(iArr2, iArr3, iArr4);
            bc.org.bouncycastle.math.ec.rfc8032.b.a(iArr, iArr4, iArr);
            b bVar = new b();
            a(iArr, iArr3, cVar2, iArr4, cVar, bVar);
            return b(bVar);
        }
        throw new IllegalArgumentException("ctx");
    }

    private static boolean a(byte[] bArr, int i2, PublicPoint publicPoint, byte[] bArr2, byte b2, byte[] bArr3, int i3, int i4) {
        if (a(bArr2, b2)) {
            byte[] a2 = a(bArr, i2, 32);
            byte[] a3 = a(bArr, i2 + 32, 32);
            if (!b(a2)) {
                return false;
            }
            int[] iArr = new int[8];
            if (!bc.org.bouncycastle.math.ec.rfc8032.b.a(a3, iArr)) {
                return false;
            }
            c cVar = new c();
            if (!a(a2, true, cVar)) {
                return false;
            }
            c cVar2 = new c();
            X25519Field.negate(publicPoint.a, cVar2.a);
            X25519Field.copy(publicPoint.a, 10, cVar2.b, 0);
            byte[] bArr4 = new byte[32];
            encodePublicPoint(publicPoint, bArr4, 0);
            Digest a4 = a();
            byte[] bArr5 = new byte[64];
            if (bArr2 != null) {
                a(a4, b2, bArr2);
            }
            a4.update(a2, 0, 32);
            a4.update(bArr4, 0, 32);
            a4.update(bArr3, i3, i4);
            a4.doFinal(bArr5, 0);
            int[] iArr2 = new int[8];
            bc.org.bouncycastle.math.ec.rfc8032.b.b(bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr5), iArr2);
            int[] iArr3 = new int[4];
            int[] iArr4 = new int[4];
            bc.org.bouncycastle.math.ec.rfc8032.b.b(iArr2, iArr3, iArr4);
            bc.org.bouncycastle.math.ec.rfc8032.b.a(iArr, iArr4, iArr);
            b bVar = new b();
            a(iArr, iArr3, cVar2, iArr4, cVar, bVar);
            return b(bVar);
        }
        throw new IllegalArgumentException("ctx");
    }

    private static void a(d[] dVarArr) {
        int length = dVarArr.length;
        int[] createTable = X25519Field.createTable(length);
        int[] create = X25519Field.create();
        X25519Field.copy(dVarArr[0].c, 0, create, 0);
        X25519Field.copy(create, 0, createTable, 0);
        int i2 = 0;
        while (true) {
            i2++;
            if (i2 >= length) {
                break;
            }
            X25519Field.mul(create, dVarArr[i2].c, create);
            X25519Field.copy(create, 0, createTable, i2 * 10);
        }
        X25519Field.add(create, create, create);
        X25519Field.invVar(create, create);
        int i3 = i2 - 1;
        int[] create2 = X25519Field.create();
        while (i3 > 0) {
            int i4 = i3 - 1;
            X25519Field.copy(createTable, i4 * 10, create2, 0);
            X25519Field.mul(create2, create, create2);
            X25519Field.mul(create, dVarArr[i3].c, create);
            X25519Field.copy(create2, 0, dVarArr[i3].c, 0);
            i3 = i4;
        }
        X25519Field.copy(create, 0, dVarArr[0].c, 0);
    }

    private static void a(b bVar, c cVar) {
        X25519Field.inv(bVar.c, cVar.b);
        X25519Field.mul(cVar.b, bVar.a, cVar.a);
        int[] iArr = cVar.b;
        X25519Field.mul(iArr, bVar.b, iArr);
        X25519Field.normalize(cVar.a);
        X25519Field.normalize(cVar.b);
    }

    private static void a(d dVar, d dVar2, d dVar3, g gVar) {
        int[] iArr = dVar3.a;
        int[] iArr2 = dVar3.b;
        int[] iArr3 = gVar.a;
        int[] iArr4 = gVar.b;
        X25519Field.apm(dVar.b, dVar.a, iArr2, iArr);
        X25519Field.apm(dVar2.b, dVar2.a, iArr4, iArr3);
        X25519Field.mul(iArr, iArr3, iArr);
        X25519Field.mul(iArr2, iArr4, iArr2);
        X25519Field.mul(dVar.d, dVar2.d, iArr3);
        X25519Field.mul(iArr3, j, iArr3);
        int[] iArr5 = dVar.c;
        X25519Field.add(iArr5, iArr5, iArr4);
        X25519Field.mul(iArr4, dVar2.c, iArr4);
        X25519Field.apm(iArr2, iArr, iArr2, iArr);
        X25519Field.apm(iArr4, iArr3, iArr4, iArr3);
        X25519Field.mul(iArr, iArr2, dVar3.d);
        X25519Field.mul(iArr3, iArr4, dVar3.c);
        X25519Field.mul(iArr, iArr3, dVar3.a);
        X25519Field.mul(iArr2, iArr4, dVar3.b);
    }

    private static void a(e eVar, b bVar, g gVar) {
        int[] iArr = bVar.a;
        int[] iArr2 = bVar.b;
        int[] iArr3 = gVar.a;
        int[] iArr4 = bVar.d;
        int[] iArr5 = bVar.e;
        X25519Field.apm(iArr2, iArr, iArr2, iArr);
        X25519Field.mul(iArr, eVar.a, iArr);
        X25519Field.mul(iArr2, eVar.b, iArr2);
        X25519Field.mul(bVar.d, bVar.e, iArr3);
        X25519Field.mul(iArr3, eVar.c, iArr3);
        X25519Field.apm(iArr2, iArr, iArr5, iArr4);
        X25519Field.apm(bVar.c, iArr3, iArr2, iArr);
        X25519Field.mul(iArr, iArr2, bVar.c);
        X25519Field.mul(iArr, iArr4, bVar.a);
        X25519Field.mul(iArr2, iArr5, bVar.b);
    }

    private static void a(boolean z, e eVar, b bVar, g gVar) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3 = bVar.a;
        int[] iArr4 = bVar.b;
        int[] iArr5 = gVar.a;
        int[] iArr6 = bVar.d;
        int[] iArr7 = bVar.e;
        if (z) {
            iArr2 = iArr3;
            iArr = iArr4;
        } else {
            iArr = iArr3;
            iArr2 = iArr4;
        }
        X25519Field.apm(iArr4, iArr3, iArr4, iArr3);
        X25519Field.mul(iArr, eVar.a, iArr);
        X25519Field.mul(iArr2, eVar.b, iArr2);
        X25519Field.mul(bVar.d, bVar.e, iArr5);
        X25519Field.mul(iArr5, eVar.c, iArr5);
        X25519Field.apm(iArr4, iArr3, iArr7, iArr6);
        X25519Field.apm(bVar.c, iArr5, iArr2, iArr);
        X25519Field.mul(iArr3, iArr4, bVar.c);
        X25519Field.mul(iArr3, iArr6, bVar.a);
        X25519Field.mul(iArr4, iArr7, bVar.b);
    }

    private static void a(boolean z, f fVar, b bVar, g gVar) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3 = bVar.a;
        int[] iArr4 = bVar.b;
        int[] iArr5 = gVar.a;
        int[] iArr6 = bVar.c;
        int[] iArr7 = bVar.d;
        int[] iArr8 = bVar.e;
        if (z) {
            iArr2 = iArr3;
            iArr = iArr4;
        } else {
            iArr = iArr3;
            iArr2 = iArr4;
        }
        X25519Field.apm(iArr4, iArr3, iArr4, iArr3);
        X25519Field.mul(iArr, fVar.a, iArr);
        X25519Field.mul(iArr2, fVar.b, iArr2);
        X25519Field.mul(bVar.d, bVar.e, iArr5);
        X25519Field.mul(iArr5, fVar.c, iArr5);
        X25519Field.mul(bVar.c, fVar.d, iArr6);
        X25519Field.apm(iArr4, iArr3, iArr8, iArr7);
        X25519Field.apm(iArr6, iArr5, iArr2, iArr);
        X25519Field.mul(iArr3, iArr4, bVar.c);
        X25519Field.mul(iArr3, iArr7, bVar.a);
        X25519Field.mul(iArr4, iArr8, bVar.b);
    }

    private static void a(b bVar, d dVar) {
        X25519Field.copy(bVar.a, 0, dVar.a, 0);
        X25519Field.copy(bVar.b, 0, dVar.b, 0);
        X25519Field.copy(bVar.c, 0, dVar.c, 0);
        X25519Field.mul(bVar.d, bVar.e, dVar.d);
    }

    private static void a(c cVar, d dVar) {
        X25519Field.copy(cVar.a, 0, dVar.a, 0);
        X25519Field.copy(cVar.b, 0, dVar.b, 0);
        X25519Field.one(dVar.c);
        X25519Field.mul(cVar.a, cVar.b, dVar.d);
    }

    private static void a(d dVar, f fVar) {
        X25519Field.apm(dVar.b, dVar.a, fVar.b, fVar.a);
        X25519Field.mul(dVar.d, j, fVar.c);
        int[] iArr = dVar.c;
        X25519Field.add(iArr, iArr, fVar.d);
    }

    private static void a(int i2, int i3, e eVar) {
        int i4 = i2 * 8 * 3 * 10;
        for (int i5 = 0; i5 < 8; i5++) {
            int i6 = ((i5 ^ i3) - 1) >> 31;
            X25519Field.cmov(i6, f10o, i4, eVar.a, 0);
            int i7 = i4 + 10;
            X25519Field.cmov(i6, f10o, i7, eVar.b, 0);
            int i8 = i7 + 10;
            X25519Field.cmov(i6, f10o, i8, eVar.c, 0);
            i4 = i8 + 10;
        }
    }

    private static void a(c cVar, d[] dVarArr, int i2, int i3, g gVar) {
        d dVar = new d();
        dVarArr[i2] = dVar;
        a(cVar, dVar);
        d dVar2 = new d();
        d dVar3 = dVarArr[i2];
        a(dVar3, dVar3, dVar2, gVar);
        for (int i4 = 1; i4 < i3; i4++) {
            int i5 = i2 + i4;
            d dVar4 = dVarArr[i5 - 1];
            d dVar5 = new d();
            dVarArr[i5] = dVar5;
            a(dVar4, dVar2, dVar5, gVar);
        }
    }

    private static void a(c cVar, f[] fVarArr, int i2, g gVar) {
        d dVar = new d();
        a(cVar, dVar);
        d dVar2 = new d();
        a(dVar, dVar, dVar2, gVar);
        int i3 = 0;
        while (true) {
            f fVar = new f();
            fVarArr[i3] = fVar;
            a(dVar, fVar);
            i3++;
            if (i3 == i2) {
                return;
            } else {
                a(dVar, dVar2, dVar, gVar);
            }
        }
    }

    private static void a(byte[] bArr, int i2, byte[] bArr2) {
        System.arraycopy(bArr, i2, bArr2, 0, 32);
        bArr2[0] = (byte) (bArr2[0] & 248);
        byte b2 = (byte) (bArr2[31] & ByteCompanionObject.MAX_VALUE);
        bArr2[31] = b2;
        bArr2[31] = (byte) (b2 | 64);
    }

    private static void a(byte[] bArr, b bVar) {
        precompute();
        int[] iArr = new int[8];
        bc.org.bouncycastle.math.ec.rfc8032.b.b(bArr, iArr);
        bc.org.bouncycastle.math.ec.rfc8032.b.a(256, iArr);
        a(iArr);
        e eVar = new e();
        g gVar = new g();
        d(bVar);
        int i2 = 28;
        int i3 = 0;
        while (true) {
            int i4 = 0;
            while (i4 < 8) {
                int i5 = iArr[i4] >>> i2;
                int i6 = (i5 >>> 3) & 1;
                a(i4, (i5 ^ (-i6)) & 7, eVar);
                int i7 = i3 ^ i6;
                X25519Field.cnegate(i7, bVar.a);
                X25519Field.cnegate(i7, bVar.d);
                a(eVar, bVar, gVar);
                i4++;
                i3 = i6;
            }
            i2 -= 4;
            if (i2 < 0) {
                X25519Field.cnegate(i3, bVar.a);
                X25519Field.cnegate(i3, bVar.d);
                return;
            }
            c(bVar);
        }
    }

    private static void a(byte[] bArr, byte[] bArr2, int i2) {
        b bVar = new b();
        a(bArr, bVar);
        if (a(bVar, bArr2, i2) == 0) {
            throw new IllegalStateException();
        }
    }

    private static void a(c cVar, b bVar) {
        byte[] bArr = new byte[253];
        bc.org.bouncycastle.math.ec.rfc8032.b.a(4, bArr);
        f[] fVarArr = new f[4];
        g gVar = new g();
        a(cVar, fVarArr, 4, gVar);
        d(bVar);
        int i2 = 252;
        while (true) {
            byte b2 = bArr[i2];
            if (b2 != 0) {
                a(b2 < 0, fVarArr[(b2 >> 1) ^ (b2 >> 31)], bVar, gVar);
            }
            i2--;
            if (i2 < 0) {
                return;
            } else {
                c(bVar);
            }
        }
    }

    private static void a(int[] iArr, int[] iArr2, c cVar, int[] iArr3, c cVar2, b bVar) {
        precompute();
        byte[] bArr = new byte[256];
        int i2 = 128;
        byte[] bArr2 = new byte[128];
        byte[] bArr3 = new byte[128];
        bc.org.bouncycastle.math.ec.rfc8032.e.a(iArr, 6, bArr);
        bc.org.bouncycastle.math.ec.rfc8032.e.a(iArr2, 4, bArr2);
        bc.org.bouncycastle.math.ec.rfc8032.e.a(iArr3, 4, bArr3);
        f[] fVarArr = new f[4];
        f[] fVarArr2 = new f[4];
        g gVar = new g();
        a(cVar, fVarArr, 4, gVar);
        a(cVar2, fVarArr2, 4, gVar);
        d(bVar);
        do {
            i2--;
            if (i2 < 0) {
                break;
            }
        } while ((bArr[i2] | bArr[i2 + 128] | bArr2[i2] | bArr3[i2]) == 0);
        while (i2 >= 0) {
            byte b2 = bArr[i2];
            if (b2 != 0) {
                a(b2 < 0, m[(b2 >> 1) ^ (b2 >> 31)], bVar, gVar);
            }
            byte b3 = bArr[i2 + 128];
            if (b3 != 0) {
                a(b3 < 0, n[(b3 >> 1) ^ (b3 >> 31)], bVar, gVar);
            }
            byte b4 = bArr2[i2];
            if (b4 != 0) {
                a(b4 < 0, fVarArr[(b4 >> 1) ^ (b4 >> 31)], bVar, gVar);
            }
            byte b5 = bArr3[i2];
            if (b5 != 0) {
                a(b5 < 0, fVarArr2[(b5 >> 1) ^ (b5 >> 31)], bVar, gVar);
            }
            c(bVar);
            i2--;
        }
        c(bVar);
        c(bVar);
    }
}
