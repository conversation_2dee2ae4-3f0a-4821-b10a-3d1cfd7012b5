package androidx.biometric;

import android.content.Context;
import android.content.DialogInterface;
import android.hardware.biometrics.BiometricPrompt;
import android.os.Build;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.biometric.BiometricPrompt;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import java.util.concurrent.Executor;
import org.bouncycastle.i18n.MessageBundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricFragment.smali */
public class BiometricFragment extends Fragment {
    private static final String TAG = "BiometricFragment";
    private android.hardware.biometrics.BiometricPrompt mBiometricPrompt;
    private Bundle mBundle;
    private CancellationSignal mCancellationSignal;
    BiometricPrompt.AuthenticationCallback mClientAuthenticationCallback;
    Executor mClientExecutor;
    DialogInterface.OnClickListener mClientNegativeButtonListener;
    private Context mContext;
    private BiometricPrompt.CryptoObject mCryptoObject;
    private CharSequence mNegativeButtonText;
    private boolean mShowing;
    private boolean mStartRespectingCancel;
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private final Executor mExecutor = new Executor() { // from class: androidx.biometric.BiometricFragment.1
        @Override // java.util.concurrent.Executor
        public void execute(Runnable runnable) {
            BiometricFragment.this.mHandler.post(runnable);
        }
    };
    final BiometricPrompt.AuthenticationCallback mAuthenticationCallback = new BiometricPrompt.AuthenticationCallback() { // from class: androidx.biometric.BiometricFragment.2
        @Override // android.hardware.biometrics.BiometricPrompt.AuthenticationCallback
        public void onAuthenticationError(final int errorCode, final CharSequence errString) {
            if (!Utils.isConfirmingDeviceCredential()) {
                BiometricFragment.this.mClientExecutor.execute(new Runnable() { // from class: androidx.biometric.BiometricFragment.2.1
                    @Override // java.lang.Runnable
                    public void run() {
                        CharSequence error = errString;
                        if (error == null) {
                            error = BiometricFragment.this.mContext.getString(R.string.default_error_msg) + " " + errorCode;
                        }
                        BiometricFragment.this.mClientAuthenticationCallback.onAuthenticationError(Utils.isUnknownError(errorCode) ? 8 : errorCode, error);
                    }
                });
                BiometricFragment.this.cleanup();
            }
        }

        @Override // android.hardware.biometrics.BiometricPrompt.AuthenticationCallback
        public void onAuthenticationHelp(int helpCode, CharSequence helpString) {
        }

        @Override // android.hardware.biometrics.BiometricPrompt.AuthenticationCallback
        public void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult result) {
            final BiometricPrompt.AuthenticationResult promptResult = result != null ? new BiometricPrompt.AuthenticationResult(BiometricFragment.unwrapCryptoObject(result.getCryptoObject())) : new BiometricPrompt.AuthenticationResult(null);
            BiometricFragment.this.mClientExecutor.execute(new Runnable() { // from class: androidx.biometric.BiometricFragment.2.2
                @Override // java.lang.Runnable
                public void run() {
                    BiometricFragment.this.mClientAuthenticationCallback.onAuthenticationSucceeded(promptResult);
                }
            });
            BiometricFragment.this.cleanup();
        }

        @Override // android.hardware.biometrics.BiometricPrompt.AuthenticationCallback
        public void onAuthenticationFailed() {
            BiometricFragment.this.mClientExecutor.execute(new Runnable() { // from class: androidx.biometric.BiometricFragment.2.3
                @Override // java.lang.Runnable
                public void run() {
                    BiometricFragment.this.mClientAuthenticationCallback.onAuthenticationFailed();
                }
            });
        }
    };
    private final DialogInterface.OnClickListener mNegativeButtonListener = new DialogInterface.OnClickListener() { // from class: androidx.biometric.BiometricFragment.3
        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(DialogInterface dialog, int which) {
            BiometricFragment.this.mClientNegativeButtonListener.onClick(dialog, which);
        }
    };
    private final DialogInterface.OnClickListener mDeviceCredentialButtonListener = new DialogInterface.OnClickListener() { // from class: androidx.biometric.BiometricFragment.4
        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(DialogInterface dialog, int which) {
            if (which == -2) {
                Utils.launchDeviceCredentialConfirmation(BiometricFragment.TAG, BiometricFragment.this.getActivity(), BiometricFragment.this.mBundle, null);
            }
        }
    };

    static BiometricFragment newInstance() {
        return new BiometricFragment();
    }

    void setCallbacks(Executor executor, DialogInterface.OnClickListener onClickListener, BiometricPrompt.AuthenticationCallback authenticationCallback) {
        this.mClientExecutor = executor;
        this.mClientNegativeButtonListener = onClickListener;
        this.mClientAuthenticationCallback = authenticationCallback;
    }

    void setCryptoObject(BiometricPrompt.CryptoObject crypto) {
        this.mCryptoObject = crypto;
    }

    void cancel() {
        if (Build.VERSION.SDK_INT >= 29 && isDeviceCredentialAllowed() && !this.mStartRespectingCancel) {
            Log.w(TAG, "Ignoring fast cancel signal");
            return;
        }
        CancellationSignal cancellationSignal = this.mCancellationSignal;
        if (cancellationSignal != null) {
            cancellationSignal.cancel();
        }
        cleanup();
    }

    void cleanup() {
        this.mShowing = false;
        FragmentActivity activity = getActivity();
        if (getFragmentManager() != null) {
            getFragmentManager().beginTransaction().detach(this).commitAllowingStateLoss();
        }
        Utils.maybeFinishHandler(activity);
    }

    protected CharSequence getNegativeButtonText() {
        return this.mNegativeButtonText;
    }

    @Override // androidx.fragment.app.Fragment
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRetainInstance(true);
    }

    void setBundle(Bundle bundle) {
        this.mBundle = bundle;
    }

    boolean isDeviceCredentialAllowed() {
        Bundle bundle = this.mBundle;
        return bundle != null && bundle.getBoolean("allow_device_credential", false);
    }

    @Override // androidx.fragment.app.Fragment
    public void onAttach(Context context) {
        super.onAttach(context);
        this.mContext = context;
    }

    @Override // androidx.fragment.app.Fragment
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Bundle bundle;
        if (!this.mShowing && (bundle = this.mBundle) != null) {
            this.mNegativeButtonText = bundle.getCharSequence("negative_text");
            BiometricPrompt.Builder builder = new BiometricPrompt.Builder(getContext());
            builder.setTitle(this.mBundle.getCharSequence(MessageBundle.TITLE_ENTRY)).setSubtitle(this.mBundle.getCharSequence("subtitle")).setDescription(this.mBundle.getCharSequence("description"));
            boolean allowDeviceCredential = this.mBundle.getBoolean("allow_device_credential");
            if (allowDeviceCredential && Build.VERSION.SDK_INT <= 28) {
                String string = getString(R.string.confirm_device_credential_password);
                this.mNegativeButtonText = string;
                builder.setNegativeButton(string, this.mClientExecutor, this.mDeviceCredentialButtonListener);
            } else if (!TextUtils.isEmpty(this.mNegativeButtonText)) {
                builder.setNegativeButton(this.mNegativeButtonText, this.mClientExecutor, this.mNegativeButtonListener);
            }
            if (Build.VERSION.SDK_INT >= 29) {
                builder.setConfirmationRequired(this.mBundle.getBoolean("require_confirmation", true));
                builder.setDeviceCredentialAllowed(allowDeviceCredential);
            }
            if (allowDeviceCredential) {
                this.mStartRespectingCancel = false;
                this.mHandler.postDelayed(new Runnable() { // from class: androidx.biometric.BiometricFragment.5
                    @Override // java.lang.Runnable
                    public void run() {
                        BiometricFragment.this.mStartRespectingCancel = true;
                    }
                }, 250L);
            }
            this.mBiometricPrompt = builder.build();
            CancellationSignal cancellationSignal = new CancellationSignal();
            this.mCancellationSignal = cancellationSignal;
            BiometricPrompt.CryptoObject cryptoObject = this.mCryptoObject;
            if (cryptoObject == null) {
                this.mBiometricPrompt.authenticate(cancellationSignal, this.mExecutor, this.mAuthenticationCallback);
            } else {
                this.mBiometricPrompt.authenticate(wrapCryptoObject(cryptoObject), this.mCancellationSignal, this.mExecutor, this.mAuthenticationCallback);
            }
        }
        this.mShowing = true;
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BiometricPrompt.CryptoObject unwrapCryptoObject(BiometricPrompt.CryptoObject cryptoObject) {
        if (cryptoObject == null) {
            return null;
        }
        if (cryptoObject.getCipher() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getCipher());
        }
        if (cryptoObject.getSignature() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getSignature());
        }
        if (cryptoObject.getMac() == null) {
            return null;
        }
        return new BiometricPrompt.CryptoObject(cryptoObject.getMac());
    }

    private static BiometricPrompt.CryptoObject wrapCryptoObject(BiometricPrompt.CryptoObject cryptoObject) {
        if (cryptoObject == null) {
            return null;
        }
        if (cryptoObject.getCipher() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getCipher());
        }
        if (cryptoObject.getSignature() != null) {
            return new BiometricPrompt.CryptoObject(cryptoObject.getSignature());
        }
        if (cryptoObject.getMac() == null) {
            return null;
        }
        return new BiometricPrompt.CryptoObject(cryptoObject.getMac());
    }
}
