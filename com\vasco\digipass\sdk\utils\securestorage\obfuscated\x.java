package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\x.smali */
public abstract class x {
    public static final String a;
    public static final String b;
    public static final String c;
    public static final String d;
    public static final String e;
    public static final String f;
    public static final String g;

    static {
        byte[] bArr = new byte[4];
        int[] iArr = {74, 73, 72, 70};
        for (int i = 0; i < 4; i++) {
            bArr[i] = (byte) (((iArr[i] + i) ^ 63) - 325);
        }
        String str = new String(bArr);
        Intrinsics.checkNotNullExpressionValue(str, "getStorageVersion0001()");
        a = str;
        byte[] bArr2 = new byte[4];
        int[] iArr2 = {59, 59, 59, 61};
        for (int i2 = 0; i2 < 4; i2++) {
            bArr2[i2] = (byte) (((iArr2[i2] - 11) ^ i2) ^ i2);
        }
        String str2 = new String(bArr2);
        Intrinsics.checkNotNullExpressionValue(str2, "getStorageVersion0002()");
        b = str2;
        byte[] bArr3 = new byte[4];
        int[] iArr3 = {23, 24, 25, 29};
        for (int i3 = 0; i3 < 4; i3++) {
            bArr3[i3] = (byte) ((iArr3[i3] - i3) + 25);
        }
        String str3 = new String(bArr3);
        Intrinsics.checkNotNullExpressionValue(str3, "getStorageVersion0003()");
        c = str3;
        byte[] bArr4 = new byte[4];
        int[] iArr4 = {80, 80, 80, 88};
        for (int i4 = 0; i4 < 4; i4++) {
            int i5 = iArr4[i4];
            bArr4[i4] = (byte) (((((i5 & 255) >> 1) | (i5 << 7)) & 255) + 8);
        }
        String str4 = new String(bArr4);
        Intrinsics.checkNotNullExpressionValue(str4, "getStorageVersion0004()");
        d = str4;
        byte[] bArr5 = new byte[4];
        int[] iArr5 = {Opcodes.LSUB, Opcodes.LSUB, Opcodes.LSUB, 90};
        for (int i6 = 0; i6 < 4; i6++) {
            bArr5[i6] = (byte) (((iArr5[i6] ^ 68) + Opcodes.IFGT) ^ Opcodes.D2I);
        }
        Intrinsics.checkNotNullExpressionValue(new String(bArr5), "getStorageVersion0005()");
        byte[] bArr6 = new byte[4];
        int[] iArr6 = {217, 217, 217, 223};
        for (int i7 = 0; i7 < 4; i7++) {
            bArr6[i7] = (byte) (((iArr6[i7] - 169) - i7) + i7);
        }
        String str5 = new String(bArr6);
        Intrinsics.checkNotNullExpressionValue(str5, "getStorageVersion0006()");
        e = str5;
        String c2 = w.c();
        Intrinsics.checkNotNullExpressionValue(c2, "getSeparatorKeyValuePairs()");
        f = c2;
        String b2 = w.b();
        Intrinsics.checkNotNullExpressionValue(b2, "getSeparatorKeyValue()");
        g = b2;
    }
}
