package androidx.core.location;

import android.location.Location;
import kotlin.Metadata;

/* compiled from: Location.kt */
@Metadata(d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\r\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0086\n\u001a\r\u0010\u0003\u001a\u00020\u0001*\u00020\u0002H\u0086\n¨\u0006\u0004"}, d2 = {"component1", "", "Landroid/location/Location;", "component2", "core-ktx_release"}, k = 2, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\location\LocationKt.smali */
public final class LocationKt {
    public static final double component1(Location $this$component1) {
        return $this$component1.getLatitude();
    }

    public static final double component2(Location $this$component2) {
        return $this$component2.getLongitude();
    }
}
