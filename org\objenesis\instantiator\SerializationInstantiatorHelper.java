package org.objenesis.instantiator;

import java.io.Serializable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\objenesis\instantiator\SerializationInstantiatorHelper.smali */
public class SerializationInstantiatorHelper {
    public static <T> Class<? super T> getNonSerializableSuperClass(Class<T> cls) {
        Class<? super T> cls2 = (Class<? super T>) cls;
        while (Serializable.class.isAssignableFrom(cls2)) {
            cls2 = cls2.getSuperclass();
            if (cls2 == null) {
                throw new Error("Bad class hierarchy: No non-serializable parents");
            }
        }
        return cls2;
    }
}
