package androidx.navigation;

import android.os.Bundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\navigation\ActionOnlyNavDirections.smali */
public final class ActionOnlyNavDirections implements NavDirections {
    private final int mActionId;

    public ActionOnlyNavDirections(int actionId) {
        this.mActionId = actionId;
    }

    @Override // androidx.navigation.NavDirections
    public int getActionId() {
        return this.mActionId;
    }

    @Override // androidx.navigation.NavDirections
    public Bundle getArguments() {
        return new Bundle();
    }

    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }
        if (object == null || getClass() != object.getClass()) {
            return false;
        }
        ActionOnlyNavDirections that = (ActionOnlyNavDirections) object;
        if (getActionId() == that.getActionId()) {
            return true;
        }
        return false;
    }

    public int hashCode() {
        int result = (1 * 31) + getActionId();
        return result;
    }

    public String toString() {
        return "ActionOnlyNavDirections(actionId=" + getActionId() + ")";
    }
}
