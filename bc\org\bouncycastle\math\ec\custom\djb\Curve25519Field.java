package bc.org.bouncycastle.math.ec.custom.djb;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.math.BigInteger;
import java.security.SecureRandom;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\djb\Curve25519Field.smali */
public class Curve25519Field {
    static final int[] a = {-19, -1, -1, -1, -1, -1, -1, Integer.MAX_VALUE};
    private static final int[] b = {361, 0, 0, 0, 0, 0, 0, 0, -19, -1, -1, -1, -1, -1, -1, Lock<PERSON>reeTaskQueueCore.MAX_CAPACITY_MASK};

    private static int a(int[] iArr) {
        int[] iArr2 = b;
        long j = (iArr[0] & 4294967295L) + (iArr2[0] & 4294967295L);
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            j2 = c6.c(8, iArr, 1);
        }
        long j3 = j2 + ((iArr[8] & 4294967295L) - 19);
        iArr[8] = (int) j3;
        long j4 = j3 >> 32;
        if (j4 != 0) {
            j4 = c6.a(15, iArr, 9);
        }
        long j5 = j4 + (iArr[15] & 4294967295L) + (4294967295L & (iArr2[15] + 1));
        iArr[15] = (int) j5;
        return (int) (j5 >> 32);
    }

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        w5.a(iArr, iArr2, iArr3);
        if (w5.c(iArr3, a)) {
            d(iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        c6.a(16, iArr, iArr2, iArr3);
        if (c6.d(16, iArr3, b)) {
            c(iArr3);
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        c6.e(8, iArr, iArr2);
        if (w5.c(iArr2, a)) {
            d(iArr2);
        }
    }

    private static int b(int[] iArr) {
        long j = (iArr[0] & 4294967295L) - 19;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            j2 = c6.a(7, iArr, 1);
        }
        long j3 = j2 + (4294967295L & iArr[7]) + 2147483648L;
        iArr[7] = (int) j3;
        return (int) (j3 >> 32);
    }

    private static int c(int[] iArr) {
        int[] iArr2 = b;
        long j = (iArr[0] & 4294967295L) - (iArr2[0] & 4294967295L);
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            j2 = c6.a(8, iArr, 1);
        }
        long j3 = j2 + (iArr[8] & 4294967295L) + 19;
        iArr[8] = (int) j3;
        long j4 = j3 >> 32;
        if (j4 != 0) {
            j4 = c6.c(15, iArr, 9);
        }
        long j5 = j4 + ((iArr[15] & 4294967295L) - (4294967295L & (iArr2[15] + 1)));
        iArr[15] = (int) j5;
        return (int) (j5 >> 32);
    }

    private static int d(int[] iArr) {
        long j = (iArr[0] & 4294967295L) + 19;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            j2 = c6.c(7, iArr, 1);
        }
        long j3 = j2 + ((4294967295L & iArr[7]) - 2147483648L);
        iArr[7] = (int) j3;
        return (int) (j3 >> 32);
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = w5.a(bigInteger);
        while (true) {
            int[] iArr = a;
            if (!w5.c(a2, iArr)) {
                return a2;
            }
            w5.e(iArr, a2);
        }
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(8, iArr, 0, iArr2);
        } else {
            w5.a(iArr, a, iArr2);
            c6.d(8, iArr2, 0);
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 8; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] c = w5.c();
        w5.c(iArr, iArr2, c);
        reduce(c, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        w5.e(iArr, iArr2, iArr3);
        if (c6.d(16, iArr3, b)) {
            c(iArr3);
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            w5.f(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            w5.f(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[32];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 8);
            iArr[7] = iArr[7] & Integer.MAX_VALUE;
        } while (c6.f(8, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        int i = iArr[7];
        c6.a(8, iArr, 8, i, iArr2, 0);
        int a2 = w5.a(19, iArr, iArr2) << 1;
        int i2 = iArr2[7];
        iArr2[7] = (i2 & Integer.MAX_VALUE) + c6.b(7, (a2 + ((i2 >>> 31) - (i >>> 31))) * 19, iArr2);
        if (w5.c(iArr2, a)) {
            d(iArr2);
        }
    }

    public static void reduce27(int i, int[] iArr) {
        int i2 = iArr[7];
        iArr[7] = (i2 & Integer.MAX_VALUE) + c6.b(7, ((i << 1) | (i2 >>> 31)) * 19, iArr);
        if (w5.c(iArr, a)) {
            d(iArr);
        }
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] c = w5.c();
        w5.d(iArr, c);
        reduce(c, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] c = w5.c();
        w5.d(iArr, c);
        reduce(c, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            w5.d(iArr2, c);
            reduce(c, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (w5.f(iArr, iArr2, iArr3) != 0) {
            b(iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(16, iArr, iArr2, iArr3) != 0) {
            a(iArr3);
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        c6.b(8, iArr, 0, iArr2);
        if (w5.c(iArr2, a)) {
            d(iArr2);
        }
    }
}
