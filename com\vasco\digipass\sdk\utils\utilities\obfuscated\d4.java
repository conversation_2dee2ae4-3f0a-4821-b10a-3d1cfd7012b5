package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.crypto.params.ECDomainParameters;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d4.smali */
public class d4 extends ECDomainParameters {
    private w g;

    public d4(w wVar, ECDomainParameters eCDomainParameters) {
        super(eCDomainParameters.getCurve(), eCDomainParameters.getG(), eCDomainParameters.getN(), eCDomainParameters.getH(), eCDomainParameters.getSeed());
        this.g = wVar;
    }

    public w a() {
        return this.g;
    }

    public d4(w wVar, X9ECParameters x9ECParameters) {
        super(x9ECParameters);
        this.g = wVar;
    }
}
