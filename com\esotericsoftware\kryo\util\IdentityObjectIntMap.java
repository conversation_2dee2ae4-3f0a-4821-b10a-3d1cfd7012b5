package com.esotericsoftware.kryo.util;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\IdentityObjectIntMap.smali */
public class IdentityObjectIntMap<K> extends ObjectIntMap<K> {
    public IdentityObjectIntMap() {
    }

    public IdentityObjectIntMap(int initialCapacity) {
        super(initialCapacity);
    }

    public IdentityObjectIntMap(int initialCapacity, float loadFactor) {
        super(initialCapacity, loadFactor);
    }

    public IdentityObjectIntMap(IdentityObjectIntMap<K> map) {
        super(map);
    }

    @Override // com.esotericsoftware.kryo.util.ObjectIntMap
    protected int place(K item) {
        return System.identityHashCode(item) & this.mask;
    }

    @Override // com.esotericsoftware.kryo.util.ObjectIntMap
    public int get(K key, int defaultValue) {
        int i = place(key);
        while (true) {
            K other = this.keyTable[i];
            if (other == null) {
                return defaultValue;
            }
            if (other == key) {
                return this.valueTable[i];
            }
            i = (i + 1) & this.mask;
        }
    }

    @Override // com.esotericsoftware.kryo.util.ObjectIntMap
    int locateKey(K key) {
        if (key == null) {
            throw new IllegalArgumentException("key cannot be null.");
        }
        K[] keyTable = this.keyTable;
        int i = place(key);
        while (true) {
            K other = keyTable[i];
            if (other == null) {
                return -(i + 1);
            }
            if (other == key) {
                return i;
            }
            i = (i + 1) & this.mask;
        }
    }

    @Override // com.esotericsoftware.kryo.util.ObjectIntMap
    public int hashCode() {
        int h = this.size;
        K[] keyTable = this.keyTable;
        int[] valueTable = this.valueTable;
        int n = keyTable.length;
        for (int i = 0; i < n; i++) {
            K key = keyTable[i];
            if (key != null) {
                h += System.identityHashCode(key) + valueTable[i];
            }
        }
        return h;
    }
}
