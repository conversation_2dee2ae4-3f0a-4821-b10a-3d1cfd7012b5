package o.er;

import android.os.Process;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import java.util.ArrayList;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\b.smali */
public final class b extends o.el.e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int g;
    private static int j;
    private final i b;
    private final o.eo.a c;
    private final f d;
    private final i e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        j = 1;
        a = 8198151081972024752L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void D(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 1
            int r6 = r6 * 3
            int r6 = r6 + 4
            byte[] r0 = o.er.b.$$a
            int r8 = r8 * 3
            int r8 = r8 + 68
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L30
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1f:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r7]
        L30:
            int r4 = -r4
            int r6 = r6 + r4
            int r7 = r7 + 1
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.b.D(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = 3;
    }

    @Override // o.ee.d
    public final /* synthetic */ EmvApplication a() {
        EmvApplication A;
        int i = g + 73;
        j = i % 128;
        switch (i % 2 != 0) {
            case false:
                A = A();
                int i2 = 56 / 0;
                break;
            default:
                A = A();
                break;
        }
        int i3 = g + 79;
        j = i3 % 128;
        switch (i3 % 2 == 0 ? '\b' : '\r') {
            case '\b':
                throw null;
            default:
                return A;
        }
    }

    public b(String str, i iVar, i iVar2, f fVar, o.eo.a aVar) {
        super(str);
        this.b = iVar;
        this.e = iVar2;
        this.d = fVar;
        this.c = aVar;
    }

    private b(String str) {
        super(str);
        this.b = new i(false, null);
        this.e = new i(false, null);
        this.d = new f(false, null);
        this.c = null;
    }

    @Override // o.el.d
    public final o.el.d b(String str) {
        b bVar = new b(str);
        int i = g + Opcodes.LREM;
        j = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    @Override // o.el.d
    public final o.ey.e<? extends o.fc.d> a(String str) {
        int i = j + 31;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        int i4 = i2 + 9;
        j = i4 % 128;
        switch (i4 % 2 == 0 ? '8' : 'W') {
            case Opcodes.POP /* 87 */:
                return null;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    @Override // o.el.d
    public final boolean o() {
        int i = g + 39;
        j = i % 128;
        switch (i % 2 != 0) {
        }
        return false;
    }

    @Override // o.el.d
    public final String r() {
        Object[] objArr = new Object[1];
        B("ᡖ隕ɴ떪ᠯ鎟ࣆꖋඅ跉⋎鏓㍏ꘆ㑊蹏夃큕丒\ue442仐쪫揎틾璕\ue4e0痞줺驍ᅢ轜❰耆ଦꄟᶶ뗆▶몯௪\udbaf忰챺昦셪䡲\ue624屠\uf730戶ﯢ䪢᳡鳃ථꃖʶ襎❿鼐⡽ꍍ㤧\uf551带\udd95", 1 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        throw new RuntimeException(((String) objArr[0]).intern());
    }

    @Override // o.el.d
    public final o.ei.a z() {
        int i = j + 71;
        g = i % 128;
        int i2 = i % 2;
        o.ei.a aVar = o.ei.a.b;
        int i3 = j + 99;
        g = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 25 : (char) 4) {
            case 4:
                return aVar;
            default:
                int i4 = 63 / 0;
                return aVar;
        }
    }

    private static EmvApplication A() {
        Object[] objArr = new Object[1];
        B("ꡰ闉᷂\u08d2ꠔ郏\u177b\u18f6붸躃㴴\u2efb荩ꕍ⯷㍸\ue931팄冤女ﻥ짻籺濏쒽\ue7a2樸瑇⩱ች郥験〽࠵뺮ꃐְ⛵ꔔ뚐殙岮폜\udb1b煆䭡呂\ue157䜆愯\ue410\uf7d6곘龆ሃᶼ늙詖㢈≯顑ꀌ⚁䠤\uee00\uded6䵙", (Process.myTid() >> 22) + 1, objArr);
        throw new RuntimeException(((String) objArr[0]).intern());
    }

    @Override // o.el.e
    public final i c() {
        int i = j + 67;
        int i2 = i % 128;
        g = i2;
        switch (i % 2 != 0 ? '!' : '@') {
            case '@':
                i iVar = this.b;
                int i3 = i2 + 17;
                j = i3 % 128;
                int i4 = i3 % 2;
                return iVar;
            default:
                throw null;
        }
    }

    @Override // o.el.e
    public final i e() {
        int i = j;
        int i2 = i + Opcodes.LSHL;
        g = i2 % 128;
        int i3 = i2 % 2;
        i iVar = this.e;
        int i4 = i + Opcodes.LREM;
        g = i4 % 128;
        switch (i4 % 2 != 0 ? '6' : Typography.quote) {
            case '\"':
                return iVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.el.e
    public final f i() {
        f fVar;
        int i = g;
        int i2 = i + 17;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                fVar = this.d;
                break;
            default:
                fVar = this.d;
                int i3 = 95 / 0;
                break;
        }
        int i4 = i + 35;
        j = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public final o.eo.a C() {
        o.eo.a aVar;
        int i = j;
        int i2 = i + Opcodes.LNEG;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? '6' : '0') {
            case '0':
                aVar = this.c;
                break;
            default:
                aVar = this.c;
                int i3 = 78 / 0;
                break;
        }
        int i4 = i + 109;
        g = i4 % 128;
        int i5 = i4 % 2;
        return aVar;
    }

    @Override // o.el.e
    public final i b() {
        Object obj = null;
        i iVar = new i(false, null);
        int i = j + 45;
        g = i % 128;
        switch (i % 2 != 0) {
            case false:
                return iVar;
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.el.e
    public final i d() {
        Object obj = null;
        i iVar = new i(false, null);
        int i = j + 51;
        g = i % 128;
        switch (i % 2 != 0 ? Typography.quote : '[') {
            case '\"':
                obj.hashCode();
                throw null;
            default:
                return iVar;
        }
    }

    @Override // o.el.e
    public final i j() {
        i iVar = new i(false, null);
        int i = g + 73;
        j = i % 128;
        int i2 = i % 2;
        return iVar;
    }

    @Override // o.el.e
    public final f g() {
        f fVar = new f(false, null);
        int i = g + Opcodes.DNEG;
        j = i % 128;
        switch (i % 2 == 0 ? 'Q' : '0') {
            case '0':
                return fVar;
            default:
                throw null;
        }
    }

    @Override // o.el.e
    public final i f() {
        i iVar = new i(false, null);
        int i = j + Opcodes.LMUL;
        g = i % 128;
        int i2 = i % 2;
        return iVar;
    }

    @Override // o.el.e
    public final c h() {
        c cVar = new c(false, null, false);
        int i = j + Opcodes.DREM;
        g = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    @Override // o.el.e
    public final i l() {
        i iVar = new i(false, null);
        int i = j + 39;
        g = i % 128;
        int i2 = i % 2;
        return iVar;
    }

    @Override // o.el.e
    public final w m() {
        w wVar = new w(false, null, new ArrayList());
        int i = j + 75;
        g = i % 128;
        int i2 = i % 2;
        return wVar;
    }

    @Override // o.el.e
    public final i k() {
        i iVar = new i(false, null);
        int i = j + 63;
        g = i % 128;
        int i2 = i % 2;
        return iVar;
    }

    /* JADX WARN: Removed duplicated region for block: B:44:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0028  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void B(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 384
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.b.B(java.lang.String, int, java.lang.Object[]):void");
    }
}
