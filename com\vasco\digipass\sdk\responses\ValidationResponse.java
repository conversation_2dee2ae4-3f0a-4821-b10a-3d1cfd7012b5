package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.obfuscated.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\ValidationResponse.smali */
public class ValidationResponse extends GenericResponse {
    private byte[] f;

    public ValidationResponse(int i) {
        super(i);
    }

    public byte[] getEncryptionKey() {
        return q.c(this.f);
    }

    public ValidationResponse(int i, Throwable th) {
        super(i, th);
    }

    public ValidationResponse(int i, int i2, byte[] bArr, int i3, byte[] bArr2) {
        super(i, i2, bArr, i3);
        this.f = q.c(bArr2);
    }
}
