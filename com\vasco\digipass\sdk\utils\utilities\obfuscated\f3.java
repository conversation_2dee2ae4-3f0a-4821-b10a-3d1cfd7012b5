package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f3.smali */
public class f3 extends f0 {
    private int L;

    public f3() {
        this.L = -1;
    }

    private int i() throws IOException {
        if (this.L < 0) {
            int length = this.b.length;
            int i = 0;
            for (int i2 = 0; i2 < length; i2++) {
                i += this.b[i2].toASN1Primitive().g().a(true);
            }
            this.L = i;
        }
        return this.L;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        return z.a(z, i());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.f0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 49);
        c3 c = zVar.c();
        int length = this.b.length;
        int i = 0;
        if (this.L >= 0 || length > 16) {
            zVar.d(i());
            while (i < length) {
                c.a(this.b[i].toASN1Primitive(), true);
                i++;
            }
            return;
        }
        b0[] b0VarArr = new b0[length];
        int i2 = 0;
        for (int i3 = 0; i3 < length; i3++) {
            b0 g = this.b[i3].toASN1Primitive().g();
            b0VarArr[i3] = g;
            i2 += g.a(true);
        }
        this.L = i2;
        zVar.d(i2);
        while (i < length) {
            c.a(b0VarArr[i], true);
            i++;
        }
    }

    public f3(i iVar) {
        super(iVar, false);
        this.L = -1;
    }

    f3(boolean z, h[] hVarArr) {
        super(z, hVarArr);
        this.L = -1;
    }

    f3(h[] hVarArr, h[] hVarArr2) {
        super(hVarArr, hVarArr2);
        this.L = -1;
    }
}
