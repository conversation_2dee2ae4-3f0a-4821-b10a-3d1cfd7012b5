package com.vasco.digipass.sdk.utils.securestorage;

import com.vasco.digipass.sdk.utils.securestorage.obfuscated.v;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;

@Metadata(d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0003\n\u0002\b\n\u0018\u0000 \u00112\u00060\u0001j\u0002`\u0002:\u0001\u0012B\u001b\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t¢\u0006\u0004\b\u000f\u0010\u0010R\u0017\u0010\b\u001a\u00020\u00038\u0006¢\u0006\f\n\u0004\b\u0004\u0010\u0005\u001a\u0004\b\u0006\u0010\u0007R\u001c\u0010\u000e\u001a\u0004\u0018\u00010\t8\u0016X\u0096\u0004¢\u0006\f\n\u0004\b\n\u0010\u000b\u001a\u0004\b\f\u0010\r¨\u0006\u0013"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorageSDKException;", "Ljava/lang/Exception;", "Lkotlin/Exception;", "", "a", "I", "getErrorCode", "()I", "errorCode", "", "b", "Ljava/lang/Throwable;", "getCause", "()Ljava/lang/Throwable;", "cause", "<init>", "(ILjava/lang/Throwable;)V", "Companion", "com/vasco/digipass/sdk/utils/securestorage/obfuscated/v", "lib_release"}, k = 1, mv = {1, 8, 0})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\SecureStorageSDKException.smali */
public final class SecureStorageSDKException extends Exception {
    public static final v Companion = new v();

    /* renamed from: a, reason: from kotlin metadata */
    public final int errorCode;

    /* renamed from: b, reason: from kotlin metadata */
    public final Throwable cause;

    public /* synthetic */ SecureStorageSDKException(int i, Throwable th, int i2, DefaultConstructorMarker defaultConstructorMarker) {
        this(i, (i2 & 2) != 0 ? null : th);
    }

    @Override // java.lang.Throwable
    public Throwable getCause() {
        return this.cause;
    }

    public final int getErrorCode() {
        return this.errorCode;
    }

    public SecureStorageSDKException(int i, Throwable th) {
        this.errorCode = i;
        this.cause = th;
    }
}
