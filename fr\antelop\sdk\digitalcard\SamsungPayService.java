package fr.antelop.sdk.digitalcard;

import android.app.Activity;
import android.content.Context;
import fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import o.eo.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SamsungPayService.smali */
public final class SamsungPayService {
    private final d innerSamsungPayService;
    private final d innerSamsungWatchPayService;

    SamsungPayService(e eVar) throws WalletValidationException {
        this.innerSamsungPayService = new d(eVar, (o.el.e) eVar.H(), false);
        this.innerSamsungWatchPayService = new d(eVar, (o.el.e) eVar.H(), true);
    }

    public final void getStatus(Context context, OperationCallback<DigitalCardServiceStatus> operationCallback) {
        this.innerSamsungPayService.b(context, operationCallback);
    }

    public final void getTspTokenId(Context context, OperationCallback<String> operationCallback) throws WalletValidationException {
        this.innerSamsungPayService.h(context, operationCallback);
    }

    public final void getTspTokenIdOnWatch(Context context, OperationCallback<String> operationCallback) throws WalletValidationException {
        this.innerSamsungWatchPayService.h(context, operationCallback);
    }

    public final void getStatusOnWatch(Context context, OperationCallback<DigitalCardServiceStatus> operationCallback) {
        this.innerSamsungWatchPayService.b(context, operationCallback);
    }

    public final void configureWallet(Activity activity) {
        this.innerSamsungPayService.d(activity);
    }

    public final void configureWalletOnWatch(Activity activity) {
        this.innerSamsungWatchPayService.d(activity);
    }

    public final void isCardInSamsungPay(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerSamsungPayService.c(context, operationCallback);
    }

    public final void isCardInSamsungPayOnWatch(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerSamsungWatchPayService.c(context, operationCallback);
    }

    public final void isCardInSamsungPayOffline(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerSamsungPayService.a(context, operationCallback);
    }

    public final void isCardInSamsungPayOnWatchOffline(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerSamsungWatchPayService.a(context, operationCallback);
    }

    public final void pushCard(Activity activity, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerSamsungPayService.b(activity, operationCallback);
    }

    public final void pushCardOnWatch(Activity activity, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerSamsungWatchPayService.b(activity, operationCallback);
    }

    public final SecureCardPushToSamsungPay getSecureCardPush() {
        return this.innerSamsungPayService.f();
    }

    public final SecureCardPushToSamsungPay getSecureCardPushOnWatch() {
        return this.innerSamsungWatchPayService.f();
    }

    public final void showCard(Activity activity) {
        this.innerSamsungPayService.b(activity);
    }
}
