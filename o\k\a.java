package o.k;

import android.content.Context;
import android.content.res.Resources;
import android.os.CancellationSignal;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.biometric.BiometricPrompt;
import androidx.fragment.app.FragmentActivity;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import java.util.Date;
import java.util.concurrent.Executor;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import kotlin.text.Typography;
import o.ee.b;
import o.ee.g;
import o.ee.o;
import o.f.d;
import o.f.e;
import o.i.j;
import o.k.a;
import o.o.c;
import o.o.e;
import org.bouncycastle.math.Primes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\k\a.smali */
public final class a extends c {
    private static final int a;
    private static final int b;
    private static final int c;
    private static final int e;
    private static char g;
    private static final int h;
    private static char l;
    private static int m;
    private static char n;

    /* renamed from: o, reason: collision with root package name */
    private static char f90o;
    final j d;
    private final String f;
    private final String i;
    private final String j;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int k = 0;

    static void i() {
        f90o = (char) 51088;
        l = (char) 37027;
        n = (char) 6715;
        g = (char) 43428;
    }

    static {
        m = 1;
        i();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        c = R.string.antelopDeviceBiometricPromptName;
        a = R.string.antelopDeviceBiometricPromptDefaultTitle;
        b = R.string.antelopDeviceBiometricPromptDefaultSubtitle;
        e = R.string.antelopDeviceBiometricPromptCancelButtonLabel;
        h = R.drawable.antelopDeviceBiometricPromptIcon;
        int i = k + 51;
        m = i % 128;
        switch (i % 2 == 0 ? 'B' : (char) 26) {
            case 26:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    @Override // o.o.c
    public final boolean c() {
        int i = k;
        int i2 = i + 57;
        m = i2 % 128;
        switch (i2 % 2 == 0) {
        }
        int i3 = i + Opcodes.LSUB;
        m = i3 % 128;
        switch (i3 % 2 == 0 ? '\f' : 'N') {
            case '\f':
                throw null;
            default:
                return false;
        }
    }

    private static String c(Context context) {
        int i = m + 45;
        k = i % 128;
        char c2 = i % 2 != 0 ? '*' : (char) 1;
        Resources resources = context.getResources();
        switch (c2) {
            case '*':
                resources.getString(c);
                throw null;
            default:
                return resources.getString(c);
        }
    }

    private static String e(Context context) {
        int i = k + Opcodes.DREM;
        m = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(a);
        int i3 = m + 3;
        k = i3 % 128;
        switch (i3 % 2 != 0 ? '\'' : 'Z') {
            case '\'':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return string;
        }
    }

    private static String a(Context context) {
        int i = k + 75;
        m = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(b);
        int i3 = m + 87;
        k = i3 % 128;
        int i4 = i3 % 2;
        return string;
    }

    private static String d(Context context) {
        int i = m + 3;
        k = i % 128;
        char c2 = i % 2 != 0 ? Typography.amp : (char) 25;
        Object obj = null;
        Resources resources = context.getResources();
        switch (c2) {
            case 25:
                String string = resources.getString(e);
                int i2 = m + 85;
                k = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 19 : '4') {
                    case 19:
                        obj.hashCode();
                        throw null;
                    default:
                        return string;
                }
            default:
                resources.getString(e);
                throw null;
        }
    }

    public a(Context context, String str, String str2, j jVar) {
        super(c(context), h);
        if (str == null) {
            this.i = o.e((CharSequence) e(context));
        } else {
            this.i = o.e((CharSequence) str);
        }
        if (str2 == null) {
            this.f = o.e((CharSequence) a(context));
        } else {
            this.f = o.e((CharSequence) str2);
        }
        this.j = d(context);
        this.d = jVar;
    }

    private String h() {
        int i = m + 65;
        k = i % 128;
        char c2 = i % 2 == 0 ? ',' : (char) 2;
        String str = this.j;
        switch (c2) {
            default:
                int i2 = 39 / 0;
            case ',':
                return str;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void b(HandlerThread handlerThread, Runnable runnable) {
        new b(handlerThread.getLooper()).post(runnable);
        int i = k + Opcodes.LREM;
        m = i % 128;
        switch (i % 2 == 0 ? 'Y' : '/') {
            case '/':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* renamed from: o.k.a$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\k\a$4.smali */
    final class AnonymousClass4 extends BiometricPrompt.AuthenticationCallback {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        private static int f;
        private static int h;
        private /* synthetic */ o.o.b b;
        private /* synthetic */ Handler c;
        private /* synthetic */ HandlerThread d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            h = 1;
            a = new char[]{50781, 50774, 50757, 50759, 50781, 50774, 50776, 50753, 50754, 50774, 50769, 50853, 50772, 50753, 50777, 50779, 50774, 50775, 50769, 50855, 50759, 50781, 50772, 50774, 50753, 50777, 50779, 50781, 50852, 50753, 50759, 50781, 50768, 50753, 50854, 50772, 50753, 50776, 50776, 50877, 50774, 50778, 50777, 50779, 50772, 50874, 50776, 50779, 50935, 50854, 50847, 50842, 50835, 50857, 50848, 50859, 50851, 50876, 50851, 50859, 50831, 50912, 50912, 50820, 50877, 50877, 50879, 50838, 50836, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50842, 50851, 50826, 50923, 50923, 50817, 50875, 50851, 50850, 50850, 50943, 50859, 50851, 50876, 50851, 50859, 50848, 50857, 50835, 50842, 50847, 50854, 50851, 50850, 50850, 50851, 50875, 50817, 50923, 50923, 50826, 50851, 50842, 50838, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50861, 50857, 50849, 50862, 50857, 50856, 50857, 50857, 50857, 50943, 50859, 50851, 50876, 50851, 50859, 50848, 50857, 50835, 50842, 50847, 50854, 50851, 50850, 50850, 50851, 50875, 50817, 50923, 50923, 50826, 50851, 50842, 50838, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50861, 50857, 50849, 50862, 50857, 50856, 50857, 50857, 50857, 50831, 50923, 50923, 50826, 50876, 50877, 50849, 50827, 50829, 50854, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50820, 50854, 50849, 50873, 50877, 50877, 51193, 51172, 51195, 51172, 51193, 51182, 51148, 51171, 51138, 51188, 51180, 51169, 51197, 51198, 51172, 51177, 51199, 51176, 51173, 51197, 51172, 51182, 50733, 51169, 51169, 51192, 51171, 50733, 50720, 50733, 51177, 51176, 51177, 51176, 51176, 51182, 51182, 51192, 51166, 51171, 51170, 51172, 51193, 51180, 51182, 51172, 51193, 51171, 51176, 51173, 51193, 51192, 51148, 51171, 51170, 50733, 50720, 50733, 51188, 50832, 50693, 50715, 50713, 50715, 50719, 50695, 50691, 50715, 50708, 50716, 50691, 50715, 50705, 50702, 50802, 50715, 50786, 50755, 50755, 50809, 50707, 50715, 50714, 50714, 50715, 50718, 50807, 50802, 50699, 50689, 50712, 50691, 50715, 50708, 50715, 50691, 50718, 50710, 50713, 50689, 50694, 50713, 50705, 50812, 50786, 50715, 50713, 50715, 50711, 50705, 50704, 50719, 50689, 50689, 50791, 50755, 50755, 50791, 50689, 50689, 50689, 50688, 50689, 50694, 50713, 50938, 50849, 50851, 50879, 50855, 50857, 50848, 50835, 50943, 50923, 50923, 50831, 50857, 50857, 50857, 50856, 50857, 50862, 50849, 50857, 50861, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50842, 50851, 50851};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void i(int r5, short r6, short r7, java.lang.Object[] r8) {
            /*
                byte[] r0 = o.k.a.AnonymousClass4.$$a
                int r7 = r7 + 66
                int r6 = r6 * 4
                int r6 = 1 - r6
                int r5 = r5 * 3
                int r5 = 4 - r5
                byte[] r1 = new byte[r6]
                r2 = 0
                if (r0 != 0) goto L14
                r4 = r5
                r3 = r2
                goto L26
            L14:
                r3 = r2
            L15:
                byte r4 = (byte) r7
                r1[r3] = r4
                int r3 = r3 + 1
                if (r3 != r6) goto L24
                java.lang.String r5 = new java.lang.String
                r5.<init>(r1, r2)
                r8[r2] = r5
                return
            L24:
                r4 = r0[r5]
            L26:
                int r5 = r5 + 1
                int r7 = r7 + r4
                goto L15
            */
            throw new UnsupportedOperationException("Method not decompiled: o.k.a.AnonymousClass4.i(int, short, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{9, -87, 124, -45};
            $$b = 77;
        }

        @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
        public final void onAuthenticationFailed() {
            int i = f + 79;
            h = i % 128;
            int i2 = i % 2;
        }

        AnonymousClass4(Handler handler, o.o.b bVar, HandlerThread handlerThread) {
            this.c = handler;
            this.b = bVar;
            this.d = handlerThread;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void b(o.o.b bVar) {
            int i = f + 49;
            h = i % 128;
            switch (i % 2 == 0 ? '0' : (char) 26) {
                case 26:
                    bVar.e(a.this);
                    return;
                default:
                    bVar.e(a.this);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void a(o.o.b bVar, e eVar) {
            int i = f + 77;
            h = i % 128;
            int i2 = i % 2;
            bVar.c(eVar, a.this);
            int i3 = h + 59;
            f = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return;
                default:
                    int i4 = 24 / 0;
                    return;
            }
        }

        @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
        public final void onAuthenticationError(int i, CharSequence charSequence) {
            g.c();
            Object[] objArr = new Object[1];
            final e eVar = null;
            g(null, new int[]{0, 48, 39, 40}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            g("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{48, 44, 0, 12}, true, objArr2);
            g.e(intern, sb.append(((String) objArr2[0]).intern()).append(i).toString());
            switch (i) {
                case 1:
                case 2:
                case 4:
                case 5:
                case 8:
                case 12:
                    eVar = e.e;
                    break;
                case 3:
                    eVar = e.d;
                    break;
                case 6:
                default:
                    eVar = e.j;
                    break;
                case 7:
                case 9:
                    eVar = e.c;
                    break;
                case 10:
                    eVar = e.a;
                    break;
                case 11:
                case 14:
                    eVar = e.j;
                    break;
                case 13:
                    if (a.this.a() == null) {
                        eVar = e.a;
                        int i2 = f + 109;
                        h = i2 % 128;
                        if (i2 % 2 != 0) {
                            break;
                        } else {
                            break;
                        }
                    } else {
                        int i3 = h + 87;
                        f = i3 % 128;
                        int i4 = i3 % 2;
                        Handler handler = this.c;
                        final o.o.b bVar = this.b;
                        handler.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda4
                            @Override // java.lang.Runnable
                            public final void run() {
                                a.AnonymousClass4.this.b(bVar);
                            }
                        });
                        break;
                    }
            }
            switch (eVar != null) {
                case true:
                    int i5 = h + Opcodes.LSUB;
                    f = i5 % 128;
                    int i6 = i5 % 2;
                    Handler handler2 = this.c;
                    final o.o.b bVar2 = this.b;
                    handler2.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda5
                        @Override // java.lang.Runnable
                        public final void run() {
                            a.AnonymousClass4.this.a(bVar2, eVar);
                        }
                    });
                    break;
            }
            this.d.quitSafely();
            int i7 = h + 25;
            f = i7 % 128;
            int i8 = i7 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void d(o.o.b bVar) {
            int i = h + 29;
            f = i % 128;
            int i2 = i % 2;
            bVar.c(e.j, a.this);
            int i3 = f + 95;
            h = i3 % 128;
            switch (i3 % 2 == 0 ? Typography.amp : 'N') {
                case 'N':
                    return;
                default:
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void a(o.o.b bVar) {
            int i = f + 15;
            h = i % 128;
            int i2 = i % 2;
            bVar.c(e.j, a.this);
            int i3 = f + Opcodes.DNEG;
            h = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return;
                default:
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void b(o.o.b bVar, byte[] bArr) {
            bVar.b(new o.f.a(e.d.c, new Date(), new d(bArr), null), a.this);
            int i = h + Opcodes.LNEG;
            f = i % 128;
            int i2 = i % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void c(o.o.b bVar) {
            int i = h + 85;
            f = i % 128;
            int i2 = i % 2;
            bVar.c(o.o.e.j, a.this);
            int i3 = h + 79;
            f = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 3 : 'R') {
                case Opcodes.DASTORE /* 82 */:
                    return;
                default:
                    throw null;
            }
        }

        @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
        public final void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult authenticationResult) {
            int i = f + Opcodes.DNEG;
            h = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            g(null, new int[]{0, 48, 39, 40}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            try {
                g.c();
                Object[] objArr2 = new Object[1];
                g("\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{92, 45, 0, 0}, false, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                BiometricPrompt.CryptoObject cryptoObject = authenticationResult.getCryptoObject();
                switch (cryptoObject != null) {
                    case false:
                        g.c();
                        Object[] objArr3 = new Object[1];
                        g("\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000", new int[]{Opcodes.L2F, 74, 0, 0}, false, objArr3);
                        g.e(intern, ((String) objArr3[0]).intern());
                        Handler handler = this.c;
                        final o.o.b bVar = this.b;
                        handler.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda0
                            @Override // java.lang.Runnable
                            public final void run() {
                                a.AnonymousClass4.this.d(bVar);
                            }
                        });
                        break;
                    default:
                        Cipher cipher = cryptoObject.getCipher();
                        try {
                            switch (cipher == null ? (char) 24 : '\\') {
                                case Opcodes.DUP2 /* 92 */:
                                    final byte[] d = a.this.d.e().d(cipher);
                                    g.c();
                                    Object[] objArr4 = new Object[1];
                                    g("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{270, 66, 104, 37}, true, objArr4);
                                    g.d(intern, ((String) objArr4[0]).intern());
                                    Handler handler2 = this.c;
                                    final o.o.b bVar2 = this.b;
                                    handler2.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda2
                                        @Override // java.lang.Runnable
                                        public final void run() {
                                            a.AnonymousClass4.this.b(bVar2, d);
                                        }
                                    });
                                    break;
                                default:
                                    g.c();
                                    Object[] objArr5 = new Object[1];
                                    g(null, new int[]{Primes.SMALL_FACTOR_LIMIT, 59, 192, 16}, true, objArr5);
                                    g.e(intern, ((String) objArr5[0]).intern());
                                    Handler handler3 = this.c;
                                    final o.o.b bVar3 = this.b;
                                    handler3.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda1
                                        @Override // java.lang.Runnable
                                        public final void run() {
                                            a.AnonymousClass4.this.a(bVar3);
                                        }
                                    });
                                    this.d.quitSafely();
                                    int i3 = f + 19;
                                    h = i3 % 128;
                                    if (i3 % 2 != 0) {
                                        return;
                                    } else {
                                        throw null;
                                    }
                            }
                        } catch (IllegalArgumentException | BadPaddingException | IllegalBlockSizeException | o.r.b e) {
                            g.c();
                            Object[] objArr6 = new Object[1];
                            g("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{336, 37, 0, 36}, true, objArr6);
                            g.a(intern, ((String) objArr6[0]).intern(), e);
                            Handler handler4 = this.c;
                            final o.o.b bVar4 = this.b;
                            handler4.post(new Runnable() { // from class: o.k.a$4$$ExternalSyntheticLambda3
                                @Override // java.lang.Runnable
                                public final void run() {
                                    a.AnonymousClass4.this.c(bVar4);
                                }
                            });
                            break;
                        }
                }
            } finally {
                this.d.quitSafely();
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
        /* JADX WARN: Type inference failed for: r0v1 */
        /* JADX WARN: Type inference failed for: r0v21, types: [byte[]] */
        private static void g(java.lang.String r20, int[] r21, boolean r22, java.lang.Object[] r23) {
            /*
                Method dump skipped, instructions count: 958
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.k.a.AnonymousClass4.g(java.lang.String, int[], boolean, java.lang.Object[]):void");
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void e(BiometricPrompt biometricPrompt, FragmentActivity fragmentActivity) {
        int i = k + 15;
        m = i % 128;
        int i2 = i % 2;
        biometricPrompt.cancelAuthentication();
        fragmentActivity.finish();
        int i3 = k + 97;
        m = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.o.c
    public final void d(final FragmentActivity fragmentActivity, int i, CancellationSignal cancellationSignal, o.o.b bVar) {
        String b2;
        int i2 = m + 1;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        p("튶뮒෩园鏡팼漞䖌\udd2eᇲ夲웲䀿ϐҞ圿谓Ň\udd2eᇲ簕씈쫖섋ﳇ骧ጮ椥芃\uf58aᓾ㇚䍎\uf8fc碑彅슰\ue001碑彅\ua48d塷ℳ뭲ﳇ骧䬼螂", 48 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p("猯㭳ǖ갰졟℀䰣눠復춂踂嗦ﴦᢸ칼慘惾딼", 17 - (ViewConfiguration.getScrollBarSize() >> 8), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            this.d.e();
            Cipher d = o.r.a.d();
            b bVar2 = new b(Looper.myLooper());
            Object[] objArr3 = new Object[1];
            p("街哔뭶鰇웷籪蜸憦鏑㴿⫇\uf8b3͘\ued7e꒗ῧﳇ骧닝曼㱬䭽諔蹒檶䲯⾥崔Ҟ圿峽\ud820㶼䊨먯嶅⍛\ue7a9\udd2eᇲᆁ\u1978䀿ϐ嬯㵖Ꭻ곊禞쬆\uda7dྠ", 51 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
            final HandlerThread handlerThread = new HandlerThread(((String) objArr3[0]).intern());
            handlerThread.start();
            Executor executor = new Executor() { // from class: o.k.a$$ExternalSyntheticLambda0
                @Override // java.util.concurrent.Executor
                public final void execute(Runnable runnable) {
                    a.b(handlerThread, runnable);
                }
            };
            switch (a() == null) {
                case false:
                    int i4 = m + 55;
                    k = i4 % 128;
                    int i5 = i4 % 2;
                    b2 = a().b();
                    break;
                default:
                    b2 = h();
                    break;
            }
            BiometricPrompt.PromptInfo.Builder negativeButtonText = new BiometricPrompt.PromptInfo.Builder().setTitle(this.i).setSubtitle(this.f).setAllowedAuthenticators(15).setNegativeButtonText(b2);
            final BiometricPrompt biometricPrompt = new BiometricPrompt(fragmentActivity, executor, new AnonymousClass4(bVar2, bVar, handlerThread));
            cancellationSignal.setOnCancelListener(new CancellationSignal.OnCancelListener() { // from class: o.k.a$$ExternalSyntheticLambda1
                @Override // android.os.CancellationSignal.OnCancelListener
                public final void onCancel() {
                    a.e(BiometricPrompt.this, fragmentActivity);
                }
            });
            biometricPrompt.authenticate(negativeButtonText.build(), new BiometricPrompt.CryptoObject(d));
        } catch (o.r.b e2) {
            bVar.c(o.o.e.j, this);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.k.a.p(java.lang.String, int, java.lang.Object[]):void");
    }
}
