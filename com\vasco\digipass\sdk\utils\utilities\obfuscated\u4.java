package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u4.smali */
public class u4 extends w7 {
    private int[] b;

    public u4(int i, int[] iArr) {
        if (i < 0) {
            throw new ArithmeticException("negative length");
        }
        this.a = i;
        int i2 = (i + 31) >> 5;
        if (iArr.length != i2) {
            throw new ArithmeticException("length mismatch");
        }
        int[] a = d5.a(iArr);
        this.b = a;
        int i3 = i & 31;
        if (i3 != 0) {
            int i4 = i2 - 1;
            a[i4] = ((1 << i3) - 1) & a[i4];
        }
    }

    public int[] b() {
        return this.b;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof u4)) {
            return false;
        }
        u4 u4Var = (u4) obj;
        return this.a == u4Var.a && d5.a(this.b, u4Var.b);
    }

    public int hashCode() {
        return (this.a * 31) + Arrays.hashCode(this.b);
    }

    public String toString() {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < this.a; i++) {
            if (i != 0 && (i & 31) == 0) {
                stringBuffer.append(' ');
            }
            if ((this.b[i >> 5] & (1 << (i & 31))) == 0) {
                stringBuffer.append('0');
            } else {
                stringBuffer.append('1');
            }
        }
        return stringBuffer.toString();
    }
}
