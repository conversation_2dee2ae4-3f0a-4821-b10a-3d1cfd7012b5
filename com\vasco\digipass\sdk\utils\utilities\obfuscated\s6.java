package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s6.smali */
public class s6 implements t0 {
    private static final BigInteger d = BigInteger.valueOf(1);
    private t6 a = new t6();
    private u6 b;
    private SecureRandom c;

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public int a() {
        return this.a.b();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public int b() {
        return this.a.a();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public void init(boolean z, CipherParameters cipherParameters) {
        this.a.a(z, cipherParameters);
        if (!(cipherParameters instanceof k6)) {
            u6 u6Var = (u6) cipherParameters;
            this.b = u6Var;
            if (u6Var instanceof v6) {
                this.c = t1.b();
                return;
            } else {
                this.c = null;
                return;
            }
        }
        k6 k6Var = (k6) cipherParameters;
        u6 u6Var2 = (u6) k6Var.a();
        this.b = u6Var2;
        if (u6Var2 instanceof v6) {
            this.c = k6Var.b();
        } else {
            this.c = null;
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.t0
    public byte[] a(byte[] bArr, int i, int i2) {
        if (this.b == null) {
            throw new IllegalStateException("RSA engine not initialised");
        }
        return this.a.a(a(this.a.a(bArr, i, i2)));
    }

    private BigInteger a(BigInteger bigInteger) {
        v6 v6Var;
        BigInteger f;
        u6 u6Var = this.b;
        if ((u6Var instanceof v6) && (f = (v6Var = (v6) u6Var).f()) != null) {
            BigInteger b = v6Var.b();
            BigInteger bigInteger2 = d;
            BigInteger a = f1.a(bigInteger2, b.subtract(bigInteger2), this.c);
            return f1.a(b, a).multiply(this.a.b(a.modPow(f, b).multiply(bigInteger).mod(b))).mod(b);
        }
        return this.a.b(bigInteger);
    }
}
