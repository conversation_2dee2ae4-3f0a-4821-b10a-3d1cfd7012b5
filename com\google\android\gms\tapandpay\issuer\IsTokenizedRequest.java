package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IsTokenizedRequest.smali */
public class IsTokenizedRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<IsTokenizedRequest> CREATOR = new zze();
    final String zza;
    final int zzb;
    final int zzc;
    final String zzd;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IsTokenizedRequest$Builder.smali */
    public static class Builder {
        private String zza;
        private int zzb;
        private int zzc;
        private String zzd;

        public IsTokenizedRequest build() {
            return new IsTokenizedRequest(this.zza, this.zzb, this.zzc, this.zzd);
        }

        public Builder setIdentifier(String str) {
            this.zza = str;
            return this;
        }

        public Builder setIssuerName(String str) {
            this.zzd = str;
            return this;
        }

        public Builder setNetwork(int i) {
            this.zzb = i;
            return this;
        }

        public Builder setTokenServiceProvider(int i) {
            this.zzc = i;
            return this;
        }
    }

    IsTokenizedRequest(String str, int i, int i2, String str2) {
        this.zza = str;
        this.zzb = i;
        this.zzc = i2;
        this.zzd = str2;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, this.zza, false);
        SafeParcelWriter.writeInt(dest, 2, this.zzb);
        SafeParcelWriter.writeInt(dest, 3, this.zzc);
        SafeParcelWriter.writeString(dest, 4, this.zzd, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
