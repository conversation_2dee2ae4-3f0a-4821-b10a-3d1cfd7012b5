package o.ej;

import android.util.Pair;
import com.esotericsoftware.asm.Opcodes;
import java.util.HashMap;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ej\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static c a;
    public static final c b;
    public static final c c;
    public static final c d;
    public static final c e;
    private static c f;
    private static c g;
    private static c h;
    private static c j;
    private static int k;
    private static final HashMap<Pair<String, String>, c> l;
    private static final /* synthetic */ c[] m;
    private static char[] n;

    /* renamed from: o, reason: collision with root package name */
    private static long f64o;
    private static int r;
    private static int t;
    private String i;

    static void c() {
        f64o = -245677456896967959L;
        n = new char[]{50924, 50822, 50845, 50826, 50822, 50847, 50822, 50820, 50822, 50827, 50925, 50823, 50819, 50818, 50941, 50862, 50849, 50878, 50878, 50877, 50835, 50872, 50726, 50751, 50745, 50747, 50743, 50751, 50751, 50717, 50717, 50743, 50744, 50722, 50722, 50720, 50692, 50692, 50751, 50746, 50728, 50901, 50940, 50943, 50943, 50935, 50875, 50868, 50868, 50921, 50827, 50824, 50841, 50794, 50869, 50751, 50748, 50750, 50900, 50936, 50900, 50939, 50876, 50728, 50903, 50938, 50902, 50942, 50900, 50937, 50784, 50784, 50786, 50849, 50709, 50897, 50933};
    }

    static void init$0() {
        $$a = new byte[]{94, -116, 51, -9};
        $$b = Opcodes.L2D;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void s(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ej.c.$$a
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = 3 - r7
            int r6 = r6 + 66
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1e:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L37:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.c.s(short, short, short, java.lang.Object[]):void");
    }

    private static /* synthetic */ c[] d() {
        int i = r;
        int i2 = i + Opcodes.LREM;
        t = i2 % 128;
        int i3 = i2 % 2;
        c[] cVarArr = {a, h, g, d, c, b, e, j, f};
        int i4 = i + 29;
        t = i4 % 128;
        int i5 = i4 % 2;
        return cVarArr;
    }

    public static c valueOf(String str) {
        int i = t + 109;
        r = i % 128;
        char c2 = i % 2 != 0 ? '.' : '6';
        c cVar = (c) Enum.valueOf(c.class, str);
        switch (c2) {
            default:
                int i2 = 83 / 0;
            case Opcodes.ISTORE /* 54 */:
                return cVar;
        }
    }

    public static c[] values() {
        int i = r + 95;
        t = i % 128;
        switch (i % 2 == 0 ? 'D' : 'P') {
            case 'P':
                c[] cVarArr = (c[]) m.clone();
                int i2 = r + 31;
                t = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 5 : 'J') {
                    case 'J':
                        return cVarArr;
                    default:
                        throw null;
                }
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    static {
        /*
            Method dump skipped, instructions count: 1546
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.c.<clinit>():void");
    }

    private c(String str, int i, String str2) {
        this.i = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i = t;
        int i2 = i + Opcodes.LSHR;
        r = i2 % 128;
        int i3 = i2 % 2;
        String str = this.i;
        int i4 = i + 17;
        r = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    private static boolean a(String str, String str2, String str3) {
        int min;
        int i = r + 69;
        t = i % 128;
        switch (i % 2 == 0 ? 'P' : '1') {
            case '1':
                int min2 = Math.min(str.length(), str2.length());
                min = Math.min(str.length(), str3.length());
                switch (Integer.parseInt(str.substring(0, min2)) < Integer.parseInt(str2.substring(0, min2)) ? '2' : (char) 25) {
                }
                return false;
            default:
                int min3 = Math.min(str.length(), str2.length());
                min = Math.min(str.length(), str3.length());
                switch (Integer.parseInt(str.substring(1, min3)) >= Integer.parseInt(str2.substring(0, min3))) {
                }
                return false;
        }
        if (Integer.parseInt(str.substring(0, min)) <= Integer.parseInt(str3.substring(0, min))) {
            return true;
        }
        int i2 = t + 9;
        r = i2 % 128;
        int i3 = i2 % 2;
        return false;
    }

    private static HashMap<Pair<String, String>, c> e() {
        int i = t + 61;
        int i2 = i % 128;
        r = i2;
        int i3 = i % 2;
        HashMap<Pair<String, String>, c> hashMap = l;
        int i4 = i2 + 57;
        t = i4 % 128;
        int i5 = i4 % 2;
        return hashMap;
    }

    private static Pair<String, String> a(String str, String str2) {
        switch (str2 == null ? 'L' : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                break;
            default:
                int i = t + 29;
                r = i % 128;
                switch (i % 2 == 0) {
                    case true:
                        str2 = str;
                        break;
                    default:
                        throw null;
                }
        }
        Pair<String, String> pair = new Pair<>(str, str2);
        int i2 = r + 19;
        t = i2 % 128;
        int i3 = i2 % 2;
        return pair;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.ej.c e(java.lang.String r7) {
        /*
            boolean r0 = o.ee.o.a(r7)
            r1 = 0
            r2 = 1
            if (r0 == 0) goto Lb
            r0 = r1
            goto Lc
        Lb:
            r0 = r2
        Lc:
            switch(r0) {
                case 0: goto L21;
                default: goto Lf;
            }
        Lf:
            java.util.HashSet r0 = new java.util.HashSet
            r0.<init>()
            java.util.HashMap r3 = e()
            java.util.Set r3 = r3.entrySet()
            java.util.Iterator r3 = r3.iterator()
            goto L2e
        L21:
            int r7 = o.ej.c.t
            int r7 = r7 + 89
            int r0 = r7 % 128
            o.ej.c.r = r0
            int r7 = r7 % 2
            o.ej.c r7 = o.ej.c.j
            return r7
        L2e:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L37
            r4 = 18
            goto L39
        L37:
            r4 = 80
        L39:
            switch(r4) {
                case 18: goto L43;
                default: goto L3c;
            }
        L3c:
            int r7 = r0.size()
            if (r7 <= r2) goto L7b
            goto L78
        L43:
            int r4 = o.ej.c.r
            int r4 = r4 + 61
            int r5 = r4 % 128
            o.ej.c.t = r5
            int r4 = r4 % 2
            java.lang.Object r4 = r3.next()
            java.util.Map$Entry r4 = (java.util.Map.Entry) r4
            java.lang.Object r5 = r4.getKey()
            android.util.Pair r5 = (android.util.Pair) r5
            java.lang.Object r5 = r5.first
            java.lang.String r5 = (java.lang.String) r5
            java.lang.Object r6 = r4.getKey()
            android.util.Pair r6 = (android.util.Pair) r6
            java.lang.Object r6 = r6.second
            java.lang.String r6 = (java.lang.String) r6
            boolean r5 = a(r7, r5, r6)
            if (r5 == 0) goto L77
            java.lang.Object r4 = r4.getValue()
            o.ej.c r4 = (o.ej.c) r4
            r0.add(r4)
        L77:
            goto L2e
        L78:
            o.ej.c r7 = o.ej.c.f
            return r7
        L7b:
            int r7 = r0.size()
            if (r7 != r2) goto L83
            r1 = r2
            goto L84
        L83:
        L84:
            switch(r1) {
                case 0: goto L92;
                default: goto L87;
            }
        L87:
            java.util.Iterator r7 = r0.iterator()
            java.lang.Object r7 = r7.next()
            o.ej.c r7 = (o.ej.c) r7
            return r7
        L92:
            o.ej.c r7 = o.ej.c.j
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.c.e(java.lang.String):o.ej.c");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.c.p(java.lang.String, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:119:0x0383, code lost:
    
        r2 = r0;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v34, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(java.lang.String r22, int[] r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1000
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.c.q(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
