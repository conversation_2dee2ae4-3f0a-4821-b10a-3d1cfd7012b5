package o.fb;

import android.graphics.Color;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.eg.d;
import o.ey.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fb\b.smali */
public final class b extends a<o.fh.b, c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int g;
    private static int h;
    private static boolean i;
    private static boolean j;
    private final String c;
    private final String d;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        a();
        ViewConfiguration.getMaximumDrawingCacheSize();
        int i2 = h + 73;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? '.' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void a() {
        a = new char[]{61461, 61453, 61455, 61445, 61467, 61462, 61451, 61458, 61460, 61680, 61447, 61471, 61459, 61669, 61457, 61464, 61671};
        j = true;
        i = true;
        b = 782102688;
    }

    static void init$0() {
        $$d = new byte[]{17, -116, 103, 33};
        $$e = Opcodes.ISHR;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.fb.b.$$d
            int r7 = r7 * 4
            int r7 = r7 + 1
            int r8 = r8 * 2
            int r8 = r8 + 4
            int r9 = r9 + 117
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r9 = r9 + 1
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.b.m(short, byte, byte, java.lang.Object[]):void");
    }

    public b() {
        Object[] objArr = new Object[1];
        k(null, View.resolveSizeAndState(0, 0, 0) + 127, null, "\u0083\u0082\u0081", objArr);
        this.d = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, 126 - ((byte) KeyEvent.getModifierMetaStateMask()), null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr2);
        this.c = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        k(null, 127 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0090\u008b\u008d", objArr3);
        this.e = ((String) objArr3[0]).intern();
    }

    @Override // o.ey.a
    public final /* synthetic */ c b(o.eg.b bVar) throws d {
        int i2 = g + 73;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return c(bVar);
            default:
                c(bVar);
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    @Override // o.ey.a
    public final /* synthetic */ o.fh.b b(o.fc.c cVar, short s) {
        int i2 = g + 11;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
        }
        o.fh.b c = c(false, cVar, s);
        int i3 = g + 15;
        h = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    @Override // o.ey.a
    public final /* synthetic */ o.eg.b e(c cVar) throws d {
        int i2 = h + 23;
        g = i2 % 128;
        int i3 = i2 % 2;
        o.eg.b b2 = b(cVar);
        int i4 = g + 89;
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return b2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ o.eg.b e(o.fh.b bVar) throws d {
        int i2 = h + 3;
        g = i2 % 128;
        int i3 = i2 % 2;
        o.eg.b d = d(bVar);
        int i4 = h + 25;
        g = i4 % 128;
        switch (i4 % 2 != 0 ? 'K' : (char) 20) {
            case 'K':
                int i5 = 75 / 0;
                return d;
            default:
                return d;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ c e(String str, String str2, boolean z) {
        int i2 = h + 73;
        g = i2 % 128;
        char c = i2 % 2 != 0 ? '\t' : Typography.quote;
        c b2 = b(str, str2, z);
        switch (c) {
            case '\"':
                break;
            default:
                int i3 = 9 / 0;
                break;
        }
        int i4 = h + Opcodes.DMUL;
        g = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                throw null;
            default:
                return b2;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ o.fh.b e(o.eg.b bVar) throws d {
        int i2 = g + 87;
        h = i2 % 128;
        boolean z = i2 % 2 == 0;
        o.fh.b d = d(bVar);
        switch (z) {
            default:
                int i3 = 34 / 0;
            case false:
                return d;
        }
    }

    @Override // o.ey.a
    public final a.d d() {
        int i2 = g + 63;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '0' : Typography.dollar) {
            case '0':
                a.d dVar = a.d.i;
                throw null;
            default:
                return a.d.i;
        }
    }

    private static c b(String str, String str2, boolean z) {
        c cVar = new c(str, str2, z);
        int i2 = h + 11;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? ',' : '4') {
            case ',':
                throw null;
            default:
                return cVar;
        }
    }

    private static o.fh.b c(boolean z, o.fc.c cVar, short s) {
        o.fh.b bVar = new o.fh.b(false, cVar, s);
        int i2 = g + 53;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return bVar;
            default:
                int i3 = 39 / 0;
                return bVar;
        }
    }

    private o.eg.b b(c cVar) throws d {
        int i2 = h + 41;
        g = i2 % 128;
        int i3 = i2 % 2;
        o.eg.b e = super.e((b) cVar);
        Object[] objArr = new Object[1];
        k(null, Color.alpha(0) + 127, null, "\u0084\u0089\u0091\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr);
        e.d(((String) objArr[0]).intern(), cVar.p());
        int i4 = h + 51;
        g = i4 % 128;
        int i5 = i4 % 2;
        return e;
    }

    private c c(o.eg.b bVar) throws d {
        c cVar;
        Object obj;
        int i2 = h + Opcodes.DDIV;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                cVar = (c) super.b(bVar);
                Object[] objArr = new Object[1];
                k(null, 127 - View.MeasureSpec.getMode(0), null, "\u0084\u0089\u0091\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr);
                obj = objArr[0];
                break;
            default:
                cVar = (c) super.b(bVar);
                Object[] objArr2 = new Object[1];
                k(null, Opcodes.FNEG / View.MeasureSpec.getMode(1), null, "\u0084\u0089\u0091\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr2);
                obj = objArr2[0];
                break;
        }
        cVar.e(bVar.i(((String) obj).intern()).intValue());
        int i3 = g + Opcodes.DSUB;
        h = i3 % 128;
        int i4 = i3 % 2;
        return cVar;
    }

    private o.fh.b d(o.eg.b bVar) throws d {
        int i2 = g + 71;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.fh.b bVar2 = (o.fh.b) super.e(bVar);
        Object[] objArr = new Object[1];
        k(null, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 127, null, "\u0083\u0082\u0081", objArr);
        bVar2.c(bVar.i(((String) objArr[0]).intern()).intValue());
        Object[] objArr2 = new Object[1];
        k(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr2);
        bVar2.b(bVar.i(((String) objArr2[0]).intern()).intValue());
        Object[] objArr3 = new Object[1];
        k(null, 127 - View.MeasureSpec.getSize(0), null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0090\u008b\u008d", objArr3);
        bVar2.a(bVar.i(((String) objArr3[0]).intern()).intValue());
        int i4 = h + 91;
        g = i4 % 128;
        int i5 = i4 % 2;
        return bVar2;
    }

    private o.eg.b d(o.fh.b bVar) throws d {
        int i2 = h + 27;
        g = i2 % 128;
        int i3 = i2 % 2;
        o.eg.b e = super.e((b) bVar);
        Object[] objArr = new Object[1];
        k(null, (ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u0083\u0082\u0081", objArr);
        e.d(((String) objArr[0]).intern(), bVar.h());
        Object[] objArr2 = new Object[1];
        k(null, Color.blue(0) + 127, null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0086\u0085\u0084", objArr2);
        e.d(((String) objArr2[0]).intern(), bVar.j());
        Object[] objArr3 = new Object[1];
        k(null, 127 - TextUtils.getTrimmedLength(""), null, "\u0089\u0088\u0085\u008f\u008e\u0089\u0088\u0087\u008d\u008c\u008b\u008a\u0090\u008b\u008d", objArr3);
        e.d(((String) objArr3[0]).intern(), bVar.i());
        int i4 = h + 65;
        g = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return e;
            default:
                int i5 = 42 / 0;
                return e;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r21, int r22, int[] r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1074
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.b.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
