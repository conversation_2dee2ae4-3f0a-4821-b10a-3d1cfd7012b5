package o.bk;

import o.ef.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bk\a.smali */
final class a {
    a() {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bk\a$c.smali */
    static final class c {
        private static int a = 0;
        static final b[] b = {b.e, b.c};
        static final b[] c = {b.b, b.a};
        private static int e;

        static {
            e = 1;
            int i = a;
            int i2 = (i ^ 85) + ((i & 85) << 1);
            e = i2 % 128;
            int i3 = i2 % 2;
        }
    }
}
