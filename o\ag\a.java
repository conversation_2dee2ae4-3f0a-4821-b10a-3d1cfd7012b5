package o.ag;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.ei.c;
import o.er.t;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ag\a.smali */
public final class a extends o.y.b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int d;
    private static int e;
    List<t> b;
    String c;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ag\a$d.smali */
    public interface d {
        void c(List<t> list);

        void c(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        n();
        View.combineMeasuredStates(0, 0);
        ExpandableListView.getPackedPositionForGroup(0);
        int i = e + 29;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$d = new byte[]{91, -22, 50, -29};
        $$e = Opcodes.ARRAYLENGTH;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.ag.a.$$d
            int r7 = r7 * 2
            int r7 = r7 + 112
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L38
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1e:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r7 = r7 + 1
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ag.a.l(short, short, short, java.lang.Object[]):void");
    }

    static void n() {
        a = -7672690488959593099L;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = e + 89;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                m();
                throw null;
            default:
                b m = m();
                int i2 = e + 33;
                d = i2 % 128;
                switch (i2 % 2 == 0) {
                    case true:
                        int i3 = 93 / 0;
                        return m;
                    default:
                        return m;
                }
        }
    }

    public a(Context context, d dVar, c cVar) {
        super(context, dVar, cVar, e.s);
    }

    public final void c(String str) {
        g.c();
        Object[] objArr = new Object[1];
        k("厢齑쨳㗂惎厛齦쨼㔿惙厾齫쩌㔋惿厵麇쩷㔑怉參麐쩦㕑怕叏麰즃㕔怩叵黎", 52433 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("厁䬭括᩵ㄍ⣲쁠？隸蹔ꔱ岭瑀Ꮻઢ≟\ud9e1\uf09d\ue829蟳뺯嘹䷍撴\u1c38㯙퍻쩘\ue18c餮뀔꾽䝷縆ᗫൄ⓶쏦רּ鋱覉ꅺ壷瞇漺ۛ㶉픿첕\ue428荛", 6311 - ExpandableListView.getPackedPositionGroup(0L), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        this.c = str;
        c();
        int i = e + 77;
        d = i % 128;
        switch (i % 2 == 0 ? 'L' : 'R') {
            case Base64.mimeLineLength /* 76 */:
                throw null;
            default:
                return;
        }
    }

    private b m() {
        b bVar = new b(this);
        int i = d + 37;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                int i2 = 3 / 0;
                return bVar;
            default:
                return bVar;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = e + 39;
        d = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                k("厢齑쨳㗂惎厛齦쨼㔿惙厾齫쩌㔋惿厵麇쩷㔑怉參麐쩦㕑怕叏麰즃㕔怩叵黎", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 52432, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("厢齑쨳㗂惎厛齦쨼㔿惙厾齫쩌㔋惿厵麇쩷㔑怉參麐쩦㕑怕叏麰즃㕔怩叵黎", 52432 >> (AudioTrack.getMaxVolume() > 1.0f ? 1 : (AudioTrack.getMaxVolume() == 1.0f ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = e + Opcodes.LSHL;
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ag\a$b.smali */
    static final class b extends o.y.c<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static long a;
        private static long b;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            e = 1;
            a = 7198970339599249670L;
            b = 5557715188470742400L;
        }

        private static void C(int i, short s, byte b2, Object[] objArr) {
            byte[] bArr = $$d;
            int i2 = 1 - (b2 * 4);
            int i3 = 4 - (i * 4);
            int i4 = 114 - s;
            byte[] bArr2 = new byte[i2];
            int i5 = -1;
            int i6 = i2 - 1;
            if (bArr == null) {
                i3++;
                i4 += i6;
                i6 = i6;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = -1;
            }
            while (true) {
                int i7 = i5 + 1;
                bArr2[i7] = (byte) i4;
                if (i7 == i6) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b3 = bArr[i3];
                i3++;
                i4 += b3;
                i6 = i6;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = i7;
            }
        }

        static void init$0() {
            $$d = new byte[]{29, -23, 98, 29};
            $$e = Opcodes.DCMPG;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 85;
            e = i % 128;
            switch (i % 2 == 0) {
                case false:
                    break;
                default:
                    int i2 = 67 / 0;
                    break;
            }
        }

        b(a aVar) {
            super(aVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = d + 63;
            e = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("㟱㭀⺄ᇛԵ\u0882篁漝剜䖸䤙뱒꾗鋲蘨覄ﳔ\ue006퍆우쨘㵩₁Ᏸܶ", 3252 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = e + 99;
            d = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            B("\ueece楮펷\u2ffd\ueeff吉ꤾ韙ᮂ宁ꎽ艔Џ䄍똶賚\u0e8b璅袳띟㬋稑茬", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
            o.cf.d dVar = new o.cf.d(context, 35, ((String) objArr[0]).intern());
            int i = d + 17;
            e = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            B("㶸鹚\udebe⥌㷛ꍤꑲ鄵좍곥", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 1, objArr);
            bVar.d(((String) objArr[0]).intern(), ((a) e()).c);
            int i = e + 91;
            d = i % 128;
            switch (i % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return bVar;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = e + 19;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            int i4 = i2 + 23;
            e = i4 % 128;
            char c = i4 % 2 == 0 ? 'b' : 'X';
            Object obj = null;
            switch (c) {
                case Opcodes.FADD /* 98 */:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = e;
            int i2 = i + 65;
            d = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    int i3 = 93 / 0;
                    break;
            }
            int i4 = i + 59;
            d = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = d + 65;
            e = i2 % 128;
            switch (i2 % 2 == 0 ? '\\' : '\b') {
                case '\b':
                    switch (i) {
                        case 5001:
                            return o.bb.a.ay;
                        case 5002:
                            o.bb.a aVar = o.bb.a.az;
                            int i3 = e + Opcodes.LUSHR;
                            d = i3 % 128;
                            switch (i3 % 2 != 0 ? ')' : 'V') {
                                case Opcodes.SASTORE /* 86 */:
                                    return aVar;
                                default:
                                    int i4 = 93 / 0;
                                    return aVar;
                            }
                        default:
                            o.bb.a c = super.c(i);
                            int i5 = d + 37;
                            e = i5 % 128;
                            switch (i5 % 2 == 0 ? 'O' : 'L') {
                                case Base64.mimeLineLength /* 76 */:
                                    return c;
                                default:
                                    throw null;
                            }
                    }
                default:
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            Object[] objArr = new Object[1];
            B("㋆\ue7c4᳷䐱㊲\udaf4昢ﱉ쟔핍沨\ue9d9\ud84b쿶礲\ue750틝贈䞶", Drawable.resolveOpacity(0, 0) + 1, objArr);
            o.eg.e p = bVar.p(((String) objArr[0]).intern());
            ArrayList arrayList = new ArrayList();
            switch (p != null ? '\\' : (char) 0) {
                case Opcodes.DUP2 /* 92 */:
                    int i = d + 61;
                    e = i % 128;
                    Object obj = null;
                    switch (i % 2 == 0 ? '`' : '!') {
                        case '!':
                            switch (p.d() > 0 ? 'U' : ']') {
                                case Opcodes.DUP2_X1 /* 93 */:
                                    break;
                                default:
                                    for (int i2 = 0; i2 < p.d(); i2++) {
                                        o.eg.b b2 = p.b(i2);
                                        Object[] objArr2 = new Object[1];
                                        w("㟿묷", 36036 - TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
                                        String r = b2.r(((String) objArr2[0]).intern());
                                        Object[] objArr3 = new Object[1];
                                        w("㟢鳠懏㚸鮜悿㕹驉漻㐄", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 43800, objArr3);
                                        t.d[] dVarArr = (t.d[]) b2.c(t.d.class, ((String) objArr3[0]).intern());
                                        Object[] objArr4 = new Object[1];
                                        w("㟸脖娹ፐ", 46817 - View.resolveSizeAndState(0, 0, 0), objArr4);
                                        String q = b2.q(((String) objArr4[0]).intern());
                                        Object[] objArr5 = new Object[1];
                                        B("\ue29b⌶ဈ㢚\ue2f7Ḇ櫑胨ឲᆟ恞", 1 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr5);
                                        String q2 = b2.q(((String) objArr5[0]).intern());
                                        arrayList.add(new t(r, dVarArr, q, q2 != null ? new o.du.i(q2) : null));
                                    }
                                    break;
                            }
                        default:
                            p.d();
                            obj.hashCode();
                            throw null;
                    }
            }
            ((a) e()).b = arrayList;
            int i3 = e + 13;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = d + 99;
            e = i % 128;
            Object obj = null;
            if (i % 2 == 0) {
                ((a) e()).b.iterator();
                throw null;
            }
            Iterator<t> it = ((a) e()).b.iterator();
            while (it.hasNext()) {
                int i2 = e + 91;
                d = i2 % 128;
                if (i2 % 2 != 0) {
                    it.next().d();
                    obj.hashCode();
                    throw null;
                }
                t next = it.next();
                switch (next.d() != null) {
                    case false:
                        break;
                    default:
                        int i3 = e + Opcodes.DREM;
                        d = i3 % 128;
                        if (i3 % 2 != 0) {
                            o.du.a.a().e(g(), next.d());
                            obj.hashCode();
                            throw null;
                            break;
                        } else {
                            try {
                                o.du.a.a().e(g(), next.d());
                                break;
                            } catch (o.du.e e2) {
                                g.c();
                                Object[] objArr = new Object[1];
                                w("㟑驲泠㽁臽呸⛵西富\u2e7a\uf0ed䍨ᗿ\uf868䫬ᵶ\ueff4뉴ӂ흪맰\u0c53\udef5ꅲ珦왌꣣筠췧遪拦㕭", ((Process.getThreadPriority(0) + 20) >> 6) + 44417, objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                B("柳姕ꨯƪ簾擤탂맂ೖ歭\uda70걀ፂ燒쿶ꋌᧉ䑖\uf16f饔ⱚ䫿\ufaf2返㊅儳\uec25艶㥏\ua7f3釫磃俈ꨶ魹潄刉낉賐戴壽蜋똕墇潾趉믜伱疽逴굘䖪硬\ue69f囒㠳軪\ued0c堅⺏镮\uf395䷆", 1 - TextUtils.getTrimmedLength(""), objArr2);
                                g.a(intern, ((String) objArr2[0]).intern(), e2);
                                break;
                            }
                        }
                        g.c();
                        Object[] objArr3 = new Object[1];
                        w("㟑驲泠㽁臽呸⛵西富\u2e7a\uf0ed䍨ᗿ\uf868䫬ᵶ\ueff4뉴ӂ흪맰\u0c53\udef5ꅲ珦왌꣣筠췧遪拦㕭", ((Process.getThreadPriority(0) + 20) >> 6) + 44417, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr22 = new Object[1];
                        B("柳姕ꨯƪ簾擤탂맂ೖ歭\uda70걀ፂ燒쿶ꋌᧉ䑖\uf16f饔ⱚ䫿\ufaf2返㊅儳\uec25艶㥏\ua7f3釫磃俈ꨶ魹潄刉낉賐戴壽蜋똕墇潾趉믜伱疽逴굘䖪硬\ue69f囒㠳軪\ued0c堅⺏镮\uf395䷆", 1 - TextUtils.getTrimmedLength(""), objArr22);
                        g.a(intern2, ((String) objArr22[0]).intern(), e2);
                }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:10:0x0040  */
        /* JADX WARN: Removed duplicated region for block: B:13:0x0054  */
        /* JADX WARN: Removed duplicated region for block: B:25:0x007f  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void t() {
            /*
                r5 = this;
                int r0 = o.ag.a.b.e
                int r0 = r0 + 93
                int r1 = r0 % 128
                o.ag.a.b.d = r1
                int r0 = r0 % 2
                r1 = 1
                r2 = 0
                if (r0 == 0) goto L10
                r0 = r2
                goto L11
            L10:
                r0 = r1
            L11:
                switch(r0) {
                    case 1: goto L25;
                    default: goto L14;
                }
            L14:
                int[] r0 = o.ag.a.AnonymousClass3.e
                o.bb.d r3 = r5.h()
                o.bb.a r3 = r3.d()
                int r3 = r3.ordinal()
                r0 = r0[r3]
                goto L39
            L25:
                int[] r0 = o.ag.a.AnonymousClass3.e
                o.bb.d r3 = r5.h()
                o.bb.a r3 = r3.d()
                int r3 = r3.ordinal()
                r0 = r0[r3]
                switch(r0) {
                    case 1: goto L54;
                    case 2: goto L40;
                    default: goto L38;
                }
            L38:
                goto L7f
            L39:
                r3 = 14
                int r3 = r3 / r2
                switch(r0) {
                    case 1: goto L54;
                    case 2: goto L40;
                    default: goto L3f;
                }
            L3f:
                goto L38
            L40:
                o.ei.c r0 = r5.f()
                android.content.Context r1 = r5.g()
                o.y.b r2 = r5.e()
                o.ag.a r2 = (o.ag.a) r2
                java.lang.String r2 = r2.c
                r0.e(r1, r2)
                return
            L54:
                o.ei.c r0 = r5.f()
                android.content.Context r3 = r5.g()
                o.y.b r4 = r5.e()
                o.ag.a r4 = (o.ag.a) r4
                java.lang.String r4 = r4.c
                r0.c(r3, r4)
                int r0 = o.ag.a.b.d
                int r0 = r0 + 83
                int r3 = r0 % 128
                o.ag.a.b.e = r3
                int r0 = r0 % 2
                if (r0 != 0) goto L74
                goto L75
            L74:
                r1 = r2
            L75:
                switch(r1) {
                    case 1: goto L79;
                    default: goto L78;
                }
            L78:
                return
            L79:
                r0 = 88
                int r0 = r0 / r2
                return
            L7d:
                r0 = move-exception
                throw r0
            L7f:
                super.t()
                return
            L83:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ag.a.b.t():void");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 53;
            e = i % 128;
            switch (i % 2 == 0 ? 'G' : (char) 30) {
                case 'G':
                    ((a) e()).j().c(((a) e()).b);
                    throw null;
                default:
                    ((a) e()).j().c(((a) e()).b);
                    int i2 = e + 49;
                    d = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = e + 67;
            d = i % 128;
            switch (i % 2 != 0) {
                case true:
                    ((a) e()).j().c(dVar);
                    int i2 = 99 / 0;
                    break;
                default:
                    ((a) e()).j().c(dVar);
                    break;
            }
            int i3 = d + 9;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r18, int r19, java.lang.Object[] r20) {
            /*
                Method dump skipped, instructions count: 744
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ag.a.b.w(java.lang.String, int, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void B(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 360
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ag.a.b.B(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.ag.a$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ag\a$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int c = 1;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            d = 0;
            int[] iArr = new int[o.bb.a.values().length];
            e = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = (c + 68) - 1;
                d = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.bb.a.az.ordinal()] = 2;
                int i2 = c;
                int i3 = (i2 & 93) + (i2 | 93);
                d = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 514
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ag.a.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
