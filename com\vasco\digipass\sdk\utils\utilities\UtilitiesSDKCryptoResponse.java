package com.vasco.digipass.sdk.utils.utilities;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\UtilitiesSDKCryptoResponse.smali */
public class UtilitiesSDKCryptoResponse {
    private final int a;
    private final byte[] b;

    public UtilitiesSDKCryptoResponse(int i) {
        this(i, null);
    }

    public byte[] getOutputData() {
        return this.b;
    }

    public int getReturnCode() {
        return this.a;
    }

    public UtilitiesSDKCryptoResponse(int i, byte[] bArr) {
        this.a = i;
        this.b = bArr;
    }
}
