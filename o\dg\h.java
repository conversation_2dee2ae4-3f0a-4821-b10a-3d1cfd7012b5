package o.dg;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import o.de.f;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\h.smali */
final class h extends c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static short[] a;
    private static int b;
    private static int c;
    private static byte[] d;
    private static int e;
    private static int f;
    private static char g;
    private static int h;
    private static int i;
    private static long j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        c();
        KeyEvent.normalizeMetaState(0);
        TextUtils.indexOf("", "");
        TextUtils.indexOf((CharSequence) "", '0');
        ViewConfiguration.getTouchSlop();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        int i2 = h + 39;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        d = new byte[]{-107, 105, -103, 97, -117, 119, -107, -103, 110, 65, UtilitiesSDKConstants.SRP_LABEL_MAC, -101, 98, -111, 119, -102, -98, 103, -103, -111, 97, 69, -66, 107, -99, 100, 111, 110, 103, -118, -113, 125, -127, 118, -117, 108, -108, 122, -116, 117, 126, -97, -97, 40, 39, -40, -41, -104, 98, 40, -45, 42, -34, 32, -46, -99, 98, 33, 47, -51, 38, -40, 42, 34, -34, -52, -97, 111, -33, -112, 45, -45, 102, -37, 49, -51, 47, 35, -44, -37, -98, -50, -51, 50, -52, -60, 52, Tnaf.POW_2_WIDTH, 31, -99, 62, -61, 63, 34, 3, -100, 54, 58, -55, -60, 55, 56, 48, -50, -62, 30, 18, -62, 60, -119, 52, -34, 34, -64, -52, 59, 52};
        b = 909053575;
        e = 1261708070;
        c = 1079272795;
        g = (char) 17957;
        f = 161105445;
        j = 6782483822559823799L;
    }

    static void init$0() {
        $$a = new byte[]{3, 85, -79, -50};
        $$b = 16;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = 110 - r7
            byte[] r0 = o.dg.h.$$a
            int r8 = r8 + 4
            int r9 = r9 * 3
            int r9 = 1 - r9
            byte[] r1 = new byte[r9]
            int r9 = r9 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r9) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            int r8 = r8 + 1
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dg.h.m(short, int, byte, java.lang.Object[]):void");
    }

    h() {
    }

    @Override // o.dg.c
    public final a e(Context context, o.eg.b bVar, Long l) {
        a aVar;
        boolean z;
        Object[] objArr = new Object[1];
        k((byte) (TextUtils.indexOf("", "", 0) - 12), (-1987800012) - TextUtils.indexOf((CharSequence) "", '0', 0), (short) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), TextUtils.indexOf("", "") - 24, (-2098933087) - View.MeasureSpec.getSize(0), objArr);
        String intern = ((String) objArr[0]).intern();
        if (bVar == null) {
            g.c();
            Object[] objArr2 = new Object[1];
            l(Gravity.getAbsoluteGravity(0, 0) + 298452112, "劙溿奿挂失ۇᢰ̸釋ஆ筀ᳶڿ䏜\ue8acបﻂ♞蚃ᇑ悳赻烰뗔寍唕\uf132䒇괞ᾤ뫮쮒酒騵곾柤葄콑喺", (char) (37753 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), "遹쨄礑纓", "䆒\uf319ꍟԾ", objArr2);
            g.e(intern, ((String) objArr2[0]).intern());
            return new a(false);
        }
        Object[] objArr3 = new Object[1];
        l(View.MeasureSpec.getMode(0), "ᤶ䆴\uf762\ueafc듾齢ⵣ뜥誈뤥\uf4af℄ⁱᑛ", (char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 58635), "㡆꽗\u0b79쿥", "䆒\uf319ꍟԾ", objArr3);
        if (bVar.b(((String) objArr3[0]).intern())) {
            g.c();
            Object[] objArr4 = new Object[1];
            l(1082302710 - TextUtils.lastIndexOf("", '0'), "깜睉培ꡋ㹑瀘ⴂ᳡讲\uef19\ud998曺⢢륪ĭ돤䃊\ue823\uf16c⋡滋鲎樨砿ꂈ忀ﱭ멺Ґ砷➰㏝蹖髣젯", (char) (ImageFormat.getBitsPerPixel(0) + 1), "\uf75d芠♀\uf1d4", "䆒\uf319ꍟԾ", objArr4);
            g.d(intern, ((String) objArr4[0]).intern());
            aVar = new a(true, false, null, new o.de.d(o.av.a.d, f.w));
            z = true;
        } else {
            Object[] objArr5 = new Object[1];
            k((byte) ((-27) - View.resolveSize(0, 0)), (-1987799984) - Process.getGidForName(""), (short) Color.red(0), (-24) - (ViewConfiguration.getLongPressTimeout() >> 16), (ViewConfiguration.getScrollBarSize() >> 8) - 2098933055, objArr5);
            if (bVar.b(((String) objArr5[0]).intern())) {
                int i2 = h + Opcodes.DREM;
                i = i2 % 128;
                int i3 = i2 % 2;
                g.c();
                Object[] objArr6 = new Object[1];
                l(525914137 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "얛렟\ud9f8僋䱗ᣌ\ueaa1\ue94c譒꜊첃嫕秽\ud9ca癯⹊\udeb1鳕焜趰꧍᳗걟\uf3a3⏿㽅\uf004땔ᾧڿ꽢堉烪", (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), "ᦄ壐뼟䱺", "䆒\uf319ꍟԾ", objArr6);
                g.d(intern, ((String) objArr6[0]).intern());
                Object[] objArr7 = new Object[1];
                l((-1) - Process.getGidForName(""), "㿗爖堋䴃鎰竦嶈ᩞ堢⭀鸈츪", (char) Gravity.getAbsoluteGravity(0, 0), "料\ue92e몮锼", "䆒\uf319ꍟԾ", objArr7);
                boolean booleanValue = bVar.b(((String) objArr7[0]).intern(), Boolean.FALSE).booleanValue();
                if (booleanValue) {
                    g.c();
                    Object[] objArr8 = new Object[1];
                    l(742631635 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "율䟯ﻒ䰢꩟謵ᮗ豾ཛ뭢\ue9a6⫁╝\ue698訝鷇雵Ѕ臘ຈ㌃蓁萡\ue211䗙롌\uf107䧷큜袪Ⲯ潁퐿\ue6e1黃詏෴捇냩碹\uee8f", (char) (20530 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), "튀䎨ㄬ㙐", "䆒\uf319ꍟԾ", objArr8);
                    g.d(intern, ((String) objArr8[0]).intern());
                    int i4 = h + 31;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                }
                boolean d2 = o.b.c.d(context, booleanValue);
                a aVar2 = new a(true);
                if (d2) {
                    int i6 = h + 21;
                    i = i6 % 128;
                    if (i6 % 2 != 0) {
                        aVar2.a().b();
                        throw null;
                    }
                    aVar2.a().b();
                }
                z = false;
                aVar = aVar2;
            } else {
                g.c();
                Object[] objArr9 = new Object[1];
                k((byte) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 77), (-1987799970) - ImageFormat.getBitsPerPixel(0), (short) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), View.resolveSize(0, 0) - 24, (-2098933075) - ExpandableListView.getPackedPositionChild(0L), objArr9);
                g.e(intern, ((String) objArr9[0]).intern());
                aVar = new a(false);
                z = false;
            }
        }
        Object[] objArr10 = new Object[1];
        l((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 2020787956, "ᡫ䱡땸⤬拾ℭƨ\u08cb럮⭳䭅䷽춹", (char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 6834), "\uf48e狆뉸縚", "䆒\uf319ꍟԾ", objArr10);
        switch (bVar.b(((String) objArr10[0]).intern(), Boolean.FALSE).booleanValue() ? '!' : 'U') {
            default:
                g.c();
                Object[] objArr11 = new Object[1];
                k((byte) (TextUtils.lastIndexOf("", '0', 0, 0) - 94), (-1987799931) - TextUtils.getCapsMode("", 0, 0), (short) View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 25, (ViewConfiguration.getScrollBarSize() >> 8) - 2098933074, objArr11);
                g.d(intern, ((String) objArr11[0]).intern());
                switch (!z ? (char) 14 : '9') {
                    case 14:
                        int i7 = h + 31;
                        i = i7 % 128;
                        int i8 = i7 % 2;
                        if (o.j.c.c(context, o.j.b.c)) {
                            aVar.a().a();
                            break;
                        }
                        break;
                }
                if (o.b.c.e(context) == o.b.e.e) {
                    int i9 = h + 47;
                    i = i9 % 128;
                    if (i9 % 2 != 0) {
                        aVar.a().b();
                        int i10 = 95 / 0;
                    } else {
                        aVar.a().b();
                    }
                }
            case Opcodes.CASTORE /* 85 */:
                return aVar;
        }
    }

    private static void k(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        int i5;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        int i6 = 2;
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(b)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(KeyEvent.getDeadChar(0, 0) + 11, (char) ExpandableListView.getPackedPositionType(0L), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 65);
                byte b3 = (byte) ($$a[0] - 1);
                byte b4 = (byte) (b3 - 3);
                Object[] objArr3 = new Object[1];
                m(b3, b4, (byte) (b4 + 1), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            switch (intValue == -1 ? '+' : '\t') {
                case '+':
                    z = true;
                    break;
                default:
                    z = false;
                    break;
            }
            switch (z ? (char) 5 : '!') {
                case '!':
                    break;
                default:
                    int i7 = $11 + 67;
                    $10 = i7 % 128;
                    if (i7 % 2 != 0) {
                        throw null;
                    }
                    byte[] bArr = d;
                    if (bArr != null) {
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i8 = 0;
                        while (true) {
                            switch (i8 < length) {
                                case true:
                                    int i9 = $10 + 87;
                                    $11 = i9 % 128;
                                    int i10 = i9 % i6;
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(bArr[i8])};
                                        Object obj2 = o.e.a.s.get(494867332);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(19 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) (16425 - Color.red(0)), 149 - TextUtils.lastIndexOf("", '0', 0));
                                            byte b5 = (byte) 0;
                                            byte b6 = (byte) (b5 - 1);
                                            Object[] objArr5 = new Object[1];
                                            m(b5, b6, (byte) (b6 + 1), objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                            o.e.a.s.put(494867332, obj2);
                                        }
                                        bArr2[i8] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                        i8++;
                                        i6 = 2;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                                default:
                                    bArr = bArr2;
                                    break;
                            }
                        }
                    }
                    switch (bArr != null) {
                        case false:
                            intValue = (short) (((short) (a[i2 + ((int) (c ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                            break;
                        default:
                            int i11 = $11 + 33;
                            $10 = i11 % 128;
                            if (i11 % 2 != 0) {
                                byte[] bArr3 = d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(c)};
                                    Object obj3 = o.e.a.s.get(-2120899312);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getTapTimeout() >> 16) + 11, (char) (ViewConfiguration.getJumpTapTimeout() >> 16), 65 - KeyEvent.getDeadChar(0, 0));
                                        byte b7 = (byte) ($$a[0] - 1);
                                        byte b8 = (byte) (b7 - 3);
                                        Object[] objArr7 = new Object[1];
                                        m(b7, b8, (byte) (b8 + 1), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj3);
                                    }
                                    i5 = ((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) * ((int) (b | (-5810760824076169584L)));
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                byte[] bArr4 = d;
                                try {
                                    Object[] objArr8 = {Integer.valueOf(i2), Integer.valueOf(c)};
                                    Object obj4 = o.e.a.s.get(-2120899312);
                                    if (obj4 == null) {
                                        Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 66 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                                        byte b9 = (byte) ($$a[0] - 1);
                                        byte b10 = (byte) (b9 - 3);
                                        Object[] objArr9 = new Object[1];
                                        m(b9, b10, (byte) (b10 + 1), objArr9);
                                        obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj4);
                                    }
                                    i5 = ((byte) (bArr4[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L)));
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            intValue = (byte) i5;
                            break;
                    }
            }
            if (intValue > 0) {
                fVar.d = ((i2 + intValue) - 2) + ((int) (c ^ (-5810760824076169584L))) + (z ? 1 : 0);
                try {
                    Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(e), sb};
                    Object obj5 = o.e.a.s.get(160906762);
                    if (obj5 == null) {
                        obj5 = ((Class) o.e.a.c(12 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (char) (MotionEvent.axisFromString("") + 1), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj5);
                    }
                    ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr5 = d;
                    if (bArr5 != null) {
                        int length2 = bArr5.length;
                        byte[] bArr6 = new byte[length2];
                        for (int i12 = 0; i12 < length2; i12++) {
                            bArr6[i12] = (byte) (bArr5[i12] ^ (-5810760824076169584L));
                        }
                        bArr5 = bArr6;
                    }
                    boolean z2 = bArr5 != null;
                    fVar.c = 1;
                    while (true) {
                        switch (fVar.c < intValue ? 'K' : (char) 21) {
                            case 21:
                                break;
                            default:
                                switch (z2 ? (char) 20 : (char) 4) {
                                    case 20:
                                        byte[] bArr7 = d;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                        int i13 = $11 + 99;
                                        $10 = i13 % 128;
                                        int i14 = i13 % 2;
                                        break;
                                    default:
                                        short[] sArr = a;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r9] ^ (-5810760824076169584L))) + s)) ^ b2));
                                        break;
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                        }
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x003f, code lost:
    
        r2 = o.dg.h.$10 + com.esotericsoftware.asm.Opcodes.LSHL;
        o.dg.h.$11 = r2 % 128;
        r2 = r2 % 2;
        r2 = r24.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x004f, code lost:
    
        r2 = r2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0052, code lost:
    
        if (r22 == null) goto L43;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0054, code lost:
    
        r7 = o.dg.h.$10 + com.esotericsoftware.asm.Opcodes.DREM;
        o.dg.h.$11 = r7 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x005d, code lost:
    
        if ((r7 % 2) != 0) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x005f, code lost:
    
        r7 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0062, code lost:
    
        switch(r7) {
            case 1: goto L38;
            default: goto L37;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0065, code lost:
    
        r22.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x006e, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0069, code lost:
    
        r7 = r22.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0074, code lost:
    
        r8 = new o.a.o();
        r9 = r2.length;
        r10 = new char[r9];
        r11 = r1.length;
        r12 = new char[r11];
        java.lang.System.arraycopy(r2, 0, r10, 0, r9);
        java.lang.System.arraycopy(r1, 0, r12, 0, r11);
        r10[0] = (char) (r10[0] ^ r23);
        r12[2] = (char) (r12[2] + ((char) r21));
        r1 = r7.length;
        r2 = new char[r1];
        r8.e = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x009e, code lost:
    
        if (r8.e >= r1) goto L106;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00a0, code lost:
    
        r9 = new java.lang.Object[]{r8};
        r11 = o.e.a.s.get(-429442487);
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x00b6, code lost:
    
        if (r11 == null) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x00fb, code lost:
    
        r3 = ((java.lang.Integer) ((java.lang.reflect.Method) r11).invoke(null, r9)).intValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0108, code lost:
    
        r4 = new java.lang.Object[]{r8};
        r9 = o.e.a.s.get(-515165572);
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0119, code lost:
    
        if (r9 == null) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0160, code lost:
    
        r4 = ((java.lang.Integer) ((java.lang.reflect.Method) r9).invoke(null, r4)).intValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x016d, code lost:
    
        r9 = r10[r8.e % 4] * 32718;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0178, code lost:
    
        r14 = new java.lang.Object[3];
        r14[2] = java.lang.Integer.valueOf(r12[r3]);
        r14[1] = java.lang.Integer.valueOf(r9);
        r14[r5] = r8;
        r9 = o.e.a.s.get(-1614232674);
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0196, code lost:
    
        if (r9 == null) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x01e5, code lost:
    
        ((java.lang.reflect.Method) r9).invoke(null, r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x01f2, code lost:
    
        r11 = new java.lang.Object[]{java.lang.Integer.valueOf(r10[r4] * 32718), java.lang.Integer.valueOf(r12[r3])};
        r3 = o.e.a.s.get(406147795);
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x020e, code lost:
    
        if (r3 == null) goto L67;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0268, code lost:
    
        r12[r4] = ((java.lang.Character) ((java.lang.reflect.Method) r3).invoke(null, r11)).charValue();
        r10[r4] = r8.d;
        r2[r8.e] = (char) ((((int) (o.dg.h.f ^ 6565854932352255525L)) ^ ((r10[r4] ^ r7[r8.e]) ^ (o.dg.h.j ^ 6565854932352255525L))) ^ ((char) (o.dg.h.g ^ 6565854932352255525L)));
        r8.e++;
        r10 = r10;
        r5 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0212, code lost:
    
        r3 = (java.lang.Class) o.e.a.c(19 - android.text.TextUtils.indexOf("", ""), (char) (14687 - android.widget.ExpandableListView.getPackedPositionType(0)), 112 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16));
        r9 = (byte) o.dg.h.$$a.length;
        r13 = (byte) (r9 - 5);
        r15 = new java.lang.Object[1];
        m(r9, r13, (byte) (r13 + 1), r15);
        r3 = r3.getMethod((java.lang.String) r15[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(406147795, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x02a3, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x02a4, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x02a8, code lost:
    
        if (r1 != null) goto L73;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x02aa, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x02ab, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0199, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(android.widget.ExpandableListView.getPackedPositionChild(0) + 12, (char) android.view.View.combineMeasuredStates(r5, r5), (android.view.ViewConfiguration.getJumpTapTimeout() >> 16) + 281);
        r15 = (byte) (-1);
        r5 = new java.lang.Object[1];
        m((byte) 7, r15, (byte) (r15 + 1), r5);
        r9 = r9.getMethod((java.lang.String) r5[0], java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(-1614232674, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x02ac, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x02ad, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x02b1, code lost:
    
        if (r1 != null) goto L78;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x02b3, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x02b4, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x011c, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), (char) (android.util.TypedValue.complexToFloat(r5) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(r5) == 0.0f ? 0 : -1)), android.graphics.Color.blue(r5) + 207);
        r15 = (byte) (-1);
        r11 = new java.lang.Object[1];
        m((byte) 9, r15, (byte) (r15 + 1), r11);
        r11 = (java.lang.String) r11[r5];
        r13 = new java.lang.Class[1];
        r13[r5] = java.lang.Object.class;
        r9 = r9.getMethod(r11, r13);
        o.e.a.s.put(-515165572, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x02b5, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x02b6, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0018, code lost:
    
        if (r25 != null) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x02ba, code lost:
    
        if (r1 != null) goto L83;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x02bc, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x02bd, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x00b9, code lost:
    
        r3 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)) + 9, (char) (20953 - android.widget.ExpandableListView.getPackedPositionChild(0)), 344 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16));
        r14 = (byte) (-1);
        r4 = new java.lang.Object[1];
        m((byte) (o.dg.h.$$b - 5), r14, (byte) (r14 + 1), r4);
        r4 = (java.lang.String) r4[r5];
        r11 = new java.lang.Class[1];
        r11[r5] = java.lang.Object.class;
        r11 = r3.getMethod(r4, r11);
        o.e.a.s.put(-429442487, r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x02be, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x02bf, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x02c3, code lost:
    
        if (r1 != null) goto L88;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x02c5, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x02c6, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0039, code lost:
    
        r1 = r25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x02c7, code lost:
    
        r26[0] = new java.lang.String(r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x02cf, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x0061, code lost:
    
        r7 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x0072, code lost:
    
        r7 = r22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:85:0x004d, code lost:
    
        r2 = r24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x0024, code lost:
    
        r1 = r1 + com.esotericsoftware.asm.Opcodes.LSUB;
        o.dg.h.$11 = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x002b, code lost:
    
        if ((r1 % 2) == 0) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:88:0x002d, code lost:
    
        r1 = r25.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x0032, code lost:
    
        r25.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x003b, code lost:
    
        r1 = r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x0035, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x001e, code lost:
    
        if (r25 != null) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x003d, code lost:
    
        if (r24 == null) goto L29;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r21, java.lang.String r22, char r23, java.lang.String r24, java.lang.String r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 732
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dg.h.l(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
