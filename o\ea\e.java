package o.ea;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ea\e.smali */
public class e {
    private final a e;
    private static int d = 0;
    private static int a = 1;

    public final o.bv.a d() {
        throw new NullPointerException();
    }

    public final int e() {
        throw new NullPointerException();
    }

    public final int hashCode() {
        int i = d + 3;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                super.hashCode();
                throw null;
            default:
                int hashCode = super.hashCode();
                int i2 = a + Opcodes.LMUL;
                d = i2 % 128;
                int i3 = i2 % 2;
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = d;
        int i2 = (i & 45) + (i | 45);
        a = i2 % 128;
        char c = i2 % 2 == 0 ? 'K' : '7';
        boolean equals = super.equals(obj);
        switch (c) {
            case 'K':
                int i3 = 48 / 0;
                break;
        }
        int i4 = a + 43;
        d = i4 % 128;
        int i5 = i4 % 2;
        return equals;
    }

    public final String toString() {
        int i = d;
        int i2 = (i ^ 47) + ((i & 47) << 1);
        a = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                String obj2 = super.toString();
                int i3 = d;
                int i4 = ((i3 | 85) << 1) - (i3 ^ 85);
                a = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return obj2;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                super.toString();
                obj.hashCode();
                throw null;
        }
    }

    protected final void finalize() throws Throwable {
        int i = a;
        int i2 = (i ^ 83) + ((i & 83) << 1);
        d = i2 % 128;
        int i3 = i2 % 2;
        super.finalize();
        int i4 = d;
        int i5 = (i4 & Opcodes.LUSHR) + (i4 | Opcodes.LUSHR);
        a = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 3 : '.') {
            case 3:
                throw null;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
