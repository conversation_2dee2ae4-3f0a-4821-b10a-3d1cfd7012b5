package o.ei;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.m;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ei\e.smali */
public final class e implements b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static e a;
    private static e b;
    private static e c;
    private static e d;
    private static e e;
    private static e g;
    private static e h;
    private static e i;
    private static e j;
    private static char l;
    private static char[] m;
    private static final /* synthetic */ e[] n;

    /* renamed from: o, reason: collision with root package name */
    private static char[] f63o;
    private static int p;
    private static int r;
    private static long t;
    private final String f;
    private final a k;

    static void c() {
        f63o = new char[]{30542, 30563, 30576, 30530, 30589, 30557, 30555, 30553, 30538, 30529, 30583, 30581, 30559, 30577, 30578, 30540, 30579, 30568, 30571, 30574, 30535, 30587, 30580, 30582, 30572, 30536, 30588, 30554, 30570, 30566, 30537, 30539, 30534, 30586, 30556, 30544};
        l = (char) 17043;
        m = new char[]{11393, 2588, 24992, 24404, 46808, 60532, 51998, 8838, 6204, 30685, 44362, 34038, 58240, 55556, 12457, 28230, 17915, 41852, 39428, 61881, 12085, 39607, 48138, 55222, 59764, 212, 23138, 32014, 38016, 44605, 49641, 6998, 13055, 21906, 42333, 33760, 59484, 54913, 16185, 25992, 11393, 2588, 24992, 24404, 46791, 60531, 51998, 30561, 20956, 14944, 1186, 60693, 47010, 37056, 31066, 17400, 11393, 2620, 24960, 24385, 46834, 60485, 11393, 2588, 24992, 24404, 46811, 60516, 51983, 7935, 14431, 21490, 27928, 33937, 56880, 63867, 4309, 10877, 31227, 24411, 13543, 2591, 58242, 47395, 40549, 30684, 19816, 8859, 63488, 40503, 47276, 54040, 60920, 1151, 24284, 31163, 36918, 43648, 50548, 8189, 13893};
        t = 7622376405026605663L;
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, -60, 102, -85};
        $$b = Opcodes.D2F;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r9 = r9 + 69
            int r7 = r7 * 4
            int r7 = 5 - r7
            byte[] r0 = o.ei.e.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            goto L32
        L17:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1b:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L32:
            int r9 = -r9
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.e.u(byte, byte, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ e[] d() {
        int i2 = r;
        int i3 = i2 + 65;
        p = i3 % 128;
        int i4 = i3 % 2;
        e[] eVarArr = {e, a, d, b, c, h, j, g, i};
        int i5 = i2 + Opcodes.LNEG;
        p = i5 % 128;
        int i6 = i5 % 2;
        return eVarArr;
    }

    public static e valueOf(String str) {
        int i2 = r + 59;
        p = i2 % 128;
        int i3 = i2 % 2;
        e eVar = (e) Enum.valueOf(e.class, str);
        int i4 = r + 81;
        p = i4 % 128;
        switch (i4 % 2 != 0 ? 'M' : 'F') {
            case 'M':
                int i5 = 62 / 0;
                return eVar;
            default:
                return eVar;
        }
    }

    public static e[] values() {
        int i2 = r + 37;
        p = i2 % 128;
        int i3 = i2 % 2;
        e[] eVarArr = (e[]) n.clone();
        int i4 = p + 33;
        r = i4 % 128;
        switch (i4 % 2 == 0 ? 'E' : (char) 22) {
            case 'E':
                int i5 = 56 / 0;
                return eVarArr;
            default:
                return eVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        p = 0;
        r = 1;
        c();
        Object[] objArr = new Object[1];
        q((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 18, "\u0012\u001a\u001a\"㘒㘒\"\u001b\u0005\u0004\u0014\u0019\u0016\u001b\u0000\u001c\u0016\u0001㘧", (byte) (TextUtils.getOffsetAfter("", 0) + 41), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        s((char) TextUtils.indexOf("", "", 0, 0), TextUtils.lastIndexOf("", '0') + 1, (ViewConfiguration.getFadingEdgeLength() >> 16) + 21, objArr2);
        e = new e(intern, 0, ((String) objArr2[0]).intern(), a.d);
        Object[] objArr3 = new Object[1];
        s((char) ((Process.myPid() >> 22) + 46646), (Process.myPid() >> 22) + 21, 14 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        q(14 - Process.getGidForName(""), "\u0015\u000e\u000b !#!\u001c\u000b\u0002\u001e\u0011\u001d\u0003㗧", (byte) ((ViewConfiguration.getScrollBarSize() >> 8) + 8), objArr4);
        a = new e(intern2, 1, ((String) objArr4[0]).intern(), a.d);
        Object[] objArr5 = new Object[1];
        q(Color.red(0) + 7, "\u0012\u001a\u001b\u0004\u0016\u0018㙌", (byte) (100 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        q(8 - Color.alpha(0), "\u0015\u000e\u000b \u0001!\n ", (byte) (TextUtils.getOffsetAfter("", 0) + 9), objArr6);
        d = new e(intern3, 2, ((String) objArr6[0]).intern(), a.d);
        Object[] objArr7 = new Object[1];
        s((char) (35292 - (Process.myTid() >> 22)), 34 - Color.alpha(0), (ViewConfiguration.getLongPressTimeout() >> 16) + 6, objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        s((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 40 - TextUtils.getTrimmedLength(""), (ViewConfiguration.getLongPressTimeout() >> 16) + 7, objArr8);
        b = new e(intern4, 3, ((String) objArr8[0]).intern(), a.d);
        Object[] objArr9 = new Object[1];
        s((char) (23520 - View.MeasureSpec.makeMeasureSpec(0, 0)), 47 - (Process.myTid() >> 22), 9 - (Process.myPid() >> 22), objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        q(10 - (ViewConfiguration.getScrollBarSize() >> 8), "\u0015\u000e\u000b ! \t\u0002\u001e\u0002", (byte) (View.MeasureSpec.getMode(0) + 58), objArr10);
        c = new e(intern5, 4, ((String) objArr10[0]).intern(), a.d);
        Object[] objArr11 = new Object[1];
        s((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 55, 6 - Color.blue(0), objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        s((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), Color.argb(0, 0, 0, 0) + 62, 7 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr12);
        h = new e(intern6, 5, ((String) objArr12[0]).intern(), a.d);
        Object[] objArr13 = new Object[1];
        s((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 12901), (ViewConfiguration.getScrollBarSize() >> 8) + 69, ((Process.getThreadPriority(0) + 20) >> 6) + 9, objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        q(10 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), " \n\u0001\u000b\u000b\u0002\u001e#\f\u0003", (byte) (87 - TextUtils.lastIndexOf("", '0')), objArr14);
        j = new e(intern7, 6, ((String) objArr14[0]).intern(), a.a);
        Object[] objArr15 = new Object[1];
        s((char) (21857 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), Process.getGidForName("") + 79, 11 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr15);
        String intern8 = ((String) objArr15[0]).intern();
        Object[] objArr16 = new Object[1];
        q(TextUtils.indexOf("", "", 0, 0) + 12, " \n\u0015!\u0002\u000b\u001e!\u0006\u000f\u0018\t", (byte) (44 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), objArr16);
        g = new e(intern8, 7, ((String) objArr16[0]).intern(), a.a);
        Object[] objArr17 = new Object[1];
        q(Color.argb(0, 0, 0, 0) + 11, "#\u0019\u0017#\u0016\u0014\u0003\r\u0016\u0001㙸", (byte) (View.resolveSizeAndState(0, 0, 0) + Opcodes.ISHR), objArr17);
        String intern9 = ((String) objArr17[0]).intern();
        Object[] objArr18 = new Object[1];
        s((char) (45754 - Color.blue(0)), 89 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 12, objArr18);
        i = new e(intern9, 8, ((String) objArr18[0]).intern(), a.b);
        n = d();
        int i2 = p + 35;
        r = i2 % 128;
        int i3 = i2 % 2;
    }

    private e(String str, int i2, String str2, a aVar) {
        this.f = str2;
        this.k = aVar;
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = r + Opcodes.DMUL;
        p = i2 % 128;
        switch (i2 % 2 != 0 ? '`' : ';') {
            case ';':
                return this.f;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final a a() {
        int i2 = r + 47;
        int i3 = i2 % 128;
        p = i3;
        int i4 = i2 % 2;
        a aVar = this.k;
        int i5 = i3 + 15;
        r = i5 % 128;
        int i6 = i5 % 2;
        return aVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.ei.e b(java.lang.String r7) {
        /*
            int r0 = o.ei.e.p
            int r0 = r0 + 91
            int r1 = r0 % 128
            o.ei.e.r = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 != 0) goto L13
            o.ei.e[] r0 = values()
            int r2 = r0.length
            goto L18
        L13:
            o.ei.e[] r0 = values()
            int r2 = r0.length
        L18:
            r3 = r1
        L19:
            if (r3 >= r2) goto L1d
            r4 = 1
            goto L1e
        L1d:
            r4 = r1
        L1e:
            r5 = 0
            switch(r4) {
                case 1: goto L23;
                default: goto L22;
            }
        L22:
            goto L4d
        L23:
            r4 = r0[r3]
            java.lang.String r6 = r4.f
            boolean r6 = r6.equals(r7)
            if (r6 == 0) goto L30
            r6 = 12
            goto L32
        L30:
            r6 = 76
        L32:
            switch(r6) {
                case 76: goto L42;
                default: goto L35;
            }
        L35:
            int r7 = o.ei.e.r
            int r7 = r7 + 35
            int r0 = r7 % 128
            o.ei.e.p = r0
            int r7 = r7 % 2
            if (r7 != 0) goto L47
            goto L45
        L42:
            int r3 = r3 + 1
            goto L19
        L45:
            return r4
        L47:
            r5.hashCode()     // Catch: java.lang.Throwable -> L4b
            throw r5     // Catch: java.lang.Throwable -> L4b
        L4b:
            r7 = move-exception
            throw r7
        L4d:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.e.b(java.lang.String):o.ei.e");
    }

    private static void q(int i2, String str, byte b2, Object[] objArr) {
        char[] cArr;
        int i3;
        char c2;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                int i4 = $11 + 23;
                $10 = i4 % 128;
                if (i4 % 2 != 0) {
                    str.toCharArray();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = f63o;
        int i5 = 16;
        switch (cArr3 == null) {
            case false:
                int i6 = $11 + Opcodes.DSUB;
                $10 = i6 % 128;
                int i7 = i6 % 2;
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i8 = 0;
                while (i8 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr3[i8])};
                        Object obj2 = o.e.a.s.get(-1401577988);
                        if (obj2 == null) {
                            Class cls = (Class) o.e.a.c(17 - (ViewConfiguration.getMinimumFlingVelocity() >> i5), (char) View.MeasureSpec.getSize(0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 76);
                            byte b3 = $$a[0];
                            byte b4 = b3;
                            Object[] objArr3 = new Object[1];
                            u(b3, b4, (byte) (b4 + 4), objArr3);
                            obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj2);
                        }
                        cArr4[i8] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                        i8++;
                        i5 = 16;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(l)};
            Object obj3 = o.e.a.s.get(-1401577988);
            long j2 = 0;
            if (obj3 == null) {
                Class cls2 = (Class) o.e.a.c(17 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), ExpandableListView.getPackedPositionType(0L) + 76);
                byte b5 = $$a[0];
                byte b6 = b5;
                Object[] objArr5 = new Object[1];
                u(b5, b6, (byte) (b6 + 4), objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj3);
            }
            char charValue = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i2];
            if (i2 % 2 != 0) {
                i3 = i2 - 1;
                cArr5[i3] = (char) (cArr2[i3] - b2);
            } else {
                i3 = i2;
            }
            char c3 = 11;
            switch (i3 > 1 ? 'T' : (char) 11) {
                case 11:
                    break;
                default:
                    mVar.b = 0;
                    while (true) {
                        switch (mVar.b < i3 ? (char) 20 : (char) 26) {
                            case 26:
                                break;
                            default:
                                int i9 = $10 + 59;
                                $11 = i9 % 128;
                                if (i9 % 2 == 0) {
                                }
                                mVar.e = cArr2[mVar.b];
                                mVar.a = cArr2[mVar.b + 1];
                                switch (mVar.e == mVar.a ? (char) 16 : (char) 14) {
                                    case 14:
                                        try {
                                            Object[] objArr6 = new Object[13];
                                            objArr6[12] = mVar;
                                            objArr6[c3] = Integer.valueOf(charValue);
                                            objArr6[10] = mVar;
                                            objArr6[9] = mVar;
                                            objArr6[8] = Integer.valueOf(charValue);
                                            objArr6[7] = mVar;
                                            objArr6[6] = mVar;
                                            objArr6[5] = Integer.valueOf(charValue);
                                            objArr6[4] = mVar;
                                            objArr6[3] = mVar;
                                            objArr6[2] = Integer.valueOf(charValue);
                                            objArr6[1] = mVar;
                                            objArr6[0] = mVar;
                                            Object obj4 = o.e.a.s.get(696901393);
                                            if (obj4 == null) {
                                                Class cls3 = (Class) o.e.a.c(TextUtils.indexOf("", "", 0, 0) + 10, (char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 8856), (ExpandableListView.getPackedPositionForChild(0, 0) > j2 ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == j2 ? 0 : -1)) + 325);
                                                byte b7 = $$a[0];
                                                byte b8 = b7;
                                                Object[] objArr7 = new Object[1];
                                                u(b7, b8, b8, objArr7);
                                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(696901393, obj4);
                                            }
                                            switch (((Integer) ((Method) obj4).invoke(null, objArr6)).intValue() == mVar.h ? (char) 27 : '9') {
                                                case '9':
                                                    switch (mVar.c == mVar.d ? (char) 0 : 'J') {
                                                        case 'J':
                                                            int i10 = (mVar.c * charValue) + mVar.h;
                                                            int i11 = (mVar.d * charValue) + mVar.i;
                                                            cArr5[mVar.b] = cArr3[i10];
                                                            cArr5[mVar.b + 1] = cArr3[i11];
                                                            c2 = 11;
                                                            break;
                                                        default:
                                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                            int i12 = (mVar.c * charValue) + mVar.i;
                                                            int i13 = (mVar.d * charValue) + mVar.h;
                                                            cArr5[mVar.b] = cArr3[i12];
                                                            cArr5[mVar.b + 1] = cArr3[i13];
                                                            c2 = 11;
                                                            break;
                                                    }
                                                default:
                                                    try {
                                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                        Object obj5 = o.e.a.s.get(1075449051);
                                                        if (obj5 != null) {
                                                            c2 = 11;
                                                        } else {
                                                            Class cls4 = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 10, (char) (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getWindowTouchSlop() >> 8) + 65);
                                                            byte b9 = $$a[0];
                                                            byte b10 = b9;
                                                            Object[] objArr9 = new Object[1];
                                                            u(b9, b10, (byte) (b10 + 1), objArr9);
                                                            c2 = 11;
                                                            obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                            o.e.a.s.put(1075449051, obj5);
                                                        }
                                                        int intValue = ((Integer) ((Method) obj5).invoke(null, objArr8)).intValue();
                                                        int i14 = (mVar.d * charValue) + mVar.h;
                                                        cArr5[mVar.b] = cArr3[intValue];
                                                        cArr5[mVar.b + 1] = cArr3[i14];
                                                        break;
                                                    } catch (Throwable th2) {
                                                        Throwable cause2 = th2.getCause();
                                                        if (cause2 == null) {
                                                            throw th2;
                                                        }
                                                        throw cause2;
                                                    }
                                            }
                                        } catch (Throwable th3) {
                                            Throwable cause3 = th3.getCause();
                                            if (cause3 == null) {
                                                throw th3;
                                            }
                                            throw cause3;
                                        }
                                    default:
                                        c2 = c3;
                                        cArr5[mVar.b] = (char) (mVar.e - b2);
                                        cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                        break;
                                }
                                mVar.b += 2;
                                c3 = c2;
                                j2 = 0;
                        }
                    }
            }
            for (int i15 = 0; i15 < i2; i15++) {
                cArr5[i15] = (char) (cArr5[i15] ^ 13722);
            }
            objArr[0] = new String(cArr5);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void s(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1024
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.e.s(char, int, int, java.lang.Object[]):void");
    }
}
