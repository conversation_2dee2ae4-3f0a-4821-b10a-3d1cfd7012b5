package o.p;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\j.smali */
public final class j extends Exception {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int c;
    private static int d;
    private final c e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        d = 1;
        b = 8294151100122751003L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0032  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x002a  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0032 -> B:4:0x003b). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 3 - r6
            int r8 = r8 * 2
            int r8 = r8 + 112
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = o.p.j.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r8 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            goto L3b
        L1c:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L21:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L32
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L32:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L3b:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L21
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.j.f(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{79, Tnaf.POW_2_WIDTH, 60, 65};
        $$b = Opcodes.L2D;
    }

    public j(c cVar) {
        this.e = cVar;
    }

    public final c e() {
        int i = d + 109;
        int i2 = i % 128;
        c = i2;
        int i3 = i % 2;
        c cVar = this.e;
        int i4 = i2 + Opcodes.LSUB;
        d = i4 % 128;
        switch (i4 % 2 == 0 ? '*' : 'I') {
            case '*':
                int i5 = 77 / 0;
                return cVar;
            default:
                return cVar;
        }
    }

    /* renamed from: o.p.j$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\j$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int b;
        private static int c = 1;
        static final /* synthetic */ int[] d;

        static {
            b = 0;
            int[] iArr = new int[c.values().length];
            d = iArr;
            try {
                iArr[c.b.ordinal()] = 1;
                int i = c + 75;
                b = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                d[c.c.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[c.e.ordinal()] = 3;
                int i2 = (c + 114) - 1;
                b = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[c.d.ordinal()] = 4;
                int i4 = c + 23;
                b = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    public final WalletValidationException c(String str) {
        int i = d + 73;
        c = i % 128;
        int i2 = i % 2;
        switch (AnonymousClass3.d[this.e.ordinal()]) {
            case 1:
                WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
                Object[] objArr = new Object[1];
                a("ꛈษ\uf756屺ָ\uead5古㬘\ue072䥱㺙\ue7de䳺㐎鴽䉻⮘邭秡℗阨罆⒲趨狆\uda10茸案텻蚹濘퓮반", 43224 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
                WalletValidationException walletValidationException = new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                int i3 = c + 5;
                d = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return walletValidationException;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            case 2:
                WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                Object[] objArr2 = new Object[1];
                a("ꛜ\u0099\uea01喾㼢\ue6c0", 42612 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr2);
                return new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
            case 3:
                return new WalletValidationException(WalletValidationErrorCode.WrongState, str);
            case 4:
                WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Unknown;
                Object[] objArr3 = new Object[1];
                a("ꛈ\ud809嬖\uda1a崸\udc35弤\ude38兲큑卙퉾啺푮坽횛䦘좍䮡쪷䶨첦信컛䇗샠䏸싴䗡", 32503 - (ViewConfiguration.getEdgeSlop() >> 16), objArr3);
                return new WalletValidationException(walletValidationErrorCode3, ((String) objArr3[0]).intern());
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                a("ꛞ\ufff2ᓀ궶슧ᮝ끢쥞湖蜠\udc4d甠课\u20cc禼麷㟁䰬", 22807 - Color.red(0), objArr4);
                throw new UnsupportedOperationException(sb.append(((String) objArr4[0]).intern()).append(this.e.name()).toString());
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\j$c.smali */
    public static final class c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static final /* synthetic */ c[] a;
        public static final c b;
        public static final c c;
        public static final c d;
        public static final c e;
        private static int g;
        private static int h;
        private static long i;
        private static char[] j;

        static void a() {
            j = new char[]{11392, 49360, 62545, 59853, 40313, 45811, 42599, 23519, 20240, 31882, 4099, 1457, 14633, 11921, 49727, 63057, 60365, 40779, 11392, 49360, 62545, 59853, 40313, 45811, 42599, 23512, 20227, 31881, 4108, 1457, 14638, 11953, 49688, 63044, 60376, 40794, 36082, 51652, 9607, 4383, 3208, 30780, 22456, 17211, 48813, 43614, 39371, 62807, 57585, 56446, 52192, 10071, 4893, 3713, 31254, 27070, 17698, 45223, 44051, 39889, 63327, 58051, 56949, 52708, 14694, 11420, 49360, 62530, 59860, 40293, 45823, 42592, 23548, 20244, 31874, 4140, 1446, 14648, 11942, 49710, 63070, 60365, 40775, 36086, 41072, 22006};
            i = 7406206726321193150L;
        }

        static void init$0() {
            $$a = new byte[]{48, 67, 97, 27};
            $$b = 28;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(int r6, short r7, short r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 4
                int r6 = r6 + 1
                int r8 = r8 + 102
                byte[] r0 = o.p.j.c.$$a
                int r7 = r7 + 4
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L16
                r8 = r7
                r3 = r8
                r4 = r2
                r7 = r6
                goto L2e
            L16:
                r3 = r2
            L17:
                int r7 = r7 + 1
                byte r4 = (byte) r8
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                r3 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r8
                r8 = r5
            L2e:
                int r6 = r6 + r3
                r3 = r4
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r5
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.p.j.c.k(int, short, short, java.lang.Object[]):void");
        }

        private c(String str, int i2) {
        }

        private static /* synthetic */ c[] e() {
            c[] cVarArr;
            int i2 = g + 109;
            int i3 = i2 % 128;
            h = i3;
            switch (i2 % 2 == 0) {
                case true:
                    cVarArr = new c[5];
                    cVarArr[0] = c;
                    cVarArr[0] = e;
                    cVarArr[5] = d;
                    cVarArr[2] = b;
                    break;
                default:
                    cVarArr = new c[]{c, e, d, b};
                    break;
            }
            int i4 = i3 + 27;
            g = i4 % 128;
            switch (i4 % 2 != 0 ? 'R' : 'W') {
                case Opcodes.POP /* 87 */:
                    return cVarArr;
                default:
                    throw null;
            }
        }

        public static c valueOf(String str) {
            int i2 = g + 19;
            h = i2 % 128;
            int i3 = i2 % 2;
            c cVar = (c) Enum.valueOf(c.class, str);
            int i4 = g + 21;
            h = i4 % 128;
            switch (i4 % 2 == 0 ? 'F' : 'R') {
                case Opcodes.DASTORE /* 82 */:
                    return cVar;
                default:
                    int i5 = 41 / 0;
                    return cVar;
            }
        }

        public static c[] values() {
            int i2 = h + 43;
            g = i2 % 128;
            int i3 = i2 % 2;
            c[] cVarArr = (c[]) a.clone();
            int i4 = h + 49;
            g = i4 % 128;
            int i5 = i4 % 2;
            return cVarArr;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            g = 0;
            h = 1;
            a();
            Object[] objArr = new Object[1];
            f((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), ViewConfiguration.getEdgeSlop() >> 16, 19 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
            c = new c(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            f((char) TextUtils.getTrimmedLength(""), 18 - View.getDefaultSize(0, 0), ExpandableListView.getPackedPositionChild(0L) + 20, objArr2);
            e = new c(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            f((char) (58700 - TextUtils.indexOf("", "")), 37 - Drawable.resolveOpacity(0, 0), TextUtils.getTrimmedLength("") + 28, objArr3);
            d = new c(((String) objArr3[0]).intern(), 2);
            Object[] objArr4 = new Object[1];
            f((char) Color.alpha(0), 65 - View.resolveSize(0, 0), Color.red(0) + 21, objArr4);
            b = new c(((String) objArr4[0]).intern(), 3);
            a = e();
            int i2 = g + 47;
            h = i2 % 128;
            int i3 = i2 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(char r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 732
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.p.j.c.f(char, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:77:0x0259  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x002b  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void a(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 942
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.j.a(java.lang.String, int, java.lang.Object[]):void");
    }
}
