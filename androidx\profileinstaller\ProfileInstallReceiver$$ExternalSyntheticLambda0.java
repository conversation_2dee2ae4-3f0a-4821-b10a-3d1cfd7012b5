package androidx.profileinstaller;

import java.util.concurrent.Executor;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\profileinstaller\ProfileInstallReceiver$$ExternalSyntheticLambda0.smali */
public final /* synthetic */ class ProfileInstallReceiver$$ExternalSyntheticLambda0 implements Executor {
    @Override // java.util.concurrent.Executor
    public final void execute(Runnable runnable) {
        runnable.run();
    }
}
