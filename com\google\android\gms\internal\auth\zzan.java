package com.google.android.gms.internal.auth;

import android.os.RemoteException;
import com.google.android.gms.auth.api.accounttransfer.DeviceMetaData;
import com.google.android.gms.common.api.Status;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\internal\auth\zzan.smali */
public class zzan extends zzas {
    public void zzb(byte[] bArr) {
        throw new UnsupportedOperationException();
    }

    public void zzc(DeviceMetaData deviceMetaData) {
        throw new UnsupportedOperationException();
    }

    public void zzd(Status status) {
        throw new UnsupportedOperationException();
    }

    public void zze() {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.auth.zzat
    public final void zzf(Status status, com.google.android.gms.auth.api.accounttransfer.zzw zzwVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.auth.zzat
    public final void zzg(Status status, com.google.android.gms.auth.api.accounttransfer.zzo zzoVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.auth.zzat
    public final void zzh(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }
}
