package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import java.math.BigInteger;
import java.util.Random;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECFieldElement.smali */
public abstract class ECFieldElement implements ECConstants {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECFieldElement$AbstractF2m.smali */
    public static abstract class AbstractF2m extends ECFieldElement {
        public ECFieldElement halfTrace() {
            int fieldSize = getFieldSize();
            if ((fieldSize & 1) == 0) {
                throw new IllegalStateException("Half-trace only defined for odd m");
            }
            int i = (fieldSize + 1) >>> 1;
            int a = 31 - e5.a(i);
            ECFieldElement eCFieldElement = this;
            int i2 = 1;
            while (a > 0) {
                eCFieldElement = eCFieldElement.squarePow(i2 << 1).add(eCFieldElement);
                a--;
                i2 = i >>> a;
                if ((i2 & 1) != 0) {
                    eCFieldElement = eCFieldElement.squarePow(2).add(this);
                }
            }
            return eCFieldElement;
        }

        public boolean hasFastTrace() {
            return false;
        }

        public int trace() {
            int fieldSize = getFieldSize();
            int a = 31 - e5.a(fieldSize);
            ECFieldElement eCFieldElement = this;
            int i = 1;
            while (a > 0) {
                eCFieldElement = eCFieldElement.squarePow(i).add(eCFieldElement);
                a--;
                i = fieldSize >>> a;
                if ((i & 1) != 0) {
                    eCFieldElement = eCFieldElement.square().add(this);
                }
            }
            if (eCFieldElement.isZero()) {
                return 0;
            }
            if (eCFieldElement.isOne()) {
                return 1;
            }
            throw new IllegalStateException("Internal error in trace calculation");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECFieldElement$AbstractFp.smali */
    public static abstract class AbstractFp extends ECFieldElement {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECFieldElement$F2m.smali */
    public static class F2m extends AbstractF2m {
        public static final int GNB = 1;
        public static final int PPB = 3;
        public static final int TPB = 2;
        private int a;
        private int b;
        private int[] c;
        a d;

        F2m(int i, int[] iArr, a aVar) {
            this.b = i;
            this.a = iArr.length == 1 ? 2 : 3;
            this.c = iArr;
            this.d = aVar;
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement add(ECFieldElement eCFieldElement) {
            a aVar = (a) this.d.clone();
            aVar.a(((F2m) eCFieldElement).d, 0);
            return new F2m(this.b, this.c, aVar);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement addOne() {
            return new F2m(this.b, this.c, this.d.a());
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public int bitLength() {
            return this.d.b();
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement divide(ECFieldElement eCFieldElement) {
            return multiply(eCFieldElement.invert());
        }

        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof F2m)) {
                return false;
            }
            F2m f2m = (F2m) obj;
            return this.b == f2m.b && this.a == f2m.a && Arrays.areEqual(this.c, f2m.c) && this.d.equals(f2m.d);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public String getFieldName() {
            return "F2m";
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public int getFieldSize() {
            return this.b;
        }

        public int getK1() {
            return this.c[0];
        }

        public int getK2() {
            int[] iArr = this.c;
            if (iArr.length >= 2) {
                return iArr[1];
            }
            return 0;
        }

        public int getK3() {
            int[] iArr = this.c;
            if (iArr.length >= 3) {
                return iArr[2];
            }
            return 0;
        }

        public int getM() {
            return this.b;
        }

        public int getRepresentation() {
            return this.a;
        }

        public int hashCode() {
            return (this.d.hashCode() ^ this.b) ^ Arrays.hashCode(this.c);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement invert() {
            int i = this.b;
            int[] iArr = this.c;
            return new F2m(i, iArr, this.d.a(i, iArr));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public boolean isOne() {
            return this.d.d();
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public boolean isZero() {
            return this.d.e();
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiply(ECFieldElement eCFieldElement) {
            int i = this.b;
            int[] iArr = this.c;
            return new F2m(i, iArr, this.d.a(((F2m) eCFieldElement).d, i, iArr));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
            return multiplyPlusProduct(eCFieldElement, eCFieldElement2, eCFieldElement3);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
            a aVar = this.d;
            a aVar2 = ((F2m) eCFieldElement).d;
            a aVar3 = ((F2m) eCFieldElement2).d;
            a aVar4 = ((F2m) eCFieldElement3).d;
            a b = aVar.b(aVar2, this.b, this.c);
            a b2 = aVar3.b(aVar4, this.b, this.c);
            if (b == aVar || b == aVar2) {
                b = (a) b.clone();
            }
            b.a(b2, 0);
            b.c(this.b, this.c);
            return new F2m(this.b, this.c, b);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement negate() {
            return this;
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement sqrt() {
            return (this.d.e() || this.d.d()) ? this : squarePow(this.b - 1);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement square() {
            int i = this.b;
            int[] iArr = this.c;
            return new F2m(i, iArr, this.d.b(i, iArr));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            return squarePlusProduct(eCFieldElement, eCFieldElement2);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            a aVar = this.d;
            a aVar2 = ((F2m) eCFieldElement).d;
            a aVar3 = ((F2m) eCFieldElement2).d;
            a d = aVar.d(this.b, this.c);
            a b = aVar2.b(aVar3, this.b, this.c);
            if (d == aVar) {
                d = (a) d.clone();
            }
            d.a(b, 0);
            d.c(this.b, this.c);
            return new F2m(this.b, this.c, d);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement squarePow(int i) {
            if (i < 1) {
                return this;
            }
            int i2 = this.b;
            int[] iArr = this.c;
            return new F2m(i2, iArr, this.d.a(i, i2, iArr));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement subtract(ECFieldElement eCFieldElement) {
            return add(eCFieldElement);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public boolean testBitZero() {
            return this.d.f();
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public BigInteger toBigInteger() {
            return this.d.g();
        }
    }

    public abstract ECFieldElement add(ECFieldElement eCFieldElement);

    public abstract ECFieldElement addOne();

    public int bitLength() {
        return toBigInteger().bitLength();
    }

    public abstract ECFieldElement divide(ECFieldElement eCFieldElement);

    public byte[] getEncoded() {
        return f1.a((getFieldSize() + 7) / 8, toBigInteger());
    }

    public abstract String getFieldName();

    public abstract int getFieldSize();

    public abstract ECFieldElement invert();

    public boolean isOne() {
        return bitLength() == 1;
    }

    public boolean isZero() {
        return toBigInteger().signum() == 0;
    }

    public abstract ECFieldElement multiply(ECFieldElement eCFieldElement);

    public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        return multiply(eCFieldElement).subtract(eCFieldElement2.multiply(eCFieldElement3));
    }

    public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        return multiply(eCFieldElement).add(eCFieldElement2.multiply(eCFieldElement3));
    }

    public abstract ECFieldElement negate();

    public abstract ECFieldElement sqrt();

    public abstract ECFieldElement square();

    public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return square().subtract(eCFieldElement.multiply(eCFieldElement2));
    }

    public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return square().add(eCFieldElement.multiply(eCFieldElement2));
    }

    public ECFieldElement squarePow(int i) {
        ECFieldElement eCFieldElement = this;
        for (int i2 = 0; i2 < i; i2++) {
            eCFieldElement = eCFieldElement.square();
        }
        return eCFieldElement;
    }

    public abstract ECFieldElement subtract(ECFieldElement eCFieldElement);

    public boolean testBitZero() {
        return toBigInteger().testBit(0);
    }

    public abstract BigInteger toBigInteger();

    public String toString() {
        return toBigInteger().toString(16);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECFieldElement$Fp.smali */
    public static class Fp extends AbstractFp {
        BigInteger a;
        BigInteger b;
        BigInteger c;

        Fp(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
            this.a = bigInteger;
            this.b = bigInteger2;
            this.c = bigInteger3;
        }

        static BigInteger a(BigInteger bigInteger) {
            int bitLength = bigInteger.bitLength();
            if (bitLength < 96 || bigInteger.shiftRight(bitLength - 64).longValue() != -1) {
                return null;
            }
            return ECConstants.ONE.shiftLeft(bitLength).subtract(bigInteger);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement add(ECFieldElement eCFieldElement) {
            return new Fp(this.a, this.b, a(this.c, eCFieldElement.toBigInteger()));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement addOne() {
            BigInteger add = this.c.add(ECConstants.ONE);
            if (add.compareTo(this.a) == 0) {
                add = ECConstants.ZERO;
            }
            return new Fp(this.a, this.b, add);
        }

        protected BigInteger b(BigInteger bigInteger) {
            BigInteger shiftLeft = bigInteger.shiftLeft(1);
            return shiftLeft.compareTo(this.a) >= 0 ? shiftLeft.subtract(this.a) : shiftLeft;
        }

        protected BigInteger c(BigInteger bigInteger) {
            if (bigInteger.testBit(0)) {
                bigInteger = this.a.subtract(bigInteger);
            }
            return bigInteger.shiftRight(1);
        }

        protected BigInteger d(BigInteger bigInteger) {
            return f1.a(this.a, bigInteger);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement divide(ECFieldElement eCFieldElement) {
            return new Fp(this.a, this.b, b(this.c, d(eCFieldElement.toBigInteger())));
        }

        protected BigInteger e(BigInteger bigInteger) {
            if (this.b == null) {
                return bigInteger.mod(this.a);
            }
            boolean z = bigInteger.signum() < 0;
            if (z) {
                bigInteger = bigInteger.abs();
            }
            int bitLength = this.a.bitLength();
            boolean equals = this.b.equals(ECConstants.ONE);
            while (bigInteger.bitLength() > bitLength + 1) {
                BigInteger shiftRight = bigInteger.shiftRight(bitLength);
                BigInteger subtract = bigInteger.subtract(shiftRight.shiftLeft(bitLength));
                if (!equals) {
                    shiftRight = shiftRight.multiply(this.b);
                }
                bigInteger = shiftRight.add(subtract);
            }
            while (bigInteger.compareTo(this.a) >= 0) {
                bigInteger = bigInteger.subtract(this.a);
            }
            return (!z || bigInteger.signum() == 0) ? bigInteger : this.a.subtract(bigInteger);
        }

        public boolean equals(Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof Fp)) {
                return false;
            }
            Fp fp = (Fp) obj;
            return this.a.equals(fp.a) && this.c.equals(fp.c);
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public String getFieldName() {
            return "Fp";
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public int getFieldSize() {
            return this.a.bitLength();
        }

        public BigInteger getQ() {
            return this.a;
        }

        public int hashCode() {
            return this.a.hashCode() ^ this.c.hashCode();
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement invert() {
            return new Fp(this.a, this.b, d(this.c));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiply(ECFieldElement eCFieldElement) {
            return new Fp(this.a, this.b, b(this.c, eCFieldElement.toBigInteger()));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
            BigInteger bigInteger = this.c;
            BigInteger bigInteger2 = eCFieldElement.toBigInteger();
            BigInteger bigInteger3 = eCFieldElement2.toBigInteger();
            BigInteger bigInteger4 = eCFieldElement3.toBigInteger();
            return new Fp(this.a, this.b, e(bigInteger.multiply(bigInteger2).subtract(bigInteger3.multiply(bigInteger4))));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
            BigInteger bigInteger = this.c;
            BigInteger bigInteger2 = eCFieldElement.toBigInteger();
            BigInteger bigInteger3 = eCFieldElement2.toBigInteger();
            BigInteger bigInteger4 = eCFieldElement3.toBigInteger();
            return new Fp(this.a, this.b, e(bigInteger.multiply(bigInteger2).add(bigInteger3.multiply(bigInteger4))));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement negate() {
            if (this.c.signum() == 0) {
                return this;
            }
            BigInteger bigInteger = this.a;
            return new Fp(bigInteger, this.b, bigInteger.subtract(this.c));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement sqrt() {
            if (isZero() || isOne()) {
                return this;
            }
            if (!this.a.testBit(0)) {
                throw new RuntimeException("not done yet");
            }
            if (this.a.testBit(1)) {
                BigInteger add = this.a.shiftRight(2).add(ECConstants.ONE);
                BigInteger bigInteger = this.a;
                return a(new Fp(bigInteger, this.b, this.c.modPow(add, bigInteger)));
            }
            if (this.a.testBit(2)) {
                BigInteger modPow = this.c.modPow(this.a.shiftRight(3), this.a);
                BigInteger b = b(modPow, this.c);
                if (b(b, modPow).equals(ECConstants.ONE)) {
                    return a(new Fp(this.a, this.b, b));
                }
                return a(new Fp(this.a, this.b, b(b, ECConstants.TWO.modPow(this.a.shiftRight(2), this.a))));
            }
            BigInteger shiftRight = this.a.shiftRight(1);
            BigInteger modPow2 = this.c.modPow(shiftRight, this.a);
            BigInteger bigInteger2 = ECConstants.ONE;
            if (!modPow2.equals(bigInteger2)) {
                return null;
            }
            BigInteger bigInteger3 = this.c;
            BigInteger b2 = b(b(bigInteger3));
            BigInteger add2 = shiftRight.add(bigInteger2);
            BigInteger subtract = this.a.subtract(bigInteger2);
            Random random = new Random();
            while (true) {
                BigInteger bigInteger4 = new BigInteger(this.a.bitLength(), random);
                if (bigInteger4.compareTo(this.a) < 0 && e(bigInteger4.multiply(bigInteger4).subtract(b2)).modPow(shiftRight, this.a).equals(subtract)) {
                    BigInteger[] a = a(bigInteger4, bigInteger3, add2);
                    BigInteger bigInteger5 = a[0];
                    BigInteger bigInteger6 = a[1];
                    if (b(bigInteger6, bigInteger6).equals(b2)) {
                        return new Fp(this.a, this.b, c(bigInteger6));
                    }
                    if (!bigInteger5.equals(ECConstants.ONE) && !bigInteger5.equals(subtract)) {
                        return null;
                    }
                }
            }
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement square() {
            BigInteger bigInteger = this.a;
            BigInteger bigInteger2 = this.b;
            BigInteger bigInteger3 = this.c;
            return new Fp(bigInteger, bigInteger2, b(bigInteger3, bigInteger3));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            BigInteger bigInteger = this.c;
            BigInteger bigInteger2 = eCFieldElement.toBigInteger();
            BigInteger bigInteger3 = eCFieldElement2.toBigInteger();
            return new Fp(this.a, this.b, e(bigInteger.multiply(bigInteger).subtract(bigInteger2.multiply(bigInteger3))));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            BigInteger bigInteger = this.c;
            BigInteger bigInteger2 = eCFieldElement.toBigInteger();
            BigInteger bigInteger3 = eCFieldElement2.toBigInteger();
            return new Fp(this.a, this.b, e(bigInteger.multiply(bigInteger).add(bigInteger2.multiply(bigInteger3))));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public ECFieldElement subtract(ECFieldElement eCFieldElement) {
            return new Fp(this.a, this.b, c(this.c, eCFieldElement.toBigInteger()));
        }

        @Override // bc.org.bouncycastle.math.ec.ECFieldElement
        public BigInteger toBigInteger() {
            return this.c;
        }

        protected BigInteger b(BigInteger bigInteger, BigInteger bigInteger2) {
            return e(bigInteger.multiply(bigInteger2));
        }

        protected BigInteger c(BigInteger bigInteger, BigInteger bigInteger2) {
            BigInteger subtract = bigInteger.subtract(bigInteger2);
            return subtract.signum() < 0 ? subtract.add(this.a) : subtract;
        }

        private ECFieldElement a(ECFieldElement eCFieldElement) {
            if (eCFieldElement.square().equals(this)) {
                return eCFieldElement;
            }
            return null;
        }

        private BigInteger[] a(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3) {
            int bitLength = bigInteger3.bitLength();
            int lowestSetBit = bigInteger3.getLowestSetBit();
            BigInteger bigInteger4 = ECConstants.ONE;
            BigInteger bigInteger5 = bigInteger;
            BigInteger bigInteger6 = bigInteger4;
            BigInteger bigInteger7 = ECConstants.TWO;
            BigInteger bigInteger8 = bigInteger6;
            for (int i = bitLength - 1; i >= lowestSetBit + 1; i--) {
                bigInteger8 = b(bigInteger8, bigInteger6);
                if (bigInteger3.testBit(i)) {
                    bigInteger6 = b(bigInteger8, bigInteger2);
                    bigInteger4 = b(bigInteger4, bigInteger5);
                    bigInteger7 = e(bigInteger5.multiply(bigInteger7).subtract(bigInteger.multiply(bigInteger8)));
                    bigInteger5 = e(bigInteger5.multiply(bigInteger5).subtract(bigInteger6.shiftLeft(1)));
                } else {
                    bigInteger4 = e(bigInteger4.multiply(bigInteger7).subtract(bigInteger8));
                    BigInteger e = e(bigInteger5.multiply(bigInteger7).subtract(bigInteger.multiply(bigInteger8)));
                    bigInteger7 = e(bigInteger7.multiply(bigInteger7).subtract(bigInteger8.shiftLeft(1)));
                    bigInteger5 = e;
                    bigInteger6 = bigInteger8;
                }
            }
            BigInteger b = b(bigInteger8, bigInteger6);
            BigInteger b2 = b(b, bigInteger2);
            BigInteger e2 = e(bigInteger4.multiply(bigInteger7).subtract(b));
            BigInteger e3 = e(bigInteger5.multiply(bigInteger7).subtract(bigInteger.multiply(b)));
            BigInteger b3 = b(b, b2);
            for (int i2 = 1; i2 <= lowestSetBit; i2++) {
                e2 = b(e2, e3);
                e3 = e(e3.multiply(e3).subtract(b3.shiftLeft(1)));
                b3 = b(b3, b3);
            }
            return new BigInteger[]{e2, e3};
        }

        protected BigInteger a(BigInteger bigInteger, BigInteger bigInteger2) {
            BigInteger add = bigInteger.add(bigInteger2);
            return add.compareTo(this.a) >= 0 ? add.subtract(this.a) : add;
        }
    }
}
