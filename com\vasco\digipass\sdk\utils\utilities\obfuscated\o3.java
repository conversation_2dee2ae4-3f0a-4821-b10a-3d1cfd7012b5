package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\o3.smali */
public class o3 {
    private int a;
    private byte[] b;
    private int c;

    public o3(byte[] bArr, int i) {
        this(bArr, i, -1);
    }

    public int a() {
        return this.c;
    }

    public byte[] b() {
        return Arrays.clone(this.b);
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof o3)) {
            return false;
        }
        o3 o3Var = (o3) obj;
        if (o3Var.c != this.c) {
            return false;
        }
        return Arrays.areEqual(this.b, o3Var.b);
    }

    public int hashCode() {
        return this.c ^ Arrays.hashCode(this.b);
    }

    public o3(byte[] bArr, int i, int i2) {
        this.b = Arrays.clone(bArr);
        this.c = i;
        this.a = i2;
    }
}
