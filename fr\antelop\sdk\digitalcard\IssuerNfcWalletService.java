package fr.antelop.sdk.digitalcard;

import android.app.Activity;
import fr.antelop.sdk.card.Card;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import o.er.m;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\IssuerNfcWalletService.smali */
public final class IssuerNfcWalletService {
    private final m innerIssuerNfcWalletService;

    public IssuerNfcWalletService(m mVar) {
        this.innerIssuerNfcWalletService = mVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerIssuerNfcWalletService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final Card getIssuerNfcCard() {
        return this.innerIssuerNfcWalletService.a();
    }

    public final boolean isCardInIssuerNfcWallet() {
        return this.innerIssuerNfcWalletService.e();
    }

    public final SecureCardPushToIssuerNfcWallet getSecureCardPush() throws WalletValidationException {
        return this.innerIssuerNfcWalletService.d();
    }

    public final void pushCard(Activity activity, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerIssuerNfcWalletService.c(activity, false, operationCallback);
    }

    public final void pushCard(Activity activity, boolean z, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerIssuerNfcWalletService.c(activity, z, operationCallback);
    }
}
