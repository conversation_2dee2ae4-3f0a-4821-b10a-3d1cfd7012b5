package com.getcapacitor;

import com.google.firebase.messaging.Constants;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\PluginResult.smali */
public class PluginResult {
    private final JSObject json;

    public PluginResult() {
        this(new JSObject());
    }

    public PluginResult(JSObject json) {
        this.json = json;
    }

    public PluginResult put(String name, boolean value) {
        return jsonPut(name, Boolean.valueOf(value));
    }

    public PluginResult put(String name, double value) {
        return jsonPut(name, Double.valueOf(value));
    }

    public PluginResult put(String name, int value) {
        return jsonPut(name, Integer.valueOf(value));
    }

    public PluginResult put(String name, long value) {
        return jsonPut(name, Long.valueOf(value));
    }

    public PluginResult put(String name, Date value) {
        TimeZone tz = TimeZone.getTimeZone("UTC");
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
        df.setTimeZone(tz);
        return jsonPut(name, df.format(value));
    }

    public PluginResult put(String name, Object value) {
        return jsonPut(name, value);
    }

    public PluginResult put(String name, PluginResult value) {
        return jsonPut(name, value.json);
    }

    PluginResult jsonPut(String name, Object value) {
        try {
            this.json.put(name, value);
        } catch (Exception ex) {
            Logger.error(Logger.tags("Plugin"), "", ex);
        }
        return this;
    }

    public String toString() {
        return this.json.toString();
    }

    public JSObject getWrappedResult() {
        JSObject ret = new JSObject();
        ret.put("pluginId", this.json.getString("pluginId"));
        ret.put("methodName", this.json.getString("methodName"));
        ret.put("success", (Object) this.json.getBoolean("success", false));
        ret.put(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, (Object) this.json.getJSObject(Constants.ScionAnalytics.MessageType.DATA_MESSAGE));
        ret.put(Constants.IPC_BUNDLE_KEY_SEND_ERROR, (Object) this.json.getJSObject(Constants.IPC_BUNDLE_KEY_SEND_ERROR));
        return ret;
    }
}
