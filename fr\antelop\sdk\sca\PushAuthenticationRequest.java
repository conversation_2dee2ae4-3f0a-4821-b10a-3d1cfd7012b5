package fr.antelop.sdk.sca;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Arrays;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.w.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\sca\PushAuthenticationRequest.smali */
public final class PushAuthenticationRequest implements CustomerAuthenticatedProcess {
    private final d inner;

    public PushAuthenticationRequest(d dVar) {
        this.inner = dVar;
    }

    public final void authenticate(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.inner.e(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.inner));
    }

    public final void authenticate(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.inner.e(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.inner));
    }

    public final void cancel(Context context) throws WalletValidationException {
        this.inner.d(context);
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.inner.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.inner.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.inner.o();
    }

    public final boolean isOnline() {
        return !this.inner.k();
    }

    public final byte[] getIssuerData() {
        return this.inner.u();
    }

    public final String getId() {
        return this.inner.w();
    }

    public final long getExpiryTimestamp() {
        return this.inner.A();
    }

    public final String getMessage() {
        return null;
    }

    public final String toString() {
        return new StringBuilder("PushAuthenticationRequest{id=").append(getId()).append(", customerAuthenticationPatternName=").append(getCustomerAuthenticationPatternName()).append(", issuerData=").append(Arrays.toString(getIssuerData())).append(", expiryTimestamp=").append(getExpiryTimestamp()).append('}').toString();
    }
}
