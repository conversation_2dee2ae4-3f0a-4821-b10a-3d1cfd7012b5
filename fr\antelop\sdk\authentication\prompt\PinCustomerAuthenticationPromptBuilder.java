package fr.antelop.sdk.authentication.prompt;

import fr.antelop.sdk.authentication.prompt.PinCustomerAuthenticationPrompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\PinCustomerAuthenticationPromptBuilder.smali */
public final class PinCustomerAuthenticationPromptBuilder extends CustomerAuthenticationPromptBuilder {
    private PinCustomerAuthenticationPrompt.InvalidPinMessageProvider invalidPinMessageProvider;
    private boolean pinCheckDisabled;
    private String subtitle;
    private String title;

    public final PinCustomerAuthenticationPromptBuilder setTitle(String str) {
        this.title = str;
        return this;
    }

    public final PinCustomerAuthenticationPromptBuilder setSubtitle(String str) {
        this.subtitle = str;
        return this;
    }

    public final PinCustomerAuthenticationPromptBuilder setInvalidPinMessageProvider(PinCustomerAuthenticationPrompt.InvalidPinMessageProvider invalidPinMessageProvider) {
        this.invalidPinMessageProvider = invalidPinMessageProvider;
        return this;
    }

    public final PinCustomerAuthenticationPromptBuilder setPinCheckDisabled(boolean z) {
        this.pinCheckDisabled = z;
        return this;
    }

    @Override // fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder
    public final PinCustomerAuthenticationPrompt build() {
        return new PinCustomerAuthenticationPrompt(this.title, this.subtitle, this.invalidPinMessageProvider, this.pinCheckDisabled);
    }
}
