package com.google.android.gms.internal.tapandpay;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import com.google.android.gms.common.api.Api;
import com.google.android.gms.common.api.GoogleApi;
import com.google.android.gms.common.api.internal.ListenerHolder;
import com.google.android.gms.common.api.internal.ListenerHolders;
import com.google.android.gms.common.api.internal.RegistrationMethods;
import com.google.android.gms.common.api.internal.RemoteCall;
import com.google.android.gms.common.api.internal.TaskApiCall;
import com.google.android.gms.common.api.internal.TaskUtil;
import com.google.android.gms.tapandpay.TapAndPay;
import com.google.android.gms.tapandpay.TapAndPayClient;
import com.google.android.gms.tapandpay.issuer.CreatePushProvisionSessionRequest;
import com.google.android.gms.tapandpay.issuer.IsTokenizedRequest;
import com.google.android.gms.tapandpay.issuer.PushProvisionSessionContext;
import com.google.android.gms.tapandpay.issuer.PushTokenizeRequest;
import com.google.android.gms.tapandpay.issuer.ServerPushProvisionRequest;
import com.google.android.gms.tapandpay.issuer.TokenInfo;
import com.google.android.gms.tapandpay.issuer.TokenStatus;
import com.google.android.gms.tapandpay.issuer.ViewTokenRequest;
import com.google.android.gms.tasks.Task;
import com.google.android.gms.tasks.TaskCompletionSource;
import java.util.List;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\tapandpay\zzag.smali */
public final class zzag extends GoogleApi implements TapAndPayClient {
    public static final /* synthetic */ int zza = 0;

    public zzag(Activity activity) {
        super(activity, (Api<Api.ApiOptions.NoOptions>) TapAndPay.zza, Api.ApiOptions.NO_OPTIONS, GoogleApi.Settings.DEFAULT_SETTINGS);
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<PushProvisionSessionContext> createPushProvisionSession(final CreatePushProvisionSessionRequest createPushProvisionSessionRequest) {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzl
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                zzag zzagVar = zzag.this;
                ((zzd) ((zzaj) obj).getService()).zzd(createPushProvisionSessionRequest, new zzz(zzagVar, (TaskCompletionSource) obj2));
            }
        }).setFeatures(com.google.android.gms.tapandpay.zza.zzo).setMethodKey(2107).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void createWallet(final Activity activity, final int i) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzg
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                Activity activity2 = activity;
                int i2 = i;
                int i3 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zze(new zzai(activity2, i2));
            }
        }).setMethodKey(2112).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<String> getActiveWalletId() {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzt
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ((zzd) ((zzaj) obj).getService()).zzf(new zzy(zzag.this, (TaskCompletionSource) obj2));
            }
        }).setMethodKey(2103).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<String> getEnvironment() {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzu
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ((zzd) ((zzaj) obj).getService()).zzg(new zzab(zzag.this, (TaskCompletionSource) obj2));
            }
        }).setMethodKey(2110).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<String> getLinkingToken(final String str) {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzj
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                zzag zzagVar = zzag.this;
                ((zzd) ((zzaj) obj).getService()).zzh(str, new zzac(zzagVar, (TaskCompletionSource) obj2));
            }
        }).setFeatures(com.google.android.gms.tapandpay.zza.zzb).setMethodKey(2111).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<String> getStableHardwareId() {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzp
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ((zzd) ((zzaj) obj).getService()).zzi(new zzaa(zzag.this, (TaskCompletionSource) obj2));
            }
        }).setMethodKey(2109).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<TokenStatus> getTokenStatus(final int i, final String str) {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzh
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                zzag zzagVar = zzag.this;
                ((zzd) ((zzaj) obj).getService()).zzj(i, str, new zzx(zzagVar, (TaskCompletionSource) obj2));
            }
        }).setMethodKey(2102).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<Boolean> isTokenized(final IsTokenizedRequest isTokenizedRequest) {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzi
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                zzag zzagVar = zzag.this;
                ((zzd) ((zzaj) obj).getService()).zzk(isTokenizedRequest, new zzae(zzagVar, (TaskCompletionSource) obj2));
            }
        }).setFeatures(com.google.android.gms.tapandpay.zza.zzz).setMethodKey(2164).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<List<TokenInfo>> listTokens() {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzn
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ((zzd) ((zzaj) obj).getService()).zzl(new zzad(zzag.this, (TaskCompletionSource) obj2));
            }
        }).setFeatures(com.google.android.gms.tapandpay.zza.zzz).setMethodKey(2163).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void pushTokenize(final Activity activity, final PushTokenizeRequest pushTokenizeRequest, final int i) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzq
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                PushTokenizeRequest pushTokenizeRequest2 = PushTokenizeRequest.this;
                Activity activity2 = activity;
                int i2 = i;
                int i3 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzm(pushTokenizeRequest2, new zzai(activity2, i2));
            }
        }).setMethodKey(2106).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<Void> registerDataChangedListener(TapAndPay.DataChangedListener dataChangedListener) {
        final ListenerHolder registerListener = registerListener(dataChangedListener, TapAndPayClient.DATA_CHANGED_LISTENER_KEY);
        return doRegisterEventListener(RegistrationMethods.builder().withHolder(registerListener).register(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzv
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ListenerHolder listenerHolder = ListenerHolder.this;
                int i = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzn(new com.google.android.gms.tapandpay.zzd(null, listenerHolder));
                ((TaskCompletionSource) obj2).setResult(null);
            }
        }).unregister(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzw
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                int i = zzag.zza;
            }
        }).setMethodKey(2155).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<Void> removeDataChangedListener(TapAndPay.DataChangedListener dataChangedListener) {
        return TaskUtil.toVoidTaskThatFailsOnFalse(doUnregisterEventListener(ListenerHolders.createListenerKey(dataChangedListener, TapAndPayClient.DATA_CHANGED_LISTENER_KEY), 2152));
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void requestDeleteToken(final Activity activity, final String str, final int i, final int i2) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzo
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                int i3 = i;
                String str2 = str;
                Activity activity2 = activity;
                int i4 = i2;
                int i5 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzo(i3, str2, new zzai(activity2, i4));
            }
        }).setMethodKey(2104).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void requestSelectToken(final Activity activity, final String str, final int i, final int i2) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzs
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                int i3 = i;
                String str2 = str;
                Activity activity2 = activity;
                int i4 = i2;
                int i5 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzp(i3, str2, new zzai(activity2, i4));
            }
        }).setMethodKey(2105).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void serverPushProvision(final Activity activity, final ServerPushProvisionRequest serverPushProvisionRequest, final int i) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzm
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                ServerPushProvisionRequest serverPushProvisionRequest2 = ServerPushProvisionRequest.this;
                Activity activity2 = activity;
                int i2 = i;
                int i3 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzq(serverPushProvisionRequest2, new zzai(activity2, i2));
            }
        }).setFeatures(com.google.android.gms.tapandpay.zza.zzo).setMethodKey(2108).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final void tokenize(final Activity activity, final String str, final int i, final String str2, final int i2, final int i3) {
        doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzr
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                int i4 = i;
                String str3 = str;
                String str4 = str2;
                int i5 = i2;
                Activity activity2 = activity;
                int i6 = i3;
                int i7 = zzag.zza;
                ((zzd) ((zzaj) obj).getService()).zzr(i4, str3, str4, i5, new zzai(activity2, i6));
            }
        }).setMethodKey(2101).build());
    }

    @Override // com.google.android.gms.tapandpay.TapAndPayClient
    public final Task<PendingIntent> viewToken(final ViewTokenRequest viewTokenRequest) {
        return doRead(TaskApiCall.builder().run(new RemoteCall() { // from class: com.google.android.gms.internal.tapandpay.zzk
            @Override // com.google.android.gms.common.api.internal.RemoteCall
            public final void accept(Object obj, Object obj2) {
                zzag zzagVar = zzag.this;
                ((zzd) ((zzaj) obj).getService()).zzs(viewTokenRequest, new zzaf(zzagVar, (TaskCompletionSource) obj2));
            }
        }).setMethodKey(2160).setFeatures(com.google.android.gms.tapandpay.zza.zzl).build());
    }

    public zzag(Context context) {
        super(context, (Api<Api.ApiOptions.NoOptions>) TapAndPay.zza, Api.ApiOptions.NO_OPTIONS, GoogleApi.Settings.DEFAULT_SETTINGS);
    }
}
