package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192R1Point.smali */
public class SecP192R1Point extends ECPoint.AbstractFp {
    SecP192R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP192R1FieldElement secP192R1FieldElement = (SecP192R1FieldElement) this.b;
        SecP192R1FieldElement secP192R1FieldElement2 = (SecP192R1FieldElement) this.c;
        SecP192R1FieldElement secP192R1FieldElement3 = (SecP192R1FieldElement) eCPoint.getXCoord();
        SecP192R1FieldElement secP192R1FieldElement4 = (SecP192R1FieldElement) eCPoint.getYCoord();
        SecP192R1FieldElement secP192R1FieldElement5 = (SecP192R1FieldElement) this.d[0];
        SecP192R1FieldElement secP192R1FieldElement6 = (SecP192R1FieldElement) eCPoint.getZCoord(0);
        int[] c = u5.c();
        int[] a = u5.a();
        int[] a2 = u5.a();
        int[] a3 = u5.a();
        boolean isOne = secP192R1FieldElement5.isOne();
        if (isOne) {
            iArr = secP192R1FieldElement3.a;
            iArr2 = secP192R1FieldElement4.a;
        } else {
            SecP192R1Field.square(secP192R1FieldElement5.a, a2);
            SecP192R1Field.multiply(a2, secP192R1FieldElement3.a, a);
            SecP192R1Field.multiply(a2, secP192R1FieldElement5.a, a2);
            SecP192R1Field.multiply(a2, secP192R1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP192R1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP192R1FieldElement.a;
            iArr4 = secP192R1FieldElement2.a;
        } else {
            SecP192R1Field.square(secP192R1FieldElement6.a, a3);
            SecP192R1Field.multiply(a3, secP192R1FieldElement.a, c);
            SecP192R1Field.multiply(a3, secP192R1FieldElement6.a, a3);
            SecP192R1Field.multiply(a3, secP192R1FieldElement2.a, a3);
            iArr3 = c;
            iArr4 = a3;
        }
        int[] a4 = u5.a();
        SecP192R1Field.subtract(iArr3, iArr, a4);
        SecP192R1Field.subtract(iArr4, iArr2, a);
        if (u5.b(a4)) {
            return u5.b(a) ? twice() : curve.getInfinity();
        }
        SecP192R1Field.square(a4, a2);
        int[] a5 = u5.a();
        SecP192R1Field.multiply(a2, a4, a5);
        SecP192R1Field.multiply(a2, iArr3, a2);
        SecP192R1Field.negate(a5, a5);
        u5.c(iArr4, a5, c);
        SecP192R1Field.reduce32(u5.b(a2, a2, a5), a5);
        SecP192R1FieldElement secP192R1FieldElement7 = new SecP192R1FieldElement(a3);
        SecP192R1Field.square(a, secP192R1FieldElement7.a);
        int[] iArr5 = secP192R1FieldElement7.a;
        SecP192R1Field.subtract(iArr5, a5, iArr5);
        SecP192R1FieldElement secP192R1FieldElement8 = new SecP192R1FieldElement(a5);
        SecP192R1Field.subtract(a2, secP192R1FieldElement7.a, secP192R1FieldElement8.a);
        SecP192R1Field.multiplyAddToExt(secP192R1FieldElement8.a, a, c);
        SecP192R1Field.reduce(c, secP192R1FieldElement8.a);
        SecP192R1FieldElement secP192R1FieldElement9 = new SecP192R1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP192R1FieldElement9.a;
            SecP192R1Field.multiply(iArr6, secP192R1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP192R1FieldElement9.a;
            SecP192R1Field.multiply(iArr7, secP192R1FieldElement6.a, iArr7);
        }
        return new SecP192R1Point(curve, secP192R1FieldElement7, secP192R1FieldElement8, new ECFieldElement[]{secP192R1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP192R1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP192R1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP192R1FieldElement secP192R1FieldElement = (SecP192R1FieldElement) this.c;
        if (secP192R1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP192R1FieldElement secP192R1FieldElement2 = (SecP192R1FieldElement) this.b;
        SecP192R1FieldElement secP192R1FieldElement3 = (SecP192R1FieldElement) this.d[0];
        int[] a = u5.a();
        int[] a2 = u5.a();
        int[] a3 = u5.a();
        SecP192R1Field.square(secP192R1FieldElement.a, a3);
        int[] a4 = u5.a();
        SecP192R1Field.square(a3, a4);
        boolean isOne = secP192R1FieldElement3.isOne();
        int[] iArr = secP192R1FieldElement3.a;
        if (!isOne) {
            SecP192R1Field.square(iArr, a2);
            iArr = a2;
        }
        SecP192R1Field.subtract(secP192R1FieldElement2.a, iArr, a);
        SecP192R1Field.add(secP192R1FieldElement2.a, iArr, a2);
        SecP192R1Field.multiply(a2, a, a2);
        SecP192R1Field.reduce32(u5.b(a2, a2, a2), a2);
        SecP192R1Field.multiply(a3, secP192R1FieldElement2.a, a3);
        SecP192R1Field.reduce32(c6.c(6, a3, 2, 0), a3);
        SecP192R1Field.reduce32(c6.a(6, a4, 3, 0, a), a);
        SecP192R1FieldElement secP192R1FieldElement4 = new SecP192R1FieldElement(a4);
        SecP192R1Field.square(a2, secP192R1FieldElement4.a);
        int[] iArr2 = secP192R1FieldElement4.a;
        SecP192R1Field.subtract(iArr2, a3, iArr2);
        int[] iArr3 = secP192R1FieldElement4.a;
        SecP192R1Field.subtract(iArr3, a3, iArr3);
        SecP192R1FieldElement secP192R1FieldElement5 = new SecP192R1FieldElement(a3);
        SecP192R1Field.subtract(a3, secP192R1FieldElement4.a, secP192R1FieldElement5.a);
        int[] iArr4 = secP192R1FieldElement5.a;
        SecP192R1Field.multiply(iArr4, a2, iArr4);
        int[] iArr5 = secP192R1FieldElement5.a;
        SecP192R1Field.subtract(iArr5, a, iArr5);
        SecP192R1FieldElement secP192R1FieldElement6 = new SecP192R1FieldElement(a2);
        SecP192R1Field.twice(secP192R1FieldElement.a, secP192R1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP192R1FieldElement6.a;
            SecP192R1Field.multiply(iArr6, secP192R1FieldElement3.a, iArr6);
        }
        return new SecP192R1Point(curve, secP192R1FieldElement4, secP192R1FieldElement5, new ECFieldElement[]{secP192R1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP192R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
