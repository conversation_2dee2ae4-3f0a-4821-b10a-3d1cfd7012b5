package com.google.android.gms.internal.fido;

/* compiled from: com.google.android.gms:play-services-fido@@20.0.1 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\fido\zzh.smali */
public final class zzh {
    public static final zzbj zza = zzbk.zzb("id");
    public static final zzbj zzb = zzbk.zzb("type");
    public static final zzbj zzc = zzbk.zzb("transports");
    public static final zzbj zzd = zzbk.zzb("name");
    public static final zzbj zze = zzbk.zzb("icon");
    public static final zzbj zzf = zzbk.zzb("displayName");
    public static final zzbj zzg = zzbk.zzb("alg");
    public static final zzbj zzh = zzbk.zzb("plat");
    public static final zzbj zzi = zzbk.zzb("rk");
    public static final zzbj zzj = zzbk.zzb("clientPin");
    public static final zzbj zzk = zzbk.zzb("up");
    public static final zzbj zzl = zzbk.zzb("uv");
    public static final zzbj zzm = zzbk.zzb("alwaysUv");
    public static final zzbj zzn = zzbk.zzb("credMgmt");
    public static final zzbj zzo = zzbk.zzb("authnrCfg");
    public static final zzbj zzp = zzbk.zzb("bioEnroll");
    public static final zzbj zzq = zzbk.zzb("largeBlobs");
    public static final zzbj zzr = zzbk.zzb("pinUvAuthToken");
    public static final zzbj zzs = zzbk.zzb("noMcGaPermissionsWithClientPin");
    public static final zzbj zzt = zzbk.zzb("ep");
    public static final zzbj zzu = zzbk.zzb("uvBioEnroll");
    public static final zzbj zzv = zzbk.zzb("uvAcfg");
    public static final zzbj zzw = zzbk.zzb("setMinPINLength");
    public static final zzbj zzx = zzbk.zzb("makeCredUvNotRqd");
    public static final zzbj zzy = zzbk.zzb("credentialMgmtPreview");
    public static final zzbj zzz = zzbk.zzb("userVerificationMgmtPreview");
    public static final zzbj zzA = zzbk.zzb("uvm");
    public static final zzbj zzB = zzbk.zzb("multiAssertion");
    public static final zzbj zzC = zzbk.zzb("sessionId");
    public static final zzbj zzD = zzbk.zzb("google_userVerificationOrigin");
}
