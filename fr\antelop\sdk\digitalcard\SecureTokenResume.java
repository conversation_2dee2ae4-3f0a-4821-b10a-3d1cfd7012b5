package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.k;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureTokenResume.smali */
public final class SecureTokenResume implements CustomerAuthenticatedProcess {
    private final k innerTokenResumeProcess;

    public SecureTokenResume(k kVar) {
        this.innerTokenResumeProcess = kVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerTokenResumeProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerTokenResumeProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerTokenResumeProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerTokenResumeProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenResumeProcess.a(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerTokenResumeProcess));
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerTokenResumeProcess.a(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerTokenResumeProcess));
    }
}
