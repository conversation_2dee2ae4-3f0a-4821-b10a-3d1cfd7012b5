package o.ei;

import android.content.Context;
import android.content.SharedPreferences;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ei\g.smali */
public final class g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int c;
    private static long d;

    static void d() {
        a = new char[]{50844, 50795, 50789, 50784, 50793, 50795, 50814, 50815, 50787, 50787, 50771, 50777, 50785, 50787, 50774, 50772, 50788, 50935, 50849, 50831, 50923, 50923, 50941, 50941, 50826, 50852, 50851, 50822, 50932, 50857, 50876, 50877, 50849, 50849, 50823, 50943, 50847, 50855, 50849, 50831, 50825, 50857, 50855, 50820, 50828, 50862, 50856, 50852, 50826, 50826, 50851, 50876, 50851, 50858, 50858, 50858, 50857, 50831, 50912, 50912, 50821, 50876, 50877, 50852, 50857, 50849, 50878, 50862, 50857, 50876, 50877, 50849, 50849, 50833, 50847, 50937, 50858, 50858, 50851, 50876, 50851, 50826, 50826, 50852, 50856, 50862, 50828, 50817, 50851, 50863, 50862, 50854, 50850, 50859, 50829, 50820, 50855, 50857, 50825, 50831, 50849, 50855, 50847, 50943, 50823, 50849, 50849, 50877, 50876, 50857, 50932, 50923, 50923, 50831, 50849, 50855, 50847, 50833, 50849, 50849, 50877, 50876, 50857, 50862, 50878, 50849, 50857, 50852, 50877, 50876, 50855, 50857, 50739, 50741, 50726, 50805, 50737, 50749, 50736, 50722, 50720, 50741, 50722, 50737, 50722, 50749, 50722, 50737, 50743, 50724, 50723, 50722, 50737, 50726, 50751, 50736, 50724, 51150, 50737, 50726, 50737, 50721, 50750, 50747, 50722, 50720, 50751, 50743, 50740, 50747, 50722, 50739, 50741, 50726, 50805, 50743, 50740, 50747, 50722, 50932, 50854, 50853, 50816, 50845, 50854};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r0 = o.ei.g.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r7 = r7 + 66
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1a:
            r3 = r2
            r5 = r8
            r8 = r7
        L1d:
            r7 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = -r6
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.g.g(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{8, 72, -108, -33};
        $$b = 236;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        d();
        d = 0L;
        int i = b + Opcodes.LMUL;
        c = i % 128;
        switch (i % 2 != 0 ? 'G' : 'U') {
            case 'G':
                int i2 = 51 / 0;
                return;
            default:
                return;
        }
    }

    public static boolean c(Context context, long j) {
        switch (j == -1 ? (char) 20 : ')') {
            case ')':
                if (j != d(context)) {
                    d(context, j);
                    Date date = new Date(j * 1000);
                    o.ee.g.c();
                    Object[] objArr = new Object[1];
                    f("\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001", new int[]{0, 17, 66, 4}, false, objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    f("\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{17, 58, 0, 43}, false, objArr2);
                    o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(date).toString());
                    return true;
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f("\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001", new int[]{0, 17, 66, 4}, false, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001", new int[]{75, 57, 0, 55}, true, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                int i = b + 99;
                c = i % 128;
                int i2 = i % 2;
                return false;
            default:
                int i3 = b + Opcodes.LSHL;
                c = i3 % 128;
                int i4 = i3 % 2;
                return false;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0013. Please report as an issue. */
    public static void c() {
        int i = c + 109;
        b = i % 128;
        switch (i % 2 == 0 ? 'E' : '^') {
        }
        d = 0L;
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x001f, code lost:
    
        if (r5 == 0) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static long d(android.content.Context r7) {
        /*
            int r0 = o.ei.g.b
            int r0 = r0 + 21
            int r1 = r0 % 128
            o.ei.g.c = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            r3 = 0
            long r5 = o.ei.g.d
            switch(r0) {
                case 0: goto L1d;
                default: goto L18;
            }
        L18:
            int r0 = (r5 > r3 ? 1 : (r5 == r3 ? 0 : -1))
            if (r0 != 0) goto L24
            goto L22
        L1d:
            int r0 = (r5 > r3 ? 1 : (r5 == r3 ? 0 : -1))
            if (r0 != 0) goto L6e
        L21:
            goto L2a
        L22:
            r0 = 4
            goto L26
        L24:
            r0 = 35
        L26:
            switch(r0) {
                case 35: goto L6e;
                default: goto L29;
            }
        L29:
            goto L21
        L2a:
            r0 = 47
            r3 = 138(0x8a, float:1.93E-43)
            r4 = 132(0x84, float:1.85E-43)
            r5 = 6
            int[] r0 = new int[]{r4, r0, r3, r5}
            java.lang.Object[] r3 = new java.lang.Object[r1]
            r4 = 0
            f(r4, r0, r1, r3)
            r0 = r3[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            android.content.SharedPreferences r7 = r7.getSharedPreferences(r0, r2)
            r0 = 179(0xb3, float:2.51E-43)
            int[] r0 = new int[]{r0, r5, r2, r5}
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r3 = "\u0001\u0001\u0001\u0001\u0001\u0001"
            f(r3, r0, r2, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r1 = -1
            long r0 = r7.getLong(r0, r1)
            o.ei.g.d = r0
            int r7 = o.ei.g.b
            int r7 = r7 + 119
            int r0 = r7 % 128
            o.ei.g.c = r0
            int r7 = r7 % 2
        L6e:
            long r0 = o.ei.g.d
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.g.d(android.content.Context):long");
    }

    private static void d(Context context, long j) {
        SharedPreferences.Editor edit;
        Object obj;
        int i = c + 61;
        b = i % 128;
        Object obj2 = null;
        switch (i % 2 == 0 ? '8' : ',') {
            case ',':
                d = j;
                Object[] objArr = new Object[1];
                f(null, new int[]{Opcodes.IINC, 47, Opcodes.L2D, 6}, true, objArr);
                edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
                Object[] objArr2 = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{Opcodes.PUTSTATIC, 6, 0, 6}, false, objArr2);
                obj = objArr2[0];
                break;
            default:
                d = j;
                Object[] objArr3 = new Object[1];
                f(null, new int[]{Opcodes.IINC, 47, Opcodes.L2D, 6}, true, objArr3);
                edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
                Object[] objArr4 = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{Opcodes.PUTSTATIC, 6, 0, 6}, true, objArr4);
                obj = objArr4[0];
                break;
        }
        edit.putLong(((String) obj).intern(), j).commit();
        int i2 = c + Opcodes.DREM;
        b = i2 % 128;
        switch (i2 % 2 == 0 ? Typography.greater : '!') {
            case '>':
                obj2.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:107:0x0101, code lost:
    
        if (r0[r1.d] == 1) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x034f, code lost:
    
        r2 = r0;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v27, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 946
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.g.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
