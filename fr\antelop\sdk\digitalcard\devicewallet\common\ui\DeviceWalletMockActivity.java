package fr.antelop.sdk.digitalcard.devicewallet.common.ui;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Bundle;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.SetAsDefaultFragment;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels.ManageGooglePayMockViewModel;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\DeviceWalletMockActivity.smali */
public final class DeviceWalletMockActivity extends AppCompatActivity {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10 = 0;
    private static int $11 = 0;
    public static final String ADD_ACTION = "ADD";
    public static final String DEVICE_WALLET_BUNDLE_KEY = "DEVICE_WALLET_BUNDLE";
    public static final String GOOGLE_PAY = "GOOGLE_PAY";
    public static final String LAST_DIGITS_BUNDLE_KEY = "LAST_DIGITS";
    public static final String LOGO_RESOURCE_KEY = "LOGO_RESOURCE";
    public static final String SAMSUNG_PAY = "SAMSUNG_PAY";
    public static final String SET_DEFAULT_ACTION = "SET_DEFAULT";
    public static final String WIPE_BUTTON_STRING_RESOURCE_KEY = "WIPE_BUTTON_STRING_RESOURCE";
    public static final String WIPE_DATA_DESCRIPTION_RESOURCE_KEY = "WIPE_DATA_DESCRIPTION_RESOURCE";
    private static int a;
    private static long b;
    private static char c;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        c = (char) 17957;
        a = -887724157;
        b = 6565854932352255525L;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Type inference failed for: r8v1, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = 106 - r8
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity.$$a
            int r6 = r6 * 2
            int r6 = 3 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r4 = r8
            r3 = r2
            r8 = r7
            r7 = r6
            goto L30
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r6 = r6 + 1
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r4 = -r4
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity.g(short, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 88;
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected final void onCreate(Bundle bundle) {
        Fragment manageDeviceWalletMockFragment;
        String str;
        super.onCreate(bundle);
        new ViewModelProvider(this).get(ManageGooglePayMockViewModel.class);
        setContentView(R.layout.device_wallet_mock_activity_layout);
        String action = getIntent().getAction();
        if (!ADD_ACTION.equals(action)) {
            switch (SET_DEFAULT_ACTION.equals(action) ? (char) 24 : (char) 23) {
                case 23:
                    manageDeviceWalletMockFragment = new ManageDeviceWalletMockFragment();
                    manageDeviceWalletMockFragment.setArguments(getFragmentBundle());
                    break;
                default:
                    int i = e + 77;
                    d = i % 128;
                    if (i % 2 == 0) {
                    }
                    String string = getIntent().getExtras().getString(DEVICE_WALLET_BUNDLE_KEY);
                    Object[] objArr = new Object[1];
                    f(KeyEvent.keyCodeFromString(""), "㕇鈦᧐\u0012\ue5b9諝梸萨햳阜", (char) (TextUtils.lastIndexOf("", '0') + 1), "朄䀺ሁ㸿", "\u0000\u0000\u0000\u0000", objArr);
                    if (((String) objArr[0]).intern().equals(string)) {
                        Object[] objArr2 = new Object[1];
                        f((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1, "㕇鈦᧐\u0012\ue5b9諝梸萨햳阜", (char) View.resolveSize(0, 0), "朄䀺ሁ㸿", "\u0000\u0000\u0000\u0000", objArr2);
                        str = ((String) objArr2[0]).intern();
                    } else {
                        str = "";
                    }
                    Object[] objArr3 = new Object[1];
                    f((-**********) - TextUtils.getOffsetAfter("", 0), "㧩늩魐\ue0c3끐兔첒闡뗰Ｍ䑾", (char) (ImageFormat.getBitsPerPixel(0) + 1), "짏햝᪵싱", "\u0000\u0000\u0000\u0000", objArr3);
                    switch (!((String) objArr3[0]).intern().equals(string)) {
                        case true:
                            break;
                        default:
                            Object[] objArr4 = new Object[1];
                            f((ViewConfiguration.getKeyRepeatDelay() >> 16) - **********, "㧩늩魐\ue0c3끐兔첒闡뗰Ｍ䑾", (char) Color.green(0), "짏햝᪵싱", "\u0000\u0000\u0000\u0000", objArr4);
                            str = ((String) objArr4[0]).intern();
                            break;
                    }
                    manageDeviceWalletMockFragment = SetAsDefaultFragment.newInstance(str);
                    int i2 = e + 21;
                    d = i2 % 128;
                    int i3 = i2 % 2;
                    break;
            }
        } else {
            new ViewModelProvider(this).get(ManageGooglePayMockViewModel.class);
            manageDeviceWalletMockFragment = new AddGooglePayFragment();
        }
        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, manageDeviceWalletMockFragment).commit();
    }

    public static Intent createGooglePayIntent(Context context) {
        Intent intent = new Intent(context, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr = new Object[1];
        f(ViewConfiguration.getJumpTapTimeout() >> 16, "㕇鈦᧐\u0012\ue5b9諝梸萨햳阜", (char) View.getDefaultSize(0, 0), "朄䀺ሁ㸿", "\u0000\u0000\u0000\u0000", objArr);
        Intent putExtra = intent.putExtra(DEVICE_WALLET_BUNDLE_KEY, ((String) objArr[0]).intern());
        int i = d + 21;
        e = i % 128;
        int i2 = i % 2;
        return putExtra;
    }

    public static Intent createSamsungPayIntent(Context context) {
        Intent intent = new Intent(context, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr = new Object[1];
        f((-1244291638) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "㧩늩魐\ue0c3끐兔첒闡뗰Ｍ䑾", (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), "짏햝᪵싱", "\u0000\u0000\u0000\u0000", objArr);
        Intent putExtra = intent.putExtra(DEVICE_WALLET_BUNDLE_KEY, ((String) objArr[0]).intern());
        int i = d + 49;
        e = i % 128;
        switch (i % 2 != 0 ? Typography.amp : 'P') {
            case '&':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return putExtra;
        }
    }

    private Bundle getFragmentBundle() {
        String str;
        int i = e + Opcodes.LSUB;
        d = i % 128;
        int i2 = i % 2;
        Bundle extras = getIntent().getExtras();
        switch (extras == null) {
            case true:
                str = null;
                break;
            default:
                str = extras.getString(DEVICE_WALLET_BUNDLE_KEY);
                break;
        }
        Object[] objArr = new Object[1];
        f(TextUtils.indexOf("", ""), "㕇鈦᧐\u0012\ue5b9諝梸萨햳阜", (char) (KeyEvent.getMaxKeyCode() >> 16), "朄䀺ሁ㸿", "\u0000\u0000\u0000\u0000", objArr);
        if (!((String) objArr[0]).intern().equals(str)) {
            Object[] objArr2 = new Object[1];
            f((-**********) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), "㧩늩魐\ue0c3끐兔첒闡뗰Ｍ䑾", (char) KeyEvent.normalizeMetaState(0), "짏햝᪵싱", "\u0000\u0000\u0000\u0000", objArr2);
            if (!((String) objArr2[0]).intern().equals(str)) {
                throw new IllegalArgumentException("you should pass SAMSUNG_PAY or GOOGLE_PAY as DEVICE_WALLET_BUNDLE argument");
            }
        }
        Object[] objArr3 = new Object[1];
        f(ExpandableListView.getPackedPositionType(0L), "㕇鈦᧐\u0012\ue5b9諝梸萨햳阜", (char) View.MeasureSpec.getMode(0), "朄䀺ሁ㸿", "\u0000\u0000\u0000\u0000", objArr3);
        switch (((String) objArr3[0]).intern().equals(str) ? (char) 11 : ':') {
            case Opcodes.ASTORE /* 58 */:
                break;
            default:
                int i3 = e + 55;
                d = i3 % 128;
                int i4 = i3 % 2;
                extras.putInt(LOGO_RESOURCE_KEY, R.drawable.antelop_google_pay_logo);
                extras.putInt(WIPE_BUTTON_STRING_RESOURCE_KEY, R.string.antelopWipeGooglePayDataTextButton);
                extras.putInt(WIPE_DATA_DESCRIPTION_RESOURCE_KEY, R.string.antelopWipeGooglePayDataTextDescription);
                break;
        }
        Object[] objArr4 = new Object[1];
        f((-**********) - Gravity.getAbsoluteGravity(0, 0), "㧩늩魐\ue0c3끐兔첒闡뗰Ｍ䑾", (char) Color.red(0), "짏햝᪵싱", "\u0000\u0000\u0000\u0000", objArr4);
        switch (((String) objArr4[0]).intern().equals(str)) {
            default:
                int i5 = d + 53;
                e = i5 % 128;
                if (i5 % 2 != 0) {
                }
                extras.putInt(LOGO_RESOURCE_KEY, R.drawable.antelop_samsung_pay_logo);
                extras.putInt(WIPE_BUTTON_STRING_RESOURCE_KEY, R.string.antelopWipeSamsungPayDataTextButton);
                extras.putInt(WIPE_DATA_DESCRIPTION_RESOURCE_KEY, R.string.antelopWipeSamsungPayDataTextDescription);
            case false:
                return extras;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0010, code lost:
    
        if (r22 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001d, code lost:
    
        r0 = r22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0018, code lost:
    
        r0 = r22.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x0016, code lost:
    
        if (r22 != null) goto L12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r18, java.lang.String r19, char r20, java.lang.String r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 682
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
