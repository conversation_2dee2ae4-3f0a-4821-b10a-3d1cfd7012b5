package o.fn;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\c.smali */
abstract class c<T> {
    private static int c = 0;
    private static int e = 1;
    private final boolean d;

    c(boolean z) {
        this.d = z;
    }

    final boolean d() {
        int i = c;
        int i2 = i + Opcodes.LSHL;
        e = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.d;
        int i4 = ((i | Opcodes.DNEG) << 1) - (i ^ Opcodes.DNEG);
        e = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 14 : (char) 3) {
            case 14:
                throw null;
            default:
                return z;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0023, code lost:
    
        r4 = o.fn.c.c + 77;
        o.fn.c.e = r4 % 128;
        r4 = r4 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x002d, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0020, code lost:
    
        if (r0 != false) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x001a, code lost:
    
        if (r0 != false) goto L15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    final void d(java.lang.String r4) throws o.ei.j {
        /*
            r3 = this;
            int r0 = o.fn.c.c
            int r0 = r0 + 46
            r1 = 1
            int r0 = r0 - r1
            int r2 = r0 % 128
            o.fn.c.e = r2
            int r0 = r0 % 2
            r2 = 0
            if (r0 != 0) goto L11
            r1 = r2
            goto L12
        L11:
        L12:
            boolean r0 = r3.d()
            switch(r1) {
                case 1: goto L1a;
                default: goto L19;
            }
        L19:
            goto L1d
        L1a:
            if (r0 == 0) goto L2e
        L1c:
            goto L23
        L1d:
            r1 = 59
            int r1 = r1 / r2
            if (r0 == 0) goto L2e
            goto L1c
        L23:
            int r4 = o.fn.c.c
            int r4 = r4 + 77
            int r0 = r4 % 128
            o.fn.c.e = r0
            int r4 = r4 % 2
            return
        L2e:
            o.ei.j r0 = new o.ei.j
            r0.<init>(r4)
            throw r0
        L34:
            r4 = move-exception
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.c.d(java.lang.String):void");
    }
}
