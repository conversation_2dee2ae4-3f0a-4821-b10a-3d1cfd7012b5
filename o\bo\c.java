package o.bo;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import com.huawei.hms.aaid.HmsInstanceId;
import com.huawei.hms.common.ApiException;
import com.huawei.hms.push.RemoteMessage;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Objects;
import kotlin.text.Typography;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\c.smali */
public final class c extends o.bs.e<o.bs.a> implements o.bo.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char d;
    private static char e;
    private static char f;
    private static int g;
    private static long h;
    private static int i;
    private static char j;
    private static int k;
    private final o.bi.d a;
    private final b c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        k = 1;
        c();
        ViewConfiguration.getMaximumDrawingCacheSize();
        Process.myPid();
        ViewConfiguration.getDoubleTapTimeout();
        Process.getThreadPriority(0);
        int i2 = i + Opcodes.LUSHR;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    static void c() {
        d = (char) 30927;
        e = (char) 52119;
        f = (char) 65194;
        b = (char) 35430;
        j = (char) 17957;
        g = 161105445;
        h = -429116638882988626L;
    }

    static void init$0() {
        $$a = new byte[]{37, -65, 15, -4};
        $$b = 39;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r6 = r6 + 99
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r0 = o.bo.c.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = r8 + 1
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.c.n(byte, byte, short, java.lang.Object[]):void");
    }

    public c(Context context, o.bs.a aVar, o.bi.d dVar) {
        super(aVar);
        this.a = dVar;
        this.c = b(context);
    }

    @Override // o.bo.b
    public final boolean d(i iVar) {
        int i2 = k + 47;
        i = i2 % 128;
        int i3 = i2 % 2;
        switch (!Objects.equals(this.a.d(), o.e((CharSequence) ((RemoteMessage) iVar.e()).getFrom()))) {
            case true:
                o.ee.g.c();
                Object[] objArr = new Object[1];
                l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", 27 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l("췬䝚殊ਲ䚯鹈≝⏹菢ṣ痣\uf047䆀챧菢ṣ䖹鶊纫\u1fd5＾膃閩‛뙣뢘\uf54b渑箅冞嗼ⲃ닄鹕ㆮ偯靻\u1756䟨컩賒ꇆ\ue04b휽Ḥꦙ＾膃閩‛㊚⩾ᆍ6硣ᾟ㮔㜥⒭燗\ue04b휽䖹鶊\ue04b휽錿쎜‟밓똑貓糬\ud973吊ꀹ\ue4bf囜箅冞혎㯰ᔕ욘ଳ\uf57b\ue703뉋", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 87, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                int i4 = i + 19;
                k = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return false;
                }
            default:
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", View.getDefaultSize(0, 0) + 27, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                l("췬䝚殊ਲ䚯鹈≝⏹菢ṣ痣\uf047䆀챧菢ṣ䖹鶊纫\u1fd5＾膃閩‛뙣뢘\uf54b渑箅冞嗼ⲃ닄鹕ㆮ偯靻\u1756䟨컩賒ꇆ\ue04b휽Ḥꦙ＾膃閩‛㊚⩾ᆍ6⒭燗\ue04b휽䖹鶊\ue04b휽錿쎜‟밓똑貓糬\ud973吊ꀹ\ue4bf囜箅冞혎㯰ᔕ욘ଳ\uf57b\ue703뉋", TextUtils.getTrimmedLength("") + 84, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                return true;
        }
    }

    public static i a(RemoteMessage remoteMessage) {
        o.eg.b bVar;
        Iterator<String> a;
        int i2 = k + 97;
        i = i2 % 128;
        Object obj = null;
        if (i2 % 2 != 0) {
            remoteMessage.getData();
            obj.hashCode();
            throw null;
        }
        switch (remoteMessage.getData() != null ? (char) 4 : Typography.amp) {
            case '&':
                break;
            default:
                if (!remoteMessage.getData().isEmpty()) {
                    HashMap hashMap = new HashMap();
                    try {
                        bVar = new o.eg.b(o.e((CharSequence) remoteMessage.getData()));
                        a = bVar.a();
                    } catch (o.eg.d e2) {
                        o.ee.g.c();
                        Object[] objArr = new Object[1];
                        l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", 27 - Color.alpha(0), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        l("\uda3d\ue977棍䢃\udfd7篩츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1⚗ᯌㆮ偯\u2073㥜壦弓謶Ჱ㬸Ö\uf5df㦺⚗ᯌ㋖\uf709苋㮯㕄ꇟ닄鹕ဿ\ue8ea䖹鶊\ue4bf囜箅冞ꕓ縩\ue3c4믋", 51 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr2);
                        o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
                        return null;
                    }
                    while (a.hasNext()) {
                        int i3 = i + 27;
                        k = i3 % 128;
                        switch (i3 % 2 == 0) {
                            case false:
                                String next = a.next();
                                switch (!bVar.a(next) ? (char) 24 : '6') {
                                    case Opcodes.ISTORE /* 54 */:
                                        break;
                                    default:
                                        hashMap.put(next, bVar.e(next).toString());
                                        break;
                                }
                            default:
                                bVar.a(a.next());
                                throw null;
                        }
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", 27 - Color.alpha(0), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr22 = new Object[1];
                        l("\uda3d\ue977棍䢃\udfd7篩츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1⚗ᯌㆮ偯\u2073㥜壦弓謶Ჱ㬸Ö\uf5df㦺⚗ᯌ㋖\uf709苋㮯㕄ꇟ닄鹕ဿ\ue8ea䖹鶊\ue4bf囜箅冞ꕓ縩\ue3c4믋", 51 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr22);
                        o.ee.g.a(intern2, ((String) objArr22[0]).intern(), e2);
                        return null;
                    }
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", ((Process.getThreadPriority(0) + 20) >> 6) + 27, objArr4);
                    String intern3 = ((String) objArr4[0]).intern();
                    Object[] objArr5 = new Object[1];
                    m(TextUtils.getTrimmedLength(""), "9\uda1e\ued97뙱鐵鲣ꥑ퇭펭䉂⬈\ue622嚄ᕆ懤\ue472\ude0e\uf5cc\ue000౿㵨쇡ꁼ銁硚䪰ߥ蹙僟㻝궷峓\uecad龪㙳賀홅熣待땼䑲㗵꿠쉥㑎ꀄ編碮", (char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), "雅㝅솶⎻", "㮋춱\ue2f6ꄕ", objArr5);
                    o.ee.g.d(intern3, ((String) objArr5[0]).intern());
                    return new i(hashMap, remoteMessage);
                }
                break;
        }
        o.ee.g.c();
        Object[] objArr6 = new Object[1];
        l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 26, objArr6);
        String intern4 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        m((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "㲯鋪Џ\uda23喙鹎Ꮏ評햢⣨쏁㥰ო쀂닸魒ﱋ\ua95a凛鎍嵣瞔﹉姂럑퐪醈ﻭ櫇栋椦ᾦᰋ见餂䨴☂ᯧ洼돧뤢簗竌\udbc9\ue2d2\u20f9שׂᯓྗ龤\ueeb8װ", (char) (MotionEvent.axisFromString("") + 37599), "㘜饲\udec0ꦒ", "㮋춱\ue2f6ꄕ", objArr7);
        o.ee.g.e(intern4, ((String) objArr7[0]).intern());
        return null;
    }

    @Override // o.bo.b
    public final String d(Context context) throws g {
        int i2 = i + 47;
        k = i2 % 128;
        Object obj = null;
        if (i2 % 2 == 0) {
            e(context);
            obj.hashCode();
            throw null;
        }
        if (!e(context)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 27, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("榗ْ歺呻㋖\uf709榗ْ芙摿ᡱ衈坱⊺㋖\uf709쳛륆닄鹕ㆮ偯\ude50랐젵틄⫛⋬ߪ\ue039ḷ\uf07e\ue88b謁\ue04b휽Ḥꦙ㆓︐瑃⾸୷\ue067ᜒ꽤츒֍\uf5df㦺苋㮯\ue88b謁㛮\uf260\ued89犽", (ViewConfiguration.getEdgeSlop() >> 16) + 58, objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            m(Color.red(0), "촢\uebc1ྲ1暈柀汣ㄱ盒ຍ꺈ش襦秛鶶ﻇ緑\u0b11멵倫휬꿂눇䋤豀\ueca9徜曆ꋣ䄒湸굺ꈕ蓰ꘌ\ud821", (char) (ViewConfiguration.getWindowTouchSlop() >> 8), "謼\ue4f6䎔Ⰺ", "㮋춱\ue2f6ꄕ", objArr3);
            throw new g(((String) objArr3[0]).intern());
        }
        try {
            String id = this.c.a.getId();
            int i3 = i + 5;
            k = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    switch (id != null ? '2' : '1') {
                        case '2':
                            return o.e((CharSequence) id);
                        default:
                            Object[] objArr4 = new Object[1];
                            l("䆀챧崎⧰츒֍풟璉୷\ue067첬\u197b滈휕謶Ჱ㿥͂㊚⩾怈\u1bf8壦弓홐紐\ue04b휽檭茖", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 30, objArr4);
                            throw new g(((String) objArr4[0]).intern());
                    }
                default:
                    throw null;
            }
        } catch (IllegalArgumentException e2) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", ExpandableListView.getPackedPositionChild(0L) + 28, objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            l("榗ْ歺呻㋖\uf709榗ْ芙摿ᡱ衈坱⊺㋖\uf709쳛륆닄鹕ㆮ偯㋖\uf709苋㮯㕄ꇟ닄鹕쮔늌ᆍ6錿쎜‟밓똑貓糬\ud973ᗊ呐칊\uffc1홡Ľ", (Process.myPid() >> 22) + 47, objArr6);
            o.ee.g.a(intern2, ((String) objArr6[0]).intern(), e2);
            Object[] objArr7 = new Object[1];
            m((KeyEvent.getMaxKeyCode() >> 16) - 1166744024, "㥄\ue4c3梄貨ࡑ暾㯮ቚ痂ꉹꦂ弤\udb7b\uefa9愒来黅抦빡藆톻쟥\ud8a2䠺ꐧ", (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), "⠶瓦뎺\uf3ca", "㮋춱\ue2f6ꄕ", objArr7);
            throw new g(((String) objArr7[0]).intern());
        }
    }

    @Override // o.bo.b
    public final String a() {
        Object obj;
        int i2 = i + 39;
        k = i2 % 128;
        char c = i2 % 2 == 0 ? 'H' : (char) 2;
        long elapsedCpuTime = Process.getElapsedCpuTime();
        switch (c) {
            case 'H':
                Object[] objArr = new Object[1];
                l("襺\ue37eힴ忶", 2 << (elapsedCpuTime > 1L ? 1 : (elapsedCpuTime == 1L ? 0 : -1)), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                l("襺\ue37eힴ忶", 4 - (elapsedCpuTime > 0L ? 1 : (elapsedCpuTime == 0L ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = i + 9;
        k = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.bo.b
    public final String a(Context context) throws g {
        String token;
        HmsInstanceId hmsInstanceId;
        String a;
        String intern;
        Object[] objArr = new Object[1];
        l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", 'K' - AndroidCharacter.getMirror('0'), objArr);
        String intern2 = ((String) objArr[0]).intern();
        if (!e(context)) {
            o.ee.g.c();
            Object[] objArr2 = new Object[1];
            l("૦낵ڥ㺗騰✬䀲澂\ue04d넒怺\udb71謶Ჱዞ≼哔聲½\udde8ʥ⠲\uef36阻\ued89犽ଳ\uf57b閩‛\ueba1⡴﹖矜ᆍ6硣ᾟ㮔㜥\uf7b7₄딮Ɂꝕ롥䮢\ue353蒘㾍", 49 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr2);
            o.ee.g.e(intern2, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            m((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "촢\uebc1ྲ1暈柀汣ㄱ盒ຍ꺈ش襦秛鶶ﻇ緑\u0b11멵倫휬꿂눇䋤豀\ueca9徜曆ꋣ䄒湸굺ꈕ蓰ꘌ\ud821", (char) TextUtils.getTrimmedLength(""), "謼\ue4f6䎔Ⰺ", "㮋춱\ue2f6ꄕ", objArr3);
            throw new g(((String) objArr3[0]).intern());
        }
        try {
            if (this.c.d) {
                int i2 = k + Opcodes.DSUB;
                i = i2 % 128;
                switch (i2 % 2 != 0 ? 'c' : (char) 19) {
                    case Opcodes.DADD /* 99 */:
                        hmsInstanceId = this.c.a;
                        a = this.a.a();
                        Object[] objArr4 = new Object[1];
                        l("큨燎献⼴", 3 >>> View.MeasureSpec.getMode(1), objArr4);
                        intern = ((String) objArr4[0]).intern();
                        break;
                    default:
                        hmsInstanceId = this.c.a;
                        a = this.a.a();
                        Object[] objArr5 = new Object[1];
                        l("큨燎献⼴", 3 - View.MeasureSpec.getMode(0), objArr5);
                        intern = ((String) objArr5[0]).intern();
                        break;
                }
                token = hmsInstanceId.getToken(a, intern);
            } else {
                token = this.c.a.getToken(this.a.d());
            }
            if (token == null) {
                o.ee.g.c();
                Object[] objArr6 = new Object[1];
                l("૦낵ڥ㺗騰✬䀲澂\ue04d넒怺\udb71혎㯰ᔕ욘혎㯰䀲澂\ue04d넒\ue4bf囜\ufae8箻繯鋛吊ꀹ㊚⩾ᆍ6\ue7aa\ue9d2\uf74aृ", 37 - Process.getGidForName(""), objArr6);
                o.ee.g.e(intern2, ((String) objArr6[0]).intern());
                Object[] objArr7 = new Object[1];
                m(Color.green(0), "\uf1db寉暽ꣳ㮼掣蒊♯\u087d냝䡱\udd93鳿箷蹷핓먭ዌ썂﨏䑰ꑮ꽪\ueae8", (char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), "끖塖\u2e70\uea03", "㮋춱\ue2f6ꄕ", objArr7);
                throw new g(((String) objArr7[0]).intern());
            }
            o.ee.g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr8 = new Object[1];
            l("૦낵ڥ㺗騰✬䀲澂\ue04d넒怺\udb71혎㯰䀲澂\ue04d넒\ue4bf囜\ufae8箻繯鋛吊ꀹ躧䔒粦趃", View.getDefaultSize(0, 0) + 29, objArr8);
            o.ee.g.d(intern2, sb.append(((String) objArr8[0]).intern()).append(token).toString());
            String e2 = o.e((CharSequence) token);
            int i3 = k + 3;
            i = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return e2;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (ApiException e3) {
            o.ee.g.c();
            Object[] objArr9 = new Object[1];
            l("૦낵ڥ㺗騰✬䀲澂\ue04d넒怺\udb71쥧⠺樔朕쵲귌﹖矜\uec22ꉾ칊\uffc1홡Ľ", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 26, objArr9);
            o.ee.g.a(intern2, ((String) objArr9[0]).intern(), e3);
            o.ee.g.c();
            Object[] objArr10 = new Object[1];
            l("૦낵ڥ㺗騰✬䀲澂\ue04d넒怺\udb71ᜒ꽤ғ每퀫잫슧\uf0b3⚗ᯌ૦낵ڥ㺗ᔕ욘籝㙠ֺ㐏이䛍硣ᾟ\ued35腢\ue4bf囜딮Ɂ䴡洤퇴뒕풟璉㐪쓖ꕞ䑥홡Ľ", 'e' - AndroidCharacter.getMirror('0'), objArr10);
            o.ee.g.e(intern2, ((String) objArr10[0]).intern());
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr11 = new Object[1];
            l("呚ᢿ½\udde8㌾Ư﹖矜\uec22ꉾ칊\uffc1⚗ᯌ㨥Ӧ", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 16, objArr11);
            throw new g(sb2.append(((String) objArr11[0]).intern()).append(e3.getMessage()).toString());
        }
    }

    @Override // o.bo.b
    public final String e() {
        Object obj;
        int i2 = i + 57;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                l("쮔늌ಇꛡ", 3 >>> KeyEvent.getDeadChar(1, 1), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                l("쮔늌ಇꛡ", 3 - KeyEvent.getDeadChar(0, 0), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = k + 79;
        i = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    private b b(Context context) {
        int i2 = k + 97;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? '\f' : (char) 26) {
            case '\f':
                this.a.a();
                String e2 = this.a.e();
                this.a.d();
                e2.isEmpty();
                throw null;
            default:
                String a = this.a.a();
                String e3 = this.a.e();
                String d2 = this.a.d();
                switch (!e3.isEmpty() ? 'b' : ']') {
                    case Opcodes.DUP2_X1 /* 93 */:
                        break;
                    default:
                        int i3 = k + 45;
                        i = i3 % 128;
                        int i4 = i3 % 2;
                        if (!a.isEmpty()) {
                            b bVar = new b(context, e3, d2);
                            o.ee.g.c();
                            Object[] objArr = new Object[1];
                            l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", View.MeasureSpec.makeMeasureSpec(0, 0) + 27, objArr);
                            String intern = ((String) objArr[0]).intern();
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr2 = new Object[1];
                            m(View.getDefaultSize(0, 0), "֛좶쾖\uda10⩅杊\uf5f2랽뾥諭널栨㡈墹ᡔ귏좐„᳝왦娯弨㱐龍릮ḹ젹搓㯬ꋹ㴇\ud7fd⻲還ኯ䪏琀㔀폶鯧\ud922簥⹉᷌儩節\ufb37\udff8裲햷綴֩岥\uf71b䊪酕㬋ϸ帍둪䂯\uf7f7\ue31e\ue3b3点\uf309⢬\ue5d3⠟텙퀂锨", (char) ExpandableListView.getPackedPositionType(0L), "䅢\uec2b㻴㯄", "㮋춱\ue2f6ꄕ", objArr2);
                            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar.d).toString());
                            int i5 = k + 45;
                            i = i5 % 128;
                            int i6 = i5 % 2;
                            return bVar;
                        }
                        break;
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                l("එ◞勊谍崷鉳\uf23c༲츒֍뜃Ꞁ瑃⾸ᗊ呐칊\uffc1\uda2a遢䋀ଡ଼\ueba1⡴ꑞ\ue221䥅㱫", 27 - View.combineMeasuredStates(0, 0), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                l("榗ْꑶꞏ崷鉳퀫잫\uef16掊㿥͂\u0a46홗樆ⓨ曊ﾃ﹖矜⧶\ue20a怺\udb71謶Ჱ㿥͂试虳䖹鶊痣\uf047？\u05eb葲\ue18b菢ṣ䖹鶊ᜒ꽤츒֍ଳ\uf57b\ufae8箻", 50 - View.resolveSize(0, 0), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                Object[] objArr5 = new Object[1];
                m((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1166744025, "㥄\ue4c3梄貨ࡑ暾㯮ቚ痂ꉹꦂ弤\udb7b\uefa9愒来黅抦빡藆톻쟥\ud8a2䠺ꐧ", (char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), "⠶瓦뎺\uf3ca", "㮋춱\ue2f6ꄕ", objArr5);
                throw new IllegalArgumentException(((String) objArr5[0]).intern());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\c$b.smali */
    static final class b {
        final HmsInstanceId a;
        final boolean d;

        b(Context context, String str, String str2) {
            this.a = HmsInstanceId.getInstance(context);
            this.d = str2.equals(str);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 560
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.c.l(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 720
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.c.m(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
