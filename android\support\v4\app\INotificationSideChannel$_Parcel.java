package android.support.v4.app;

import android.os.Parcel;
import android.os.Parcelable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\android\support\v4\app\INotificationSideChannel$_Parcel.smali */
public class INotificationSideChannel$_Parcel {
    /* JADX INFO: Access modifiers changed from: private */
    public static <T> T readTypedObject(Parcel parcel, Parcelable.Creator<T> c) {
        if (parcel.readInt() != 0) {
            return c.createFromParcel(parcel);
        }
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static <T extends Parcelable> void writeTypedObject(Parcel parcel, T value, int parcelableFlags) {
        if (value != null) {
            parcel.writeInt(1);
            value.writeToParcel(parcel, parcelableFlags);
        } else {
            parcel.writeInt(0);
        }
    }
}
