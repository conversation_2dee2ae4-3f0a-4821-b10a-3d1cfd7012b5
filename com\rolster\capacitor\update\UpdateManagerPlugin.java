package com.rolster.capacitor.update;

import android.content.Context;
import android.content.pm.PackageManager;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "UpdateManager")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\rolster\capacitor\update\UpdateManagerPlugin.smali */
public class UpdateManagerPlugin extends Plugin {
    private UpdateManagerResolver updateManagerResolver;

    @Override // com.getcapacitor.Plugin
    public void load() {
        try {
            this.updateManagerResolver = (UpdateManagerResolver) Class.forName("com.rolster.capacitor.update.google.GoogleUpdateManagerResolver").getConstructor(Context.class).newInstance(getContext());
        } catch (Exception e) {
            throw new RuntimeException("Error init UpdateManagerResolver", e);
        }
    }

    @PluginMethod
    public void verifyStatus(PluginCall call) {
        this.updateManagerResolver.execute(call, getNumberApp());
    }

    private int getNumberApp() {
        try {
            PackageManager packageManager = getContext().getPackageManager();
            String packageName = getContext().getPackageName();
            return packageManager.getPackageInfo(packageName, 0).versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            return 0;
        }
    }
}
