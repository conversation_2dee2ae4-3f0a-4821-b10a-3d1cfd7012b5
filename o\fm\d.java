package o.fm;

import android.graphics.Color;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import o.ee.o;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fm\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long c;
    private static int j;
    private boolean d = false;
    private boolean e = false;
    private final HashMap<f, Boolean> b = new HashMap<>();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        j = 1;
        a();
        KeyEvent.getModifierMetaStateMask();
        View.resolveSize(0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        MotionEvent.axisFromString("");
        ViewConfiguration.getEdgeSlop();
        Color.blue(0);
        int i = j + 65;
        a = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 12 / 0;
                break;
        }
    }

    static void a() {
        c = -1289455857296375605L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r8, short r9, int r10, java.lang.Object[] r11) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 4
            int r9 = r9 * 4
            int r9 = r9 + 1
            byte[] r0 = o.fm.d.$$a
            int r10 = r10 * 3
            int r10 = r10 + 68
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r11
            r11 = r9
            r9 = r8
            goto L39
        L1a:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r10
            r10 = r6
        L1f:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r10) goto L2e
            java.lang.String r8 = new java.lang.String
            r8.<init>(r1, r2)
            r11[r2] = r8
            return
        L2e:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r6
            r7 = r11
            r11 = r10
            r10 = r3
            r3 = r1
            r1 = r0
            r0 = r7
        L39:
            int r8 = r8 + 1
            int r9 = r9 + r10
            r10 = r11
            r11 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.d.g(byte, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{24, -81, 39, 82};
        $$b = 90;
    }

    /* JADX WARN: Code restructure failed: missing block: B:24:0x0073, code lost:
    
        if (r11 != null) goto L13;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0045, code lost:
    
        if (r11 != null) goto L13;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x011e, code lost:
    
        r1 = new java.lang.Object[1];
        f("륐盖뤽ᅹ뚧เ鄥且륯뛖鄡亼릂똇釗仹맒랷透伏롵럱週佖뢳뜝邯侬룑띐錃䰽묙뒲鍈䱟믰됬鎤䲐믬", android.view.ViewConfiguration.getTouchSlop() >> 8, r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0138, code lost:
    
        throw new o.eg.d(((java.lang.String) r1[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(o.eg.b r11) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 326
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.d.d(o.eg.b):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private void b(o.eg.b r12) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 294
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.d.b(o.eg.b):void");
    }

    private void c(o.eg.b bVar) throws o.eg.d {
        int i = a + 5;
        j = i % 128;
        switch (i % 2 == 0 ? (char) 6 : 'b') {
            case Opcodes.FADD /* 98 */:
                Iterator<String> a2 = bVar.a();
                while (true) {
                    switch (a2.hasNext() ? '7' : (char) 14) {
                        case '7':
                            int i2 = a + 1;
                            j = i2 % 128;
                            int i3 = i2 % 2;
                            String next = a2.next();
                            this.b.put((f) o.c(f.class, next), bVar.g(next));
                            int i4 = a + 25;
                            j = i4 % 128;
                            int i5 = i4 % 2;
                        default:
                            return;
                    }
                }
            default:
                bVar.a();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    final o.eg.b e() throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Iterator<Map.Entry<f, Boolean>> it = this.b.entrySet().iterator();
        while (true) {
            switch (it.hasNext() ? '0' : (char) 24) {
                case '0':
                    int i = j + 3;
                    a = i % 128;
                    int i2 = i % 2;
                    Map.Entry<f, Boolean> next = it.next();
                    bVar.d(next.getKey().toString(), next.getValue());
                    int i3 = a + 75;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                default:
                    Object[] objArr = new Object[1];
                    f("\uf89b漕\uf8ed왮꽤簑䘴㱞\uf8a2꼞䙸", ExpandableListView.getPackedPositionGroup(0L), objArr);
                    bVar.d(((String) objArr[0]).intern(), 3);
                    return bVar;
            }
        }
    }

    public final void d() {
        int i = j + 73;
        a = i % 128;
        int i2 = i % 2;
        this.b.clear();
        int i3 = j + Opcodes.LUSHR;
        a = i3 % 128;
        int i4 = i3 % 2;
    }

    final boolean c() {
        int i = j + 97;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        boolean z = this.e;
        int i4 = i2 + 75;
        j = i4 % 128;
        switch (i4 % 2 == 0 ? '`' : (char) 20) {
            case Opcodes.IADD /* 96 */:
                throw null;
            default:
                return z;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    final void b() {
        int i = a;
        int i2 = i + 37;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
        }
        this.e = false;
        int i3 = i + Opcodes.DDIV;
        j = i3 % 128;
        int i4 = i3 % 2;
    }

    public final boolean a(f fVar) {
        int i = j + 41;
        a = i % 128;
        int i2 = i % 2;
        Boolean bool = this.b.get(fVar);
        switch (bool != null ? '-' : 'F') {
            case '-':
                return bool.booleanValue();
            default:
                switch (this.d ? 'M' : (char) 19) {
                    case 19:
                        return false;
                    default:
                        int i3 = a + 43;
                        j = i3 % 128;
                        if (i3 % 2 == 0) {
                        }
                        a(fVar, true);
                        int i4 = a + 5;
                        j = i4 % 128;
                        if (i4 % 2 != 0) {
                            return true;
                        }
                        int i5 = 8 / 0;
                        return true;
                }
        }
    }

    public final void a(f fVar, boolean z) {
        int i = a + 31;
        j = i % 128;
        int i2 = i % 2;
        this.e = true;
        this.b.put(fVar, Boolean.valueOf(z));
        int i3 = a + 85;
        j = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 350
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.d.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
