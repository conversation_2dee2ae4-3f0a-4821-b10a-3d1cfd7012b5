package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.util.Pool;
import java.lang.ref.SoftReference;
import java.util.ArrayDeque;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Predicate;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Pool.smali */
public abstract class Pool<T> {
    private final Queue<T> freeObjects;
    private int peak;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Pool$Poolable.smali */
    public interface Poolable {
        void reset();
    }

    protected abstract T create();

    public Pool(boolean threadSafe, boolean softReferences) {
        this(threadSafe, softReferences, Integer.MAX_VALUE);
    }

    public Pool(boolean threadSafe, boolean softReferences, final int maximumCapacity) {
        Queue<T> queue;
        if (threadSafe) {
            queue = new LinkedBlockingQueue<T>(maximumCapacity) { // from class: com.esotericsoftware.kryo.util.Pool.1
                @Override // java.util.AbstractQueue, java.util.AbstractCollection, java.util.Collection, java.util.Queue, java.util.concurrent.BlockingQueue
                public boolean add(T o2) {
                    return super.offer(o2);
                }
            };
        } else if (softReferences) {
            queue = new LinkedList<T>() { // from class: com.esotericsoftware.kryo.util.Pool.2
                @Override // java.util.LinkedList, java.util.AbstractList, java.util.AbstractCollection, java.util.Collection, java.util.List, java.util.Deque, java.util.Queue
                public boolean add(T object) {
                    if (size() >= maximumCapacity) {
                        return false;
                    }
                    super.add(object);
                    return true;
                }
            };
        } else {
            queue = new ArrayDeque<T>() { // from class: com.esotericsoftware.kryo.util.Pool.3
                @Override // java.util.ArrayDeque, java.util.Deque, java.util.Queue
                public boolean offer(T object) {
                    if (size() >= maximumCapacity) {
                        return false;
                    }
                    super.offer(object);
                    return true;
                }
            };
        }
        this.freeObjects = softReferences ? new SoftReferenceQueue<>(queue) : queue;
    }

    public T obtain() {
        T object = this.freeObjects.poll();
        return object != null ? object : create();
    }

    public void free(T object) {
        if (object == null) {
            throw new IllegalArgumentException("object cannot be null.");
        }
        reset(object);
        if (!this.freeObjects.offer(object)) {
            Queue<T> queue = this.freeObjects;
            if (queue instanceof SoftReferenceQueue) {
                ((SoftReferenceQueue) queue).cleanOne();
                this.freeObjects.offer(object);
            }
        }
        this.peak = Math.max(this.peak, this.freeObjects.size());
    }

    protected void reset(T object) {
        if (object instanceof Poolable) {
            ((Poolable) object).reset();
        }
    }

    public void clear() {
        this.freeObjects.clear();
    }

    public void clean() {
        Queue<T> queue = this.freeObjects;
        if (queue instanceof SoftReferenceQueue) {
            ((SoftReferenceQueue) queue).clean();
        }
    }

    public int getFree() {
        return this.freeObjects.size();
    }

    public int getPeak() {
        return this.peak;
    }

    public void resetPeak() {
        this.peak = 0;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Pool$SoftReferenceQueue.smali */
    static class SoftReferenceQueue<T> implements Queue<T> {
        private final Queue<SoftReference<T>> delegate;

        public SoftReferenceQueue(Queue<SoftReference<T>> delegate) {
            this.delegate = delegate;
        }

        @Override // java.util.Queue
        public T poll() {
            T object;
            do {
                SoftReference<T> reference = this.delegate.poll();
                if (reference == null) {
                    return null;
                }
                object = reference.get();
            } while (object == null);
            return object;
        }

        @Override // java.util.Queue
        public boolean offer(T e) {
            return this.delegate.add(new SoftReference<>(e));
        }

        @Override // java.util.Collection
        public int size() {
            return this.delegate.size();
        }

        @Override // java.util.Collection
        public void clear() {
            this.delegate.clear();
        }

        void cleanOne() {
            Iterator<SoftReference<T>> iter = this.delegate.iterator();
            while (iter.hasNext()) {
                if (iter.next().get() == null) {
                    iter.remove();
                    return;
                }
            }
        }

        static /* synthetic */ boolean lambda$clean$0(SoftReference o2) {
            return o2.get() == null;
        }

        void clean() {
            this.delegate.removeIf(new Predicate() { // from class: com.esotericsoftware.kryo.util.Pool$SoftReferenceQueue$$ExternalSyntheticLambda0
                @Override // java.util.function.Predicate
                public final boolean test(Object obj) {
                    return Pool.SoftReferenceQueue.lambda$clean$0((SoftReference) obj);
                }
            });
        }

        @Override // java.util.Queue, java.util.Collection
        public boolean add(T e) {
            return false;
        }

        @Override // java.util.Collection
        public boolean isEmpty() {
            return false;
        }

        @Override // java.util.Collection
        public boolean contains(Object o2) {
            return false;
        }

        @Override // java.util.Collection, java.lang.Iterable
        public Iterator<T> iterator() {
            return null;
        }

        @Override // java.util.Queue
        public T remove() {
            return null;
        }

        @Override // java.util.Collection
        public Object[] toArray() {
            return null;
        }

        @Override // java.util.Queue
        public T element() {
            return null;
        }

        @Override // java.util.Queue
        public T peek() {
            return null;
        }

        @Override // java.util.Collection
        public <E> E[] toArray(E[] a) {
            return null;
        }

        @Override // java.util.Collection
        public boolean remove(Object o2) {
            return false;
        }

        @Override // java.util.Collection
        public boolean containsAll(Collection c) {
            return false;
        }

        @Override // java.util.Collection
        public boolean addAll(Collection<? extends T> c) {
            return false;
        }

        @Override // java.util.Collection
        public boolean removeAll(Collection c) {
            return false;
        }

        @Override // java.util.Collection
        public boolean retainAll(Collection c) {
            return false;
        }
    }
}
