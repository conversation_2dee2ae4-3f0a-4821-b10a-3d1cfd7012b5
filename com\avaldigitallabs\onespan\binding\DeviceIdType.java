package com.avaldigitallabs.onespan.binding;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\avaldigitallabs\onespan\binding\DeviceIdType.smali */
public enum DeviceIdType {
    ANDROID_ID(0),
    SERIAL_NUMBER(1),
    ANDROID_ID_AND_SERIAL_NUMBER(2);

    private Integer code;

    public Integer getCode() {
        return this.code;
    }

    DeviceIdType(Integer code) {
        this.code = code;
    }

    public static DeviceIdType getByCode(String code) {
        for (DeviceIdType element : values()) {
            if (element.getCode().equals(Integer.valueOf(code))) {
                return element;
            }
        }
        throw new IllegalArgumentException();
    }
}
