package kotlin.math;

import kotlin.Metadata;

/* compiled from: MathH.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\"\u0016\u0010\u0000\u001a\u00020\u00018\u0006X\u0087T¢\u0006\b\n\u0000\u0012\u0004\b\u0002\u0010\u0003\"\u0016\u0010\u0004\u001a\u00020\u00018\u0006X\u0087T¢\u0006\b\n\u0000\u0012\u0004\b\u0005\u0010\u0003¨\u0006\u0006"}, d2 = {"E", "", "getE$annotations", "()V", "PI", "getPI$annotations", "kotlin-stdlib"}, k = 5, mv = {1, 9, 0}, xi = 49, xs = "kotlin/math/MathKt")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\math\MathKt__MathHKt.smali */
class MathKt__MathHKt {
    public static /* synthetic */ void getE$annotations() {
    }

    public static /* synthetic */ void getPI$annotations() {
    }
}
