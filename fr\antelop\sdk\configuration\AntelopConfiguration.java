package fr.antelop.sdk.configuration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\configuration\AntelopConfiguration.smali */
public final class AntelopConfiguration {
    private final String applicationId;
    private final String issuerId;

    public AntelopConfiguration(Long l, String str) {
        if (l != null) {
            this.applicationId = String.valueOf(l);
        } else {
            this.applicationId = null;
        }
        this.issuerId = str;
    }

    public AntelopConfiguration(String str, String str2) {
        this.applicationId = str;
        this.issuerId = str2;
    }

    public final String getApplicationId() {
        return this.applicationId;
    }

    public final String getIssuerId() {
        return this.issuerId;
    }
}
