package o.dj;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.util.concurrent.CountDownLatch;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.ee.g;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final e c;
    private static int f;
    private static byte[] g;
    private static int h;
    private static short[] i;
    private static int j;
    private static int k;

    /* renamed from: o, reason: collision with root package name */
    private static int f55o;
    private boolean a;
    private CountDownLatch b;
    private final Object d = new Object();
    private boolean e = false;

    static void i() {
        g = new byte[]{-103, -91, 89, -114, 120, -91, -82, -117, 117, 82, -85, 88, PSSSigner.TRAILER_IMPLICIT, -81, 67, -88, -96, 66, -77, -94, 100, -104, 57, 114, -117, -119, 105, -44, 113, -113, 48, 125, -124, 119, -109, ByteCompanionObject.MIN_VALUE, 108, -121, -113, 109, -100, 99, 124, -124, -124, 113, -109, 113, -96, 92, -127, -85, 96, 125, 113, -117, 118, -122, -114, -111, 102, 125, -124, 119, -111, 124, 120, 119, -124, -117, ByteCompanionObject.MAX_VALUE, 118, 125, -74, 76, -88, 87, -115, 112, -39, -109, -104, -99, -35, 41, 100, -99, 110, -118, -103, 117, -98, -106, 116, 101, -49, 34, 98, -98, 101, -109, 111, -104, -111, -39, 125, -127, 39, 100, 104, -110, 111, -97, -105, 104, -55, 54, -111, -46, 41, 100, -99, 110, -120, 101, 97, 110, -99, -110, 102, 111, 100, -113, -38, 41, -74, -70, 104, -106, 55, -103, -118, 118, -70, 126, 100, -99, 110, -118, -103, 117, -98, -106, 116, -123, 125, -124, ByteCompanionObject.MAX_VALUE, -109, 111, -104, -111, -35, -86, -95, -92, -28, Tnaf.POW_2_WIDTH, 93, -92, 87, -77, -96, 76, -89, -81, 77, 92, -10, 27, 91, -89, 92, -86, 86, -95, -88, -32, 14, -89, -93, -20, 68, -72, 30, 93, 81, -85, 86, -90, -82, 81, -16, 15, -88, -21, Tnaf.POW_2_WIDTH, 93, -92, 87, -79, 92, 88, 87, -92, -85, 95, 86, 93, -74, -29, 19, -125, -116, 81, -81, 14, -96, -77, 79, -125, 71, 93, -92, 87, -77, -96, 76, -89, -81, 77, PSSSigner.TRAILER_IMPLICIT, 68, -67, 70, -86, 86, -95, -88, -99, 87, 68, -72, 116, UtilitiesSDKConstants.SRP_LABEL_ENC, -86, 83, -96, 68, 87, -69, 80, 88, -70, 75, -77, 74, -79, 93, -95, 86, 95, -89, -104, 97, -110, 116, -103, -99, -110, 97, 110, -102, -109, -104, 115, 38, -43, -104, 40, -50, 111, 100, 97, 33, -36, -107, 117, -101, -98, 96, -108, 53, -108, 106, -37, -104, -97, -100, 100, 110, -109, 106, 114, -123, -104, 97, -110, 116, -103, -99, -110, 97, 110, -102, -109, -104, 83, -76, 120, -115, -107, 117, -101, -98, 96, -108, -91, 60, 57, -50, 54, -59, 120, -119, -60, Base64.padSymbol, -50, 42, 57, -43, 62, 54, -44, -59, 111, -119, -60, 116, -110, 51, 56, Base64.padSymbol, 125, ByteCompanionObject.MIN_VALUE, -55, 41, -57, -62, 60, -56, 105, -56, 54, -113, 60, 57, -50, 54, 37, -18, -60, Base64.padSymbol, -50, 42, 57, -43, 62, 54, -44, 37, -35, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -47, -55, 41, -57, -62, 60, -56, -65, ByteCompanionObject.MIN_VALUE, ByteCompanionObject.MAX_VALUE, 51, -60, -119, 112, -125, 103, 116, -104, 115, 123, -103, -120, 34, -60, -119, 57, -33, 126, 117, 112, 48, -51, -124, 100, -118, -113, 113, -123, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -123, 123, -54, ByteCompanionObject.MIN_VALUE, 95, -95, -119, 112, -125, 103, 116, -104, 115, 123, -103, 104, -112, 105, -100, -124, 100, -118, -113, 113, -123};
        f = 909053594;
        h = -556025766;
        j = -1106658641;
    }

    static void init$0() {
        $$a = new byte[]{0, -16, -96, 75};
        $$b = Opcodes.L2I;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r6v11 */
    /* JADX WARN: Type inference failed for: r6v12 */
    /* JADX WARN: Type inference failed for: r6v3 */
    /* JADX WARN: Type inference failed for: r6v4 */
    /* JADX WARN: Type inference failed for: r6v8, types: [int] */
    private static void m(byte b, short s, int i2, Object[] objArr) {
        int i3 = (b * 3) + 4;
        int i4 = 1 - (i2 * 2);
        int i5 = (s * 2) + 108;
        byte[] bArr = $$a;
        byte[] bArr2 = new byte[i4];
        int i6 = -1;
        int i7 = i4 - 1;
        ?? r6 = i5;
        if (bArr == null) {
            i3++;
            r6 = i3 + i5;
        }
        while (true) {
            byte b2 = r6;
            int i8 = i3;
            i6++;
            bArr2[i6] = b2;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            } else {
                i3 = i8 + 1;
                r6 = b2 + bArr[i8];
            }
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f55o = 0;
        k = 1;
        i();
        KeyEvent.keyCodeFromString("");
        KeyEvent.normalizeMetaState(0);
        ViewConfiguration.getScrollFriction();
        TextUtils.getOffsetBefore("", 0);
        Color.green(0);
        c = new e();
        int i2 = k + 89;
        f55o = i2 % 128;
        switch (i2 % 2 != 0 ? '5' : ':') {
            case Opcodes.SALOAD /* 53 */:
                throw null;
            default:
                return;
        }
    }

    public static synchronized e a() {
        e eVar;
        synchronized (e.class) {
            int i2 = f55o + 23;
            int i3 = i2 % 128;
            k = i3;
            int i4 = i2 % 2;
            eVar = c;
            int i5 = i3 + 85;
            f55o = i5 % 128;
            switch (i5 % 2 != 0 ? '=' : (char) 27) {
                case 27:
                    break;
                default:
                    throw null;
            }
        }
        return eVar;
    }

    private e() {
    }

    final void c(boolean z) {
        synchronized (this.d) {
            g.c();
            Object[] objArr = new Object[1];
            l((byte) (60 - Process.getGidForName("")), 2010729409 - (ViewConfiguration.getFadingEdgeLength() >> 16), (short) (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getMaximumFlingVelocity() >> 16) - 11, 386615691 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            l((byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 18), 2010729428 - Color.argb(0, 0, 0, 0), (short) TextUtils.getCapsMode("", 0, 0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 12, 386615721 - ((Process.getThreadPriority(0) + 20) >> 6), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z).toString());
            this.e = z;
        }
    }

    final boolean b() {
        boolean z;
        this.a = true;
        synchronized (this.d) {
            if (this.e) {
                g.c();
                Object[] objArr = new Object[1];
                l((byte) (61 - TextUtils.getOffsetAfter("", 0)), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 2010729409, (short) View.MeasureSpec.makeMeasureSpec(0, 0), (-11) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 386615689, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l((byte) (11 - (ViewConfiguration.getJumpTapTimeout() >> 16)), 2010729488 - TextUtils.indexOf("", ""), (short) (TextUtils.lastIndexOf("", '0') + 1), (-12) - TextUtils.lastIndexOf("", '0', 0), Drawable.resolveOpacity(0, 0) + 386615704, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.e = false;
                CountDownLatch countDownLatch = new CountDownLatch(1);
                this.b = countDownLatch;
                try {
                    countDownLatch.await();
                    synchronized (this.d) {
                        z = this.a;
                    }
                    return z;
                } catch (InterruptedException e) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    l((byte) (61 - View.MeasureSpec.getMode(0)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 2010729409, (short) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), KeyEvent.keyCodeFromString("") - 11, TextUtils.getOffsetAfter("", 0) + 386615690, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    l((byte) (Color.red(0) - 59), 2010729657 - MotionEvent.axisFromString(""), (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 12, 386615705 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr4);
                    g.a(intern2, ((String) objArr4[0]).intern(), e);
                    return false;
                }
            }
            g.c();
            Object[] objArr5 = new Object[1];
            l((byte) (60 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), TextUtils.getOffsetBefore("", 0) + 2010729409, (short) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (-11) - View.resolveSize(0, 0), 386615690 - (ViewConfiguration.getEdgeSlop() >> 16), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            l((byte) (50 - Gravity.getAbsoluteGravity(0, 0)), 2010729571 - Drawable.resolveOpacity(0, 0), (short) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-11) - (ViewConfiguration.getPressedStateDuration() >> 16), (KeyEvent.getMaxKeyCode() >> 16) + 386615704, objArr6);
            g.d(intern3, ((String) objArr6[0]).intern());
            return true;
        }
    }

    public final void d() {
        synchronized (this.d) {
            this.e = false;
            if (this.b != null) {
                g.c();
                Object[] objArr = new Object[1];
                l((byte) (62 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), Color.red(0) + 2010729409, (short) ((-1) - MotionEvent.axisFromString("")), (-10) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 386615690 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l((byte) ((ViewConfiguration.getEdgeSlop() >> 16) - 9), 2010729681 + (ViewConfiguration.getJumpTapTimeout() >> 16), (short) (ViewConfiguration.getDoubleTapTimeout() >> 16), (-11) - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 386615720 + KeyEvent.keyCodeFromString(""), objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.a = true;
                this.b.countDown();
                this.b = null;
            }
        }
    }

    public final void c() {
        synchronized (this.d) {
            this.e = false;
            if (this.b != null) {
                g.c();
                Object[] objArr = new Object[1];
                l((byte) (TextUtils.indexOf("", "", 0, 0) + 61), 2010729409 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (short) (Process.myTid() >> 22), Process.getGidForName("") - 10, 386615690 - Gravity.getAbsoluteGravity(0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l((byte) (TextUtils.getTrimmedLength("") - 85), 2010729746 + (ViewConfiguration.getWindowTouchSlop() >> 8), (short) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (ViewConfiguration.getLongPressTimeout() >> 16) - 11, 386615721 + TextUtils.lastIndexOf("", '0', 0), objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.a = false;
                this.b.countDown();
                this.b = null;
            }
        }
    }

    final void e() {
        synchronized (this.d) {
            if (this.b != null) {
                g.c();
                Object[] objArr = new Object[1];
                l((byte) (TextUtils.indexOf("", "") + 61), 2010729410 + TextUtils.lastIndexOf("", '0'), (short) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), ExpandableListView.getPackedPositionGroup(0L) - 11, 386615690 + (ViewConfiguration.getTapTimeout() >> 16), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l((byte) ((-26) - View.MeasureSpec.getMode(0)), 2010729809 - (ViewConfiguration.getWindowTouchSlop() >> 8), (short) KeyEvent.keyCodeFromString(""), (-11) - (Process.myPid() >> 22), TextUtils.indexOf((CharSequence) "", '0', 0) + 386615721, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.a = false;
                this.b.countDown();
                this.b = null;
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:88:0x029f, code lost:
    
        r3 = r8;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 944
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.e.l(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
