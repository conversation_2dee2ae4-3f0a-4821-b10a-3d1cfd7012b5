package o.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\a\g.smali */
public final class g {
    public int a;
    public int c;
    public int e;

    public static void d(int[] iArr) {
        for (int i = 0; i < iArr.length / 2; i++) {
            int i2 = iArr[i];
            iArr[i] = iArr[(iArr.length - i) - 1];
            iArr[(iArr.length - i) - 1] = i2;
        }
    }

    public static int b(int i) {
        d dVar = d.e;
        return ((dVar.c[0][(i >>> 24) & 255] + dVar.c[1][(i >>> 16) & 255]) ^ dVar.c[2][(i >>> 8) & 255]) + dVar.c[3][i & 255];
    }
}
