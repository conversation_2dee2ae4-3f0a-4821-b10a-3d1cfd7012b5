package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.serializers.ClosureSerializer;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.util.Generics;
import com.esotericsoftware.minlog.Log;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import kotlin.text.Typography;
import org.objenesis.strategy.PlatformDescription;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\Util.smali */
public class Util {
    public static final boolean isAndroid = PlatformDescription.DALVIK.equals(System.getProperty("java.vm.name"));
    public static final int maxArraySize = 2147483639;
    private static final Map<Class<?>, Class<?>> primitiveWrappers;
    public static final boolean unsafe;

    static {
        boolean found = false;
        if ("false".equals(System.getProperty("kryo.unsafe"))) {
            if (Log.TRACE) {
                Log.trace("kryo", "Unsafe is disabled.");
            }
        } else {
            try {
                found = Class.forName("com.esotericsoftware.kryo.unsafe.UnsafeUtil", true, FieldSerializer.class.getClassLoader()).getField("unsafe").get(null) != null;
            } catch (Throwable ex) {
                if (Log.TRACE) {
                    Log.trace("kryo", "Unsafe is unavailable.", ex);
                }
            }
        }
        unsafe = found;
        HashMap hashMap = new HashMap();
        primitiveWrappers = hashMap;
        hashMap.put(Boolean.TYPE, Boolean.class);
        hashMap.put(Byte.TYPE, Byte.class);
        hashMap.put(Character.TYPE, Character.class);
        hashMap.put(Double.TYPE, Double.class);
        hashMap.put(Float.TYPE, Float.class);
        hashMap.put(Integer.TYPE, Integer.class);
        hashMap.put(Long.TYPE, Long.class);
        hashMap.put(Short.TYPE, Short.class);
    }

    public static boolean isClassAvailable(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (Exception e) {
            Log.debug("kryo", "Class not available: " + className);
            return false;
        }
    }

    public static Class getWrapperClass(Class type) {
        return type == Integer.TYPE ? Integer.class : type == Float.TYPE ? Float.class : type == Boolean.TYPE ? Boolean.class : type == Byte.TYPE ? Byte.class : type == Long.TYPE ? Long.class : type == Character.TYPE ? Character.class : type == Double.TYPE ? Double.class : type == Short.TYPE ? Short.class : type;
    }

    public static Class getPrimitiveClass(Class type) {
        return type == Integer.class ? Integer.TYPE : type == Float.class ? Float.TYPE : type == Boolean.class ? Boolean.TYPE : type == Byte.class ? Byte.TYPE : type == Long.class ? Long.TYPE : type == Character.class ? Character.TYPE : type == Double.class ? Double.TYPE : type == Short.class ? Short.TYPE : type == Void.class ? Void.TYPE : type;
    }

    public static boolean isWrapperClass(Class type) {
        return type == Integer.class || type == Float.class || type == Boolean.class || type == Byte.class || type == Long.class || type == Character.class || type == Double.class || type == Short.class;
    }

    public static boolean isEnum(Class type) {
        return Enum.class.isAssignableFrom(type) && type != Enum.class;
    }

    public static void log(String message, Object object, int position) {
        if (object == null) {
            if (Log.TRACE) {
                Log.trace("kryo", message + ": null" + pos(position));
                return;
            }
            return;
        }
        Class type = object.getClass();
        if (!type.isPrimitive() && !isWrapperClass(type) && type != String.class) {
            Log.debug("kryo", message + ": " + string(object) + pos(position));
        } else if (Log.TRACE) {
            Log.trace("kryo", message + ": " + string(object) + pos(position));
        }
    }

    public static String pos(int position) {
        return position == -1 ? "" : " [" + position + "]";
    }

    public static String string(Object object) {
        if (object == null) {
            return "null";
        }
        Class type = object.getClass();
        if (type.isArray()) {
            return className(type);
        }
        String className = Log.TRACE ? className(type) : type.getSimpleName();
        try {
            if (type.getMethod("toString", new Class[0]).getDeclaringClass() == Object.class) {
                return className;
            }
        } catch (Exception e) {
        }
        try {
            String value = String.valueOf(object) + " (" + className + ")";
            return value.length() > 97 ? value.substring(0, 97) + "..." : value;
        } catch (Throwable ex) {
            return className + " (toString exception: " + ex + ")";
        }
    }

    public static String className(Class type) {
        if (type == null) {
            return "null";
        }
        if (type.isArray()) {
            Class elementClass = getElementClass(type);
            StringBuilder buffer = new StringBuilder(16);
            int n = getDimensionCount(type);
            for (int i = 0; i < n; i++) {
                buffer.append("[]");
            }
            return className(elementClass) + ((Object) buffer);
        }
        if (type.isPrimitive() || type == Object.class || type == Boolean.class || type == Byte.class || type == Character.class || type == Short.class || type == Integer.class || type == Long.class || type == Float.class || type == Double.class || type == String.class) {
            return type.getSimpleName();
        }
        return type.getName();
    }

    public static String classNames(Class[] types) {
        StringBuilder buffer = new StringBuilder(32);
        int n = types.length;
        for (int i = 0; i < n; i++) {
            if (i > 0) {
                buffer.append(", ");
            }
            buffer.append(className(types[i]));
        }
        return buffer.toString();
    }

    public static String canonicalName(Class type) {
        if (type == null) {
            return "null";
        }
        String canonicalName = type.getCanonicalName();
        return canonicalName != null ? canonicalName : className(type);
    }

    public static String simpleName(Type type) {
        return type instanceof Class ? ((Class) type).getSimpleName() : type.toString();
    }

    public static String simpleName(Class type, Generics.GenericType genericType) {
        StringBuilder buffer = new StringBuilder(32);
        buffer.append((type.isArray() ? getElementClass(type) : type).getSimpleName());
        if (genericType.arguments != null) {
            buffer.append(Typography.less);
            int n = genericType.arguments.length;
            for (int i = 0; i < n; i++) {
                if (i > 0) {
                    buffer.append(", ");
                }
                buffer.append(genericType.arguments[i].toString());
            }
            buffer.append(Typography.greater);
        }
        if (type.isArray()) {
            int n2 = getDimensionCount(type);
            for (int i2 = 0; i2 < n2; i2++) {
                buffer.append("[]");
            }
        }
        return buffer.toString();
    }

    public static int getDimensionCount(Class arrayClass) {
        int depth = 0;
        for (Class nextClass = arrayClass.getComponentType(); nextClass != null; nextClass = nextClass.getComponentType()) {
            depth++;
        }
        return depth;
    }

    public static Class getElementClass(Class arrayClass) {
        Class elementClass = arrayClass;
        while (elementClass.getComponentType() != null) {
            elementClass = elementClass.getComponentType();
        }
        return elementClass;
    }

    public static boolean isAssignableTo(Class<?> from, Class<?> to) {
        if (to == Object.class || to.isAssignableFrom(from)) {
            return true;
        }
        if (from.isPrimitive()) {
            return isPrimitiveWrapperOf(to, from) || to.isAssignableFrom(getPrimitiveWrapper(from));
        }
        if (to.isPrimitive()) {
            return isPrimitiveWrapperOf(from, to);
        }
        if (from == ClosureSerializer.Closure.class) {
            return to.isInterface();
        }
        return false;
    }

    private static boolean isPrimitiveWrapperOf(Class<?> targetClass, Class<?> primitive) {
        return getPrimitiveWrapper(primitive) == targetClass;
    }

    private static Class<?> getPrimitiveWrapper(Class<?> primitive) {
        if (!primitive.isPrimitive()) {
            throw new IllegalArgumentException("Argument has to be primitive type");
        }
        return primitiveWrappers.get(primitive);
    }

    public static boolean isAscii(String value) {
        int n = value.length();
        for (int i = 0; i < n; i++) {
            if (value.charAt(i) > 127) {
                return false;
            }
        }
        return true;
    }

    public static <T extends SerializerFactory> T newFactory(Class<T> factoryClass, Class<? extends Serializer> serializerClass) {
        if (serializerClass != null) {
            try {
                try {
                    return factoryClass.getConstructor(Class.class).newInstance(serializerClass);
                } catch (NoSuchMethodException e) {
                }
            } catch (Exception ex) {
                if (serializerClass == null) {
                    throw new IllegalArgumentException("Unable to create serializer factory: " + factoryClass.getName(), ex);
                }
                throw new IllegalArgumentException("Unable to create serializer factory \"" + factoryClass.getName() + "\" for serializer class: " + className(serializerClass), ex);
            }
        }
        return factoryClass.newInstance();
    }
}
