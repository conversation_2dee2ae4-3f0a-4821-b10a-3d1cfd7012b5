package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.drawable.Drawable;
import fr.antelop.sdk.CancellationSignal;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.m;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureVirtualCardNumberDisplay.smali */
public final class SecureVirtualCardNumberDisplay implements CustomerAuthenticatedProcess {
    private CustomerAuthenticatedProcessActivityCallback activityCallback;
    private Drawable cardDrawable = null;
    private Integer cardForegroundColor = null;
    private final m innerSecureDigitalCardVcnDisplayProcess;

    public SecureVirtualCardNumberDisplay(m mVar) {
        this.innerSecureDigitalCardVcnDisplayProcess = mVar;
    }

    public final SecureVirtualCardNumberDisplay setCardBackground(Drawable drawable) {
        this.cardDrawable = drawable;
        return this;
    }

    public final SecureVirtualCardNumberDisplay setCardForegroundColor(Integer num) {
        this.cardForegroundColor = num;
        return this;
    }

    public final SecureVirtualCardNumberDisplay setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.activityCallback = customerAuthenticatedProcessActivityCallback;
        return this;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardVcnDisplayProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardVcnDisplayProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardVcnDisplayProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardVcnDisplayProcess.k();
    }

    public final String getMessage() {
        return null;
    }

    public final void display(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnDisplayProcess.a(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnDisplayProcess), new o.dw.e());
    }

    public final void display(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardVcnDisplayProcess.a(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardVcnDisplayProcess), new o.dw.e());
    }

    public final CancellationSignal getCancellationSignal() {
        return this.innerSecureDigitalCardVcnDisplayProcess.a();
    }
}
