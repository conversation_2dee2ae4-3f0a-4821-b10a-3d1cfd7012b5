package o.em;

import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\d.smali */
abstract class d<T> {
    private static char a;
    private static char c;
    private static char e;
    private static int f;
    private static char i;
    private final List<T> b;
    private long d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 0;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\d$e.smali */
    public interface e<T> {
        void e(AntelopError antelopError);

        void e(List<T> list);
    }

    static {
        f = 1;
        c();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        TextUtils.getOffsetBefore("", 0);
        int i2 = h + 67;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        c = (char) 19701;
        e = (char) 61540;
        i = (char) 40687;
        a = (char) 39413;
    }

    protected abstract o.eg.b d(T t) throws o.eg.d;

    protected abstract T e(o.eg.b bVar) throws o.eg.d;

    private d(List<T> list) {
        this.b = list;
        this.d = 0L;
    }

    d() {
        this(new ArrayList());
    }

    protected final void a(List<T> list, long j) {
        int i2 = f + Opcodes.LMUL;
        h = i2 % 128;
        int i3 = i2 % 2;
        this.b.clear();
        this.b.addAll(list);
        this.d = j;
        int i4 = f + 53;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    void b(o.eg.b bVar) throws o.eg.d {
        int i2 = f + 11;
        h = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("ꆼ볃뙇\uf2c4\u10c8챺谽\ue06c嵓ㄡ\uda9b工", 12 - KeyEvent.getDeadChar(0, 0), objArr);
        long longValue = bVar.e(((String) objArr[0]).intern(), (Long) 0L).longValue();
        this.d = longValue;
        if (longValue == 0) {
            this.d = new Date().getTime();
        }
        this.b.clear();
        Object[] objArr2 = new Object[1];
        k("돰㫼誓\uf424", 4 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr2);
        o.eg.e s = bVar.s(((String) objArr2[0]).intern());
        int i4 = 0;
        while (true) {
            switch (i4 < s.d() ? (char) 25 : '\t') {
                case '\t':
                    int i5 = f + 73;
                    h = i5 % 128;
                    switch (i5 % 2 != 0 ? '8' : '#') {
                        case '#':
                            return;
                        default:
                            int i6 = 59 / 0;
                            return;
                    }
                default:
                    this.b.add(e(s.b(i4)));
                    i4++;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    o.eg.b e() throws o.eg.d {
        /*
            r8 = this;
            o.eg.b r0 = new o.eg.b
            r0.<init>()
            o.eg.e r1 = new o.eg.e
            r1.<init>()
            java.util.List<T> r2 = r8.b
            java.util.Iterator r2 = r2.iterator()
            int r3 = o.em.d.h
            int r3 = r3 + 85
            int r4 = r3 % 128
            o.em.d.f = r4
            int r3 = r3 % 2
        L1b:
            boolean r3 = r2.hasNext()
            r4 = 1
            r5 = 0
            if (r3 == 0) goto L25
            r3 = r5
            goto L26
        L25:
            r3 = r4
        L26:
            switch(r3) {
                case 0: goto L60;
                default: goto L29;
            }
        L29:
            java.lang.String r2 = ""
            r3 = 48
            int r2 = android.text.TextUtils.indexOf(r2, r3, r5)
            int r2 = 11 - r2
            java.lang.Object[] r3 = new java.lang.Object[r4]
            java.lang.String r6 = "ꆼ볃뙇\uf2c4\u10c8챺谽\ue06c嵓ㄡ\uda9b工"
            k(r6, r2, r3)
            r2 = r3[r5]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            long r6 = r8.d
            r0.d(r2, r6)
            int r2 = android.graphics.Color.green(r5)
            int r2 = 4 - r2
            java.lang.Object[] r3 = new java.lang.Object[r4]
            java.lang.String r4 = "돰㫼誓\uf424"
            k(r4, r2, r3)
            r2 = r3[r5]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            r0.d(r2, r1)
            goto L87
        L60:
            int r3 = o.em.d.f
            int r3 = r3 + 27
            int r4 = r3 % 128
            o.em.d.h = r4
            int r3 = r3 % 2
            java.lang.Object r3 = r2.next()
            o.eg.b r3 = r8.d(r3)
            r1.b(r3)
            int r3 = o.em.d.h
            int r3 = r3 + 81
            int r4 = r3 % 128
            o.em.d.f = r4
            int r3 = r3 % 2
            if (r3 != 0) goto L84
            r3 = 31
            goto L1b
        L84:
            r3 = 29
            goto L1b
        L87:
            int r1 = o.em.d.h
            int r1 = r1 + 121
            int r2 = r1 % 128
            o.em.d.f = r2
            int r1 = r1 % 2
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.d.e():o.eg.b");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0011. Please report as an issue. */
    public void b() {
        int i2 = f + 85;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
        }
        this.b.clear();
        this.d = 0L;
        int i3 = f + Opcodes.LMUL;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    public List<T> d() {
        int i2 = f + 93;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        List<T> list = this.b;
        int i5 = i3 + 91;
        f = i5 % 128;
        switch (i5 % 2 == 0 ? 'F' : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return list;
            default:
                throw null;
        }
    }

    public long a() {
        int i2 = f + 35;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return this.d;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.d.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
