package com.esotericsoftware.asm;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\asm\Item.smali */
final class Item {
    int a;
    int b;
    int c;
    long d;
    String g;
    String h;
    String i;
    int j;
    Item k;

    Item() {
    }

    Item(int i) {
        this.a = i;
    }

    Item(int i, Item item) {
        this.a = i;
        this.b = item.b;
        this.c = item.c;
        this.d = item.d;
        this.g = item.g;
        this.h = item.h;
        this.i = item.i;
        this.j = item.j;
    }

    void a(double d) {
        this.b = 6;
        this.d = Double.doubleToRawLongBits(d);
        this.j = Integer.MAX_VALUE & (this.b + ((int) d));
    }

    void a(float f) {
        this.b = 4;
        this.c = Float.floatToRawIntBits(f);
        this.j = Integer.MAX_VALUE & (this.b + ((int) f));
    }

    void a(int i) {
        this.b = 3;
        this.c = i;
        this.j = (3 + i) & Integer.MAX_VALUE;
    }

    void a(int i, int i2) {
        this.b = 33;
        this.c = i;
        this.j = i2;
    }

    void a(int i, String str, String str2, String str3) {
        int hashCode;
        int hashCode2;
        int hashCode3;
        this.b = i;
        this.g = str;
        this.h = str2;
        this.i = str3;
        switch (i) {
            case 7:
                this.c = 0;
            case 1:
            case 8:
            case 16:
            case 30:
                hashCode = str.hashCode();
                break;
            case 12:
                hashCode2 = str.hashCode();
                hashCode3 = str2.hashCode();
                hashCode = hashCode2 * hashCode3;
                break;
            default:
                hashCode2 = str.hashCode() * str2.hashCode();
                hashCode3 = str3.hashCode();
                hashCode = hashCode2 * hashCode3;
                break;
        }
        this.j = (i + hashCode) & Integer.MAX_VALUE;
    }

    void a(long j) {
        this.b = 5;
        this.d = j;
        this.j = Integer.MAX_VALUE & (5 + ((int) j));
    }

    void a(String str, String str2, int i) {
        this.b = 18;
        this.d = i;
        this.g = str;
        this.h = str2;
        this.j = Integer.MAX_VALUE & ((i * str.hashCode() * this.h.hashCode()) + 18);
    }

    boolean a(Item item) {
        switch (this.b) {
            case 1:
            case 7:
            case 8:
            case 16:
            case 30:
                break;
            case 3:
            case 4:
                if (item.c != this.c) {
                    break;
                }
                break;
            case 5:
            case 6:
            case 32:
                if (item.d != this.d) {
                    break;
                }
                break;
            case 12:
                if (!item.g.equals(this.g) || !item.h.equals(this.h)) {
                    break;
                }
                break;
            case 18:
                if (item.d != this.d || !item.g.equals(this.g) || !item.h.equals(this.h)) {
                    break;
                }
                break;
            case 31:
                if (item.c != this.c || !item.g.equals(this.g)) {
                    break;
                }
                break;
            default:
                if (!item.g.equals(this.g) || !item.h.equals(this.h) || !item.i.equals(this.i)) {
                    break;
                }
                break;
        }
        return false;
    }
}
