package o.dx;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import o.a.n;
import o.dk.b;
import o.ec.e;
import o.ee.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dx\d.smali */
public final class d implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long e;
    private static int f;
    private static int g;
    private final String a;
    private final String b;
    private final Map<String, String> c;
    private final String d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        e = 449221815589786949L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 4
            int r7 = r7 * 3
            int r7 = 71 - r7
            byte[] r0 = o.dx.d.$$a
            int r9 = r9 * 4
            int r9 = r9 + 1
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L31
        L19:
            r3 = r2
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L31:
            int r9 = -r9
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dx.d.i(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -127, -25, -85};
        $$b = 11;
    }

    private d(String str, Map<String, String> map) {
        this.b = str;
        this.c = map;
        this.d = b.e(e.c(str.getBytes(j.c())));
        this.a = a(str);
    }

    public d(String str) {
        this(str, new HashMap(0));
    }

    public final String b() {
        int i = f + 25;
        g = i % 128;
        switch (i % 2 == 0 ? (char) 19 : '`') {
            case 19:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final Map<String, String> a() {
        int i = g + Opcodes.LSHR;
        f = i % 128;
        switch (i % 2 != 0) {
            case false:
                return this.c;
            default:
                throw null;
        }
    }

    @Override // o.dx.a
    public final String d() {
        String str;
        int i = f + 77;
        int i2 = i % 128;
        g = i2;
        switch (i % 2 == 0 ? (char) 31 : 'a') {
            case 31:
                str = this.d;
                int i3 = 23 / 0;
                break;
            default:
                str = this.d;
                break;
        }
        int i4 = i2 + Opcodes.DDIV;
        f = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String c() {
        int i = g;
        int i2 = i + 71;
        f = i2 % 128;
        int i3 = i2 % 2;
        String str = this.a;
        int i4 = i + 43;
        f = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    @Override // o.dx.a
    public final o.dv.a e() {
        o.dv.e eVar = new o.dv.e(this);
        int i = g + 43;
        f = i % 128;
        int i2 = i % 2;
        return eVar;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        h("醺釲ꭷ㺙ཹ\u2439ㅪ\uf1d4잡䜧ꗀ☱㴅\ue969ﱘ岦鋧㏖\u0a3a\uf519졨䐮ꂈ⭗⇝\ueea0ｷ䇟鞯㜆ᗴ\uf630쵟夢", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.d).append('\'');
        Object[] objArr2 = new Object[1];
        h("獋獧幼쯆\u0a80⇁鯦孚╏뉃ꁺ", ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(this.b).append('\'');
        Object[] objArr3 = new Object[1];
        h("쟄쟨抋\uf731鞒볎\udeb9Ḓ重軭㴷짽歧⃜", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(this.c).append('}').toString();
        int i = g + Opcodes.LSHR;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                return obj;
            default:
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = f + 75;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        if (this == obj) {
            int i4 = i2 + Opcodes.LSHR;
            f = i4 % 128;
            int i5 = i4 % 2;
            return true;
        }
        switch (obj == null) {
            case false:
                switch (getClass() != obj.getClass() ? '`' : 'P') {
                    case 'P':
                        return this.b.equals(((d) obj).b);
                }
            default:
                return false;
        }
    }

    private static String a(String str) {
        Object[] objArr = new Object[1];
        h("ᮨ\u1bf4\ue3e3癗⨽ġ㡥\uf8f7䶓ྚ肎⽿띌ꇴ\ud90c嗟", TextUtils.getCapsMode("", 0, 0), objArr);
        Matcher matcher = Pattern.compile(((String) objArr[0]).intern()).matcher(str);
        switch (!matcher.find()) {
            case true:
                break;
            default:
                switch (matcher.groupCount() != 1) {
                    case false:
                        int i = g + 35;
                        f = i % 128;
                        int i2 = i % 2;
                        return matcher.group(1).toLowerCase(Locale.US);
                }
        }
        int i3 = f + 1;
        g = i3 % 128;
        int i4 = i3 % 2;
        return null;
    }

    private static void h(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2 = $10 + 59;
        $11 = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                switch (str != null) {
                    case true:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str;
                        break;
                }
                n nVar = new n();
                char[] b = n.b(e ^ 8632603938177761503L, cArr, i);
                int i3 = 4;
                nVar.c = 4;
                while (nVar.c < b.length) {
                    int i4 = $10 + 91;
                    $11 = i4 % 128;
                    int i5 = i4 % 2;
                    nVar.e = nVar.c - i3;
                    int i6 = nVar.c;
                    try {
                        Object[] objArr2 = {Long.valueOf(b[nVar.c] ^ b[nVar.c % i3]), Long.valueOf(nVar.e), Long.valueOf(e)};
                        Object obj2 = o.e.a.s.get(-1945790373);
                        if (obj2 == null) {
                            Class cls = (Class) o.e.a.c(TextUtils.indexOf("", "", 0) + 11, (char) View.getDefaultSize(0, 0), 43 - (Process.myPid() >> 22));
                            byte b2 = (byte) ($$b & 5);
                            byte b3 = (byte) (b2 - 1);
                            Object[] objArr3 = new Object[1];
                            i(b2, b3, b3, objArr3);
                            obj2 = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                            o.e.a.s.put(-1945790373, obj2);
                        }
                        b[i6] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                        try {
                            Object[] objArr4 = {nVar, nVar};
                            Object obj3 = o.e.a.s.get(-341518981);
                            if (obj3 == null) {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getTouchSlop() >> 8) + 10, (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 248);
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                i(b4, b5, b5, objArr5);
                                obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                o.e.a.s.put(-341518981, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr4);
                            i3 = 4;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(b, 4, b.length - 4);
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }
}
