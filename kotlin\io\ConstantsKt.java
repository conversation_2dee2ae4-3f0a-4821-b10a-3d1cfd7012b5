package kotlin.io;

import kotlin.Metadata;

/* compiled from: Constants.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0086T¢\u0006\u0002\n\u0000\"\u000e\u0010\u0003\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000¨\u0006\u0004"}, d2 = {"DEFAULT_BLOCK_SIZE", "", "DEFAULT_BUFFER_SIZE", "MINIMUM_BLOCK_SIZE", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\io\ConstantsKt.smali */
public final class ConstantsKt {
    public static final int DEFAULT_BLOCK_SIZE = 4096;
    public static final int DEFAULT_BUFFER_SIZE = 8192;
    public static final int MINIMUM_BLOCK_SIZE = 512;
}
