package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y2.smali */
public class y2 extends d {
    y2(byte[] bArr, boolean z) {
        super(bArr, z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.d, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 3, this.b);
    }

    static int a(boolean z, int i) {
        return z.a(z, i);
    }

    static void a(z zVar, boolean z, byte[] bArr, int i, int i2) throws IOException {
        zVar.a(z, 3, bArr, i, i2);
    }

    static void a(z zVar, boolean z, byte b, byte[] bArr, int i, int i2) throws IOException {
        zVar.a(z, 3, b, bArr, i, i2);
    }
}
