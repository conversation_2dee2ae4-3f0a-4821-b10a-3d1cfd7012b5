package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.minlog.Log;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\VersionFieldSerializer.smali */
public class VersionFieldSerializer<T> extends FieldSerializer<T> {
    private final VersionFieldSerializerConfig config;
    private int[] fieldVersion;
    private int typeVersion;

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\VersionFieldSerializer$Since.smali */
    public @interface Since {
        int value() default 0;
    }

    public VersionFieldSerializer(Kryo kryo, Class type) {
        this(kryo, type, new VersionFieldSerializerConfig());
    }

    public VersionFieldSerializer(Kryo kryo, Class type, VersionFieldSerializerConfig config) {
        super(kryo, type, config);
        this.config = config;
        setAcceptsNull(true);
        initializeCachedFields();
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    protected void initializeCachedFields() {
        FieldSerializer.CachedField[] fields = this.cachedFields.fields;
        this.fieldVersion = new int[fields.length];
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            Field field = fields[i].field;
            Since since = (Since) field.getAnnotation(Since.class);
            if (since != null) {
                this.fieldVersion[i] = since.value();
                this.typeVersion = Math.max(this.fieldVersion[i], this.typeVersion);
            } else {
                this.fieldVersion[i] = 0;
            }
        }
        if (Log.DEBUG) {
            Log.debug("Version for type " + getType().getName() + ": " + this.typeVersion);
        }
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    public void removeField(String fieldName) {
        super.removeField(fieldName);
        initializeCachedFields();
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    public void removeField(FieldSerializer.CachedField field) {
        super.removeField(field);
        initializeCachedFields();
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        if (object == null) {
            output.writeByte((byte) 0);
            return;
        }
        int pop = pushTypeVariables();
        FieldSerializer.CachedField[] fields = this.cachedFields.fields;
        output.writeVarInt(this.typeVersion + 1, true);
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            if (Log.TRACE) {
                log("Write", fields[i], output.position());
            }
            fields[i].write(output, object);
        }
        popTypeVariables(pop);
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        int version = input.readVarInt(true);
        if (version == 0) {
            return null;
        }
        int version2 = version - 1;
        if (!this.config.compatible && version2 != this.typeVersion) {
            throw new KryoException("Version is not compatible: " + version2 + " != " + this.typeVersion);
        }
        int pop = pushTypeVariables();
        T object = create(kryo, input, type);
        kryo.reference(object);
        FieldSerializer.CachedField[] fields = this.cachedFields.fields;
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            if (this.fieldVersion[i] > version2) {
                if (Log.DEBUG) {
                    Log.debug("Skip field: " + fields[i].field.getName());
                }
            } else {
                if (Log.TRACE) {
                    log("Read", fields[i], input.position());
                }
                fields[i].read(input, object);
            }
        }
        popTypeVariables(pop);
        return object;
    }

    public VersionFieldSerializerConfig getVersionFieldSerializerConfig() {
        return this.config;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\VersionFieldSerializer$VersionFieldSerializerConfig.smali */
    public static class VersionFieldSerializerConfig extends FieldSerializer.FieldSerializerConfig {
        boolean compatible = true;

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.FieldSerializerConfig
        /* renamed from: clone */
        public VersionFieldSerializerConfig mo108clone() {
            return (VersionFieldSerializerConfig) super.mo108clone();
        }

        public void setCompatible(boolean compatible) {
            this.compatible = compatible;
            if (Log.TRACE) {
                Log.trace("kryo", "VersionFieldSerializerConfig setCompatible: " + compatible);
            }
        }

        public boolean getCompatible() {
            return this.compatible;
        }
    }
}
