package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\TokenStatus.smali */
public final class TokenStatus extends AbstractSafeParcelable {
    public static final Parcelable.Creator<TokenStatus> CREATOR = new zzl();
    final String zza;
    final int zzb;
    final boolean zzc;

    public TokenStatus(String issuerTokenId, int tokenState, boolean isSelected) {
        this.zza = issuerTokenId;
        this.zzb = tokenState;
        this.zzc = isSelected;
    }

    public int getTokenState() {
        return this.zzb;
    }

    public boolean isSelected() {
        return this.zzc;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 2, this.zza, false);
        SafeParcelWriter.writeInt(dest, 3, this.zzb);
        SafeParcelWriter.writeBoolean(dest, 4, this.zzc);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
